<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0,user-scalable=no,viewport-fit=cover" />


    <link rel="icon" href="<%= BASE_URL %>favicon.ico">
    <link rel="stylesheet" type="text/css" href="/static/css/reset.css">

    <link href="/static/cdn/vant/vant.min.css" rel="preload" as="style">
    <link rel="stylesheet" href="/static/cdn/vant/vant.min.css" as="style">
    <link rel="stylesheet" rel="preload" href="/static/css/mobiscroll.custom-2.17.0.min.css" as="style">
    <script src="/static/vue.min.js"></script>
    <!-- <link href="https://cdn.staticfile.org/vue/2.6.10/vue.min.js" rel="preload" as="script">
    <script src="https://cdn.staticfile.org/vue/2.6.10/vue.min.js"></script> -->

    <!--<link href="https://cdn.staticfile.org/vuex/3.1.0/vuex.min.js" rel="preload" as="script">-->
    <script src="/static/vuex.min.js"></script>

    <link href="/static/cdn/vant/vant.min.js" rel="preload" as="script">
    <script src="/static/cdn/vant/vant.min.js"></script>

    <!-- <link href="https://cdn.staticfile.org/echarts/4.6.0/echarts.min.js" rel="preload" as="script">
    <script src="https://cdn.staticfile.org/echarts/4.6.0/echarts.min.js"></script> -->
    <script src="/static/echarts.min.js"></script>
    <!--<script type="text/javascript" src="https://res.wx.qq.com/open/js/jweixin-1.3.2.js"></script>-->
    <script src="/static/jquery.min.js"></script>
    <script src="/static/mobiscroll.custom-2.17.0.min.js"></script>

    <!--<title>管家</title>-->
    <!-- <script type="text/javascript" src="https://res.wx.qq.com/open/js/jweixin-1.3.2.js"></script> -->

    <script type="text/javascript">
        !function(e,t,n,g,i){e[i]=e[i]||function(){(e[i].q=e[i].q||[]).push(arguments)},n=t.createElement("script"),tag=t.getElementsByTagName("script")[0],n.async=1,n.src=('https:'==document.location.protocol?'https://':'http://')+g,tag.parentNode.insertBefore(n,tag)}(window,document,"script","assets.giocdn.com/2.0/gio-wxwv.js","gio");
        // ‘你的appid’为选填项，如果你的微信内嵌页应用有微信分配的appid，建议填写；如果没有，可以留空。
        gio('init', '9f52391822b91b1c', '', { platform:'Minp' });
        gio('send');
    </script>
</head>
<body>

<noscript>
    <strong>We're sorry but vue-cli3-project doesn't work properly without JavaScript enabled. Please enable it to
        continue.</strong>
</noscript>
<div id="app">
</div>
<!-- built files will be auto injected -->
</body>
</html>
