<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>健康助手智能机器人</title>
</head>
<body>
    <h1>健康助手智能机器人测试页面</h1>
    <p>点击右下角图标开始测试，功能尚不完善，遇到问题可以截图给我反馈；仅供测试使用，完全不能对外，谢谢；</p>
    <!-- <button onclick="openChat()">Open Chat</button> -->

    <!-- 变更日志模块 -->
    <section>
        <h2>变更日志</h2>
        <table border="1" style="width: 100%; border-collapse: collapse;">
            <thead>
                <tr>
                    <th style="padding: 8px; text-align: left;">日期</th>
                    <th style="padding: 8px; text-align: left;">变更说明</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td style="padding: 8px;">2024-12-3</td>
                    <td style="padding: 8px;">更换账号，且更新模型知识库。</td>
                </tr>
                <tr>
                    <td style="padding: 8px;">2024-11-25</td>
                    <td style="padding: 8px;">新增变更日志模块，支持记录日期和说明。</td>
                </tr>
                <tr>
                    <td style="padding: 8px;">2024-11-24</td>
                    <td style="padding: 8px;">替换模型为多agent模型。</td>
                </tr>
            </tbody>
        </table>
    </section>

    <!-- Coze Web SDK script -->
    <script src="https://lf-cdn.coze.cn/obj/unpkg/flow-platform/chat-app-sdk/0.1.0-beta.7/libs/cn/index.js"></script>
    <script>
        // Initialize the Coze WebChatClient
        const chatClient = new CozeWebSDK.WebChatClient({
            config: {
                bot_id: '7442258941259710479', // Replace with your bot ID
            },
            componentProps: {
                title: '健康助手聊天机器人', // Title shown in the chat window
            },
        });

        // Function to open the chat window
        function openChat() {
            chatClient.open(); // Open the chat window on button click
        }
    </script>
</body>
</html>

                <tr>
                    <td style="padding: 8px;">2024-11-25</td>
                    <td style="padding: 8px;">更换模型。</td>
                </tr>
                <tr>
                    <td style="padding: 8px;">2024-11-25</td>
                    <td style="padding: 8px;">新增变更日志模块，支持记录日期和说明。</td>
                </tr>
                <tr>
                    <td style="padding: 8px;">2024-11-24</td>
                    <td style="padding: 8px;">替换模型为多agent模型。</td>
                </tr>
            </tbody>
        </table>
    </section>

    <!-- Coze Web SDK script -->
    <script src="https://lf-cdn.coze.cn/obj/unpkg/flow-platform/chat-app-sdk/0.1.0-beta.7/libs/cn/index.js"></script>
    <script>
        // Initialize the Coze WebChatClient
        const chatClient = new CozeWebSDK.WebChatClient({
            config: {
                bot_id: '7442258941259710479', // Replace with your bot ID
            },
            componentProps: {
                title: 'Coze Chat', // Title shown in the chat window
            },
        });

        // Function to open the chat window
        function openChat() {
            chatClient.open(); // Open the chat window on button click
        }
    </script>
</body>
</html>
