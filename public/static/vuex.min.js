/**
 * vuex v3.1.0
 * (c) 2019 <PERSON> You
 * @license MIT
 */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t=t||self).Vuex=e()}(this,function(){"use strict";var u="undefined"!=typeof window&&window.__VUE_DEVTOOLS_GLOBAL_HOOK__;function a(e,n){Object.keys(e).forEach(function(t){return n(e[t],t)})}var r=function(t,e){this.runtime=e,this._children=Object.create(null);var n=(this._rawModule=t).state;this.state=("function"==typeof n?n():n)||{}},t={namespaced:{configurable:!0}};t.namespaced.get=function(){return!!this._rawModule.namespaced},r.prototype.addChild=function(t,e){this._children[t]=e},r.prototype.removeChild=function(t){delete this._children[t]},r.prototype.getChild=function(t){return this._children[t]},r.prototype.update=function(t){this._rawModule.namespaced=t.namespaced,t.actions&&(this._rawModule.actions=t.actions),t.mutations&&(this._rawModule.mutations=t.mutations),t.getters&&(this._rawModule.getters=t.getters)},r.prototype.forEachChild=function(t){a(this._children,t)},r.prototype.forEachGetter=function(t){this._rawModule.getters&&a(this._rawModule.getters,t)},r.prototype.forEachAction=function(t){this._rawModule.actions&&a(this._rawModule.actions,t)},r.prototype.forEachMutation=function(t){this._rawModule.mutations&&a(this._rawModule.mutations,t)},Object.defineProperties(r.prototype,t);var m,f=function(t){this.register([],t,!1)};f.prototype.get=function(t){return t.reduce(function(t,e){return t.getChild(e)},this.root)},f.prototype.getNamespace=function(t){var n=this.root;return t.reduce(function(t,e){return t+((n=n.getChild(e)).namespaced?e+"/":"")},"")},f.prototype.update=function(t){!function t(e,n,o){n.update(o);if(o.modules)for(var i in o.modules){if(!n.getChild(i))return;t(e.concat(i),n.getChild(i),o.modules[i])}}([],this.root,t)},f.prototype.register=function(n,t,o){var i=this;void 0===o&&(o=!0);var e=new r(t,o);0===n.length?this.root=e:this.get(n.slice(0,-1)).addChild(n[n.length-1],e);t.modules&&a(t.modules,function(t,e){i.register(n.concat(e),t,o)})},f.prototype.unregister=function(t){var e=this.get(t.slice(0,-1)),n=t[t.length-1];e.getChild(n).runtime&&e.removeChild(n)};var e=function(t){var e=this;void 0===t&&(t={}),!m&&"undefined"!=typeof window&&window.Vue&&p(window.Vue);var n=t.plugins;void 0===n&&(n=[]);var o=t.strict;void 0===o&&(o=!1),this._committing=!1,this._actions=Object.create(null),this._actionSubscribers=[],this._mutations=Object.create(null),this._wrappedGetters=Object.create(null),this._modules=new f(t),this._modulesNamespaceMap=Object.create(null),this._subscribers=[],this._watcherVM=new m;var i=this,r=this.dispatch,s=this.commit;this.dispatch=function(t,e){return r.call(i,t,e)},this.commit=function(t,e,n){return s.call(i,t,e,n)},this.strict=o;var a,c=this._modules.root.state;v(this,c,[],this._modules.root),h(this,c),n.forEach(function(t){return t(e)}),(void 0!==t.devtools?t.devtools:m.config.devtools)&&(a=this,u&&((a._devtoolHook=u).emit("vuex:init",a),u.on("vuex:travel-to-state",function(t){a.replaceState(t)}),a.subscribe(function(t,e){u.emit("vuex:mutation",t,e)})))},n={state:{configurable:!0}};function o(e,n){return n.indexOf(e)<0&&n.push(e),function(){var t=n.indexOf(e);-1<t&&n.splice(t,1)}}function i(t,e){t._actions=Object.create(null),t._mutations=Object.create(null),t._wrappedGetters=Object.create(null),t._modulesNamespaceMap=Object.create(null);var n=t.state;v(t,n,[],t._modules.root,!0),h(t,n,e)}function h(n,t,e){var o=n._vm;n.getters={};var i=n._wrappedGetters,r={};a(i,function(t,e){r[e]=function(){return t(n)},Object.defineProperty(n.getters,e,{get:function(){return n._vm[e]},enumerable:!0})});var s=m.config.silent;m.config.silent=!0,n._vm=new m({data:{$$state:t},computed:r}),m.config.silent=s,n.strict&&n._vm.$watch(function(){return this._data.$$state},function(){},{deep:!0,sync:!0}),o&&(e&&n._withCommit(function(){o._data.$$state=null}),m.nextTick(function(){return o.$destroy()}))}function v(c,n,o,t,i){var e=!o.length,u=c._modules.getNamespace(o);if(t.namespaced&&(c._modulesNamespaceMap[u]=t),!e&&!i){var r=_(n,o.slice(0,-1)),s=o[o.length-1];c._withCommit(function(){m.set(r,s,t.state)})}var a,f,h,p,l,d=t.context=(a=c,h=o,l={dispatch:(p=""===(f=u))?a.dispatch:function(t,e,n){var o=y(t,e,n),i=o.payload,r=o.options,s=o.type;return r&&r.root||(s=f+s),a.dispatch(s,i)},commit:p?a.commit:function(t,e,n){var o=y(t,e,n),i=o.payload,r=o.options,s=o.type;r&&r.root||(s=f+s),a.commit(s,i,r)}},Object.defineProperties(l,{getters:{get:p?function(){return a.getters}:function(){return n=a,i={},r=(o=f).length,Object.keys(n.getters).forEach(function(t){if(t.slice(0,r)===o){var e=t.slice(r);Object.defineProperty(i,e,{get:function(){return n.getters[t]},enumerable:!0})}}),i;var n,o,i,r}},state:{get:function(){return _(a.state,h)}}}),l);t.forEachMutation(function(t,e){var n,o,i,r;o=u+e,i=t,r=d,((n=c)._mutations[o]||(n._mutations[o]=[])).push(function(t){i.call(n,r.state,t)})}),t.forEachAction(function(t,e){var i,n,r,s,o=t.root?e:u+e,a=t.handler||t;n=o,r=a,s=d,((i=c)._actions[n]||(i._actions[n]=[])).push(function(t,e){var n,o=r.call(i,{dispatch:s.dispatch,commit:s.commit,getters:s.getters,state:s.state,rootGetters:i.getters,rootState:i.state},t,e);return(n=o)&&"function"==typeof n.then||(o=Promise.resolve(o)),i._devtoolHook?o.catch(function(t){throw i._devtoolHook.emit("vuex:error",t),t}):o})}),t.forEachGetter(function(t,e){!function(t,e,n,o){if(t._wrappedGetters[e])return;t._wrappedGetters[e]=function(t){return n(o.state,o.getters,t.state,t.getters)}}(c,u+e,t,d)}),t.forEachChild(function(t,e){v(c,n,o.concat(e),t,i)})}function _(t,e){return e.length?e.reduce(function(t,e){return t[e]},t):t}function y(t,e,n){var o;return null!==(o=t)&&"object"==typeof o&&t.type&&(n=e,t=(e=t).type),{type:t,payload:e,options:n}}function p(t){m&&t===m||function(t){if(2<=Number(t.version.split(".")[0]))t.mixin({beforeCreate:n});else{var e=t.prototype._init;t.prototype._init=function(t){void 0===t&&(t={}),t.init=t.init?[n].concat(t.init):n,e.call(this,t)}}function n(){var t=this.$options;t.store?this.$store="function"==typeof t.store?t.store():t.store:t.parent&&t.parent.$store&&(this.$store=t.parent.$store)}}(m=t)}n.state.get=function(){return this._vm._data.$$state},n.state.set=function(t){},e.prototype.commit=function(t,e,n){var o=this,i=y(t,e,n),r=i.type,s=i.payload,a={type:r,payload:s},c=this._mutations[r];c&&(this._withCommit(function(){c.forEach(function(t){t(s)})}),this._subscribers.forEach(function(t){return t(a,o.state)}))},e.prototype.dispatch=function(t,e){var n=this,o=y(t,e),i=o.type,r=o.payload,s={type:i,payload:r},a=this._actions[i];if(a){try{this._actionSubscribers.filter(function(t){return t.before}).forEach(function(t){return t.before(s,n.state)})}catch(t){}return(1<a.length?Promise.all(a.map(function(t){return t(r)})):a[0](r)).then(function(t){try{n._actionSubscribers.filter(function(t){return t.after}).forEach(function(t){return t.after(s,n.state)})}catch(t){}return t})}},e.prototype.subscribe=function(t){return o(t,this._subscribers)},e.prototype.subscribeAction=function(t){return o("function"==typeof t?{before:t}:t,this._actionSubscribers)},e.prototype.watch=function(t,e,n){var o=this;return this._watcherVM.$watch(function(){return t(o.state,o.getters)},e,n)},e.prototype.replaceState=function(t){var e=this;this._withCommit(function(){e._vm._data.$$state=t})},e.prototype.registerModule=function(t,e,n){void 0===n&&(n={}),"string"==typeof t&&(t=[t]),this._modules.register(t,e),v(this,this.state,t,this._modules.get(t),n.preserveState),h(this,this.state)},e.prototype.unregisterModule=function(e){var n=this;"string"==typeof e&&(e=[e]),this._modules.unregister(e),this._withCommit(function(){var t=_(n.state,e.slice(0,-1));m.delete(t,e[e.length-1])}),i(this)},e.prototype.hotUpdate=function(t){this._modules.update(t),i(this,!0)},e.prototype._withCommit=function(t){var e=this._committing;this._committing=!0,t(),this._committing=e},Object.defineProperties(e.prototype,n);var s=b(function(i,t){var n={};return g(t).forEach(function(t){var e=t.key,o=t.val;n[e]=function(){var t=this.$store.state,e=this.$store.getters;if(i){var n=w(this.$store,"mapState",i);if(!n)return;t=n.context.state,e=n.context.getters}return"function"==typeof o?o.call(this,t,e):t[o]},n[e].vuex=!0}),n}),c=b(function(r,t){var n={};return g(t).forEach(function(t){var e=t.key,i=t.val;n[e]=function(){for(var t=[],e=arguments.length;e--;)t[e]=arguments[e];var n=this.$store.commit;if(r){var o=w(this.$store,"mapMutations",r);if(!o)return;n=o.context.commit}return"function"==typeof i?i.apply(this,[n].concat(t)):n.apply(this.$store,[i].concat(t))}}),n}),l=b(function(o,t){var i={};return g(t).forEach(function(t){var e=t.key,n=t.val;n=o+n,i[e]=function(){if(!o||w(this.$store,"mapGetters",o))return this.$store.getters[n]},i[e].vuex=!0}),i}),d=b(function(r,t){var n={};return g(t).forEach(function(t){var e=t.key,i=t.val;n[e]=function(){for(var t=[],e=arguments.length;e--;)t[e]=arguments[e];var n=this.$store.dispatch;if(r){var o=w(this.$store,"mapActions",r);if(!o)return;n=o.context.dispatch}return"function"==typeof i?i.apply(this,[n].concat(t)):n.apply(this.$store,[i].concat(t))}}),n});function g(e){return Array.isArray(e)?e.map(function(t){return{key:t,val:t}}):Object.keys(e).map(function(t){return{key:t,val:e[t]}})}function b(n){return function(t,e){return"string"!=typeof t?(e=t,t=""):"/"!==t.charAt(t.length-1)&&(t+="/"),n(t,e)}}function w(t,e,n){return t._modulesNamespaceMap[n]}return{Store:e,install:p,version:"3.1.0",mapState:s,mapMutations:c,mapGetters:l,mapActions:d,createNamespacedHelpers:function(t){return{mapState:s.bind(null,t),mapGetters:l.bind(null,t),mapMutations:c.bind(null,t),mapActions:d.bind(null,t)}}}});
