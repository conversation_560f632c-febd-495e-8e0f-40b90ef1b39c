﻿@charset "utf-8";
/* CSS Document */

body { font-size:16px !important; font-family:"微软雅黑" !important;}
/* 问卷调查 */
.ws-row { margin:20px auto; padding:20px 10px; background:#f8f8f8; border-radius:6px;}
.ws-badges { width:40px; height:auto; float:left;}
.ws-badge { width:25px; padding:0; height:25px; line-height:25px; border-radius:25px; background:#ffaa57; font-size:1rem;}
.ws-surveys { width:80%; float:left;}
.ws-surveys dt { font-size:1.1rem; color:#6c6c6c;}
.ws-surveys dd { margin-top:1rem; font-size:1.1rem; color:#222020;}
.ws-events { margin-top:0.6rem;}
.ws-events-bg { background:#fff; padding:0;}
.ws-event { width:100%; height:42px; line-height:42px; border-top:1px solid #ddd;}
.ws-event-left { text-align:left; float:left; padding-left:10px;}
.ws-event-right { text-align:right; float:right; padding-right:10px;}
.ws-event-infos { margin-top:10px;}
.ws-event-info { color:#6c6c6c; font-size:0.9rem; line-height:1.6;}
.ws-red { color:#ff0000;}

/* 实验室检查 */
.ws-examines {}
.ws-examines h2 { font-size:1.2rem;}
.ws-examine-title { padding:6px 6px 6px 0; color:#9c9c9c; text-align:right; font-size:1rem;}
.ws-examine-content { padding:0;}
.ws-examine-content .layui-table {}
.ws-examine-content .layui-table, .layui-table-view { margin:0;}
.ws-examine-content .layui-table td, .layui-table th { padding:12px 0; font-size:1rem; border-bottom:1px solid #e6e6e6;}
.ws-examine-content .name,.ws-examine-content .data { color:#222020;}
.ws-examine-content .unit { color:#a4a4a4;}
.ws-examine-trbg { background:#f8f8f8;}
.ws-examine-trbg .name { color:#666;}

@media screen and (max-width: 320px) {
    body {
        font-size:14px !important;
    }
    .ws-examines h2 { font-size:1rem;}
    .ws-examine-content .layui-table td, .layui-table th { font-size:0.9rem;}
}

/* 小工具 */
.ws-tools { width:100%; margin:0 auto; padding:0;}
.ws-tools-menu { height:auto; padding:2rem 0; border-bottom:1px solid #ddd; text-align:center;}
.ws-tools-menu:nth-child(odd) { border-right:1px solid #ddd;}
.ws-tools-menu:active { background:#f3f2f2;}
.ws-tools-icon { margin:0 auto; width:60px; height:60px;}
.ws-tools-icon1 { background:url(../images/tools_tzzs.png) 0 0 no-repeat; background-size:cover;}
.ws-tools-icon2 { background:url(../images/tools_sxq.png) 0 0 no-repeat; background-size:cover;}
.ws-tools-icon3 { background:url(../images/tools_yds.png) 0 0 no-repeat; background-size:cover;}
.ws-tools-icon4 { background:url(../images/tools_sxq_tow.png) 0 0 no-repeat; background-size:cover;}
.ws-tools-title { height:2rem; line-height:3rem; font-size:1.4rem;}
@media screen and (max-width: 480px) {
    .ws-tools-title {
        padding-top: 5px;
        font-style: normal;
        font-weight: 400;
        font-size: 15px;
        line-height: 21px;
        /* identical to box height, or 140% */
        text-align: center;
        /* 文字/Text-4 */
        color: #5A6266;
    }
    .ws-tools-title span{
        font-weight: 400;
        font-size: 12px;
        line-height: 20px;
        text-align: center;
        color: #5A6266;
    }
}

/* 体重指数 */
.ws-tools-row { padding: 1rem 0.8rem; font-size:1rem;}
.ws-tools-row-line { border-bottom:1px solid #ececec;}
.ws-tools-input { width:90%; border:0;}
.ws-text { height:1.6rem; line-height:1.6rem; font-size:1.05rem; color:#3e3e3e;}
.ws-text-big { font-size:1.4rem;}
.ws-text-center { text-align:center;}
.ws-text-right { text-align:right;}
.ws-text-gray { color:#9a9a9a;}
.ws-sup { font-size:0.5rem; vertical-align:text-top;}
.ws-btn { width:90%; display:block; margin:0 auto 1.5rem auto; background:#ff7f00; height:2.5rem; line-height:2.5rem; font-size:1rem;}
.ws-interval { width:100%; font-size:1.1rem; height:2rem; line-height:2rem; color:#9a9a9a; background:#f3f3f3; text-indent:0.8rem;}
.ws-tools-hint { line-height:1.8;}
.ws-text-intent { text-indent:2rem;}
.layui-layer-hui { background:rgba(255,255,255,1) !important; color:#666 !important; border-radius:6px !important; min-width:220px !important; padding:20px 0 !important;}
.layui-layer-hui .layui-layer-content { font-size:1rem !important;}
.ws-tools .layui-input-block { margin:-2px 0 0 0; min-height:28px;}
.ws-tools .layui-form-item { margin:0;}
.ws-tools .layui-form-radio { margin:0; padding-right:40px;}
@media screen and (max-width: 320px) {
    .ws-xsHide { display:none;}
}
@media screen and (max-width: 400px) {
    .ws-tools-row { font-size:0.9rem;}
}

[v-cloak] { display: none }

/* 关于MMC */
.ws-about { padding:1rem;}
.ws-about-title { color:#535353; font-size:1.2rem; line-height:2;}
.ws-about-content { color:#808080; font-size:1rem; text-indent:2rem; line-height:1.8;}
.ws-about-empty { margin-top:2rem;}
.ws-about-logo { width:100px; height:100px; margin:3rem auto 0 auto; background:url(../images/logo.png) 0 0 no-repeat; background-size:cover;}
.ws-about-ver { text-align:center; color:#535353; font-size:1rem; margin-top:4px;}

.ws-error { text-align:center; color:#535353; height:45px; line-height:40px; color:#ff0000;}