@charset "utf-8";

/* CSS Document */

html,
body,
div,
span,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
a,
address,
em,
img,
strong,
i,
dl,
dt,
dd,
ol,
ul,
li,
form,
fieldset,
label,
table,
tbody,
thead,
tfoot,
tr,
th,
td,
article,
aside,
header,
footer,
menu,
nav,
section,
audio,
video,
input,
select,
button,
textarea {
	margin: 0;
	padding: 0;
	border: 0;
	box-sizing: border-box;
	-webkit-box-sizing: border-box;
}
body{
	margin:0;
}
html {
	font-size: 20px
}
ul,
ol,
li {
	list-style: none;
}

table {
	border-collapse: collapse;
	border-spacing: 0;
}

input,
select,
textarea,
button {
	font-family: inherit;
}

input[type="submit"],
input[type="reset"],
input[type="button"],
input[type="text"],
button,
select {
	-webkit-appearance: none;
}

a {
	text-decoration: none;
}

a:hover {
	text-decoration: none;
}

header,
footer,
section,
article,
aside,
nav,
figure {
	display: block;
}

h1,
h2,
h3,
h4,
h5,
h6,
strong,
i,
em,
address,
caption,
th {
	font-weight: normal;
	font-style: normal;
}

fieldset,
img {
	border: none;
	display: block;
}


/* 清楚浮动 */

.clearfix:after {
	content: "";
	visibility: hidden;
	height: 0;
	display: block;
	clear: both;
}

.clearfix {
	zoom: 1;
}


/* 浮动 */

.fleft {
	float: left;
}

.fright {
	float: right;
}


/* 定位 */

.positionr,
.positionR {
	position: relative;
}

.positiona,
.positionA {
	position: absolute;
}

.positionf,
.positionF {
	position: fixed;
}


/* 文字对齐方式 */

.tleft {
	text-align: left;
}

.tcenter {
	text-align: center;
}

.tright {
	text-align: right;
}

.mauto {
	margin: 0 auto;
}


/* 小手样式 */

.cursor {
	cursor: pointer;
}

.dis-inline {
	display: inline-block;
}

.bor-none {
	border: 0!important;
}

.borBotNone {
	border-bottom: 0!important;
}

.borTopNone {
	border-top: 0!important;
}

.borLeftNone {
	border-left: 0!important;
}

.borRightNone {
	border-right: 0!important;
}


/* 显示隐藏 */

.show {
	display: block;
}

.hide {
	display: none;
}


/* 文字溢出省略号 */

.ellipsis {
	white-space: nowrap;
	text-overflow: ellipsis;
	overflow: hidden;
}




 
/*手机端实现真正的一像素边框*/
.border-1px, .border-bottom-1px, .border-top-1px, .border-left-1px, .border-right-1px {
   position: relative;
 }
/*线条颜色 黑色*/
.border-1px::after, .border-bottom-1px::after, .border-top-1px::after, .border-left-1px::after, .border-right-1px::after {
     background-color: #d2d2d2; 
}

 /*底边边框一像素*/
.border-bottom-1px::after {
     content:"";
     position: absolute; 
     left: 0;
     bottom: 0;
     width: 100%;
     height: 1px;
     transform-origin: 0 0;
 }

 /*上边边框一像素*/
.border-top-1px::after {
   content:"";
    position: absolute; 
    left: 0;
    top: 0;
    width: 100%;
    height: 1px;
    transform-origin: 0 0;
}

 /*左边边框一像素*/
.border-left-1px::after {
  content:"";
  position: absolute; 
  left: 0;
  top: 0;
  width: 1px;
  height: 100%;
  transform-origin: 0 0;
 }

/*右边边框1像素*/
.border-right-1px::after {
	  content: "";
	  box-sizing: border-box;
	  position: absolute; 
	  right: 0;
	  top: 0;
	  width: 1px;
	  height: 100%;
	  transform-origin: 0 0;
 }

/*边框一像素*/
.border-1px::after {
  content: "";
  box-sizing: border-box;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  border: 1px solid red;
}

/*设备像素比*/
@media only screen and (-webkit-min-device-pixel-ratio: 2.0), only screen and (min-resolution: 2dppx) {
    .border-bottom-1px::after, .border-top-1px::after {
      transform: scaleY(0.5);
    }  

   .border-left-1px::after, .border-right-1px::after {

       transform: scaleX(0.5);
    } 
   .border-1px::after {
      width: 200%;
      height: 200%;
      transform: scale(0.5);
      transform-origin: 0 0;
    }
}

/*设备像素比*/
@media only screen and (-webkit-min-device-pixel-ratio: 3.0), only screen and (min-resolution: 3dppx) {
   .border-bottom-1px::after, .border-top-1px::after {
      transform: scaleY(0.333);
   } 
   .border-left-1px::after, .border-right-1px::after {
     transform: scaleX(0.333);
   } 
  .border-1px::after {
      width: 300%;
      height: 300%;
      transform: scale(0.333);
      transform-origin: 0 0;
  }
}

.line {position:relative;}
.line:after {
	width:200%;
	height:200%;
	position:absolute;
	top:0;
	left:0;
	z-index:0;
	content:"";
	-webkit-transform:scale(0.5);
	-webkit-transform-origin:0 0;
	transform:scale(0.5);
	transform-origin:0 0;
}



/* mui弹框样式修改 */
.mui-popup{
	width:11.56rem;
}

.mui-popup .mui-popup-inner{
	height:5.94rem;
	padding:.26rem;
}

.mui-popup .mui-popup-text{
	margin-top:2.58rem;
	font-size:.74rem;
}

.mui-popup .mui-popup-buttons{
	height:1.78rem;
}

.mui-popup .mui-popup-buttons .mui-popup-button{
	height:1.78rem;
	line-height:1.78rem;
	font-size:.74rem;
}

/* mui弹框样式修改 */


/* ios禁止用户在微信端更改字体大小 */
body { 
	-webkit-text-size-adjust: 100% !important; 
	text-size-adjust: 100% !important; 
	-moz-text-size-adjust: 100% !important; 
}