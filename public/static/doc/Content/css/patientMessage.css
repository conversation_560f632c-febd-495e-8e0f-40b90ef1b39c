.patient-round {
  width: 18.75rem;
  background: #fff;
  padding-bottom: 0.6944444444rem; }
  .patient-round .top-list {
    width: 100%; }
  .patient-round .top-list li {
    height: 2.1875rem;
    padding: 0 0.5034722222rem;
    font-size: 0.625rem;
    overflow: hidden; }
  .patient-round .top-list .content-list {
    margin-top: 0.78125rem; }
  .patient-round .content-list .title {
    color: #323232; }
  .patient-round .content-list .name {
    color: #9a9a9a; }
  .patient-round .inss_type_photo {
    width: 7.2222222222rem;
    height: 7.5868055556rem;
    margin-top: 0.5555555556rem; }
  .patient-round .introduce {
    height: 1.3888888889rem;
    line-height: 1.3888888889rem;
    font-size: 0.7986111111rem;
    color: #333333;
    margin-top: 0.3472222222rem; }
  .patient-round .binging_doctor {
    height: 1.2152777778rem;
    line-height: 1.2152777778rem;
    font-size: 0.5902777778rem;
    color: #999999; }
  .patient-round .binging_patient {
    width: 17.3611111111rem;
    height: 2.4305555556rem;
    line-height: 2.4305555556rem;
    font-size: 0.8333333333rem;
    background: #ff7f00;
    color: #fff;
    border-radius: 50px;
    margin-top: 0.9895833333rem; }

.patient-view-round {
  width: 18.75rem;
  background: #fff;
  padding-bottom: 0.6944444444rem; }
  .patient-view-round .top-list {
    width: 100%; }
  .patient-view-round .top-list li {
    height: 2.1875rem;
    padding: 0 0.5034722222rem;
    font-size: 0.7465277778rem;
    overflow: hidden; }
  .patient-view-round .top-list .content-list {
    margin-top: 0.6944444444rem; }
  .patient-view-round .content-list .title {
    color: #323232; }
  .patient-view-round .content-list .name {
    color: #9a9a9a; }
  .patient-view-round .inss_type_photo {
    width: 7.2222222222rem;
    height: 7.5868055556rem;
    margin-top: 0.5555555556rem; }
  .patient-view-round .introduce {
    height: 1.3888888889rem;
    line-height: 1.3888888889rem;
    font-size: 0.7986111111rem;
    color: #333333;
    margin-top: 0.3472222222rem; }
  .patient-view-round .binging_doctor {
    height: 1.2152777778rem;
    line-height: 1.2152777778rem;
    font-size: 0.5902777778rem;
    color: #999999; }
  .patient-view-round .binging_patient {
    width: 17.3611111111rem;
    height: 2.4305555556rem;
    line-height: 2.4305555556rem;
    font-size: 0.7465277778rem;
    border-radius: 50px; }
    .patient-view-round .binging_patient .content-text {
      width: 9.0277777778rem;
      margin: 0 auto; }
    .patient-view-round .binging_patient img {
      width: 0.9548611111rem;
      height: 0.9548611111rem;
      margin-top: 0.7465277778rem; }
  .patient-view-round .binging_patient_v7 {
    margin-top: 3.90625rem;
    background: #ff7f00;
    color: #fff; }
  .patient-view-round .binging_patient_v8 {
    margin-top: 0.5729166667rem;
    color: #ff7f00; }
  .patient-view-round .binging_patient_v9 {
    margin-top: 0.5729166667rem;
    color: #ff7f00; }
  .patient-view-round .binging_patient_v10 {
    margin-top: 0.5729166667rem;
    color: #ff7f00;
    background: #ffefe0; }
  .patient-view-round .binging_patient_v8::after {
    border: 1px solid #ff7f00;
    border-radius: 50px; }
  .patient-view-round .binging_patient_v9::after {
    border: 1px solid #ff7f00;
    border-radius: 50px; }

.doctor-loadPage-round {
  width: 18.75rem;
  background: #fff;
  padding-bottom: 5.4513888889rem;
}
  .doctor-loadPage-round .top-photo {
    width: 100%;
    height: 12.2743055556rem; }
  .doctor-loadPage-round .logo_icon{
    width: 100%;
    display: flex;
    justify-content: center;
  }
  .doctor-loadPage-round .con-photo {
    width: 6rem;
    height: 6rem;
    top: 8.8645833333rem;
  }
  .doctor-loadPage-round .title {
    height: 1.6840277778rem;
    line-height: 1.6840277778rem;
    font-size: 0.9375rem;
    color: #333333;
    text-align: center;
    margin-top: 2.7256944444rem;
    font-weight: bold; }
  .doctor-loadPage-round .content {
    height: 1.0069444444rem;
    line-height: 1.0069444444rem;
    margin-top: 0.2604166667rem;
    font-size: 0.7986111111rem;
    color: #333333;
    text-align: center; }
  .doctor-loadPage-round .loadBtn {
    width: 10.4166666667rem;
    height: 2.**********rem;
    line-height: 2.**********rem;
    font-size: 0.8506944444rem;
    margin: auto; }
  .doctor-loadPage-round .loadBtn::after {
    /* border: 1px solid #ff8100; */
    border-radius: 50px; }
  .doctor-loadPage-round .loadBtn img {
    width: 1.1284722222rem;
    height: 1.3541666667rem;
    margin: 0.5381944444rem 1.0416666667rem 0 1.09375rem; }
    .doctor-loadPage-round .loadBtn img.img{
      width: 1.3541666667rem;
      height: 1.3541666667rem;
      margin: 0.5381944444rem 1.0416666667rem 0 1.09375rem;
    }
  .doctor-loadPage-round .iosload {
    color: white;
    border-radius: 50px;
    background: linear-gradient(180deg, #3A97FE 0%, #0C7FFF 64.06%, #2E5DFF 100%);
    box-shadow: 0px 4px 30px rgba(153, 175, 255, 0.25), inset 0px -2px 12px rgba(172, 88, 255, 0.1);
    margin-toP: 6.66rem;

  }
  .doctor-loadPage-round .adload {
    background: linear-gradient(180deg, #3A97FE 0%, #0C7FFF 64.06%, #2E5DFF 100%);
    box-shadow: 0px 4px 30px rgba(153, 175, 255, 0.25), inset 0px -2px 12px rgba(172, 88, 255, 0.1);
    border-radius: 50px;
    margin-toP: 6.66rem; }
  .doctor-loadPage-round .adload span {
    color: #fff; }

html {
  background: #fff; }

body {
  max-width: 540px; }

/*# sourceMappingURL=patientMessage.css.map */
.tip-contain{
  width: 100%;
  height: 48px;
  background: rgba(0, 0, 0, 0.12);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 2;
  text-align: center;
}
.tip-contain p{
  display: inline-block;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 300;
  width: 300px;
  color: white;
  font-size: 16px;
  line-height: 48px;
  text-shadow: 0px 1px 2px rgba(0, 0, 0, 0.4);
}
.tip-contain img{
  /* display: inline-block; */
  width: 33px;
  height: 33px;
  vertical-align: middle;
  float: right;
  margin: 7px 10px;
  -webkit-animation: scaleout 2.5s infinite ease-in-out;
  animation: scaleout 2.5s infinite ease-in-out;
}
@-webkit-keyframes scaleout {
  0% {
    transform: scale(1.0);
    -webkit-transform: scale(1.0);
  } 100% {
      transform: scale(1.2);
      -webkit-transform: scale(1.2);
      opacity: 0.3;
    }
}
@keyframes scaleout {
  0% {
    transform: scale(1.0);
    -webkit-transform: scale(1.0);
  } 100% {
      transform: scale(1.2);
      -webkit-transform: scale(1.2);
      opacity: 0.3;
    }
}
@keyframes changebg {
  0% {
    background: rgba(0, 0, 0, 0.12);
  } 
  50% {
    background: rgb(255, 255, 255);
    opacity: 0.1;
  }
  100% {
    background: rgba(0, 0, 0, 0.12);
  }
}
