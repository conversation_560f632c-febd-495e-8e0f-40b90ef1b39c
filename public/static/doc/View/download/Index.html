﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta charset="UTF-8">
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport"/>
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>下载</title>
    <link rel="stylesheet" href="../../Content/css/mui.min.css">
    <link rel="stylesheet" type="text/css" href="../../Content/css/base.css"/>
    <link rel="stylesheet" type="text/css" href="../../Content/css/patientMessage.css?v1=1"/>
    <style>
        .hide {
            display: none;
        }
    </style>
</head>
<body>
<div class="doctor-loadPage-round positionr">
    <img src="../../Content/images/load_page_01.png?v1=1" alt="" class="top-photo">
    <div class="logo_icon">
        <img src="../../Content/images/load_page_02.png?v1=1" alt="" class="positiona con-photo">
    </div>

    <p class="title">医生工作室</p>
    <!-- 在安卓浏览器中 就不展示。尽在微信浏览器中显示 -->
    <div id="tip" class="hide tip-contain">
        <p>请点击右上角按钮，选择在浏览器中打开</p>
        <img src="../../Content/images/load_page_05.png?v1=1" alt="img">
    </div>

    <a href="https://itunes.apple.com/us/app/mmc医家/id1338358436?l=zh&ls=1&mt=8" id="isIphone" class="hide">
        <div class="iosload loadBtn line clearfloat bg-red">
            <img src="../../Content/images/load_page_03.png" alt="" class="fleft img">
            <span class="fleft">iPhone 版下载</span>
        </div>
    </a>
    <!--<a href="http://download.zz-med.com/Mmc_Mobile_Android_Application/new/MMCDoc_Android_Application.apk">-->
    <a href="https://download-zz-med-pro.oss-cn-hangzhou.aliyuncs.com/doc-app/new/doc-app-newest.apk" id="isAndroid"
       class="hide">
        <div class="adload loadBtn clearfloat bg-red">
            <img src="../../Content/images/load_page_04.png" alt="" class="fleft">
            <span class="fleft">Android 版下载</span>
        </div>
    </a>

    <div id="isBrowser" class="hide">
        <div class="adload loadBtn clearfloat bg-red">
            <img src="../../Content/images/load_page_04.png" alt="" class="fleft">
            <span class="fleft">Android 版下载</span>
        </div>
    </div>
</div>
<script src="../../Content/js/jquery.min.js"></script>
<script src="../../Content/js/rem.js"></script>
<script src="../../Content/js/jweixin-1.2.0.js"></script>
<script>
    $(function () {
        var u = navigator.userAgent;
        var isAndroid = u.indexOf('Android') > -1 || u.indexOf('Adr') > -1; //android终端
        var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); //ios终端
        let url = window.location.href
        let obj = {
            url,
            account: 'ihec'
        }
        $.post('https://patient-api.zz-med.com/api/v1/wechat/js_api/sign', obj, function (res) {
            if (res.status === 0) {
                wx.config({
                    debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
                    appId: res.data.appId, // 必填，公众号的唯一标识
                    timestamp: res.data.timestamp, // 必填，生成签名的时间戳
                    nonceStr: res.data.nonceStr, // 必填，生成签名的随机串
                    signature: res.data.signature, // 必填，签名
                    jsApiList: [
                        "onMenuShareAppMessage"
                    ],
                });

                wx.ready(function () {   //需在用户可能点击分享按钮前就先调用
                    wx.onMenuShareAppMessage({
                        title: '下载医生工作室App',
                        desc: '医生工作室，为医生搭建专业的患者管理、科室管理、个人工作管理平台', // 分享描述
                        link: url, // 分享链接，该链接域名或路径必须与当前页面对应的公众号 JS 安全域名一致
                        imgUrl: 'https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/guoshou/logo.png', // 分享图标
                        success: function () {
                            // 设置成功
                            console.log(1111)
                        }
                    })

                });
            }
        });
        var isWeixin = navigator.userAgent.toLowerCase().match(/MicroMessenger/i) == 'micromessenger';
        if (isAndroid) {
            if (isWeixin) {
                $("#isBrowser").show()
                $("#isAndroid").hide()
                $("#tip").show()
            } else {
                $("#isAndroid").show()
                $("#isBrowser").hide()
                $("#tip").hide()
            }
        }
        if (isiOS) {
            $("#isIphone").show()

            if (isWeixin) {
                $("#tip").show()
            } else {
                $("#tip").hide()
            }
        }
        $("#isBrowser").click(function () {
            $("#tip").css('animation', 'changebg 2.5s infinite ease-in-out')
            setTimeout(function () {
                $("#tip").css('animation', 'none')
            }, 1000)
        })
    })
</script>
</body>
</html>
