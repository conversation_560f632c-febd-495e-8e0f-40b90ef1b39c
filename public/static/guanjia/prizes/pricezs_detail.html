<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0,
      maximum-scale=1.0, user-scalable=0"
    />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>健康金明细</title>
    <link rel="stylesheet" href="./assets/css/vant.css" />
    <link rel="stylesheet" href="./assets/css/common.css" />
    <link rel="stylesheet" href="./assets/css/prizes_detail.css" />
    <script src="./assets/js/rem.js"></script>
    <script>
      (function() {
        if (
          typeof WeixinJSBridge == 'object' &&
          typeof WeixinJSBridge.invoke == 'function'
        ) {
          handleFontSize()
        } else {
          document.addEventListener(
            'WeixinJSBridgeReady',
            handleFontSize,
            false
          )
        }
        function handleFontSize() {
          // 设置网页字体为默认大小
          WeixinJSBridge.invoke('setFontSizeCallback&', { fontSize: 0 })
          // 重写设置网页字体大小的事件
          WeixinJSBridge.on('menu:setfont', function() {
            WeixinJSBridge.invoke('setFontSizeCallback', { 'fontSize&': 0 })
          })
        }
      })()
    </script>
  </head>
  <body>
    <div id="pricezs-detail" class="wrapper" v-cloak>
      <div class="content">
        <div class="content-bg">
          <img class="content-bg-img" src="./assets/images/header-bg.png" />
        </div>
        <div class="content-up">
          <a href="./Integral_description.html">规则</a>
        </div>
        <div class="content-info">
          当前<span class="content-info-num">{{ balance }}</span>个
          <img class="content-info-img" src="./assets/images/<EMAIL>" />
        </div>
        <div class="content-list">
          <div class="list-group" v-for="item in detailList">
            <div class="group-left">
              <p class="group-left-p1">{{ item.remark }}</p>
              <p class="group-left-p2">{{ item.time }}</p>
            </div>
            <div class="group-right" v-if="item.amount >= 0">+{{ item.amount }}</div>
            <div class="group-right group-right2" v-else>{{ item.amount }}</div>
          </div>
          <div class="list-no">没有更多了</div>
        </div>
      </div>
    </div>
    <script src="./assets/js/vue.min.js"></script>
    <script src="./assets/js/vant.min.js"></script>
    <script src="./assets/js/vue-resource.min.js"></script>
    <script src="./assets/js/dayjs.min.js"></script>
    <script src="./assets/js/util.js"></script>
    <script src="./assets/js/pricezs_detail.js"></script>
  </body>
</html>
