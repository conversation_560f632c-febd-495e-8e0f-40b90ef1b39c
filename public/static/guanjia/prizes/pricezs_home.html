<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0,
      maximum-scale=1.0, user-scalable=0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>健康金赢豪礼</title>
    <link rel="stylesheet" href="./assets/css/vant.css"/>
    <link rel="stylesheet" href="./assets/css/index.css">
    <link rel="stylesheet" href="./assets/css/common.css">
    <script src="./assets/js/rem.js"></script>
    <script>
        (function() {
            if (typeof WeixinJSBridge == "object" && typeof WeixinJSBridge.invoke == "function") {
                handleFontSize();
            } else {
                document.addEventListener("WeixinJSBridgeReady", handleFontSize, false);
            }
            function handleFontSize() {
                // 设置网页字体为默认大小
                WeixinJSBridge.invoke("setFontSizeCallback&", { "fontSize" : 0 });
                // 重写设置网页字体大小的事件
                WeixinJSBridge.on("menu:setfont", function() {
                    WeixinJSBridge.invoke("setFontSizeCallback", { "fontSize&" : 0 });
                });
            }
        })();
    </script>
</head>
<body>
<div id="raffle-page" v-cloak>
    <div class="header">
        <!-- <p class="one">{{ exchageCurrActivity.name }}</p> -->
        <div class="header-bg">
            <img class="header-bg-img" src="./assets/images/header-bg.png" />
        </div>
        <div class="header-up">
            <a href="./Integral_description.html">规则</a>
        </div>
        <div class="header-title">
            <img src="./assets/images/title2.png">
        </div>
        <div class="header-notice">
            <van-swipe
                vertical
                class="notice-swipe"
                :autoplay="3000"
                :show-indicators="false"
            >
                <van-swipe-item v-for="item in lastPrizeUser" :key="item.id">
                    {{item.user_cell}}通过{{formatActive(item.act_type)}}{{item.p_name}}
                </van-swipe-item>
            </van-swipe>
        </div>
        <div class="header-info">
            <img class="header-info-icon" src="./assets/images/jiankangjin2.png" />
            <div class="header-info-right">
                <p class="header-info-text">当前健康金</p>
                <div class="header-info-box">
                    <div class="info-box-left">
                        <span class="header-info-num">{{userInfo.point}}</span>
                        <!-- 明细新页面 -->
                        <a class="header-info-detail" href="./pricezs_detail.html">明细<van-icon name="arrow" /></a>
                    </div>
                    <a class="header-info-prize" href="./my_prizes.html">我的奖品</a>
                </div>
            </div>
        </div>
        <div class="header-turntable" @click="showLuckDigo">
            <img class="header-turntable-icon" src="./assets/images/choujiang.png" />
            <div class="header-turntable-box">
                <div class="header-turntable-mid">
                    <p class="header-turntable-text1">幸运抽奖</p>
                    <p class="header-turntable-text2">健康金抽幸运好礼</p>
                </div>
                <van-icon name="arrow" class="header-turntable-arrow" />
            </div>
        </div>
    </div>
    <div class="products">
        <div v-if="productData.productList.length>0">
            <div class="list">
                <div class="list-tips">
                    <span class="list-tips-text1">本期奖品</span>
                    <span class="list-tips-text2">还剩{{diffDay}}天结束兑换</span>
                </div>
                <div class="item content" v-for="(item,index) in
              productData.productList" :key="item.id">
                    <van-row type="flex" justify="center"
                             @click.stop="handleConfirmDuihuan(item)">
                        <van-col span="7" class="pic">
                            <img :src="item.p_pic" class="pto">
                        </van-col>
                        <van-col span="11" class="des">
                            <p>{{item.p_name}}</p>
                            <p><span>价值：</span>{{item.p_original_price}}{{item.p_original_unit}}</p>
                            <p>需要健康金：<img src="./assets/images/coin.png"
                                          alt=""><span style="color:#FFA300;">{{item.p_current_price}}</span></p>
                        </van-col>
                        <van-col span="6" class="btn-content">
                            <button class="btn-confirm-blod">
                                兑换
                            </button>
                        </van-col>
                    </van-row>
                </div>
            </div>
        </div>
    </div>
    <van-overlay :show="showDigo" @click="showDigo= false">
        <div class="custDigo">
            <div class="duihuan">
                <div class="product">
                    <img :src="currentProduct.p_pic" alt="">
                </div>
                <p>奖品名：{{currentProduct.p_name}}</p>
                <p v-if="lucky.currentLuckPro.p_sub_type==1">
                    价值：{{currentProduct.p_original_price}}{{currentProduct.p_original_unit}}</p>
                <p v-if="lucky.currentLuckPro.p_sub_type==1">限量：{{currentProduct.stock_num}}件</p>
                <p class="last">确认用{{currentProduct.p_current_price}}健康金兑换该奖品吗？</p>
                <button class="btn-confirm-blod confirm" style="width:
              4.8rem;margin-bottom:0"
                        :loading="duihuanLoading"
                        :disabled="duihuanLoading"
                        @click.stop="handleFinallyDuihuan">
                    确定兑换
                </button>

            </div>
        </div>
    </van-overlay>
    <van-dialog v-model="showDigoSuccess" title="兑换成功"
                :show-cancel-button="false" :show-confirm-button="false"
                :close-on-click-overlay="true">
        <button class="btn-confirm-blod confirm" @click="handleDuiSuccess">
            查看我们的奖品
        </button>


    </van-dialog>
    <van-overlay :show="lucky.showLuck" @click="lucky.showLuck= false">
        <div id="rotary-table" @click.stop="return false">
            <div class="award" v-for="(award,index) in lucky.awards"
                 :class="['award'+index,{'active': index==lucky.current}]">
                <img :src="award.p_pic" alt="">
                <p>{{award.p_name}}</p>
            </div>
            <button id="start-btn"
                    style="padding:0;border-radius:0;" @click.stop="start"
                    :disabled="lucky.startBtn">
                <p>点击抽奖</p>
                <p v-if="game.user_info.user_residue_free_degree<1">
                    {{game.game_info.rule_limit_type_no_free_point}}健康金</p>
                <p v-if="game.user_info.user_residue_free_degree<1">抽一次</p>
                <p v-if="game.user_info.user_residue_free_degree>0"

                >本次免费</p>
            </button>
        </div>
        <div class="btn-content-lucky">
            <button class="btn-lucky">今天还能再抽{{game.user_info.user_residue_degree}}次</button>
        </div>
    </van-overlay>
    <van-dialog v-model="showDigo1" title="今天抽奖机会已经用完"
                :show-cancel-button="false" :show-confirm-button="false"
                :close-on-click-overlay="true">
        <button class="btn-confirm-blod confirm" style="width: 3.9rem;"
                @click="showDigo1=false"> 确定
        </button>
    </van-dialog>

    <van-dialog v-model="showDigo2" title="健康金不够"
                :show-cancel-button="false" :show-confirm-button="false">
        <button class="btn-confirm-blod confirm" style="width: 3.9rem;"
                @click="showDigo2=false"> 确定
        </button>
    </van-dialog>
    <van-dialog v-model="showDigo3" title="很遗憾，没有中奖"
                :show-cancel-button="false" :show-confirm-button="false"
                :close-on-click-overlay="true">
        <div class="tip">
            中奖贵在坚持，没中奖就再来一次吧
        </div>
        <button class="btn-confirm-blod confirm" style="width: 3.9rem;
          margin-top:0 ;" @click="showDigo3=false"> 确定
        </button>
    </van-dialog>
    <van-dialog v-model="showDigo4" title="健康金不够"
                :close-on-click-overlay="true"
                :show-cancel-button="false" :show-confirm-button="false">
        <button class="btn-confirm-blod confirm" style="width: 3.9rem;"
                @click="goLucky"> 去幸运抽奖
        </button>
    </van-dialog>
    <van-overlay :show="lucky.gxdigo" @click="lucky.gxdigo= false">
        <div class="custDigo gxi">
            <div class="gxi-content">
                <img :src="lucky.currentLuckPro.p_pic" alt="">
                <p>奖品名：{{lucky.currentLuckPro.p_name}}</p>
                <p v-show="lucky.currentLuckPro.p_sub_type==1">
                    价值：{{lucky.currentLuckPro.p_original_price}}{{lucky.currentLuckPro.p_original_unit}}</p>
                <p v-show="lucky.currentLuckPro.p_sub_type==1">限量：{{lucky.currentLuckPro.stock_num}}</p>
                <p :class="{lastP:lucky.currentLuckPro.p_sub_type!==1}">
                    <a href="./my_prizes.html">
                        <button class="btn-confirm-blod"
                                style="width: 3.9rem;">查看我的奖品
                        </button>
                    </a>
                </p>
            </div>

        </div>
    </van-overlay>

</div>
<script src="./assets/js/vue.min.js"></script>
<script src="./assets/js/vant.min.js"></script>
<script src="./assets/js/vue-resource.min.js"></script>
<script src="./assets/js/dayjs.min.js"></script>
<script src="./assets/js/util.js"></script>
<script src="./assets/js/pricezs_home.js?v1"></script>

</body>
</html>
