!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(require("vue")):"function"==typeof define&&define.amd?define("vant",["vue"],e):"object"==typeof exports?exports.vant=e(require("vue")):t.vant=e(t.Vue)}("undefined"!=typeof self?self:this,(function(t){return function(t){var e={};function i(n){if(e[n])return e[n].exports;var s=e[n]={i:n,l:!1,exports:{}};return t[n].call(s.exports,s,s.exports,i),s.l=!0,s.exports}return i.m=t,i.c=e,i.d=function(t,e,n){i.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:n})},i.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},i.t=function(t,e){if(1&e&&(t=i(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(i.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var s in t)i.d(n,s,function(e){return t[e]}.bind(null,s));return n},i.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return i.d(e,"a",e),e},i.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},i.p="",i(i.s=12)}([function(t,e,i){"use strict";i.d(e,"f",(function(){return s})),i.d(e,"g",(function(){return r})),i.d(e,"b",(function(){return o})),i.d(e,"c",(function(){return a})),i.d(e,"d",(function(){return l})),i.d(e,"e",(function(){return c})),i.d(e,"a",(function(){return u}));var n=i(2),s=i.n(n).a.prototype.$isServer;function r(){}function o(t){return null!=t}function a(t){return"function"==typeof t}function l(t){return null!==t&&"object"==typeof t}function c(t){return l(t)&&a(t.then)&&a(t.catch)}function u(t,e){var i=e.split("."),n=t;return i.forEach((function(t){n=o(n[t])?n[t]:""})),n}},function(t,e,i){"use strict";function n(){return(n=Object.assign||function(t){for(var e,i=1;i<arguments.length;i++)for(var n in e=arguments[i])Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t}).apply(this,arguments)}var s=["attrs","props","domProps"],r=["class","style","directives"],o=["on","nativeOn"],a=function(t,e){return function(){t&&t.apply(this,arguments),e&&e.apply(this,arguments)}};t.exports=function(t){return t.reduce((function(t,e){for(var i in e)if(t[i])if(-1!==s.indexOf(i))t[i]=n({},t[i],e[i]);else if(-1!==r.indexOf(i)){var l=t[i]instanceof Array?t[i]:[t[i]],c=e[i]instanceof Array?e[i]:[e[i]];t[i]=l.concat(c)}else if(-1!==o.indexOf(i))for(var u in e[i])if(t[i][u]){var h=t[i][u]instanceof Array?t[i][u]:[t[i][u]],d=e[i][u]instanceof Array?e[i][u]:[e[i][u]];t[i][u]=h.concat(d)}else t[i][u]=e[i][u];else if("hook"==i)for(var f in e[i])t[i][f]=t[i][f]?a(t[i][f],e[i][f]):e[i][f];else t[i]=e[i];else t[i]=e[i];return t}),{})}},function(e,i){e.exports=t},function(t,e,i){"use strict";i.d(e,"a",(function(){return s})),i.d(e,"b",(function(){return r}));var n=/-(\w)/g;function s(t){return t.replace(n,(function(t,e){return e.toUpperCase()}))}function r(t,e){void 0===e&&(e=2);for(var i=t+"";i.length<e;)i="0"+i;return i}},function(t,e,i){"use strict";(function(t){i.d(e,"c",(function(){return l})),i.d(e,"b",(function(){return c})),i.d(e,"a",(function(){return u}));var n=i(0),s=Date.now();var r=n.f?t:window,o=r.requestAnimationFrame||function(t){var e=Date.now(),i=Math.max(0,16-(e-s)),n=setTimeout(t,i);return s=e+i,n},a=r.cancelAnimationFrame||r.clearTimeout;function l(t){return o.call(r,t)}function c(t){l((function(){l(t)}))}function u(t){a.call(r,t)}}).call(this,i(11))},function(t,e,i){"use strict";function n(t){return/^\d+(\.\d+)?$/.test(t)}function s(t){return Number.isNaN?Number.isNaN(t):t!=t}i.d(e,"b",(function(){return n})),i.d(e,"a",(function(){return s}))},function(t,e,i){"use strict";var n=i(2),s=i.n(n),r=i(7),o=s.a.prototype,a=s.a.util.defineReactive;a(o,"$vantLang","zh-CN"),a(o,"$vantMessages",{"zh-CN":{name:"姓名",tel:"电话",save:"保存",confirm:"确认",cancel:"取消",delete:"删除",complete:"完成",loading:"加载中...",telEmpty:"请填写电话",nameEmpty:"请填写姓名",nameInvalid:"请输入正确的姓名",confirmDelete:"确定要删除吗",telInvalid:"请输入正确的手机号",vanCalendar:{end:"结束",start:"开始",title:"日期选择",confirm:"确定",startEnd:"开始/结束",weekdays:["日","一","二","三","四","五","六"],monthTitle:function(t,e){return t+"年"+e+"月"},rangePrompt:function(t){return"选择天数不能超过 "+t+" 天"}},vanContactCard:{addText:"添加联系人"},vanContactList:{addText:"新建联系人"},vanPagination:{prev:"上一页",next:"下一页"},vanPullRefresh:{pulling:"下拉即可刷新...",loosing:"释放即可刷新..."},vanSubmitBar:{label:"合计："},vanCoupon:{unlimited:"无使用门槛",discount:function(t){return t+"折"},condition:function(t){return"满"+t+"元可用"}},vanCouponCell:{title:"优惠券",tips:"暂无可用",count:function(t){return t+"张可用"}},vanCouponList:{empty:"暂无优惠券",exchange:"兑换",close:"不使用优惠券",enable:"可用",disabled:"不可用",placeholder:"请输入优惠码"},vanAddressEdit:{area:"地区",postal:"邮政编码",areaEmpty:"请选择地区",addressEmpty:"请填写详细地址",postalEmpty:"邮政编码格式不正确",defaultAddress:"设为默认收货地址",telPlaceholder:"收货人手机号",namePlaceholder:"收货人姓名",areaPlaceholder:"选择省 / 市 / 区"},vanAddressEditDetail:{label:"详细地址",placeholder:"街道门牌、楼层房间号等信息"},vanAddressList:{add:"新增地址"}}});e.a={messages:function(){return o.$vantMessages[o.$vantLang]},use:function(t,e){var i;o.$vantLang=t,this.add(((i={})[t]=e,i))},add:function(t){void 0===t&&(t={}),Object(r.a)(o.$vantMessages,t)}}},function(t,e,i){"use strict";i.d(e,"a",(function(){return r}));var n=i(0),s=Object.prototype.hasOwnProperty;function r(t,e){return Object.keys(e).forEach((function(i){!function(t,e,i){var o=e[i];Object(n.b)(o)&&(s.call(t,i)&&Object(n.d)(o)?t[i]=r(Object(t[i]),e[i]):t[i]=o)}(t,e,i)})),t}},function(t,e,i){"use strict";i.d(e,"a",(function(){return r}));var n=i(0),s=i(5);function r(t){if(Object(n.b)(t))return t=String(t),Object(s.b)(t)?t+"px":t}},function(t,e,i){"use strict";function n(t){return function(e,i){return e&&"string"!=typeof e&&(i=e,e=""),""+(e=e?t+"__"+e:t)+function t(e,i){return i?"string"==typeof i?" "+e+"--"+i:Array.isArray(i)?i.reduce((function(i,n){return i+t(e,n)}),""):Object.keys(i).reduce((function(n,s){return n+(i[s]?t(e,s):"")}),""):""}(e,i)}}i.d(e,"a",(function(){return f}));var s=i(0),r=i(3),o=i(2),a=i.n(o).a.extend({methods:{slots:function(t,e){void 0===t&&(t="default");var i=this.$slots,n=this.$scopedSlots[t];return n?n(e):i[t]}}});function l(t){var e=this.name;t.component(e,this),t.component(Object(r.a)("-"+e),this)}function c(t){return{functional:!0,props:t.props,model:t.model,render:function(e,i){return t(e,i.props,function(t){var e=t.scopedSlots||t.data.scopedSlots||{},i=t.slots();return Object.keys(i).forEach((function(t){e[t]||(e[t]=function(){return i[t]})})),e}(i),i)}}}function u(t){return function(e){return Object(s.c)(e)&&(e=c(e)),e.functional||(e.mixins=e.mixins||[],e.mixins.push(a)),e.name=t,e.install=l,e}}var h=i(6);function d(t){var e=Object(r.a)(t)+".";return function(t){for(var i=h.a.messages(),n=Object(s.a)(i,e+t)||Object(s.a)(i,t),r=arguments.length,o=new Array(r>1?r-1:0),a=1;a<r;a++)o[a-1]=arguments[a];return Object(s.c)(n)?n.apply(void 0,o):n}}function f(t){return[u(t="van-"+t),n(t),d(t)]}},function(t,e,i){
/*!
 * Vue-Lazyload.js v1.2.3
 * (c) 2018 Awe <<EMAIL>>
 * Released under the MIT License.
 */
t.exports=function(){"use strict";function t(t){t=t||{};var n=arguments.length,s=0;if(1===n)return t;for(;++s<n;){var r=arguments[s];d(t)&&(t=r),i(r)&&e(t,r)}return t}function e(e,s){for(var r in f(e,s),s)if("__proto__"!==r&&n(s,r)){var o=s[r];i(o)?("undefined"===m(e[r])&&"function"===m(o)&&(e[r]=o),e[r]=t(e[r]||{},o)):e[r]=o}return e}function i(t){return"object"===m(t)||"function"===m(t)}function n(t,e){return Object.prototype.hasOwnProperty.call(t,e)}function s(t,e){if(t.length){var i=t.indexOf(e);return i>-1?t.splice(i,1):void 0}}function r(t,e){if("IMG"===t.tagName&&t.getAttribute("data-srcset")){var i=t.getAttribute("data-srcset"),n=[],s=t.parentNode.offsetWidth*e,r=void 0,o=void 0,a=void 0;(i=i.trim().split(",")).map((function(t){t=t.trim(),-1===(r=t.lastIndexOf(" "))?(o=t,a=999998):(o=t.substr(0,r),a=parseInt(t.substr(r+1,t.length-r-2),10)),n.push([a,o])})),n.sort((function(t,e){if(t[0]<e[0])return-1;if(t[0]>e[0])return 1;if(t[0]===e[0]){if(-1!==e[1].indexOf(".webp",e[1].length-5))return 1;if(-1!==t[1].indexOf(".webp",t[1].length-5))return-1}return 0}));for(var l="",c=void 0,u=n.length,h=0;h<u;h++)if((c=n[h])[0]>=s){l=c[1];break}return l}}function o(t,e){for(var i=void 0,n=0,s=t.length;n<s;n++)if(e(t[n])){i=t[n];break}return i}function a(){if(!g)return!1;var t=!0,e=document;try{var i=e.createElement("object");i.type="image/webp",i.style.visibility="hidden",i.innerHTML="!",e.body.appendChild(i),t=!i.offsetWidth,e.body.removeChild(i)}catch(e){t=!1}return t}function l(){}var c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},u=function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")},h=function(){function t(t,e){for(var i=0;i<e.length;i++){var n=e[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,i,n){return i&&t(e.prototype,i),n&&t(e,n),e}}(),d=function(t){return null==t||"function"!=typeof t&&"object"!==(void 0===t?"undefined":c(t))},f=function(t,e){if(null==t)throw new TypeError("expected first argument to be an object.");if(void 0===e||"undefined"==typeof Symbol)return t;if("function"!=typeof Object.getOwnPropertySymbols)return t;for(var i=Object.prototype.propertyIsEnumerable,n=Object(t),s=arguments.length,r=0;++r<s;)for(var o=Object(arguments[r]),a=Object.getOwnPropertySymbols(o),l=0;l<a.length;l++){var c=a[l];i.call(o,c)&&(n[c]=o[c])}return n},p=Object.prototype.toString,m=function(t){var e=void 0===t?"undefined":c(t);return"undefined"===e?"undefined":null===t?"null":!0===t||!1===t||t instanceof Boolean?"boolean":"string"===e||t instanceof String?"string":"number"===e||t instanceof Number?"number":"function"===e||t instanceof Function?void 0!==t.constructor.name&&"Generator"===t.constructor.name.slice(0,9)?"generatorfunction":"function":void 0!==Array.isArray&&Array.isArray(t)?"array":t instanceof RegExp?"regexp":t instanceof Date?"date":"[object RegExp]"===(e=p.call(t))?"regexp":"[object Date]"===e?"date":"[object Arguments]"===e?"arguments":"[object Error]"===e?"error":"[object Promise]"===e?"promise":function(t){return t.constructor&&"function"==typeof t.constructor.isBuffer&&t.constructor.isBuffer(t)}(t)?"buffer":"[object Set]"===e?"set":"[object WeakSet]"===e?"weakset":"[object Map]"===e?"map":"[object WeakMap]"===e?"weakmap":"[object Symbol]"===e?"symbol":"[object Map Iterator]"===e?"mapiterator":"[object Set Iterator]"===e?"setiterator":"[object String Iterator]"===e?"stringiterator":"[object Array Iterator]"===e?"arrayiterator":"[object Int8Array]"===e?"int8array":"[object Uint8Array]"===e?"uint8array":"[object Uint8ClampedArray]"===e?"uint8clampedarray":"[object Int16Array]"===e?"int16array":"[object Uint16Array]"===e?"uint16array":"[object Int32Array]"===e?"int32array":"[object Uint32Array]"===e?"uint32array":"[object Float32Array]"===e?"float32array":"[object Float64Array]"===e?"float64array":"object"},v=t,g="undefined"!=typeof window,b=g&&"IntersectionObserver"in window,y="event",S="observer",k=function(){function t(t,e){e=e||{bubbles:!1,cancelable:!1,detail:void 0};var i=document.createEvent("CustomEvent");return i.initCustomEvent(t,e.bubbles,e.cancelable,e.detail),i}if(g)return"function"==typeof window.CustomEvent?window.CustomEvent:(t.prototype=window.Event.prototype,t)}(),x=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;return g&&window.devicePixelRatio||t},w=function(){if(g){var t=!1;try{var e=Object.defineProperty({},"passive",{get:function(){t=!0}});window.addEventListener("test",null,e)}catch(t){}return t}}(),C={on:function(t,e,i){var n=arguments.length>3&&void 0!==arguments[3]&&arguments[3];w?t.addEventListener(e,i,{capture:n,passive:!0}):t.addEventListener(e,i,n)},off:function(t,e,i){var n=arguments.length>3&&void 0!==arguments[3]&&arguments[3];t.removeEventListener(e,i,n)}},O=function(t,e,i){var n=new Image;n.src=t.src,n.onload=function(){e({naturalHeight:n.naturalHeight,naturalWidth:n.naturalWidth,src:n.src})},n.onerror=function(t){i(t)}},T=function(t,e){return"undefined"!=typeof getComputedStyle?getComputedStyle(t,null).getPropertyValue(e):t.style[e]},$=function(t){return T(t,"overflow")+T(t,"overflow-y")+T(t,"overflow-x")},B={},I=function(){function t(e){var i=e.el,n=e.src,s=e.error,r=e.loading,o=e.bindType,a=e.$parent,l=e.options,c=e.elRenderer;u(this,t),this.el=i,this.src=n,this.error=s,this.loading=r,this.bindType=o,this.attempt=0,this.naturalHeight=0,this.naturalWidth=0,this.options=l,this.rect=null,this.$parent=a,this.elRenderer=c,this.performanceData={init:Date.now(),loadStart:0,loadEnd:0},this.filter(),this.initState(),this.render("loading",!1)}return h(t,[{key:"initState",value:function(){this.el.dataset.src=this.src,this.state={error:!1,loaded:!1,rendered:!1}}},{key:"record",value:function(t){this.performanceData[t]=Date.now()}},{key:"update",value:function(t){var e=t.src,i=t.loading,n=t.error,s=this.src;this.src=e,this.loading=i,this.error=n,this.filter(),s!==this.src&&(this.attempt=0,this.initState())}},{key:"getRect",value:function(){this.rect=this.el.getBoundingClientRect()}},{key:"checkInView",value:function(){return this.getRect(),this.rect.top<window.innerHeight*this.options.preLoad&&this.rect.bottom>this.options.preLoadTop&&this.rect.left<window.innerWidth*this.options.preLoad&&this.rect.right>0}},{key:"filter",value:function(){var t=this;(function(t){if(!(t instanceof Object))return[];if(Object.keys)return Object.keys(t);var e=[];for(var i in t)t.hasOwnProperty(i)&&e.push(i);return e})(this.options.filter).map((function(e){t.options.filter[e](t,t.options)}))}},{key:"renderLoading",value:function(t){var e=this;O({src:this.loading},(function(i){e.render("loading",!1),t()}),(function(){t(),e.options.silent||console.warn("VueLazyload log: load failed with loading image("+e.loading+")")}))}},{key:"load",value:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:l;return this.attempt>this.options.attempt-1&&this.state.error?(this.options.silent||console.log("VueLazyload log: "+this.src+" tried too more than "+this.options.attempt+" times"),void e()):this.state.loaded||B[this.src]?(this.state.loaded=!0,e(),this.render("loaded",!0)):void this.renderLoading((function(){t.attempt++,t.record("loadStart"),O({src:t.src},(function(i){t.naturalHeight=i.naturalHeight,t.naturalWidth=i.naturalWidth,t.state.loaded=!0,t.state.error=!1,t.record("loadEnd"),t.render("loaded",!1),B[t.src]=1,e()}),(function(e){!t.options.silent&&console.error(e),t.state.error=!0,t.state.loaded=!1,t.render("error",!1)}))}))}},{key:"render",value:function(t,e){this.elRenderer(this,t,e)}},{key:"performance",value:function(){var t="loading",e=0;return this.state.loaded&&(t="loaded",e=(this.performanceData.loadEnd-this.performanceData.loadStart)/1e3),this.state.error&&(t="error"),{src:this.src,state:t,time:e}}},{key:"destroy",value:function(){this.el=null,this.src=null,this.error=null,this.loading=null,this.bindType=null,this.attempt=0}}]),t}(),j="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",E=["scroll","wheel","mousewheel","resize","animationend","transitionend","touchmove"],D={rootMargin:"0px",threshold:0},N=function(t){return function(){function e(t){var i=t.preLoad,n=t.error,s=t.throttleWait,r=t.preLoadTop,o=t.dispatchEvent,l=t.loading,c=t.attempt,h=t.silent,d=void 0===h||h,f=t.scale,p=t.listenEvents,m=(t.hasbind,t.filter),v=t.adapter,g=t.observer,b=t.observerOptions;u(this,e),this.version="1.2.3",this.mode=y,this.ListenerQueue=[],this.TargetIndex=0,this.TargetQueue=[],this.options={silent:d,dispatchEvent:!!o,throttleWait:s||200,preLoad:i||1.3,preLoadTop:r||0,error:n||j,loading:l||j,attempt:c||3,scale:f||x(f),ListenEvents:p||E,hasbind:!1,supportWebp:a(),filter:m||{},adapter:v||{},observer:!!g,observerOptions:b||D},this._initEvent(),this.lazyLoadHandler=function(t,e){var i=null,n=0;return function(){if(!i){var s=Date.now()-n,r=this,o=arguments,a=function(){n=Date.now(),i=!1,t.apply(r,o)};s>=e?a():i=setTimeout(a,e)}}}(this._lazyLoadHandler.bind(this),this.options.throttleWait),this.setMode(this.options.observer?S:y)}return h(e,[{key:"config",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};v(this.options,t)}},{key:"performance",value:function(){var t=[];return this.ListenerQueue.map((function(e){t.push(e.performance())})),t}},{key:"addLazyBox",value:function(t){this.ListenerQueue.push(t),g&&(this._addListenerTarget(window),this._observer&&this._observer.observe(t.el),t.$el&&t.$el.parentNode&&this._addListenerTarget(t.$el.parentNode))}},{key:"add",value:function(e,i,n){var s=this;if(function(t,e){for(var i=!1,n=0,s=t.length;n<s;n++)if(e(t[n])){i=!0;break}return i}(this.ListenerQueue,(function(t){return t.el===e})))return this.update(e,i),t.nextTick(this.lazyLoadHandler);var o=this._valueFormatter(i.value),a=o.src,l=o.loading,c=o.error;t.nextTick((function(){a=r(e,s.options.scale)||a,s._observer&&s._observer.observe(e);var o=Object.keys(i.modifiers)[0],u=void 0;o&&(u=(u=n.context.$refs[o])?u.$el||u:document.getElementById(o)),u||(u=function(t){if(g){if(!(t instanceof HTMLElement))return window;for(var e=t;e&&e!==document.body&&e!==document.documentElement&&e.parentNode;){if(/(scroll|auto)/.test($(e)))return e;e=e.parentNode}return window}}(e));var h=new I({bindType:i.arg,$parent:u,el:e,loading:l,error:c,src:a,elRenderer:s._elRenderer.bind(s),options:s.options});s.ListenerQueue.push(h),g&&(s._addListenerTarget(window),s._addListenerTarget(u)),s.lazyLoadHandler(),t.nextTick((function(){return s.lazyLoadHandler()}))}))}},{key:"update",value:function(e,i){var n=this,s=this._valueFormatter(i.value),a=s.src,l=s.loading,c=s.error;a=r(e,this.options.scale)||a;var u=o(this.ListenerQueue,(function(t){return t.el===e}));u&&u.update({src:a,loading:l,error:c}),this._observer&&(this._observer.unobserve(e),this._observer.observe(e)),this.lazyLoadHandler(),t.nextTick((function(){return n.lazyLoadHandler()}))}},{key:"remove",value:function(t){if(t){this._observer&&this._observer.unobserve(t);var e=o(this.ListenerQueue,(function(e){return e.el===t}));e&&(this._removeListenerTarget(e.$parent),this._removeListenerTarget(window),s(this.ListenerQueue,e)&&e.destroy())}}},{key:"removeComponent",value:function(t){t&&(s(this.ListenerQueue,t),this._observer&&this._observer.unobserve(t.el),t.$parent&&t.$el.parentNode&&this._removeListenerTarget(t.$el.parentNode),this._removeListenerTarget(window))}},{key:"setMode",value:function(t){var e=this;b||t!==S||(t=y),this.mode=t,t===y?(this._observer&&(this.ListenerQueue.forEach((function(t){e._observer.unobserve(t.el)})),this._observer=null),this.TargetQueue.forEach((function(t){e._initListen(t.el,!0)}))):(this.TargetQueue.forEach((function(t){e._initListen(t.el,!1)})),this._initIntersectionObserver())}},{key:"_addListenerTarget",value:function(t){if(t){var e=o(this.TargetQueue,(function(e){return e.el===t}));return e?e.childrenCount++:(e={el:t,id:++this.TargetIndex,childrenCount:1,listened:!0},this.mode===y&&this._initListen(e.el,!0),this.TargetQueue.push(e)),this.TargetIndex}}},{key:"_removeListenerTarget",value:function(t){var e=this;this.TargetQueue.forEach((function(i,n){i.el===t&&(--i.childrenCount||(e._initListen(i.el,!1),e.TargetQueue.splice(n,1),i=null))}))}},{key:"_initListen",value:function(t,e){var i=this;this.options.ListenEvents.forEach((function(n){return C[e?"on":"off"](t,n,i.lazyLoadHandler)}))}},{key:"_initEvent",value:function(){var t=this;this.Event={listeners:{loading:[],loaded:[],error:[]}},this.$on=function(e,i){t.Event.listeners[e].push(i)},this.$once=function(e,i){var n=t;t.$on(e,(function t(){n.$off(e,t),i.apply(n,arguments)}))},this.$off=function(e,i){i?s(t.Event.listeners[e],i):t.Event.listeners[e]=[]},this.$emit=function(e,i,n){t.Event.listeners[e].forEach((function(t){return t(i,n)}))}}},{key:"_lazyLoadHandler",value:function(){var t=this;this.ListenerQueue.forEach((function(e,i){e.state.loaded||e.checkInView()&&e.load((function(){!e.error&&e.loaded&&t.ListenerQueue.splice(i,1)}))}))}},{key:"_initIntersectionObserver",value:function(){var t=this;b&&(this._observer=new IntersectionObserver(this._observerHandler.bind(this),this.options.observerOptions),this.ListenerQueue.length&&this.ListenerQueue.forEach((function(e){t._observer.observe(e.el)})))}},{key:"_observerHandler",value:function(t,e){var i=this;t.forEach((function(t){t.isIntersecting&&i.ListenerQueue.forEach((function(e){if(e.el===t.target){if(e.state.loaded)return i._observer.unobserve(e.el);e.load()}}))}))}},{key:"_elRenderer",value:function(t,e,i){if(t.el){var n=t.el,s=t.bindType,r=void 0;switch(e){case"loading":r=t.loading;break;case"error":r=t.error;break;default:r=t.src}if(s?n.style[s]='url("'+r+'")':n.getAttribute("src")!==r&&n.setAttribute("src",r),n.setAttribute("lazy",e),this.$emit(e,t,i),this.options.adapter[e]&&this.options.adapter[e](t,this.options),this.options.dispatchEvent){var o=new k(e,{detail:t});n.dispatchEvent(o)}}}},{key:"_valueFormatter",value:function(t){var e=t,i=this.options.loading,n=this.options.error;return function(t){return null!==t&&"object"===(void 0===t?"undefined":c(t))}(t)&&(t.src||this.options.silent||console.error("Vue Lazyload warning: miss src with "+t),e=t.src,i=t.loading||this.options.loading,n=t.error||this.options.error),{src:e,loading:i,error:n}}}]),e}()},L=function(t){return{props:{tag:{type:String,default:"div"}},render:function(t){return!1===this.show?t(this.tag):t(this.tag,null,this.$slots.default)},data:function(){return{el:null,state:{loaded:!1},rect:{},show:!1}},mounted:function(){this.el=this.$el,t.addLazyBox(this),t.lazyLoadHandler()},beforeDestroy:function(){t.removeComponent(this)},methods:{getRect:function(){this.rect=this.$el.getBoundingClientRect()},checkInView:function(){return this.getRect(),g&&this.rect.top<window.innerHeight*t.options.preLoad&&this.rect.bottom>0&&this.rect.left<window.innerWidth*t.options.preLoad&&this.rect.right>0},load:function(){this.show=!0,this.state.loaded=!0,this.$emit("show",this)}}}},A=function(){function t(e){var i=e.lazy;u(this,t),this.lazy=i,i.lazyContainerMananger=this,this._queue=[]}return h(t,[{key:"bind",value:function(t,e,i){var n=new z({el:t,binding:e,vnode:i,lazy:this.lazy});this._queue.push(n)}},{key:"update",value:function(t,e,i){var n=o(this._queue,(function(e){return e.el===t}));n&&n.update({el:t,binding:e,vnode:i})}},{key:"unbind",value:function(t,e,i){var n=o(this._queue,(function(e){return e.el===t}));n&&(n.clear(),s(this._queue,n))}}]),t}(),P={selector:"img"},z=function(){function t(e){var i=e.el,n=e.binding,s=e.vnode,r=e.lazy;u(this,t),this.el=null,this.vnode=s,this.binding=n,this.options={},this.lazy=r,this._queue=[],this.update({el:i,binding:n})}return h(t,[{key:"update",value:function(t){var e=this,i=t.el,n=t.binding;this.el=i,this.options=v({},P,n.value),this.getImgs().forEach((function(t){e.lazy.add(t,v({},e.binding,{value:{src:t.dataset.src,error:t.dataset.error,loading:t.dataset.loading}}),e.vnode)}))}},{key:"getImgs",value:function(){return function(t){for(var e=t.length,i=[],n=0;n<e;n++)i.push(t[n]);return i}(this.el.querySelectorAll(this.options.selector))}},{key:"clear",value:function(){var t=this;this.getImgs().forEach((function(e){return t.lazy.remove(e)})),this.vnode=null,this.binding=null,this.lazy=null}}]),t}();return{install:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=N(t),n=new i(e),s=new A({lazy:n}),r="2"===t.version.split(".")[0];t.prototype.$Lazyload=n,e.lazyComponent&&t.component("lazy-component",L(n)),r?(t.directive("lazy",{bind:n.add.bind(n),update:n.update.bind(n),componentUpdated:n.lazyLoadHandler.bind(n),unbind:n.remove.bind(n)}),t.directive("lazy-container",{bind:s.bind.bind(s),update:s.update.bind(s),unbind:s.unbind.bind(s)})):(t.directive("lazy",{bind:n.lazyLoadHandler.bind(n),update:function(t,e){v(this.vm.$refs,this.vm.$els),n.add(this.el,{modifiers:this.modifiers||{},arg:this.arg,value:t,oldValue:e},{context:this.vm})},unbind:function(){n.remove(this.el)}}),t.directive("lazy-container",{update:function(t,e){s.update(this.el,{modifiers:this.modifiers||{},arg:this.arg,value:t,oldValue:e},{context:this.vm})},unbind:function(){s.unbind(this.el)}}))}}}()},function(t,e){var i;i=function(){return this}();try{i=i||new Function("return this")()}catch(t){"object"==typeof window&&(i=window)}t.exports=i},function(t,e,i){"use strict";function n(){return(n=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var i=arguments[e];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(t[n]=i[n])}return t}).apply(this,arguments)}i.r(e),i.d(e,"install",(function(){return Gc})),i.d(e,"version",(function(){return Qc})),i.d(e,"ActionSheet",(function(){return St})),i.d(e,"AddressEdit",(function(){return ni})),i.d(e,"AddressList",(function(){return Ii})),i.d(e,"Area",(function(){return Ut})),i.d(e,"Button",(function(){return $e})),i.d(e,"Calendar",(function(){return qi})),i.d(e,"Card",(function(){return tn})),i.d(e,"Cell",(function(){return se})),i.d(e,"CellGroup",(function(){return on})),i.d(e,"Checkbox",(function(){return ln})),i.d(e,"CheckboxGroup",(function(){return dn})),i.d(e,"Circle",(function(){return yn})),i.d(e,"Col",(function(){return wn})),i.d(e,"Collapse",(function(){return $n})),i.d(e,"CollapseItem",(function(){return Dn})),i.d(e,"ContactCard",(function(){return Mn})),i.d(e,"ContactEdit",(function(){return Wn})),i.d(e,"ContactList",(function(){return Qn})),i.d(e,"CountDown",(function(){return es})),i.d(e,"Coupon",(function(){return ls})),i.d(e,"CouponCell",(function(){return ms})),i.d(e,"CouponList",(function(){return Rs})),i.d(e,"DatetimePicker",(function(){return Qs})),i.d(e,"Dialog",(function(){return Le})),i.d(e,"Divider",(function(){return er})),i.d(e,"DropdownItem",(function(){return rr})),i.d(e,"DropdownMenu",(function(){return ur})),i.d(e,"Empty",(function(){return vr})),i.d(e,"Field",(function(){return le})),i.d(e,"Form",(function(){return Sr})),i.d(e,"GoodsAction",(function(){return Cr})),i.d(e,"GoodsActionButton",(function(){return Br})),i.d(e,"GoodsActionIcon",(function(){return Dr})),i.d(e,"Grid",(function(){return Pr})),i.d(e,"GridItem",(function(){return Vr})),i.d(e,"Icon",(function(){return st})),i.d(e,"Image",(function(){return Xi})),i.d(e,"ImagePreview",(function(){return no})),i.d(e,"IndexAnchor",(function(){return ao})),i.d(e,"IndexBar",(function(){return ho})),i.d(e,"Info",(function(){return Z})),i.d(e,"Lazyload",(function(){return po})),i.d(e,"List",(function(){return yo})),i.d(e,"Loading",(function(){return mt})),i.d(e,"Locale",(function(){return So.a})),i.d(e,"NavBar",(function(){return Co})),i.d(e,"NoticeBar",(function(){return Bo})),i.d(e,"Notify",(function(){return zo})),i.d(e,"NumberKeyboard",(function(){return Uo})),i.d(e,"Overlay",(function(){return $})),i.d(e,"Pagination",(function(){return Jo})),i.d(e,"Panel",(function(){return sa})),i.d(e,"PasswordInput",(function(){return ca})),i.d(e,"Picker",(function(){return _t})),i.d(e,"Popup",(function(){return lt})),i.d(e,"Progress",(function(){return fa})),i.d(e,"PullRefresh",(function(){return ya})),i.d(e,"Radio",(function(){return bi})),i.d(e,"RadioGroup",(function(){return ui})),i.d(e,"Rate",(function(){return wa})),i.d(e,"Row",(function(){return $a})),i.d(e,"Search",(function(){return Na})),i.d(e,"ShareSheet",(function(){return Fa})),i.d(e,"Sidebar",(function(){return Ha})),i.d(e,"SidebarItem",(function(){return Ua})),i.d(e,"Skeleton",(function(){return Za})),i.d(e,"Sku",(function(){return pc})),i.d(e,"Slider",(function(){return bc})),i.d(e,"Step",(function(){return xc})),i.d(e,"Stepper",(function(){return jl})),i.d(e,"Steps",(function(){return Tc})),i.d(e,"Sticky",(function(){return Bs})),i.d(e,"SubmitBar",(function(){return Dc})),i.d(e,"Swipe",(function(){return Wr})),i.d(e,"SwipeCell",(function(){return Pc})),i.d(e,"SwipeItem",(function(){return Kr})),i.d(e,"Switch",(function(){return Ye})),i.d(e,"SwitchCell",(function(){return Ge})),i.d(e,"Tab",(function(){return ys})),i.d(e,"Tabbar",(function(){return Vc})),i.d(e,"TabbarItem",(function(){return Wc})),i.d(e,"Tabs",(function(){return Ps})),i.d(e,"Tag",(function(){return mi})),i.d(e,"Toast",(function(){return ke})),i.d(e,"TreeSelect",(function(){return Xc})),i.d(e,"Uploader",(function(){return ql}));var s=i(1),r=i.n(s),o=i(9),a=i(2),l=i.n(a),c=["ref","style","class","attrs","nativeOn","directives","staticClass","staticStyle"],u={nativeOn:"on"};function h(t,e){var i=c.reduce((function(e,i){return t.data[i]&&(e[u[i]||i]=t.data[i]),e}),{});return e&&(i.on=i.on||{},n(i.on,t.data.on)),i}function d(t,e){for(var i=arguments.length,n=new Array(i>2?i-2:0),s=2;s<i;s++)n[s-2]=arguments[s];var r=t.listeners[e];r&&(Array.isArray(r)?r.forEach((function(t){t.apply(void 0,n)})):r.apply(void 0,n))}function f(t,e){var i=new l.a({el:document.createElement("div"),props:t.props,render:function(i){return i(t,n({props:this.$props},e))}});return document.body.appendChild(i.$el),i}var p={zIndex:2e3,lockCount:0,stack:[],find:function(t){return this.stack.filter((function(e){return e.vm===t}))[0]}},m=i(0),v=!1;if(!m.f)try{var g={};Object.defineProperty(g,"passive",{get:function(){v=!0}}),window.addEventListener("test-passive",null,g)}catch(t){}function b(t,e,i,n){void 0===n&&(n=!1),m.f||t.addEventListener(e,i,!!v&&{capture:!1,passive:n})}function y(t,e,i){m.f||t.removeEventListener(e,i)}function S(t){t.stopPropagation()}function k(t,e){("boolean"!=typeof t.cancelable||t.cancelable)&&t.preventDefault(),e&&S(t)}var x=Object(o.a)("overlay"),w=x[0],C=x[1];function O(t){k(t,!0)}function T(t,e,i,s){var o=n({zIndex:e.zIndex},e.customStyle);return Object(m.b)(e.duration)&&(o.animationDuration=e.duration+"s"),t("transition",{attrs:{name:"van-fade"}},[t("div",r()([{directives:[{name:"show",value:e.show}],style:o,class:[C(),e.className],on:{touchmove:e.lockScroll?O:m.g}},h(s,!0)]),[null==i.default?void 0:i.default()])])}T.props={show:Boolean,zIndex:[Number,String],duration:[Number,String],className:null,customStyle:Object,lockScroll:{type:Boolean,default:!0}};var $=w(T);function B(t){var e=t.parentNode;e&&e.removeChild(t)}var I={className:"",customStyle:{}};function j(t){var e=p.find(t);if(e){var i=t.$el,s=e.config,r=e.overlay;i&&i.parentNode&&i.parentNode.insertBefore(r.$el,i),n(r,I,s,{show:!0})}}function E(t,e){var i=p.find(t);if(i)i.config=e;else{var n=function(t){return f($,{on:{click:function(){t.$emit("click-overlay"),t.closeOnClickOverlay&&(t.onClickOverlay?t.onClickOverlay():t.close())}}})}(t);p.stack.push({vm:t,config:e,overlay:n})}j(t)}function D(t){var e=p.find(t);e&&(e.overlay.show=!1)}function N(t){return t===window}var L=/scroll|auto/i;function A(t,e){void 0===e&&(e=window);for(var i=t;i&&"HTML"!==i.tagName&&1===i.nodeType&&i!==e;){var n=window.getComputedStyle(i).overflowY;if(L.test(n)){if("BODY"!==i.tagName)return i;var s=window.getComputedStyle(i.parentNode).overflowY;if(L.test(s))return i}i=i.parentNode}return e}function P(t){return"scrollTop"in t?t.scrollTop:t.pageYOffset}function z(t,e){"scrollTop"in t?t.scrollTop=e:t.scrollTo(t.scrollX,e)}function M(){return window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0}function F(t){z(window,t),z(document.body,t)}function V(t,e){if(N(t))return 0;var i=e?P(e):M();return t.getBoundingClientRect().top+i}var R=l.a.extend({data:function(){return{direction:""}},methods:{touchStart:function(t){this.resetTouchStatus(),this.startX=t.touches[0].clientX,this.startY=t.touches[0].clientY},touchMove:function(t){var e,i,n=t.touches[0];this.deltaX=n.clientX-this.startX,this.deltaY=n.clientY-this.startY,this.offsetX=Math.abs(this.deltaX),this.offsetY=Math.abs(this.deltaY),this.direction=this.direction||(e=this.offsetX,i=this.offsetY,e>i&&e>10?"horizontal":i>e&&i>10?"vertical":"")},resetTouchStatus:function(){this.direction="",this.deltaX=0,this.deltaY=0,this.offsetX=0,this.offsetY=0},bindTouchEvent:function(t){var e=this.onTouchStart,i=this.onTouchMove,n=this.onTouchEnd;b(t,"touchstart",e),b(t,"touchmove",i),n&&(b(t,"touchend",n),b(t,"touchcancel",n))}}});function _(t){var e=t.ref,i=t.afterPortal;return l.a.extend({props:{getContainer:[String,Function]},watch:{getContainer:"portal"},mounted:function(){this.getContainer&&this.portal()},methods:{portal:function(){var t,n,s=this.getContainer,r=e?this.$refs[e]:this.$el;s?t="string"==typeof(n=s)?document.querySelector(n):n():this.$parent&&(t=this.$parent.$el),t&&t!==r.parentNode&&t.appendChild(r),i&&i.call(this)}}})}function H(t){function e(){this.binded||(t.call(this,b,!0),this.binded=!0)}function i(){this.binded&&(t.call(this,y,!1),this.binded=!1)}return{mounted:e,activated:e,deactivated:i,beforeDestroy:i}}var W={mixins:[H((function(t,e){this.handlePopstate(e&&this.closeOnPopstate)}))],props:{closeOnPopstate:Boolean},data:function(){return{bindStatus:!1}},watch:{closeOnPopstate:function(t){this.handlePopstate(t)}},methods:{handlePopstate:function(t){var e=this;this.$isServer||this.bindStatus!==t&&(this.bindStatus=t,(t?b:y)(window,"popstate",(function(){e.close(),e.shouldReopen=!1})))}}},q={value:Boolean,overlay:Boolean,overlayStyle:Object,overlayClass:String,closeOnClickOverlay:Boolean,zIndex:[Number,String],lockScroll:{type:Boolean,default:!0},lazyRender:{type:Boolean,default:!0}};function Y(t){return void 0===t&&(t={}),{mixins:[R,W,_({afterPortal:function(){this.overlay&&j()}})],props:q,data:function(){return{inited:this.value}},computed:{shouldRender:function(){return this.inited||!this.lazyRender}},watch:{value:function(e){var i=e?"open":"close";this.inited=this.inited||this.value,this[i](),t.skipToggleEvent||this.$emit(i)},overlay:"renderOverlay"},mounted:function(){this.value&&this.open()},activated:function(){this.shouldReopen&&(this.$emit("input",!0),this.shouldReopen=!1)},beforeDestroy:function(){var t,e;this.removeLock(),t=this,(e=p.find(t))&&B(e.overlay.$el),this.getContainer&&B(this.$el)},deactivated:function(){this.value&&(this.close(),this.shouldReopen=!0)},methods:{open:function(){this.$isServer||this.opened||(void 0!==this.zIndex&&(p.zIndex=this.zIndex),this.opened=!0,this.renderOverlay(),this.addLock())},addLock:function(){this.lockScroll&&(b(document,"touchstart",this.touchStart),b(document,"touchmove",this.onTouchMove),p.lockCount||document.body.classList.add("van-overflow-hidden"),p.lockCount++)},removeLock:function(){this.lockScroll&&(p.lockCount--,y(document,"touchstart",this.touchStart),y(document,"touchmove",this.onTouchMove),p.lockCount||document.body.classList.remove("van-overflow-hidden"))},close:function(){this.opened&&(D(this),this.opened=!1,this.removeLock(),this.$emit("input",!1))},onTouchMove:function(t){this.touchMove(t);var e=this.deltaY>0?"10":"01",i=A(t.target,this.$el),n=i.scrollHeight,s=i.offsetHeight,r=i.scrollTop,o="11";0===r?o=s>=n?"00":"01":r+s>=n&&(o="10"),"11"===o||"vertical"!==this.direction||parseInt(o,2)&parseInt(e,2)||k(t,!0)},renderOverlay:function(){var t=this;!this.$isServer&&this.value&&this.$nextTick((function(){t.updateZIndex(t.overlay?1:0),t.overlay?E(t,{zIndex:p.zIndex++,duration:t.duration,className:t.overlayClass,customStyle:t.overlayStyle}):D(t)}))},updateZIndex:function(t){void 0===t&&(t=0),this.$el.style.zIndex=++p.zIndex+t}}}}var U=i(8),K=Object(o.a)("info"),X=K[0],Q=K[1];function G(t,e,i,n){var s=e.dot,o=e.info,a=Object(m.b)(o)&&""!==o;if(s||a)return t("div",r()([{class:Q({dot:s})},h(n,!0)]),[s?"":e.info])}G.props={dot:Boolean,info:[Number,String]};var Z=X(G),J=Object(o.a)("icon"),tt=J[0],et=J[1];var it={medel:"medal","medel-o":"medal-o"};function nt(t,e,i,n){var s=function(t){return t&&it[t]||t}(e.name),o=function(t){return!!t&&-1!==t.indexOf("/")}(s);return t(e.tag,r()([{class:[e.classPrefix,o?"":e.classPrefix+"-"+s],style:{color:e.color,fontSize:Object(U.a)(e.size)}},h(n,!0)]),[i.default&&i.default(),o&&t("img",{class:et("image"),attrs:{src:s}}),t(Z,{attrs:{dot:e.dot,info:Object(m.b)(e.badge)?e.badge:e.info}})])}nt.props={dot:Boolean,name:String,size:[Number,String],info:[Number,String],badge:[Number,String],color:String,tag:{type:String,default:"i"},classPrefix:{type:String,default:et()}};var st=tt(nt),rt=Object(o.a)("popup"),ot=rt[0],at=rt[1],lt=ot({mixins:[Y()],props:{round:Boolean,duration:[Number,String],closeable:Boolean,transition:String,safeAreaInsetBottom:Boolean,closeIcon:{type:String,default:"cross"},closeIconPosition:{type:String,default:"top-right"},position:{type:String,default:"center"},overlay:{type:Boolean,default:!0},closeOnClickOverlay:{type:Boolean,default:!0}},beforeCreate:function(){var t=this,e=function(e){return function(i){return t.$emit(e,i)}};this.onClick=e("click"),this.onOpened=e("opened"),this.onClosed=e("closed")},render:function(){var t,e=arguments[0];if(this.shouldRender){var i=this.round,n=this.position,s=this.duration,r="center"===n,o=this.transition||(r?"van-fade":"van-popup-slide-"+n),a={};if(Object(m.b)(s)){var l=r?"animationDuration":"transitionDuration";a[l]=s+"s"}return e("transition",{attrs:{name:o},on:{afterEnter:this.onOpened,afterLeave:this.onClosed}},[e("div",{directives:[{name:"show",value:this.value}],style:a,class:at((t={round:i},t[n]=n,t["safe-area-inset-bottom"]=this.safeAreaInsetBottom,t)),on:{click:this.onClick}},[this.slots(),this.closeable&&e(st,{attrs:{role:"button",tabindex:"0",name:this.closeIcon},class:at("close-icon",this.closeIconPosition),on:{click:this.close}})])])}}}),ct=Object(o.a)("loading"),ut=ct[0],ht=ct[1];function dt(t,e){if("spinner"===e.type){for(var i=[],n=0;n<12;n++)i.push(t("i"));return i}return t("svg",{class:ht("circular"),attrs:{viewBox:"25 25 50 50"}},[t("circle",{attrs:{cx:"50",cy:"50",r:"20",fill:"none"}})])}function ft(t,e,i){if(i.default){var n=e.textSize&&{fontSize:Object(U.a)(e.textSize)};return t("span",{class:ht("text"),style:n},[i.default()])}}function pt(t,e,i,n){var s=e.color,o=e.size,a=e.type,l={color:s};if(o){var c=Object(U.a)(o);l.width=c,l.height=c}return t("div",r()([{class:ht([a,{vertical:e.vertical}])},h(n,!0)]),[t("span",{class:ht("spinner",a),style:l},[dt(t,e)]),ft(t,e,i)])}pt.props={color:String,size:[Number,String],vertical:Boolean,textSize:[Number,String],type:{type:String,default:"circular"}};var mt=ut(pt),vt=Object(o.a)("action-sheet"),gt=vt[0],bt=vt[1];function yt(t,e,i,n){var s=e.title,o=e.cancelText;function a(){d(n,"input",!1),d(n,"cancel")}var l=e.description&&t("div",{class:bt("description")},[e.description]);return t(lt,r()([{class:bt(),attrs:{position:"bottom",round:e.round,value:e.value,overlay:e.overlay,duration:e.duration,lazyRender:e.lazyRender,lockScroll:e.lockScroll,getContainer:e.getContainer,closeOnPopstate:e.closeOnPopstate,closeOnClickOverlay:e.closeOnClickOverlay,safeAreaInsetBottom:e.safeAreaInsetBottom}},h(n,!0)]),[function(){if(s)return t("div",{class:bt("header")},[s,t(st,{attrs:{name:e.closeIcon},class:bt("close"),on:{click:a}})])}(),l,e.actions&&e.actions.map((function(i,s){var r=i.disabled,o=i.loading,a=i.callback;return t("button",{attrs:{type:"button"},class:[bt("item",{disabled:r,loading:o}),i.className],style:{color:i.color},on:{click:function(t){t.stopPropagation(),r||o||(a&&a(i),d(n,"select",i,s),e.closeOnClickAction&&d(n,"input",!1))}}},[o?t(mt,{attrs:{size:"20px"}}):[t("span",{class:bt("name")},[i.name]),i.subname&&t("span",{class:bt("subname")},[i.subname])]])})),function(){if(i.default)return t("div",{class:bt("content")},[i.default()])}(),function(){if(o)return[t("div",{class:bt("gap")}),t("button",{attrs:{type:"button"},class:bt("cancel"),on:{click:a}},[o])]}()])}yt.props=n(n({},q),{},{title:String,actions:Array,duration:[Number,String],cancelText:String,description:String,getContainer:[String,Function],closeOnPopstate:Boolean,closeOnClickAction:Boolean,round:{type:Boolean,default:!0},closeIcon:{type:String,default:"cross"},safeAreaInsetBottom:{type:Boolean,default:!0},overlay:{type:Boolean,default:!0},closeOnClickOverlay:{type:Boolean,default:!0}});var St=gt(yt);function kt(t){return t=t.replace(/[^-|\d]/g,""),/^((\+86)|(86))?(1)\d{10}$/.test(t)||/^0[0-9-]{10,13}$/.test(t)}var xt={title:String,loading:Boolean,showToolbar:Boolean,cancelButtonText:String,confirmButtonText:String,allowHtml:{type:Boolean,default:!0},visibleItemCount:{type:[Number,String],default:5},itemHeight:{type:[Number,String],default:44},swipeDuration:{type:[Number,String],default:1e3}},wt="#ee0a24",Ct="van-hairline",Ot=Ct+"--top",Tt=Ct+"--bottom",$t=Ct+"--top-bottom",Bt=i(7);function It(t){return Array.isArray(t)?t.map((function(t){return It(t)})):"object"==typeof t?Object(Bt.a)({},t):t}function jt(t,e,i){return Math.min(Math.max(t,e),i)}function Et(t,e,i){var n=t.indexOf(e);return-1===n?t:"-"===e&&0!==n?t.slice(0,n):t.slice(0,n+1)+t.slice(n).replace(i,"")}function Dt(t,e){t=e?Et(t,".",/\./g):t.split(".")[0];var i=e?/[^-0-9.]/g:/[^-0-9]/g;return(t=Et(t,"-",/-/g)).replace(i,"")}var Nt=Object(o.a)("picker-column"),Lt=Nt[0],At=Nt[1];function Pt(t){return Object(m.d)(t)&&t.disabled}var zt=Lt({mixins:[R],props:{valueKey:String,allowHtml:Boolean,className:String,itemHeight:[Number,String],defaultIndex:Number,swipeDuration:[Number,String],visibleItemCount:[Number,String],initialOptions:{type:Array,default:function(){return[]}}},data:function(){return{offset:0,duration:0,options:It(this.initialOptions),currentIndex:this.defaultIndex}},created:function(){this.$parent.children&&this.$parent.children.push(this),this.setIndex(this.currentIndex)},mounted:function(){this.bindTouchEvent(this.$el)},destroyed:function(){var t=this.$parent.children;t&&t.splice(t.indexOf(this),1)},watch:{initialOptions:"setOptions",defaultIndex:function(t){this.setIndex(t)}},computed:{count:function(){return this.options.length},baseOffset:function(){return this.itemHeight*(this.visibleItemCount-1)/2}},methods:{setOptions:function(t){JSON.stringify(t)!==JSON.stringify(this.options)&&(this.options=It(t),this.setIndex(this.defaultIndex))},onTouchStart:function(t){if(this.touchStart(t),this.moving){var e=function(t){var e=window.getComputedStyle(t),i=e.transform||e.webkitTransform,n=i.slice(7,i.length-1).split(", ")[5];return Number(n)}(this.$refs.wrapper);this.offset=Math.min(0,e-this.baseOffset),this.startOffset=this.offset}else this.startOffset=this.offset;this.duration=0,this.transitionEndTrigger=null,this.touchStartTime=Date.now(),this.momentumOffset=this.startOffset},onTouchMove:function(t){this.touchMove(t),"vertical"===this.direction&&(this.moving=!0,k(t,!0)),this.offset=jt(this.startOffset+this.deltaY,-this.count*this.itemHeight,this.itemHeight);var e=Date.now();e-this.touchStartTime>300&&(this.touchStartTime=e,this.momentumOffset=this.offset)},onTouchEnd:function(){var t=this,e=this.offset-this.momentumOffset,i=Date.now()-this.touchStartTime;if(i<300&&Math.abs(e)>15)this.momentum(e,i);else{var n=this.getIndexByOffset(this.offset);this.duration=200,this.setIndex(n,!0),setTimeout((function(){t.moving=!1}),0)}},onTransitionEnd:function(){this.stopMomentum()},onClickItem:function(t){this.moving||(this.duration=200,this.setIndex(t,!0))},adjustIndex:function(t){for(var e=t=jt(t,0,this.count);e<this.count;e++)if(!Pt(this.options[e]))return e;for(var i=t-1;i>=0;i--)if(!Pt(this.options[i]))return i},getOptionText:function(t){return Object(m.d)(t)&&this.valueKey in t?t[this.valueKey]:t},setIndex:function(t,e){var i=this,n=-(t=this.adjustIndex(t)||0)*this.itemHeight,s=function(){t!==i.currentIndex&&(i.currentIndex=t,e&&i.$emit("change",t))};this.moving&&n!==this.offset?this.transitionEndTrigger=s:s(),this.offset=n},setValue:function(t){for(var e=this.options,i=0;i<e.length;i++)if(this.getOptionText(e[i])===t)return this.setIndex(i)},getValue:function(){return this.options[this.currentIndex]},getIndexByOffset:function(t){return jt(Math.round(-t/this.itemHeight),0,this.count-1)},momentum:function(t,e){var i=Math.abs(t/e);t=this.offset+i/.003*(t<0?-1:1);var n=this.getIndexByOffset(t);this.duration=+this.swipeDuration,this.setIndex(n,!0)},stopMomentum:function(){this.moving=!1,this.duration=0,this.transitionEndTrigger&&(this.transitionEndTrigger(),this.transitionEndTrigger=null)},genOptions:function(){var t=this,e=this.$createElement,i={height:this.itemHeight+"px"};return this.options.map((function(n,s){var o,a=t.getOptionText(n),l=Pt(n),c={style:i,attrs:{role:"button",tabindex:l?-1:0},class:[At("item",{disabled:l,selected:s===t.currentIndex})],on:{click:function(){t.onClickItem(s)}}},u={class:"van-ellipsis",domProps:(o={},o[t.allowHtml?"innerHTML":"innerText"]=a,o)};return e("li",r()([{},c]),[e("div",r()([{},u]))])}))}},render:function(){var t=arguments[0],e={transform:"translate3d(0, "+(this.offset+this.baseOffset)+"px, 0)",transitionDuration:this.duration+"ms",transitionProperty:this.duration?"all":"none"};return t("div",{class:[At(),this.className]},[t("ul",{ref:"wrapper",style:e,class:At("wrapper"),on:{transitionend:this.onTransitionEnd}},[this.genOptions()])])}}),Mt=Object(o.a)("picker"),Ft=Mt[0],Vt=Mt[1],Rt=Mt[2],_t=Ft({props:n(n({},xt),{},{defaultIndex:{type:[Number,String],default:0},columns:{type:Array,default:function(){return[]}},toolbarPosition:{type:String,default:"top"},valueKey:{type:String,default:"text"}}),data:function(){return{children:[],formattedColumns:[]}},computed:{dataType:function(){var t=this.columns[0]||{};return t.children?"cascade":t.values?"object":"text"}},watch:{columns:{handler:"format",immediate:!0}},methods:{format:function(){var t=this.columns,e=this.dataType;"text"===e?this.formattedColumns=[{values:t}]:"cascade"===e?this.formatCascade():this.formattedColumns=t},formatCascade:function(){for(var t=this,e=[],i={children:this.columns};i&&i.children;){var n=i.defaultIndex||+this.defaultIndex;e.push({values:i.children.map((function(e){return e[t.valueKey]})),className:i.className,defaultIndex:n}),i=i.children[n]}this.formattedColumns=e},emit:function(t){"text"===this.dataType?this.$emit(t,this.getColumnValue(0),this.getColumnIndex(0)):this.$emit(t,this.getValues(),this.getIndexes())},onCascadeChange:function(t){for(var e={children:this.columns},i=this.getIndexes(),n=0;n<=t;n++)e=e.children[i[n]];for(;e&&e.children;)t++,this.setColumnValues(t,e.children),e=e.children[e.defaultIndex||0]},onChange:function(t){"cascade"===this.dataType&&this.onCascadeChange(t),"text"===this.dataType?this.$emit("change",this,this.getColumnValue(0),this.getColumnIndex(0)):this.$emit("change",this,this.getValues(),t)},getColumn:function(t){return this.children[t]},getColumnValue:function(t){var e=this.getColumn(t);return e&&e.getValue()},setColumnValue:function(t,e){var i=this.getColumn(t);i&&(i.setValue(e),"cascade"===this.dataType&&this.onCascadeChange(t))},getColumnIndex:function(t){return(this.getColumn(t)||{}).currentIndex},setColumnIndex:function(t,e){var i=this.getColumn(t);i&&(i.setIndex(e),"cascade"===this.dataType&&this.onCascadeChange(t))},getColumnValues:function(t){return(this.children[t]||{}).options},setColumnValues:function(t,e){var i=this,n=this.children[t];n&&("cascade"===this.dataType?n.setOptions(e.map((function(t){return Object(m.d)(t)?t[i.valueKey]:t}))):n.setOptions(e))},getValues:function(){return this.children.map((function(t){return t.getValue()}))},setValues:function(t){var e=this;t.forEach((function(t,i){e.setColumnValue(i,t)}))},getIndexes:function(){return this.children.map((function(t){return t.currentIndex}))},setIndexes:function(t){var e=this;t.forEach((function(t,i){e.setColumnIndex(i,t)}))},confirm:function(){this.children.forEach((function(t){return t.stopMomentum()})),this.emit("confirm")},cancel:function(){this.emit("cancel")},genTitle:function(){var t=this.$createElement,e=this.slots("title");return e||(this.title?t("div",{class:["van-ellipsis",Vt("title")]},[this.title]):void 0)},genToolbar:function(){var t=this.$createElement;if(this.showToolbar)return t("div",{class:Vt("toolbar")},[this.slots()||[t("button",{attrs:{type:"button"},class:Vt("cancel"),on:{click:this.cancel}},[this.cancelButtonText||Rt("cancel")]),this.genTitle(),t("button",{attrs:{type:"button"},class:Vt("confirm"),on:{click:this.confirm}},[this.confirmButtonText||Rt("confirm")])]])},genColumns:function(){var t=this,e=this.$createElement;return this.formattedColumns.map((function(i,n){return e(zt,{attrs:{valueKey:t.valueKey,allowHtml:t.allowHtml,className:i.className,itemHeight:t.itemHeight,defaultIndex:i.defaultIndex||+t.defaultIndex,swipeDuration:t.swipeDuration,visibleItemCount:t.visibleItemCount,initialOptions:i.values},on:{change:function(){t.onChange(n)}}})}))}},render:function(t){var e=+this.itemHeight,i=e*this.visibleItemCount,n={height:e+"px"},s={height:i+"px"},r={backgroundSize:"100% "+(i-e)/2+"px"};return t("div",{class:Vt()},["top"===this.toolbarPosition?this.genToolbar():t(),this.loading?t(mt,{class:Vt("loading")}):t(),this.slots("columns-top"),t("div",{class:Vt("columns"),style:s,on:{touchmove:k}},[this.genColumns(),t("div",{class:Vt("mask"),style:r}),t("div",{class:["van-hairline-unset--top-bottom",Vt("frame")],style:n})]),this.slots("columns-bottom"),"bottom"===this.toolbarPosition?this.genToolbar():t()])}}),Ht=Object(o.a)("area"),Wt=Ht[0],qt=Ht[1];function Yt(t,e){var i=t.$slots,n=t.$scopedSlots,s={};return e.forEach((function(t){n[t]?s[t]=n[t]:i[t]&&(s[t]=function(){return i[t]})})),s}var Ut=Wt({props:n(n({},xt),{},{value:String,areaList:{type:Object,default:function(){return{}}},columnsNum:{type:[Number,String],default:3},isOverseaCode:{type:Function,default:function(t){return"9"===t[0]}},columnsPlaceholder:{type:Array,default:function(){return[]}}}),data:function(){return{code:this.value,columns:[{values:[]},{values:[]},{values:[]}]}},computed:{province:function(){return this.areaList.province_list||{}},city:function(){return this.areaList.city_list||{}},county:function(){return this.areaList.county_list||{}},displayColumns:function(){return this.columns.slice(0,+this.columnsNum)},placeholderMap:function(){return{province:this.columnsPlaceholder[0]||"",city:this.columnsPlaceholder[1]||"",county:this.columnsPlaceholder[2]||""}}},watch:{value:function(t){this.code=t,this.setValues()},areaList:{deep:!0,handler:"setValues"},columnsNum:function(){var t=this;this.$nextTick((function(){t.setValues()}))}},mounted:function(){this.setValues()},methods:{getList:function(t,e){var i=[];if("province"!==t&&!e)return i;var n=this[t];if(i=Object.keys(n).map((function(t){return{code:t,name:n[t]}})),e&&(this.isOverseaCode(e)&&"city"===t&&(e="9"),i=i.filter((function(t){return 0===t.code.indexOf(e)}))),this.placeholderMap[t]&&i.length){var s="";"city"===t?s="000000".slice(2,4):"county"===t&&(s="000000".slice(4,6)),i.unshift({code:""+e+s,name:this.placeholderMap[t]})}return i},getIndex:function(t,e){var i="province"===t?2:"city"===t?4:6,n=this.getList(t,e.slice(0,i-2));this.isOverseaCode(e)&&"province"===t&&(i=1),e=e.slice(0,i);for(var s=0;s<n.length;s++)if(n[s].code.slice(0,i)===e)return s;return 0},parseOutputValues:function(t){var e=this;return t.map((function(t,i){return t?((t=JSON.parse(JSON.stringify(t))).code&&t.name!==e.columnsPlaceholder[i]||(t.code="",t.name=""),t):t}))},onChange:function(t,e,i){this.code=e[i].code,this.setValues();var n=t.getValues();n=this.parseOutputValues(n),this.$emit("change",t,n,i)},onConfirm:function(t,e){t=this.parseOutputValues(t),this.setValues(),this.$emit("confirm",t,e)},getDefaultCode:function(){if(this.columnsPlaceholder.length)return"000000";var t=Object.keys(this.county);if(t[0])return t[0];var e=Object.keys(this.city);return e[0]?e[0]:""},setValues:function(){var t=this.code;t||(t=this.getDefaultCode());var e=this.$refs.picker,i=this.getList("province"),n=this.getList("city",t.slice(0,2));e&&(e.setColumnValues(0,i),e.setColumnValues(1,n),n.length&&"00"===t.slice(2,4)&&!this.isOverseaCode(t)&&(t=n[0].code),e.setColumnValues(2,this.getList("county",t.slice(0,4))),e.setIndexes([this.getIndex("province",t),this.getIndex("city",t),this.getIndex("county",t)]))},getValues:function(){var t=this.$refs.picker,e=t?t.getValues().filter((function(t){return!!t})):[];return e=this.parseOutputValues(e),e},getArea:function(){var t=this.getValues(),e={code:"",country:"",province:"",city:"",county:""};if(!t.length)return e;var i=t.map((function(t){return t.name})),n=t.filter((function(t){return!!t.code}));return e.code=n.length?n[n.length-1].code:"",this.isOverseaCode(e.code)?(e.country=i[1]||"",e.province=i[2]||""):(e.province=i[0]||"",e.city=i[1]||"",e.county=i[2]||""),e},reset:function(t){this.code=t||"",this.setValues()}},render:function(){var t=arguments[0],e=n(n({},this.$listeners),{},{change:this.onChange,confirm:this.onConfirm});return t(_t,{ref:"picker",class:qt(),attrs:{showToolbar:!0,valueKey:"name",title:this.title,loading:this.loading,columns:this.displayColumns,itemHeight:this.itemHeight,swipeDuration:this.swipeDuration,visibleItemCount:this.visibleItemCount,cancelButtonText:this.cancelButtonText,confirmButtonText:this.confirmButtonText},scopedSlots:Yt(this,["title","columns-top","columns-bottom"]),on:n({},e)})}});var Kt=!m.f&&/ios|iphone|ipad|ipod/.test(navigator.userAgent.toLowerCase());function Xt(){Kt&&F(M())}function Qt(t,e){var i=e.to,n=e.url,s=e.replace;if(i&&t){var r=t[s?"replace":"push"](i);r&&r.catch&&r.catch((function(t){if(t&&"NavigationDuplicated"!==t.name)throw t}))}else n&&(s?location.replace(n):location.href=n)}function Gt(t){Qt(t.parent&&t.parent.$router,t.props)}var Zt={url:String,replace:Boolean,to:[String,Object]},Jt={icon:String,size:String,center:Boolean,isLink:Boolean,required:Boolean,clickable:Boolean,iconPrefix:String,titleStyle:null,titleClass:null,valueClass:null,labelClass:null,title:[Number,String],value:[Number,String],label:[Number,String],arrowDirection:String,border:{type:Boolean,default:!0}},te=Object(o.a)("cell"),ee=te[0],ie=te[1];function ne(t,e,i,n){var s=e.icon,o=e.size,a=e.title,l=e.label,c=e.value,u=e.isLink,f=i.title||Object(m.b)(a);function p(){if(i.label||Object(m.b)(l))return t("div",{class:[ie("label"),e.labelClass]},[i.label?i.label():l])}var v=u||e.clickable,g={clickable:v,center:e.center,required:e.required,borderless:!e.border};return o&&(g[o]=o),t("div",r()([{class:ie(g),attrs:{role:v?"button":null,tabindex:v?0:null},on:{click:function(t){d(n,"click",t),Gt(n)}}},h(n)]),[i.icon?i.icon():s?t(st,{class:ie("left-icon"),attrs:{name:s,classPrefix:e.iconPrefix}}):void 0,function(){if(f)return t("div",{class:[ie("title"),e.titleClass],style:e.titleStyle},[i.title?i.title():t("span",[a]),p()])}(),function(){if(i.default||Object(m.b)(c))return t("div",{class:[ie("value",{alone:!f}),e.valueClass]},[i.default?i.default():t("span",[c])])}(),function(){var n=i["right-icon"];if(n)return n();if(u){var s=e.arrowDirection;return t(st,{class:ie("right-icon"),attrs:{name:s?"arrow-"+s:"arrow"}})}}(),null==i.extra?void 0:i.extra()])}ne.props=n(n({},Jt),Zt);var se=ee(ne),re=Object(o.a)("field"),oe=re[0],ae=re[1],le=oe({inheritAttrs:!1,provide:function(){return{vanField:this}},inject:{vanForm:{default:null}},props:n(n({},Jt),{},{name:String,rules:Array,disabled:Boolean,readonly:Boolean,autosize:[Boolean,Object],leftIcon:String,rightIcon:String,clearable:Boolean,formatter:Function,maxlength:[Number,String],labelWidth:[Number,String],labelClass:null,labelAlign:String,inputAlign:String,placeholder:String,errorMessage:String,errorMessageAlign:String,showWordLimit:Boolean,type:{type:String,default:"text"},error:{type:Boolean,default:null},colon:{type:Boolean,default:null}}),data:function(){return{focused:!1,validateMessage:""}},watch:{value:function(){this.resetValidation(),this.validateWithTrigger("onChange"),this.$nextTick(this.adjustSize)}},mounted:function(){this.format(),this.$nextTick(this.adjustSize),this.vanForm&&this.vanForm.addField(this)},beforeDestroy:function(){this.vanForm&&this.vanForm.removeField(this)},computed:{showClear:function(){return this.clearable&&this.focused&&""!==this.value&&Object(m.b)(this.value)&&!this.readonly},showError:function(){return null!==this.error?this.error:!!(this.vanForm&&this.vanForm.showError&&this.validateMessage)||void 0},listeners:function(){return n(n({},this.$listeners),{},{blur:this.onBlur,focus:this.onFocus,input:this.onInput,click:this.onClickInput,keypress:this.onKeypress})},labelStyle:function(){var t=this.getProp("labelWidth");if(t)return{width:Object(U.a)(t)}},formValue:function(){return this.children&&(this.$scopedSlots.input||this.$slots.input)?this.children.value:this.value}},methods:{focus:function(){this.$refs.input&&this.$refs.input.focus()},blur:function(){this.$refs.input&&this.$refs.input.blur()},runValidator:function(t,e){return new Promise((function(i){var n=e.validator(t,e);if(Object(m.e)(n))return n.then(i);i(n)}))},isEmptyValue:function(t){return Array.isArray(t)?!t.length:!t},runSyncRule:function(t,e){return(!e.required||!this.isEmptyValue(t))&&!(e.pattern&&!e.pattern.test(t))},getRuleMessage:function(t,e){var i=e.message;return Object(m.c)(i)?i(t,e):i},runRules:function(t){var e=this;return t.reduce((function(t,i){return t.then((function(){if(!e.validateMessage){var t=e.formValue;if(i.formatter&&(t=i.formatter(t,i)),e.runSyncRule(t,i))return i.validator?e.runValidator(t,i).then((function(n){!1===n&&(e.validateMessage=e.getRuleMessage(t,i))})):void 0;e.validateMessage=e.getRuleMessage(t,i)}}))}),Promise.resolve())},validate:function(t){var e=this;return void 0===t&&(t=this.rules),new Promise((function(i){t||i(),e.runRules(t).then((function(){e.validateMessage?i({name:e.name,message:e.validateMessage}):i()}))}))},validateWithTrigger:function(t){if(this.vanForm&&this.rules){var e=this.vanForm.validateTrigger===t,i=this.rules.filter((function(i){return i.trigger?i.trigger===t:e}));this.validate(i)}},resetValidation:function(){this.validateMessage&&(this.validateMessage="")},format:function(t){if(void 0===t&&(t=this.$refs.input),t){var e=t.value,i=this.maxlength;if(Object(m.b)(i)&&e.length>i&&(e=e.slice(0,i),t.value=e),"number"===this.type||"digit"===this.type){var n=e;(e=Dt(e,"number"===this.type))!==n&&(t.value=e)}if(this.formatter){var s=e;(e=this.formatter(e))!==s&&(t.value=e)}return e}},onInput:function(t){t.target.composing||this.$emit("input",this.format(t.target))},onFocus:function(t){this.focused=!0,this.$emit("focus",t),this.readonly&&this.blur()},onBlur:function(t){this.focused=!1,this.$emit("blur",t),this.validateWithTrigger("onBlur"),Xt()},onClick:function(t){this.$emit("click",t)},onClickInput:function(t){this.$emit("click-input",t)},onClickLeftIcon:function(t){this.$emit("click-left-icon",t)},onClickRightIcon:function(t){this.$emit("click-right-icon",t)},onClear:function(t){k(t),this.$emit("input",""),this.$emit("clear",t)},onKeypress:function(t){13===t.keyCode&&(this.getProp("submitOnEnter")||"textarea"===this.type||k(t),"search"===this.type&&this.blur());this.$emit("keypress",t)},adjustSize:function(){var t=this.$refs.input;if("textarea"===this.type&&this.autosize&&t){t.style.height="auto";var e=t.scrollHeight;if(Object(m.d)(this.autosize)){var i=this.autosize,n=i.maxHeight,s=i.minHeight;n&&(e=Math.min(e,n)),s&&(e=Math.max(e,s))}e&&(t.style.height=e+"px")}},genInput:function(){var t=this.$createElement,e=this.type,i=this.slots("input"),s=this.getProp("inputAlign");if(i)return t("div",{class:ae("control",[s,"custom"]),on:{click:this.onClickInput}},[i]);var o={ref:"input",class:ae("control",s),domProps:{value:this.value},attrs:n(n({},this.$attrs),{},{name:this.name,disabled:this.disabled,readonly:this.readonly,placeholder:this.placeholder}),on:this.listeners,directives:[{name:"model",value:this.value}]};if("textarea"===e)return t("textarea",r()([{},o]));var a,l=e;return"number"===e&&(l="text",a="decimal"),"digit"===e&&(l="tel",a="numeric"),t("input",r()([{attrs:{type:l,inputmode:a}},o]))},genLeftIcon:function(){var t=this.$createElement;if(this.slots("left-icon")||this.leftIcon)return t("div",{class:ae("left-icon"),on:{click:this.onClickLeftIcon}},[this.slots("left-icon")||t(st,{attrs:{name:this.leftIcon,classPrefix:this.iconPrefix}})])},genRightIcon:function(){var t=this.$createElement,e=this.slots;if(e("right-icon")||this.rightIcon)return t("div",{class:ae("right-icon"),on:{click:this.onClickRightIcon}},[e("right-icon")||t(st,{attrs:{name:this.rightIcon,classPrefix:this.iconPrefix}})])},genWordLimit:function(){var t=this.$createElement;if(this.showWordLimit&&this.maxlength){var e=(this.value||"").length,i=e>=this.maxlength;return t("div",{class:ae("word-limit")},[t("span",{class:ae("word-num",{full:i})},[e]),"/",this.maxlength])}},genMessage:function(){var t=this.$createElement;if(!this.vanForm||!1!==this.vanForm.showErrorMessage){var e=this.errorMessage||this.validateMessage;if(e){var i=this.getProp("errorMessageAlign");return t("div",{class:ae("error-message",i)},[e])}}},getProp:function(t){return Object(m.b)(this[t])?this[t]:this.vanForm&&Object(m.b)(this.vanForm[t])?this.vanForm[t]:void 0},genLabel:function(){var t=this.$createElement,e=this.getProp("colon")?":":"";return this.slots("label")?[this.slots("label"),e]:this.label?t("span",[this.label+e]):void 0}},render:function(){var t,e=arguments[0],i=this.slots,n=this.getProp("labelAlign"),s={icon:this.genLeftIcon},r=this.genLabel();r&&(s.title=function(){return r});var o=this.slots("extra");return o&&(s.extra=function(){return o}),e(se,{attrs:{icon:this.leftIcon,size:this.size,center:this.center,border:this.border,isLink:this.isLink,required:this.required,clickable:this.clickable,titleStyle:this.labelStyle,valueClass:ae("value"),titleClass:[ae("label",n),this.labelClass],arrowDirection:this.arrowDirection},scopedSlots:s,class:ae((t={error:this.showError},t["label-"+n]=n,t["min-height"]="textarea"===this.type&&!this.autosize,t)),on:{click:this.onClick}},[e("div",{class:ae("body")},[this.genInput(),this.showClear&&e(st,{attrs:{name:"clear"},class:ae("clear"),on:{touchstart:this.onClear}}),this.genRightIcon(),i("button")&&e("div",{class:ae("button")},[i("button")])]),this.genWordLimit(),this.genMessage()])}}),ce=0;var ue=Object(o.a)("toast"),he=ue[0],de=ue[1],fe=he({mixins:[Y()],props:{icon:String,className:null,iconPrefix:String,loadingType:String,forbidClick:Boolean,closeOnClick:Boolean,message:[Number,String],type:{type:String,default:"text"},position:{type:String,default:"middle"},transition:{type:String,default:"van-fade"},lockScroll:{type:Boolean,default:!1}},data:function(){return{clickable:!1}},mounted:function(){this.toggleClickable()},destroyed:function(){this.toggleClickable()},watch:{value:"toggleClickable",forbidClick:"toggleClickable"},methods:{onClick:function(){this.closeOnClick&&this.close()},toggleClickable:function(){var t=this.value&&this.forbidClick;this.clickable!==t&&(this.clickable=t,t?(ce||document.body.classList.add("van-toast--unclickable"),ce++):--ce||document.body.classList.remove("van-toast--unclickable"))},onAfterEnter:function(){this.$emit("opened"),this.onOpened&&this.onOpened()},onAfterLeave:function(){this.$emit("closed")},genIcon:function(){var t=this.$createElement,e=this.icon,i=this.type,n=this.iconPrefix,s=this.loadingType;return e||"success"===i||"fail"===i?t(st,{class:de("icon"),attrs:{classPrefix:n,name:e||i}}):"loading"===i?t(mt,{class:de("loading"),attrs:{type:s}}):void 0},genMessage:function(){var t=this.$createElement,e=this.type,i=this.message;if(Object(m.b)(i)&&""!==i)return"html"===e?t("div",{class:de("text"),domProps:{innerHTML:i}}):t("div",{class:de("text")},[i])}},render:function(){var t,e=arguments[0];return e("transition",{attrs:{name:this.transition},on:{afterEnter:this.onAfterEnter,afterLeave:this.onAfterLeave}},[e("div",{directives:[{name:"show",value:this.value}],class:[de([this.position,(t={},t[this.type]=!this.icon,t)]),this.className],on:{click:this.onClick}},[this.genIcon(),this.genMessage()])])}}),pe={icon:"",type:"text",mask:!1,value:!0,message:"",className:"",overlay:!1,onClose:null,onOpened:null,duration:2e3,iconPrefix:void 0,position:"middle",transition:"van-fade",forbidClick:!1,loadingType:void 0,getContainer:"body",overlayStyle:null,closeOnClick:!1,closeOnClickOverlay:!1},me={},ve=[],ge=!1,be=n({},pe);function ye(t){return Object(m.d)(t)?t:{message:t}}function Se(t){void 0===t&&(t={});var e=function(){if(m.f)return{};if(!ve.length||ge){var t=new(l.a.extend(fe))({el:document.createElement("div")});t.$on("input",(function(e){t.value=e})),ve.push(t)}return ve[ve.length-1]}();return e.value&&e.updateZIndex(),t=ye(t),(t=n(n(n({},be),me[t.type||be.type]),t)).clear=function(){e.value=!1,t.onClose&&t.onClose(),ge&&!m.f&&e.$on("closed",(function(){clearTimeout(e.timer),ve=ve.filter((function(t){return t!==e})),B(e.$el),e.$destroy()}))},n(e,function(t){return n(n({},t),{},{overlay:t.mask||t.overlay,mask:void 0,duration:void 0})}(t)),clearTimeout(e.timer),t.duration>0&&(e.timer=setTimeout((function(){e.clear()}),t.duration)),e}["loading","success","fail"].forEach((function(t){var e;Se[t]=(e=t,function(t){return Se(n({type:e},ye(t)))})})),Se.clear=function(t){ve.length&&(t?(ve.forEach((function(t){t.clear()})),ve=[]):ge?ve.shift().clear():ve[0].clear())},Se.setDefaultOptions=function(t,e){"string"==typeof t?me[t]=e:n(be,t)},Se.resetDefaultOptions=function(t){"string"==typeof t?me[t]=null:(be=n({},pe),me={})},Se.allowMultiple=function(t){void 0===t&&(t=!0),ge=t},Se.install=function(){l.a.use(fe)},l.a.prototype.$toast=Se;var ke=Se,xe=Object(o.a)("button"),we=xe[0],Ce=xe[1];function Oe(t,e,i,n){var s,o=e.tag,a=e.icon,l=e.type,c=e.color,u=e.plain,f=e.disabled,p=e.loading,m=e.hairline,v=e.loadingText,g={};c&&(g.color=u?c:"#fff",u||(g.background=c),-1!==c.indexOf("gradient")?g.border=0:g.borderColor=c);var b,y,S=[Ce([l,e.size,{plain:u,loading:p,disabled:f,hairline:m,block:e.block,round:e.round,square:e.square}]),(s={},s["van-hairline--surround"]=m,s)];return t(o,r()([{style:g,class:S,attrs:{type:e.nativeType,disabled:f},on:{click:function(t){p||f||(d(n,"click",t),Gt(n))},touchstart:function(t){d(n,"touchstart",t)}}},h(n)]),[t("div",{class:Ce("content")},[(y=[],p?y.push(t(mt,{class:Ce("loading"),attrs:{size:e.loadingSize,type:e.loadingType,color:"currentColor"}})):a&&y.push(t(st,{attrs:{name:a,classPrefix:e.iconPrefix},class:Ce("icon")})),(b=p?v:i.default?i.default():e.text)&&y.push(t("span",{class:Ce("text")},[b])),y)])])}Oe.props=n(n({},Zt),{},{text:String,icon:String,color:String,block:Boolean,plain:Boolean,round:Boolean,square:Boolean,loading:Boolean,hairline:Boolean,disabled:Boolean,iconPrefix:String,nativeType:String,loadingText:String,loadingType:String,tag:{type:String,default:"button"},type:{type:String,default:"default"},size:{type:String,default:"normal"},loadingSize:{type:String,default:"20px"}});var Te,$e=we(Oe),Be=Object(o.a)("dialog"),Ie=Be[0],je=Be[1],Ee=Be[2],De=Ie({mixins:[Y()],props:{title:String,width:[Number,String],message:String,className:null,callback:Function,beforeClose:Function,messageAlign:String,cancelButtonText:String,cancelButtonColor:String,confirmButtonText:String,confirmButtonColor:String,showCancelButton:Boolean,transition:{type:String,default:"van-dialog-bounce"},showConfirmButton:{type:Boolean,default:!0},overlay:{type:Boolean,default:!0},closeOnClickOverlay:{type:Boolean,default:!1}},data:function(){return{loading:{confirm:!1,cancel:!1}}},methods:{onClickOverlay:function(){this.handleAction("overlay")},handleAction:function(t){var e=this;this.$emit(t),this.value&&(this.beforeClose?(this.loading[t]=!0,this.beforeClose(t,(function(i){!1!==i&&e.loading[t]&&e.onClose(t),e.loading.confirm=!1,e.loading.cancel=!1}))):this.onClose(t))},onClose:function(t){this.close(),this.callback&&this.callback(t)},onOpened:function(){this.$emit("opened")},onClosed:function(){this.$emit("closed")},genButtons:function(){var t,e=this,i=this.$createElement,n=this.showCancelButton&&this.showConfirmButton;return i("div",{class:[Ot,je("footer",{buttons:n})]},[this.showCancelButton&&i($e,{attrs:{size:"large",loading:this.loading.cancel,text:this.cancelButtonText||Ee("cancel")},class:je("cancel"),style:{color:this.cancelButtonColor},on:{click:function(){e.handleAction("cancel")}}}),this.showConfirmButton&&i($e,{attrs:{size:"large",loading:this.loading.confirm,text:this.confirmButtonText||Ee("confirm")},class:[je("confirm"),(t={},t["van-hairline--left"]=n,t)],style:{color:this.confirmButtonColor},on:{click:function(){e.handleAction("confirm")}}})])}},render:function(){var t,e=arguments[0];if(this.shouldRender){var i=this.message,n=this.messageAlign,s=this.slots(),r=this.slots("title")||this.title,o=r&&e("div",{class:je("header",{isolated:!i&&!s})},[r]),a=(s||i)&&e("div",{class:je("content")},[s||e("div",{domProps:{innerHTML:i},class:je("message",(t={"has-title":r},t[n]=n,t))})]);return e("transition",{attrs:{name:this.transition},on:{afterEnter:this.onOpened,afterLeave:this.onClosed}},[e("div",{directives:[{name:"show",value:this.value}],attrs:{role:"dialog","aria-labelledby":this.title||i},class:[je(),this.className],style:{width:Object(U.a)(this.width)}},[o,a,this.genButtons()])])}}});function Ne(t){return m.f?Promise.resolve():new Promise((function(e,i){var s;Te&&(s=Te.$el,document.body.contains(s))||(Te&&Te.$destroy(),(Te=new(l.a.extend(De))({el:document.createElement("div"),propsData:{lazyRender:!1}})).$on("input",(function(t){Te.value=t}))),n(Te,Ne.currentOptions,t,{resolve:e,reject:i})}))}Ne.defaultOptions={value:!0,title:"",width:"",message:"",overlay:!0,className:"",lockScroll:!0,transition:"van-dialog-bounce",beforeClose:null,overlayClass:"",overlayStyle:null,messageAlign:"",getContainer:"body",cancelButtonText:"",cancelButtonColor:null,confirmButtonText:"",confirmButtonColor:null,showConfirmButton:!0,showCancelButton:!1,closeOnPopstate:!1,closeOnClickOverlay:!1,callback:function(t){Te["confirm"===t?"resolve":"reject"](t)}},Ne.alert=Ne,Ne.confirm=function(t){return Ne(n({showCancelButton:!0},t))},Ne.close=function(){Te&&(Te.value=!1)},Ne.setDefaultOptions=function(t){n(Ne.currentOptions,t)},Ne.resetDefaultOptions=function(){Ne.currentOptions=n({},Ne.defaultOptions)},Ne.resetDefaultOptions(),Ne.install=function(){l.a.use(De)},Ne.Component=De,l.a.prototype.$dialog=Ne;var Le=Ne,Ae=Object(o.a)("address-edit-detail"),Pe=Ae[0],ze=Ae[1],Me=Ae[2],Fe=!m.f&&/android/.test(navigator.userAgent.toLowerCase()),Ve=Pe({props:{value:String,errorMessage:String,focused:Boolean,detailRows:[Number,String],searchResult:Array,detailMaxlength:[Number,String],showSearchResult:Boolean},computed:{shouldShowSearchResult:function(){return this.focused&&this.searchResult&&this.showSearchResult}},methods:{onSelect:function(t){this.$emit("select-search",t),this.$emit("input",((t.address||"")+" "+(t.name||"")).trim())},onFinish:function(){this.$refs.field.blur()},genFinish:function(){var t=this.$createElement;if(this.value&&this.focused&&Fe)return t("div",{class:ze("finish"),on:{click:this.onFinish}},[Me("complete")])},genSearchResult:function(){var t=this,e=this.$createElement,i=this.value,n=this.shouldShowSearchResult,s=this.searchResult;if(n)return s.map((function(n){return e(se,{key:n.name+n.address,attrs:{clickable:!0,border:!1,icon:"location-o",label:n.address},class:ze("search-item"),on:{click:function(){t.onSelect(n)}},scopedSlots:{title:function(){if(n.name){var t=n.name.replace(i,"<span class="+ze("keyword")+">"+i+"</span>");return e("div",{domProps:{innerHTML:t}})}}}})}))}},render:function(){var t=arguments[0];return t(se,{class:ze()},[t(le,{attrs:{autosize:!0,rows:this.detailRows,clearable:!Fe,type:"textarea",value:this.value,errorMessage:this.errorMessage,border:!this.shouldShowSearchResult,label:Me("label"),maxlength:this.detailMaxlength,placeholder:Me("placeholder")},ref:"field",scopedSlots:{icon:this.genFinish},on:n({},this.$listeners)}),this.genSearchResult()])}}),Re={size:[Number,String],value:null,loading:Boolean,disabled:Boolean,activeColor:String,inactiveColor:String,activeValue:{type:null,default:!0},inactiveValue:{type:null,default:!1}},_e={inject:{vanField:{default:null}},watch:{value:function(){var t=this.vanField;t&&(t.resetValidation(),t.validateWithTrigger("onChange"))}},created:function(){var t=this.vanField;t&&!t.children&&(t.children=this)}},He=Object(o.a)("switch"),We=He[0],qe=He[1],Ye=We({mixins:[_e],props:Re,computed:{checked:function(){return this.value===this.activeValue},style:function(){return{fontSize:Object(U.a)(this.size),backgroundColor:this.checked?this.activeColor:this.inactiveColor}}},methods:{onClick:function(t){if(this.$emit("click",t),!this.disabled&&!this.loading){var e=this.checked?this.inactiveValue:this.activeValue;this.$emit("input",e),this.$emit("change",e)}},genLoading:function(){var t=this.$createElement;if(this.loading){var e=this.checked?this.activeColor:this.inactiveColor;return t(mt,{class:qe("loading"),attrs:{color:e}})}}},render:function(){var t=arguments[0],e=this.checked,i=this.loading,n=this.disabled;return t("div",{class:qe({on:e,loading:i,disabled:n}),attrs:{role:"switch","aria-checked":String(e)},style:this.style,on:{click:this.onClick}},[t("div",{class:qe("node")},[this.genLoading()])])}}),Ue=Object(o.a)("switch-cell"),Ke=Ue[0],Xe=Ue[1];function Qe(t,e,i,s){return t(se,r()([{attrs:{center:!0,size:e.cellSize,title:e.title,border:e.border},class:Xe([e.cellSize])},h(s)]),[t(Ye,{props:n({},e),on:n({},s.listeners)})])}Qe.props=n(n({},Re),{},{title:String,cellSize:String,border:{type:Boolean,default:!0},size:{type:String,default:"24px"}});var Ge=Ke(Qe),Ze=Object(o.a)("address-edit"),Je=Ze[0],ti=Ze[1],ei=Ze[2],ii={name:"",tel:"",country:"",province:"",city:"",county:"",areaCode:"",postalCode:"",addressDetail:"",isDefault:!1};var ni=Je({props:{areaList:Object,isSaving:Boolean,isDeleting:Boolean,validator:Function,showDelete:Boolean,showPostal:Boolean,searchResult:Array,showSetDefault:Boolean,showSearchResult:Boolean,saveButtonText:String,deleteButtonText:String,areaPlaceholder:String,showArea:{type:Boolean,default:!0},showDetail:{type:Boolean,default:!0},disableArea:Boolean,detailRows:{type:[Number,String],default:1},detailMaxlength:{type:[Number,String],default:200},addressInfo:{type:Object,default:function(){return n({},ii)}},telValidator:{type:Function,default:kt},postalValidator:{type:Function,default:function(t){return/^\d{6}$/.test(t)}},areaColumnsPlaceholder:{type:Array,default:function(){return[]}}},data:function(){return{data:{},showAreaPopup:!1,detailFocused:!1,errorInfo:{tel:"",name:"",areaCode:"",postalCode:"",addressDetail:""}}},computed:{areaListLoaded:function(){return Object(m.d)(this.areaList)&&Object.keys(this.areaList).length},areaText:function(){var t=this.data,e=t.country,i=t.province,n=t.city,s=t.county;if(t.areaCode){var r=[e,i,n,s];return i&&i===n&&r.splice(1,1),r.filter((function(t){return t})).join("/")}return""}},watch:{addressInfo:{handler:function(t){this.data=n(n({},ii),t),this.setAreaCode(t.areaCode)},deep:!0,immediate:!0},areaList:function(){this.setAreaCode(this.data.areaCode)}},methods:{onFocus:function(t){this.errorInfo[t]="",this.detailFocused="addressDetail"===t,this.$emit("focus",t)},onChangeDetail:function(t){this.data.addressDetail=t,this.$emit("change-detail",t)},onAreaConfirm:function(t){(t=t.filter((function(t){return!!t}))).some((function(t){return!t.code}))?ke(ei("areaEmpty")):(this.showAreaPopup=!1,this.assignAreaValues(),this.$emit("change-area",t))},assignAreaValues:function(){var t=this.$refs.area;if(t){var e=t.getArea();e.areaCode=e.code,delete e.code,n(this.data,e)}},onSave:function(){var t=this,e=["name","tel"];this.showArea&&e.push("areaCode"),this.showDetail&&e.push("addressDetail"),this.showPostal&&e.push("postalCode"),e.every((function(e){var i=t.getErrorMessage(e);return i&&(t.errorInfo[e]=i),!i}))&&!this.isSaving&&this.$emit("save",this.data)},getErrorMessage:function(t){var e=String(this.data[t]||"").trim();if(this.validator){var i=this.validator(t,e);if(i)return i}switch(t){case"name":return e?"":ei("nameEmpty");case"tel":return this.telValidator(e)?"":ei("telInvalid");case"areaCode":return e?"":ei("areaEmpty");case"addressDetail":return e?"":ei("addressEmpty");case"postalCode":return e&&!this.postalValidator(e)?ei("postalEmpty"):""}},onDelete:function(){var t=this;Le.confirm({title:ei("confirmDelete")}).then((function(){t.$emit("delete",t.data)})).catch((function(){t.$emit("cancel-delete",t.data)}))},getArea:function(){return this.$refs.area?this.$refs.area.getValues():[]},setAreaCode:function(t){this.data.areaCode=t||"",t&&this.$nextTick(this.assignAreaValues)},setAddressDetail:function(t){this.data.addressDetail=t},onDetailBlur:function(){var t=this;setTimeout((function(){t.detailFocused=!1}))}},render:function(){var t=this,e=arguments[0],i=this.data,n=this.errorInfo,s=this.searchResult,r=this.disableArea,o=function(e){return function(){return t.onFocus(e)}},a=s&&s.length&&this.detailFocused;return e("div",{class:ti()},[e("div",{class:ti("fields")},[e(le,{attrs:{clearable:!0,label:ei("name"),placeholder:ei("namePlaceholder"),errorMessage:n.name},on:{focus:o("name")},model:{value:i.name,callback:function(e){t.$set(i,"name",e)}}}),e(le,{attrs:{clearable:!0,type:"tel",label:ei("tel"),placeholder:ei("telPlaceholder"),errorMessage:n.tel},on:{focus:o("tel")},model:{value:i.tel,callback:function(e){t.$set(i,"tel",e)}}}),e(le,{directives:[{name:"show",value:this.showArea}],attrs:{readonly:!0,clickable:!r,label:ei("area"),placeholder:this.areaPlaceholder||ei("areaPlaceholder"),errorMessage:n.areaCode,rightIcon:r?null:"arrow",value:this.areaText},on:{focus:o("areaCode"),click:function(){t.$emit("click-area"),t.showAreaPopup=!r}}}),e(Ve,{directives:[{name:"show",value:this.showDetail}],attrs:{focused:this.detailFocused,value:i.addressDetail,errorMessage:n.addressDetail,detailRows:this.detailRows,detailMaxlength:this.detailMaxlength,searchResult:this.searchResult,showSearchResult:this.showSearchResult},on:{focus:o("addressDetail"),blur:this.onDetailBlur,input:this.onChangeDetail,"select-search":function(e){t.$emit("select-search",e)}}}),this.showPostal&&e(le,{directives:[{name:"show",value:!a}],attrs:{type:"tel",maxlength:"6",label:ei("postal"),placeholder:ei("postal"),errorMessage:n.postalCode},on:{focus:o("postalCode")},model:{value:i.postalCode,callback:function(e){t.$set(i,"postalCode",e)}}}),this.slots()]),this.showSetDefault&&e(Ge,{class:ti("default"),directives:[{name:"show",value:!a}],attrs:{title:ei("defaultAddress")},on:{change:function(e){t.$emit("change-default",e)}},model:{value:i.isDefault,callback:function(e){t.$set(i,"isDefault",e)}}}),e("div",{directives:[{name:"show",value:!a}],class:ti("buttons")},[e($e,{attrs:{block:!0,round:!0,loading:this.isSaving,type:"danger",text:this.saveButtonText||ei("save")},on:{click:this.onSave}}),this.showDelete&&e($e,{attrs:{block:!0,round:!0,loading:this.isDeleting,text:this.deleteButtonText||ei("delete")},on:{click:this.onDelete}})]),e(lt,{attrs:{position:"bottom",lazyRender:!1,getContainer:"body"},model:{value:t.showAreaPopup,callback:function(e){t.showAreaPopup=e}}},[e(Ut,{ref:"area",attrs:{loading:!this.areaListLoaded,value:i.areaCode,areaList:this.areaList,columnsPlaceholder:this.areaColumnsPlaceholder},on:{confirm:this.onAreaConfirm,cancel:function(){t.showAreaPopup=!1}}})])])}});function si(t,e){var i=e.$vnode.componentOptions;if(i&&i.children){var n=function(t){var e=[];return function t(i){i.forEach((function(i){e.push(i),i.componentInstance&&t(i.componentInstance.$children.map((function(t){return t.$vnode}))),i.children&&t(i.children)}))}(t),e}(i.children);t.sort((function(t,e){return n.indexOf(t.$vnode)-n.indexOf(e.$vnode)}))}}function ri(t,e){var i,n;void 0===e&&(e={});var s=e.indexKey||"index";return l.a.extend({inject:(i={},i[t]={default:null},i),computed:(n={parent:function(){return this.disableBindRelation?null:this[t]}},n[s]=function(){return this.bindRelation(),this.parent?this.parent.children.indexOf(this):null},n),mounted:function(){this.bindRelation()},beforeDestroy:function(){var t=this;this.parent&&(this.parent.children=this.parent.children.filter((function(e){return e!==t})))},methods:{bindRelation:function(){if(this.parent&&-1===this.parent.children.indexOf(this)){var t=[].concat(this.parent.children,[this]);si(t,this.parent),this.parent.children=t}}}})}function oi(t){return{provide:function(){var e;return(e={})[t]=this,e},data:function(){return{children:[]}}}}var ai=Object(o.a)("radio-group"),li=ai[0],ci=ai[1],ui=li({mixins:[oi("vanRadio"),_e],props:{value:null,disabled:Boolean,direction:String,checkedColor:String,iconSize:[Number,String]},watch:{value:function(t){this.$emit("change",t)}},render:function(){var t=arguments[0];return t("div",{class:ci([this.direction]),attrs:{role:"radiogroup"}},[this.slots()])}}),hi=Object(o.a)("tag"),di=hi[0],fi=hi[1];function pi(t,e,i,n){var s,o,a=e.type,l=e.mark,c=e.plain,u=e.color,f=e.round,p=e.size,m=((s={})[c?"color":"backgroundColor"]=u,s);e.textColor&&(m.color=e.textColor);var v={mark:l,plain:c,round:f};p&&(v[p]=p);var g=e.closeable&&t(st,{attrs:{name:"cross"},class:fi("close"),on:{click:function(t){t.stopPropagation(),d(n,"close")}}});return t("transition",{attrs:{name:e.closeable?"van-fade":null}},[t("span",r()([{key:"content",style:m,class:[fi([v,a]),(o={},o["van-hairline--surround"]=c,o)]},h(n,!0)]),[null==i.default?void 0:i.default(),g])])}pi.props={size:String,mark:Boolean,color:String,plain:Boolean,round:Boolean,textColor:String,closeable:Boolean,type:{type:String,default:"default"}};var mi=di(pi),vi=function(t){var e=t.parent,i=t.bem,n=t.role;return{mixins:[ri(e),_e],props:{name:null,value:null,disabled:Boolean,iconSize:[Number,String],checkedColor:String,labelPosition:String,labelDisabled:Boolean,shape:{type:String,default:"round"},bindGroup:{type:Boolean,default:!0}},computed:{disableBindRelation:function(){return!this.bindGroup},isDisabled:function(){return this.parent&&this.parent.disabled||this.disabled},direction:function(){return this.parent&&this.parent.direction||null},iconStyle:function(){var t=this.checkedColor||this.parent&&this.parent.checkedColor;if(t&&this.checked&&!this.isDisabled)return{borderColor:t,backgroundColor:t}},tabindex:function(){return this.isDisabled||"radio"===n&&!this.checked?-1:0}},methods:{onClick:function(t){var e=this,i=t.target,n=this.$refs.icon,s=n===i||n.contains(i);this.isDisabled||!s&&this.labelDisabled?this.$emit("click",t):(this.toggle(),setTimeout((function(){e.$emit("click",t)})))},genIcon:function(){var t=this.$createElement,e=this.checked,n=this.iconSize||this.parent&&this.parent.iconSize;return t("div",{ref:"icon",class:i("icon",[this.shape,{disabled:this.isDisabled,checked:e}]),style:{fontSize:Object(U.a)(n)}},[this.slots("icon",{checked:e})||t(st,{attrs:{name:"success"},style:this.iconStyle})])},genLabel:function(){var t=this.$createElement,e=this.slots();if(e)return t("span",{class:i("label",[this.labelPosition,{disabled:this.isDisabled}])},[e])}},render:function(){var t=arguments[0],e=[this.genIcon()];return"left"===this.labelPosition?e.unshift(this.genLabel()):e.push(this.genLabel()),t("div",{attrs:{role:n,tabindex:this.tabindex,"aria-checked":String(this.checked)},class:i([{disabled:this.isDisabled,"label-disabled":this.labelDisabled},this.direction]),on:{click:this.onClick}},[e])}}},gi=Object(o.a)("radio"),bi=(0,gi[0])({mixins:[vi({bem:gi[1],role:"radio",parent:"vanRadio"})],computed:{currentValue:{get:function(){return this.parent?this.parent.value:this.value},set:function(t){(this.parent||this).$emit("input",t)}},checked:function(){return this.currentValue===this.name}},methods:{toggle:function(){this.currentValue=this.name}}}),yi=Object(o.a)("address-item"),Si=yi[0],ki=yi[1];function xi(t,e,i,s){var o=e.disabled,a=e.switchable;function l(){if(e.data.isDefault&&e.defaultTagText)return t(mi,{attrs:{type:"danger",round:!0},class:ki("tag")},[e.defaultTagText])}return t("div",{class:ki({disabled:o}),on:{click:function(){a&&d(s,"select"),d(s,"click")}}},[t(se,r()([{attrs:{border:!1,valueClass:ki("value")},scopedSlots:{default:function(){var i=e.data,n=[t("div",{class:ki("name")},[i.name+" "+i.tel,l()]),t("div",{class:ki("address")},[i.address])];return a&&!o?t(bi,{attrs:{name:i.id,iconSize:18}},[n]):n},"right-icon":function(){return t(st,{attrs:{name:"edit"},class:ki("edit"),on:{click:function(t){t.stopPropagation(),d(s,"edit"),d(s,"click")}}})}}},h(s)])),null==i.bottom?void 0:i.bottom(n(n({},e.data),{},{disabled:o}))])}xi.props={data:Object,disabled:Boolean,switchable:Boolean,defaultTagText:String};var wi=Si(xi),Ci=Object(o.a)("address-list"),Oi=Ci[0],Ti=Ci[1],$i=Ci[2];function Bi(t,e,i,n){function s(s,r){if(s)return s.map((function(s,o){return t(wi,{attrs:{data:s,disabled:r,switchable:e.switchable,defaultTagText:e.defaultTagText},key:s.id,scopedSlots:{bottom:i["item-bottom"]},on:{select:function(){d(n,r?"select-disabled":"select",s,o),r||d(n,"input",s.id)},edit:function(){d(n,r?"edit-disabled":"edit",s,o)},click:function(){d(n,"click-item",s,o)}}})}))}var o=s(e.list),a=s(e.disabledList,!0);return t("div",r()([{class:Ti()},h(n)]),[null==i.top?void 0:i.top(),t(ui,{attrs:{value:e.value}},[o]),e.disabledText&&t("div",{class:Ti("disabled-text")},[e.disabledText]),a,null==i.default?void 0:i.default(),t("div",{class:Ti("bottom")},[t($e,{attrs:{round:!0,block:!0,type:"danger",text:e.addButtonText||$i("add")},class:Ti("add"),on:{click:function(){d(n,"add")}}})])])}Bi.props={list:Array,value:[Number,String],disabledList:Array,disabledText:String,addButtonText:String,defaultTagText:String,switchable:{type:Boolean,default:!0}};var Ii=Oi(Bi),ji=i(5);function Ei(t){return"[object Date]"===Object.prototype.toString.call(t)&&!Object(ji.a)(t.getTime())}var Di=Object(o.a)("calendar"),Ni=Di[0],Li=Di[1],Ai=Di[2];function Pi(t,e){var i=t.getFullYear(),n=e.getFullYear(),s=t.getMonth(),r=e.getMonth();return i===n?s===r?0:s>r?1:-1:i>n?1:-1}function zi(t,e){var i=Pi(t,e);if(0===i){var n=t.getDate(),s=e.getDate();return n===s?0:n>s?1:-1}return i}function Mi(t,e){return(t=new Date(t)).setDate(t.getDate()+e),t}function Fi(t){return Mi(t,1)}function Vi(t){return new Date(t)}function Ri(t){return Array.isArray(t)?t.map((function(t){return null===t?t:Vi(t)})):Vi(t)}function _i(t,e){return 32-new Date(t,e-1,32).getDate()}var Hi=(0,Object(o.a)("calendar-month")[0])({props:{date:Date,type:String,color:String,minDate:Date,maxDate:Date,showMark:Boolean,rowHeight:[Number,String],formatter:Function,lazyRender:Boolean,currentDate:[Date,Array],allowSameDay:Boolean,showSubtitle:Boolean,showMonthTitle:Boolean},data:function(){return{visible:!1}},computed:{title:function(){return t=this.date,Ai("monthTitle",t.getFullYear(),t.getMonth()+1);var t},offset:function(){return this.date.getDay()},totalDay:function(){return _i(this.date.getFullYear(),this.date.getMonth()+1)},shouldRender:function(){return this.visible||!this.lazyRender},monthStyle:function(){if(!this.shouldRender)return{paddingBottom:Math.ceil((this.totalDay+this.offset)/7)*this.rowHeight+"px"}},days:function(){for(var t=[],e=this.date.getFullYear(),i=this.date.getMonth(),n=1;n<=this.totalDay;n++){var s=new Date(e,i,n),r=this.getDayType(s),o={date:s,type:r,text:n,bottomInfo:this.getBottomInfo(r)};this.formatter&&(o=this.formatter(o)),t.push(o)}return t}},mounted:function(){this.height=this.$el.getBoundingClientRect().height},methods:{scrollIntoView:function(){this.showSubtitle?this.$refs.days.scrollIntoView():this.$refs.month.scrollIntoView()},getMultipleDayType:function(t){var e=this,i=function(t){return e.currentDate.some((function(e){return 0===zi(e,t)}))};if(i(t)){var n=Mi(t,-1),s=Fi(t),r=i(n),o=i(s);return r&&o?"multiple-middle":r?"end":o?"start":"multiple-selected"}return""},getRangeDayType:function(t){var e=this.currentDate,i=e[0],n=e[1];if(!i)return"";var s=zi(t,i);if(!n)return 0===s?"start":"";var r=zi(t,n);return 0===s&&0===r&&this.allowSameDay?"start-end":0===s?"start":0===r?"end":s>0&&r<0?"middle":void 0},getDayType:function(t){var e=this.type,i=this.minDate,n=this.maxDate,s=this.currentDate;return zi(t,i)<0||zi(t,n)>0?"disabled":"single"===e?0===zi(t,s)?"selected":"":"multiple"===e?this.getMultipleDayType(t):"range"===e?this.getRangeDayType(t):void 0},getBottomInfo:function(t){if("range"===this.type){if("start"===t||"end"===t)return Ai(t);if("start-end"===t)return Ai("startEnd")}},getDayStyle:function(t,e){var i={};return 0===e&&(i.marginLeft=100*this.offset/7+"%"),64!==this.rowHeight&&(i.height=this.rowHeight+"px"),this.color&&("start"===t||"end"===t||"start-end"===t||"multiple-selected"===t||"multiple-middle"===t?i.background=this.color:"middle"===t&&(i.color=this.color)),i},genTitle:function(){var t=this.$createElement;if(this.showMonthTitle)return t("div",{class:Li("month-title")},[this.title])},genMark:function(){var t=this.$createElement;if(this.showMark)return t("div",{class:Li("month-mark")},[this.date.getMonth()+1])},genDays:function(){var t=this.$createElement;return this.shouldRender?t("div",{ref:"days",attrs:{role:"grid"},class:Li("days")},[this.genMark(),this.days.map(this.genDay)]):t("div",{ref:"days"})},genDay:function(t,e){var i=this,n=this.$createElement,s=t.type,r=t.topInfo,o=t.bottomInfo,a=this.getDayStyle(s,e),l="disabled"===s,c=function(){l||i.$emit("click",t)},u=r&&n("div",{class:Li("top-info")},[r]),h=o&&n("div",{class:Li("bottom-info")},[o]);return"selected"===s?n("div",{attrs:{role:"gridcell",tabindex:-1},style:a,class:[Li("day"),t.className],on:{click:c}},[n("div",{class:Li("selected-day"),style:{background:this.color}},[u,t.text,h])]):n("div",{attrs:{role:"gridcell",tabindex:l?null:-1},style:a,class:[Li("day",s),t.className],on:{click:c}},[u,t.text,h])}},render:function(){var t=arguments[0];return t("div",{class:Li("month"),ref:"month",style:this.monthStyle},[this.genTitle(),this.genDays()])}}),Wi=(0,Object(o.a)("calendar-header")[0])({props:{title:String,subtitle:String,showTitle:Boolean,showSubtitle:Boolean},methods:{genTitle:function(){var t=this.$createElement;if(this.showTitle){var e=this.slots("title")||this.title||Ai("title");return t("div",{class:Li("header-title")},[e])}},genSubtitle:function(){var t=this.$createElement;if(this.showSubtitle)return t("div",{class:Li("header-subtitle")},[this.subtitle])},genWeekDays:function(){var t=this.$createElement,e=Ai("weekdays");return t("div",{class:Li("weekdays")},[e.map((function(e){return t("span",{class:Li("weekday")},[e])}))])}},render:function(){var t=arguments[0];return t("div",{class:Li("header")},[this.genTitle(),this.genSubtitle(),this.genWeekDays()])}}),qi=Ni({props:{title:String,color:String,value:Boolean,formatter:Function,confirmText:String,rangePrompt:String,defaultDate:[Date,Array],getContainer:[String,Function],allowSameDay:Boolean,closeOnPopstate:Boolean,confirmDisabledText:String,type:{type:String,default:"single"},minDate:{type:Date,validator:Ei,default:function(){return new Date}},maxDate:{type:Date,validator:Ei,default:function(){var t=new Date;return new Date(t.getFullYear(),t.getMonth()+6,t.getDate())}},position:{type:String,default:"bottom"},rowHeight:{type:[Number,String],default:64},round:{type:Boolean,default:!0},poppable:{type:Boolean,default:!0},lazyRender:{type:Boolean,default:!0},showMark:{type:Boolean,default:!0},showTitle:{type:Boolean,default:!0},showConfirm:{type:Boolean,default:!0},showSubtitle:{type:Boolean,default:!0},safeAreaInsetBottom:{type:Boolean,default:!0},closeOnClickOverlay:{type:Boolean,default:!0},maxRange:{type:[Number,String],default:null}},data:function(){return{subtitle:"",currentDate:this.getInitialDate()}},computed:{months:function(){var t=[],e=new Date(this.minDate);e.setDate(1);do{t.push(new Date(e)),e.setMonth(e.getMonth()+1)}while(1!==Pi(e,this.maxDate));return t},buttonDisabled:function(){var t=this.type,e=this.currentDate;return"range"===t?!e[0]||!e[1]:"multiple"===t?!e.length:!e}},watch:{type:"reset",value:"init",defaultDate:function(t){this.currentDate=t,this.scrollIntoView()}},mounted:function(){this.init()},activated:function(){this.init()},methods:{reset:function(){this.currentDate=this.getInitialDate(),this.scrollIntoView()},init:function(){var t=this;this.poppable&&!this.value||(this.$nextTick((function(){t.bodyHeight=Math.floor(t.$refs.body.getBoundingClientRect().height),t.onScroll()})),this.scrollIntoView())},scrollIntoView:function(){var t=this;this.$nextTick((function(){var e=t.currentDate,i="single"===t.type?e:e[0],n=t.value||!t.poppable;i&&n&&t.months.some((function(e,n){return 0===Pi(e,i)&&(t.$refs.months[n].scrollIntoView(),!0)}))}))},getInitialDate:function(){var t=this.type,e=this.minDate,i=this.maxDate,n=this.defaultDate,s=new Date;if(-1===zi(s,e)?s=e:1===zi(s,i)&&(s=i),"range"===t){var r=n||[];return[r[0]||s,r[1]||Fi(s)]}return"multiple"===t?n||[s]:n||s},onScroll:function(){var t=this.$refs,e=t.body,i=t.months,n=P(e),s=n+this.bodyHeight,r=i.map((function(t){return t.height})),o=r.reduce((function(t,e){return t+e}),0);if(!(n<0||s>o&&n>0)){for(var a,l=0,c=0;c<i.length;c++){var u=l<=s&&l+r[c]>=n;u&&!a&&(a=i[c]),!i[c].visible&&u&&this.$emit("month-show",{date:i[c].date,title:i[c].title}),i[c].visible=u,l+=r[c]}a&&(this.subtitle=a.title)}},onClickDay:function(t){var e=t.date,i=this.type,n=this.currentDate;if("range"===i){var s=n[0],r=n[1];if(s&&!r){var o=zi(e,s);1===o?this.select([s,e],!0):-1===o?this.select([e,null]):this.allowSameDay&&this.select([e,e])}else this.select([e,null])}else if("multiple"===i){var a;if(this.currentDate.some((function(t,i){var n=0===zi(t,e);return n&&(a=i),n}))){var l=n.splice(a,1)[0];this.$emit("unselect",Vi(l))}else this.maxRange&&n.length>=this.maxRange?ke(this.rangePrompt||Ai("rangePrompt",this.maxRange)):this.select([].concat(n,[e]))}else this.select(e,!0)},togglePopup:function(t){this.$emit("input",t)},select:function(t,e){var i=this,n=function(t){i.currentDate=t,i.$emit("select",Ri(i.currentDate))};if(e&&"range"===this.type&&!this.checkRange(t))return void(this.showConfirm?n([t[0],Mi(t[0],this.maxRange-1)]):n(t));n(t),e&&!this.showConfirm&&this.onConfirm()},checkRange:function(t){var e=this.maxRange,i=this.rangePrompt;return!(e&&function(t){var e=t[0].getTime();return(t[1].getTime()-e)/864e5+1}(t)>e)||(ke(i||Ai("rangePrompt",e)),!1)},onConfirm:function(){this.$emit("confirm",Ri(this.currentDate))},genMonth:function(t,e){var i=this.$createElement,n=0!==e||!this.showSubtitle;return i(Hi,{ref:"months",refInFor:!0,attrs:{date:t,type:this.type,color:this.color,minDate:this.minDate,maxDate:this.maxDate,showMark:this.showMark,formatter:this.formatter,rowHeight:this.rowHeight,lazyRender:this.lazyRender,currentDate:this.currentDate,showSubtitle:this.showSubtitle,allowSameDay:this.allowSameDay,showMonthTitle:n},on:{click:this.onClickDay}})},genFooterContent:function(){var t=this.$createElement,e=this.slots("footer");if(e)return e;if(this.showConfirm){var i=this.buttonDisabled?this.confirmDisabledText:this.confirmText;return t($e,{attrs:{round:!0,block:!0,type:"danger",color:this.color,disabled:this.buttonDisabled,nativeType:"button"},class:Li("confirm"),on:{click:this.onConfirm}},[i||Ai("confirm")])}},genFooter:function(){return(0,this.$createElement)("div",{class:Li("footer",{unfit:!this.safeAreaInsetBottom})},[this.genFooterContent()])},genCalendar:function(){var t=this,e=this.$createElement;return e("div",{class:Li()},[e(Wi,{attrs:{title:this.title,showTitle:this.showTitle,subtitle:this.subtitle,showSubtitle:this.showSubtitle},scopedSlots:{title:function(){return t.slots("title")}}}),e("div",{ref:"body",class:Li("body"),on:{scroll:this.onScroll}},[this.months.map(this.genMonth)]),this.genFooter()])}},render:function(){var t=this,e=arguments[0];if(this.poppable){var i,n=function(e){return function(){return t.$emit(e)}};return e(lt,{attrs:(i={round:!0,value:this.value},i.round=this.round,i.position=this.position,i.closeable=this.showTitle||this.showSubtitle,i.getContainer=this.getContainer,i.closeOnPopstate=this.closeOnPopstate,i.closeOnClickOverlay=this.closeOnClickOverlay,i),class:Li("popup"),on:{input:this.togglePopup,open:n("open"),opened:n("opened"),close:n("close"),closed:n("closed")}},[this.genCalendar()])}return this.genCalendar()}}),Yi=Object(o.a)("image"),Ui=Yi[0],Ki=Yi[1],Xi=Ui({props:{src:String,fit:String,alt:String,round:Boolean,width:[Number,String],height:[Number,String],radius:[Number,String],lazyLoad:Boolean,showError:{type:Boolean,default:!0},showLoading:{type:Boolean,default:!0},errorIcon:{type:String,default:"warning-o"},loadingIcon:{type:String,default:"photo-o"}},data:function(){return{loading:!0,error:!1}},watch:{src:function(){this.loading=!0,this.error=!1}},computed:{style:function(){var t={};return Object(m.b)(this.width)&&(t.width=Object(U.a)(this.width)),Object(m.b)(this.height)&&(t.height=Object(U.a)(this.height)),Object(m.b)(this.radius)&&(t.overflow="hidden",t.borderRadius=Object(U.a)(this.radius)),t}},created:function(){var t=this.$Lazyload;t&&(t.$on("loaded",this.onLazyLoaded),t.$on("error",this.onLazyLoadError))},beforeDestroy:function(){var t=this.$Lazyload;t&&(t.$off("loaded",this.onLazyLoaded),t.$off("error",this.onLazyLoadError))},methods:{onLoad:function(t){this.loading=!1,this.$emit("load",t)},onLazyLoaded:function(t){t.el===this.$refs.image&&this.loading&&this.onLoad()},onLazyLoadError:function(t){t.el!==this.$refs.image||this.error||this.onError()},onError:function(t){this.error=!0,this.loading=!1,this.$emit("error",t)},onClick:function(t){this.$emit("click",t)},genPlaceholder:function(){var t=this.$createElement;return this.loading&&this.showLoading?t("div",{class:Ki("loading")},[this.slots("loading")||t(st,{attrs:{name:this.loadingIcon},class:Ki("loading-icon")})]):this.error&&this.showError?t("div",{class:Ki("error")},[this.slots("error")||t(st,{attrs:{name:this.errorIcon},class:Ki("error-icon")})]):void 0},genImage:function(){var t=this.$createElement,e={class:Ki("img"),attrs:{alt:this.alt},style:{objectFit:this.fit}};if(!this.error)return this.lazyLoad?t("img",r()([{ref:"image",directives:[{name:"lazy",value:this.src}]},e])):t("img",r()([{attrs:{src:this.src},on:{load:this.onLoad,error:this.onError}},e]))}},render:function(){var t=arguments[0];return t("div",{class:Ki({round:this.round}),style:this.style,on:{click:this.onClick}},[this.genImage(),this.genPlaceholder()])}}),Qi=Object(o.a)("card"),Gi=Qi[0],Zi=Qi[1];function Ji(t,e,i,n){var s,o=e.thumb,a=i.num||Object(m.b)(e.num),l=i.price||Object(m.b)(e.price),c=i["origin-price"]||Object(m.b)(e.originPrice),u=a||l||c||i.bottom;function f(t){d(n,"click-thumb",t)}function p(){if(i.tag||e.tag)return t("div",{class:Zi("tag")},[i.tag?i.tag():t(mi,{attrs:{mark:!0,type:"danger"}},[e.tag])])}return t("div",r()([{class:Zi()},h(n,!0)]),[t("div",{class:Zi("header")},[function(){if(i.thumb||o)return t("a",{attrs:{href:e.thumbLink},class:Zi("thumb"),on:{click:f}},[i.thumb?i.thumb():t(Xi,{attrs:{src:o,width:"100%",height:"100%",fit:"cover","lazy-load":e.lazyLoad}}),p()])}(),t("div",{class:Zi("content",{centered:e.centered})},[t("div",[i.title?i.title():e.title?t("div",{class:[Zi("title"),"van-multi-ellipsis--l2"]},[e.title]):void 0,i.desc?i.desc():e.desc?t("div",{class:[Zi("desc"),"van-ellipsis"]},[e.desc]):void 0,null==i.tags?void 0:i.tags()]),u&&t("div",{class:"van-card__bottom"},[null==(s=i["price-top"])?void 0:s.call(i),function(){if(l)return t("div",{class:Zi("price")},[i.price?i.price():(n=e.price.toString().split("."),t("div",[t("span",{class:Zi("price-currency")},[e.currency]),t("span",{class:Zi("price-integer")},[n[0]]),".",t("span",{class:Zi("price-decimal")},[n[1]])]))]);var n}(),function(){if(c){var n=i["origin-price"];return t("div",{class:Zi("origin-price")},[n?n():e.currency+" "+e.originPrice])}}(),function(){if(a)return t("div",{class:Zi("num")},[i.num?i.num():"x"+e.num])}(),null==i.bottom?void 0:i.bottom()])])]),function(){if(i.footer)return t("div",{class:Zi("footer")},[i.footer()])}()])}Ji.props={tag:String,desc:String,thumb:String,title:String,centered:Boolean,lazyLoad:Boolean,thumbLink:String,num:[Number,String],price:[Number,String],originPrice:[Number,String],currency:{type:String,default:"¥"}};var tn=Gi(Ji),en=Object(o.a)("cell-group"),nn=en[0],sn=en[1];function rn(t,e,i,n){var s,o=t("div",r()([{class:[sn(),(s={},s[$t]=e.border,s)]},h(n,!0)]),[null==i.default?void 0:i.default()]);return e.title||i.title?t("div",[t("div",{class:sn("title")},[i.title?i.title():e.title]),o]):o}rn.props={title:String,border:{type:Boolean,default:!0}};var on=nn(rn),an=Object(o.a)("checkbox"),ln=(0,an[0])({mixins:[vi({bem:an[1],role:"checkbox",parent:"vanCheckbox"})],computed:{checked:{get:function(){return this.parent?-1!==this.parent.value.indexOf(this.name):this.value},set:function(t){this.parent?this.setParentValue(t):this.$emit("input",t)}}},watch:{value:function(t){this.$emit("change",t)}},methods:{toggle:function(t){var e=this;void 0===t&&(t=!this.checked),clearTimeout(this.toggleTask),this.toggleTask=setTimeout((function(){e.checked=t}))},setParentValue:function(t){var e=this.parent,i=e.value.slice();if(t){if(e.max&&i.length>=e.max)return;-1===i.indexOf(this.name)&&(i.push(this.name),e.$emit("input",i))}else{var n=i.indexOf(this.name);-1!==n&&(i.splice(n,1),e.$emit("input",i))}}}}),cn=Object(o.a)("checkbox-group"),un=cn[0],hn=cn[1],dn=un({mixins:[oi("vanCheckbox"),_e],props:{max:[Number,String],disabled:Boolean,direction:String,iconSize:[Number,String],checkedColor:String,value:{type:Array,default:function(){return[]}}},watch:{value:function(t){this.$emit("change",t)}},methods:{toggleAll:function(t){if(!1!==t){var e=this.children;t||(e=e.filter((function(t){return!t.checked})));var i=e.map((function(t){return t.name}));this.$emit("input",i)}else this.$emit("input",[])}},render:function(){var t=arguments[0];return t("div",{class:hn([this.direction])},[this.slots()])}}),fn=i(4),pn=Object(o.a)("circle"),mn=pn[0],vn=pn[1],gn=0;function bn(t){return Math.min(Math.max(t,0),100)}var yn=mn({props:{text:String,strokeLinecap:String,value:{type:Number,default:0},speed:{type:[Number,String],default:0},size:{type:[Number,String],default:100},fill:{type:String,default:"none"},rate:{type:[Number,String],default:100},layerColor:{type:String,default:"#fff"},color:{type:[String,Object],default:"#1989fa"},strokeWidth:{type:[Number,String],default:40},clockwise:{type:Boolean,default:!0}},beforeCreate:function(){this.uid="van-circle-gradient-"+gn++},computed:{style:function(){var t=Object(U.a)(this.size);return{width:t,height:t}},path:function(){return t=this.clockwise,"M "+(e=this.viewBoxSize)/2+" "+e/2+" m 0, -500 a 500, 500 0 1, "+(i=t?1:0)+" 0, 1000 a 500, 500 0 1, "+i+" 0, -1000";var t,e,i},viewBoxSize:function(){return+this.strokeWidth+1e3},layerStyle:function(){var t=3140*this.value/100;return{stroke:""+this.color,strokeWidth:+this.strokeWidth+1+"px",strokeLinecap:this.strokeLinecap,strokeDasharray:t+"px 3140px"}},hoverStyle:function(){return{fill:""+this.fill,stroke:""+this.layerColor,strokeWidth:this.strokeWidth+"px"}},gradient:function(){return Object(m.d)(this.color)},LinearGradient:function(){var t=this,e=this.$createElement;if(this.gradient){var i=Object.keys(this.color).sort((function(t,e){return parseFloat(t)-parseFloat(e)})).map((function(i,n){return e("stop",{key:n,attrs:{offset:i,"stop-color":t.color[i]}})}));return e("defs",[e("linearGradient",{attrs:{id:this.uid,x1:"100%",y1:"0%",x2:"0%",y2:"0%"}},[i])])}}},watch:{rate:{handler:function(t){this.startTime=Date.now(),this.startRate=this.value,this.endRate=bn(t),this.increase=this.endRate>this.startRate,this.duration=Math.abs(1e3*(this.startRate-this.endRate)/this.speed),this.speed?(Object(fn.a)(this.rafId),this.rafId=Object(fn.c)(this.animate)):this.$emit("input",this.endRate)},immediate:!0}},methods:{animate:function(){var t=Date.now(),e=Math.min((t-this.startTime)/this.duration,1)*(this.endRate-this.startRate)+this.startRate;this.$emit("input",bn(parseFloat(e.toFixed(1)))),(this.increase?e<this.endRate:e>this.endRate)&&(this.rafId=Object(fn.c)(this.animate))}},render:function(){var t=arguments[0];return t("div",{class:vn(),style:this.style},[t("svg",{attrs:{viewBox:"0 0 "+this.viewBoxSize+" "+this.viewBoxSize}},[this.LinearGradient,t("path",{class:vn("hover"),style:this.hoverStyle,attrs:{d:this.path}}),t("path",{attrs:{d:this.path,stroke:this.gradient?"url(#"+this.uid+")":this.color},class:vn("layer"),style:this.layerStyle})]),this.slots()||this.text&&t("div",{class:vn("text")},[this.text])])}}),Sn=Object(o.a)("col"),kn=Sn[0],xn=Sn[1],wn=kn({mixins:[ri("vanRow")],props:{span:[Number,String],offset:[Number,String],tag:{type:String,default:"div"}},computed:{style:function(){var t=this.index,e=(this.parent||{}).spaces;if(e&&e[t]){var i=e[t],n=i.left,s=i.right;return{paddingLeft:n?n+"px":null,paddingRight:s?s+"px":null}}}},methods:{onClick:function(t){this.$emit("click",t)}},render:function(){var t,e=arguments[0],i=this.span,n=this.offset;return e(this.tag,{style:this.style,class:xn((t={},t[i]=i,t["offset-"+n]=n,t)),on:{click:this.onClick}},[this.slots()])}}),Cn=Object(o.a)("collapse"),On=Cn[0],Tn=Cn[1],$n=On({mixins:[oi("vanCollapse")],props:{accordion:Boolean,value:[String,Number,Array],border:{type:Boolean,default:!0}},methods:{switch:function(t,e){this.accordion||(t=e?this.value.concat(t):this.value.filter((function(e){return e!==t}))),this.$emit("change",t),this.$emit("input",t)}},render:function(){var t,e=arguments[0];return e("div",{class:[Tn(),(t={},t[$t]=this.border,t)]},[this.slots()])}}),Bn=Object(o.a)("collapse-item"),In=Bn[0],jn=Bn[1],En=["title","icon","right-icon"],Dn=In({mixins:[ri("vanCollapse")],props:n(n({},Jt),{},{name:[Number,String],disabled:Boolean,isLink:{type:Boolean,default:!0}}),data:function(){return{show:null,inited:null}},computed:{currentName:function(){return Object(m.b)(this.name)?this.name:this.index},expanded:function(){var t=this;if(!this.parent)return null;var e=this.parent,i=e.value;return e.accordion?i===this.currentName:i.some((function(e){return e===t.currentName}))}},created:function(){this.show=this.expanded,this.inited=this.expanded},watch:{expanded:function(t,e){var i=this;null!==e&&(t&&(this.show=!0,this.inited=!0),(t?this.$nextTick:fn.c)((function(){var e=i.$refs,n=e.content,s=e.wrapper;if(n&&s){var r=n.offsetHeight;if(r){var o=r+"px";s.style.height=t?0:o,Object(fn.b)((function(){s.style.height=t?o:0}))}else i.onTransitionEnd()}})))}},methods:{onClick:function(){if(!this.disabled){var t=this.parent,e=this.currentName,i=t.accordion&&e===t.value?"":e;t.switch(i,!this.expanded)}},onTransitionEnd:function(){this.expanded?this.$refs.wrapper.style.height="":this.show=!1},genTitle:function(){var t=this,e=this.$createElement,i=this.disabled,s=this.expanded,r=En.reduce((function(e,i){return t.slots(i)&&(e[i]=function(){return t.slots(i)}),e}),{});return this.slots("value")&&(r.default=function(){return t.slots("value")}),e(se,{attrs:{role:"button",tabindex:i?-1:0,"aria-expanded":String(s)},class:jn("title",{disabled:i,expanded:s}),on:{click:this.onClick},scopedSlots:r,props:n({},this.$props)})},genContent:function(){var t=this.$createElement;if(this.inited)return t("div",{directives:[{name:"show",value:this.show}],ref:"wrapper",class:jn("wrapper"),on:{transitionend:this.onTransitionEnd}},[t("div",{ref:"content",class:jn("content")},[this.slots()])])}},render:function(){var t=arguments[0];return t("div",{class:[jn({border:this.index})]},[this.genTitle(),this.genContent()])}}),Nn=Object(o.a)("contact-card"),Ln=Nn[0],An=Nn[1],Pn=Nn[2];function zn(t,e,i,n){var s=e.type,o=e.editable;return t(se,r()([{attrs:{center:!0,border:!1,isLink:o,valueClass:An("value"),icon:"edit"===s?"contact":"add-square"},class:An([s]),on:{click:function(t){o&&d(n,"click",t)}}},h(n)]),["add"===s?e.addText||Pn("addText"):[t("div",[Pn("name")+"："+e.name]),t("div",[Pn("tel")+"："+e.tel])]])}zn.props={tel:String,name:String,addText:String,editable:{type:Boolean,default:!0},type:{type:String,default:"add"}};var Mn=Ln(zn),Fn=Object(o.a)("contact-edit"),Vn=Fn[0],Rn=Fn[1],_n=Fn[2],Hn={tel:"",name:""},Wn=Vn({props:{isEdit:Boolean,isSaving:Boolean,isDeleting:Boolean,showSetDefault:Boolean,setDefaultLabel:String,contactInfo:{type:Object,default:function(){return n({},Hn)}},telValidator:{type:Function,default:kt}},data:function(){return{data:n(n({},Hn),this.contactInfo),errorInfo:{name:"",tel:""}}},watch:{contactInfo:function(t){this.data=n(n({},Hn),t)}},methods:{onFocus:function(t){this.errorInfo[t]=""},getErrorMessageByKey:function(t){var e=this.data[t].trim();switch(t){case"name":return e?"":_n("nameInvalid");case"tel":return this.telValidator(e)?"":_n("telInvalid")}},onSave:function(){var t=this;["name","tel"].every((function(e){var i=t.getErrorMessageByKey(e);return i&&(t.errorInfo[e]=i),!i}))&&!this.isSaving&&this.$emit("save",this.data)},onDelete:function(){var t=this;Le.confirm({message:_n("confirmDelete")}).then((function(){t.$emit("delete",t.data)}))}},render:function(){var t=this,e=arguments[0],i=this.data,n=this.errorInfo,s=function(e){return function(){return t.onFocus(e)}};return e("div",{class:Rn()},[e("div",{class:Rn("fields")},[e(le,{attrs:{clearable:!0,maxlength:"30",label:_n("name"),placeholder:_n("nameEmpty"),errorMessage:n.name},on:{focus:s("name")},model:{value:i.name,callback:function(e){t.$set(i,"name",e)}}}),e(le,{attrs:{clearable:!0,type:"tel",label:_n("tel"),placeholder:_n("telEmpty"),errorMessage:n.tel},on:{focus:s("tel")},model:{value:i.tel,callback:function(e){t.$set(i,"tel",e)}}})]),this.showSetDefault&&e(se,{attrs:{title:this.setDefaultLabel,border:!1},class:Rn("switch-cell")},[e(Ye,{attrs:{size:24},on:{change:function(e){t.$emit("change-default",e)}},model:{value:i.isDefault,callback:function(e){t.$set(i,"isDefault",e)}}})]),e("div",{class:Rn("buttons")},[e($e,{attrs:{block:!0,round:!0,type:"danger",text:_n("save"),loading:this.isSaving},on:{click:this.onSave}}),this.isEdit&&e($e,{attrs:{block:!0,round:!0,text:_n("delete"),loading:this.isDeleting},on:{click:this.onDelete}})])])}}),qn=Object(o.a)("contact-list"),Yn=qn[0],Un=qn[1],Kn=qn[2];function Xn(t,e,i,n){var s=e.list&&e.list.map((function(i,s){function r(){d(n,"input",i.id),d(n,"select",i,s)}return t(se,{key:i.id,attrs:{isLink:!0,center:!0,valueClass:Un("item-value")},class:Un("item"),scopedSlots:{icon:function(){return t(st,{attrs:{name:"edit"},class:Un("edit"),on:{click:function(t){t.stopPropagation(),d(n,"edit",i,s)}}})},default:function(){var n=[i.name+"，"+i.tel];return i.isDefault&&e.defaultTagText&&n.push(t(mi,{attrs:{type:"danger",round:!0},class:Un("item-tag")},[e.defaultTagText])),n},"right-icon":function(){return t(bi,{attrs:{name:i.id,iconSize:16,checkedColor:wt},on:{click:r}})}},on:{click:r}})}));return t("div",r()([{class:Un()},h(n)]),[t(ui,{attrs:{value:e.value},class:Un("group")},[s]),t("div",{class:Un("bottom")},[t($e,{attrs:{round:!0,block:!0,type:"danger",text:e.addText||Kn("addText")},class:Un("add"),on:{click:function(){d(n,"add")}}})])])}Xn.props={value:null,list:Array,addText:String,defaultTagText:String};var Qn=Yn(Xn),Gn=i(3);var Zn=Object(o.a)("count-down"),Jn=Zn[0],ts=Zn[1],es=Jn({props:{millisecond:Boolean,time:{type:[Number,String],default:0},format:{type:String,default:"HH:mm:ss"},autoStart:{type:Boolean,default:!0}},data:function(){return{remain:0}},computed:{timeData:function(){return t=this.remain,{days:Math.floor(t/864e5),hours:Math.floor(t%864e5/36e5),minutes:Math.floor(t%36e5/6e4),seconds:Math.floor(t%6e4/1e3),milliseconds:Math.floor(t%1e3)};var t},formattedTime:function(){return function(t,e){var i=e.days,n=e.hours,s=e.minutes,r=e.seconds,o=e.milliseconds;if(-1===t.indexOf("DD")?n+=24*i:t=t.replace("DD",Object(Gn.b)(i)),-1===t.indexOf("HH")?s+=60*n:t=t.replace("HH",Object(Gn.b)(n)),-1===t.indexOf("mm")?r+=60*s:t=t.replace("mm",Object(Gn.b)(s)),-1===t.indexOf("ss")?o+=1e3*r:t=t.replace("ss",Object(Gn.b)(r)),-1!==t.indexOf("S")){var a=Object(Gn.b)(o,3);t=-1!==t.indexOf("SSS")?t.replace("SSS",a):-1!==t.indexOf("SS")?t.replace("SS",a.slice(0,2)):t.replace("S",a.charAt(0))}return t}(this.format,this.timeData)}},watch:{time:{immediate:!0,handler:"reset"}},activated:function(){this.keepAlivePaused&&(this.counting=!0,this.keepAlivePaused=!1,this.tick())},deactivated:function(){this.counting&&(this.pause(),this.keepAlivePaused=!0)},beforeDestroy:function(){this.pause()},methods:{start:function(){this.counting||(this.counting=!0,this.endTime=Date.now()+this.remain,this.tick())},pause:function(){this.counting=!1,Object(fn.a)(this.rafId)},reset:function(){this.pause(),this.remain=+this.time,this.autoStart&&this.start()},tick:function(){this.millisecond?this.microTick():this.macroTick()},microTick:function(){var t=this;this.rafId=Object(fn.c)((function(){t.counting&&(t.setRemain(t.getRemain()),t.remain>0&&t.microTick())}))},macroTick:function(){var t=this;this.rafId=Object(fn.c)((function(){if(t.counting){var e,i,n=t.getRemain();e=n,i=t.remain,(Math.floor(e/1e3)!==Math.floor(i/1e3)||0===n)&&t.setRemain(n),t.remain>0&&t.macroTick()}}))},getRemain:function(){return Math.max(this.endTime-Date.now(),0)},setRemain:function(t){this.remain=t,this.$emit("change",this.timeData),0===t&&(this.pause(),this.$emit("finish"))}},render:function(){var t=arguments[0];return t("div",{class:ts()},[this.slots("default",this.timeData)||this.formattedTime])}}),is=Object(o.a)("coupon"),ns=is[0],ss=is[1],rs=is[2];function os(t){var e=new Date(1e3*t);return e.getFullYear()+"."+Object(Gn.b)(e.getMonth()+1)+"."+Object(Gn.b)(e.getDate())}function as(t){return(t/100).toFixed(t%100==0?0:t%10==0?1:2)}var ls=ns({props:{coupon:Object,chosen:Boolean,disabled:Boolean,currency:{type:String,default:"¥"}},computed:{validPeriod:function(){var t=this.coupon,e=t.startAt,i=t.endAt;return os(e)+" - "+os(i)},faceAmount:function(){var t,e=this.coupon;if(e.valueDesc)return e.valueDesc+"<span>"+(e.unitDesc||"")+"</span>";if(e.denominations){var i=as(e.denominations);return"<span>"+this.currency+"</span> "+i}return e.discount?rs("discount",((t=e.discount)/10).toFixed(t%10==0?0:1)):""},conditionMessage:function(){var t=as(this.coupon.originCondition);return"0"===t?rs("unlimited"):rs("condition",t)}},render:function(){var t=arguments[0],e=this.coupon,i=this.disabled,n=i&&e.reason||e.description;return t("div",{class:ss({disabled:i})},[t("div",{class:ss("content")},[t("div",{class:ss("head")},[t("h2",{class:ss("amount"),domProps:{innerHTML:this.faceAmount}}),t("p",{class:ss("condition")},[this.coupon.condition||this.conditionMessage])]),t("div",{class:ss("body")},[t("p",{class:ss("name")},[e.name]),t("p",{class:ss("valid")},[this.validPeriod]),!this.disabled&&t(ln,{attrs:{size:18,value:this.chosen,checkedColor:wt},class:ss("corner")})])]),n&&t("p",{class:ss("description")},[n])])}}),cs=Object(o.a)("coupon-cell"),us=cs[0],hs=cs[1],ds=cs[2];function fs(t,e,i,n){var s=e.coupons[+e.chosenCoupon]?"van-coupon-cell--selected":"",o=function(t){var e=t.coupons,i=t.chosenCoupon,n=t.currency,s=e[+i];return s?"-"+n+((s.value||s.denominations||0)/100).toFixed(2):0===e.length?ds("tips"):ds("count",e.length)}(e);return t(se,r()([{class:hs(),attrs:{value:o,title:e.title||ds("title"),border:e.border,isLink:e.editable,valueClass:s}},h(n,!0)]))}fs.model={prop:"chosenCoupon"},fs.props={title:String,coupons:{type:Array,default:function(){return[]}},currency:{type:String,default:"¥"},border:{type:Boolean,default:!0},editable:{type:Boolean,default:!0},chosenCoupon:{type:[Number,String],default:-1}};var ps,ms=us(fs),vs=Object(o.a)("tab"),gs=vs[0],bs=vs[1],ys=gs({mixins:[ri("vanTabs")],props:n(n({},Zt),{},{dot:Boolean,name:[Number,String],info:[Number,String],badge:[Number,String],title:String,titleStyle:null,disabled:Boolean}),data:function(){return{inited:!1}},computed:{computedName:function(){return Object(m.b)(this.name)?this.name:this.index},isActive:function(){var t=this.computedName===this.parent.currentName;return t&&(this.inited=!0),t}},watch:{title:function(){this.parent.setLine()},inited:function(t){var e=this;this.parent.lazyRender&&t&&this.$nextTick((function(){e.parent.$emit("rendered",e.computedName,e.title)}))}},render:function(t){var e=this.slots,i=this.parent,n=this.isActive,s=this.inited||i.scrollspy||!i.lazyRender,r=i.scrollspy||n,o=s?e():t();return i.animated?t("div",{attrs:{role:"tabpanel","aria-hidden":!n},class:bs("pane-wrapper",{inactive:!n})},[t("div",{class:bs("pane")},[o])]):t("div",{directives:[{name:"show",value:r}],attrs:{role:"tabpanel"},class:bs("pane")},[o])}});function Ss(t){var e=window.getComputedStyle(t),i="none"===e.display,n=null===t.offsetParent&&"fixed"!==e.position;return i||n}var ks=Object(o.a)("tab"),xs=ks[0],ws=ks[1],Cs=xs({props:{dot:Boolean,type:String,info:[Number,String],color:String,title:String,isActive:Boolean,ellipsis:Boolean,disabled:Boolean,scrollable:Boolean,activeColor:String,inactiveColor:String,swipeThreshold:[Number,String]},computed:{style:function(){var t={},e=this.color,i=this.isActive,n="card"===this.type;e&&n&&(t.borderColor=e,this.disabled||(i?t.backgroundColor=e:t.color=e));var s=i?this.activeColor:this.inactiveColor;return s&&(t.color=s),this.scrollable&&this.ellipsis&&(t.flexBasis=88/this.swipeThreshold+"%"),t}},methods:{onClick:function(){this.$emit("click")},genText:function(){var t=this.$createElement,e=t("span",{class:ws("text",{ellipsis:this.ellipsis})},[this.slots()||this.title]);return this.dot||Object(m.b)(this.info)&&""!==this.info?t("span",{class:ws("text-wrapper")},[e,t(Z,{attrs:{dot:this.dot,info:this.info}})]):e}},render:function(){var t=arguments[0];return t("div",{attrs:{role:"tab","aria-selected":this.isActive},class:[ws({active:this.isActive,disabled:this.disabled,complete:!this.ellipsis})],style:this.style,on:{click:this.onClick}},[this.genText()])}}),Os=Object(o.a)("sticky"),Ts=Os[0],$s=Os[1],Bs=Ts({mixins:[H((function(t,e){if(this.scroller||(this.scroller=A(this.$el)),this.observer){var i=e?"observe":"unobserve";this.observer[i](this.$el)}t(this.scroller,"scroll",this.onScroll,!0),this.onScroll()}))],props:{zIndex:[Number,String],container:null,offsetTop:{type:[Number,String],default:0}},data:function(){return{fixed:!1,height:0,transform:0}},computed:{style:function(){if(this.fixed){var t={};return Object(m.b)(this.zIndex)&&(t.zIndex=this.zIndex),this.offsetTop&&this.fixed&&(t.top=this.offsetTop+"px"),this.transform&&(t.transform="translate3d(0, "+this.transform+"px, 0)"),t}}},created:function(){var t=this;!m.f&&window.IntersectionObserver&&(this.observer=new IntersectionObserver((function(e){e[0].intersectionRatio>0&&t.onScroll()}),{root:document.body}))},methods:{onScroll:function(){var t=this;if(!Ss(this.$el)){this.height=this.$el.offsetHeight;var e=this.container,i=+this.offsetTop,n=P(window),s=V(this.$el),r=function(){t.$emit("scroll",{scrollTop:n,isFixed:t.fixed})};if(e){var o=s+e.offsetHeight;if(n+i+this.height>o){var a=this.height+n-o;return a<this.height?(this.fixed=!0,this.transform=-(a+i)):this.fixed=!1,void r()}}n+i>s?(this.fixed=!0,this.transform=0):this.fixed=!1,r()}}},render:function(){var t=arguments[0],e=this.fixed,i={height:e?this.height+"px":null};return t("div",{style:i},[t("div",{class:$s({fixed:e}),style:this.style},[this.slots()])])}}),Is=Object(o.a)("tabs"),js=Is[0],Es=Is[1],Ds=js({mixins:[R],props:{count:Number,duration:[Number,String],animated:Boolean,swipeable:Boolean,currentIndex:Number},computed:{style:function(){if(this.animated)return{transform:"translate3d("+-1*this.currentIndex*100+"%, 0, 0)",transitionDuration:this.duration+"s"}},listeners:function(){if(this.swipeable)return{touchstart:this.touchStart,touchmove:this.touchMove,touchend:this.onTouchEnd,touchcancel:this.onTouchEnd}}},methods:{onTouchEnd:function(){var t=this.direction,e=this.deltaX,i=this.currentIndex;"horizontal"===t&&this.offsetX>=50&&(e>0&&0!==i?this.$emit("change",i-1):e<0&&i!==this.count-1&&this.$emit("change",i+1))},genChildren:function(){var t=this.$createElement;return this.animated?t("div",{class:Es("track"),style:this.style},[this.slots()]):this.slots()}},render:function(){var t=arguments[0];return t("div",{class:Es("content",{animated:this.animated}),on:n({},this.listeners)},[this.genChildren()])}}),Ns=Object(o.a)("tabs"),Ls=Ns[0],As=Ns[1],Ps=Ls({mixins:[oi("vanTabs"),H((function(t){this.scroller||(this.scroller=A(this.$el)),t(window,"resize",this.resize,!0),this.scrollspy&&t(this.scroller,"scroll",this.onScroll,!0)}))],model:{prop:"active"},props:{color:String,sticky:Boolean,animated:Boolean,swipeable:Boolean,scrollspy:Boolean,background:String,lineWidth:[Number,String],lineHeight:[Number,String],titleActiveColor:String,titleInactiveColor:String,type:{type:String,default:"line"},active:{type:[Number,String],default:0},border:{type:Boolean,default:!0},ellipsis:{type:Boolean,default:!0},duration:{type:[Number,String],default:.3},offsetTop:{type:[Number,String],default:0},lazyRender:{type:Boolean,default:!0},swipeThreshold:{type:[Number,String],default:4}},data:function(){return{position:"",currentIndex:null,lineStyle:{backgroundColor:this.color}}},computed:{scrollable:function(){return this.children.length>this.swipeThreshold||!this.ellipsis},navStyle:function(){return{borderColor:this.color,background:this.background}},currentName:function(){var t=this.children[this.currentIndex];if(t)return t.computedName},scrollOffset:function(){return this.sticky?+this.offsetTop+this.tabHeight:0}},watch:{color:"setLine",active:function(t){t!==this.currentName&&this.setCurrentIndexByName(t)},children:function(){var t=this;this.setCurrentIndexByName(this.currentName||this.active),this.setLine(),this.$nextTick((function(){t.scrollIntoView(!0)}))},currentIndex:function(){this.scrollIntoView(),this.setLine(),this.stickyFixed&&!this.scrollspy&&F(Math.ceil(V(this.$el)-this.offsetTop))},scrollspy:function(t){t?b(this.scroller,"scroll",this.onScroll,!0):y(this.scroller,"scroll",this.onScroll)}},mounted:function(){this.onShow()},activated:function(){this.onShow(),this.setLine()},methods:{resize:function(){this.setLine()},onShow:function(){var t=this;this.$nextTick((function(){var e;t.inited=!0,t.tabHeight=N(e=t.$refs.wrap)?e.innerHeight:e.getBoundingClientRect().height,t.scrollIntoView(!0)}))},setLine:function(){var t=this,e=this.inited;this.$nextTick((function(){var i=t.$refs.titles;if(i&&i[t.currentIndex]&&"line"===t.type&&!Ss(t.$el)){var n=i[t.currentIndex].$el,s=t.lineWidth,r=t.lineHeight,o=Object(m.b)(s)?s:n.offsetWidth/2,a=n.offsetLeft+n.offsetWidth/2,l={width:Object(U.a)(o),backgroundColor:t.color,transform:"translateX("+a+"px) translateX(-50%)"};if(e&&(l.transitionDuration=t.duration+"s"),Object(m.b)(r)){var c=Object(U.a)(r);l.height=c,l.borderRadius=c}t.lineStyle=l}}))},setCurrentIndexByName:function(t){var e=this.children.filter((function(e){return e.computedName===t})),i=(this.children[0]||{}).index||0;this.setCurrentIndex(e.length?e[0].index:i)},setCurrentIndex:function(t){if(t=this.findAvailableTab(t),Object(m.b)(t)&&t!==this.currentIndex){var e=null!==this.currentIndex;this.currentIndex=t,this.$emit("input",this.currentName),e&&this.$emit("change",this.currentName,this.children[t].title)}},findAvailableTab:function(t){for(var e=t<this.currentIndex?-1:1;t>=0&&t<this.children.length;){if(!this.children[t].disabled)return t;t+=e}},onClick:function(t){var e=this.children[t],i=e.title,n=e.disabled,s=e.computedName;n?this.$emit("disabled",s,i):(this.setCurrentIndex(t),this.scrollToCurrentContent(),this.$emit("click",s,i))},scrollIntoView:function(t){var e=this.$refs.titles;if(this.scrollable&&e&&e[this.currentIndex]){var i=this.$refs.nav,n=e[this.currentIndex].$el;!function(t,e,i){Object(fn.a)(ps);var n=0,s=t.scrollLeft,r=0===i?1:Math.round(1e3*i/16);!function i(){t.scrollLeft+=(e-s)/r,++n<r&&(ps=Object(fn.c)(i))}()}(i,n.offsetLeft-(i.offsetWidth-n.offsetWidth)/2,t?0:+this.duration)}},onSticktScroll:function(t){this.stickyFixed=t.isFixed,this.$emit("scroll",t)},scrollToCurrentContent:function(){var t=this;if(this.scrollspy){var e=this.children[this.currentIndex],i=null==e?void 0:e.$el;if(i){var n=V(i,this.scroller)-this.scrollOffset;this.lockScroll=!0,function(t,e,i,n){var s=P(t),r=s<e,o=0===i?1:Math.round(1e3*i/16),a=(e-s)/o;!function i(){s+=a,(r&&s>e||!r&&s<e)&&(s=e),z(t,s),r&&s<e||!r&&s>e?Object(fn.c)(i):n&&Object(fn.c)(n)}()}(this.scroller,n,+this.duration,(function(){t.lockScroll=!1}))}}},onScroll:function(){if(this.scrollspy&&!this.lockScroll){var t=this.getCurrentIndexOnScroll();this.setCurrentIndex(t)}},getCurrentIndexOnScroll:function(){for(var t,e=this.children,i=0;i<e.length;i++){if((N(t=e[i].$el)?0:t.getBoundingClientRect().top)>this.scrollOffset)return 0===i?0:i-1}return e.length-1}},render:function(){var t,e=this,i=arguments[0],n=this.type,s=this.ellipsis,r=this.animated,o=this.scrollable,a=this.children.map((function(t,r){return i(Cs,{ref:"titles",refInFor:!0,attrs:{type:n,dot:t.dot,info:Object(m.b)(t.badge)?t.badge:t.info,title:t.title,color:e.color,isActive:r===e.currentIndex,ellipsis:s,disabled:t.disabled,scrollable:o,activeColor:e.titleActiveColor,inactiveColor:e.titleInactiveColor,swipeThreshold:e.swipeThreshold},style:t.titleStyle,scopedSlots:{default:function(){return t.slots("title")}},on:{click:function(){e.onClick(r),Qt(t.$router,t)}}})})),l=i("div",{ref:"wrap",class:[As("wrap",{scrollable:o}),(t={},t[$t]="line"===n&&this.border,t)]},[i("div",{ref:"nav",attrs:{role:"tablist"},class:As("nav",[n]),style:this.navStyle},[this.slots("nav-left"),a,"line"===n&&i("div",{class:As("line"),style:this.lineStyle}),this.slots("nav-right")])]);return i("div",{class:As([n])},[this.sticky?i(Bs,{attrs:{container:this.$el,offsetTop:this.offsetTop},on:{scroll:this.onSticktScroll}},[l]):l,i(Ds,{attrs:{count:this.children.length,animated:r,duration:this.duration,swipeable:this.swipeable,currentIndex:this.currentIndex},on:{change:this.setCurrentIndex}},[this.slots()])])}}),zs=Object(o.a)("coupon-list"),Ms=zs[0],Fs=zs[1],Vs=zs[2],Rs=Ms({model:{prop:"code"},props:{code:String,closeButtonText:String,inputPlaceholder:String,enabledTitle:String,disabledTitle:String,exchangeButtonText:String,exchangeButtonLoading:Boolean,exchangeButtonDisabled:Boolean,exchangeMinLength:{type:Number,default:1},chosenCoupon:{type:Number,default:-1},coupons:{type:Array,default:function(){return[]}},disabledCoupons:{type:Array,default:function(){return[]}},displayedCouponIndex:{type:Number,default:-1},showExchangeBar:{type:Boolean,default:!0},showCloseButton:{type:Boolean,default:!0},showCount:{type:Boolean,default:!0},currency:{type:String,default:"¥"},emptyImage:{type:String,default:"https://img.yzcdn.cn/vant/coupon-empty.png"}},data:function(){return{tab:0,winHeight:window.innerHeight,currentCode:this.code||""}},computed:{buttonDisabled:function(){return!this.exchangeButtonLoading&&(this.exchangeButtonDisabled||!this.currentCode||this.currentCode.length<this.exchangeMinLength)},listStyle:function(){return{height:this.winHeight-(this.showExchangeBar?140:94)+"px"}}},watch:{code:function(t){this.currentCode=t},currentCode:function(t){this.$emit("input",t)},displayedCouponIndex:"scrollToShowCoupon"},mounted:function(){this.scrollToShowCoupon(this.displayedCouponIndex)},methods:{onClickExchangeButton:function(){this.$emit("exchange",this.currentCode),this.code||(this.currentCode="")},scrollToShowCoupon:function(t){var e=this;-1!==t&&this.$nextTick((function(){var i=e.$refs,n=i.card,s=i.list;s&&n&&n[t]&&(s.scrollTop=n[t].$el.offsetTop-100)}))},genEmpty:function(){var t=this.$createElement;return t("div",{class:Fs("empty")},[t("img",{attrs:{src:this.emptyImage}}),t("p",[Vs("empty")])])},genExchangeButton:function(){return(0,this.$createElement)($e,{attrs:{plain:!0,type:"danger",text:this.exchangeButtonText||Vs("exchange"),loading:this.exchangeButtonLoading,disabled:this.buttonDisabled},class:Fs("exchange"),on:{click:this.onClickExchangeButton}})}},render:function(){var t=this,e=arguments[0],i=this.coupons,n=this.disabledCoupons,s=this.showCount?" ("+i.length+")":"",r=(this.enabledTitle||Vs("enable"))+s,o=this.showCount?" ("+n.length+")":"",a=(this.disabledTitle||Vs("disabled"))+o,l=this.showExchangeBar&&e("div",{class:Fs("exchange-bar")},[e(le,{attrs:{clearable:!0,border:!1,placeholder:this.inputPlaceholder||Vs("placeholder"),maxlength:"20"},class:Fs("field"),model:{value:t.currentCode,callback:function(e){t.currentCode=e}}}),this.genExchangeButton()]),c=function(e){return function(){return t.$emit("change",e)}},u=e(ys,{attrs:{title:r}},[e("div",{class:Fs("list",{"with-bottom":this.showCloseButton}),style:this.listStyle},[i.map((function(i,n){return e(ls,{ref:"card",key:i.id,attrs:{coupon:i,currency:t.currency,chosen:n===t.chosenCoupon},nativeOn:{click:c(n)}})})),!i.length&&this.genEmpty()])]),h=e(ys,{attrs:{title:a}},[e("div",{class:Fs("list",{"with-bottom":this.showCloseButton}),style:this.listStyle},[n.map((function(i){return e(ls,{attrs:{disabled:!0,coupon:i,currency:t.currency},key:i.id})})),!n.length&&this.genEmpty()])]);return e("div",{class:Fs()},[l,e(Ps,{class:Fs("tab"),attrs:{border:!1},model:{value:t.tab,callback:function(e){t.tab=e}}},[u,h]),e("div",{class:Fs("bottom")},[e($e,{directives:[{name:"show",value:this.showCloseButton}],attrs:{round:!0,type:"danger",block:!0,text:this.closeButtonText||Vs("close")},class:Fs("close"),on:{click:c(-1)}})])])}}),_s=n(n({},xt),{},{value:null,filter:Function,showToolbar:{type:Boolean,default:!0},formatter:{type:Function,default:function(t,e){return e}}}),Hs={data:function(){return{innerValue:this.formatValue(this.value)}},computed:{originColumns:function(){var t=this;return this.ranges.map((function(e){var i=e.type,n=e.range,s=function(t,e){for(var i=-1,n=Array(t);++i<t;)n[i]=e(i);return n}(n[1]-n[0]+1,(function(t){return Object(Gn.b)(n[0]+t)}));return t.filter&&(s=t.filter(i,s)),{type:i,values:s}}))},columns:function(){var t=this;return this.originColumns.map((function(e){return{values:e.values.map((function(i){return t.formatter(e.type,i)}))}}))}},watch:{columns:"updateColumnValue",innerValue:function(t){this.$emit("input",t)}},mounted:function(){var t=this;this.updateColumnValue(),this.$nextTick((function(){t.updateInnerValue()}))},methods:{getPicker:function(){return this.$refs.picker},onConfirm:function(){this.$emit("confirm",this.innerValue)},onCancel:function(){this.$emit("cancel")}},render:function(){var t=this,e=arguments[0],i={};return Object.keys(xt).forEach((function(e){i[e]=t[e]})),e(_t,{ref:"picker",attrs:{columns:this.columns},on:{change:this.onChange,confirm:this.onConfirm,cancel:this.onCancel},props:n({},i)})}},Ws=(0,Object(o.a)("time-picker")[0])({mixins:[Hs],props:n(n({},_s),{},{minHour:{type:[Number,String],default:0},maxHour:{type:[Number,String],default:23},minMinute:{type:[Number,String],default:0},maxMinute:{type:[Number,String],default:59}}),computed:{ranges:function(){return[{type:"hour",range:[+this.minHour,+this.maxHour]},{type:"minute",range:[+this.minMinute,+this.maxMinute]}]}},watch:{filter:"updateInnerValue",minHour:"updateInnerValue",maxHour:"updateInnerValue",minMinute:"updateInnerValue",maxMinute:"updateInnerValue",value:function(t){(t=this.formatValue(t))!==this.innerValue&&(this.innerValue=t,this.updateColumnValue())}},methods:{formatValue:function(t){t||(t=Object(Gn.b)(this.minHour)+":"+Object(Gn.b)(this.minMinute));var e=t.split(":"),i=e[0],n=e[1];return(i=Object(Gn.b)(jt(i,this.minHour,this.maxHour)))+":"+(n=Object(Gn.b)(jt(n,this.minMinute,this.maxMinute)))},updateInnerValue:function(){var t=this.getPicker().getIndexes(),e=t[0],i=t[1],n=this.originColumns,s=n[0],r=n[1],o=s.values[e]||s.values[0],a=r.values[i]||r.values[0];this.innerValue=this.formatValue(o+":"+a),this.updateColumnValue()},onChange:function(t){var e=this;this.updateInnerValue(),this.$nextTick((function(){e.$nextTick((function(){e.$emit("change",t)}))}))},updateColumnValue:function(){var t=this,e=this.formatter,i=this.innerValue.split(":"),n=[e("hour",i[0]),e("minute",i[1])];this.$nextTick((function(){t.getPicker().setValues(n)}))}}}),qs=(new Date).getFullYear(),Ys=(0,Object(o.a)("date-picker")[0])({mixins:[Hs],props:n(n({},_s),{},{type:{type:String,default:"datetime"},minDate:{type:Date,default:function(){return new Date(qs-10,0,1)},validator:Ei},maxDate:{type:Date,default:function(){return new Date(qs+10,11,31)},validator:Ei}}),watch:{filter:"updateInnerValue",minDate:"updateInnerValue",maxDate:"updateInnerValue",value:function(t){(t=this.formatValue(t)).valueOf()!==this.innerValue.valueOf()&&(this.innerValue=t)}},computed:{ranges:function(){var t=this.getBoundary("max",this.innerValue),e=t.maxYear,i=t.maxDate,n=t.maxMonth,s=t.maxHour,r=t.maxMinute,o=this.getBoundary("min",this.innerValue),a=o.minYear,l=o.minDate,c=[{type:"year",range:[a,e]},{type:"month",range:[o.minMonth,n]},{type:"day",range:[l,i]},{type:"hour",range:[o.minHour,s]},{type:"minute",range:[o.minMinute,r]}];return"date"===this.type&&(c=c.slice(0,3)),"year-month"===this.type&&(c=c.slice(0,2)),"month-day"===this.type&&(c=c.slice(1,3)),c}},methods:{formatValue:function(t){return Ei(t)||(t=this.minDate),t=Math.max(t,this.minDate.getTime()),t=Math.min(t,this.maxDate.getTime()),new Date(t)},getBoundary:function(t,e){var i,n=this[t+"Date"],s=n.getFullYear(),r=1,o=1,a=0,l=0;return"max"===t&&(r=12,o=_i(e.getFullYear(),e.getMonth()+1),a=23,l=59),e.getFullYear()===s&&(r=n.getMonth()+1,e.getMonth()+1===r&&(o=n.getDate(),e.getDate()===o&&(a=n.getHours(),e.getHours()===a&&(l=n.getMinutes())))),(i={})[t+"Year"]=s,i[t+"Month"]=r,i[t+"Date"]=o,i[t+"Hour"]=a,i[t+"Minute"]=l,i},updateInnerValue:function(){var t,e,i,n=this,s=this.type,r=this.getPicker().getIndexes(),o=function(t){return function(t){if(!t)return 0;for(;Object(ji.a)(parseInt(t,10));){if(!(t.length>1))return 0;t=t.slice(1)}return parseInt(t,10)}(n.originColumns[t].values[r[t]])};"month-day"===s?(t=this.innerValue.getFullYear(),e=o(0),i=o(1)):(t=o(0),e=o(1),i="year-month"===s?1:o(2));var a=_i(t,e);i=i>a?a:i;var l=0,c=0;"datetime"===s&&(l=o(3),c=o(4));var u=new Date(t,e-1,i,l,c);this.innerValue=this.formatValue(u)},onChange:function(t){var e=this;this.updateInnerValue(),this.$nextTick((function(){e.$nextTick((function(){e.$emit("change",t)}))}))},updateColumnValue:function(){var t=this,e=this.innerValue,i=this.formatter,n=[i("year",""+e.getFullYear()),i("month",Object(Gn.b)(e.getMonth()+1)),i("day",Object(Gn.b)(e.getDate()))];"datetime"===this.type&&n.push(i("hour",Object(Gn.b)(e.getHours())),i("minute",Object(Gn.b)(e.getMinutes()))),"year-month"===this.type&&(n=n.slice(0,2)),"month-day"===this.type&&(n=n.slice(1,3)),this.$nextTick((function(){t.getPicker().setValues(n)}))}}}),Us=Object(o.a)("datetime-picker"),Ks=Us[0],Xs=Us[1],Qs=Ks({props:n(n({},Ws.props),Ys.props),methods:{getPicker:function(){return this.$refs.root.getPicker()}},render:function(){var t=arguments[0],e="time"===this.type?Ws:Ys;return t(e,{ref:"root",class:Xs(),props:n({},this.$props),on:n({},this.$listeners)})}}),Gs=Object(o.a)("divider"),Zs=Gs[0],Js=Gs[1];function tr(t,e,i,n){var s;return t("div",r()([{attrs:{role:"separator"},style:{borderColor:e.borderColor},class:Js((s={dashed:e.dashed,hairline:e.hairline},s["content-"+e.contentPosition]=i.default,s))},h(n,!0)]),[i.default&&i.default()])}tr.props={dashed:Boolean,hairline:{type:Boolean,default:!0},contentPosition:{type:String,default:"center"}};var er=Zs(tr),ir=Object(o.a)("dropdown-item"),nr=ir[0],sr=ir[1],rr=nr({mixins:[_({ref:"wrapper"}),ri("vanDropdownMenu")],props:{value:null,title:String,disabled:Boolean,titleClass:String,options:{type:Array,default:function(){return[]}},lazyRender:{type:Boolean,default:!0}},data:function(){return{transition:!0,showPopup:!1,showWrapper:!1}},computed:{displayTitle:function(){var t=this;if(this.title)return this.title;var e=this.options.filter((function(e){return e.value===t.value}));return e.length?e[0].text:""}},watch:{showPopup:function(t){this.bindScroll(t)}},beforeCreate:function(){var t=this,e=function(e){return function(){return t.$emit(e)}};this.onOpen=e("open"),this.onClose=e("close"),this.onOpened=e("opened")},methods:{toggle:function(t,e){void 0===t&&(t=!this.showPopup),void 0===e&&(e={}),t!==this.showPopup&&(this.transition=!e.immediate,this.showPopup=t,t&&(this.parent.updateOffset(),this.showWrapper=!0))},bindScroll:function(t){(t?b:y)(this.parent.scroller,"scroll",this.onScroll,!0)},onScroll:function(){this.parent.updateOffset()},onClickWrapper:function(t){this.getContainer&&t.stopPropagation()}},render:function(){var t=this,e=arguments[0],i=this.parent,n=i.zIndex,s=i.offset,r=i.overlay,o=i.duration,a=i.direction,l=i.activeColor,c=i.closeOnClickOverlay,u=this.options.map((function(i){var n=i.value===t.value;return e(se,{attrs:{clickable:!0,icon:i.icon,title:i.text},key:i.value,class:sr("option",{active:n}),style:{color:n?l:""},on:{click:function(){t.showPopup=!1,i.value!==t.value&&(t.$emit("input",i.value),t.$emit("change",i.value))}}},[n&&e(st,{class:sr("icon"),attrs:{color:l,name:"success"}})])})),h={zIndex:n};return"down"===a?h.top=s+"px":h.bottom=s+"px",e("div",[e("div",{directives:[{name:"show",value:this.showWrapper}],ref:"wrapper",style:h,class:sr([a]),on:{click:this.onClickWrapper}},[e(lt,{attrs:{overlay:r,position:"down"===a?"top":"bottom",duration:this.transition?o:0,lazyRender:this.lazyRender,overlayStyle:{position:"absolute"},closeOnClickOverlay:c},class:sr("content"),on:{open:this.onOpen,close:this.onClose,opened:this.onOpened,closed:function(){t.showWrapper=!1,t.$emit("closed")}},model:{value:t.showPopup,callback:function(e){t.showPopup=e}}},[u,this.slots("default")])])])}}),or=function(t){return l.a.extend({props:{closeOnClickOutside:{type:Boolean,default:!0}},data:function(){var e=this;return{clickOutsideHandler:function(i){e.closeOnClickOutside&&!e.$el.contains(i.target)&&e[t.method]()}}},mounted:function(){b(document,t.event,this.clickOutsideHandler)},beforeDestroy:function(){y(document,t.event,this.clickOutsideHandler)}})},ar=Object(o.a)("dropdown-menu"),lr=ar[0],cr=ar[1],ur=lr({mixins:[oi("vanDropdownMenu"),or({event:"click",method:"onClickOutside"})],props:{zIndex:[Number,String],activeColor:String,overlay:{type:Boolean,default:!0},duration:{type:[Number,String],default:.2},direction:{type:String,default:"down"},closeOnClickOverlay:{type:Boolean,default:!0}},data:function(){return{offset:0}},computed:{scroller:function(){return A(this.$el)},opened:function(){return this.children.some((function(t){return t.showWrapper}))},barStyle:function(){if(this.opened&&Object(m.b)(this.zIndex))return{zIndex:1+this.zIndex}}},methods:{updateOffset:function(){if(this.$refs.bar){var t=this.$refs.bar.getBoundingClientRect();"down"===this.direction?this.offset=t.bottom:this.offset=window.innerHeight-t.top}},toggleItem:function(t){this.children.forEach((function(e,i){i===t?e.toggle():e.showPopup&&e.toggle(!1,{immediate:!0})}))},onClickOutside:function(){this.children.forEach((function(t){t.toggle(!1)}))}},render:function(){var t=this,e=arguments[0],i=this.children.map((function(i,n){return e("div",{attrs:{role:"button",tabindex:i.disabled?-1:0},class:cr("item",{disabled:i.disabled}),on:{click:function(){i.disabled||t.toggleItem(n)}}},[e("span",{class:[cr("title",{active:i.showPopup,down:i.showPopup===("down"===t.direction)}),i.titleClass],style:{color:i.showPopup?t.activeColor:""}},[e("div",{class:"van-ellipsis"},[i.slots("title")||i.displayTitle])])])}));return e("div",{class:cr()},[e("div",{ref:"bar",style:this.barStyle,class:cr("bar",{opened:this.opened})},[i]),this.slots("default")])}}),hr={render:function(){var t=arguments[0],e=function(e,i,n){return t("stop",{attrs:{"stop-color":e,offset:i+"%","stop-opacity":n}})};return t("svg",{attrs:{viewBox:"0 0 160 160",xmlns:"http://www.w3.org/2000/svg"}},[t("defs",[t("linearGradient",{attrs:{id:"c",x1:"64.022%",y1:"100%",x2:"64.022%",y2:"0%"}},[e("#FFF",0,.5),e("#F2F3F5",100)]),t("linearGradient",{attrs:{id:"d",x1:"64.022%",y1:"96.956%",x2:"64.022%",y2:"0%"}},[e("#F2F3F5",0,.3),e("#F2F3F5",100)]),t("linearGradient",{attrs:{id:"h",x1:"50%",y1:"0%",x2:"50%",y2:"84.459%"}},[e("#EBEDF0",0),e("#DCDEE0",100,0)]),t("linearGradient",{attrs:{id:"i",x1:"100%",y1:"0%",x2:"100%",y2:"100%"}},[e("#EAEDF0",0),e("#DCDEE0",100)]),t("linearGradient",{attrs:{id:"k",x1:"100%",y1:"100%",x2:"100%",y2:"0%"}},[e("#EAEDF0",0),e("#DCDEE0",100)]),t("linearGradient",{attrs:{id:"m",x1:"0%",y1:"43.982%",x2:"100%",y2:"54.703%"}},[e("#EAEDF0",0),e("#DCDEE0",100)]),t("linearGradient",{attrs:{id:"n",x1:"94.535%",y1:"43.837%",x2:"5.465%",y2:"54.948%"}},[e("#EAEDF0",0),e("#DCDEE0",100)]),t("radialGradient",{attrs:{id:"g",cx:"50%",cy:"0%",fx:"50%",fy:"0%",r:"100%",gradientTransform:"matrix(0 1 -.54835 0 .5 -.5)"}},[e("#EBEDF0",0),e("#FFF",100,0)])]),t("g",{attrs:{fill:"none","fill-rule":"evenodd"}},[t("g",{attrs:{opacity:".8"}},[t("path",{attrs:{d:"M0 124V46h20v20h14v58H0z",fill:"url(#c)",transform:"matrix(-1 0 0 1 36 7)"}}),t("path",{attrs:{d:"M40.5 5a8.504 8.504 0 018.13 6.009l.12-.005L49 11a8 8 0 11-1 15.938V27H34v-.174a6.5 6.5 0 11-1.985-12.808A8.5 8.5 0 0140.5 5z",fill:"url(#d)",transform:"translate(2 7)"}}),t("path",{attrs:{d:"M96.016 0a4.108 4.108 0 013.934 2.868l.179-.004c2.138 0 3.871 1.71 3.871 3.818 0 2.109-1.733 3.818-3.871 3.818-.164 0-.325-.01-.484-.03v.03h-6.774v-.083a3.196 3.196 0 01-.726.083C90.408 10.5 89 9.111 89 7.398c0-1.636 1.284-2.976 2.911-3.094a3.555 3.555 0 01-.008-.247c0-2.24 1.842-4.057 4.113-4.057z",fill:"url(#d)",transform:"translate(2 7)"}}),t("path",{attrs:{d:"M121 8h22.231v14H152v77.37h-31V8z",fill:"url(#c)",transform:"translate(2 7)"}})]),t("path",{attrs:{fill:"url(#g)",d:"M0 139h160v21H0z"}}),t("path",{attrs:{d:"M37 18a7 7 0 013 13.326v26.742c0 1.23-.997 2.227-2.227 2.227h-1.546A2.227 2.227 0 0134 58.068V31.326A7 7 0 0137 18z",fill:"url(#h)","fill-rule":"nonzero",transform:"translate(43 36)"}}),t("g",{attrs:{opacity:".6","stroke-linecap":"round","stroke-width":"7"}},[t("path",{attrs:{d:"M20.875 11.136a18.868 18.868 0 00-5.284 13.121c0 5.094 2.012 9.718 5.284 13.12",stroke:"url(#i)",transform:"translate(43 36)"}}),t("path",{attrs:{d:"M9.849 0C3.756 6.225 0 14.747 0 24.146c0 9.398 3.756 17.92 9.849 24.145",stroke:"url(#i)",transform:"translate(43 36)"}}),t("path",{attrs:{d:"M57.625 11.136a18.868 18.868 0 00-5.284 13.121c0 5.094 2.012 9.718 5.284 13.12",stroke:"url(#k)",transform:"rotate(-180 76.483 42.257)"}}),t("path",{attrs:{d:"M73.216 0c-6.093 6.225-9.849 14.747-9.849 24.146 0 9.398 3.756 17.92 9.849 24.145",stroke:"url(#k)",transform:"rotate(-180 89.791 42.146)"}})]),t("g",{attrs:{transform:"translate(31 105)","fill-rule":"nonzero"}},[t("rect",{attrs:{fill:"url(#m)",width:"98",height:"34",rx:"2"}}),t("rect",{attrs:{fill:"#FFF",x:"9",y:"8",width:"80",height:"18",rx:"1.114"}}),t("rect",{attrs:{fill:"url(#n)",x:"15",y:"12",width:"18",height:"6",rx:"1.114"}})])])])}},dr=Object(o.a)("empty"),fr=dr[0],pr=dr[1],mr=["error","search","default"],vr=fr({props:{description:String,image:{type:String,default:"default"}},methods:{genImageContent:function(){var t=this.$createElement,e=this.slots("image");if(e)return e;if("network"===this.image)return t(hr);var i=this.image;return-1!==mr.indexOf(i)&&(i="https://img.yzcdn.cn/vant/empty-image-"+i+".png"),t("img",{attrs:{src:i}})},genImage:function(){return(0,this.$createElement)("div",{class:pr("image")},[this.genImageContent()])},genDescription:function(){var t=this.$createElement,e=this.slots("description")||this.description;if(e)return t("p",{class:pr("description")},[e])},genBottom:function(){var t=this.$createElement,e=this.slots();if(e)return t("div",{class:pr("bottom")},[e])}},render:function(){var t=arguments[0];return t("div",{class:pr()},[this.genImage(),this.genDescription(),this.genBottom()])}}),gr=Object(o.a)("form"),br=gr[0],yr=gr[1],Sr=br({props:{colon:Boolean,labelWidth:[Number,String],labelAlign:String,inputAlign:String,scrollToError:Boolean,validateFirst:Boolean,errorMessageAlign:String,submitOnEnter:{type:Boolean,default:!0},validateTrigger:{type:String,default:"onBlur"},showError:{type:Boolean,default:!0},showErrorMessage:{type:Boolean,default:!0}},provide:function(){return{vanForm:this}},data:function(){return{fields:[]}},methods:{validateSeq:function(){var t=this;return new Promise((function(e,i){var n=[];t.fields.reduce((function(t,e){return t.then((function(){if(!n.length)return e.validate().then((function(t){t&&n.push(t)}))}))}),Promise.resolve()).then((function(){n.length?i(n):e()}))}))},validateAll:function(){var t=this;return new Promise((function(e,i){Promise.all(t.fields.map((function(t){return t.validate()}))).then((function(t){(t=t.filter((function(t){return t}))).length?i(t):e()}))}))},validate:function(t){return t?this.validateField(t):this.validateFirst?this.validateSeq():this.validateAll()},validateField:function(t){var e=this.fields.filter((function(e){return e.name===t}));return e.length?new Promise((function(t,i){e[0].validate().then((function(e){e?i(e):t()}))})):Promise.reject()},resetValidation:function(t){this.fields.forEach((function(e){t&&e.name!==t||e.resetValidation()}))},scrollToField:function(t,e){this.fields.forEach((function(i){i.name===t&&i.$el.scrollIntoView(e)}))},addField:function(t){this.fields.push(t),si(this.fields,this)},removeField:function(t){this.fields=this.fields.filter((function(e){return e!==t}))},getValues:function(){return this.fields.reduce((function(t,e){return t[e.name]=e.formValue,t}),{})},onSubmit:function(t){t.preventDefault(),this.submit()},submit:function(){var t=this,e=this.getValues();this.validate().then((function(){t.$emit("submit",e)})).catch((function(i){t.$emit("failed",{values:e,errors:i}),t.scrollToError&&t.scrollToField(i[0].name)}))}},render:function(){var t=arguments[0];return t("form",{class:yr(),on:{submit:this.onSubmit}},[this.slots()])}}),kr=Object(o.a)("goods-action"),xr=kr[0],wr=kr[1],Cr=xr({mixins:[oi("vanGoodsAction")],props:{safeAreaInsetBottom:{type:Boolean,default:!0}},render:function(){var t=arguments[0];return t("div",{class:wr({unfit:!this.safeAreaInsetBottom})},[this.slots()])}}),Or=Object(o.a)("goods-action-button"),Tr=Or[0],$r=Or[1],Br=Tr({mixins:[ri("vanGoodsAction")],props:n(n({},Zt),{},{type:String,text:String,icon:String,color:String,loading:Boolean,disabled:Boolean}),computed:{isFirst:function(){var t=this.parent&&this.parent.children[this.index-1];return!t||t.$options.name!==this.$options.name},isLast:function(){var t=this.parent&&this.parent.children[this.index+1];return!t||t.$options.name!==this.$options.name}},methods:{onClick:function(t){this.$emit("click",t),Qt(this.$router,this)}},render:function(){var t=arguments[0];return t($e,{class:$r([{first:this.isFirst,last:this.isLast},this.type]),attrs:{square:!0,size:"large",type:this.type,icon:this.icon,color:this.color,loading:this.loading,disabled:this.disabled},on:{click:this.onClick}},[this.slots()||this.text])}}),Ir=Object(o.a)("goods-action-icon"),jr=Ir[0],Er=Ir[1],Dr=jr({mixins:[ri("vanGoodsAction")],props:n(n({},Zt),{},{dot:Boolean,text:String,icon:String,color:String,info:[Number,String],badge:[Number,String],iconClass:null}),methods:{onClick:function(t){this.$emit("click",t),Qt(this.$router,this)},genIcon:function(){var t=this.$createElement,e=this.slots("icon"),i=Object(m.b)(this.badge)?this.badge:this.info;return e?t("div",{class:Er("icon")},[e,t(Z,{attrs:{dot:this.dot,info:i}})]):t(st,{class:[Er("icon"),this.iconClass],attrs:{tag:"div",dot:this.dot,info:i,name:this.icon,color:this.color}})}},render:function(){var t=arguments[0];return t("div",{attrs:{role:"button",tabindex:"0"},class:Er(),on:{click:this.onClick}},[this.genIcon(),this.slots()||this.text])}}),Nr=Object(o.a)("grid"),Lr=Nr[0],Ar=Nr[1],Pr=Lr({mixins:[oi("vanGrid")],props:{square:Boolean,gutter:[Number,String],iconSize:[Number,String],direction:String,clickable:Boolean,columnNum:{type:[Number,String],default:4},center:{type:Boolean,default:!0},border:{type:Boolean,default:!0}},computed:{style:function(){var t=this.gutter;if(t)return{paddingLeft:Object(U.a)(t)}}},render:function(){var t,e=arguments[0];return e("div",{style:this.style,class:[Ar(),(t={},t[Ot]=this.border&&!this.gutter,t)]},[this.slots()])}}),zr=Object(o.a)("grid-item"),Mr=zr[0],Fr=zr[1],Vr=Mr({mixins:[ri("vanGrid")],props:n(n({},Zt),{},{dot:Boolean,text:String,icon:String,iconPrefix:String,info:[Number,String],badge:[Number,String]}),computed:{style:function(){var t=this.parent,e=t.square,i=t.gutter,n=t.columnNum,s=100/n+"%",r={flexBasis:s};if(e)r.paddingTop=s;else if(i){var o=Object(U.a)(i);r.paddingRight=o,this.index>=n&&(r.marginTop=o)}return r},contentStyle:function(){var t=this.parent,e=t.square,i=t.gutter;if(e&&i){var n=Object(U.a)(i);return{right:n,bottom:n,height:"auto"}}}},methods:{onClick:function(t){this.$emit("click",t),Qt(this.$router,this)},genIcon:function(){var t=this.$createElement,e=this.slots("icon"),i=Object(m.b)(this.badge)?this.badge:this.info;return e?t("div",{class:Fr("icon-wrapper")},[e,t(Z,{attrs:{dot:this.dot,info:i}})]):this.icon?t(st,{attrs:{name:this.icon,dot:this.dot,info:i,size:this.parent.iconSize,classPrefix:this.iconPrefix},class:Fr("icon")}):void 0},getText:function(){var t=this.$createElement,e=this.slots("text");return e||(this.text?t("span",{class:Fr("text")},[this.text]):void 0)},genContent:function(){var t=this.slots();return t||[this.genIcon(),this.getText()]}},render:function(){var t,e=arguments[0],i=this.parent,n=i.center,s=i.border,r=i.square,o=i.gutter,a=i.direction,l=i.clickable;return e("div",{class:[Fr({square:r})],style:this.style},[e("div",{style:this.contentStyle,attrs:{role:l?"button":null,tabindex:l?0:null},class:[Fr("content",[a,{center:n,square:r,clickable:l,surround:s&&o}]),(t={},t[Ct]=s,t)],on:{click:this.onClick}},[this.genContent()])])}}),Rr=Object(o.a)("swipe"),_r=Rr[0],Hr=Rr[1],Wr=_r({mixins:[R,oi("vanSwipe"),H((function(t,e){t(window,"resize",this.resize,!0),t(window,"visibilitychange",this.onVisibilityChange),e?this.initialize():this.clear()}))],props:{width:[Number,String],height:[Number,String],autoplay:[Number,String],vertical:Boolean,lazyRender:Boolean,indicatorColor:String,loop:{type:Boolean,default:!0},duration:{type:[Number,String],default:500},touchable:{type:Boolean,default:!0},initialSwipe:{type:[Number,String],default:0},showIndicators:{type:Boolean,default:!0},stopPropagation:{type:Boolean,default:!0}},data:function(){return{rect:null,offset:0,active:0,deltaX:0,deltaY:0,swiping:!1,computedWidth:0,computedHeight:0}},watch:{children:function(){this.initialize()},initialSwipe:function(){this.initialize()},autoplay:function(t){t>0?this.autoPlay():this.clear()}},computed:{count:function(){return this.children.length},maxCount:function(){return Math.ceil(Math.abs(this.minOffset)/this.size)},delta:function(){return this.vertical?this.deltaY:this.deltaX},size:function(){return this[this.vertical?"computedHeight":"computedWidth"]},trackSize:function(){return this.count*this.size},activeIndicator:function(){return(this.active+this.count)%this.count},isCorrectDirection:function(){var t=this.vertical?"vertical":"horizontal";return this.direction===t},trackStyle:function(){var t,e=this.vertical?"height":"width",i=this.vertical?"width":"height";return(t={})[e]=this.trackSize+"px",t[i]=this[i]?this[i]+"px":"",t.transitionDuration=(this.swiping?0:this.duration)+"ms",t.transform="translate"+(this.vertical?"Y":"X")+"("+this.offset+"px)",t},indicatorStyle:function(){return{backgroundColor:this.indicatorColor}},minOffset:function(){return(this.vertical?this.rect.height:this.rect.width)-this.size*this.count}},mounted:function(){this.bindTouchEvent(this.$refs.track)},methods:{initialize:function(t){if(void 0===t&&(t=+this.initialSwipe),this.$el&&!Ss(this.$el)){clearTimeout(this.timer);var e=this.$el.getBoundingClientRect();this.rect=e,this.swiping=!0,this.active=t,this.computedWidth=Math.round(+this.width||e.width),this.computedHeight=Math.round(+this.height||e.height),this.offset=this.getTargetOffset(t),this.children.forEach((function(t){t.offset=0})),this.autoPlay()}},resize:function(){this.initialize(this.activeIndicator)},onVisibilityChange:function(){document.hidden?this.clear():this.autoPlay()},onTouchStart:function(t){this.touchable&&(this.clear(),this.touchStartTime=Date.now(),this.touchStart(t),this.correctPosition())},onTouchMove:function(t){this.touchable&&this.swiping&&(this.touchMove(t),this.isCorrectDirection&&(k(t,this.stopPropagation),this.move({offset:this.delta})))},onTouchEnd:function(){if(this.touchable&&this.swiping){var t=this.size,e=this.delta,i=e/(Date.now()-this.touchStartTime);if((Math.abs(i)>.25||Math.abs(e)>t/2)&&this.isCorrectDirection){var n=this.vertical?this.offsetY:this.offsetX,s=0;s=this.loop?n>0?e>0?-1:1:0:-Math[e>0?"ceil":"floor"](e/t),this.move({pace:s,emitChange:!0})}else e&&this.move({pace:0});this.swiping=!1,this.autoPlay()}},getTargetActive:function(t){var e=this.active,i=this.count,n=this.maxCount;return t?this.loop?jt(e+t,-1,i):jt(e+t,0,n):e},getTargetOffset:function(t,e){void 0===e&&(e=0);var i=t*this.size;this.loop||(i=Math.min(i,-this.minOffset));var n=Math.round(e-i);return this.loop||(n=jt(n,this.minOffset,0)),n},move:function(t){var e=t.pace,i=void 0===e?0:e,n=t.offset,s=void 0===n?0:n,r=t.emitChange,o=this.loop,a=this.count,l=this.active,c=this.children,u=this.trackSize,h=this.minOffset;if(!(a<=1)){var d=this.getTargetActive(i),f=this.getTargetOffset(d,s);if(o){if(c[0]&&f!==h){var p=f<h;c[0].offset=p?u:0}if(c[a-1]&&0!==f){var m=f>0;c[a-1].offset=m?-u:0}}this.active=d,this.offset=f,r&&d!==l&&this.$emit("change",this.activeIndicator)}},prev:function(){var t=this;this.correctPosition(),this.resetTouchStatus(),Object(fn.b)((function(){t.swiping=!1,t.move({pace:-1,emitChange:!0})}))},next:function(){var t=this;this.correctPosition(),this.resetTouchStatus(),Object(fn.b)((function(){t.swiping=!1,t.move({pace:1,emitChange:!0})}))},swipeTo:function(t,e){var i=this;void 0===e&&(e={}),this.correctPosition(),this.resetTouchStatus(),Object(fn.b)((function(){var n;n=i.loop&&t===i.count?0===i.active?0:t:t%i.count,e.immediate?Object(fn.b)((function(){i.swiping=!1})):i.swiping=!1,i.move({pace:n-i.active,emitChange:!0})}))},correctPosition:function(){this.swiping=!0,this.active<=-1&&this.move({pace:this.count}),this.active>=this.count&&this.move({pace:-this.count})},clear:function(){clearTimeout(this.timer)},autoPlay:function(){var t=this,e=this.autoplay;e>0&&this.count>1&&(this.clear(),this.timer=setTimeout((function(){t.next(),t.autoPlay()}),e))},genIndicator:function(){var t=this,e=this.$createElement,i=this.count,n=this.activeIndicator,s=this.slots("indicator");return s||(this.showIndicators&&i>1?e("div",{class:Hr("indicators",{vertical:this.vertical})},[Array.apply(void 0,Array(i)).map((function(i,s){return e("i",{class:Hr("indicator",{active:s===n}),style:s===n?t.indicatorStyle:null})}))]):void 0)}},render:function(){var t=arguments[0];return t("div",{class:Hr()},[t("div",{ref:"track",style:this.trackStyle,class:Hr("track",{vertical:this.vertical})},[this.slots()]),this.genIndicator()])}}),qr=Object(o.a)("swipe-item"),Yr=qr[0],Ur=qr[1],Kr=Yr({mixins:[ri("vanSwipe")],data:function(){return{offset:0,mounted:!1}},mounted:function(){var t=this;this.$nextTick((function(){t.mounted=!0}))},computed:{style:function(){var t={},e=this.parent,i=e.size,n=e.vertical;return t[n?"height":"width"]=i+"px",this.offset&&(t.transform="translate"+(n?"Y":"X")+"("+this.offset+"px)"),t},shouldRender:function(){var t=this.index,e=this.parent,i=this.mounted;if(!e.lazyRender)return!0;if(!i)return!1;var n=e.activeIndicator,s=e.count-1;return t===n||t===(0===n?s:n-1)||t===(n===s?0:n+1)}},render:function(){var t=arguments[0];return t("div",{class:Ur(),style:this.style,on:n({},this.$listeners)},[this.shouldRender&&this.slots()])}}),Xr=Object(o.a)("image-preview"),Qr=Xr[0],Gr=Xr[1];function Zr(t){return Math.sqrt(Math.pow(t[0].clientX-t[1].clientX,2)+Math.pow(t[0].clientY-t[1].clientY,2))}var Jr,to=Qr({mixins:[Y({skipToggleEvent:!0}),R],props:{className:null,asyncClose:Boolean,showIndicators:Boolean,images:{type:Array,default:function(){return[]}},loop:{type:Boolean,default:!0},swipeDuration:{type:[Number,String],default:500},overlay:{type:Boolean,default:!0},showIndex:{type:Boolean,default:!0},startPosition:{type:[Number,String],default:0},minZoom:{type:[Number,String],default:1/3},maxZoom:{type:[Number,String],default:3},overlayClass:{type:String,default:Gr("overlay")},closeable:Boolean,closeIcon:{type:String,default:"clear"},closeIconPosition:{type:String,default:"top-right"}},data:function(){return{scale:1,moveX:0,moveY:0,active:0,moving:!1,zooming:!1,doubleClickTimer:null}},computed:{imageStyle:function(){var t=this.scale,e={transitionDuration:this.zooming||this.moving?"0s":".3s"};return 1!==t&&(e.transform="scale3d("+t+", "+t+", 1) translate("+this.moveX/t+"px, "+this.moveY/t+"px)"),e}},watch:{startPosition:"setActive",value:function(t){var e=this;t?(this.setActive(+this.startPosition),this.$nextTick((function(){e.$refs.swipe.swipeTo(+e.startPosition,{immediate:!0})}))):this.$emit("close",{index:this.active,url:this.images[this.active]})},shouldRender:{handler:function(t){var e=this;t&&this.$nextTick((function(){var t=e.$refs.swipe.$el;b(t,"touchstart",e.onWrapperTouchStart),b(t,"touchmove",k),b(t,"touchend",e.onWrapperTouchEnd),b(t,"touchcancel",e.onWrapperTouchEnd)}))},immediate:!0}},methods:{emitClose:function(){this.asyncClose||this.$emit("input",!1)},onWrapperTouchStart:function(){this.touchStartTime=new Date},onWrapperTouchEnd:function(t){var e=this;k(t);var i=new Date-this.touchStartTime,n=this.$refs.swipe||{},s=n.offsetX,r=void 0===s?0:s,o=n.offsetY;i<250&&r<10&&(void 0===o?0:o)<10&&(this.doubleClickTimer?(clearTimeout(this.doubleClickTimer),this.doubleClickTimer=null,this.toggleScale()):this.doubleClickTimer=setTimeout((function(){e.emitClose(),e.doubleClickTimer=null}),250))},startMove:function(t){var e=t.currentTarget.getBoundingClientRect(),i=window.innerWidth,n=window.innerHeight;this.touchStart(t),this.moving=!0,this.startMoveX=this.moveX,this.startMoveY=this.moveY,this.maxMoveX=Math.max(0,(e.width-i)/2),this.maxMoveY=Math.max(0,(e.height-n)/2)},startZoom:function(t){this.moving=!1,this.zooming=!0,this.startScale=this.scale,this.startDistance=Zr(t.touches)},onImageTouchStart:function(t){var e=t.touches,i=(this.$refs.swipe||{}).offsetX,n=void 0===i?0:i;1===e.length&&1!==this.scale?this.startMove(t):2!==e.length||n||this.startZoom(t)},onImageTouchMove:function(t){var e=t.touches;if((this.moving||this.zooming)&&k(t,!0),this.moving){this.touchMove(t);var i=this.deltaX+this.startMoveX,n=this.deltaY+this.startMoveY;this.moveX=jt(i,-this.maxMoveX,this.maxMoveX),this.moveY=jt(n,-this.maxMoveY,this.maxMoveY)}if(this.zooming&&2===e.length){var s=Zr(e),r=this.startScale*s/this.startDistance;this.setScale(r)}},onImageTouchEnd:function(t){if(this.moving||this.zooming){var e=!0;this.moving&&this.startMoveX===this.moveX&&this.startMoveY===this.moveY&&(e=!1),t.touches.length||(this.moving=!1,this.zooming=!1,this.startMoveX=0,this.startMoveY=0,this.startScale=1,this.scale<1&&this.resetScale()),e&&k(t,!0)}},setActive:function(t){this.resetScale(),t!==this.active&&(this.active=t,this.$emit("change",t))},setScale:function(t){var e=jt(t,+this.minZoom,+this.maxZoom);this.scale=e,this.$emit("scale",{index:this.active,scale:e})},resetScale:function(){this.setScale(1),this.moveX=0,this.moveY=0},toggleScale:function(){var t=this.scale>1?1:2;this.setScale(t),this.moveX=0,this.moveY=0},genIndex:function(){var t=this.$createElement;if(this.showIndex)return t("div",{class:Gr("index")},[this.slots("index")||this.active+1+" / "+this.images.length])},genCover:function(){var t=this.$createElement,e=this.slots("cover");if(e)return t("div",{class:Gr("cover")},[e])},genImages:function(){var t=this,e=this.$createElement,i={loading:function(){return e(mt,{attrs:{type:"spinner"}})}};return e(Wr,{ref:"swipe",attrs:{lazyRender:!0,loop:this.loop,indicatorColor:"white",duration:this.swipeDuration,initialSwipe:this.startPosition,showIndicators:this.showIndicators},class:Gr("swipe"),on:{change:this.setActive}},[this.images.map((function(n,s){return e(Kr,[e(Xi,{attrs:{src:n,fit:"contain"},class:Gr("image"),scopedSlots:i,style:s===t.active?t.imageStyle:null,nativeOn:{touchstart:t.onImageTouchStart,touchmove:t.onImageTouchMove,touchend:t.onImageTouchEnd,touchcancel:t.onImageTouchEnd}})])}))])},genClose:function(){var t=this.$createElement;if(this.closeable)return t(st,{attrs:{role:"button",name:this.closeIcon},class:Gr("close-icon",this.closeIconPosition),on:{click:this.emitClose}})},onClosed:function(){this.$emit("closed")}},render:function(){var t=arguments[0];if(this.shouldRender)return t("transition",{attrs:{name:"van-fade"},on:{afterLeave:this.onClosed}},[t("div",{directives:[{name:"show",value:this.value}],class:[Gr(),this.className]},[this.genClose(),this.genImages(),this.genIndex(),this.genCover()])])}}),eo={loop:!0,images:[],value:!0,minZoom:1/3,maxZoom:3,className:"",onClose:null,onChange:null,showIndex:!0,closeable:!1,closeIcon:"clear",asyncClose:!1,startPosition:0,swipeDuration:500,showIndicators:!1,closeOnPopstate:!1,closeIconPosition:"top-right"},io=function(t,e){if(void 0===e&&(e=0),!m.f){Jr||(Jr=new(l.a.extend(to))({el:document.createElement("div")}),document.body.appendChild(Jr.$el),Jr.$on("change",(function(t){Jr.onChange&&Jr.onChange(t)})),Jr.$on("scale",(function(t){Jr.onScale&&Jr.onScale(t)})));var i=Array.isArray(t)?{images:t,startPosition:e}:t;return n(Jr,eo,i),Jr.$once("input",(function(t){Jr.value=t})),Jr.$once("closed",(function(){Jr.images=[]})),i.onClose&&(Jr.$off("close"),Jr.$once("close",i.onClose)),Jr}};io.Component=to,io.install=function(){l.a.use(to)};var no=io,so=Object(o.a)("index-anchor"),ro=so[0],oo=so[1],ao=ro({mixins:[ri("vanIndexBar",{indexKey:"childrenIndex"})],props:{index:[Number,String]},data:function(){return{top:0,left:null,width:null,active:!1}},computed:{sticky:function(){return this.active&&this.parent.sticky},anchorStyle:function(){if(this.sticky)return{zIndex:""+this.parent.zIndex,left:this.left?this.left+"px":null,width:this.width?this.width+"px":null,transform:"translate3d(0, "+this.top+"px, 0)",color:this.parent.highlightColor}}},mounted:function(){this.height=this.$el.offsetHeight},methods:{scrollIntoView:function(){this.$el.scrollIntoView()}},render:function(){var t,e=arguments[0],i=this.sticky;return e("div",{style:{height:i?this.height+"px":null}},[e("div",{style:this.anchorStyle,class:[oo({sticky:i}),(t={},t[Tt]=i,t)]},[this.slots("default")||this.index])])}});var lo=Object(o.a)("index-bar"),co=lo[0],uo=lo[1],ho=co({mixins:[R,oi("vanIndexBar"),H((function(t){this.scroller||(this.scroller=A(this.$el)),t(this.scroller,"scroll",this.onScroll)}))],props:{zIndex:[Number,String],highlightColor:String,sticky:{type:Boolean,default:!0},stickyOffsetTop:{type:Number,default:0},indexList:{type:Array,default:function(){for(var t=[],e="A".charCodeAt(0),i=0;i<26;i++)t.push(String.fromCharCode(e+i));return t}}},data:function(){return{activeAnchorIndex:null}},computed:{sidebarStyle:function(){if(Object(m.b)(this.zIndex))return{zIndex:this.zIndex+1}},highlightStyle:function(){var t=this.highlightColor;if(t)return{color:t}}},watch:{indexList:function(){this.$nextTick(this.onScroll)}},methods:{onScroll:function(){var t=this;if(!Ss(this.$el)){var e=P(this.scroller),i=this.getScrollerRect(),n=this.children.map((function(e){return{height:e.height,top:t.getElementTop(e.$el,i)}})),s=this.getActiveAnchorIndex(e,n);this.activeAnchorIndex=this.indexList[s],this.sticky&&this.children.forEach((function(r,o){if(o===s||o===s-1){var a=r.$el.getBoundingClientRect();r.left=a.left,r.width=a.width}else r.left=null,r.width=null;if(o===s)r.active=!0,r.top=Math.max(t.stickyOffsetTop,n[o].top-e)+i.top;else if(o===s-1){var l=n[s].top-e;r.active=l>0,r.top=l+i.top-r.height}else r.active=!1}))}},getScrollerRect:function(){return this.scroller.getBoundingClientRect?this.scroller.getBoundingClientRect():{top:0,left:0}},getElementTop:function(t,e){var i=this.scroller;return i===window||i===document.body?V(t):t.getBoundingClientRect().top-e.top+P(i)},getActiveAnchorIndex:function(t,e){for(var i=this.children.length-1;i>=0;i--){var n=i>0?e[i-1].height:0;if(t+(this.sticky?n+this.stickyOffsetTop:0)>=e[i].top)return i}return-1},onClick:function(t){this.scrollToElement(t.target)},onTouchMove:function(t){if(this.touchMove(t),"vertical"===this.direction){k(t);var e=t.touches[0],i=e.clientX,n=e.clientY,s=document.elementFromPoint(i,n);if(s){var r=s.dataset.index;this.touchActiveIndex!==r&&(this.touchActiveIndex=r,this.scrollToElement(s))}}},scrollToElement:function(t){var e=t.dataset.index;if(e){var i=this.children.filter((function(t){return String(t.index)===e}));i[0]&&(i[0].scrollIntoView(),this.sticky&&this.stickyOffsetTop&&F(M()-this.stickyOffsetTop),this.$emit("select",i[0].index))}},onTouchEnd:function(){this.active=null}},render:function(){var t=this,e=arguments[0],i=this.indexList.map((function(i){var n=i===t.activeAnchorIndex;return e("span",{class:uo("index",{active:n}),style:n?t.highlightStyle:null,attrs:{"data-index":i}},[i])}));return e("div",{class:uo()},[e("div",{class:uo("sidebar"),style:this.sidebarStyle,on:{click:this.onClick,touchstart:this.touchStart,touchmove:this.onTouchMove,touchend:this.onTouchEnd,touchcancel:this.onTouchEnd}},[i]),this.slots("default")])}}),fo=i(10),po=i.n(fo).a,mo=Object(o.a)("list"),vo=mo[0],go=mo[1],bo=mo[2],yo=vo({mixins:[H((function(t){this.scroller||(this.scroller=A(this.$el)),t(this.scroller,"scroll",this.check)}))],model:{prop:"loading"},props:{error:Boolean,loading:Boolean,finished:Boolean,errorText:String,loadingText:String,finishedText:String,immediateCheck:{type:Boolean,default:!0},offset:{type:[Number,String],default:300},direction:{type:String,default:"down"}},data:function(){return{innerLoading:this.loading}},updated:function(){this.innerLoading=this.loading},mounted:function(){this.immediateCheck&&this.check()},watch:{loading:"check",finished:"check"},methods:{check:function(){var t=this;this.$nextTick((function(){if(!(t.innerLoading||t.finished||t.error)){var e,i=t.$el,n=t.scroller,s=t.offset,r=t.direction;if(!((e=n.getBoundingClientRect?n.getBoundingClientRect():{top:0,bottom:n.innerHeight}).bottom-e.top)||Ss(i))return!1;var o=t.$refs.placeholder.getBoundingClientRect();("up"===r?e.top-o.top<=s:o.bottom-e.bottom<=s)&&(t.innerLoading=!0,t.$emit("input",!0),t.$emit("load"))}}))},clickErrorText:function(){this.$emit("update:error",!1),this.check()},genLoading:function(){var t=this.$createElement;if(this.innerLoading&&!this.finished)return t("div",{class:go("loading"),key:"loading"},[this.slots("loading")||t(mt,{attrs:{size:"16"}},[this.loadingText||bo("loading")])])},genFinishedText:function(){var t=this.$createElement;if(this.finished){var e=this.slots("finished")||this.finishedText;if(e)return t("div",{class:go("finished-text")},[e])}},genErrorText:function(){var t=this.$createElement;if(this.error){var e=this.slots("error")||this.errorText;if(e)return t("div",{on:{click:this.clickErrorText},class:go("error-text")},[e])}}},render:function(){var t=arguments[0],e=t("div",{ref:"placeholder",class:go("placeholder")});return t("div",{class:go(),attrs:{role:"feed","aria-busy":this.innerLoading}},["down"===this.direction?this.slots():e,this.genLoading(),this.genFinishedText(),this.genErrorText(),"up"===this.direction?this.slots():e])}}),So=i(6),ko=Object(o.a)("nav-bar"),xo=ko[0],wo=ko[1],Co=xo({props:{title:String,fixed:Boolean,zIndex:[Number,String],leftText:String,rightText:String,leftArrow:Boolean,placeholder:Boolean,border:{type:Boolean,default:!0}},data:function(){return{height:null}},mounted:function(){this.placeholder&&this.fixed&&(this.height=this.$refs.navBar.getBoundingClientRect().height)},methods:{genLeft:function(){var t=this.$createElement,e=this.slots("left");return e||[this.leftArrow&&t(st,{class:wo("arrow"),attrs:{name:"arrow-left"}}),this.leftText&&t("span",{class:wo("text")},[this.leftText])]},genRight:function(){var t=this.$createElement,e=this.slots("right");return e||(this.rightText?t("span",{class:wo("text")},[this.rightText]):void 0)},genNavBar:function(){var t,e=this.$createElement;return e("div",{ref:"navBar",style:{zIndex:this.zIndex},class:[wo({fixed:this.fixed}),(t={},t[Tt]=this.border,t)]},[e("div",{class:wo("left"),on:{click:this.onClickLeft}},[this.genLeft()]),e("div",{class:[wo("title"),"van-ellipsis"]},[this.slots("title")||this.title]),e("div",{class:wo("right"),on:{click:this.onClickRight}},[this.genRight()])])},onClickLeft:function(t){this.$emit("click-left",t)},onClickRight:function(t){this.$emit("click-right",t)}},render:function(){var t=arguments[0];return this.placeholder&&this.fixed?t("div",{class:wo("placeholder"),style:{height:this.height+"px"}},[this.genNavBar()]):this.genNavBar()}}),Oo=Object(o.a)("notice-bar"),To=Oo[0],$o=Oo[1],Bo=To({props:{text:String,mode:String,color:String,leftIcon:String,wrapable:Boolean,background:String,scrollable:{type:Boolean,default:!0},delay:{type:[Number,String],default:1},speed:{type:[Number,String],default:50}},data:function(){return{show:!0,offset:0,duration:0,wrapWidth:0,contentWidth:0}},watch:{scrollable:"start",text:{handler:"start",immediate:!0}},activated:function(){this.start()},methods:{onClickIcon:function(t){"closeable"===this.mode&&(this.show=!1,this.$emit("close",t))},onTransitionEnd:function(){var t=this;this.offset=this.wrapWidth,this.duration=0,Object(fn.b)((function(){t.offset=-t.contentWidth,t.duration=(t.contentWidth+t.wrapWidth)/t.speed,t.$emit("replay")}))},reset:function(){this.offset=0,this.duration=0,this.wrapWidth=0,this.contentWidth=0},start:function(){var t=this,e=Object(m.b)(this.delay)?1e3*this.delay:0;this.reset(),setTimeout((function(){var e=t.$refs,i=e.wrap,n=e.content;if(i&&n){var s=i.getBoundingClientRect().width,r=n.getBoundingClientRect().width;t.scrollable&&r>s&&Object(fn.b)((function(){t.offset=-r,t.duration=r/t.speed,t.wrapWidth=s,t.contentWidth=r}))}}),e)}},render:function(){var t=this,e=arguments[0],i=this.slots,n=this.mode,s=this.leftIcon,r=this.onClickIcon,o={color:this.color,background:this.background},a={transform:this.offset?"translateX("+this.offset+"px)":"",transitionDuration:this.duration+"s"};function l(){var t=i("left-icon");return t||(s?e(st,{class:$o("left-icon"),attrs:{name:s}}):void 0)}function c(){var t,s=i("right-icon");return s||("closeable"===n?t="cross":"link"===n&&(t="arrow"),t?e(st,{class:$o("right-icon"),attrs:{name:t},on:{click:r}}):void 0)}return e("div",{attrs:{role:"alert"},directives:[{name:"show",value:this.show}],class:$o({wrapable:this.wrapable}),style:o,on:{click:function(e){t.$emit("click",e)}}},[l(),e("div",{ref:"wrap",class:$o("wrap"),attrs:{role:"marquee"}},[e("div",{ref:"content",class:[$o("content"),{"van-ellipsis":!this.scrollable&&!this.wrapable}],style:a,on:{transitionend:this.onTransitionEnd}},[this.slots()||this.text])]),c()])}}),Io=Object(o.a)("notify"),jo=Io[0],Eo=Io[1];function Do(t,e,i,n){var s={color:e.color,background:e.background};return t(lt,r()([{attrs:{value:e.value,position:"top",overlay:!1,duration:.2,lockScroll:!1},style:s,class:[Eo([e.type]),e.className]},h(n,!0)]),[(null==i.default?void 0:i.default())||e.message])}Do.props=n(n({},q),{},{color:String,message:[Number,String],duration:[Number,String],className:null,background:String,getContainer:[String,Function],type:{type:String,default:"danger"}});var No,Lo,Ao=jo(Do);function Po(t){var e;if(!m.f)return Lo||(Lo=f(Ao,{on:{click:function(t){Lo.onClick&&Lo.onClick(t)},close:function(){Lo.onClose&&Lo.onClose()},opened:function(){Lo.onOpened&&Lo.onOpened()}}})),t=n(n({},Po.currentOptions),(e=t,Object(m.d)(e)?e:{message:e})),n(Lo,t),clearTimeout(No),t.duration&&t.duration>0&&(No=setTimeout(Po.clear,t.duration)),Lo}Po.clear=function(){Lo&&(Lo.value=!1)},Po.currentOptions={type:"danger",value:!0,message:"",color:void 0,background:void 0,duration:3e3,className:"",onClose:null,onClick:null,onOpened:null},Po.setDefaultOptions=function(t){n(Po.currentOptions,t)},Po.resetDefaultOptions=function(){Po.currentOptions={type:"danger",value:!0,message:"",color:void 0,background:void 0,duration:3e3,className:"",onClose:null,onClick:null,onOpened:null}},Po.install=function(){l.a.use(Ao)},Po.Component=Ao,l.a.prototype.$notify=Po;var zo=Po,Mo={render:function(){var t=arguments[0];return t("svg",{attrs:{viewBox:"0 0 32 22",xmlns:"http://www.w3.org/2000/svg"}},[t("path",{attrs:{d:"M28.016 0A3.991 3.991 0 0132 3.987v14.026c0 2.2-1.787 3.987-3.98 3.987H10.382c-.509 0-.996-.206-1.374-.585L.89 13.09C.33 12.62 0 11.84 0 11.006c0-.86.325-1.62.887-2.08L9.01.585A1.936 1.936 0 0110.383 0zm0 1.947H10.368L2.24 10.28c-.224.226-.312.432-.312.73 0 .287.094.51.312.729l8.128 8.333h17.648a2.041 2.041 0 002.037-2.04V3.987c0-1.127-.915-2.04-2.037-2.04zM23.028 6a.96.96 0 01.678.292.95.95 0 01-.003 1.377l-3.342 3.348 3.326 3.333c.189.188.292.43.292.679 0 .248-.103.49-.292.679a.96.96 0 01-.678.292.959.959 0 01-.677-.292L18.99 12.36l-3.343 3.345a.96.96 0 01-.677.292.96.96 0 01-.678-.292.962.962 0 01-.292-.68c0-.248.104-.49.292-.679l3.342-3.348-3.342-3.348A.963.963 0 0114 6.971c0-.248.104-.49.292-.679A.96.96 0 0114.97 6a.96.96 0 01.677.292l3.358 3.348 3.345-3.348A.96.96 0 0123.028 6z",fill:"currentColor"}})])}},Fo={render:function(){var t=arguments[0];return t("svg",{attrs:{viewBox:"0 0 30 24",xmlns:"http://www.w3.org/2000/svg"}},[t("path",{attrs:{d:"M25.877 12.843h-1.502c-.188 0-.188 0-.188.19v1.512c0 .188 0 .188.188.188h1.5c.187 0 .187 0 .187-.188v-1.511c0-.19 0-.191-.185-.191zM17.999 10.2c0 .188 0 .188.188.188h1.687c.188 0 .188 0 .188-.188V8.688c0-.187.004-.187-.186-.19h-1.69c-.187 0-.187 0-.187.19V10.2zm2.25-3.967h1.5c.188 0 .188 0 .188-.188v-1.7c0-.19 0-.19-.188-.19h-1.5c-.189 0-.189 0-.189.19v1.7c0 .188 0 .188.19.188zm2.063 4.157h3.563c.187 0 .187 0 .187-.189V4.346c0-.19.004-.19-.185-.19h-1.69c-.187 0-.187 0-.187.188v4.155h-1.688c-.187 0-.187 0-.187.189v1.514c0 .19 0 .19.187.19zM14.812 24l2.812-3.4H12l2.813 3.4zm-9-11.157H4.31c-.188 0-.188 0-.188.19v1.512c0 .188 0 .188.188.188h1.502c.187 0 .187 0 .187-.188v-1.511c0-.19.01-.191-.189-.191zm15.937 0H8.25c-.188 0-.188 0-.188.19v1.512c0 .188 0 .188.188.188h13.5c.188 0 .188 0 .188-.188v-1.511c0-.19 0-.191-.188-.191zm-11.438-2.454h1.5c.188 0 .188 0 .188-.188V8.688c0-.187 0-.187-.188-.189h-1.5c-.187 0-.187 0-.187.189V10.2c0 .188 0 .188.187.188zM27.94 0c.563 0 .917.21 1.313.567.518.466.748.757.748 1.51v14.92c0 .567-.188 1.134-.562 1.512-.376.378-.938.566-1.313.566H2.063c-.563 0-.938-.188-1.313-.566-.562-.378-.75-.945-.75-1.511V2.078C0 1.51.188.944.562.567.938.189 1.5 0 1.875 0zm-.062 2H2v14.92h25.877V2zM5.81 4.157c.19 0 .19 0 .19.189v1.762c-.003.126-.024.126-.188.126H4.249c-.126-.003-.126-.023-.126-.188v-1.7c-.187-.19 0-.19.188-.19zm10.5 2.077h1.503c.187 0 .187 0 .187-.188v-1.7c0-.19 0-.19-.187-.19h-1.502c-.188 0-.188.001-.188.19v1.7c0 .188 0 .188.188.188zM7.875 8.5c.187 0 .187.002.187.189V10.2c0 .188 0 .188-.187.188H4.249c-.126-.002-.126-.023-.126-.188V8.625c.003-.126.024-.126.188-.126zm7.875 0c.19.002.19.002.19.189v1.575c-.003.126-.024.126-.19.126h-1.563c-.126-.002-.126-.023-.126-.188V8.625c.002-.126.023-.126.189-.126zm-6-4.342c.187 0 .187 0 .187.189v1.7c0 .188 0 .188-.187.188H8.187c-.126-.003-.126-.023-.126-.188V4.283c.003-.126.024-.126.188-.126zm3.94 0c.185 0 .372 0 .372.189v1.762c-.002.126-.023.126-.187.126h-1.75C12 6.231 12 6.211 12 6.046v-1.7c0-.19.187-.19.187-.19z",fill:"currentColor"}})])}},Vo=Object(o.a)("key"),Ro=Vo[0],_o=Vo[1],Ho=Ro({mixins:[R],props:{type:String,text:[Number,String],color:String,wider:Boolean,large:Boolean,loading:Boolean},data:function(){return{active:!1}},mounted:function(){this.bindTouchEvent(this.$el)},methods:{onTouchStart:function(t){t.stopPropagation(),this.touchStart(t),this.active=!0},onTouchMove:function(t){this.touchMove(t),this.direction&&(this.active=!1)},onTouchEnd:function(){this.active&&(this.active=!1,this.$emit("press",this.text,this.type))},genContent:function(){var t=this.$createElement,e="extra"===this.type,i="delete"===this.type,n=this.slots("default")||this.text;return this.loading?t(mt,{class:_o("loading-icon")}):i?n||t(Mo,{class:_o("delete-icon")}):e?n||t(Fo,{class:_o("collapse-icon")}):n}},render:function(){var t=arguments[0];return t("div",{class:_o("wrapper",{wider:this.wider})},[t("button",{attrs:{type:"button"},class:_o([this.color,{large:this.large,active:this.active,delete:"delete"===this.type}])},[this.genContent()])])}}),Wo=Object(o.a)("number-keyboard"),qo=Wo[0],Yo=Wo[1],Uo=(Wo[2],qo({mixins:[H((function(t){this.hideOnClickOutside&&t(document.body,"touchstart",this.onBlur)}))],model:{event:"update:value"},props:{show:Boolean,title:String,zIndex:[Number,String],closeButtonText:String,deleteButtonText:String,closeButtonLoading:Boolean,theme:{type:String,default:"default"},value:{type:String,default:""},extraKey:{type:[String,Array],default:""},maxlength:{type:[Number,String],default:Number.MAX_VALUE},transition:{type:Boolean,default:!0},showDeleteKey:{type:Boolean,default:!0},hideOnClickOutside:{type:Boolean,default:!0},safeAreaInsetBottom:{type:Boolean,default:!0}},watch:{show:function(t){this.transition||this.$emit(t?"show":"hide")}},computed:{keys:function(){return"custom"===this.theme?this.genCustomKeys():this.genDefaultKeys()}},methods:{genBasicKeys:function(){for(var t=[],e=1;e<=9;e++)t.push({text:e});return t},genDefaultKeys:function(){return[].concat(this.genBasicKeys(),[{text:this.extraKey,type:"extra"},{text:0},{text:this.showDeleteKey?this.deleteButtonText:"",type:this.showDeleteKey?"delete":""}])},genCustomKeys:function(){var t=this.genBasicKeys(),e=this.extraKey,i=Array.isArray(e)?e:[e];return 1===i.length?t.push({text:0,wider:!0},{text:e[0],type:"extra"}):2===i.length&&t.push({text:e[0],type:"extra"},{text:0},{text:e[1],type:"extra"}),t},onBlur:function(){this.show&&this.$emit("blur")},onClose:function(){this.$emit("close"),this.onBlur()},onAnimationEnd:function(){this.$emit(this.show?"show":"hide")},onPress:function(t,e){if(""!==t){var i=this.value;"delete"===e?(this.$emit("delete"),this.$emit("update:value",i.slice(0,i.length-1))):"close"===e?this.onClose():i.length<this.maxlength&&(this.$emit("input",t),this.$emit("update:value",i+t))}else"extra"===e&&this.onBlur()},genTitle:function(){var t=this.$createElement,e=this.title,i=this.theme,n=this.closeButtonText,s=this.slots("title-left"),r=n&&"default"===i;if(e||r||s)return t("div",{class:Yo("header")},[s&&t("span",{class:Yo("title-left")},[s]),e&&t("h2",{class:Yo("title")},[e]),r&&t("button",{attrs:{type:"button"},class:Yo("close"),on:{click:this.onClose}},[n])])},genKeys:function(){var t=this,e=this.$createElement;return this.keys.map((function(i){return e(Ho,{key:i.text,attrs:{text:i.text,type:i.type,wider:i.wider,color:i.color},on:{press:t.onPress}},["delete"===i.type&&t.slots("delete"),"extra"===i.type&&t.slots("extra-key")])}))},genSidebar:function(){var t=this.$createElement;if("custom"===this.theme)return t("div",{class:Yo("sidebar")},[this.showDeleteKey&&t(Ho,{attrs:{large:!0,text:this.deleteButtonText,type:"delete"},on:{press:this.onPress}},[this.slots("delete")]),t(Ho,{attrs:{large:!0,text:this.closeButtonText,type:"close",color:"blue",loading:this.closeButtonLoading},on:{press:this.onPress}})])}},render:function(){var t=arguments[0],e=this.genTitle();return t("transition",{attrs:{name:this.transition?"van-slide-up":""}},[t("div",{directives:[{name:"show",value:this.show}],style:{zIndex:this.zIndex},class:Yo({unfit:!this.safeAreaInsetBottom,"with-title":e}),on:{touchstart:S,animationend:this.onAnimationEnd,webkitAnimationEnd:this.onAnimationEnd}},[e,t("div",{class:Yo("body")},[t("div",{class:Yo("keys")},[this.genKeys()]),this.genSidebar()])])])}})),Ko=Object(o.a)("pagination"),Xo=Ko[0],Qo=Ko[1],Go=Ko[2];function Zo(t,e,i){return{number:t,text:e,active:i}}var Jo=Xo({props:{prevText:String,nextText:String,forceEllipses:Boolean,mode:{type:String,default:"multi"},value:{type:Number,default:0},pageCount:{type:[Number,String],default:0},totalItems:{type:[Number,String],default:0},itemsPerPage:{type:[Number,String],default:10},showPageSize:{type:[Number,String],default:5}},computed:{count:function(){var t=this.pageCount||Math.ceil(this.totalItems/this.itemsPerPage);return Math.max(1,t)},pages:function(){var t=[],e=this.count,i=+this.showPageSize;if("multi"!==this.mode)return t;var n=1,s=e,r=i<e;r&&(s=(n=Math.max(this.value-Math.floor(i/2),1))+i-1)>e&&(n=(s=e)-i+1);for(var o=n;o<=s;o++){var a=Zo(o,o,o===this.value);t.push(a)}if(r&&i>0&&this.forceEllipses){if(n>1){var l=Zo(n-1,"...",!1);t.unshift(l)}if(s<e){var c=Zo(s+1,"...",!1);t.push(c)}}return t}},watch:{value:{handler:function(t){this.select(t||this.value)},immediate:!0}},methods:{select:function(t,e){t=Math.min(this.count,Math.max(1,t)),this.value!==t&&(this.$emit("input",t),e&&this.$emit("change",t))}},render:function(){var t=this,e=arguments[0],i=this.value,n="multi"!==this.mode,s=function(e){return function(){t.select(e,!0)}};return e("ul",{class:Qo({simple:n})},[e("li",{class:[Qo("item",{disabled:1===i}),Qo("prev"),Ct],on:{click:s(i-1)}},[this.prevText||Go("prev")]),this.pages.map((function(t){return e("li",{class:[Qo("item",{active:t.active}),Qo("page"),Ct],on:{click:s(t.number)}},[t.text])})),n&&e("li",{class:Qo("page-desc")},[this.slots("pageDesc")||i+"/"+this.count]),e("li",{class:[Qo("item",{disabled:i===this.count}),Qo("next"),Ct],on:{click:s(i+1)}},[this.nextText||Go("next")])])}}),ta=Object(o.a)("panel"),ea=ta[0],ia=ta[1];function na(t,e,i,n){return t(on,r()([{class:ia(),scopedSlots:{default:function(){return[i.header?i.header():t(se,{attrs:{icon:e.icon,label:e.desc,title:e.title,value:e.status,valueClass:ia("header-value")},class:ia("header")}),t("div",{class:ia("content")},[i.default&&i.default()]),i.footer&&t("div",{class:[ia("footer"),Ot]},[i.footer()])]}}},h(n,!0)]))}na.props={icon:String,desc:String,title:String,status:String};var sa=ea(na),ra=Object(o.a)("password-input"),oa=ra[0],aa=ra[1];function la(t,e,i,n){for(var s,o=e.mask,a=e.value,l=e.length,c=e.gutter,u=e.focused,f=e.errorInfo,p=f||e.info,m=[],v=0;v<l;v++){var g,b=a[v],y=0!==v&&!c,S=u&&v===a.length,k=void 0;0!==v&&c&&(k={marginLeft:Object(U.a)(c)}),m.push(t("li",{class:(g={},g["van-hairline--left"]=y,g),style:k},[o?t("i",{style:{visibility:b?"visible":"hidden"}}):b,S&&t("div",{class:aa("cursor")})]))}return t("div",{class:aa()},[t("ul",r()([{class:[aa("security"),(s={},s["van-hairline--surround"]=!c,s)],on:{touchstart:function(t){t.stopPropagation(),d(n,"focus",t)}}},h(n,!0)]),[m]),p&&t("div",{class:aa(f?"error-info":"info")},[p])])}la.props={info:String,gutter:[Number,String],focused:Boolean,errorInfo:String,mask:{type:Boolean,default:!0},value:{type:String,default:""},length:{type:[Number,String],default:6}};var ca=oa(la),ua=Object(o.a)("progress"),ha=ua[0],da=ua[1],fa=ha({props:{color:String,inactive:Boolean,pivotText:String,textColor:String,pivotColor:String,trackColor:String,strokeWidth:[Number,String],percentage:{type:[Number,String],required:!0,validator:function(t){return t>=0&&t<=100}},showPivot:{type:Boolean,default:!0}},data:function(){return{pivotWidth:0,progressWidth:0}},mounted:function(){this.setWidth()},watch:{showPivot:"setWidth",pivotText:"setWidth"},methods:{setWidth:function(){var t=this;this.$nextTick((function(){t.progressWidth=t.$el.offsetWidth,t.pivotWidth=t.$refs.pivot?t.$refs.pivot.offsetWidth:0}))}},render:function(){var t=arguments[0],e=this.pivotText,i=this.percentage,n=Object(m.b)(e)?e:i+"%",s=this.showPivot&&n,r=this.inactive?"#cacaca":this.color,o={color:this.textColor,left:(this.progressWidth-this.pivotWidth)*i/100+"px",background:this.pivotColor||r},a={background:r,width:this.progressWidth*i/100+"px"},l={background:this.trackColor,height:Object(U.a)(this.strokeWidth)};return t("div",{class:da(),style:l},[t("span",{class:da("portion"),style:a},[s&&t("span",{ref:"pivot",style:o,class:da("pivot")},[n])])])}}),pa=Object(o.a)("pull-refresh"),ma=pa[0],va=pa[1],ga=pa[2],ba=["pulling","loosing","success"],ya=ma({mixins:[R],props:{disabled:Boolean,successText:String,pullingText:String,loosingText:String,loadingText:String,value:{type:Boolean,required:!0},successDuration:{type:[Number,String],default:500},animationDuration:{type:[Number,String],default:300},headHeight:{type:[Number,String],default:50}},data:function(){return{status:"normal",distance:0,duration:0}},computed:{touchable:function(){return"loading"!==this.status&&"success"!==this.status&&!this.disabled},headStyle:function(){if(50!==this.headHeight)return{height:this.headHeight+"px"}}},watch:{value:function(t){this.duration=this.animationDuration,t?this.setStatus(+this.headHeight,!0):this.slots("success")||this.successText?this.showSuccessTip():this.setStatus(0,!1)}},mounted:function(){this.bindTouchEvent(this.$refs.track),this.scrollEl=A(this.$el)},methods:{checkPullStart:function(t){this.ceiling=0===P(this.scrollEl),this.ceiling&&(this.duration=0,this.touchStart(t))},onTouchStart:function(t){this.touchable&&this.checkPullStart(t)},onTouchMove:function(t){this.touchable&&(this.ceiling||this.checkPullStart(t),this.touchMove(t),this.ceiling&&this.deltaY>=0&&"vertical"===this.direction&&(k(t),this.setStatus(this.ease(this.deltaY))))},onTouchEnd:function(){var t=this;this.touchable&&this.ceiling&&this.deltaY&&(this.duration=this.animationDuration,"loosing"===this.status?(this.setStatus(+this.headHeight,!0),this.$emit("input",!0),this.$nextTick((function(){t.$emit("refresh")}))):this.setStatus(0))},ease:function(t){var e=+this.headHeight;return t>e&&(t=t<2*e?e+(t-e)/2:1.5*e+(t-2*e)/4),Math.round(t)},setStatus:function(t,e){var i;i=e?"loading":0===t?"normal":t<this.headHeight?"pulling":"loosing",this.distance=t,i!==this.status&&(this.status=i)},genStatus:function(){var t=this.$createElement,e=this.status,i=this.distance,n=this.slots(e,{distance:i});if(n)return n;var s=[],r=this[e+"Text"]||ga(e);return-1!==ba.indexOf(e)&&s.push(t("div",{class:va("text")},[r])),"loading"===e&&s.push(t(mt,{attrs:{size:"16"}},[r])),s},showSuccessTip:function(){var t=this;this.status="success",setTimeout((function(){t.setStatus(0)}),this.successDuration)}},render:function(){var t=arguments[0],e={transitionDuration:this.duration+"ms",transform:this.distance?"translate3d(0,"+this.distance+"px, 0)":""};return t("div",{class:va()},[t("div",{ref:"track",class:va("track"),style:e},[t("div",{class:va("head"),style:this.headStyle},[this.genStatus()]),this.slots()])])}}),Sa=Object(o.a)("rate"),ka=Sa[0],xa=Sa[1];var wa=ka({mixins:[R,_e],props:{size:[Number,String],color:String,gutter:[Number,String],readonly:Boolean,disabled:Boolean,allowHalf:Boolean,voidColor:String,iconPrefix:String,disabledColor:String,value:{type:Number,default:0},icon:{type:String,default:"star"},voidIcon:{type:String,default:"star-o"},count:{type:[Number,String],default:5},touchable:{type:Boolean,default:!0}},computed:{list:function(){for(var t,e,i,n=[],s=1;s<=this.count;s++)n.push((t=this.value,e=s,i=this.allowHalf,t>=e?"full":t+.5>=e&&i?"half":"void"));return n},sizeWithUnit:function(){return Object(U.a)(this.size)},gutterWithUnit:function(){return Object(U.a)(this.gutter)}},mounted:function(){this.bindTouchEvent(this.$el)},methods:{select:function(t){this.disabled||this.readonly||t===this.value||(this.$emit("input",t),this.$emit("change",t))},onTouchStart:function(t){var e=this;if(!this.readonly&&!this.disabled&&this.touchable){this.touchStart(t);var i=this.$refs.items.map((function(t){return t.getBoundingClientRect()})),n=[];i.forEach((function(t,i){e.allowHalf?n.push({score:i+.5,left:t.left},{score:i+1,left:t.left+t.width/2}):n.push({score:i+1,left:t.left})})),this.ranges=n}},onTouchMove:function(t){if(!this.readonly&&!this.disabled&&this.touchable&&(this.touchMove(t),"horizontal"===this.direction)){k(t);var e=t.touches[0].clientX;this.select(this.getScoreByPosition(e))}},getScoreByPosition:function(t){for(var e=this.ranges.length-1;e>0;e--)if(t>this.ranges[e].left)return this.ranges[e].score;return this.allowHalf?.5:1},genStar:function(t,e){var i,n=this,s=this.$createElement,r=this.icon,o=this.color,a=this.count,l=this.voidIcon,c=this.disabled,u=this.voidColor,h=this.disabledColor,d=e+1,f="full"===t,p="void"===t;return this.gutterWithUnit&&d!==+a&&(i={paddingRight:this.gutterWithUnit}),s("div",{ref:"items",refInFor:!0,key:e,attrs:{role:"radio",tabindex:"0","aria-setsize":a,"aria-posinset":d,"aria-checked":String(!p)},style:i,class:xa("item")},[s(st,{attrs:{size:this.sizeWithUnit,name:f?r:l,color:c?h:f?o:u,classPrefix:this.iconPrefix,"data-score":d},class:xa("icon",{disabled:c,full:f}),on:{click:function(){n.select(d)}}}),this.allowHalf&&s(st,{attrs:{size:this.sizeWithUnit,name:p?l:r,color:c?h:p?u:o,classPrefix:this.iconPrefix,"data-score":d-.5},class:xa("icon",["half",{disabled:c,full:!p}]),on:{click:function(){n.select(d-.5)}}})])}},render:function(){var t=this,e=arguments[0];return e("div",{class:xa({readonly:this.readonly,disabled:this.disabled}),attrs:{tabindex:"0",role:"radiogroup"}},[this.list.map((function(e,i){return t.genStar(e,i)}))])}}),Ca=Object(o.a)("row"),Oa=Ca[0],Ta=Ca[1],$a=Oa({mixins:[oi("vanRow")],props:{type:String,align:String,justify:String,tag:{type:String,default:"div"},gutter:{type:[Number,String],default:0}},computed:{spaces:function(){var t=Number(this.gutter);if(t){var e=[],i=[[]],n=0;return this.children.forEach((function(t,e){(n+=Number(t.span))>24?(i.push([e]),n-=24):i[i.length-1].push(e)})),i.forEach((function(i){var n=t*(i.length-1)/i.length;i.forEach((function(i,s){if(0===s)e.push({right:n});else{var r=t-e[i-1].right,o=n-r;e.push({left:r,right:o})}}))})),e}}},methods:{onClick:function(t){this.$emit("click",t)}},render:function(){var t,e=arguments[0],i=this.align,n=this.justify,s="flex"===this.type;return e(this.tag,{class:Ta((t={flex:s},t["align-"+i]=s&&i,t["justify-"+n]=s&&n,t)),on:{click:this.onClick}},[this.slots()])}}),Ba=Object(o.a)("search"),Ia=Ba[0],ja=Ba[1],Ea=Ba[2];function Da(t,e,i,s){var o={attrs:s.data.attrs,on:n(n({},s.listeners),{},{keypress:function(t){13===t.keyCode&&(k(t),d(s,"search",e.value)),d(s,"keypress",t)}})},a=h(s);return a.attrs=void 0,t("div",r()([{class:ja({"show-action":e.showAction}),style:{background:e.background}},a]),[null==i.left?void 0:i.left(),t("div",{class:ja("content",e.shape)},[function(){if(i.label||e.label)return t("div",{class:ja("label")},[i.label?i.label():e.label])}(),t(le,r()([{attrs:{type:"search",border:!1,value:e.value,leftIcon:e.leftIcon,rightIcon:e.rightIcon,clearable:e.clearable},scopedSlots:{"left-icon":i["left-icon"],"right-icon":i["right-icon"]}},o]))]),function(){if(e.showAction)return t("div",{class:ja("action"),attrs:{role:"button",tabindex:"0"},on:{click:function(){i.action||(d(s,"input",""),d(s,"cancel"))}}},[i.action?i.action():e.actionText||Ea("cancel")])}()])}Da.props={value:String,label:String,rightIcon:String,actionText:String,showAction:Boolean,background:String,shape:{type:String,default:"square"},clearable:{type:Boolean,default:!0},leftIcon:{type:String,default:"search"}};var Na=Ia(Da),La=["qq","weibo","wechat","link","qrcode","poster"],Aa=Object(o.a)("share-sheet"),Pa=Aa[0],za=Aa[1],Ma=Aa[2],Fa=Pa({props:n(n({},q),{},{title:String,cancelText:String,description:String,getContainer:[String,Function],options:{type:Array,default:function(){return[]}},overlay:{type:Boolean,default:!0},closeOnPopstate:{type:Boolean,default:!0},safeAreaInsetBottom:{type:Boolean,default:!0},closeOnClickOverlay:{type:Boolean,default:!0}}),methods:{onCancel:function(){this.toggle(!1),this.$emit("cancel")},onSelect:function(t,e){this.$emit("select",t,e)},toggle:function(t){this.$emit("input",t)},getIconURL:function(t){return-1!==La.indexOf(t)?"https://img.yzcdn.cn/vant/share-icon-"+t+".png":t},genHeader:function(){var t=this.$createElement,e=this.slots("title")||this.title,i=this.slots("description")||this.description;if(e||i)return t("div",{class:za("header")},[e&&t("h2",{class:za("title")},[e]),i&&t("span",{class:za("description")},[i])])},genOptions:function(t,e){var i=this,n=this.$createElement;return n("div",{class:za("options",{border:e})},[t.map((function(t,e){return n("div",{attrs:{role:"button",tabindex:"0"},class:za("option"),on:{click:function(){i.onSelect(t,e)}}},[n("img",{attrs:{src:i.getIconURL(t.icon)},class:za("icon")}),t.name&&n("span",{class:za("name")},[t.name]),t.description&&n("span",{class:za("option-description")},[t.description])])}))])},genRows:function(){var t=this,e=this.options;return Array.isArray(e[0])?e.map((function(e,i){return t.genOptions(e,0!==i)})):this.genOptions(e)},genCancelText:function(){var t=this.$createElement,e=Object(m.b)(this.cancelText)?this.cancelText:Ma("cancel");if(e)return t("button",{attrs:{type:"button"},class:za("cancel"),on:{click:this.onCancel}},[e])}},render:function(){var t=arguments[0];return t(lt,{attrs:{round:!0,value:this.value,position:"bottom",overlay:this.overlay,duration:this.duration,lazyRender:this.lazyRender,lockScroll:this.lockScroll,getContainer:this.getContainer,closeOnPopstate:this.closeOnPopstate,closeOnClickOverlay:this.closeOnClickOverlay,safeAreaInsetBottom:this.safeAreaInsetBottom},class:za(),on:{input:this.toggle}},[this.genHeader(),this.genRows(),this.genCancelText()])}}),Va=Object(o.a)("sidebar"),Ra=Va[0],_a=Va[1],Ha=Ra({mixins:[oi("vanSidebar")],model:{prop:"activeKey"},props:{activeKey:{type:[Number,String],default:0}},data:function(){return{index:+this.activeKey}},watch:{activeKey:function(){this.setIndex(+this.activeKey)}},methods:{setIndex:function(t){t!==this.index&&(this.index=t,this.$emit("change",t))}},render:function(){var t=arguments[0];return t("div",{class:_a()},[this.slots()])}}),Wa=Object(o.a)("sidebar-item"),qa=Wa[0],Ya=Wa[1],Ua=qa({mixins:[ri("vanSidebar")],props:n(n({},Zt),{},{dot:Boolean,info:[Number,String],badge:[Number,String],title:String,disabled:Boolean}),computed:{select:function(){return this.index===+this.parent.activeKey}},methods:{onClick:function(){this.disabled||(this.$emit("click",this.index),this.parent.$emit("input",this.index),this.parent.setIndex(this.index),Qt(this.$router,this))}},render:function(){var t=arguments[0];return t("a",{class:Ya({select:this.select,disabled:this.disabled}),on:{click:this.onClick}},[t("div",{class:Ya("text")},[this.title,t(Z,{attrs:{dot:this.dot,info:Object(m.b)(this.badge)?this.badge:this.info},class:Ya("info")})])])}}),Ka=Object(o.a)("skeleton"),Xa=Ka[0],Qa=Ka[1];function Ga(t,e,i,n){if(!e.loading)return i.default&&i.default();return t("div",r()([{class:Qa({animate:e.animate,round:e.round})},h(n)]),[function(){if(e.avatar){var i=Object(U.a)(e.avatarSize);return t("div",{class:Qa("avatar",e.avatarShape),style:{width:i,height:i}})}}(),t("div",{class:Qa("content")},[function(){if(e.title)return t("h3",{class:Qa("title"),style:{width:Object(U.a)(e.titleWidth)}})}(),function(){for(var i,n=[],s=e.rowWidth,r=0;r<e.row;r++)n.push(t("div",{class:Qa("row"),style:{width:Object(U.a)((i=r,"100%"===s&&i===+e.row-1?"60%":Array.isArray(s)?s[i]:s))}}));return n}()])])}Ga.props={title:Boolean,round:Boolean,avatar:Boolean,row:{type:[Number,String],default:0},loading:{type:Boolean,default:!0},animate:{type:Boolean,default:!0},avatarSize:{type:String,default:"32px"},avatarShape:{type:String,default:"round"},titleWidth:{type:[Number,String],default:"40%"},rowWidth:{type:[Number,String,Array],default:"100%"}};var Za=Xa(Ga),Ja={QUOTA_LIMIT:0,STOCK_LIMIT:1},tl={LIMIT_TYPE:Ja,UNSELECTED_SKU_VALUE_ID:""},el=function(t){var e={};return t.forEach((function(t){e[t.k_s]=t.v})),e},il=function(t,e){var i=Object.keys(e).filter((function(t){return""!==e[t]}));return t.length===i.length},nl=function(t,e){return t.filter((function(t){return Object.keys(e).every((function(i){return String(t[i])===String(e[i])}))}))[0]},sl=function(t,e){var i=el(t);return Object.keys(e).reduce((function(t,n){var s=i[n],r=e[n];if(""!==r){var o=s.filter((function(t){return t.id===r}))[0];o&&t.push(o)}return t}),[])},rl=function(t,e,i){var s,r=i.key,o=i.valueId,a=n(n({},e),{},((s={})[r]=o,s)),l=Object.keys(a).filter((function(t){return""!==a[t]}));return t.filter((function(t){return l.every((function(e){return String(a[e])===String(t[e])}))})).reduce((function(t,e){return t+=e.stock_num}),0)>0},ol=function(t,e){var i=function(t){var e={};return t.forEach((function(t){var i={};t.v.forEach((function(t){i[t.id]=t})),e[t.k_id]=i})),e}(t);return Object.keys(e).reduce((function(t,s){return e[s].forEach((function(e){t.push(n({},i[s][e]))})),t}),[])},al=function(t,e){var i=[];return(t||[]).forEach((function(t){if(e[t.k_id]&&e[t.k_id].length>0){var s=[];t.v.forEach((function(i){e[t.k_id].indexOf(i.id)>-1&&s.push(n({},i))})),i.push(n(n({},t),{},{v:s}))}})),i},ll={normalizeSkuTree:el,getSkuComb:nl,getSelectedSkuValues:sl,isAllSelected:il,isSkuChoosable:rl,getSelectedPropValues:ol,getSelectedProperties:al},cl=Object(o.a)("sku-header"),ul=cl[0],hl=cl[1];function dl(t,e,i,n){var s,o=e.sku,a=e.goods,l=e.skuEventBus,c=function(t,e){var i;return t.tree.some((function(t){var n=e[t.k_s];if(n&&t.v){var s=t.v.filter((function(t){return t.id===n}))[0]||{};return i=s.previewImgUrl||s.imgUrl||s.img_url}return!1})),i}(o,e.selectedSku)||a.picture;return t("div",r()([{class:[hl(),Tt]},h(n)]),[t("div",{class:hl("img-wrap"),on:{click:function(){l.$emit("sku:previewImage",c)}}},[t("img",{attrs:{src:c}}),null==(s=i["sku-header-image-extra"])?void 0:s.call(i)]),t("div",{class:hl("goods-info")},[null==i.default?void 0:i.default()])])}dl.props={sku:Object,goods:Object,skuEventBus:Object,selectedSku:Object};var fl=ul(dl),pl=Object(o.a)("sku-header-item"),ml=pl[0],vl=pl[1];var gl=ml((function(t,e,i,n){return t("div",r()([{class:vl()},h(n)]),[i.default&&i.default()])})),bl=Object(o.a)("sku-row"),yl=bl[0],Sl=bl[1],kl=bl[2];function xl(t,e,i,n){var s=e.skuRow.is_multiple&&t("span",{class:Sl("title-multiple")},["（",kl("multiple"),"）"]);return t("div",r()([{class:[Sl(),Tt]},h(n)]),[t("div",{class:Sl("title")},[e.skuRow.k,s]),i.default&&i.default()])}xl.props={skuRow:Object};var wl=yl(xl),Cl=(0,Object(o.a)("sku-row-item")[0])({props:{skuValue:Object,skuKeyStr:String,skuEventBus:Object,selectedSku:Object,skuList:{type:Array,default:function(){return[]}}},computed:{choosable:function(){return rl(this.skuList,this.selectedSku,{key:this.skuKeyStr,valueId:this.skuValue.id})}},methods:{onSelect:function(){this.choosable&&this.skuEventBus.$emit("sku:select",n(n({},this.skuValue),{},{skuKeyStr:this.skuKeyStr}))}},render:function(){var t=arguments[0],e=this.skuValue.id===this.selectedSku[this.skuKeyStr],i=this.skuValue.imgUrl||this.skuValue.img_url;return t("span",{class:["van-sku-row__item",{"van-sku-row__item--active":e,"van-sku-row__item--disabled":!this.choosable}],on:{click:this.onSelect}},[i&&t("img",{class:"van-sku-row__item-img",attrs:{src:i}}),t("span",{class:"van-sku-row__item-name"},[this.skuValue.name])])}}),Ol=(0,Object(o.a)("sku-row-prop-item")[0])({props:{skuValue:Object,skuKeyStr:String,skuEventBus:Object,selectedProp:Object,multiple:Boolean},computed:{choosed:function(){var t=this.selectedProp,e=this.skuKeyStr,i=this.skuValue;return!(!t||!t[e])&&t[e].indexOf(i.id)>-1}},methods:{onSelect:function(){this.skuEventBus.$emit("sku:propSelect",n(n({},this.skuValue),{},{skuKeyStr:this.skuKeyStr,multiple:this.multiple}))}},render:function(){var t=arguments[0];return t("span",{class:["van-sku-row__item",{"van-sku-row__item--active":this.choosed}],on:{click:this.onSelect}},[t("span",{class:"van-sku-row__item-name"},[this.skuValue.name])])}}),Tl=Object(o.a)("stepper"),$l=Tl[0],Bl=Tl[1];function Il(t,e){return String(t)===String(e)}var jl=$l({mixins:[_e],props:{value:null,theme:String,integer:Boolean,disabled:Boolean,inputWidth:[Number,String],buttonSize:[Number,String],asyncChange:Boolean,disablePlus:Boolean,disableMinus:Boolean,disableInput:Boolean,decimalLength:[Number,String],name:{type:[Number,String],default:""},min:{type:[Number,String],default:1},max:{type:[Number,String],default:1/0},step:{type:[Number,String],default:1},defaultValue:{type:[Number,String],default:1},showPlus:{type:Boolean,default:!0},showMinus:{type:Boolean,default:!0},longPress:{type:Boolean,default:!0}},data:function(){var t=Object(m.b)(this.value)?this.value:this.defaultValue,e=this.format(t);return Il(e,this.value)||this.$emit("input",e),{currentValue:e}},computed:{minusDisabled:function(){return this.disabled||this.disableMinus||this.currentValue<=this.min},plusDisabled:function(){return this.disabled||this.disablePlus||this.currentValue>=this.max},inputStyle:function(){var t={};return this.inputWidth&&(t.width=Object(U.a)(this.inputWidth)),this.buttonSize&&(t.height=Object(U.a)(this.buttonSize)),t},buttonStyle:function(){if(this.buttonSize){var t=Object(U.a)(this.buttonSize);return{width:t,height:t}}}},watch:{max:"check",min:"check",integer:"check",decimalLength:"check",value:function(t){Il(t,this.currentValue)||(this.currentValue=this.format(t))},currentValue:function(t){this.$emit("input",t),this.$emit("change",t,{name:this.name})}},methods:{check:function(){var t=this.format(this.currentValue);Il(t,this.currentValue)||(this.currentValue=t)},formatNumber:function(t){return Dt(String(t),!this.integer)},format:function(t){return t=""===(t=this.formatNumber(t))?0:+t,t=Object(ji.a)(t)?this.min:t,t=Math.max(Math.min(this.max,t),this.min),Object(m.b)(this.decimalLength)&&(t=t.toFixed(this.decimalLength)),t},onInput:function(t){var e=t.target.value,i=this.formatNumber(e);if(Object(m.b)(this.decimalLength)&&-1!==i.indexOf(".")){var n=i.split(".");i=n[0]+"."+n[1].slice(0,this.decimalLength)}Il(e,i)||(t.target.value=i),this.emitChange(i)},emitChange:function(t){this.asyncChange?(this.$emit("input",t),this.$emit("change",t,{name:this.name})):this.currentValue=t},onChange:function(){var t=this.type;if(this[t+"Disabled"])this.$emit("overlimit",t);else{var e,i,n,s="minus"===t?-this.step:+this.step,r=this.format((e=+this.currentValue,i=s,n=Math.pow(10,10),Math.round((e+i)*n)/n));this.emitChange(r),this.$emit(t)}},onFocus:function(t){this.disableInput&&this.$refs.input?this.$refs.input.blur():this.$emit("focus",t)},onBlur:function(t){var e=this.format(t.target.value);t.target.value=e,this.currentValue=e,this.$emit("blur",t),Xt()},longPressStep:function(){var t=this;this.longPressTimer=setTimeout((function(){t.onChange(),t.longPressStep(t.type)}),200)},onTouchStart:function(){var t=this;this.longPress&&(clearTimeout(this.longPressTimer),this.isLongPress=!1,this.longPressTimer=setTimeout((function(){t.isLongPress=!0,t.onChange(),t.longPressStep()}),600))},onTouchEnd:function(t){this.longPress&&(clearTimeout(this.longPressTimer),this.isLongPress&&k(t))}},render:function(){var t=this,e=arguments[0],i=function(e){return{on:{click:function(){t.type=e,t.onChange()},touchstart:function(){t.type=e,t.onTouchStart()},touchend:t.onTouchEnd,touchcancel:t.onTouchEnd}}};return e("div",{class:Bl([this.theme])},[e("button",r()([{directives:[{name:"show",value:this.showMinus}],attrs:{type:"button"},style:this.buttonStyle,class:Bl("minus",{disabled:this.minusDisabled})},i("minus")])),e("input",{ref:"input",attrs:{type:this.integer?"tel":"text",role:"spinbutton",disabled:this.disabled,readonly:this.disableInput,inputmode:this.integer?"numeric":"decimal","aria-valuemax":this.max,"aria-valuemin":this.min,"aria-valuenow":this.currentValue},class:Bl("input"),domProps:{value:this.currentValue},style:this.inputStyle,on:{input:this.onInput,focus:this.onFocus,blur:this.onBlur}}),e("button",r()([{directives:[{name:"show",value:this.showPlus}],attrs:{type:"button"},style:this.buttonStyle,class:Bl("plus",{disabled:this.plusDisabled})},i("plus")]))])}}),El=Object(o.a)("sku-stepper"),Dl=El[0],Nl=El[2],Ll=Ja.QUOTA_LIMIT,Al=Ja.STOCK_LIMIT,Pl=Dl({props:{stock:Number,skuEventBus:Object,skuStockNum:Number,selectedNum:Number,stepperTitle:String,disableStepperInput:Boolean,customStepperConfig:Object,hideQuotaText:Boolean,quota:{type:Number,default:0},quotaUsed:{type:Number,default:0},startSaleNum:{type:Number,default:1}},data:function(){return{currentNum:this.selectedNum,limitType:Al}},watch:{currentNum:function(t){var e=parseInt(t,10);e>=this.stepperMinLimit&&e<=this.stepperLimit&&this.skuEventBus.$emit("sku:numChange",e)},stepperLimit:function(t){t<this.currentNum&&this.stepperMinLimit<=t&&(this.currentNum=t),this.checkState(this.stepperMinLimit,t)},stepperMinLimit:function(t){(t>this.currentNum||t>this.stepperLimit)&&(this.currentNum=t),this.checkState(t,this.stepperLimit)}},computed:{stepperLimit:function(){var t,e=this.quota-this.quotaUsed;return this.quota>0&&e<=this.stock?(t=e<0?0:e,this.limitType=Ll):(t=this.stock,this.limitType=Al),t},stepperMinLimit:function(){return this.startSaleNum<1?1:this.startSaleNum},quotaText:function(){var t=this.customStepperConfig,e=t.quotaText;if(t.hideQuotaText)return"";var i="";if(e)i=e;else{var n=[];this.startSaleNum>1&&n.push(Nl("quotaStart",this.startSaleNum)),this.quota>0&&n.push(Nl("quotaLimit",this.quota)),i=n.join(Nl("comma"))}return i}},created:function(){this.checkState(this.stepperMinLimit,this.stepperLimit)},methods:{setCurrentNum:function(t){this.currentNum=t,this.checkState(this.stepperMinLimit,this.stepperLimit)},onOverLimit:function(t){this.skuEventBus.$emit("sku:overLimit",{action:t,limitType:this.limitType,quota:this.quota,quotaUsed:this.quotaUsed,startSaleNum:this.startSaleNum})},onChange:function(t){var e=parseInt(t,10),i=this.customStepperConfig.handleStepperChange;i&&i(e),this.$emit("change",e)},checkState:function(t,e){this.currentNum<t||t>e?this.currentNum=t:this.currentNum>e&&(this.currentNum=e),this.skuEventBus.$emit("sku:stepperState",{valid:t<=e,min:t,max:e,limitType:this.limitType,quota:this.quota,quotaUsed:this.quotaUsed,startSaleNum:this.startSaleNum})}},render:function(){var t=this,e=arguments[0];return e("div",{class:"van-sku-stepper-stock"},[e("div",{class:"van-sku__stepper-title"},[this.stepperTitle||Nl("num")]),e(jl,{attrs:{integer:!0,min:this.stepperMinLimit,max:this.stepperLimit,disableInput:this.disableStepperInput},class:"van-sku__stepper",on:{overlimit:this.onOverLimit,change:this.onChange},model:{value:t.currentNum,callback:function(e){t.currentNum=e}}}),!this.hideQuotaText&&this.quotaText&&e("span",{class:"van-sku__stepper-quota"},["(",this.quotaText,")"])])}});function zl(t){return/^((([a-z]|\d|[!#\$%&'\*\+\-\/=\?\^_`{\|}~]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])+(\.([a-z]|\d|[!#\$%&'\*\+\-\/=\?\^_`{\|}~]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])+)*)|((\x22)((((\x20|\x09)*(\x0d\x0a))?(\x20|\x09)+)?(([\x01-\x08\x0b\x0c\x0e-\x1f\x7f]|\x21|[\x23-\x5b]|[\x5d-\x7e]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(\\([\x01-\x09\x0b\x0c\x0d-\x7f]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]))))*(((\x20|\x09)*(\x0d\x0a))?(\x20|\x09)+)?(\x22)))@((([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.)+(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))$/i.test(t)}function Ml(t){return Array.isArray(t)?t:[t]}function Fl(t,e){return new Promise((function(i){if("file"!==e){var n=new FileReader;n.onload=function(t){i(t.target.result)},"dataUrl"===e?n.readAsDataURL(t):"text"===e&&n.readAsText(t)}else i()}))}var Vl=/\.(jpeg|jpg|gif|png|svg|webp|jfif|bmp|dpg)/i;function Rl(t){return!!t.isImage||(t.file&&t.file.type?0===t.file.type.indexOf("image"):t.url?(e=t.url,Vl.test(e)):!!t.content&&0===t.content.indexOf("data:image"));var e}var _l=Object(o.a)("uploader"),Hl=_l[0],Wl=_l[1],ql=Hl({inheritAttrs:!1,mixins:[_e],model:{prop:"fileList"},props:{disabled:Boolean,lazyLoad:Boolean,uploadText:String,afterRead:Function,beforeRead:Function,beforeDelete:Function,previewSize:[Number,String],name:{type:[Number,String],default:""},accept:{type:String,default:"image/*"},fileList:{type:Array,default:function(){return[]}},maxSize:{type:[Number,String],default:Number.MAX_VALUE},maxCount:{type:[Number,String],default:Number.MAX_VALUE},deletable:{type:Boolean,default:!0},showUpload:{type:Boolean,default:!0},previewImage:{type:Boolean,default:!0},previewFullImage:{type:Boolean,default:!0},imageFit:{type:String,default:"cover"},resultType:{type:String,default:"dataUrl"},uploadIcon:{type:String,default:"photograph"}},computed:{previewSizeWithUnit:function(){return Object(U.a)(this.previewSize)},value:function(){return this.fileList}},methods:{getDetail:function(t){return void 0===t&&(t=this.fileList.length),{name:this.name,index:t}},onChange:function(t){var e=this,i=t.target.files;if(!this.disabled&&i.length){if(i=1===i.length?i[0]:[].slice.call(i),this.beforeRead){var n=this.beforeRead(i,this.getDetail());if(!n)return void this.resetInput();if(Object(m.e)(n))return void n.then((function(t){t?e.readFile(t):e.readFile(i)})).catch(this.resetInput)}this.readFile(i)}},readFile:function(t){var e=this,i=function(t,e){return Ml(t).some((function(t){return t.size>e}))}(t,this.maxSize);if(Array.isArray(t)){var n=this.maxCount-this.fileList.length;t.length>n&&(t=t.slice(0,n)),Promise.all(t.map((function(t){return Fl(t,e.resultType)}))).then((function(n){var s=t.map((function(t,e){var i={file:t,status:"",message:""};return n[e]&&(i.content=n[e]),i}));e.onAfterRead(s,i)}))}else Fl(t,this.resultType).then((function(n){var s={file:t,status:"",message:""};n&&(s.content=n),e.onAfterRead(s,i)}))},onAfterRead:function(t,e){var i=this;this.resetInput();var n=t;if(e){var s=t;Array.isArray(t)?(s=[],n=[],t.forEach((function(t){t.file&&(t.file.size>i.maxSize?s.push(t):n.push(t))}))):n=null,this.$emit("oversize",s,this.getDetail())}(Array.isArray(n)?Boolean(n.length):Boolean(n))&&(this.$emit("input",[].concat(this.fileList,Ml(n))),this.afterRead&&this.afterRead(n,this.getDetail()))},onDelete:function(t,e){var i=this;if(this.beforeDelete){var n=this.beforeDelete(t,this.getDetail(e));if(!n)return;if(Object(m.e)(n))return void n.then((function(){i.deleteFile(t,e)})).catch(m.g)}this.deleteFile(t,e)},deleteFile:function(t,e){var i=this.fileList.slice(0);i.splice(e,1),this.$emit("input",i),this.$emit("delete",t,this.getDetail(e))},resetInput:function(){this.$refs.input&&(this.$refs.input.value="")},onPreviewImage:function(t){var e=this;if(this.previewFullImage){var i=this.fileList.filter((function(t){return Rl(t)})),n=i.map((function(t){return t.content||t.url}));this.imagePreview=no({images:n,closeOnPopstate:!0,startPosition:i.indexOf(t),onClose:function(){e.$emit("close-preview")}})}},closeImagePreview:function(){this.imagePreview&&this.imagePreview.close()},chooseFile:function(){this.disabled||this.$refs.input&&this.$refs.input.click()},genPreviewMask:function(t){var e=this.$createElement,i=t.status,n=t.message;if("uploading"===i||"failed"===i){var s="failed"===i?e(st,{attrs:{name:"warning-o"},class:Wl("mask-icon")}):e(mt,{class:Wl("loading")}),r=Object(m.b)(n)&&""!==n;return e("div",{class:Wl("mask")},[s,r&&e("div",{class:Wl("mask-message")},[n])])}},genPreviewItem:function(t,e){var i=this,n=this.$createElement,s="uploading"!==t.status&&this.deletable&&n(st,{attrs:{name:"clear"},class:Wl("preview-delete"),on:{click:function(n){n.stopPropagation(),i.onDelete(t,e)}}}),r=Rl(t)?n(Xi,{attrs:{fit:this.imageFit,src:t.content||t.url,width:this.previewSize,height:this.previewSize,lazyLoad:this.lazyLoad},class:Wl("preview-image"),on:{click:function(){i.onPreviewImage(t)}}}):n("div",{class:Wl("file"),style:{width:this.previewSizeWithUnit,height:this.previewSizeWithUnit}},[n(st,{class:Wl("file-icon"),attrs:{name:"description"}}),n("div",{class:[Wl("file-name"),"van-ellipsis"]},[t.file?t.file.name:t.url])]);return n("div",{class:Wl("preview"),on:{click:function(){i.$emit("click-preview",t,i.getDetail(e))}}},[r,this.genPreviewMask(t),s])},genPreviewList:function(){if(this.previewImage)return this.fileList.map(this.genPreviewItem)},genUpload:function(){var t=this.$createElement;if(!(this.fileList.length>=this.maxCount)&&this.showUpload){var e,i=this.slots(),s=t("input",{attrs:n(n({},this.$attrs),{},{type:"file",accept:this.accept,disabled:this.disabled}),ref:"input",class:Wl("input"),on:{change:this.onChange}});if(i)return t("div",{class:Wl("input-wrapper")},[i,s]);if(this.previewSize){var r=this.previewSizeWithUnit;e={width:r,height:r}}return t("div",{class:Wl("upload"),style:e},[t(st,{attrs:{name:this.uploadIcon},class:Wl("upload-icon")}),this.uploadText&&t("span",{class:Wl("upload-text")},[this.uploadText]),s])}}},render:function(){var t=arguments[0];return t("div",{class:Wl()},[t("div",{class:Wl("wrapper",{disabled:this.disabled})},[this.genPreviewList(),this.genUpload()])])}}),Yl=Object(o.a)("sku-img-uploader"),Ul=Yl[0],Kl=Yl[1],Xl=Yl[2],Ql=Ul({props:{value:String,uploadImg:Function,maxSize:{type:Number,default:6}},data:function(){return{paddingImg:"",uploadFail:!1}},methods:{afterReadFile:function(t){var e=this;this.paddingImg=t.content,this.uploadFail=!1,this.uploadImg(t.file,t.content).then((function(t){e.$emit("input",t),e.$nextTick((function(){e.paddingImg=""}))})).catch((function(){e.uploadFail=!0}))},onOversize:function(){this.$toast(Xl("oversize",this.maxSize))},genUploader:function(t,e){void 0===e&&(e=!1);var i=this.$createElement;return i(ql,{class:Kl("uploader"),attrs:{disabled:e,afterRead:this.afterReadFile,maxSize:1024*this.maxSize*1024},on:{oversize:this.onOversize}},[i("div",{class:Kl("img")},[t])])},genMask:function(){var t=this.$createElement;return t("div",{class:Kl("mask")},[this.uploadFail?[t(st,{attrs:{name:"warning-o",size:"20px"}}),t("div",{class:Kl("warn-text"),domProps:{innerHTML:Xl("fail")}})]:t(mt,{attrs:{type:"spinner",size:"20px",color:"white"}})])}},render:function(){var t=this,e=arguments[0];return e("div",{class:Kl()},[this.value&&this.genUploader([e("img",{attrs:{src:this.value}}),e(st,{attrs:{name:"clear"},class:Kl("delete"),on:{click:function(){t.$emit("input","")}}})],!0),this.paddingImg&&this.genUploader([e("img",{attrs:{src:this.paddingImg}}),this.genMask()],!this.uploadFail),!this.value&&!this.paddingImg&&this.genUploader(e("div",{class:Kl("trigger")},[e(st,{attrs:{name:"photograph",size:"22px"}})]))])}}),Gl=Object(o.a)("sku-messages"),Zl=Gl[0],Jl=Gl[1],tc=Gl[2],ec=Zl({props:{messageConfig:Object,goodsId:[Number,String],messages:{type:Array,default:function(){return[]}}},data:function(){return{messageValues:this.resetMessageValues(this.messages)}},watch:{messages:function(t){this.messageValues=this.resetMessageValues(t)}},methods:{resetMessageValues:function(t){var e=this.messageConfig.initialMessages,i=void 0===e?{}:e;return(t||[]).map((function(t){return{value:i[t.name]||""}}))},getType:function(t){return 1==+t.multiple?"textarea":"id_no"===t.type?"text":t.datetime>0?"datetime-local":t.type},getMessages:function(){var t=this,e={};return this.messageValues.forEach((function(i,n){var s=i.value;t.messages[n].datetime>0&&(s=s.replace(/T/g," ")),e["message_"+n]=s})),e},getCartMessages:function(){var t=this,e={};return this.messageValues.forEach((function(i,n){var s=i.value,r=t.messages[n];r.datetime>0&&(s=s.replace(/T/g," ")),e[r.name]=s})),e},getPlaceholder:function(t){var e=1==+t.multiple?"textarea":t.type,i=this.messageConfig.placeholderMap||{};return t.placeholder||i[e]||tc("placeholder."+e)},validateMessages:function(){for(var t=this.messageValues,e=0;e<t.length;e++){var i=t[e].value,n=this.messages[e];if(""===i){if("1"===String(n.required))return tc("image"===n.type?"upload":"fill")+n.name}else{if("tel"===n.type&&!Object(ji.b)(i))return tc("invalid.tel");if("mobile"===n.type&&!/^\d{6,20}$/.test(i))return tc("invalid.mobile");if("email"===n.type&&!zl(i))return tc("invalid.email");if("id_no"===n.type&&(i.length<15||i.length>18))return tc("invalid.id_no")}}},genMessage:function(t,e){var i=this,n=this.$createElement;return"image"===t.type?n(se,{key:this.goodsId+"-"+e,attrs:{title:t.name,label:tc("imageLabel"),required:"1"===String(t.required),valueClass:Jl("image-cell-value")},class:Jl("image-cell")},[n(Ql,{attrs:{maxSize:this.messageConfig.uploadMaxSize,uploadImg:this.messageConfig.uploadImg},model:{value:i.messageValues[e].value,callback:function(t){i.$set(i.messageValues[e],"value",t)}}})]):n(le,{attrs:{maxlength:"200",label:t.name,required:"1"===String(t.required),placeholder:this.getPlaceholder(t),type:this.getType(t)},key:this.goodsId+"-"+e,model:{value:i.messageValues[e].value,callback:function(t){i.$set(i.messageValues[e],"value",t)}}})}},render:function(){var t=arguments[0];return t("div",{class:Jl()},[this.messages.map(this.genMessage)])}}),ic=Object(o.a)("sku-actions"),nc=ic[0],sc=ic[1],rc=ic[2];function oc(t,e,i,n){var s=function(t){return function(){e.skuEventBus.$emit(t)}};return t("div",r()([{class:sc()},h(n)]),[e.showAddCartBtn&&t($e,{attrs:{size:"large",type:"warning",text:e.addCartText||rc("addCart")},on:{click:s("sku:addCart")}}),t($e,{attrs:{size:"large",type:"danger",text:e.buyText||rc("buy")},on:{click:s("sku:buy")}})])}oc.props={buyText:String,addCartText:String,skuEventBus:Object,showAddCartBtn:Boolean};var ac=nc(oc),lc=Object(o.a)("sku"),cc=lc[0],uc=lc[1],hc=lc[2],dc=Ja.QUOTA_LIMIT,fc=cc({props:{sku:Object,priceTag:String,goods:Object,value:Boolean,buyText:String,goodsId:[Number,String],hideStock:Boolean,addCartText:String,stepperTitle:String,getContainer:[String,Function],hideQuotaText:Boolean,hideSelectedText:Boolean,resetStepperOnHide:Boolean,customSkuValidator:Function,closeOnClickOverlay:Boolean,disableStepperInput:Boolean,resetSelectedSkuOnHide:Boolean,properties:Array,quota:{type:Number,default:0},quotaUsed:{type:Number,default:0},startSaleNum:{type:Number,default:1},initialSku:{type:Object,default:function(){return{}}},stockThreshold:{type:Number,default:50},showSoldoutSku:{type:Boolean,default:!0},showAddCartBtn:{type:Boolean,default:!0},bodyOffsetTop:{type:Number,default:200},messageConfig:{type:Object,default:function(){return{initialMessages:{},placeholderMap:{},uploadImg:function(){return Promise.resolve()},uploadMaxSize:5}}},customStepperConfig:{type:Object,default:function(){return{}}},previewOnClickImage:{type:Boolean,default:!0},safeAreaInsetBottom:{type:Boolean,default:!0}},data:function(){return{selectedSku:{},selectedProp:{},selectedNum:1,show:this.value}},watch:{show:function(t){this.$emit("input",t),t||(this.$emit("sku-close",{selectedSkuValues:this.selectedSkuValues,selectedNum:this.selectedNum,selectedSkuComb:this.selectedSkuComb}),this.resetStepperOnHide&&this.resetStepper(),this.resetSelectedSkuOnHide&&this.resetSelectedSku())},value:function(t){this.show=t},skuTree:"resetSelectedSku",initialSku:function(){this.resetStepper(),this.resetSelectedSku()}},computed:{skuGroupClass:function(){return["van-sku-group-container",{"van-sku-group-container--hide-soldout":!this.showSoldoutSku}]},bodyStyle:function(){if(!this.$isServer)return{maxHeight:window.innerHeight-this.bodyOffsetTop+"px"}},isSkuCombSelected:function(){var t=this;return!(this.hasSku&&!il(this.skuTree,this.selectedSku))&&!this.propList.some((function(e){return(t.selectedProp[e.k_id]||[]).length<1}))},isSkuEmpty:function(){return 0===Object.keys(this.sku).length},hasSku:function(){return!this.sku.none_sku},hasSkuOrAttr:function(){return this.hasSku||this.propList.length>0},selectedSkuComb:function(){var t=null;return this.isSkuCombSelected&&(t=this.hasSku?nl(this.sku.list,this.selectedSku):{id:this.sku.collection_id,price:Math.round(100*this.sku.price),stock_num:this.sku.stock_num})&&(t.properties=al(this.propList,this.selectedProp),t.property_price=this.selectedPropValues.reduce((function(t,e){return t+(e.price||0)}),0)),t},selectedSkuValues:function(){return sl(this.skuTree,this.selectedSku)},selectedPropValues:function(){return ol(this.propList,this.selectedProp)},price:function(){return this.selectedSkuComb?((this.selectedSkuComb.price+this.selectedSkuComb.property_price)/100).toFixed(2):this.sku.price},originPrice:function(){return this.selectedSkuComb&&this.selectedSkuComb.origin_price?((this.selectedSkuComb.origin_price+this.selectedSkuComb.property_price)/100).toFixed(2):this.sku.origin_price},skuTree:function(){return this.sku.tree||[]},propList:function(){return this.properties||[]},imageList:function(){var t=[this.goods.picture];return this.skuTree.length>0&&this.skuTree.forEach((function(e){e.v&&e.v.forEach((function(e){var i=e.previewImgUrl||e.imgUrl||e.img_url;i&&t.push(i)}))})),t},stock:function(){var t=this.customStepperConfig.stockNum;return void 0!==t?t:this.selectedSkuComb?this.selectedSkuComb.stock_num:this.sku.stock_num},stockText:function(){var t=this.$createElement,e=this.customStepperConfig.stockFormatter;return e?e(this.stock):[hc("stock")+" ",t("span",{class:uc("stock-num",{highlight:this.stock<this.stockThreshold})},[this.stock])," "+hc("stockUnit")]},selectedText:function(){var t=this;if(this.selectedSkuComb){var e=this.selectedSkuValues.concat(this.selectedPropValues);return hc("selected")+" "+e.map((function(t){return t.name})).join("；")}var i=this.skuTree.filter((function(e){return""===t.selectedSku[e.k_s]})).map((function(t){return t.k})),n=this.propList.filter((function(e){return(t.selectedProp[e.k_id]||[]).length<1})).map((function(t){return t.k}));return hc("select")+" "+i.concat(n).join("；")}},created:function(){var t=new l.a;this.skuEventBus=t,t.$on("sku:select",this.onSelect),t.$on("sku:propSelect",this.onPropSelect),t.$on("sku:numChange",this.onNumChange),t.$on("sku:previewImage",this.onPreviewImage),t.$on("sku:overLimit",this.onOverLimit),t.$on("sku:stepperState",this.onStepperState),t.$on("sku:addCart",this.onAddCart),t.$on("sku:buy",this.onBuy),this.resetStepper(),this.resetSelectedSku(),this.$emit("after-sku-create",t)},methods:{resetStepper:function(){var t=this.$refs.skuStepper,e=this.initialSku.selectedNum,i=Object(m.b)(e)?e:this.startSaleNum;this.stepperError=null,t?t.setCurrentNum(i):this.selectedNum=i},resetSelectedSku:function(){var t=this;this.selectedSku={},this.skuTree.forEach((function(e){t.selectedSku[e.k_s]=t.initialSku[e.k_s]||""})),this.skuTree.forEach((function(e){var i=e.k_s,n=e.v[0].id;1===e.v.length&&rl(t.sku.list,t.selectedSku,{key:i,valueId:n})&&(t.selectedSku[i]=n)}));var e=this.selectedSkuValues;e.length>0&&this.$nextTick((function(){t.$emit("sku-selected",{skuValue:e[e.length-1],selectedSku:t.selectedSku,selectedSkuComb:t.selectedSkuComb})})),this.selectedProp={};var i=this.initialSku.selectedProp,n=void 0===i?{}:i;this.propList.forEach((function(e){e.v&&1===e.v.length?t.selectedProp[e.k_id]=[e.v[0].id]:n[e.k_id]&&(t.selectedProp[e.k_id]=n[e.k_id])}));var s=this.selectedPropValues;s.length>0&&this.$emit("sku-prop-selected",{propValue:s[s.length-1],selectedProp:this.selectedProp,selectedSkuComb:this.selectedSkuComb}),this.$emit("sku-reset",{selectedSku:this.selectedSku,selectedProp:this.selectedProp,selectedSkuComb:this.selectedSkuComb})},getSkuMessages:function(){return this.$refs.skuMessages?this.$refs.skuMessages.getMessages():{}},getSkuCartMessages:function(){return this.$refs.skuMessages?this.$refs.skuMessages.getCartMessages():{}},validateSkuMessages:function(){return this.$refs.skuMessages?this.$refs.skuMessages.validateMessages():""},validateSku:function(){if(0===this.selectedNum)return hc("unavailable");if(this.isSkuCombSelected)return this.validateSkuMessages();if(this.customSkuValidator){var t=this.customSkuValidator(this);if(t)return t}return hc("selectSku")},onSelect:function(t){var e,i;this.selectedSku=this.selectedSku[t.skuKeyStr]===t.id?n(n({},this.selectedSku),{},((e={})[t.skuKeyStr]="",e)):n(n({},this.selectedSku),{},((i={})[t.skuKeyStr]=t.id,i)),this.$emit("sku-selected",{skuValue:t,selectedSku:this.selectedSku,selectedSkuComb:this.selectedSkuComb})},onPropSelect:function(t){var e,i=this.selectedProp[t.skuKeyStr]||[],s=i.indexOf(t.id);s>-1?i.splice(s,1):t.multiple?i.push(t.id):i.splice(0,1,t.id),this.selectedProp=n(n({},this.selectedProp),{},((e={})[t.skuKeyStr]=i,e)),this.$emit("sku-prop-selected",{propValue:t,selectedProp:this.selectedProp,selectedSkuComb:this.selectedSkuComb})},onNumChange:function(t){this.selectedNum=t},onPreviewImage:function(t){var e=this,i=this.previewOnClickImage,n=this.imageList.findIndex((function(e){return e===t})),s={index:n,imageList:this.imageList,indexImage:t};this.$emit("open-preview",s),i&&no({images:this.imageList,startPosition:n,closeOnPopstate:!0,onClose:function(){e.$emit("close-preview",s)}})},onOverLimit:function(t){var e=t.action,i=t.limitType,n=t.quota,s=t.quotaUsed,r=this.customStepperConfig.handleOverLimit;r?r(t):"minus"===e?this.startSaleNum>1?ke(hc("minusStartTip",this.startSaleNum)):ke(hc("minusTip")):"plus"===e&&ke(i===dc?s>0?hc("quotaUsedTip",n,s):hc("quotaTip",n):hc("soldout"))},onStepperState:function(t){t.valid?this.stepperError=null:this.stepperError=n(n({},t),{},{action:"plus"})},onAddCart:function(){this.onBuyOrAddCart("add-cart")},onBuy:function(){this.onBuyOrAddCart("buy-clicked")},onBuyOrAddCart:function(t){if(this.stepperError)return this.onOverLimit(this.stepperError);var e=this.validateSku();e?ke(e):this.$emit(t,this.getSkuData())},getSkuData:function(){return{goodsId:this.goodsId,selectedNum:this.selectedNum,selectedSkuComb:this.selectedSkuComb,messages:this.getSkuMessages(),cartMessages:this.getSkuCartMessages()}}},render:function(){var t=this,e=arguments[0];if(!this.isSkuEmpty){var i=this.sku,n=this.goods,s=this.price,r=this.originPrice,o=this.skuEventBus,a=this.selectedSku,l=this.selectedProp,c=this.selectedNum,u=this.stepperTitle,h=this.selectedSkuComb,d={price:s,originPrice:r,selectedNum:c,skuEventBus:o,selectedSku:a,selectedSkuComb:h},f=function(e){return t.slots(e,d)},p=f("sku-header")||e(fl,{attrs:{sku:i,goods:n,skuEventBus:o,selectedSku:a}},[e("template",{slot:"sku-header-image-extra"},[f("sku-header-image-extra")]),f("sku-header-price")||e("div",{class:"van-sku__goods-price"},[e("span",{class:"van-sku__price-symbol"},["￥"]),e("span",{class:"van-sku__price-num"},[s]),this.priceTag&&e("span",{class:"van-sku__price-tag"},[this.priceTag])]),f("sku-header-origin-price")||r&&e(gl,[hc("originPrice")," ￥",r]),!this.hideStock&&e(gl,[e("span",{class:"van-sku__stock"},[this.stockText])]),this.hasSkuOrAttr&&!this.hideSelectedText&&e(gl,[this.selectedText]),f("sku-header-extra")]),m=f("sku-group")||this.hasSkuOrAttr&&e("div",{class:this.skuGroupClass},[this.skuTree.map((function(t){return e(wl,{attrs:{skuRow:t}},[t.v.map((function(n){return e(Cl,{attrs:{skuList:i.list,skuValue:n,selectedSku:a,skuEventBus:o,skuKeyStr:t.k_s}})}))])})),this.propList.map((function(t){return e(wl,{attrs:{skuRow:t}},[t.v.map((function(i){return e(Ol,{attrs:{skuValue:i,skuKeyStr:t.k_id+"",selectedProp:l,skuEventBus:o,multiple:t.is_multiple}})}))])}))]),v=f("sku-stepper")||e(Pl,{ref:"skuStepper",attrs:{stock:this.stock,quota:this.quota,quotaUsed:this.quotaUsed,startSaleNum:this.startSaleNum,skuEventBus:o,selectedNum:c,stepperTitle:u,skuStockNum:i.stock_num,disableStepperInput:this.disableStepperInput,customStepperConfig:this.customStepperConfig,hideQuotaText:this.hideQuotaText},on:{change:function(e){t.$emit("stepper-change",e)}}}),g=f("sku-messages")||e(ec,{ref:"skuMessages",attrs:{goodsId:this.goodsId,messageConfig:this.messageConfig,messages:i.messages}}),b=f("sku-actions")||e(ac,{attrs:{buyText:this.buyText,skuEventBus:o,addCartText:this.addCartText,showAddCartBtn:this.showAddCartBtn}});return e(lt,{attrs:{round:!0,closeable:!0,position:"bottom",getContainer:this.getContainer,closeOnClickOverlay:this.closeOnClickOverlay,safeAreaInsetBottom:this.safeAreaInsetBottom},class:"van-sku-container",model:{value:t.show,callback:function(e){t.show=e}}},[p,e("div",{class:"van-sku-body",style:this.bodyStyle},[f("sku-body-top"),m,f("extra-sku-group"),v,g]),f("sku-actions-top"),b])}}});So.a.add({"zh-CN":{vanSku:{select:"选择",selected:"已选",selectSku:"请先选择商品规格",soldout:"库存不足",originPrice:"原价",minusTip:"至少选择一件",minusStartTip:function(t){return t+"件起售"},unavailable:"商品已经无法购买啦",stock:"剩余",stockUnit:"件",quotaTip:function(t){return"每人限购"+t+"件"},quotaUsedTip:function(t,e){return"每人限购"+t+"件，你已购买"+e+"件"}},vanSkuActions:{buy:"立即购买",addCart:"加入购物车"},vanSkuImgUploader:{oversize:function(t){return"最大可上传图片为"+t+"MB，请尝试压缩图片尺寸"},fail:"上传失败<br />重新上传"},vanSkuStepper:{quotaLimit:function(t){return"限购"+t+"件"},quotaStart:function(t){return t+"件起售"},comma:"，",num:"购买数量"},vanSkuMessages:{fill:"请填写",upload:"请上传",imageLabel:"仅限一张",invalid:{tel:"请填写正确的数字格式留言",mobile:"手机号长度为6-20位数字",email:"请填写正确的邮箱",id_no:"请填写正确的身份证号码"},placeholder:{id_no:"输入身份证号码",text:"输入文本",tel:"输入数字",email:"输入邮箱",date:"点击选择日期",time:"点击选择时间",textarea:"点击填写段落文本",mobile:"输入手机号码"}},vanSkuRow:{multiple:"可多选"}}}),fc.SkuActions=ac,fc.SkuHeader=fl,fc.SkuHeaderItem=gl,fc.SkuMessages=ec,fc.SkuStepper=Pl,fc.SkuRow=wl,fc.SkuRowItem=Cl,fc.SkuRowPropItem=Ol,fc.skuHelper=ll,fc.skuConstants=tl;var pc=fc,mc=Object(o.a)("slider"),vc=mc[0],gc=mc[1],bc=vc({mixins:[R,_e],props:{disabled:Boolean,vertical:Boolean,barHeight:[Number,String],buttonSize:[Number,String],activeColor:String,inactiveColor:String,min:{type:[Number,String],default:0},max:{type:[Number,String],default:100},step:{type:[Number,String],default:1},value:{type:Number,default:0}},data:function(){return{dragStatus:""}},computed:{range:function(){return this.max-this.min},buttonStyle:function(){if(this.buttonSize){var t=Object(U.a)(this.buttonSize);return{width:t,height:t}}}},created:function(){this.updateValue(this.value)},mounted:function(){this.bindTouchEvent(this.$refs.wrapper)},methods:{onTouchStart:function(t){this.disabled||(this.touchStart(t),this.startValue=this.format(this.value),this.dragStatus="start")},onTouchMove:function(t){if(!this.disabled){"start"===this.dragStatus&&this.$emit("drag-start"),k(t,!0),this.touchMove(t),this.dragStatus="draging";var e=this.$el.getBoundingClientRect(),i=(this.vertical?this.deltaY:this.deltaX)/(this.vertical?e.height:e.width)*this.range;this.newValue=this.startValue+i,this.updateValue(this.newValue)}},onTouchEnd:function(){this.disabled||("draging"===this.dragStatus&&(this.updateValue(this.newValue,!0),this.$emit("drag-end")),this.dragStatus="")},onClick:function(t){if(t.stopPropagation(),!this.disabled){var e=this.$el.getBoundingClientRect(),i=this.vertical?t.clientY-e.top:t.clientX-e.left,n=this.vertical?e.height:e.width,s=+this.min+i/n*this.range;this.startValue=this.value,this.updateValue(s,!0)}},updateValue:function(t,e){(t=this.format(t))!==this.value&&this.$emit("input",t),e&&t!==this.startValue&&this.$emit("change",t)},format:function(t){return Math.round(Math.max(this.min,Math.min(t,this.max))/this.step)*this.step}},render:function(){var t,e,i=arguments[0],n=this.vertical,s=n?"height":"width",r=n?"width":"height",o=((t={background:this.inactiveColor})[r]=Object(U.a)(this.barHeight),t),a=((e={})[s]=100*(this.value-this.min)/this.range+"%",e.background=this.activeColor,e);return this.dragStatus&&(a.transition="none"),i("div",{style:o,class:gc({disabled:this.disabled,vertical:n}),on:{click:this.onClick}},[i("div",{class:gc("bar"),style:a},[i("div",{ref:"wrapper",attrs:{role:"slider",tabindex:this.disabled?-1:0,"aria-valuemin":this.min,"aria-valuenow":this.value,"aria-valuemax":this.max,"aria-orientation":this.vertical?"vertical":"horizontal"},class:gc("button-wrapper")},[this.slots("button")||i("div",{class:gc("button"),style:this.buttonStyle})])])])}}),yc=Object(o.a)("step"),Sc=yc[0],kc=yc[1],xc=Sc({mixins:[ri("vanSteps")],computed:{status:function(){return this.index<this.parent.active?"finish":this.index===+this.parent.active?"process":void 0},active:function(){return"process"===this.status},lineStyle:function(){if("finish"===this.status&&this.parent.activeColor)return{background:this.parent.activeColor}}},methods:{genCircle:function(){var t=this.$createElement,e=this.parent,i=e.activeIcon,n=e.activeColor,s=e.inactiveIcon;if(this.active)return this.slots("active-icon")||t(st,{class:kc("icon","active"),attrs:{name:i,color:n}});var r=this.slots("inactive-icon");return s||r?r||t(st,{class:kc("icon"),attrs:{name:s}}):t("i",{class:kc("circle"),style:this.lineStyle})},onClickStep:function(){this.parent.$emit("click-step",this.index)}},render:function(){var t,e=arguments[0],i=this.status,n=this.active,s=this.parent,r=s.activeColor,o=s.direction,a=n&&{color:r};return e("div",{class:[Ct,kc([o,(t={},t[i]=i,t)])]},[e("div",{class:kc("title",{active:n}),style:a,on:{click:this.onClickStep}},[this.slots()]),e("div",{class:kc("circle-container"),on:{click:this.onClickStep}},[this.genCircle()]),e("div",{class:kc("line"),style:this.lineStyle})])}}),wc=Object(o.a)("steps"),Cc=wc[0],Oc=wc[1],Tc=Cc({mixins:[oi("vanSteps")],props:{activeColor:String,inactiveIcon:String,active:{type:[Number,String],default:0},direction:{type:String,default:"horizontal"},activeIcon:{type:String,default:"checked"}},render:function(){var t=arguments[0];return t("div",{class:Oc([this.direction])},[t("div",{class:Oc("items")},[this.slots()])])}}),$c=Object(o.a)("submit-bar"),Bc=$c[0],Ic=$c[1],jc=$c[2];function Ec(t,e,i,n){var s=e.tip,o=e.price,a=e.tipIcon;return t("div",r()([{class:Ic({unfit:!e.safeAreaInsetBottom})},h(n)]),[i.top&&i.top(),function(){if(i.tip||s)return t("div",{class:Ic("tip")},[a&&t(st,{class:Ic("tip-icon"),attrs:{name:a}}),s&&t("span",{class:Ic("tip-text")},[s]),i.tip&&i.tip()])}(),t("div",{class:Ic("bar")},[i.default&&i.default(),function(){if("number"==typeof o){var i=(o/100).toFixed(e.decimalLength).split("."),n=e.decimalLength?"."+i[1]:"";return t("div",{style:{textAlign:e.textAlign?e.textAlign:""},class:Ic("text")},[t("span",[e.label||jc("label")]),t("span",{class:Ic("price")},[e.currency,t("span",{class:Ic("price","integer")},[i[0]]),n]),e.suffixLabel&&t("span",{class:Ic("suffix-label")},[e.suffixLabel])])}}(),t($e,{attrs:{round:!0,type:e.buttonType,loading:e.loading,disabled:e.disabled,text:e.loading?"":e.buttonText},class:Ic("button",e.buttonType),on:{click:function(){d(n,"submit")}}})])])}Ec.props={tip:String,label:String,price:Number,tipIcon:String,loading:Boolean,disabled:Boolean,textAlign:String,buttonText:String,suffixLabel:String,safeAreaInsetBottom:{type:Boolean,default:!0},decimalLength:{type:[Number,String],default:2},currency:{type:String,default:"¥"},buttonType:{type:String,default:"danger"}};var Dc=Bc(Ec),Nc=Object(o.a)("swipe-cell"),Lc=Nc[0],Ac=Nc[1],Pc=Lc({mixins:[R,or({event:"touchstart",method:"onClick"})],props:{onClose:Function,disabled:Boolean,leftWidth:[Number,String],rightWidth:[Number,String],beforeClose:Function,stopPropagation:Boolean,name:{type:[Number,String],default:""}},data:function(){return{offset:0,dragging:!1}},computed:{computedLeftWidth:function(){return+this.leftWidth||this.getWidthByRef("left")},computedRightWidth:function(){return+this.rightWidth||this.getWidthByRef("right")}},mounted:function(){this.bindTouchEvent(this.$el)},methods:{getWidthByRef:function(t){return this.$refs[t]?this.$refs[t].getBoundingClientRect().width:0},open:function(t){var e="left"===t?this.computedLeftWidth:-this.computedRightWidth;this.opened=!0,this.offset=e,this.$emit("open",{position:t,name:this.name,detail:this.name})},close:function(t){this.offset=0,this.opened&&(this.opened=!1,this.$emit("close",{position:t,name:this.name}))},onTouchStart:function(t){this.disabled||(this.startOffset=this.offset,this.touchStart(t))},onTouchMove:function(t){this.disabled||(this.touchMove(t),"horizontal"===this.direction&&(this.dragging=!0,this.lockClick=!0,(!this.opened||this.deltaX*this.startOffset<0)&&k(t,this.stopPropagation),this.offset=jt(this.deltaX+this.startOffset,-this.computedRightWidth,this.computedLeftWidth)))},onTouchEnd:function(){var t=this;this.disabled||this.dragging&&(this.toggle(this.offset>0?"left":"right"),this.dragging=!1,setTimeout((function(){t.lockClick=!1}),0))},toggle:function(t){var e=Math.abs(this.offset),i=this.opened?.85:.15,n=this.computedLeftWidth,s=this.computedRightWidth;s&&"right"===t&&e>s*i?this.open("right"):n&&"left"===t&&e>n*i?this.open("left"):this.close()},onClick:function(t){void 0===t&&(t="outside"),this.$emit("click",t),this.opened&&!this.lockClick&&(this.beforeClose?this.beforeClose({position:t,name:this.name,instance:this}):this.onClose?this.onClose(t,this,{name:this.name}):this.close(t))},getClickHandler:function(t,e){var i=this;return function(n){e&&n.stopPropagation(),i.onClick(t)}},genLeftPart:function(){var t=this.$createElement,e=this.slots("left");if(e)return t("div",{ref:"left",class:Ac("left"),on:{click:this.getClickHandler("left",!0)}},[e])},genRightPart:function(){var t=this.$createElement,e=this.slots("right");if(e)return t("div",{ref:"right",class:Ac("right"),on:{click:this.getClickHandler("right",!0)}},[e])}},render:function(){var t=arguments[0],e={transform:"translate3d("+this.offset+"px, 0, 0)",transitionDuration:this.dragging?"0s":".6s"};return t("div",{class:Ac(),on:{click:this.getClickHandler("cell")}},[t("div",{class:Ac("wrapper"),style:e},[this.genLeftPart(),this.slots(),this.genRightPart()])])}}),zc=Object(o.a)("tabbar"),Mc=zc[0],Fc=zc[1],Vc=Mc({mixins:[oi("vanTabbar")],props:{route:Boolean,zIndex:[Number,String],placeholder:Boolean,activeColor:String,inactiveColor:String,value:{type:[Number,String],default:0},border:{type:Boolean,default:!0},fixed:{type:Boolean,default:!0},safeAreaInsetBottom:{type:Boolean,default:null}},data:function(){return{height:null}},computed:{fit:function(){return null!==this.safeAreaInsetBottom?this.safeAreaInsetBottom:this.fixed}},watch:{value:"setActiveItem",children:"setActiveItem"},mounted:function(){this.placeholder&&this.fixed&&(this.height=this.$refs.tabbar.getBoundingClientRect().height)},methods:{setActiveItem:function(){var t=this;this.children.forEach((function(e,i){e.active=(e.name||i)===t.value}))},onChange:function(t){t!==this.value&&(this.$emit("input",t),this.$emit("change",t))},genTabbar:function(){var t;return(0,this.$createElement)("div",{ref:"tabbar",style:{zIndex:this.zIndex},class:[(t={},t[$t]=this.border,t),Fc({unfit:!this.fit,fixed:this.fixed})]},[this.slots()])}},render:function(){var t=arguments[0];return this.placeholder&&this.fixed?t("div",{class:Fc("placeholder"),style:{height:this.height+"px"}},[this.genTabbar()]):this.genTabbar()}}),Rc=Object(o.a)("tabbar-item"),_c=Rc[0],Hc=Rc[1],Wc=_c({mixins:[ri("vanTabbar")],props:n(n({},Zt),{},{dot:Boolean,icon:String,name:[Number,String],info:[Number,String],badge:[Number,String],iconPrefix:String}),data:function(){return{active:!1}},computed:{routeActive:function(){var t=this.to,e=this.$route;if(t&&e){var i=Object(m.d)(t)?t:{path:t},n=i.path===e.path,s=Object(m.b)(i.name)&&i.name===e.name;return n||s}}},methods:{onClick:function(t){this.parent.onChange(this.name||this.index),this.$emit("click",t),Qt(this.$router,this)},genIcon:function(t){var e=this.$createElement,i=this.slots("icon",{active:t});return i||(this.icon?e(st,{attrs:{name:this.icon,classPrefix:this.iconPrefix}}):void 0)}},render:function(){var t=arguments[0],e=this.parent.route?this.routeActive:this.active,i=this.parent[e?"activeColor":"inactiveColor"];return t("div",{class:Hc({active:e}),style:{color:i},on:{click:this.onClick}},[t("div",{class:Hc("icon")},[this.genIcon(e),t(Z,{attrs:{dot:this.dot,info:Object(m.b)(this.badge)?this.badge:this.info}})]),t("div",{class:Hc("text")},[this.slots("default",{active:e})])])}}),qc=Object(o.a)("tree-select"),Yc=qc[0],Uc=qc[1];function Kc(t,e,i,n){var s=e.height,o=e.items,a=e.mainActiveIndex,l=e.activeId,c=(o[+a]||{}).children||[],u=Array.isArray(l);function f(t){return u?-1!==l.indexOf(t):l===t}var p=o.map((function(e){return t(Ua,{attrs:{dot:e.dot,info:Object(m.b)(e.badge)?e.badge:e.info,title:e.text,disabled:e.disabled},class:[Uc("nav-item"),e.className]})}));return t("div",r()([{class:Uc(),style:{height:Object(U.a)(s)}},h(n)]),[t(Ha,{class:Uc("nav"),attrs:{activeKey:a},on:{change:function(t){d(n,"update:main-active-index",t),d(n,"click-nav",t),d(n,"navclick",t)}}},[p]),t("div",{class:Uc("content")},[i.content?i.content():c.map((function(i){return t("div",{key:i.id,class:["van-ellipsis",Uc("item",{active:f(i.id),disabled:i.disabled})],on:{click:function(){if(!i.disabled){var t=i.id;if(u){var s=(t=l.slice()).indexOf(i.id);-1!==s?t.splice(s,1):t.length<e.max&&t.push(i.id)}d(n,"update:active-id",t),d(n,"click-item",i),d(n,"itemclick",i)}}}},[i.text,f(i.id)&&t(st,{attrs:{name:"success"},class:Uc("selected")})])}))])])}Kc.props={max:{type:[Number,String],default:1/0},items:{type:Array,default:function(){return[]}},height:{type:[Number,String],default:300},activeId:{type:[Number,String,Array],default:0},mainActiveIndex:{type:[Number,String],default:0}};var Xc=Yc(Kc),Qc="2.8.5";function Gc(t){[St,ni,Ii,Ut,$e,qi,tn,se,on,ln,dn,yn,wn,$n,Dn,Mn,Wn,Qn,es,ls,ms,Rs,Qs,Le,er,rr,ur,vr,le,Sr,Cr,Br,Dr,Pr,Vr,st,Xi,no,ao,ho,Z,yo,mt,So.a,Co,Bo,zo,Uo,$,Jo,sa,ca,_t,lt,fa,ya,bi,ui,wa,$a,Na,Fa,Ha,Ua,Za,pc,bc,xc,jl,Tc,Bs,Dc,Wr,Pc,Kr,Ye,Ge,ys,Vc,Wc,Ps,mi,ke,Xc,ql].forEach((function(e){e.install?t.use(e):e.name&&t.component(e.name,e)}))}"undefined"!=typeof window&&window.Vue&&Gc(window.Vue);e.default={install:Gc,version:Qc}}])}));