let host = ''
var location_host = window.location.host
if (location_host === 'patient-h5.zz-med-test.com') {
  host = 'https://patient-api.zz-med-test.com'
} else if (location_host === 'patient-h5.zz-med-stg.com') {
  host = 'https://patient-api.zz-med-stg.com'
} else {
  host = 'https://patient-api.zz-med.com'
}
console.log(location_host)
new Vue({
  el: '#raffle-page',
  data: {
    showDigo: false,
    showDigoSuccess: false,
    showDigo1: false,
    showDigo2: false,
    showDigo3: false,
    showDigo4: false,
    duihuanLoading: false,
    game: {
      game_info: {},
      product_info: [],
      user_info: {}
    }, // 游戏活动详情
    productData: {
      page: 1,
      total: 0,
      productList: []// 产品列表
    },
    currentProduct: {}, // 当前商品详情
    userInfo: {
      user_id: '', // 用户id
      point: ''// 用户健康金
    },
    exchageCurrActivity: { // 健康金活动
      act_uuid: '', // id
      name: '' // 名称
    },
    lotteryGameActivity: { // 当前游戏活动
      act_uuid: '', // id
      name: '' // 名称
    },
    act_info: {}, // 当前活动详情
    lastPrizeUser: [], // 小喇叭
    // isFlagShowFreeTx:true, //提示领取次数已用完
    lucky: {
      startBtn: false,
      isLuck: true,
      gxdigo: false,
      currentLuckPro: {},
      showLuck: false,
      current: -1,
      awards: [ // 奖品数组
      ],
      speed: 200, // 速度
      diff: 15, // 速度增加的值
      award: {}, // 抽中的奖品
      time: 0 // 记录开始抽奖时的时间
    }
  },
  computed: {
    diffDay: function () {
      return dayjs(this.act_info.validity_end).diff(dayjs(new Date()), 'day') + 1
    }
  },
  mounted() {
    // Vue.http.headers.common['Authorization'] = "bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************.uBCNYNgG3rMp1swJwYsS9efsBn5whVUfUwEuA4wM-sk";
    // 获取token;
    if (utils_prox.getQueryString('authorization')) {
      var token = 'bearer ' + utils_prox.getQueryString('authorization')
      var info = {
        zz_app_id: utils_prox.getQueryString('appId'),
        zz_app_name: utils_prox.getQueryString('appName'),
        zz_app_version: utils_prox.getQueryString('appVersion')
      }
    }
    localStorage.setItem('appInfo', JSON.stringify(info))
    utils_prox.setCookie('token', token)

    Vue.http.headers.common['Authorization'] = utils_prox.getCookie('token')
    if (localStorage.getItem('appInfo')) {
      Vue.http.headers.common['X-ZZ-APP-INFO'] = localStorage.getItem('appInfo')
    }
    // Vue.http.headers.common['Authorization'] =token;
    // Vue.http.headers.common['X-ZZ-APP-INFO'] =JSON.stringify(info);
    // 去除地址栏参数

    // 获取当前活动
    this.getCurrentActivity()
    // * 获取健康金
    this.getUserPoint()
    var _this = this
    // 判断滚动到底部
    window.onscroll = function () {
      // 变量scrollTop是滚动条滚动时，距离顶部的距离
      var scrollTop = document.documentElement.scrollTop || document.body.scrollTop
      // 变量windowHeight是可视区的高度
      var windowHeight = document.documentElement.clientHeight || document.body.clientHeight
      // 变量scrollHeight是滚动条的总高度
      var scrollHeight = document.documentElement.scrollHeight || document.body.scrollHeight

      // 滚动条到底部的条件
      if ((scrollTop + windowHeight + 100) > scrollHeight) {
        // 到了这个就可以进行业务逻辑加载后台数据了
        if (_this.productData.total > _this.productData.productList.length) {
          _this.productData.page++
          _this.getExchangeActivityProductList()
        }
      }
    }
  },
  methods: {
    showLuckDigo: function () {
      var _this = this
      if (_this.game.user_info.user_residue_degree == 0) {
        _this.showDigo1 = true

        return
      }
      _this.lucky.showLuck = true
    },
    handleDuiSuccess: function () {
      this.showDigoSuccess = false
      window.location.href = './my_prizes.html'
    },
    goLucky: function () {
      this.showDigo4 = false
      this.lucky.showLuck = true
    },
    handleFinallyDuihuan: function () {
      var _this = this
      _this.duihuanLoading = true
      this.$http.post(host + '/api/activity/v1/exchangeActivityProduct', {
        act_product_uuid: _this.currentProduct.act_product_uuid
      }, { emulateJSON: true }).then((response) => {
        var res = response.data
        if (res.status == 0) {
          this.showDigo = false
          this.showDigoSuccess = true
          // 更新健康金
          _this.getUserPoint()
          _this.getExchangeActivityProductList()
        } else {
          vant.Toast(res.msg)
        }
        _this.duihuanLoading = false
      })
    },
    handleConfirmDuihuan(item) {
      this.currentProduct = item
      //TODO if (this.userInfo.point < this.currentProduct.p_current_price) {
      //   this.showDigo4 = true
      //   return
      // }
      this.showDigo = true
    },
    // 当前活动
    getCurrentActivity: function () {
      var _this = this
      this.$http.post(host + '/api/activity/v1/getCurrentActivity').then((response) => {
        var res = response.data
        if (res.status == 0) {
          _this.exchageCurrActivity = res.data.exchageCurrActivity
          _this.lotteryGameActivity = res.data.lotteryGameActivity
          /*
            获取当前商品
          */
          _this.getExchangeActivityProductList()
          _this.getExchangeActivityDetail()
          // 小喇叭通告
          _this.getLornLastPrize()
          _this.getGameDetal()
        } else {
          vant.Toast(res.msg)
        }
      })
    },
    // 当前活动详情
    getExchangeActivityDetail: function () {
      var _this = this
      this.$http.post(host + '/api/activity/v1/getExchangeActivityDetail', {
        uuid: _this.exchageCurrActivity.act_uuid
      }, { emulateJSON: true }).then((response) => {
        var res = response.data
        if (res.status == 0) {
          var data = res.data
          _this.act_info = data.act_info
        } else {
          vant.Toast(res.msg)
        }
      })
    },
    // 当前游戏活动详情
    getGameDetal: function () {
      var _this = this

      this.$http.post(host + '/api/activity/v1/getGameDetail', {
        uuid: _this.lotteryGameActivity.act_uuid
      }, { emulateJSON: true }).then((response) => {
        var res = response.data
        if (res.status == 0) {
          _this.game = res.data

          _this.lucky.awards = res.data.product_info

          //  _this.isFlagShowFreeTx=_this.game.user_info.user_residue_free_degree==0?false:true;
        } else {
          vant.Toast(res.msg)
        }
      })
    },
    // 获取当前活动的商品列表
    getExchangeActivityProductList() {
      var _this = this
      this.$http.post(host + '/api/activity/v1/getExchangeActivityProductList', {
        uuid: _this.exchageCurrActivity.act_uuid,
        page: _this.productData.page,
        pageSize: 10
      }, { emulateJSON: true }).then((response) => {
        var res = response.data
        if (res.status == 0) {
          res.data.list.forEach(function (item) {
            _this.productData.productList.push(item)
          })

          _this.productData.total = res.data.total
        } else {
          vant.Toast(res.msg)
        }
      })
    },
    // 获取健康金
    getUserPoint: function () {
      var _this = this
      this.$http.post(host + '/api/point/v1/getUserPoint').then((response) => {
        var res = response.data
        if (res.status == 0) {
          _this.userInfo.user_id = res.data.info.user_id
          _this.userInfo.point = res.data.info.point
        } else {
          vant.Toast(res.msg)
        }
      })
    },

    getLornLastPrize: function () {
      var _this = this
      this.$http.post(host + '/api/activity/v1/getLornLastPrize', { uuids: [{ uuid: _this.exchageCurrActivity.act_uuid }, { uuid: _this.lotteryGameActivity.act_uuid }] }).then((response) => {
        var res = response.data
        if (res.status == 0) {
          _this.lastPrizeUser = res.data.lastPrizeUser
        } else {
          vant.Toast(res.msg)
        }
      })
    },
    formatActive: function (type) {
      // 1兑换活动 2九宫格活动
      switch (type) {
        case 1:
          return '兑换获得'
          break
        case 2:
          return '抽奖获得'
          break
      }
    },
    handleDuihuan: function () {
      this.showDigo = true
    },
    start() {
      // 开始抽奖
      this.drawAward()
      this.lucky.time = Date.now()
      this.lucky.speed = 200
      this.lucky.diff = 15
    },
    drawAward() {
      // 请求接口, 这里我就模拟请求后的数据(请求时间为2s)
      var _this = this
      if (_this.game.user_info.user_residue_degree == 0) {
        _this.showDigo1 = true
        return
      }
      if (_this.game.user_info.user_residue_free_degree < 1 && _this.game.user_info.user_point < _this.game.game_info.rule_limit_type_no_free_point) {
        _this.showDigo2 = true
        return
      }

      _this.lucky.startBtn = true
      // 去抽奖
      this.$http.post(host + '/api/activity/v1/lottery', { uuid: _this.lotteryGameActivity.act_uuid }, { emulateJSON: true }).then((response) => {
        var res = response.data
        _this.game.user_info.user_residue_free_degree-- // 此人免费次数
        _this.game.user_info.user_point = _this.game.user_info.user_point - _this.game.game_info.rule_limit_type_no_free_point // 总的健康金比较
        _this.game.user_info.user_residue_degree-- // 今日抽奖的次数
        // 更新当前健康金
        _this.getUserPoint()

        if (res.status == 0) {
          // _this.isFlagShowFreeTx=_this.game.user_info.user_residue_free_degree<1?false:true;
          _this.lucky.award = res.data.product_info
        } else {
          // vant.Toast(res.msg);
          _this.lucky.award = {
            act_product_id: -1
          }
        }
        this.move()
      })
    },
    move() {
      window.timeout = setTimeout(() => {
        this.lucky.current++
        if (this.lucky.current > 7) {
          this.lucky.current = 0
        }

        // 若抽中的奖品id存在，则开始减速转动
        if (this.lucky.award.act_product_id && (Date.now() - this.lucky.time) / 1000 > 2) {
          this.lucky.speed += this.lucky.diff // 转动减速
          // 没有中奖
          if ((Date.now() - this.lucky.time) / 1000 > 4 && this.lucky.award.act_product_id == -1) {
            clearTimeout(window.timeout)
            this.showDigo3 = true
            this.lucky.current = -1
            this.lucky.startBtn = false
            return
          }
          // 若转动时间超过4秒，并且奖品id等于小格子的奖品id，则停下来！
          if ((Date.now() - this.lucky.time) / 1000 > 4 && this.lucky.award.act_product_id == this.lucky.awards[this.lucky.current].id) {
            clearTimeout(window.timeout)
            this.lucky.startBtn = false
            // 获奖产品
            this.lucky.currentLuckPro = this.lucky.awards.find((item) => {
              return item.act_product_uuid == this.lucky.award.act_product_uuid
            })
            setTimeout(() => {
              this.lucky.gxdigo = true
            }, 1000)

            return
          }

          // 若抽中的奖品不存在，则加速转动
        } else {
          this.lucky.speed -= this.lucky.diff // 转动加速
        }

        this.move()
      }, this.lucky.speed)
    }
  }
})
