// 工具函数
(function (window) {
    var Utils = function (html) {

    };

    Utils.prototype = {
        //获取地址栏参数
        getQueryString: function (name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) {
                return unescape(r[2]);
            }
            return null;
        },
        //设置cookie
        setCookie: function (name, value) {
            if (value) {
                var days = 1; //定义一天
                var exp = new Date();
                exp.setTime(exp.getTime() + days * 24 * 60 * 60 * 1000);
                // 写入Cookie, toGMTString将时间转换成字符串
                document.cookie = name + "=" + escape(value) + ";expires=" + exp.toGMTString;
            }
        },
        /**
           * cookie中取值
           * */
        getCookie: function (name) {
            var arr, reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)"); //匹配字段
            if (arr = document.cookie.match(reg)) {
                return unescape(arr[2]);
            } else {
                return null;
            }
        },
        delCookie: function (name) {
            var exp = new Date();
            exp.setTime(exp.getTime() - 1);
            var cval = setCookie(name);
            if (cval && cval != null) {
                document.cookie = name + "=" + cval + ";expires=" + exp.toGMTString()
            }
        },
        /**
            * 清除全部cookie值
            * */

        clearCookie: function () {
            var keys = document.cookie.match(/[^ =;]+(?=\=)/g);
            if (keys) {
                for (var i = keys.length; i--;) {
                    // document.cookie = keys[i] +'=0;expires=' + new Date( 0).toUTCString()
                    document.cookie = keys[i] + '=0;expires=' + new Date(0).toUTCString() + ";path=/video_learning" + ";domain=localhost";
                }
            }
        }
    }
    //接下来引入代理类 proxySingletonCreateDiv:
    var proxySingleUtils = (function () {
        var instance;
        return function (html) {
            if (!instance) {
                instance = new Utils(html);
            }
            return instance;
        }
    })()
    window.utils_prox = new Utils();
})(window)

