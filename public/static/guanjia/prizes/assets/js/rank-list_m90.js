let host = ''
var location_host = window.location.host
if (location_host === 'patient-h5.zz-med-test.com') {
  host = 'https://patient-api.zz-med-test.com'
} else if (location_host === 'patient-h5.zz-med-stg.com') {
  host = 'https://patient-api.zz-med-stg.com'
} else {
  host = 'https://patient-api.zz-med.com'
}
console.log(location_host)
new Vue({
  el: '#rank-list-page',
  data: {
    currentInfo: {
      name: '任务',
      ranking_range: {
        start_at: '',
        end_at: ''
      }
    },
    dataInfo: {},
    dataInfo_2: {},
    dataInfo_3: {},
    dataInfo_4: {},
    userName: '章三', // 姓名
    user_img: 'https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1591957792719&di=cb1ebe0dd1d9c12a6496ed25a127a8c2&imgtype=0&src=http%3A%2F%2Fa0.att.hudong.com%2F16%2F12%2F01300535031999137270128786964.jpg', // 头像
    taskList: [
    ]
  },
  computed: {
    dayformat() {
      // 2020年5月21日-27日
      if (this.currentInfo.ranking_range.start_at !== '') {
        return `${dayjs(this.currentInfo.ranking_range.start_at).format('M月D日')}-${dayjs(this.currentInfo.ranking_range.end_at).format('D日')}`
      }
    }
  },
  mounted() {
    // Vue.http.headers.common['Authorization'] = "bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************.uBCNYNgG3rMp1swJwYsS9efsBn5whVUfUwEuA4wM-sk";
    //  Vue.http.headers.common['Authorization'] = utils_prox.getCookie('token');
    //     if(localStorage.getItem('appInfo')){
    //                               Vue.http.headers.common['X-ZZ-APP-INFO'] = localStorage.getItem('appInfo');
    //    }
    // var token= 'bearer '+utils_prox.getQueryString('authorization');
    // var info={
    //   zz_app_id:utils_prox.getQueryString('appId'),
    //   zz_app_name:utils_prox.getQueryString('appName'),
    //   zz_app_version:utils_prox.getQueryString('appVersion'),
    // }

    // Vue.http.headers.common['Authorization'] =token;
    // Vue.http.headers.common['X-ZZ-APP-INFO'] =JSON.stringify(info);
    // localStorage.setItem('appInfo',JSON.stringify(info));
    // utils_prox.setCookie('token',token)
    // 获取token;
    if (utils_prox.getQueryString('authorization')) {
      var token = 'bearer ' + utils_prox.getQueryString('authorization')
      var info = {
        zz_app_id: utils_prox.getQueryString('appId'),
        zz_app_name: utils_prox.getQueryString('appName'),
        zz_app_version: utils_prox.getQueryString('appVersion')
      }
    }
    localStorage.setItem('appInfo', JSON.stringify(info))
    utils_prox.setCookie('token', token)

    Vue.http.headers.common['Authorization'] = utils_prox.getCookie('token')
    if (localStorage.getItem('appInfo')) {
      Vue.http.headers.common['X-ZZ-APP-INFO'] = localStorage.getItem('appInfo')
    }
    // Vue.http.headers.common['Authorization'] =token;
    // Vue.http.headers.common['X-ZZ-APP-INFO'] =JSON.stringify(info);

    this.getPointRanking()
    this.getStepsRanking()
    this.getAnswerRanking()
    this.getFoodRanking()
  },
  methods: {
    changeTab(index) {
      var _this = this
      switch (index) {
        case 0:
          _this.currentInfo.user_user_headimgurl = _this.dataInfo.user_user_headimgurl
          _this.currentInfo.user_ranking = _this.dataInfo.user_ranking
          _this.currentInfo.user_user_nick_name = _this.dataInfo.user_user_nick_name
          _this.currentInfo.ranking_range = _this.dataInfo.ranking_range
          _this.currentInfo.name = '任务'
          break
        case 1:
          _this.currentInfo.user_user_headimgurl = _this.dataInfo_2.user_user_headimgurl
          _this.currentInfo.user_ranking = _this.dataInfo_2.user_ranking
          _this.currentInfo.user_user_nick_name = _this.dataInfo_2.user_user_nick_name
          _this.currentInfo.ranking_range = _this.dataInfo_2.ranking_range
          _this.currentInfo.name = '步数'
          break
        case 2:
          _this.currentInfo.user_user_headimgurl = _this.dataInfo_3.user_user_headimgurl
          _this.currentInfo.user_ranking = _this.dataInfo_3.user_ranking
          _this.currentInfo.user_user_nick_name = _this.dataInfo_3.user_user_nick_name
          _this.currentInfo.ranking_range = _this.dataInfo_3.ranking_range
          _this.currentInfo.name = '答题'
          break
        case 3:
          _this.currentInfo.user_user_headimgurl = _this.dataInfo_3.user_user_headimgurl
          _this.currentInfo.user_ranking = _this.dataInfo_3.user_ranking
          _this.currentInfo.user_user_nick_name = _this.dataInfo_3.user_user_nick_name
          _this.currentInfo.ranking_range = _this.dataInfo_3.ranking_range
          _this.currentInfo.name = '饮食'
          break
      }
    },
    initRankImg_1() {
      this.dataInfo.ranking_list = this.dataInfo.ranking_list.map(item => {
        switch (item.row) {
          case '1': item.rankImg = './assets/images/icon_no1.png'; break
          case '2': item.rankImg = './assets/images/icon_no2.png'; break
          case '3': item.rankImg = './assets/images/icon_no3.png'; break
          default:item.rankImg = null; break
        }
        return item
      })
    },
    initRankImg_2() {
      this.dataInfo_2.ranking_list = this.dataInfo_2.ranking_list.map(item => {
        switch (item.row) {
          case '1': item.rankImg = './assets/images/icon_no1.png'; break
          case '2': item.rankImg = './assets/images/icon_no2.png'; break
          case '3': item.rankImg = './assets/images/icon_no3.png'; break
          default:item.rankImg = null; break
        }
        return item
      })
    },
    initRankImg_3() {
      this.dataInfo_3.ranking_list = this.dataInfo_3.ranking_list.map(item => {
        switch (item.row) {
          case '1': item.rankImg = './assets/images/icon_no1.png'; break
          case '2': item.rankImg = './assets/images/icon_no2.png'; break
          case '3': item.rankImg = './assets/images/icon_no3.png'; break
          default:item.rankImg = null; break
        }
        return item
      })
    },
    initRankImg_4() {
      this.dataInfo_4.ranking_list = this.dataInfo_4.ranking_list.map(item => {
        switch (item.row) {
          case '1': item.rankImg = './assets/images/icon_no1.png'; break
          case '2': item.rankImg = './assets/images/icon_no2.png'; break
          case '3': item.rankImg = './assets/images/icon_no3.png'; break
          default:item.rankImg = null; break
        }
        return item
      })
    },
    getPointRanking: function() {
      var _this = this
      this.$http.post(host + '/api/activity/v1/ranking/getPointRanking').then((response) => {
        var res = response.data
        if (res.status == 0) {
          _this.dataInfo = res.data
          _this.currentInfo.user_user_headimgurl = res.data.user_user_headimgurl
          _this.currentInfo.user_ranking = res.data.user_ranking
          _this.currentInfo.user_user_nick_name = res.data.user_user_nick_name
          _this.currentInfo.ranking_range = res.data.ranking_range
          this.initRankImg_1()
        } else {
          vant.Toast(res.msg)
        }
      })
    },
    getStepsRanking: function() {
      var _this = this
      this.$http.post(host + '/api/activity/v1/ranking/getStepsRanking').then((response) => {
        var res = response.data
        if (res.status == 0) {
          _this.dataInfo_2 = res.data
          this.initRankImg_2()
        } else {
          vant.Toast(res.msg)
        }
      })
    },
    getAnswerRanking: function() {
      var _this = this
      this.$http.post(host + '/api/activity/v1/ranking/getAnswerRanking').then((response) => {
        var res = response.data
        if (res.status == 0) {
          _this.dataInfo_3 = res.data
          this.initRankImg_3()
        } else {
          vant.Toast(res.msg)
        }
      })
    },
    getFoodRanking: function() {
      var _this = this
      this.$http.post(host + '/api/activity/v1/ranking/getFoodRanking').then((response) => {
        var res = response.data
        if (res.status == 0) {
          _this.dataInfo_4 = res.data
          this.initRankImg_4()
        } else {
          vant.Toast(res.msg)
        }
      })
    }
  }
})
