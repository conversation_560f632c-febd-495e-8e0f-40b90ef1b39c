let host = ''
var location_host = window.location.host
if (location_host === 'patient-h5.zz-med-test.com') {
  host = 'https://patient-api.zz-med-test.com'
} else if (location_host === 'patient-h5.zz-med-stg.com') {
  host = 'https://patient-api.zz-med-stg.com'
} else {
  host = 'https://patient-api.zz-med.com'
}
console.log(location_host)
new Vue({
  el: '#raffle-page',
  data: {
    noData: false,
    showDigo: false,
    address_info: {}, // 回调信息
    areaList: [{values: []}, {values: []}, {values: []}],
    showDigoSuccess: false,
    showArea: false,
    listData: {
      page: 1,
      data: [],
      total: 0
    },
    selectItem: {},
    form: {
      area: '',
      address: '',
      districtId: '',
      cityId: '',
      provinceId: '',
      name: '',
      cell: ''
    },
    location: []
  },
  mounted: function () {
    //  Vue.http.headers.common['Authorization'] = "bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************.uBCNYNgG3rMp1swJwYsS9efsBn5whVUfUwEuA4wM-sk";
    Vue.http.headers.common['Authorization'] = utils_prox.getCookie('token')
    if (localStorage.getItem('appInfo')) {
      Vue.http.headers.common['X-ZZ-APP-INFO'] = localStorage.getItem('appInfo')
    }
    this.getSelfActivityPrizeList()
    this.getUserDefaultAddress()
    // 获取省
    this.getProvince()
    var _this = this
    // 判断滚动到底部
    window.onscroll = function () {
      // 变量scrollTop是滚动条滚动时，距离顶部的距离
      var scrollTop = document.documentElement.scrollTop || document.body.scrollTop
      // 变量windowHeight是可视区的高度
      var windowHeight = document.documentElement.clientHeight || document.body.clientHeight
      // 变量scrollHeight是滚动条的总高度
      var scrollHeight = document.documentElement.scrollHeight || document.body.scrollHeight

      // 滚动条到底部的条件
      if ((scrollTop + windowHeight + 100) > scrollHeight) {
        // 到了这个就可以进行业务逻辑加载后台数据了
        if (_this.listData.total > _this.listData.data.length) {
          _this.listData.page++
          _this.getSelfActivityPrizeList()
        }
      }
    }
  },
  methods: {
    getUserDefaultAddress: function () {
      var _this = this
      this.$http.post(host + '/api/activity/v1/getUserDefaultAddress').then((response) => {
        var res = response.data
        if (res.status == 0) {
          if (!res.data.address_info.cell) {
            return
          }
          _this.address_info = res.data.address_info
          _this.form.area = _this.address_info.provinceName + '/' + _this.address_info.cityName + '/' + _this.address_info.districtName
          _this.form.cell = _this.address_info.cell
          _this.form.name = _this.address_info.name
          _this.form.address = _this.address_info.address
          _this.form.provinceId = _this.address_info.provinceId
          _this.form.cityId = _this.address_info.cityId
          _this.form.districtId = _this.address_info.districtId
        } else {
          vant.Toast(res.msg)
        }
      })
    },
    handleSecondconfirm: function (params) {
      this.showDigoSuccess = false
      this.showDigo = false
      this.listData.page = 1
      this.listData.data = []
      this.getSelfActivityPrizeList()
    },
    // 获取当前列表
    getSelfActivityPrizeList: function () {
      var _this = this
      this.$http.post(host + '/api/activity/v1/getSelfActivityPrizeList', {
        page: _this.listData.page,
        pageSize: 10
      }).then((response) => {
        var res = response.data
        if (res.status == 0) {
          res.data.list.forEach(function (item) {
            _this.listData.data.push(item)
          })
          _this.listData.total = res.data.total
          // _this.listData.total=0;

          if (_this.listData.total < 1) {
            _this.noData = true
          }
        } else {
          vant.Toast(res.msg)
        }
      })
    },
    onAreaChange: function (picker, values, index) {
      // console.log(values)
      // console.log(index)
      if (index == 0) {
        this.getCity(values[0].id)
        this.areaList[2].values = []
      }
      if (index == 1) {
        this.district(values[1].id)
      }
    },
    onCancel: function () {
      this.showArea = false
    },
    onAreaConfirm: function (values) {
      if (values[0] && values[0].id == -1) {
        vant.Dialog.alert({
          title: '提示',
          message: '请选择省份'
        })
        return
      }
      ;
      if (!values[1] || values[1].id == -1) {
        vant.Dialog.alert({
          title: '提示',
          message: '请选择城市'
        })
        return
      }
      if (!values[2] || values[2].id == -1) {
        vant.Dialog.alert({
          title: '提示',
          message: '请选择地区'
        })
        return
      }

      this.location = values
      this.form.provinceId = values[0].id
      this.form.cityId = values[1].id
      this.form.districtId = values[2].id

      this.form.area = values.map((item) => {
        return item.name
      }).join('/')
      this.showArea = false
    },
    getProvince: function () {
      var _this = this
      this.$http.post(host + '/api/v1/location/province').then((response) => {
        var res = response.data
        if (res.status == 0) {
          res.data.unshift({id: -1, name: '请选择'})
          this.areaList[0].values = res.data
        } else {
          vant.Toast(res.msg)
        }
      })
    },
    getCity: function (id) {
      var _this = this
      this.$http.post(host + '/api/v1/location/city', {province_id: id}, {emulateJSON: true}).then((response) => {
        var res = response.data
        if (res.status == 0) {
          res.data.unshift({id: -1, name: '请选择'})
          this.areaList[1].values = res.data
        } else {
          vant.Toast(res.msg)
        }
      })
    },
    district: function (id) {
      var _this = this
      this.$http.post(host + '/api/v1/location/district', {city_id: id}, {emulateJSON: true}).then((response) => {
        var res = response.data
        if (res.status == 0) {
          res.data.unshift({id: -1, name: '请选择'})
          this.areaList[2].values = res.data
        } else {
          vant.Toast(res.msg)
        }
      })
    },
    goLinQu: function (item) {
      var _this = this
      this.$http.post(host + '/api/activity/v1/getSelfThirdActivityPrizeUrl', {
        id: item.id
      }, {emulateJSON: true}).then((response) => {
        var data = response.data
        if (data.status == 0) {
          window.location.href = data.data.prize_info.third_prize_url

          _this.listData.page = 1
          _this.listData.data = []
          _this.getSelfActivityPrizeList()
          // console.log(data.data.prize_info.third_prize_url)
        } else {
          vant.Toast(data.msg)
        }
      })
    },
    handleSqdj(item) {
      this.selectItem = item
      this.showDigo = true
    },
    onSubmit: function () {
      if (this.form.provinceId == '') {
        vant.Dialog.alert({
          title: '提示',
          message: '请选择省市区'
        })
        return
      }
      if (!this.form.address) {
        vant.Dialog.alert({
          title: '提示',
          message: '请填写详细地址'
        })
        return
      }
      if (!this.form.name) {
        vant.Dialog.alert({
          title: '提示',
          message: '请输入收货人姓名'
        })
        return
      }
      if (!this.form.cell) {
        vant.Dialog.alert({
          title: '标题',
          message: '请输入手机号'
        })
        return
      }
      if (!/^1(3\d|4\d|5\d|6\d|7\d|8\d|9\d)\d{8}$/g.test(this.form.cell)) {
        vant.Dialog.alert({
          title: '提示',
          message: '请输入正确的手机号'
        })
        return
      }
      var _this = this
      this.$http.post(host + '/api/activity/v1/saveProductShippingAddress', {
        prize_id: _this.selectItem.id,
        province_id: _this.form.provinceId,
        city_id: _this.form.cityId,
        district_id: _this.form.districtId,
        address: _this.form.address,
        name: _this.form.name,
        cell: _this.form.cell
      }, {emulateJSON: true}).then((response) => {
        var data = response.data
        if (data.status == 0) {
          _this.showDigoSuccess = true
          _this.getUserDefaultAddress()
          _this.listData.page = 1
          _this.listData.data = []
          _this.getSelfActivityPrizeList()
        } else {
          vant.Toast(data.msg)
        }
      })
    },

    formatActive: function (type) {
      // 1兑换活动 2九宫格活动
      switch (type) {
        case 1:
          return '兑换获得'
          break
        case 2:
          return '抽奖获得'
          break
      }
    }
  }

})
