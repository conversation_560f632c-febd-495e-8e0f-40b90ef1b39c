let host = ''
var location_host = window.location.host
if (location_host === 'patient-h5.zz-med-test.com') {
  host = 'https://patient-api.zz-med-test.com'
} else if (location_host === 'patient-h5.zz-med-stg.com') {
  host = 'https://patient-api.zz-med-stg.com'
} else {
  host = 'https://patient-api.zz-med.com'
}

new Vue({
  el: '#pricezs-detail',
  data: {
    balance: '',
    detailList: []
  },
  mounted() {
    // 获取token;
    if (utils_prox.getQueryString('authorization')) {
      var token = 'bearer ' + utils_prox.getQueryString('authorization')
      var info = {
        zz_app_id: utils_prox.getQueryString('appId'),
        zz_app_name: utils_prox.getQueryString('appName'),
        zz_app_version: utils_prox.getQueryString('appVersion')
      }
    }
    localStorage.setItem('appInfo', JSON.stringify(info))
    utils_prox.setCookie('token', token)

    Vue.http.headers.common['Authorization'] = utils_prox.getCookie('token')
    if (localStorage.getItem('appInfo')) {
      Vue.http.headers.common['X-ZZ-APP-INFO'] = localStorage.getItem('appInfo')
    }
    // 去除地址栏参数
    var url = window.location.href // 获取当前页面的url
    if (url.indexOf('?') != -1) { // 判断是否存在参数
      url = url.replace(/(\?|#)[^'"]*/, '') // 去除参数
      window.history.pushState({}, 0, url)
    }

    // 获取健康金-明细列表
    this.getAccountList()
  },
  methods: {
    // 获取健康金-明细列表
    getAccountList() {
      this.$http.get(host + '/api/activity/v1/user/account/list', {
        params: {
          page: 1,
          per_page: 10000
        }
      }).then((response) => {
        var res = response.data
        if (res.status == 0) {
          const data = res.data || {}
          this.balance = data.balance
          this.detailList = data.items
        } else {
          vant.Toast(res.msg)
        }
      })
    },
  }
})
