body,html{
  padding: 0;
  margin: 0;
  min-height: 100%;
  background: #F5F6FA;
}
p{
  padding: 0;
  margin: 0;
}
.header {
  width: 100%;
  height: 269px;
  position: relative;
  z-index: 0;
}
.header-bg {
  width: 100%;
  line-height: 0;
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
}
.header-bg-img {
  width: 100%;
}
.header-up {
  text-align: right;
  font-size: 15px;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 15px;
  padding: 23px 20px 0 0;
}
.header-up a {
  color: #FFFFFF;
}
.header .one {
  text-align: center;
  font-size: 0.8rem;
  margin: 0.78rem auto;
  margin-bottom: 0.7rem;
  font-weight: 700;
  color: transparent;
  background-clip:text;
  -webkit-background-clip: text;
  background-image: linear-gradient(135deg,#fedcd1,#fff);
}
.header-title {
  width: 350px;
  line-height: 0;
  margin: 0 auto;
}
.header-title img {
  max-width: 100%;
}
.header-notice {
  height: 34px;
  color: #fff;
  margin: 0 auto;
  background-image: url("./../images/bobao.png");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}
.notice-swipe {
  width: 100%;
  height: 34px;
  font-size: 14px;
  line-height: 34px;
  text-align: center;
}
.header-info {
  width: 6.7rem;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin: 18px auto 0;
}
.header-info-icon {
  width: 55px;
}
.header-info-right {
  flex: 1;
  margin-left: 6px;
}
.header-info-text {
  font-size: 16px;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 22px;
}
.header-info-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.info-box-left {
  display: flex;
  line-height: 25px;
  align-items: baseline;
}
.header-info-num {
  font-size: 36px;
  font-weight: 600;
  color: #FFFFFF;
}
.header-info-detail {
  font-size: 15px;
  font-weight: 500;
  color: #FFFFFF;
  line-height: 21px;
  display: flex;
  align-items: center;
  margin-left: 6px;
}
.header-info-prize {
  width: 87px;
  height: 34px;
  line-height: 34px;
  font-size: 16px;
  font-weight: 500;
  text-align: center;
  color: #FFFFFF;
  border-radius: 19px;
  border: 1px solid #FFFFFF;
}
.header-turntable {
  width: 6.7rem;
  height: 95px;
  box-sizing: border-box;
  padding: 0 20px;
  border-radius: 9px;
  background: #FFFFFF;
  box-shadow: 0px 2px 10px 0px rgba(0,0,0,0.05);
  position: absolute;
  bottom: -63px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
}
.header-turntable-icon {
  width: 103px;
}
.header-turntable-box {
  flex: 1;
  margin-left: 14px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.header-turntable-mid {
  flex: 1;
}
.header-turntable-text1 {
  font-size: 18px;
  font-weight: 600;
  color: #333333;
  line-height: 25px;
}
.header-turntable-text2 {
  font-size: 16px;
  font-weight: 400;
  color: #666666;
  line-height: 22px;
  margin-top: 3px;
}
.header-turntable-arrow {
  font-size: 12px;
  color: #cccccc;
}
/* 成品 */
  .products .item {
    width: 6.7rem;
    background: #fff;
    box-sizing: border-box;
    margin: 0 auto;
    padding: 0.29rem 0.35rem;
    border-radius:0.26rem ;
  }
  .pto{
    width: 1.65rem;
  }
  .pic{
    height: 1.5rem;
    overflow: hidden;
  }
  .mgb10{
    margin-bottom: 0.2rem !important;
  }
  .pro-title{
    font-size: 0.24rem;
    color: #FFFFFF;
    letter-spacing: 0;
    text-align: center;
    background: url(./../images/column_title_bg.png) no-repeat;
    background-size: contain;
    width: 5rem;
    margin: 0 auto;
    height: 0.6rem;
    line-height: 0.6rem;
       position: relative;
     top: 0.17rem;
    margin-top: 0.2rem;
  }
  .des p:nth-child(1){

      font-size: 0.28rem;
      margin-top: 0.1rem;
      color: #333333;
      line-height: 0.42rem;
  }
   .des p:nth-child(2){

     font-size: 0.24rem;
      color: #999;

       line-height: 0.42rem;
  }
   .des p:nth-child(2) span{
     color: #333333;
   }
    .des p:nth-child(3) {
      font-size: 0.24rem;
      color: #333333;
      line-height: 0.42rem;
    }
     .des p:nth-child(3) img{
      width: 0.3rem;
      vertical-align: sub;
      margin-right: 0.1rem;
    }
       .des p:nth-child(3) span{
      font-size: 0.28rem;
      color:  #EF001D;
    }
     .btn-content{
       text-align: right;
     }
    .btn-content img{
      height: 0.68rem;
      vertical-align: middle;

    }
    .content.item{
      border-radius: 0;
     border-bottom: 1px solid #E8E8E8;
    }
    .list{
      border-radius: 0.26rem;
      width: 6.7rem;
      margin: 0 auto;
      overflow: hidden;
      margin-top: 78px;
      margin-bottom: 1rem;
    }
    .list-tips {
      height: 40px;
      padding: 0 16px;
      background: #ffffff;
      border-bottom: 1px solid #E8E8E8;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .list-tips-text1 {
      font-size: 18px;
      font-weight: 400;
      color: #333333;
    }
    .list-tips-text2 {
      font-size: 15px;
      font-weight: 400;
      color: #999999;
    }
    .custDigo{
      background: #FFFFFF;
      border-radius: 0.26rem;
      border-radius: 0.26rem;
      margin: 0 auto;
      background: #fff;
      width: 5.73rem;
      font-size: 0.28rem;
     margin-top: 1.2rem;
      overflow: hidden;
      padding: 0.53rem 0.44rem;
    }
    .duihuan .product{
      width: 100%;
      height: 4.8rem;
      overflow: hidden;
      border-radius: 0.26rem;
      border: 1px solid #eee;
      margin-bottom: 0.25rem;
    }
     .duihuan .product img{
       width: 100%;
     }

       .duihuan p:nth-of-type(1){
    margin-top: 0.3rem;
    }
    .duihuan p{
      font-size: 0.28rem;
      color: #000000;
      letter-spacing: 0;
      text-align: left;
      line-height:  0.28rem;
      margin-bottom: 0.25rem;
    }
      .duihuan .last{

      margin-top: 0.5rem;
      font-size: 0.31rem;
      color: #333;
      font-weight:500 ;
      line-height: 0.42rem;
    }
    .duihuan .btn{
      width: 100%;
      height: 0.9rem;
    }
    .confirm{
      margin:0.58rem auto;
      display: block;

    }

    /* //lucky */

 #rotary-table {
        width: 6.7rem;
        height: 6.7rem;
        position: relative;
        top: 1.82rem;
        margin: auto;
        padding: 0.5rem;
        box-sizing: border-box;
        background: url(./../images/draw_bg.png) no-repeat;
        background-size: cover;

    }
 #rotary-table .award {
            width: 1.8rem;
            height: 1.8rem;
            /* line-height: ; */
            text-align: center;
            padding: 0 0.24rem;
            padding-top: 0.24rem;
            box-sizing: border-box;
            float: left;
            position: absolute;
            overflow: hidden;
           background: url(./../images/draw_grid.png) no-repeat;
           background-size: cover;
           overflow: hidden;

 }
  .award img{
    max-width: 100%;
    max-height: 0.93rem;
    display: block;
    margin: 0 auto;
  }
  .award p{
    font-size: 0.24rem;
    color: #000000;
    letter-spacing: 0;
    line-height: 0.24rem;
    margin-top: 0.1rem;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
  }
#rotary-table .award.active {
   background-image:url(./../images/lucky_active.png);
}
.award.award0 {
    top: 0.52rem;
    left:  0.52rem;
}
.award.award1 {
     top: 0.52rem;
   left: 2.46rem;
}
.award.award2 {
    top: 0.52rem;
    right:  0.52rem;
}
.award.award3 {
  top: 2.44rem;
    right:  0.52rem;
}
.award.award4 {
   bottom:  0.52rem;
    right:  0.52rem;
}
.award.award5 {
    bottom:  0.52rem;
   left: 2.46rem;
}
.award.award6 {
    bottom:  0.52rem;
   left:  0.52rem;
}
.award.award7 {
   top: 2.44rem;
    left:  0.52rem;
}
#rotary-table  #start-btn {
    position: absolute;
  top: 2.44rem;
    left: 2.46rem;
    width: 1.8rem;
    height: 1.8rem;
    line-height: 90px;
    text-align: center;
   background: url(./../images/draw_btn.png);
   background-size: cover;

}
#start-btn p{

  font-size: 0.32rem;
color: #E7003D;
letter-spacing: 0;
text-align: center;
line-height: 0.33rem;
}
#start-btn p:first-child{
   /* margin-top: 0.41rem; */
  font-size: 0.37rem;
  color: #E7003D;
  font-weight: bold;
  line-height: 0.52rem;
}
.btn-content-lucky{
  position: relative;
    top: 2rem;
    text-align: center;
}
.gxi{
  background: url(./../images/pop_bg.png) no-repeat;
  height: 8.8rem;
  background-size: 100%;
  color: #fff;
}
.gxi-content{
    width: 4.8rem;
    margin: 0 auto;
    margin-top: 1rem;

}
.gxi-content img{
    display: block;
    border-radius: 0.44rem;
    width: 100%;
}

.gxi-content p{
  font-size: 0.3rem;
  color: #FFFFFF;
  line-height:0.42rem;
  margin-bottom: 0.1rem;
}
.gxi-content p:nth-of-type(1){
  margin-top: 0.3rem;
}
.gxi-content p:nth-of-type(4){
  text-align: center;
  margin-top: 0.3rem;
}
.gxi-content p.lastP{
 margin-top: 75px;
}
