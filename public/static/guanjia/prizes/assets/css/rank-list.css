body,html{
  padding: 0;
  margin: 0;
  height: 100%;
   background-color: #ff9c49;
}
#rank-list-page{
 height: 100%;
}
.top_container{
  display: flex;
  align-items: center;
  padding: 0.42rem;
  padding-bottom: 1rem;
  color: #fff;
}
.van-tab{
  font-size: 0.25rem;
}
.van-tab--active{
  font-weight: normal;
}
.van-tabs__content{
  min-height: calc(100vh - 2.3rem);
}
.van-icon{
  font-size: 60px;
}
.top_container{
   background: linear-gradient(top left, #ff9d51, #ff5340);/*渐变从左上角到右下角*/
    background: -ms-linear-gradient(top left, #ff9d51,  #ff5340);
    background: -webkit-linear-gradient(top left, #ff9d51,  #ff5340);
    background: -moz-linear-gradient(top left, #ff9d51,  #ff5340);
    padding-bottom: 2rem;
}
.user_logo_container{

  border-radius: 50%;
  width: 1.2rem;
  height: 1.2rem;
  overflow: hidden;
  border: 2px solid #fff;

}
.user_logo_container img{
  width: 100%;
}
.user_info_container{
  margin-left: 0.25rem;
  font-size: 0.26rem;

}
.user_info_container .user_name{
  font-size: 0.5rem;
  width:3rem;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.rank_list_header{
  background: url(./../images/bg1.png) no-repeat;
  background-size: cover;
  height: 3.5rem;
  margin-top: -3.5rem;
}

.rank_list_block{
  width: 6.6rem;
 margin-left: 0.44rem;
  background: #fff;
}
.rank_list_block .van-tabs--card>.van-tabs__wrap{

  height: 50Px;
}
.rank_list_block .van-tabs__nav--card{
  border: 0Px;
  background-color: #fdf0de;
  border-radius: 20Px;
  overflow: hidden;
  height: 40Px;
}
.rank_list_block .van-tabs__nav--card .van-tab{
  border: 0Px;
  border-radius: 20Px;
}
table{
  width: 100%;
  margin: 0 auto;
  text-align: center;
  border: none;
  border-collapse: collapse;
  color: #333333;
}
table .header{

  background: #fef3e5;
  font-size: 0.3rem;
  
color: #333333;

}
table th{
   font-weight: normal !important;
}
table tr{
  font-size: 0.32rem;
 
}
table .num{
  color: #666;
 
}
tr td:nth-child(1){
width: 1.25rem;
text-align: center;
}
table td img.rank_img{
  width: 0.56rem;
}
table td img.user_img{
  width: 0.68rem;
  height: 0.68rem;
  border-radius: 50%;
  vertical-align: middle;
  margin-right: 0.15rem;
  margin-left: 0.4rem;
}
table td,table th{
  padding: 8Px 0;
  overflow: hidden;
}
table td .rank_round{
  display: inline-block;
  border: 1Px solid #f59a4a;
  border-radius: 50%;
  font-size: 0.24rem;
  height: 0.48rem;
  width: 0.48rem;
  color: #f59a4a;
  line-height: 0.48rem;
}
table td .user_name_width{
  display: inline-block;
  width: 90Px;
  text-align: left;
  vertical-align: middle;
}
table tr.current{
 
  border-bottom: 10px solid #fef3e5;
  color: #ff762c;
}
table tr.current i{
  font-style: normal;
 font-size: 0.32rem
}
table tr.current td img.user_img{
  border: 1Px solid #f59a4a;
}
[v-cloak]{
            display: none
}