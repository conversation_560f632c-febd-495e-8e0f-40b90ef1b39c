button{
    border: none;
    background: none;
    font-size: 0.32rem;
    text-align: center;
    border-radius: 0.44rem;
   padding: 0.15rem 0.38rem;
   position: relative;
}
button:active::before {
  display: block;
  content: '';
  position: absolute;
    border-radius: 0.44rem;
  top: 0px;
  left: 0px;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 5%)
}
.btn-confirm{
    color: #fff;
    background: -webkit-linear-gradient(right, #F8875C , #FAAF19); /* Safari 5.1 - 6.0 */
    background: -o-linear-gradient(right, #F8875C, #FAAF19); /* Opera 11.1 - 12.0 */
    background: -moz-linear-gradient(right, #F8875C, #FAAF19); /* Firefox 3.6 - 15 */
    background: linear-gradient(right, #F8875C ,#FAAF19 ); /* 标准的语法（必须放在最后） */
}
.btn-primary{
    border: 1px solid #979797;
    font-size: 0.32rem;
    color: #979797;
    letter-spacing: 0;
    text-align: center;
    line-height: 0.34rem;
}
h2{
    padding: 0;
    margin: 0;
}
.btn-confirm-blod{
     color: #fff;
     font-weight: 500;
    background: -webkit-linear-gradient(right, #F8875C , #FAAF19); /* Safari 5.1 - 6.0 */
    background: -o-linear-gradient(right, #F8875C, #FAAF19); /* Opera 11.1 - 12.0 */
    background: -moz-linear-gradient(right, #F8875C, #FAAF19); /* Firefox 3.6 - 15 */
    background: linear-gradient(right, #F8875C ,#FAAF19 ); /* 标准的语法（必须放在最后） */
}
.van-dialog__header{
    font-size: 0.36rem;
    color: #333;
}
.btn-lucky{
    background: #E7003D;
    color: #fff;
    font-size: 0.28rem;
}
.tip{
  padding:0.43rem;
  font-size: 0.32rem;
color: #666666;
line-height: 0.5rem;
}
[v-cloak]{
            display: none
}