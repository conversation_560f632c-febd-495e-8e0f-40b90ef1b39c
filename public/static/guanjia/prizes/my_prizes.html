<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0,
            maximum-scale=1.0, user-scalable=0">
    <title>我的奖品</title>
    <link rel="stylesheet" href="./assets/css/vant.css">
    <link rel="stylesheet" href="./assets/css/my_prizes.css">
    <link rel="stylesheet" href="./assets/css/common.css">
    <script src="./assets/js/rem.js"></script>
    <script>
        (function() {
            if (typeof WeixinJSBridge == "object" && typeof WeixinJSBridge.invoke == "function") {
                handleFontSize();
            } else {
                document.addEventListener("WeixinJSBridgeReady", handleFontSize, false);
            }
            function handleFontSize() {
                // 设置网页字体为默认大小
                WeixinJSBridge.invoke("setFontSizeCallback&", { "fontSize" : 0 });
                // 重写设置网页字体大小的事件
                WeixinJSBridge.on("menu:setfont", function() {
                    WeixinJSBridge.invoke("setFontSizeCallback", { "fontSize&" : 0 });
                });
            }
        })();
    </script>
</head>
<body>
<div id="raffle-page" v-cloak>

    <div v-if="listData.data.length>0" class="products">
        <van-row type="flex" justify="space-between"
                 class="prodcut-row" v-for="item in listData.data"
                 :key="item.id">
            <van-col span="6" class="product_item">
                <img :src="item.p_pic" alt="">
            </van-col>
            <van-col span="8" class="product_des">
                <p>{{item.p_name}}</p>
                <p>{{formatActive(item.act_type)}}</p>
            </van-col>
            <van-col span="10" class="btn-content">
                <button class="btn-primary"
                        v-if="item.p_sub_type!==1&&item.status==1">已领取
                </button>
                <button class="btn-confirm"
                        v-if="item.p_sub_type!==1&&item.status==0"
                        @click="goLinQu(item)">去领取
                </button>
                <button class="btn-confirm"
                        v-if="item.p_sub_type==1&&item.status==0"
                        @click="handleSqdj(item)">申请邮寄到家
                </button>
                <button class="btn-primary"
                        v-if="item.p_sub_type==1&&item.status==2">已申请
                </button>
            </van-col>
        </van-row>
        <!-- <van-row type="flex" justify="space-between"
            class="prodcut-row">
            <van-col span="6" class="product_item">
                <img src="./assets/images/pic_demo.png" alt="">
            </van-col>
            <van-col span="8" class="product_des">
                <p>健康金大+10</p>
                <p>抽奖获得</p>
            </van-col>
            <van-col span="10" class="btn-content">
                <button class="btn-confirm">申请寄送到家</button>
            </van-col>
        </van-row> -->
        <van-divider>无更多数据了</van-divider>
    </div>
    <div class="no-data" v-if="noData">
        <img src="./assets/images/nodata.png" alt="">
        <p>现在还没有奖品，继续努力吧</p>
    </div>
    <van-dialog v-model="showDigo"
                :close-on-click-overlay="true"
                :show-cancel-button="false" :show-confirm-button="false"
                class="address-digo">
        <div class="title">
            <h2>我们将在7个工作日为您寄送,</h2>
            <h2>请确定收货信息是否正确</h2>
        </div>
        <van-form @submit="onSubmit">
            <van-field
                    readonly
                    clickable
                    name="area"
                    :value="form.area"
                    label="收货地址"
                    placeholder="请选择省、市"
                    right-icon="arrow"
                    @click="showArea= true"></van-field>
            <van-field
                    v-model="form.address"
                    rows="3"
                    autosize
                    label="详细地址"
                    type="textarea"
                    maxlength="100"
                    show-word-limit
                    placeholder="请填写详细地址（街道、门牌号）"
                    t></van-field>
            <van-field
                    v-model="form.name"
                    name="name"
                    label="收货人"
                    maxlength="50"
                    show-word-limit
                    placeholder="请输入收货人姓名"></van-field>
            <van-field
                    v-model="form.cell" type="tel"
                    name="cell"
                    label="手机号码"
                    placeholder="请输入收货人手机号码"></van-field>
            <div class="address-content-btn">
                <button class="btn-confirm-blod" style="width:2.9rem">保存</button>
            </div>
        </van-form>
    </van-dialog>

    <van-popup v-model="showArea" position="bottom">

        <van-picker
                show-toolbar
                value-key="name"
                :columns="areaList"
                @change="onAreaChange"
                @cancel="onCancel"
                @confirm="onAreaConfirm"
        />

    </van-popup>
    <van-dialog v-model="showDigoSuccess" title="提示"
                :show-cancel-button="false" :show-confirm-button="false">
        <div class="tip">
            收货信息保存成功
        </div>
        <button class="btn-confirm-blod confirm"
                style="width:3.9rem" @click="handleSecondconfirm">确定
        </button>
    </van-dialog>
</div>


<script src="./assets/js/vue.min.js"></script>
<script src="./assets/js/vant.min.js"></script>

<script src="./assets/js/vue-resource.min.js"></script>
<script src="./assets/js/util.js"></script>
<script src="./assets/js/my_prizes.js?v1"></script>
</body>
</html>
