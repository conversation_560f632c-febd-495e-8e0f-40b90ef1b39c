<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0,
      maximum-scale=1.0, user-scalable=0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>健康金规则</title>
    <link rel="stylesheet" href="./assets/css/Integral_description.css">
    <link rel="stylesheet" href="./assets/css/common.css">
    <script src="./assets/js/rem.js"></script>
    <script>
        (function() {
            if (typeof WeixinJSBridge == "object" && typeof WeixinJSBridge.invoke == "function") {
                handleFontSize();
            } else {
                document.addEventListener("WeixinJSBridgeReady", handleFontSize, false);
            }
            function handleFontSize() {
                // 设置网页字体为默认大小
                WeixinJSBridge.invoke("setFontSizeCallback&", { "fontSize" : 0 });
                // 重写设置网页字体大小的事件
                WeixinJSBridge.on("menu:setfont", function() {
                    WeixinJSBridge.invoke("setFontSizeCallback", { "fontSize&" : 0 });
                });
            }
        })();
    </script>
</head>
<body>
<div class="content">
    <div>
        <div class="title pt60">
            <span>01</span>
            如何获得健康金<img src="./assets/images/<EMAIL>" alt="">？
        </div>
        <div class="desc">
            <div class="section">
                <span>1</span>
                完成健康管理任务即可获得健康金。每周每月会有额外的运营活动任务，积极参与均可获得健康金。
            </div>
            <div class="section pt20">
                <span>2</span>
                通过抽奖活动也可以获得健康金。
            </div>
        </div>
    </div>
    <div>
        <div class="title">
            <span>02</span>
            健康金<img src="./assets/images/<EMAIL>" alt="">怎么用？
        </div>
        <div class="desc">
            <div class="section">
                <span>1</span>
                健康金可以兑换奖品页礼品。
            </div>
            <div class="section pt20">
                <span>2</span>
                健康金还可以进行抽奖。抽奖奖品包括健康金、奖品页奖品和礼券红包等。
            </div>
        </div>
    </div>

    <div>
        <div class="title">
            <span>03</span>
            注意事项
        </div>
        <div class="desc">
            <div class="section">
                <span>1</span>
                “已领取奖品”页面的礼品，每周可以申请一次寄送到家。
            </div>
            <div class="section pt20">
                <span>2</span>
                收货信息请用真实姓名，如未填写收货信息、信息不完整或拒收兑换/中奖礼品，则视为放弃领奖资格。
            </div>

            <div class="section pt20">
                <span>3</span>
                礼品在填写正确的收货地址后，工作人员会在7个工作日内电话联系确认信息，并安排包邮寄送（青海、新疆、西藏、港澳台及国外地区除外）。
            </div>

            <div class="section pt20">
                <span>4</span>
                兑奖时请仔细核对您的收货信息，礼品发货后不可修改。
            </div>

            <div class="section pt20">
                <span>5</span>
                请保持手机畅通，如快递员无法与您取得联系，将视为放弃礼品，健康金不返还。
            </div>
            <div class="section pt20">
                <span>6</span>
                兑换/抽取的红包、话费、优惠券等虚拟礼品均有有效期限，请在有效期内使用，逾期不使用，健康金将不予退还。
            </div>
            <div class="section pt20">
                <span>7</span>
                此活动与苹果公司无关
            </div>
        </div>
    </div>
    <div>
        <div class="title">
            <span>04</span>
            违规行为处理
        </div>
        <div class="desc">
            <div class="section">
                <span>1</span>
                一部手机仅限绑定一个账号，如发现多个账号使用同一手机或相同收货信息，订单将取消，健康金不返还。
            </div>
            <div class="section pt20">
                <span>2</span>
                如有用户利用系统漏洞作弊等违规方式获得或消耗健康金，经查证后，工作人员有权处罚相关账号，并保留追究相应法律责任的权利。
            </div>
        </div>
    </div>

    <div class="footer">
        <div>在法律许可范围内</div>
        <div>活动最终解释权归主办方所有</div>
    </div>

</div>
</body>
</html>
