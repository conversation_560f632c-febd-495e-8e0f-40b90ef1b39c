<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0,
      maximum-scale=1.0, user-scalable=0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>排行榜</title>
    <link rel="stylesheet" href="./assets/css/vant.css" />
    <link rel="stylesheet" href="./assets/css/rank-list.css">
    <script src="./assets/js/rem.js"></script>
    <script>
        (function() {
            if (typeof WeixinJSBridge == "object" && typeof WeixinJSBridge.invoke == "function") {
                handleFontSize();
            } else {
                document.addEventListener("WeixinJSBridgeReady", handleFontSize, false);
            }
            function handleFontSize() {
                // 设置网页字体为默认大小
                WeixinJSBridge.invoke("setFontSizeCallback&", { "fontSize" : 0 });
                // 重写设置网页字体大小的事件
                WeixinJSBridge.on("menu:setfont", function() {
                    WeixinJSBridge.invoke("setFontSizeCallback", { "fontSize&" : 0 });
                });
            }
        })();
    </script>
  </head>
  <body>
    <div id="rank-list-page" v-cloak>
      <div class="top_container">
        <div class="user_logo_container">
          <img :src="currentInfo.user_user_headimgurl">
        </div>
        <div class="user_info_container">
          <div class="user_name">{{currentInfo.user_user_nick_name}}</div>
          <div>您当前{{currentInfo.name}}排行第{{currentInfo.user_ranking}}名</div>
          <div>周期：{{dayformat}}</div>
        </div>
      </div>
      <div class="rank_list_header">

      </div>
      <div class="rank_list">
        <div class="rank_list_block">
          <van-tabs type="card" color="#f59a4a" @change="changeTab">
            <van-tab title="任务达人榜">
              <table>
                <tr class="header">
                  <th>排名</th>
                  <th>昵称</th>
                  <th>本月健康金</th>
                </tr>
                <tr class="current">
                  <td> <i>第{{dataInfo.user_ranking}}名</i></td>
                  <td><img :src="dataInfo.user_user_headimgurl"
                      class="user_img"><span
                      class="user_name_width van-ellipsis">
                      {{dataInfo.user_user_nick_name}}



                    </span></td>
                  <td>{{dataInfo.user_point}}</td>
                </tr>
                <tr v-for="(item,index) in dataInfo.ranking_list" :key="index">
                  <td><img :src="item.rankImg" class="rank_img"
                      v-if="item.rankImg"><span v-else class="rank_round">{{item.row}}</span></td>
                  <td><img :src="item.user_headimgurl" class="user_img"><span
                      class="user_name_width van-ellipsis">{{item.user_nick_name}}</span></td>
                  <td class="num">{{item.point}}</td>
                </tr>
              </table>
            </van-tab>
            <van-tab title="步数达标榜">
              <table>
                <tr class="header">
                  <th>排名</th>
                  <th>昵称</th>
                  <th>达标天数</th>
                </tr>
                <tr class="current">
                  <td>
                    <i>第{{dataInfo_2.user_ranking}}名</i>
                  </td>
                  <td><img :src="dataInfo_2.user_user_headimgurl"
                      class="user_img"><span
                      class="user_name_width van-ellipsis">
                      {{dataInfo_2.user_user_nick_name}}



                    </span></td>
                  <td>{{dataInfo_2.user_standard_number_days}}</td>
                </tr>
                <tr v-for="(item,index) in dataInfo_2.ranking_list"
                  :key="index">
                  <td><img :src="item.rankImg" class="rank_img"
                      v-if="item.rankImg"><span v-else class="rank_round">{{item.row}}</span></td>
                  <td><img :src="item.user_headimgurl" class="user_img"><span
                      class="user_name_width van-ellipsis">{{item.user_nick_name}}</span></td>
                  <td class="num">{{item.standard_number_days}}</td>
                </tr>
              </table>
            </van-tab>
            <van-tab title="答题达人榜">
              <table>
                <tr class="header">
                  <th>排名</th>
                  <th>昵称</th>
                  <th>答题数量</th>
                </tr>
                <tr class="current">
                  <td> <i>第{{dataInfo_3.user_ranking}}名</i></td>
                  <td><img :src="dataInfo_3.user_user_headimgurl"
                      class="user_img"><span
                      class="user_name_width van-ellipsis">
                      {{dataInfo_3.user_user_nick_name}}



                    </span></td>
                  <td>{{dataInfo_3.user_answer_numbers}}</td>
                </tr>
                <tr v-for="(item,index) in dataInfo_3.ranking_list"
                  :key="index">
                  <td><img :src="item.rankImg" class="rank_img"
                      v-if="item.rankImg"><span v-else class="rank_round">{{item.row}}</span></td>
                  <td><img :src="item.user_headimgurl" class="user_img"><span
                      class="user_name_width van-ellipsis">{{item.user_nick_name}}</span></td>
                  <td class="num">{{item.answer_numbers}}</td>
                </tr>
              </table>
            </van-tab>
            <van-tab title="饮食达人榜">
              <table>
                <tr class="header">
                  <th>排名</th>
                  <th>昵称</th>
                  <th>查看次数</th>
                </tr>
                <tr class="current">
                  <td>
                    <i>第{{dataInfo_4.user_ranking}}名</i>
                  </td>
                  <td><img :src="dataInfo_4.user_user_headimgurl"
                      class="user_img"><span
                      class="user_name_width van-ellipsis">
                      {{dataInfo_4.user_user_nick_name}}



                    </span></td>
                  <td>{{dataInfo_4.user_food_punch_card_numbers}}</td>
                </tr>
                <tr v-for="(item,index) in dataInfo_4.ranking_list"
                  :key="index">
                  <td><img :src="item.rankImg" class="rank_img"
                      v-if="item.rankImg"><span v-else class="rank_round">{{item.row}}</span></td>
                  <td><img :src="item.user_headimgurl" class="user_img"><span
                      class="user_name_width van-ellipsis">{{item.user_nick_name}}</span></td>
                  <td class="num">{{item.food_punch_card_numbers}}</td>
                </tr>
              </table>
            </van-tab>
          </van-tabs>
        </div>
      </div>
    </div>
    <script src="./assets/js/vue.min.js"></script>
    <script src="./assets/js/vant.min.js"></script>
    <script src="./assets/js/vue-resource.min.js"></script>
    <script src="./assets/js/util.js"></script>
    <script src="./assets/js/dayjs.min.js"></script>
    <script src="./assets/js/rank-list_m90.js?v1"></script>
  </body>
</html>
