{"name": "vue-cli3-project", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "start": "npm run serve", "build": "vue-cli-service build --mode prod", "build-stg": "vue-cli-service build --mode stg", "build-test": "vue-cli-service build --mode test", "lint": "vue-cli-service lint"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "axios": "^0.19.0", "clipboard": "^2.0.4", "core-js": "^2.6.5", "cssnano": "^4.1.10", "cssnano-preset-advanced": "^4.0.7", "dayjs": "^1.10.4", "dsbridge": "^3.1.4", "easyscroller": "^1.0.1", "echarts": "^4.2.1", "eslint-plugin-html": "^6.0.0", "fastclick": "^1.0.6", "html2canvas": "^1.4.1", "js-base64": "^3.7.2", "js-cookie": "^2.2.0", "js-md5": "^0.7.3", "jsbarcode": "^3.11.5", "jspdf": "^2.5.1", "lib-flexible": "^0.3.2", "moment": "^2.29.4", "pdfjs-dist": "2.5.207", "postcss-aspect-ratio-mini": "^1.0.1", "qrcodejs2": "^0.0.2", "swiper": "^8.3.2", "vant": "^2.8.7", "vue": "^2.6.10", "vue-growingio": "^1.0.8", "vue-i18n": "^8.23.0", "vue-pdf": "4.3.0", "vue-print-nb": "^1.7.5", "vue-router": "^3.0.3", "vue-video-player": "^5.0.2", "vuex": "^3.0.1", "weixin-js-sdk": "^1.6.0"}, "devDependencies": {"@vue/cli-plugin-babel": "^3.8.0", "@vue/cli-plugin-eslint": "^3.8.0", "@vue/cli-service": "^3.8.0", "@vue/eslint-config-standard": "^4.0.0", "babel-eslint": "^10.0.1", "babel-plugin-component": "^1.1.1", "babel-plugin-transform-remove-console": "^6.9.4", "compression-webpack-plugin": "^3.0.0", "eslint": "^5.16.0", "eslint-plugin-vue": "^5.0.0", "mockjs": "^1.1.0", "postcss-pxtorem": "^4.0.1", "sass": "^1.18.0", "sass-loader": "^7.1.0", "svg-sprite-loader": "^4.1.6", "vue-template-compiler": "^2.6.10", "webpack-alioss-plugin": "^2.4.2"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "@vue/standard"], "rules": {}, "parserOptions": {"parser": "babel-es<PERSON>"}}, "postcss": {"plugins": {"autoprefixer": {}}}, "browserslist": ["> 1%", "last 2 versions"]}