variables:
  KUBERNETES_SERVICE_ACCOUNT: gitlab-sa
  KUBERNETES_SERVICE_ACCOUNT_OVERWRITE: gitlab-sa
  KUBERNETES_SERVICE_ACCOUNT_OVERWRITE_ALLOWED: gitlab-.*
  KUBE_TOKEN: *******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  KUBERNETES_BEARER_TOKEN: *******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  KUBERNETES_BEARER_TOKEN_OVERWRITE_ALLOWED: "true"
  KUBERNETES_PRIVILEGED: "true"
  KUBERNETES_VOLUMES_PVC_NAME: builder-cache-gitlab-pvc
  KUBERNETES_VOLUMES_PVC_MOUNT_PATH: /data
  DOCKER_HOST: tcp://localhost:2375
  DOCKER_DAEMON: |
    {
      "registry-mirrors": ["https://sgp4jtx5.mirror.aliyuncs.com"],
      "insecure-registries":["hub.zz-med-stg.com"]
    }

services:
- name: docker:dind
  command:
  - /bin/sh
  - -c
  - |
    mkdir -p /etc/docker || exit
    echo "$DOCKER_DAEMON" > /etc/docker/daemon.json || exit
    dockerd-entrypoint.sh || exit

stages:
- build
- deploy

build:
  stage: build
  image: registry.cn-hangzhou.aliyuncs.com/zz-med/php:72-builder
  variables:
    CI_DEBUG_TRACE: "false"
    CI_PROJECT_NAME: patient-h5
  environment:
    name: staging
  script:
  - export RELEASE_NUM="$(date +%Y%m%d)-$(echo $CI_COMMIT_SHA | cut -c1-8)"
  - cnpm install
  - cnpm run build-stg
  - docker login hub.zz-med-stg.com -u builder -p CnHP4MxREZr2udi
  - docker build -t "hub.zz-med-stg.com/$CI_PROJECT_NAME/stg:$RELEASE_NUM" . -f docker/Dockerfile
  - docker push "hub.zz-med-stg.com/$CI_PROJECT_NAME/stg:$RELEASE_NUM"
  only:
  - develop
  tags:
  - k8s-stg-runner

deploy_stg:
  stage: deploy
  image: hub.zz-med-stg.com/kubectl/kubectl-stg:1.13.4
  variables:
    NAMESPACE: zzmed-stg
    CI_PROJECT_NAME: patient-h5
  script:
  - export RELEASE_NUM="$(date +%Y%m%d)-$(echo $CI_COMMIT_SHA | cut -c1-8)"
  - export IMAGE_NAME=hub.zz-med-stg.com/$CI_PROJECT_NAME/stg:$RELEASE_NUM
  - export NAMESPACE=zzmed-stg
  - cd docker && ./config.sh ./k8s-stg.tpl.yaml > k8s-stg.yaml
  - cat k8s-stg.yaml
  - kubectl apply -f k8s-stg.yaml
  only:
  - develop
  tags:
  - k8s-stg-runner

build_prd:
  stage: build
  image: registry.cn-hangzhou.aliyuncs.com/zz-med/php:72-builder
  variables:
    CI_DEBUG_TRACE: "false"
    CI_PROJECT_NAME: patient-h5
  environment:
    name: production
  script:
  - export CACHE_DIR="/data/build_cache/$CI_PROJECT_NAME"
  - export RELEASE_NUM="$CI_COMMIT_REF_NAME"
  - composer install --no-dev --no-interaction -o
  - docker login hub.zz-med-stg.com -u builder -p CnHP4MxREZr2udi
  - docker build -t "hub.zz-med-stg.com/$CI_PROJECT_NAME/production:$RELEASE_NUM" . -f docker/Dockerfile
  - docker push "hub.zz-med-stg.com/$CI_PROJECT_NAME/production:$RELEASE_NUM"
  only:
  - tags
  except:
  - staging
  - testing
  tags:
  - k8s-stg-runner

deploy_prd:
  stage: deploy
  image: hub.zz-med-stg.com/kubectl/kubectl-prd:1.13.4
  variables:
    NAMESPACE: zzmed-prd
    CI_PROJECT_NAME: ihec-official
    KUBERNETES_BEARER_TOKEN: *******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    KUBE_TOKEN: *******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  script:
  - export RELEASE_NUM="$CI_COMMIT_REF_NAME"
  - export IMAGE_NAME=hub.zz-med-stg.com/$CI_PROJECT_NAME/production:$RELEASE_NUM
  - export NAMESPACE=zzmed-prd
  - cd docker && ./config.sh ./k8s-prd.tpl.yaml > k8s-prd.yaml
  - cat k8s-prd.yaml
  - kubectl apply -f k8s-prd.yaml
  only:
  - tags
  except:
  - staging
  - testing
  tags:
  - k8s-prod-runner
  environment:
    name: production
  when: manual