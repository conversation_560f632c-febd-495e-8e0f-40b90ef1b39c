const CompressionPlugin = require('compression-webpack-plugin')
const path = require('path')
const resolve = dir => path.join(__dirname, dir)
const os = require('os');
//获取机器ip、方便手机调试
function getIpAddress() {
  let ifaces = os.networkInterfaces()
  for (let dev in ifaces) {
    let iface = ifaces[dev]
    for (let i = 0; i < iface.length; i++) {
      let { family, address, internal } = iface[i]
      if (family === 'IPv4' && address !== '127.0.0.1' && !internal) {
        return address
      }
    }
  }
}
module.exports = {
  publicPath: '/',
  // baseUrl: '/',// 部署应用时的根路径(默认'/'),也可用相对路径(存在使用限制)
  outputDir: 'dist', // 运行时生成的生产环境构建文件的目录(默认''dist''，构建之前会被清除)
  assetsDir: '', // 放置生成的静态资源(s、css、img、fonts)的(相对于 outputDir 的)目录(默认'')
  indexPath: 'index.html', // 指定生成的 index.html 的输出路径(相对于 outputDir)也可以是一个绝对路径。
  pages: {
    // pages 里配置的路径和文件名在你的文档目录必须存在 否则启动服务会报错
    index: {
      // 除了 entry 之外都是可选的
      entry: 'src/main.js', // page 的入口,每个“page”应该有一个对应的 JavaScript 入口文件
      template: 'public/index.html', // 模板来源
      filename: 'index.html', // 在 dist/index.html 的输出
      title: 'Index Page', // 当使用 title 选项时,在 template 中使用：<title><%= htmlWebpackPlugin.options.title %></title>
      chunks: ['chunk-vendors', 'chunk-common', 'index'] // 在这个页面中包含的块，默认情况下会包含,提取出来的通用 chunk 和 vendor chunk
    }
    // subpage: 'src/subpage/main.js'//官方解释：当使用只有入口的字符串格式时,模板会被推导为'public/subpage.html',若找不到就回退到'public/index.html',输出文件名会被推导为'subpage.html'
  },
  lintOnSave: true, // 是否在保存的时候检查
  productionSourceMap: false, // 生产环境是否生成 sourceMap 文件
  css: {
    // extract: true, // 是否使用css分离插件 ExtractTextPlugin
    sourceMap: false, // 开启 CSS source maps
    loaderOptions: {
      postcss: {
        plugins: [
          require('postcss-pxtorem')({
            rootValue: 32, // 换算的基数
            selectorBlackList: ['echarts_'], // 忽略转换正则匹配项
            propList: ['*']
          })
        ]
      }
    }
    // modules: false// 启用 CSS modules for all css / pre-processor files.
  },
  devServer: {
    // 环境配置
    host: '0.0.0.0',
    port: 9000,
    public: getIpAddress() + ':9000',
    overlay: {
      warnings: false,
      errors: false
    },
    https: false,
    hotOnly: false,
    open: true, // 配置自动启动浏览器
    proxy: {
      // 配置多个代理(配置一个 proxy: 'http://localhost:4000' )
      '/api': {
        target: 'https://mmc-api.zz-med-test.com',
        ws: true,
        changeOrigin: true
      },
      '/foo': {
        target: '<other_url>'
      }
    }
  },
  configureWebpack: {
    resolve: {
      extensions: ['.js', '.vue', '.json'],
      alias: {
        vue$: 'vue/dist/vue.esm.js',
        '@': resolve('src'),
        _store_: resolve('src/store/index.js'),
        '@common': resolve('src/common'),
        '@components': resolve('src/components/index.js')
      }
    }
  },
  chainWebpack: config => {
    config.plugins.delete('prefetch-index')
    config.plugins.delete('preload-index');
    // #region svg-config
    const svgRule = config.module.rule('svg') // 找到svg-loader
    svgRule.uses.clear() // 清除已有的loader, 如果不这样做会添加在此loader之后
    svgRule.exclude.add(/node_modules/) // 正则匹配排除node_modules目录
    svgRule // 添加svg新的loader处理
      .test(/\.svg$/)
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]'
      })

    // #endregion
    var externals = {
      vue: 'Vue',
      vant: 'vant',
      vuex: 'Vuex',
      echarts: 'echarts'
    }
    config.externals(externals)
    // #region 启用GZip压缩
    config
      .plugin('compression')
      .use(CompressionPlugin, {
        asset: '[path].gz[query]',
        algorithm: 'gzip',
        test: new RegExp('\\.(' + ['js', 'css'].join('|') + ')$'),
        threshold: 10240,
        minRatio: 0.8,
        cache: true
      })
      .tap(args => {
      })
  }
}
