<!--
 * @Descripttion: 
 * @version: 
 * @Author: xulh
 * @Date: 2021-05-24 13:07:09
 * @LastEditors: xulh
 * @LastEditTime: 2021-05-26 15:03:03
-->
<template>
  <div id="app">
    <router-view/>
  </div>
</template>
<!--<script>-->
  <!--export default {-->
    <!--name: '',-->
    <!--data() {-->
      <!--return {-->
        <!--onLine: navigator.onLine-->
      <!--}-->
    <!--},-->
    <!--created() {-->

    <!--},-->
    <!--mounted(){-->
      <!--window.addEventListener('online', this.updateOnlineStatus)-->
      <!--window.addEventListener('offline', this.updateOnlineStatus)-->
      <!--if (navigator.onLine) {-->
        <!--alert('online')-->
        <!---->
      <!--} else {-->
        <!--alert('offline')-->
      <!--}-->
    <!--},-->
    <!--methods: {-->
      <!--updateOnlineStatus(e) {-->
        <!--const { type } = e;-->
        <!--this.onLine = type === 'online';-->
      <!--}-->
    <!--},-->
    <!--beforeDestroy(){-->
      <!--window.removeEventListener('online', this.updateOnlineStatus);-->
      <!--window.removeEventListener('offline', this.updateOnlineStatus);-->
    <!--}-->
  <!--}-->
<!--</script>-->
<style lang="scss" scoped>
  #app {
    font-family: 'Avenir', Helvetica, Arial, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-align: center;
    color: #2c3e50;
  }

  #nav {
    padding: 30px;
    a {
      font-weight: bold;
      color: #2c3e50;
      &.router-link-exact-active {
        color: #42b983;
      }

    }
  }


</style>
