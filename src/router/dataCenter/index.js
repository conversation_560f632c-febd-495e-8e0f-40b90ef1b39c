/*
 * @Descripttion: 数据中心h5 路由
 * @version: 
 * @Author: guxiang
 * @Date: 2022-01-18 10:26:44
 * @LastEditors: guxiang
 * @LastEditTime: 2022-01-18 10:26:44
 */
import mmcDataCenter from '@/views/dataCenter/mmcDataCenter.vue' // mmc数据中心
import ihecDataCenter from '@/views/dataCenter/ihecDataCenter.vue' // mmc数据中心
import ihecHospitalData from '@/views/dataCenter/ihecHospitalData.vue' // 医院数据
// mmc数据中心
const routes = [
    {
        path: '/dataCenter/mmcDataCenter',
        name: 'dataCenter.mmcDataCenter',
        component: mmcDataCenter,
        meta: {
            title: '数据中心'
        }
    },
    {
        path: '/dataCenter/ihecDataCenter',
        name: 'dataCenter.ihecDataCenter',
        component: ihecDataCenter,
        meta: {
            title: '数据中心'
        }
    },
    {
        path: '/dataCenter/ihecHospitalData',
        name: 'dataCenter.ihecHospitalData',
        component: ihecHospitalData,
        meta: {
            title: '医院数据'
        }
    }
]

export default routes