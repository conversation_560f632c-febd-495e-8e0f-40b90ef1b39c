// import questionnaireList from '@/views/ihecMiniwechat/questionnaireList.vue'
const routes = [
    {
        path: '/ihecMiniwechat/questionnaireList',
        name: 'ihecMiniwechat.questionnaireList',
        component: ()=> import('@/views/ihecMiniwechat/questionnaireList.vue'),
        meta: {
          title: '选择患者问卷'
        }
    },
    {
      path: '/ihecMiniwechat/servicePackageExpressInfo',
      name: 'ihecMiniwechat.servicePackageExpressInfo',
      component: ()=> import('@/views/ihecMiniwechat/servicePackageExpressInfo.vue'),
      meta: {
        title: '物流信息'
      }
    },
    {
      path: '/ihecMiniwechat/medicationReminder',
      name: 'ihecMiniwechat.medicationReminder',
      component: ()=> import('@/views/ihecMiniwechat/medicationReminder.vue'),
      meta: {
        title: '就诊提醒'
      }
    },
    {
      path: '/ihecMiniwechat/healthInfo',
      name: 'ihecMiniwechat.healthInfo',
      component: ()=> import('@/views/ihecMiniwechat/healthInfo.vue'),
      meta: {
        title: '健康信息'
      }
    },
    {
      path: '/ihecMiniwechat/myEquityActivity',
      name: 'ihecMiniwechat.myEquityActivity',
      component: ()=> import('@/views/ihecMiniwechat/myEquityActivity.vue'),
      meta: {
        title: '我的权益'
      }
    },
    {
      path: '/ihecMiniwechat/equityActivityDetails',
      name: 'ihecMiniwechat.equityActivityDetails',
      component: ()=> import('@/views/ihecMiniwechat/equityActivityDetails.vue'),
      meta: {
        title: '权益活动详情'
      }
    },
]
export default routes