import home from '@/views/Home.vue'
import about from '@/views/About.vue'
import echartIndex from '@/views/Home/index.vue'

import HealthReport from '@/views/HealthReport/HealthReport.vue'
import HealthReportList from '@/views/HealthReport/HealthReportList.vue'
import HealthReportExam from '@/views/HealthReport/HealthReportExam.vue'
import articleDetail from '@/views/articleDetail.vue'

const appRouter = [
  {
    path: '/',
    component: about
  },
  {
    path: '/home',
    component: home
  },
  {
    path: '/articleDetail',
    component: articleDetail
  },
  {
    path: '/echartIndex',
    component: echartIndex
  },
  {
    path: '/guanjia/health-report',
    name: 'MMC.HealthReport',
    component: HealthReport,
    children: [
      {
        path: 'list',
        name: 'MMC.HealthReport.HealthReportList',
        component: HealthReportList
      }
    ]
  },
  {
    path: '/health-list',
    name: 'M<PERSON>.HealthReportList',
    component: HealthReportList
  },
  {
    path: '/health-exam',
    name: 'MMC.HealthReportExam',
    component: HealthReportExam
  }
]

export const routes = [
  ...appRouter
]
