import Recipes from '@/views/cookBook/recipes.vue'
import EstimatedWeight from '@/views/cookBook/estimatedWeight.vue'
import SwitchFood from '@/views/cookBook/switchFood.vue'
import FoodDetail from '@/views/cookBook/foodDetail.vue'

const routes = [
  {
    path: '/cookbook/recipes',
    name: 'Cookbook.Recipes',
    component: Recipes,
    meta: {
      title: '今日食谱'
    }
  },
  {
    path: '/cookbook/estimated/weight',
    name: 'Cookbook.Estimated.Weight',
    component: EstimatedWeight,
    meta: {
      title: '估算重量'
    }
  },
  {
    path: '/cookbook/switch',
    name: 'Cookbook.Switch.Food',
    component: SwitchFood,
    meta: {
      title: '替换食物'
    }
  },
  {
    path: '/cookbook/detail',
    name: 'Cookbook.Food.Detail',
    component: FoodDetail,
    meta: {
      title: '食物详情'
    }
  }
]

export default routes
