/*
 * @Descripttion: 医生工作史h5 路由
 * @version:
 * @Author: l<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-22 14:16:54
 * @LastEditors: liu<PERSON><PERSON>n
 * @LastEditTime: 2021-07-27 15:23:52
 */
import bloodPressureAtlas from '@/views/docWorkRoom/bloodPressureAtlas.vue' // 血压图谱
import bloodPressureSevenDayReport from '@/views/docWorkRoom/bloodPressureSevenDayReport.vue' // 血压七日报告
import bloodSugarTrendStat from '@/views/docWorkRoom/bloodSugarTrendStat.vue' // 血糖趋势统计
import bloodSugarTrendStatFull from '@/views/docWorkRoom/bloodSugarTrendStatFull.vue' // 血糖趋势统计Full
import bloodSugarSevenDayReport from '@/views/docWorkRoom/bloodSugarSevenDayReport.vue' // 血糖七日报告

import sportStepNumberSevenDayReport from '@/views/docWorkRoom/sportStepNumberSevenDayReport.vue'
import heightWeightSevenDayReport from '@/views/docWorkRoom/heightWeightSevenDayReport.vue' // 身高体重七日报告

import completeFiles from '@/views/docWorkRoom/completeFiles.vue'       // 健康便利店完整档案
import pressureCheck from '@/views/docWorkRoom/pressureCheck.vue'       // 健康便利店完整档案
import completeReport from '@/views/docWorkRoom/completeReport'         // 四肢血压/动脉硬化检测完整档案

/**
 * 医生工作室对接--患者详情
 */
import hospitalData from '@/views/docWorkRoom/hospitalData.vue'         // 医院数据
import completePatientExample from '@/views/docWorkRoom/completePatientExample.vue' // 完整病例
import vec from '@/views/docWorkRoom/vec.vue' // 血管内皮
import visceralFat from '@/views/docWorkRoom/visceralFat.vue' // 内脏脂肪
import pwvAbiTbi from '@/views/docWorkRoom/pwvAbiTbi.vue' // 内脏脂肪
import fundusExam from '@/views/docWorkRoom/fundusExam.vue' // 眼底检查
import breathExam from '@/views/docWorkRoom/breathExam.vue' // 肺功能监测
import ecgExam from '@/views/docWorkRoom/ecgExam.vue' //心电图
import ecgChartScreen from '@/views/docWorkRoom/ecgChartScreen.vue'
import liverHardness from '@/views/docWorkRoom/liverHardness.vue' //肝脏瞬时弹性硬度检测
import humancomponent from '@/views/docWorkRoom/humancomponent.vue' //人体成分
import nerveconduction from '@/views/docWorkRoom/nerveconduction.vue' //神经传导
import patientReport from '@/views/docWorkRoom/patientReport.vue' // 患者报告
import patientReportDetail from '@/views/docWorkRoom/patientReportDetail.vue' // 患者报告
import questionnaire from '@/views/docWorkRoom/questionnaire.vue' // 问卷
import diagnoseDetail from '@/views/docWorkRoom/diagnoseDetail.vue' // 诊断
import hoursBp from '@/views/docWorkRoom/hoursBp.vue' // 24小时动态血压
import reportInfo from '@/views/docWorkRoom/reportInfo'
import reportDetails from '@/views/docWorkRoom/reportDetails'

import workroomLocation from '@/views/docWorkRoom/workroomLocation.vue'

/**
 * 医生工作室对接--我的病案
 */
import sickerMedicalRecord from '@/views/docWorkRoom/sickerMedicalRecord/index.vue' // 我的病案
import commonMenu from '@/views/docWorkRoom/sickerMedicalRecord/commonMenu/index.vue'   // 个人体征
import commonCheck from '@/views/docWorkRoom/sickerMedicalRecord/commonCheck/index.vue' // 实验室检查，辅助检查，生物学标志物
import personal from '@/views/docWorkRoom/sickerMedicalRecord/personal/index.vue' // 个人体征
import commonReport from '@/views/docWorkRoom/sickerMedicalRecord/commonReport/index.vue' // 报告页面
import commonMenu2 from '@/views/docWorkRoom/sickerMedicalRecord/commonMenu/index2.vue'   // 个人体征
import assessment from '@/views/docWorkRoom/sickerMedicalRecord/assessment/index.vue'   // 评估
import chart from '@/views/docWorkRoom/sickerMedicalRecord/chart/index.vue'
import abnormal from '@/views/docWorkRoom/sickerMedicalRecord/abnormal/index.vue'
import cgmBs from '@/views/docWorkRoom/sickerMedicalRecord/cgmBs/index.vue' // 动态血糖
import cgmChart from '@/views/docWorkRoom/sickerMedicalRecord/cgmBs/chart.vue'
import cgmChartFullscreen from '@/views/docWorkRoom/sickerMedicalRecord/cgmBs/chart-fullscreen.vue'


import promotionDocApp from '@/views/docWorkRoom/promotionDocApp.vue'
import medication from '@/views/docWorkRoom/medication/index.vue'
import medicationSearch from '@/views/docWorkRoom/medication/search.vue'
import medicationSearchMoreList from '@/views/docWorkRoom/medication/searchMoreList.vue'
import addMedication from '@/views/docWorkRoom/medication/addMedication.vue'
import medicationFrequence from '@/views/docWorkRoom/medication/medicationFrequence.vue'
import medicationDosage from '@/views/docWorkRoom/medication/medicationDosage.vue'
import drugDetails from '@/views/docWorkRoom/medication/components/drugDetails.vue'

import hisDetails from '@/views/docWorkRoom/medication/components/hisDetails.vue'
import historyData from '@/views/docWorkRoom/medication/components/historyData.vue'

import projectMain from '@/views/docWorkRoom/projectMain/projectMain.vue'
import registerStatus from '@/views/docWorkRoom/projectMain/registerStatus.vue'
import dataList from '@/views/docWorkRoom/projectMain/dataList.vue'
import previewData from '@/views/docWorkRoom/projectMain/previewData.vue'
import levelDatalist from '@/views/docWorkRoom/projectMain/levelDatalist.vue'
import newProjectMain from '@/views/docWorkRoom/projectMain/newProjectMain.vue'
import appHome from '@/views/docWorkRoom/appHome.vue'
import equityVerificationCode from '@/views/docWorkRoom/equityVerificationCode.vue'
import patientRights from '@/views/docWorkRoom/patientRights.vue'
import newsList from '@/views/docWorkRoom/news-list.vue'
import appHomeOld from '@/views/docWorkRoom/appHomeOld.vue'

import bloodglucoseTrend from '@/views/docWorkRoom/houseHoldData/bloodglucoseTrend.vue'
import bloodglucoseRecord from '@/views/docWorkRoom/houseHoldData/bloodglucoseRecord.vue'
import bloodgOxygenSaturationTrend from '@/views/docWorkRoom/houseHoldData/bloodgOxygenSaturationTrend.vue'
import bloodgOxygenSaturationRecord from '@/views/docWorkRoom/houseHoldData/bloodgOxygenSaturationRecord.vue'
import wheezingSound from '@/views/docWorkRoom/houseHoldData/wheezingSound.vue'
import wheezingTrend from '@/views/docWorkRoom/houseHoldData/wheezingTrend.vue'
import bodyFatIndexTrend from '@/views/docWorkRoom/houseHoldData/bodyFatIndexTrend.vue'
import bodyFatIndexRecord from '@/views/docWorkRoom/houseHoldData/bodyFatIndexRecord.vue'
import peakFlowRateRecord from '@/views/docWorkRoom/houseHoldData/peakFlowRateRecord.vue'
import peakFlowRateTrend from '@/views/docWorkRoom/houseHoldData/peakFlowRateTrend.vue'
import bloodLipidRecord from '@/views/docWorkRoom/houseHoldData/bloodLipidRecord.vue'
import nebulizedMedicationTrend from '@/views/docWorkRoom/houseHoldData/nebulizedMedicationTrend.vue'
import nebulizedMedicationRecord from '@/views/docWorkRoom/houseHoldData/nebulizedMedicationRecord.vue'
import stepsTrend from '@/views/docWorkRoom/houseHoldData/stepsTrend.vue'
import stepsRecord from '@/views/docWorkRoom/houseHoldData/stepsRecord.vue'
// 血糖

// 渠道码
import invite from '@/views/docWorkRoom/channel/invite.vue'
import inviteEntries from '@/views/docWorkRoom/channel/invite-entries.vue'
import inviteChannel from '@/views/docWorkRoom/channel/invite-channel.vue'
import inviteReferral from '@/views/docWorkRoom/channel/invite-referral.vue'

import channel from '@/views/docWorkRoom/channel/channel.vue'
import channelCompany from '@/views/docWorkRoom/channel/channel-company.vue'
import channelOrder from '@/views/docWorkRoom/channel/channel-order.vue'
// 测量结果
import measureResultBg from '@/views/docWorkRoom/measureResult/bg.vue'
import measureResultBp from '@/views/docWorkRoom/measureResult/bp.vue'

import companyrevenue from '@/views/docWorkRoom/channel/income/companyrevenue.vue' // 公司收入
import immediately from '@/views/docWorkRoom/channel/income/immediately.vue' // 立即提现
import employeeView from '@/views/docWorkRoom/channel/income/employeeView.vue'  // 按员工查看
import bankcard from '@/views/docWorkRoom/channel/income/bankcard.vue'  // 银行卡列表
import deactivateChannel from '@/views/docWorkRoom/channel/deactivateChannel.vue' // 停用渠道1

const routes = [
  {
    path: '/docWorkRoom/bloodPressureAtlas',
    name: 'docWorkRoom.bloodPressureAtlas',
    component: bloodPressureAtlas,
    meta: {
      title: '血压图谱'
    }
  },
  {
    path: '/docWorkRoom/bloodPressureSevenDayReport',
    name: 'docWorkRoom.bloodPressureSevenDayReport',
    component: bloodPressureSevenDayReport,
    meta: {
      title: '血压七天报告'
    }
  },
  {
    path: '/docWorkRoom/bloodSugarTrendStat',
    name: 'docWorkRoom.bloodSugarTrendStat',
    component: bloodSugarTrendStat,
    meta: {
      title: '血糖趋势统计'
    }
  },
  {
    path: '/docWorkRoom/bloodSugarTrendStatFull',
    name: 'docWorkRoom.bloodSugarTrendStatFull',
    component: bloodSugarTrendStatFull,
    meta: {
      title: '血糖记录'
    }
  },
  {
    path: '/docWorkRoom/bloodSugarSevenDayReport',
    name: 'docWorkRoom.bloodSugarSevenDayReport',
    component: bloodSugarSevenDayReport,
    meta: {
      title: '血糖七天报告'
    }
  },
  {
    path: '/docWorkRoom/sportStepNumberSevenDayReport',
    name: 'docWorkRoom.sportStepNumberSevenDayReport',
    component: sportStepNumberSevenDayReport,
    meta: {
      title: '运动步数七日报告'
    }
  },
  {
    path: '/docWorkRoom/heightWeightSevenDayReport',
    name: 'docWorkRoom.heightWeightSevenDayReport',
    component: heightWeightSevenDayReport,
    meta: {
      title: '身高体重七日报告'
    }
  },
  {
    path: '/docWorkRoom/completeFiles',
    name: 'docWorkRoom.completeFiles',
    component: completeFiles,
    meta: {
      title: '完整档案'
    }
  },
  {
    path: '/docWorkRoom/pressureCheck',
    name: 'docWorkRoom.pressureCheck',
    component: pressureCheck,
    meta: {
      title: '四肢血压/动脉硬化检测'
    }
  },
  {
    path: '/docWorkRoom/completeReport',
    name: 'docWorkRoom.completeReport',
    component: completeReport,
    meta: {
      title: '四肢血压/动脉硬化检测'
    }
  },
  {
    path: '/docWorkRoom/hospitalData',
    name: 'docWorkRoom.hospitalData',
    component: hospitalData,
    meta: {
      title: '医院数据'
    }
  },
  {
    path: '/docWorkRoom/diagnoseDetail',
    name: 'docWorkRoom.diagnoseDetail',
    component: diagnoseDetail,
    meta: {
      title: '诊断信息'
    }
  },
  {
    path: '/docWorkRoom/completePatientExample',
    name: 'docWorkRoom.completePatientExample',
    component: completePatientExample,
    meta: {
      title: '完整病例'
    }
  },
  {
    path: '/docWorkRoom/vec',
    name: 'docWorkRoom.vec',
    component: vec,
    meta: {
      title: '血管内皮'
    }
  },
  {
    path: '/docWorkRoom/visceralFat',
    name: 'docWorkRoom.visceralFat',
    component: visceralFat,
    meta: {
      title: '内脏脂肪'
    }
  },
  {
    path: '/docWorkRoom/pwvAbiTbi',
    name: 'docWorkRoom.pwvAbiTbi',
    component: pwvAbiTbi,
    meta: {
      title: 'PWV+ABI'
    }
  },
  {
    path: '/docWorkRoom/fundusExam',
    name: 'docWorkRoom.fundusExam',
    component: fundusExam,
    meta: {
      title: '眼底检查'
    }
  },
  {
    path: '/docWorkRoom/breathExam',
    name: 'docWorkRoom.breathExam',
    component: breathExam,
    meta: {
      title: '肺功能监测'
    }
  },
  {
    path: '/docWorkRoom/ecgExam',
    name: 'docWorkRoom.ecgExam',
    component: ecgExam,
    meta: {
      title: '心电图'
    }
  },
  {
    path: '/docWorkRoom/ecgChartScreen',
    name: 'docWorkRoom.ecgChartScreen',
    component: ecgChartScreen,
    meta: {
      title: '心电图'
    }
  },
  {
    path: '/docWorkRoom/liverHardness',
    name: 'docWorkRoom.liverHardness',
    component: liverHardness,
    meta: {
      title: '肝脏瞬时弹性硬度检测'
    }
  },
  {
    path: '/docWorkRoom/humancomponent',
    name: 'docWorkRoom.humancomponent',
    component: humancomponent,
    meta: {
      title: '人体成分分析'
    }
  },
  {
    path: '/docWorkRoom/nerveconduction',
    name: 'docWorkRoom.nerveconduction',
    component: nerveconduction,
    meta: {
      title: '神经传导'
    }
  },
  {
    path: '/docWorkRoom/patientReport',
    name: 'docWorkRoom.patientReport',
    component: patientReport,
    meta: {
      title: '患者报告'
    }
  },
  {
    path: '/docWorkRoom/patientReportDetail',
    name: 'docWorkRoom.patientReportDetail',
    component: patientReportDetail,
    meta: {
      title: '报告详情'
    }
  },
  {
    path: '/docWorkRoom/questionnaire',
    name: 'docWorkRoom.questionnaire',
    component: questionnaire,
    meta: {
      title: '问卷管理'
    }
  },
  {
    path: '/docWorkRoom/workroomLocation',
    name: 'docWorkRoom.workroomLocation',
    component: workroomLocation,
    meta: {
      title: '定位'
    }
  },
  {
    path: '/docWorkRoom/servicePackageExchange',
    name: 'docWorkRoom.servicePackageExchange',
    component: () => import('@/views/docWorkRoom/servicePackageExchange'),
    meta: {
      title: '兑换服务包'
    }
  },
  {
    path: '/docWorkRoom/sickerMedicalRecord',
    name: 'docWorkRoom.sickerMedicalRecord',
    component: sickerMedicalRecord,
    meta: {
      title: '患者病案'
    }
  },
  {
    path: '/docWorkRoom/sickerMedicalRecord/commonMenu',
    name: 'docWorkRoom.sickerMedicalRecord.commonMenu',
    component: commonMenu,
    meta: {
      title: ''
    }
  },
  {
    path: '/docWorkRoom/sickerMedicalRecord/commonCheck',
    name: 'docWorkRoom.sickerMedicalRecord.commonCheck',
    component: commonCheck,
    meta: {
      title: ''
    }
  },
  {
    path: '/docWorkRoom/sickerMedicalRecord/abnormal',
    name: 'docWorkRoom.sickerMedicalRecord.abnormal',
    component: abnormal,
    meta: {
      title: ''
    }
  },
  {
    path: '/docWorkRoom/hoursBp',
    name: 'docWorkRoom.hoursBp',
    component: hoursBp,
    meta: {
      title: '24小时动态血压'
    }
  },
  {
    path: '/docWorkRoom/sickerMedicalRecord/personal',
    name: 'docWorkRoom.sickerMedicalRecord.personal',
    component: personal,
    meta: {
      title: ''
    }
  },
  {
    path: '/docWorkRoom/sickerMedicalRecord/commonReport',
    name: 'docWorkRoom.sickerMedicalRecord.commonReport',
    component: commonReport,
    meta: {
      title: '查看报告'
    }
  },
  {
    path: '/docWorkRoom/commonMenu2',
    name: 'docWorkRoom.commonMenu2',
    component: commonMenu2,
    meta: {
      title: '24小时动态血压'
    }
  },
  {
    path: '/docWorkRoom/sickerMedicalRecord/assessment',
    name: 'docWorkRoom.assessment',
    component: assessment,
    meta: {
      title: '评估'
    }
  },
  {
    path: '/docWorkRoom/sickerMedicalRecord/chart',
    name: 'docWorkRoom.sickerMedicalRecord.chart',
    component: chart,
    meta: {
      title: ''
    }
  },
  {
    path: '/docWorkRoom/sickerMedicalRecord/cgmBs',
    name: 'docWorkRoom.sickerMedicalRecord.chart',
    component: cgmBs,
    meta: {
      title: '动态血糖历史记录'
    }
  },
  {
    path: '/docWorkRoom/sickerMedicalRecord/cgmBs/chart',
    name: 'docWorkRoom.sickerMedicalRecord.cgmBs.chart',
    component: cgmChart,
    meta: {
      title: '动态血糖历史记录'
    }
  },
  {
    path: '/docWorkRoom/sickerMedicalRecord/cgmBs/chart-fullscreen',
    name: 'docWorkRoom.sickerMedicalRecord.cgmBs.chartFullscreen',
    component: cgmChartFullscreen,
    meta: {
      title: '动态血糖历史记录'
    }
  },
  {
    path: '/docWorkRoom/reportInfo',
    name: 'docWorkRoom.reportInfo',
    component: reportInfo,
    meta: {
      title: '识别报告',
      keep: true

    }
  },
  {
    path: '/docWorkRoom/reportDetails',
    name: 'docWorkRoom.reportDetails',
    component: reportDetails,
    meta: {
      title: '修改检查单',
    }
  },
  {
    path: '/docWorkRoom/promotionDocApp',
    name: 'docWorkRoom.promotionDocApp',
    component: promotionDocApp,
    meta: {
      title: '开通医生工作室',
    }
  },
  {
    path: '/docWorkRoom/medication/index',
    name: 'docWorkRoom.medication',
    component: medication,
    meta: {
      title: '用药',
    }
  },
  {
    path: '/docWorkRoom/medication/search',
    name: 'docWorkRoom.medicationSearch',
    component: medicationSearch,
    meta: {
      title: '搜药品',
    }
  },
  {
    path: '/docWorkRoom/medication/searchMoreList',
    name: 'docWorkRoom.searchMoreList',
    component: medicationSearchMoreList,
    meta: {
      title: '搜药品',
    }
  },
  {
    path: '/docWorkRoom/medication/addMedication',
    name: 'docWorkRoom.addMedication',
    component: addMedication,
    meta: {
      title: '添加药品',
    }
  },
  {
    path: '/docWorkRoom/medication/medicationFrequence',
    name: 'docWorkRoom.medicationFrequence',
    component: medicationFrequence,
    meta: {
      title: '用药频次',
    }
  },
  {
    path: '/docWorkRoom/medication/medicationDosage',
    name: 'docWorkRoom.medicationDosage',
    component: medicationDosage,
    meta: {
      title: '用药剂量',
    }
  },
  {
    path: '/docWorkRoom/medication/drugDetails',
    name: 'docWorkRoom.drugDetails',
    component: drugDetails,
    meta: {
      title: '用药详情',
    }
  },
  {
    path: '/docWorkRoom/medication/hisDetails',
    name: 'docWorkRoom.hisDetails',
    component: hisDetails,
    meta: {
      title: 'HIS记录',
    }
  },
  {
    path: '/docWorkRoom/medication/historyData',
    name: 'docWorkRoom.historyData',
    component: historyData,
    meta: {
      title: '历史数据',
    }
  },
  {
    path: '/docWorkRoom/projectMain/projectMain',
    name: 'docWorkRoom.projectMain',
    component: projectMain,
    meta: {
      title: '',
    }
  },
  {
    path: '/docWorkRoom/projectMain/registerStatus',
    name: 'docWorkRoom.registerStatus',
    component: registerStatus,
    meta: {
      title: '报名进度',
    }
  },
  {
    path: '/docWorkRoom/projectMain/dataList',
    name: 'docWorkRoom.dataList',
    component: dataList,
    meta: {
      title: '资料目录',
    }
  },
  {
    path: '/docWorkRoom/projectMain/previewData',
    name: 'docWorkRoom.previewData',
    component: previewData,
    meta: {
      title: '项目介绍',
    }
  },
  {
    path: '/docWorkRoom/projectMain/levelDatalist/:id',
    name: 'docWorkRoom.levelDatalist',
    component: levelDatalist,
    meta: {
      title: '',
    }
  },
  {
    path: '/docWorkRoom/projectMain/newProjectMain',
    name: 'docWorkRoom.newProjectMain',
    component: newProjectMain,
    meta: {
      title: '',
    }
  },
  {
    path: '/docWorkRoom/appHome',
    name: 'docWorkRoom.appHome',
    component: appHome,
    meta: {
      title: '',
    }
  },
  {
    path: '/docWorkRoom/equityVerificationCode',
    name: 'docWorkRoom.equityVerificationCode',
    component: equityVerificationCode,
    meta: {
      title: '患者权益核销码'
    }
  },
  {
    path: '/docWorkRoom/patientRights',
    name: 'docWorkRoom.patientRights',
    component: patientRights,
    meta: {
      title: '患者权益'
    }
  },
  {
    path: '/docWorkRoom/invite-entries',
    name: 'docWorkRoom.inviteEntries',
    component: inviteEntries,
    meta: {
      title: '选择邀请渠道'
    }
  },
  {
    path: '/docWorkRoom/invite-channel',
    name: 'docWorkRoom.inviteChannel',
    component: inviteChannel,
    meta: {
      title: '我的代理商邀请码'
    }
  },
  {
    path: '/docWorkRoom/invite',
    name: 'docWorkRoom.invite',
    component: invite,
    meta: {
      title: '我的邀请码'
    }
  },
  {
    path: '/docWorkRoom/invite-referral',
    name: 'docWorkRoom.inviteReferral',
    component: inviteReferral,
    meta: {
      title: '修改推荐人名称'
    }
  },
  {
    path: '/docWorkRoom/myServeDocList',
    name: 'docWorkRoom.myServeDocList',
    component: () => import('@/views/docWorkRoom/channel/myServeDocList'),
    meta: {
      title: '我服务的客户'
    }
  },
  {
    path: '/docWorkRoom/servicePackageIntroList',
    name: 'docWorkRoom.servicePackageIntroList',
    component: () => import('@/views/docWorkRoom/channel/servicePackageIntroList'),
    meta: {
      title: '服务包介绍'
    }
  },
  {
    path: '/docWorkRoom/myServeDocDetails',
    name: 'docWorkRoom.myServeDocDetails',
    component: () => import('@/views/docWorkRoom/channel/myServeDocDetails'),
    meta: {
      title: '客户详情'
    }
  },
  {
    path: '/docWorkRoom/myPatients',
    name: 'docWorkRoom.myPatients',
    component: () => import('@/views/docWorkRoom/channel/myPatients'),
    meta: {
      title: '科室管理患者情况'
    }
  },
  {
    path: '/docWorkRoom/myTransferSale',
    name: 'docWorkRoom.myTransferSale',
    component: () => import('@/views/docWorkRoom/channel/myTransferSale'),
    meta: {
      title: '人员转交'
    }
  },
  {
    path: '/docWorkRoom/invite-referral',
    name: 'docWorkRoom.inviteReferral',
    component: inviteReferral,
    meta: {
      title: '修改推荐人名称'
    }
  },
  {
    path: '/docWorkRoom/channel',
    name: 'docWorkRoom.channel',
    component: channel,
    meta: {
      title: '我的渠道'
    }
  },
  {
    path: '/docWorkRoom/channel-company',
    name: 'docWorkRoom.channelCompany',
    component: channelCompany,
    meta: {
      title: '我的渠道'
    }
  },
  {
    path: '/docWorkRoom/channel-order',
    name: 'docWorkRoom.channelOrder',
    component: channelOrder,
    meta: {
      title: '我的订单'
    }
  },
  {
    path: '/docWorkRoom/abnormalRecord',
    name: 'docWorkRoom.abnormalRecord',
    component: ()=> import('@/views/docWorkRoom/abnormalRecord.vue'),
    meta: {
      title: '异常记录'
    }
  },
  {
    path: '/docWorkRoom/bpRecord',
    name: 'docWorkRoom.bpRecord',
    component: ()=> import('@/views/docWorkRoom/houseHoldData/bpRecord.vue'),
    meta: {
      title: '血压记录'
    }
  },
  {
    path: '/docWorkRoom/measurementRecord',
    name: 'docWorkRoom.measurementRecord',
    component: ()=> import('@/views/docWorkRoom/measurementRecord.vue'),
    meta: {
      title: '测量记录'
    }
  },
  {
    path: '/docWorkRoom/bgTrend',
    name: 'docWorkRoom.bgTrend',
    component: bloodglucoseTrend,
    meta: {
      title: '血糖趋势'
    }
  },
  {
    path: '/docWorkRoom/bgRecord',
    name: 'docWorkRoom.bgRecord',
    component: bloodglucoseRecord,
    meta: {
      title: '血糖明细'
    }
  },
  {
    path: '/docWorkRoom/bloodgOxygenSaturationTrend',
    name: 'docWorkRoom.bloodgOxygenSaturationTrend',
    component: bloodgOxygenSaturationTrend,
    meta: {
      title: '血氧饱和趋势'
    }
  },
  {
    path: '/docWorkRoom/bloodgOxygenSaturationRecord',
    name: 'docWorkRoom.bloodgOxygenSaturationRecord',
    component: bloodgOxygenSaturationRecord,
    meta: {
      title: '血氧饱和明细'
    }
  },
  {
    path: '/docWorkRoom/wheezingSound',
    name: 'docWorkRoom.wheezingSound',
    component: wheezingSound,
    meta: {
      title: '哮鸣音明细'
    }
  },
  {
    path: '/docWorkRoom/wheezingTrend',
    name: 'docWorkRoom.wheezingTrend',
    component: wheezingTrend,
    meta: {
      title: '哮鸣音趋势'
    }
  },
  {
    path: '/docWorkRoom/bodyFatIndexTrend',
    name: 'docWorkRoom.bodyFatIndexTrend',
    component: bodyFatIndexTrend,
    meta: {
      title: '体脂指数趋势'
    }
  },
  {
    path: '/docWorkRoom/bodyFatIndexRecord',
    name: 'docWorkRoom.bodyFatIndexRecord',
    component: bodyFatIndexRecord,
    meta: {
      title: '体脂指数明细'
    }
  },
  {
    path: '/docWorkRoom/peakFlowRateRecord',
    name: 'docWorkRoom.peakFlowRateRecord',
    component: peakFlowRateRecord,
    meta: {
      title: '峰流速明细'
    }
  },
  {
    path: '/docWorkRoom/peakFlowRateTrend',
    name: 'docWorkRoom.peakFlowRateTrend',
    component: peakFlowRateTrend,
    meta: {
      title: '峰流速趋势'
    }
  },
  {
    path: '/docWorkRoom/bloodLipidRecord',
    name: 'docWorkRoom.bloodLipidRecord',
    component: bloodLipidRecord,
    meta: {
      title: '血脂明细'
    }
  },
  {
    path: '/docWorkRoom/nebulizedMedicationTrend',
    name: 'docWorkRoom.nebulizedMedicationTrend',
    component: nebulizedMedicationTrend,
    meta: {
      title: '雾化用药趋势'
    }
  },
  {
    path: '/docWorkRoom/nebulizedMedicationRecord',
    name: 'docWorkRoom.nebulizedMedicationRecord',
    component: nebulizedMedicationRecord,
    meta: {
      title: '雾化用药明细'
    }
  },
  {
    path: '/docWorkRoom/stepsTrend',
    name: 'docWorkRoom.stepsTrend',
    component: stepsTrend,
    meta: {
      title: '步数趋势'
    }
  },
  {
    path: '/docWorkRoom/stepsRecord',
    name: 'docWorkRoom.stepsRecord',
    component: stepsRecord,
    meta: {
      title: '步数明细'
    }
  },
  {
    path: '/docWorkRoom/measureResultBg',
    name: 'docWorkRoom.measureResultBg',
    component: measureResultBg,
    meta: {
      title: '血糖测量结果'
    }
  },
  {
    path: '/docWorkRoom/measureResultBp',
    name: 'docWorkRoom.measureResultBp',
    component: measureResultBp,
    meta: {
      title: '血压测量结果'
    }
  },
  {
    path: '/docWorkRoom/companyrevenue',
    name: 'docWorkRoom.companyrevenue',
    component: companyrevenue,
    meta: {
      title: '公司收入'
    }
  },
  {
    path: '/docWorkRoom/innerrevenue',
    name: 'docWorkRoom.innerrevenue',
    component: companyrevenue,
    meta: {
      title: '我的收入'
    }
  },
  {
    path: '/docWorkRoom/personalrevenue',
    name: 'docWorkRoom.personalrevenue',
    component: companyrevenue,
    meta: {
      title: '我的收入'
    }
  },
  {
    path: '/docWorkRoom/immediately',
    name: 'docWorkRoom.immediately',
    component: immediately,
    meta: {
      title: '立即提现'
    }
  },
  {
    path: '/docWorkRoom/employee-view',
    name: 'docWorkRoom.employee-view',
    component: employeeView,
    meta: {
      title: '按员工查看'
    }
  },
  {
    path: '/docWorkRoom/bankcard',
    name: 'docWorkRoom.bankcard',
    component: bankcard,
    meta: {
      title: '支持打款银行'
    }
  },
  {
    path: '/docWorkRoom/deactivateChannel',
    name: 'docWorkRoom.deactivateChannel',
    component: deactivateChannel,
    meta: {
      title: '渠道设置'
    }
  },
  {
    path: '/docWorkRoom/news-list',
    name: 'docWorkRoom.newsList',
    component: newsList,
    meta: {
      title: '新闻动态'
    }
  },
  {
    path: '/docWorkRoom/appHomeOld',
    name: 'docWorkRoom.appHomeOld',
    component: appHomeOld,
    meta: {
      title: '首页'
    }
  },
  {
    path: '/docWorkRoom/projectMain/commonIframe',
    name: 'docWorkRoom.commonIframe',
    component: () => import('@/views/docWorkRoom/projectMain/commonIframe'),
    meta: {
      title: ''
    }
  },
  {
    path: '/docWorkRoom/myShowcase/goodsList',
    name: 'docWorkRoom.goodsList',
    component: () => import('@/views/docWorkRoom/myShowcase/goodsList'),
    meta: {
      title: ''
    }
  },
  {
    path: '/docWorkRoom/goodsDetails',
    name: 'docWorkRoom.goodsDetails',
    component: () => import('@/views/docWorkRoom/myShowcase/goodsDetails'),
    meta: {
      title: ''
    }
  },
  {
    path: '/recipesList',
    name: 'recipesList',
    component: () => import('@/views/recipes/list'),
    meta: {
      title: '今日食谱推荐'
    }
  },
  {
    path: '/recipesDetails',
    name: 'recipesDetails',
    component: () => import('@/views/recipes/details'),
    meta: {
      title: '食物详情'
    }
  },
  {
    path: '/docWorkRoom/consigneeInfo',
    name: 'consigneeInfo',
    component: () => import('@/views/docWorkRoom/channel/consigneeInfo'),
    meta: {
      title: '收货人信息'
    }
  }
]

export default routes
