import sportReport from '@/views/common/report/sportReport.vue'
import weightReport from '@/views/common/report/weightReport.vue'
import bpReport from '@/views/common/report/bpReport.vue'
import bgReport from '@/views/common/report/bgReport.vue'
import patientReport from '@/views/common/report/patientReport.vue'

const routes = [
  {
    path: '/common/report/sport',
    name: 'Common.Report.Sport',
    component: sportReport,
    meta: {
      title: '运动报告'
    }
  },
  {
    path: '/common/report/weight',
    name: 'Common.Report.Weight',
    component: weightReport,
    meta: {
      title: '体重报告'
    }
  },
  {
    path: '/common/report/bp',
    name: 'Common.Report.Bp',
    component: bpReport,
    meta: {
      title: '血压报告'
    }
  },
  {
    path: '/common/report/bg',
    name: 'Common.Report.Bg',
    component: bgReport,
    meta: {
      title: '血糖报告'
    }
  },
  {
    path: '/common/report/patient',
    name: 'Common.Report.Patient',
    component: patientReport,
    meta: {
      title: '患者报告'
    }
  }
]

export default routes
