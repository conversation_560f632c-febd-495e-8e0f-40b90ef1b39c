/*
 * @Descripttion: 
 * @version: 
 * @Author: l<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-09 16:13:01
 * @LastEditors: liuqiannan
 * @LastEditTime: 2021-08-03 11:12:47
 */
import activity from '@/views/common/publish/activity.vue'
import activityDetail from '@/views/common/publish/activityDetail.vue'
import shareActivityDetail from '@/views/common/publish/shareActivityDetail.vue'
import doctor from '@/views/common/publish/doctor.vue'
import doctorList from '@/views/common/publish/doctorList.vue'
import doctorDetail from '@/views/common/publish/doctorDetail.vue'
import notice from '@/views/common/publish/notice.vue'

const routes = [
  {
    path: '/common/publish/activity',
    name: 'Common.Publish.Activity',
    component: activity,
    meta: {
      title: '医生动态'
    }
  },
  {
    path: '/common/publish/activity/detail',
    name: 'Common.Publish.activity.Detail',
    component: activityDetail,
    meta: {
      title: '动态详情'
    }
  },
  {
    path: '/common/publish/activity/shareActivityDetail',
    name: 'Common.Publish.activity.shareActivityDetail',
    component: shareActivityDetail,
    meta: {
      title: '动态详情'
    }
  },
  {
    path: '/common/publish/doctor',
    name: 'Common.Publish.Doctor',
    component: doctor,
    meta: {
      title: ''
    }
  },
  {
    path: '/common/publish/doctor/detail',
    name: 'Common.Publish.Doctor.Detail',
    component: doctorDetail,
    meta: {
      title: '医生介绍'
    }
  },
  {
    path: '/common/publish/doctor/followed',
    name: 'Common.Publish.Doctor.Followed',
    component: doctorList,
    meta: {
      title: '关注的医生'
    }
  },
  {
    path: '/common/publish/notice',
    name: 'Common.Publish.Notice',
    component: notice,
    meta: {
      title: '发布通知'
    }
  }
]

export default routes
