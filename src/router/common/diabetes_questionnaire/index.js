import Q_first from '@/views/common/diabetes_questionnaire/index.vue'
import Q_Second from '@/views/common/diabetes_questionnaire/second.vue'
import home from '@/views/common/diabetes_questionnaire/home.vue'
import success from '@/views/common/diabetes_questionnaire/success.vue'

const routes = [
  {
    path: '/com/diabetes/questionnaire',
    name: 'com.diabetes.questionnaire.index',
    component: home,
    meta: {
      title: '糖尿病神经问卷'
    }
  },
  {
    path: '/com/diabetes/questionnaire/first',
    name: 'com.diabetes.questionnaire.one',
    component: Q_first,
    meta: {
      title: '糖尿病神经问卷'
    }
  },
  {
    path: '/com/diabetes/questionnaire/second',
    name: 'com.diabetes.questionnaire.second',
    component: Q_Second,
    meta: {
      title: '糖尿病神经问卷'
    }
  },
  {
    path: '/com/diabetes/questionnaire/success',
    name: 'com.diabetes.questionnaire.success',
    component: success,
    meta: {
      title: '糖尿病神经问卷'
    }
  }
]

export default routes
