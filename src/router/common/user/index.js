/*
 * @Descripttion:
 * @version:
 * @Author: xulh
 * @Date: 2021-05-24 10:35:49
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2021-07-14 10:17:12
 */
import userHome from '@/views/common/user/home.vue'
import stationHome from '@/views/common/user/stationHome.vue'

import outpatient from '@/views/common/user/outpatient.vue'
import editInfo from '@/views/common/user/editInfo.vue'
import treatmentOptions from '@/views/common/user/treatmentOptions.vue'
import selectOptions from '@/views/common/user/selectOptions.vue'



const routes = [
  {
    path: '/common/user/home',
    name: 'Common.User.Home',
    component: userHome,
    meta: {
      title: '个人主页'
    }
  },
  // 非医护关系个人主页  里面只有简介
  {
    path: '/common/user/stationHome',
    name: 'Common.User.stationHome',
    component: stationHome,
    meta: {
      title: '个人主页'
    }
  },
  {
    path: '/common/user/outpatient',
    name: 'Common.User.Outpatient',
    component: outpatient,
    meta: {
      title: '门诊时间'
    }
  },
  {
    path: '/common/user/edit',
    name: 'Common.User.Edit',
    component: editInfo,
    meta: {
      title: ''//动态去配置头部名称
    }
  }
  , {
    path: '/common/user/treatmentOptions',
    name: 'Common.User.TreatmentOptions',
    component: treatmentOptions,
    meta: {
      title: '治疗方案'
    }
  },
  {
    path: '/common/user/selectOptions',
    name: 'Common.User.selectOptions',
    component: selectOptions,
    meta: {
      title: '选择方案'
    }
  }
]

export default routes
