import questionH5 from '@/views/mmc-question-h5/index.vue'
import questionSurvey from '@/views/mmc-question-h5/survey.vue'
import questionUserInfo from '@/views/mmc-question-h5/userInfo.vue'
import middleStatusPage from '@/views/mmc-question-h5/middleStatusPage.vue'
import notLogin from '@/views/mmc-question-h5/notLogin.vue'
const routes = [
  {
    path: '/mmc/question/index',
    name: 'MMC.questionHome',
    component: questionH5,
    meta: {
      title: 'MMC患者访视问卷'
    }
  },
  {
    path: '/mmc/question/survey',
    name: 'MMC.questionSurvey',
    component: questionSurvey,
    meta: {
      title: 'MMC患者访视问卷'
    }
  },
  {
    path: '/mmc/question/userInfo',
    name: 'MMC.questionUserInfo',
    component: questionUserInfo,
    meta: {
      title: '确认个人信息'
    }
  },
  {
    path: '/mmc/question/middleStatusPage',
    name: 'MMC.middleStatusPage',
    component: middleStatusPage,
    meta: {
      title: 'MMC患者访视问卷'
    }
  },
  {
    path: '/mmc/question/notLogin',
    name: 'MMC.notLogin',
    component: notLogin,
    meta: {
      title: 'MMC患者访视问卷'
    }
  }
]

export default routes
