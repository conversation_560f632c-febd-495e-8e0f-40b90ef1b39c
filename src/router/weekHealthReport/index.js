import bloodReport from '@/views/weekHealthReport/bloodReport.vue'
import bpAtlas from '@/views/weekHealthReport/bpAtlas.vue'
import trendStat from '@/views/weekHealthReport/trendStat.vue'
import trendStatFull from '@/views/weekHealthReport/trendStatFull.vue'
import sugarReport from '@/views/weekHealthReport/sugarReport.vue'
import beforeReport from '@/views/weekHealthReport/beforeList.vue'
import reportDetails from '@/views/weekHealthReport/reportDetails.vue'
import sevenDayBg from '@/views/weekHealthReport/sevenDayBg.vue'
import sevenDayBp from '@/views/weekHealthReport/sevenDayBp.vue'
import sevenDayWheezingRale from '@/views/weekHealthReport/sevenDayWheezingRale.vue'
const routes = [
  {
    path: '/guanjia/trend/stat',
    name: 'MMC.Trend.Stat',
    component: trendStat,
    meta: {
      title: '趋势统计'
    }
  },
  {
    path: '/guanjia/trend/stat/full',
    name: 'MMC.Trend.Stat.Full',
    component: trendStatFull,
    meta: {
      title: '血糖记录'
    }
  },
  {
    path: '/guanjia/bp/atlas',
    name: 'MMC.Bp.Atlas',
    component: bpAtlas,
    meta: {
      title: '血压图谱'
    }
  },
  {
    path: '/blood/report',
    name: 'Blood.Report',
    component: bloodReport,
    meta: {
      title: '血压评估报告'
    }
  },
  {
    path: '/sugar/report',
    name: 'Sugar.Report',
    component: sugarReport,
    meta: {
      title: '血糖评估报告'
    }
  },
  {
    path: '/before/report',
    name: 'Before.Report',
    component: beforeReport,
    meta: {
      title: '往期报告列表'
    }
  },
  {
    path: '/sugar/seven_day',
    name: 'Sugar.seven.Day',
    component: sevenDayBg,
    meta: {
      title: '血糖七天报告'
    }
  },
  {
    path: '/bp/seven_day',
    name: 'bp.seven.Day',
    component: sevenDayBp,
    meta: {
      title: '血压七天报告'
    }
  },
  {
    path: '/wheezing_rale/seven_day',
    name: 'WheezingRale.seven.Day',
    component: sevenDayWheezingRale,
    meta: {
      title: '哮鸣音七天报告'
    }
  },
  {
    path: '/report/details',
    name: 'Report.Details',
    component: reportDetails,
    meta: {
      title: '往期报告'
    }
  }
]

export default routes
