import DxzsHome from '@/views/Questionnaire/dxzs/home.vue'
import Dxzs from '@/views/Questionnaire/dxzs/dxzs.vue'
import TwoTnbHome from '@/views/Questionnaire/twoQuestion/home.vue'
import TwoTnb from '@/views/Questionnaire/twoQuestion/twoQuestion.vue'
import Xxg from '@/views/Questionnaire/xxgQuestion/xxgQuestion.vue'

import GuoshouDxzsHome from '@/views/Questionnaire/guoshou_dxzs/home.vue'
import GuoshouDxzs from '@/views/Questionnaire/guoshou_dxzs/dxzs.vue'

const routes = [
  {
    path: '/guoshou/dxzsHome',
    name: 'guoshou.dxzsHome',
    component: GuoshouDxzsHome,
    meta: {
      title: '代谢指数评估'
    }
  },
  {
    path: '/guoshou/dxzs',
    name: 'guoshou.dxzs',
    component: GuoshouDxzs,
    meta: {
      title: '代谢指数评估'
    }
  },
  {
    path: '/dxzsHome',
    name: 'dxzsHome',
    component: DxzsHome,
    meta: {
      title: '代谢指数评估'
    }
  },
  {
    path: '/dxzs',
    name: 'dxzs',
    component: Dxzs,
    meta: {
      title: '代谢指数评估'
    }
  },
  {
    path: '/twoTnbHome',
    name: 'twoTnbHome',
    component: TwoTnbHome,
    meta: {
      title: '二型糖尿病风险评估'
    }
  },
  {
    path: '/twoTnb',
    name: 'twoTnb',
    component: TwoTnb,
    meta: {
      title: '二型糖尿病风险评估'
    }
  },
  {
    path: '/xxg',
    name: 'xxg',
    component: Xxg,
    meta: {
      title: '心血管疾病评估'
    }
  }
]

export default routes
