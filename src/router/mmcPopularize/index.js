import popularize from '@/views/mmcPopularize/popularize.vue'
import downloadPopularize from '@/views/mmcPopularize/downloadPopularize.vue'
import activity from '@/views/mmcPopularize/activity.vue'
import sugarList from '@/views/mmcPopularize/sugarList.vue'
import maskWxShare from '@/views/mmcPopularize/maskWxShare.vue'
import maskNoticeResult from '@/views/mmcPopularize/maskNoticeResult.vue'

const routes = [
  {
    path: '/mmc/popularize',
    name: 'MMC.Popularize',
    component: popularize,
    meta: {
      title: 'MMC管家下载'
    }
  },
  {
    path: '/download/popularize',
    name: 'Download.Popularize',
    component: downloadPopularize,
    meta: {
      title: 'MMC管家下载'
    }
  },
  {
    path: '/mmc/activity',
    name: 'MMC.Activity',
    component: activity,
    meta: {
      title: 'MMC福利关怀'
    }
  },
  {
    path: '/sugar/list',
    name: 'Sugar.List',
    component: sugarList,
    meta: {
      title: '控糖知识列表'
    }
  },
  {
    path: '/mask/wx/share',
    name: 'Mask.Wx.Share',
    component: maskWxShare,
    meta: {
      title: '微信分享'
    }
  },
  {
    path: '/mask/notice/result',
    name: 'Mask.Notice.Result',
    component: maskNoticeResult,
    meta: {
      title: '通知结果'
    }
  }
]

export default routes
