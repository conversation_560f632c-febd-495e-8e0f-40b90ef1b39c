import CollectUserInfo from '@/views/UserInfo/CollectUserInfo.vue'
import GoalManage from '@/views/UserInfo/GoalManage.vue'
import UserInfoHome from '@/views/UserInfo/UserInfoHome.vue'
import AddKeFu from '@/views/UserInfo/AddKeFu.vue'

const routes = [
  {
    path: '/user/info/collect',
    name: 'MMC.User.Info',
    component: CollectUserInfo,
    meta: {
      title: '完善您的基础信息'
    }
  },
  {
    path: '/user/info/goal',
    name: 'MMC.User.Goal',
    component: GoalManage,
    meta: {
      title: '目标管理'
    }
  },
  {
    path: '/user/info/home',
    name: 'MMC.User.Home',
    component: UserInfoHome,
    meta: {
      title: '首页'
    }
  },
  {
    path: '/user/info/kefu',
    name: 'MMC.User.KeFu',
    component: AddKeFu,
    meta: {
      title: '添加客服'
    }
  }
]

export default routes
