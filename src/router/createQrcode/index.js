import createQrcode from '@/views/createQrcode/createQrcode.vue'
import qrCodeDetails from '@/views/createQrcode/qrCodeDetails.vue'
import omronBox from "@/views/createQrcode/omronBox.vue"

const routes = [
  {
      path: '/create/qrcode',
      name: 'createQrcode',
      component: createQrcode,
      meta: {
          title: '生成二维码'
      }
  },
  {
    path: '/code/details',
    name: 'codeDetails',
    component: qrCodeDetails,
    meta: {
      title: '扫码跳转文章'
    }
  },
  {
    path: '/omron/box',
    name: 'omronBox',
    component: omronBox,
    meta: {
      title: '欧姆龙盒子二维码'
    }
  }
]

export default routes
