import saasreport from '@/views/saasreport/healthReport.vue'
import eyeReport from '@/views/saasreport/eyeReport.vue'
import eyeReportNew from '@/views/saasreport/eyeReportNew.vue'

const routes = [
    {
      path: '/saasreport/healthReport',
      name: 'saasreport.healthReport',
      component: saasreport,
      meta: {
        title: '健康报告'
      }
    },
  {
    path: '/saas/report/eye_report',
    name: 'saas.report.eye_report',
    component: eyeReport,
    meta: {
      title: '眼底报告'
    }
  },
  {
    path: '/saas/report/eye_report_new',
    name: 'saas.report.eye_report_new',
    component: eyeReportNew,
    meta: {
      title: '眼底报告'
    }
  },
  {
    path: '/saasReport/VMCHealthReport',
    name: 'saasReport.VMCHealthReport',
    component: ()=>import('@/views/saasreport/VMCHealthReport.vue'),
    meta: {
      title: '健康报告'
    }
  },
  {
    path: '/saasReport/humanBodyCompositionReport',
    name: 'saasReport.humanBodyCompositionReport',
    component: ()=>import('@/views/saasreport/humanBodyCompositionReport.vue'),
    meta: {
      title: '人体成分分析报告'
    }
  }
  ]

  export default routes
  