// 家庭测量数据
import bgResult from '@/views/newHealthReport/measures/bgResult.vue'
import bpResult from '@/views/newHealthReport/measures/bpResult.vue'
import bmiResult from '@/views/newHealthReport/measures/bmiResult.vue'
import stepResult from '@/views/newHealthReport/measures/stepResult.vue'

// 医院报告数据
import hisSum from '@/views/newHealthReport/his/hisSum.vue'
import hisVisit from '@/views/newHealthReport/his/hisVisit.vue'
import hisTrend from '@/views/newHealthReport/his/hisTrend.vue'
import hisTrendFull from '@/views/newHealthReport/his/hisTrendFull.vue'
import hisArteri from '@/views/newHealthReport/his/hisArteri.vue'

const routes = [
  {
    path: '/guanjia/new/report/measures/bg',
    name: 'MMC.NewReport.BgResult',
    component: bgResult,
    meta: {
      title: '血糖测量结果'
    }
  },
  {
    path: '/guanjia/new/report/measures/bp',
    name: 'MMC.NewReport.BpResult',
    component: bpResult,
    meta: {
      title: '血压测量结果'
    }
  },
  {
    path: '/guanjia/new/report/measures/bmi',
    name: 'MMC.NewReport.BMIResult',
    component: bmiResult,
    meta: {
      title: '体重上传结果'
    }
  },
  {
    path: '/guanjia/new/report/measures/step',
    name: 'MMC.NewReport.StepResult',
    component: stepResult,
    meta: {
      title: '运动上传结果'
    }
  },
  {
    path: '/guanjia/new/report/his/sum',
    name: 'MMC.NewReport.HisSum',
    component: hisSum,
    meta: {
      title: '医院全部指标汇总'
    }
  },
  {
    path: '/guanjia/new/report/his/vist',
    name: 'MMC.NewReport.HisVisit',
    component: hisVisit,
    meta: {
      title: '历史就诊数据'
    }
  },
  {
    path: '/guanjia/new/report/his/trend',
    name: 'MMC.NewReport.hisTrend',
    component: hisTrend,
    meta: {
      title: '核心指标变化趋势'
    }
  },
  {
    path: '/guanjia/new/report/his/trend/full',
    name: 'MMC.NewReport.hisTrendFull',
    component: hisTrendFull,
    meta: {
      title: '核心指标变化趋势'
    }
  },
  {
    path: '/guanjia/new/report/his/arteri',
    name: 'MMC.NewReport.hisArteri',
    component: hisArteri,
    meta: {
      title: '动脉硬化检查报告'
    }
  }
]

export default routes
