import ihecExchangeIndexNew from '@/views/ihecExchange/indexNew.vue'
import ihecExchangeIndex from '@/views/ihecExchange/index.vue'
import ihecExchangeCode from '@/views/ihecExchange/changeCode.vue'
import ihecExchangeCodeV3 from '@/views/ihecExchange/v3/changeCode.vue'
import ihecExchangeSuccess from '@/views/ihecExchange/success.vue'

import ihecOldExchangeIndex from '@/views/ihecExchange_old/index.vue'
import ihecOldExchangeCode from '@/views/ihecExchange_old/changeCode.vue'
import ihecOldExchangeSuccess from '@/views/ihecExchange_old/success.vue'

const routes = [
  {
    path: '/ihec/new/exchage/index',
    name: 'ihec.new.exchage.index',
    component: ihecExchangeIndex,
    meta: {
      title: '兑换优惠券'
    }
  },
  {
    path: '/ihec/new/exchage/changeCode',
    name: 'ihec.new.exchage.changeCode',
    component: ihecExchangeCode,
    meta: {
      title: '兑换权益'
    }
  },
  {
    path: '/ihec/new/exchage/success',
    name: 'ihec.new.exchage.success',
    component: ihecExchangeSuccess,
    meta: {
      title: '兑换成功'
    }
  },
  {
    path: '/ihec/exchage/index',
    name: 'ihec.exchage.index',
    component: ihecOldExchangeIndex,
    meta: {
      title: '兑换优惠券'
    }
  },
  {
    path: '/ihec/exchage/changeCode',
    name: 'ihec.exchage.changeCode',
    component: ihecOldExchangeCode,
    meta: {
      title: '兑换权益'
    }
  },
  {
    path: '/ihec/exchage/success',
    name: 'ihec.exchage.success',
    component: ihecOldExchangeSuccess,
    meta: {
      title: '兑换成功'
    }
  },
  {
    path: '/ihec/v3/exchage/index',
    name: 'ihec.v3.exchage.index',
    component: ihecExchangeIndexNew,
    meta: {
      title: '兑换优惠券'
    }
  },
  {
    path: '/ihec/v3/exchage/changeCode',
    name: 'ihec.v3.exchage.changeCode',
    component: ihecExchangeCodeV3,
    meta: {
      title: '兑换权益'
    }
  },
  {
    path: '/ihec/v3/exchage/success',
    name: 'ihec.v3.exchage.success',
    component: ihecExchangeSuccess,
    meta: {
      title: '兑换成功'
    }
  }
]

export default routes
