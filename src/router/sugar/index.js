import Sugar from '@/views/Sugar/Sugar.vue'
import SugarNew from '@/views/Sugar/SugarNew.vue'
import SugarM90 from '@/views/Sugar/SugarM90.vue'
import WeightManage from '@/views/Sugar/WeightManage.vue'
import M90WeightManage from '@/views/Sugar/WeightManageM90.vue'
import WeightManageNew from '@/views/Sugar/WeightManageNew.vue'
const routes = [
  {
    path: '/sugar/manage',
    name: 'MMC.Sugar',
    component: Sugar,
    meta: {
      title: '控糖强化训练营'
    }
  },
  {
    path: '/new/sugar/manage',
    name: 'New.MMC.Sugar',
    component: SugarNew,
    meta: {
      title: 'MMC控糖助手'
    }
  },
  {
    path: '/m90/sugar/manage',
    name: 'm90.MMC.Sugar',
    component: SugarM90,
    meta: {
      title: '血糖列表'
    }
  },
  {
    path: '/weight/manage',
    name: 'MMC.WeightManage',
    component: WeightManage,
    meta: {
      title: '体重管理'
    }
  },
  {
    path: '/m90/weight/manage',
    name: 'm90.MMC.WeightManage',
    component: M90WeightManage,
    meta: {
      title: '体重管理'
    }
  },
  {
    path: '/new/weight/manage',
    name: 'New.MMC.WeightManage',
    component: WeightManageNew,
    meta: {
      title: '体重管理'
    }
  }
]

export default routes
