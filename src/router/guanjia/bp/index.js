import home from '@/views/guanjia/bp/info.vue'
import videoShare from '@/views/guanjia/video/index.vue'
import friendShare from '@/views/guanjia/video/friend_share.vue'
const routes = [
  {
    path: '/gj_app/bp/info',
    name: 'gj_app.bp.info',
    component: home,
    meta: {
      title: '血压记录'
    }
  },
  {
    path: '/gj_app/video/share/info',
    name: 'gj_app.video.share.info',
    component: videoShare,
    meta: {
      title: '视频分享'
    }
  },
  {
    path: '/gj_app/friend/share/info',
    name: 'gj_app.friend.share.info',
    component: friendShare,
    meta: {
      title: '好友分享'
    }
  }
]

export default routes
