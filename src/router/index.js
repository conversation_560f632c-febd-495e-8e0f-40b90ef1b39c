import Vue from 'vue'
import Router from 'vue-router'
import reportRoutes from './report'
import newReportRoutes from './newHealthReport'
import riskReport from './riskReport'
import surveyRoutes from './survey'
import userInfoRoutes from './userInfo'
import sugarRoutes from './sugar'
import newSurveyRoutes from './newSurvey'
import newsRoutes from './news'
import mmcRoutes from './mmcPopularize'
import imgPage from './imgPage'
import weekHealthReport from './weekHealthReport'
import noticeDetail from './noticeDetail'
import questionnaireRoutes from './Questionnaire'
import measureRecord from './measureRecord'
import test from './test'
import questionDetail from './questionDetail'
import drugRemind from './drugRemind'
import help from './help/help'
import weekReport from './weekReport'
import cookBook from './cookBook'
import ihecExchange from './ihecExchange'
import createQrcode from './createQrcode'
import jumpQuestion from './jumpMmcQuestion'
import redeemCode from './redeemCode'
import getCode from './guanjia/getCode'

import commonPublish from './common/publish'
import commonUser from './common/user'
import commonReport from './common/report'
import commonDiabetesQuestionnaire from './common/diabetes_questionnaire'
import commonNotice from './common/notice'
import servicePackage from './servicePackage/index'
import wrxy from './wrxy/index'
import disconnection from './disconnection/disconnection'
import operation from './operationReport/index'
import docWorkRoom from './docWorkRoom/index'
import m90WeekReport from './m90WeekReport'

import saas from './saas/index'
import bpIndex from './guanjia/bp/index'
import hospTheory from './hosp_theory/hosp_theory'
import docSuggest from './common/docSuggestion'
import gjIntroduce from './common/gj_introduce'
import saasreport from './saasreport'
import dataCenter from './dataCenter'
import mmcReport from './mmcReport'
import breathReport from './common/breathReport'
import reportCenter from './reportCenter'

// mmc问卷
import mmcQuestionnaire from './mmc-questionnaire'

// mmc患者病例
import MMCMedicalRecord from './mmc-medical-record'

//mmc-h5问卷
import mmcQuestionH5 from './mmc-question-h5'

import ihecMiniwechat from './ihecMiniwechat/ihecMiniwechat'

import scanRedirectMp from './scanRedirectMp/scanRedirectMp'

Vue.use(Router)
const routes = [
  ...reportRoutes,
  ...surveyRoutes,
  ...userInfoRoutes,
  ...sugarRoutes,
  ...newsRoutes,
  ...newSurveyRoutes,
  ...mmcRoutes,
  ...imgPage,
  ...questionnaireRoutes,
  ...measureRecord,
  ...questionDetail,
  ...noticeDetail,
  ...newReportRoutes,
  ...riskReport,
  ...weekHealthReport,
  ...test,
  ...drugRemind,
  ...help,
  ...weekReport,
  ...cookBook,
  ...ihecExchange,
  ...createQrcode,
  ...jumpQuestion,
  ...servicePackage,
  ...redeemCode,
  ...commonPublish,
  ...commonUser,
  ...commonReport,
  ...getCode,
  ...wrxy,
  ...operation,
  ...docWorkRoom,
  ...saas,
  ...commonDiabetesQuestionnaire,
  ...disconnection,
  ...commonNotice,
  ...m90WeekReport,
  ...bpIndex,
  ...hospTheory,
  ...docSuggest,
  ...gjIntroduce,
  ...saasreport,
  ...dataCenter,
  ...mmcReport,
  ...breathReport,
  ...reportCenter,
  ...mmcQuestionnaire,
  ...ihecMiniwechat,
  ...MMCMedicalRecord,
  ...mmcQuestionH5,
  ...scanRedirectMp,
]

const router = new Router({
  mode: 'history',
  scrollBehavior(to, from) {
    if (to.name === 'MMCMedicalRecord') {
      return { x: 0, y: 0 }
    }
  },
  routes
})

router.beforeEach(function (to, from, next) {
  // console.log(to);
  // if (to) { 
  //  // localStorage.clear()
  // }

  if (to.meta.title) {
    document.title = to.meta.title
  }

  if (to.fullPath.indexOf('appId') > -1) {
    localStorage.setItem('appInfo', '{"zz_app_id": "' + to.query['appId'] + '", "zz_app_name": "' + to.query['appName'] + '", "zz_app_version":"' + to.query['appVersion'] + '"}')
  }

  if (to.fullPath.indexOf('authorization') > -1) {
    localStorage.setItem('authorization', to.query['authorization'])
  }

  if (to.fullPath.indexOf('workroom_id') > -1) {
    localStorage.setItem('workroom_id', to.query['workroom_id'])
  }

  if (to.fullPath.indexOf('access-token') > -1) {
    localStorage.setItem('access-token', to.query['access-token'].replace(/ /g, '+'))
  }

  if (to.query['room_id']) {
    localStorage.setItem('saas_room_id', to.query['room_id'])
  }

  if (to.fullPath.indexOf('project_id') > -1) {
    localStorage.setItem('docworkroom_project_id', to.query['project_id'])
  }

  // mmc
  // if (to.fullPath.indexOf('access_token') > -1) {
  //   localStorage.setItem('access_token', to.query['access_token'])
  // }

  if (to.fullPath.indexOf('source_type') > -1) {
    localStorage.setItem('source_type', to.query['source_type'])
  }

  // console.log(to.path + ' == ' + location.pathname)
  if ((to.path !== location.pathname) && location.pathname.length < 50) {
    if(to.path.indexOf('medication') == -1){
      location.assign(to.fullPath)
    }else{
      next()
    }
  } else {
    next()
  }
})

export default router
