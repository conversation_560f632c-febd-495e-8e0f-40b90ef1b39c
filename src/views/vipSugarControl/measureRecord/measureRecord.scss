.mainBox{
  display: flex;
  flex-direction: column;
}
.table{
  display: flex;
  align-items: center;
  justify-content: space-around;
}
.tabItem{
  display: flex;
  align-items: center;
  height: 55px;
  width: 63px;
  color: #333;
  border-bottom: 1px #FFF solid;
}
.tabItem1{
  display: flex;
  align-items: center;
  height: 55px;
  width: 63px;
  color: #FF7C35;
  border-bottom: 1px #FF7C35 solid;
}
.tabIconOne{
  width: 14px;
  height: 17px;
}
.tabIconTwo{
  width: 20px;
  height: 18px;
}
.tabIconThree{
  width: 19px;
  height: 17px;
}
.tabIconFour{
  width: 18px;
  height: 17px;
}
.tabItem span, .tabItem1 span{
  font-size: 17px;
  font-weight: 500;
  margin-left: 5px;
}
.contentBox{
  margin-top: 13px;
}
.tips{
  width: 375px;
  height: 90px;
  background: #f7f7f7;
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
.tips1{
  height: 120px;
}
.tips span{
  color: #666;
  font-size: 15px;
}
.tips div{
  color: #333;
  font-size: 16px;
  margin-top: 16px;
}
.listBox{
  display: flex;
  flex-direction: column;
}
.list{
  margin-top: 32px;
  padding: 0 10px;
  width: 355px;
}
.listDate{
  color: #333;
  font-size: 16px;
  text-align: left;
}
.listItem{
  display: flex;
  align-items: center;
  height: 100px;
  justify-content: space-around;
}
.result{
  font-size: 20px;
  width: 60px;
  color: #07C160;   //正常颜色
}
.resultNum{
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.num{
  font-size: 23px;
  color: #07C160;
  margin-bottom: 9px;
}
.resultNum span{
  color: #999;
  font-size: 15px;
}
.measureType{
  color: #333;
  font-size: 16px;
}
.timeSlot{
  color: #333;
  font-size: 16px;
}
.measureTime{
  font-size: 15px;
  color: #999;
}
.numBox{
  display: flex;
  font-size: 23px;
  color: #333;
  margin-bottom: 9px;
}
.motionBox{
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 319px;
  margin: 0 auto;
  height: 60px;
  border-bottom: 1px #e6e6e6 solid;
  padding: 0 18px;
}
.motionNum{
  font-size: 23px;
  color: #333;
}
.motionTime{
  color: #999;
  font-size: 15px;
}
.noDataBox{
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.noDataBox img{
  width: 164px;
  height: 193px;
  margin-top: 66px;
  margin-bottom: 21px;
}
.noDataBox div{
  font-size: 17px;
  color: #333;
  margin-bottom: 13px;
  line-height: 24px;
}
.noDataBox span{
  font-size: 15px;
  color: #999;
  line-height: 24px;
}








.highColor{
  color: #F5222D!important;   //偏高和肥胖颜色
}
.highestColor{
  color: #E30000!important;   //很高颜色
}
.overweight{
  color: #F19C0E!important;   //超重颜色
}
.lowColor{
  color: #7B62FF!important;   //偏低颜色
}
.lowerColor{
  color: #517DFF!important;   //低血糖和严重低血糖颜色
}