.mian{
  display: flex;
  flex-direction: column;
}
.headBox{
  width: 355px;
  height: 77px;
  background: url("../image/gzhBg.png");
  background-size: cover;
  margin: 0 auto;
  margin-top: 20px;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.headMid{
  display: flex;
  flex-direction: column;
  margin-right: 30px;
}
.headMid div{
  color: #222;
  font-size: 17px;
  margin-bottom: 3px;
  line-height: 24px;
}
.headMid span{
  color: #666;
  font-size: 14px;
  line-height: 20px;
}
.btn{
  width: 58px;
  height: 32px;
  line-height: 32px;
  text-align: center;
  color: #fff;
  font-size: 17px;
  background: #F3955F;
  border-radius: 2px;
  margin-right: 10px;
}
.tips{
  font-size: 17px;
  color: #222;
  line-height: 26px;
  text-align: left;
  padding: 0 15px;
}
.tips span{
  color: #FF7C35;
}