<template>
    <div class="main">
      <div class="headBox">
        <div class="headMid">
          <div>关注“MMC管家”公众号</div>
          <span>获得亲友测量报告的通知权限</span>
        </div>
        <div class="btn">关注</div>
      </div>
      <div class="tips">
        扰哥，您好！您已经与 <span>微风徐徐</span>  绑定为亲友关系，对方血糖、血压测量数据异常时会微信通知您。
      </div>
    </div>
</template>

<script>

  export default {
    data() {
      return {
      }
    },
    watch: {

    },
    created() {

    },
    methods: {
    }
  }
</script>

<style lang="scss" scoped>
  @import "style.scss";
</style>
