<template>
  <div class='mainBox'>
    <div class='table'>
      <div :class="tabItem == 0?'tabItem1':'tabItem'" @click='changeTabType(0)'>
        <img v-show="tabItem == 0" src='../image/<EMAIL>' class='tabIconOne'>
        <img v-show="tabItem != 0" src='../image/bloodSuger_grey.png' class='tabIconOne'>
        <span>血糖</span>
      </div>
      <div :class="tabItem == 1?'tabItem1':'tabItem'" @click='changeTabType(1)'>
        <img v-show="tabItem == 1" src='../image/<EMAIL>' class='tabIconTwo'>
        <img v-show="tabItem != 1" src='../image/bloodPressure_grey.png' class='tabIconTwo'>
        <span>血压</span>
      </div>
      <div :class="tabItem == 2?'tabItem1':'tabItem'" @click='changeTabType(2)'>
        <img v-show="tabItem == 2" src='../image/<EMAIL>' class='tabIconThree'>
        <img v-show="tabItem != 2" src='../image/step_grey.png' class='tabIconThree'>
        <span>步数</span>
      </div>
      <div :class="tabItem == 3?'tabItem1':'tabItem'" @click='changeTabType(3)'>
        <img v-show="tabItem == 3" src='../image/<EMAIL>' class='tabIconFour'>
        <img v-show="tabItem != 3" src='../image/BMI_grey.png' class='tabIconFour'>
        <span>BMI</span>
      </div>
    </div>
    <div class="contentBox">
      <div class="content" v-show="tabItem==0">
        <div class="tips">
          <span>血糖正常范围</span>
          <div>空腹：4.4--7.0mmol/L 非空腹：4.4--10.0mmol/L</div>
        </div>
        <div v-if="bsArr.length!=0" class="listBox">
          <div class="list" v-for="(item, index) in bsArr" :key="index">
            <div class="listDate">{{item.date}}</div>
            <div class="listItem" v-for="(item1, index) in item.data" :key="index">
              <div class="result">{{item1.bg_type}}</div>
              <div class="resultNum">
                <div class="num">{{item1.bg}}</div>
                <span>mmol/L</span>
              </div>
              <div class="measureType">{{item1.measure_type_desc}}</div>
              <div class="timeSlot">{{item1.dining_status}}</div>
              <div class="measureTime">{{item1.time}}</div>
            </div>
          </div>
        </div>
        <div v-if="bsArr.length==0" class="noDataBox">
          <img src="../image/kongshuju.png" alt="">
          <div>暂时没有血糖数据呢</div>
          <span>可以提醒TA积极控糖</span>
        </div>
      </div>
      <div class="content" v-show="tabItem==1">
        <div class="tips">
          <span>血压正常范围</span>
          <div>收缩压：<135mmHg； 舒张压：<85mmHg</div>
        </div>
        <div v-if="bpArr.length!=0" class="listBox">
          <div class="list" v-for="(item, index) in bpArr" :key="index">
            <div class="listDate">{{item.group_time}}</div>
            <div class="listItem" v-for="(item1, index) in item.data" :key="index">
              <div class="result" >{{item1.normal}}</div>
              <div class="resultNum">
                <div class="numBox">
                  <div class="sbp" >{{item1.sbp}}</div>/
                  <div class="dbp" >{{item1.dbp}}</div>
                </div>
                <span>mmHg</span>
              </div>
              <div class="resultNum">
                <div class="num">{{item1.pulse}}</div>
                <span>bpm</span>
              </div>
              <div class="measureTime">{{item1.time}}</div>
            </div>
          </div>
        </div>
        <div v-if="bpArr.length==0" class="noDataBox">
          <img src="../image/kongshuju.png" alt="">
          <div>暂时没有血压数据呢</div>
          <span>可以提醒TA积极控糖</span>
        </div>
      </div>
      <div class="content" v-show="tabItem==2">
        <div v-if="motionArr.length!=0" class="listBox">
          <div class="motionBox" v-for="(item, index) in motionArr" :key="index">
            <div class="motionNum">{{item.step}}</div>
            <div class="motionTime">{{item.motion_at}}</div>
          </div>
        </div>
        <div v-if="motionArr.length==0" class="noDataBox">
          <img src="../image/kongshuju.png" alt="">
          <div>暂时没有步数数据呢</div>
          <span>可以提醒TA积极控糖</span>
        </div>
      </div>
      <div class="content" v-show="tabItem==3">
        <div class="tips tips1">
          <span>BMI正常范围</span>
          <div>消瘦：BMI<18.5 超重：24≤BMI<28</div>
          <div>正常：18.5≤BMI<24 肥胖：28≤BMI</div>
        </div>
        <div v-if="bmiArr.length!=0" class="list" v-for="(item, index) in bmiArr" :key="index">
          <div class="listDate">{{item.group_time}}</div>
          <div class="listItem" v-for="(item1, index) in item.data" :key="index">
            <div class="result" v-if="item1.normal==1">{{item1.normal_text}}</div>
            <div class="result overweight" v-if="item1.normal==2">{{item1.normal_text}}</div>
            <div class="result lowColor" v-if="item1.normal==3">{{item1.normal_text}}</div>
            <div class="result highColor" v-if="item1.normal==4">{{item1.normal_text}}</div>
            <div class="resultNum">
              <div class="num">{{item1.weight}}</div>
              <span>kg</span>
            </div>
            <div class="resultNum">
              <div class="num">{{item1.height}}</div>
              <span>cm</span>
            </div>
            <div class="resultNum">
              <div class="num" v-if="item1.normal==1">{{item1.bmi}}</div>
              <div class="num overweight" v-if="item1.normal==2">{{item1.bmi}}</div>
              <div class="num lowColor" v-if="item1.normal==3">{{item1.bmi}}</div>
              <div class="num highColor" v-if="item1.normal==4">{{item1.bmi}}</div>
              <span>kg/m²</span>
            </div>
            <div class="measureTime">{{item1.time}}</div>
          </div>
        </div>
        <div v-if="bmiArr.length==0" class="noDataBox">
          <img src="../image/kongshuju.png" alt="">
          <div>暂时没有BMI数据呢</div>
          <span>可以提醒TA积极控糖</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import { getBsList2, getBpList2, getMotionList2, getWeightList2 } from '@/api/vipSugarControl'
  import { getClientHeight, getScrollTop, getScrollHeight } from '@/utils/getHight.js'
  export default {
    data() {
      return {
        tabItem: 0,
        bsArr: [],
        bpArr: [],
        motionArr: [],
        bmiArr: [],
        user_id: '',
        measureList: [],
        pageNo: 1,
        pageSize: 10,
        totalPage: 1,
        isClientBottom: false,
        isLoading: false
      }
    },
    watch: {
      isClientBottom(newVal, oldVal) {
        // 滑倒底部并且还有更多报告时，以及不在加载中（防止重复加载）
        if (newVal) {
          // 获取往期报告列表
          let pageNo = parseInt(this.pageNo)
          let totalPage = parseInt(this.totalPage)
          if (pageNo < totalPage) {
            this.pageNo = this.pageNo + 1
            this.init(this.tabItem, this.pageNo)
          }
        }
      }
    },
    mounted() {
      // 判断是否滑到底部
      this.checkScroll()
    },
    created() {
      this.tabItem = parseInt(this.$route.query.type)
      this.init(this.tabItem)
    },
    methods: {
      init(type = 0, PageNo = 1) {
        this.user_id = this.$route.query.user_id
        if (type === 0) {
          this.getBsData(this.pageSize, this.pageNo)
        } else if (type === 1) {
          this.getBpData(this.pageSize, this.pageNo)
        } else if (type === 2) {
          this.getMotionData(this.pageSize, this.pageNo)
        } else if (type === 3) {
          this.getWeightData(this.pageSize, this.pageNo)
        }
      },
      getBsData(pageSize, pageNo) {
        let that = this
        getBsList2({
          'user_id': that.user_id,
          'page_size': pageSize,
          'page_no': pageNo
        }).then(res => {
          let data = res.data
          let measureList = this.measureList
          this.measureList = measureList.concat(data.measure_data)
          this.bsArr = this.formatList(this.measureList, 'date')
          this.totalPage = data.total_page
          this.pageNo = data.current_page
        })
      },
      getBpData(pageSize, pageNo) {
        let that = this
        getBpList2({
          'user_id': that.user_id,
          'page_size': pageSize,
          'page_no': pageNo
        }).then(res => {
          let data = res.data
          let measureList = this.measureList
          this.measureList = measureList.concat(data.measure_data)
          this.bpArr = this.formatList(this.measureList, 'group_time')
          this.totalPage = data.total_page
          this.pageNo = data.current_page
        })
      },
      getMotionData(pageSize, pageNo) {
        let that = this
        getMotionList2({
          'user_id': that.user_id,
          'page_size': pageSize,
          'page_no': pageNo,
          'product_id': 15
        }).then(res => {
          this.motionArr = this.motionArr.concat(res.data.motion_data)
          this.totalPage = res.data.total_page
          this.pageNo = res.data.current_page
        })
      },
      getWeightData(pageSize, pageNo) {
        let that = this
        getWeightList2({
          'user_id': that.user_id,
          'page_size': pageSize,
          'page_no': pageNo
        }).then(res => {
          let data = res.data
          let measureList = this.measureList
          this.measureList = measureList.concat(data.data)
          this.bmiArr = this.formatList(this.measureList, 'group_time')
          this.totalPage = data.total_page
          this.pageNo = data.current_page
        })
      },
      formatList(measureList, key) {
        let map = {}
        let dest = []
        for (let i = 0; i < measureList.length; i++) {
          let ai = measureList[i]
          if (!map[ai[key]]) {
            dest.push({
              [key]: ai[key],
              data: [ai]
            })
            map[ai[key]] = ai
          } else {
            for (let j = 0; j < dest.length; j++) {
              let dj = dest[j]
              if (dj[key] == ai[key]) {
                dj.data.push(ai)
                break
              }
            }
          }
        }
        return dest
      },
      changeTabType: function (e) {
        this.tabItem = e
        this.measureList = []
        this.pageNo = 1
        this.init(this.tabItem)
      },
      checkScroll() {
        let scrollTop = getScrollTop()
        window.onscroll = () => {
          let newSrollTop = getScrollTop() // 当前滚动高度
          let scrollHeight = getScrollHeight() // 文档完整的高度
          let clientHeight = getClientHeight() // 可视范围的高度
          let distance = 2 // 底部间距

          // 如果当前滚动高度大于之前滚动高度 -> 下滑
          if (newSrollTop > scrollTop) {
            // 如果到底部 this.isClientBottom = true；如果不在底部 this.isClientBottom = false
            if (newSrollTop + clientHeight + distance < scrollHeight) {
              // console.log('没到底')
              this.isClientBottom = false
            } else {
              // console.log('到底了')
              this.isClientBottom = true
            }
          } else {
            this.isClientBottom = false
          }

          // 每次滚动后，把当前滚动高度赋值给之前滚动高度
          scrollTop = newSrollTop
        }
      }

    }
  }
</script>

<style lang="scss" scoped>
  @import "measureRecord.scss";
</style>
