<template>
  <div @click="goUrl" class="tips">
    <p class="tips-text">{{ title }}</p>
    <img src="~/@/assets/images/newHealthReport/his-sum-tips-oval.png" class="tips-oval" >
  </div>
</template>

<script>
export default {
  data: () => {
    return {}
  },
  props: {
    title: {
      type: String,
      default: ''
    },
    url: {
      type: String,
      default: ''
    }
  },
  methods: {
    goUrl() {
      window.location.href = this.url
    }
  }
}
</script>

<style lang="scss" scoped>
.tips {
  height: 75px;
  display: flex;
  padding: 0 10px;
  border-radius: 9px;
  align-items: center;
  justify-content: space-between;
  background: url('~@/assets/images/newHealthReport/his-sum-tips-bg.png') no-repeat;
  background-size: cover;

  .tips-text {
    width: 80%;
    color: #666;
    font-size: 16px;
    text-align: left;
    font-weight: 400;
    line-height: 24px;
  }

  .tips-oval {
    width: 18px;
  }
}
</style>
