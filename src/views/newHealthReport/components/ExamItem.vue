<template>
  <div class="group" v-if="(itemKey === 'jiancha_imgs' &&itemData.length>0)||itemKey !== 'jiancha_imgs'">
    <div :class="{'header': true, 'header-unchecked': itemData.has_data === '0'}">
      <span v-if="itemData.subquestion_name" class="title before_is_new_icon"><img class="is_new_icon" v-if="itemData.is_new" src="./../../../assets/images/<EMAIL>">{{ title }}{{ itemData.subquestion_name }}</span>
      <div
        v-if="itemData.has_chart"
        @click="goChart"
        class="chart"
      >
        <cc-svg-icon icon-class="line-chart" class="line-chart"/>
        <span>图表</span>
      </div>
      <!-- 动脉硬化检查报告 -->
      <div
        v-if="itemKey === 'jiancha_imgs' &&itemData.length>0"
        @click="checkArteriosclerosis"
        class="check"
      >
        <div>
          <p class="check-text">动脉硬化检查</p>
          <p class="check-text">报告<img src="~/@/assets/images/newHealthReport/his-sum-tips-oval.png"
                                       class="check-arrow"></p>
        </div>
        <img src="../images/report.png" class="check-img">
      </div>
    </div>
    <div v-if="itemData.has_data === '1'" class="value">
      <div class="value-up">
        <span :class="{'value-num': true, 'value-num-abnormal': itemData.conclusion}"> {{ itemData.answer_name }}</span>
        <span class="value-unit">{{ itemData.unit }}</span>
      </div>
      <span class="value-status">{{ itemData.conclusion }}</span>
    </div>
    <div v-if="itemData.has_data === '1' && itemData.reference" class="range">
      <span>参考范围：{{ itemData.reference }}</span>
    </div>
    <div v-if="itemData.has_data === '1' && itemData.visit_at" class="date">
      <span>检查时间：{{ itemData.visit_at }}</span>
    </div>
    <div v-if="itemData.has_data === '0'" class="unchecked">
      <cc-svg-icon icon-class="cry-face" class="unchecked-cry"/>
      <span>未检查</span>
    </div>
  </div>
</template>

<script>
  export default {
    data: () => {
      return {}
    },
    props: {
      itemData: {
        type: Object,
        required: true
      },
      itemKey: String
    },
    methods: {
      /**
       * 查看图表
       */
      goChart() {
        this.$router.push({
          path: '/guanjia/new/report/his/trend',
          query: {
            code: this.itemKey
          }
        })
      },
      /**
       * 查看动脉硬化检查报告
       */
      checkArteriosclerosis() {
        this.$router.push({
          path: '/guanjia/new/report/his/arteri',
          query: {
            imgs: this.itemData
          }
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  .group {
    width: 100%;
    padding: 20px 0;

    .header {
      color: #333;
      display: flex;
      line-height: 30px;
      align-items: center;
      justify-content: space-between;

      .title {
        flex: 1;
        font-size: 20px;
        text-align: left;
        font-weight: 400;
      }

      .chart {
        width: 68px;
        height: 30px;
        display: flex;
        font-size: 15px;
        font-weight: 400;
        margin-left: 8px;
        color: #EE7800;
        border-radius: 5px;
        align-items: center;
        justify-content: center;
        background: linear-gradient(180deg, #FFF7F1 0%, #FFE5CB 100%);

        .line-chart {
          width: 16px;
          height: 14px;
          margin-right: 4px;
        }
      }

      .check {
        width: 100%;
        height: 75px;
        display: flex;
        padding-left: 15px;
        border-radius: 9px;
        align-items: center;
        box-sizing: border-box;
        justify-content: space-between;
        background: linear-gradient(180deg, rgba(255, 247, 241, 1) 0%, rgba(255, 236, 222, 1) 100%);

        .check-text {
          font-size: 17px;
          font-weight: 400;
          color: #EE7800;
          text-align: left;
          line-height: 22px;

          .check-arrow {
            width: 18px;
            height: 18px;
            margin-left: 4px;
            vertical-align: middle;
          }
        }

        .check-img {
          width: 106px;
        }
      }
    }

    .header-unchecked {
      color: #999;
    }

    .value {
      display: flex;
      margin-top: 10px;
      align-items: baseline;
      justify-content: space-between;

      .value-up {
        text-align: left;
      }

      .value-num {
        color: #222;
        font-size: 30px;
        font-weight: 400;
        overflow-wrap: anywhere;
      }

      .value-num-abnormal {
        color: #EF001D;
      }

      .value-unit {
        color: #333;
        font-size: 15px;
        font-weight: 400;
        margin-left: 10px;
      }

      .value-status {
        font-size: 15px;
        color: #EF001D;
        text-align: left;
        font-weight: 400;
        line-height: 20px;
      }
    }

    .date,
    .range {
      color: #999;
      font-size: 15px;
      text-align: left;
      font-weight: 400;
      margin-top: 10px;
    }

    .unchecked {
      color: #CCC;
      display: flex;
      font-size: 18px;
      margin-top: 10px;
      text-align: left;
      font-weight: 400;
      align-items: center;
      justify-content: flex-start;

      .unchecked-cry {
        width: 20px;
        height: 20px;
        margin-right: 2px;
      }
    }
  }
  .before_is_new_icon{
    position: relative;
  }
  .is_new_icon{
      width: 22px;
      height: 22px;
      position: absolute;
      top: -18px;
  }
</style>
