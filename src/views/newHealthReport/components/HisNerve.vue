<template>
  <div class="his-nerve">
    <his-tips
      v-if="tipsTitle !== ''"
      :title="tipsTitle"
      :url="tipsUrl"
    />
    <p v-if="checkDate" class="eye-date">检查时间：{{ checkDate }}</p>
    <div v-if="nerve" class="nerve-end">
      <his-title
        :name="'结论'"
        :imgSrc="require('../images/<EMAIL>')"
      />
      <p class="nerve-text">{{ nerve }}</p>
    </div>
    <div v-else class="unchecked">
      <cc-svg-icon icon-class="cry-face" class="unchecked-cry"/>
      <span>未检查</span>
    </div>
  </div>
</template>

<script>
import HisTips from '../components/HisTips.vue'
import HisTitle from '../components/HisTitle.vue'
import { getMetricsDetail } from '@/api/newHealthReport.js'

export default {
  data: () => {
    return {
      metricsCode: '9',
      checkDate: '',
      nerve: '',
      tipsTitle: '',
      tipsUrl: ''
    }
  },
  props: {
    propData: {
      type: Object,
      default: () => {
        return {
          param: {
            code: 0
          },
          metrics: {}
        }
      }
    }
  },
  components: {
    'his-tips': HisTips,
    'his-title': HisTitle
  },
  created() {
    // 初始化
    this.init()
  },
  methods: {
    /**
     * 初始化
     */
    init() {
      let code = 0
      if (this.propData.param && this.propData.param.code) {
        code = this.propData.param.code
      }
      if (code === this.metricsCode) {
        // 详细数据处理
        this.handleData(this.propData)
      } else {
        // 获取详细
        this.getDetail()
      }
    },
    /**
     * 获取详细
     */
    getDetail() {
      getMetricsDetail(this.metricsCode).then(res => {
        if (res.status === 0) {
          // 详细数据处理
          this.handleData(res.data)
          // 保存详细数据
          this.$emit('saveData', this.metricsCode, res.data)
        } else {
          this.$toast(res.msg)
        }
      })
    },
    /**
     * 详细数据处理
     */
    handleData(data) {
      let { metrics, param } = data
      let oData = metrics['904033'] || {}
      this.nerve = oData.conclusion || ''
      this.checkDate = oData.visit_at || ''
      let title = param.title || {}
      this.tipsTitle = title.title || ''
      this.tipsUrl = title.url || ''
    }
  }
}
</script>

<style lang="scss" scoped>
.his-nerve {
  width: 100%;

  .eye-date {
    color: #999;
    font-size: 15px;
    margin-top: 15px;
    text-align: left;
    font-weight: 400;
    line-height: 20px;
  }

  .nerve-end {
    margin-top: 15px;

    .nerve-text {
      color: #333;
      font-size: 18px;
      margin-top: 5px;
      font-weight: 400;
      text-align: left;
      text-indent: 30px;
      line-height: 30px;
      word-break: break-all;
    }
  }

  .unchecked {
    color: #CCC;
    display: flex;
    font-size: 18px;
    margin-top: 10px;
    text-align: left;
    font-weight: 400;
    align-items: center;
    justify-content: flex-start;

    .unchecked-cry {
      width: 20px;
      height: 20px;
      margin-right: 2px;
    }
  }
}
</style>
