<template>
  <div v-if="isEmpty" class="photo-item">
    <div class="photo-top">
      <span class="photo-at">{{ photoDate }}</span>
      <div v-if="editAble" class="photo-operate">
        <span @click="cancelEdit" class="photo-cancel">取消</span>
        <span @click="finishEdit" class="photo-finish">完成</span>
      </div>
      <van-button
        v-else
        round
        @click="editAble = true"
        class="photo-edit"
      >
        <img src="../images/edit.png" class="photo-edit-icon">
        <span class="photo-edit-text">编辑</span>
      </van-button>
    </div>
    <div class="photo-bottom">
      <div
        v-for="(photoSrc, index) in photoArr"
        :key="index"
        class="photo-img"
      >
        <img @click="showBigImg(index)" :src="photoSrc" >
        <van-icon
          v-if="editAble"
          @click="photoDel(index)"
          name="close"
          class="photo-del"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { Toast, Dialog } from 'vant'

export default {
  data: () => {
    return {
      isEmpty: false,
      editAble: false
    }
  },
  created() {
    this.isEmpty = this.photoArr.length > 0
  },
  props: {
    photoDate: String,
    photoArr: Array,
    photoId: Number,
    photoIsDel: Boolean
  },
  methods: {
    /**
     * 完成编辑
     */
    finishEdit() {
      if (this.photoIsDel) {
        Dialog.confirm({
          title: '确定本次修改？'
        }).then(() => {
          this.$emit('finishEdit', this.photoId)
          this.editAble = false
          this.isEmpty = this.photoArr.length > 0
        }).catch(() => {
          // 取消不做操作
        })
      } else {
        this.editAble = false
      }
    },
    /**
     * 取消编辑
     */
    cancelEdit() {
      if (this.photoIsDel) {
        Dialog.confirm({
          title: '确定放弃修改？'
        }).then(() => {
          this.$emit('cancelEdit', this.photoId)
          this.editAble = false
        }).catch(() => {
          // 取消不做操作
        })
      } else {
        this.editAble = false
      }
    },
    /**
     * 删除照片
     * @param {Number} index 照片数组下标
     */
    photoDel(index) {
      this.$emit('photoDel', this.photoId, index)
    },
    /**
     * 显示大图片
     * @param {Number} index 照片数组下标
     */
    showBigImg(index) {
      const UA = navigator.userAgent
      const isAndroid = UA.indexOf('Android') > -1 || UA.indexOf('Adr') > -1 // android终端
      const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
      let param = JSON.stringify({
        index,
        imgs: this.photoArr
      })
      // 原生app方法名称：showLargeImage
      if (isAndroid) {
        console.log('安卓')
        window.android.showLargeImage(param)
      } else if (isIOS) {
        console.log('苹果')
        window.webkit.messageHandlers.showLargeImage.postMessage(param)
      } else {
        Toast({
          message: '无法放大图片',
          type: 'html',
          forbidClick: true,
          duration: 1000
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.photo-item {
  padding: 15px 20px 0;

  &:last-of-type {
    padding-bottom: 15px;
  }

  .photo-top {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .photo-at {
      color: #666;
      font-size: 16px;
      font-weight: 400;
      line-height: 22px;
      padding-left: 20px;
      background: url('../images/<EMAIL>') no-repeat 0 center;
      background-size: 15px;
    }

    .photo-operate {
      height: 32px;
      display: flex;

      .photo-finish,
      .photo-cancel {
        padding: 0;
        width: 70px;
        height: 32px;
        color: #333;
        font-size: 18px;
        font-weight: 400;
        line-height: 32px;
        background: #F5F6FA;
      }

      .photo-finish {
        color: #fff;
        font-size: 18px;
        font-weight: 600;
        border-radius: 18px;
        background: linear-gradient(90deg, #FF8100 0%, #FD9A57 100%);
      }
    }

    .photo-edit {
      padding: 0;
      width: 76px;
      height: 32px;
      line-height: 30px;
      border-radius: 18px;
      background: #F5F6FA;
      border: 1px solid #F66031;

      .photo-edit-icon {
        width: 16px;
        vertical-align: middle;
      }

      .photo-edit-text {
        font-size: 16px;
        color: #F66031;
        margin-left: 4px;
        font-weight: 400;
        line-height: 22px;
        vertical-align: middle;
      }
    }
  }

  .photo-bottom {
    display: flex;
    flex-wrap: wrap;
    overflow: hidden;
    align-items: center;

    .photo-img {
      width: 94px;
      height: 94px;
      margin-top: 15px;
      margin-right: 11px;
      position: relative;

      &:nth-of-type(3n) {
        margin-right: 0;
      }

      img {
        width: 100%;
        height: 100%;
      }

      .photo-del {
        color: #FFF;
        font-size: 20px;
        position: absolute;
        top: 0;
        right: 0;
      }
    }
  }
}
</style>
