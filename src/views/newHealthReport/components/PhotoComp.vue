<template>
  <div class="photo-wrapper">
    <div v-if="photoList.length === 0" class="photo-none">
      <img src="../images/nodata2.png" alt="nodata" class="photo-none-img" />
      <p class="photo-none-text">您未上传报告照片</p>
    </div>
    <div v-else>
      <div class="photo-box">
        <photo-item
          v-for="photo in photoList"
          :key="photo.id"
          :photoId="photo.id"
          :photoIsDel="delFlag[photo.id]"
          :photoDate="photo.date"
          :photoArr="photo.imgs"
          @photoDel="photoDel"
          @finishEdit="finishEdit"
          @cancelEdit="cancelEdit"
        />
      </div>
      <!-- <div class="photo-bottom">
        <van-button class="photo-btn">删除本次记录</van-button>
      </div> -->
    </div>
  </div>
</template>

<script>
import PhotoItem from '../components/PhotoItem.vue'
import { deepCopy } from '@/utils/utils.js'

export default {
  data: () => {
    return {
      photoList: [],
      photoListCopy: [],
      delFlag: {}
    }
  },
  props: {
    photoData: Object,
    uploadReport: Function
  },
  components: {
    'photo-item': PhotoItem
  },
  created() {
    // 初始化
    this.init()
  },
  methods: {
    /**
     * 初始化
     */
    init() {
      this.photoList = this.photoData.map(item => {
        this.$set(this.delFlag, item.id, false)
        return item
      })
      // 深拷贝照片数据
      this.photoListCopy = deepCopy(this.photoList)
    },
    /**
     * 删除照片
     * @param {Number} id 记录id
     * @param {Number} index 照片下标
     */
    photoDel(id, index) {
      this.photoList.forEach(photo => {
        if (photo.id === id) {
          photo.imgs.splice(index, 1)
          this.$set(this.delFlag, id, true)
        }
      })
    },
    /**
     * 完成编辑
     * @param {Number} id 记录id
     */
    finishEdit(id) {
      if (this.delFlag[id]) {
        this.photoList.forEach(photo => {
          if (photo.id === id) {
            this.$emit('uploadReport', id, photo.imgs)
          }
        })
        this.$set(this.delFlag, id, false)
      }
      this.photoListCopy = deepCopy(this.photoList)
    },
    /**
     * 取消编辑
     * @param {Number} id 记录id
     */
    cancelEdit(id) {
      this.photoList.forEach(photo => {
        if (photo.id === id) {
          this.$set(this.delFlag, id, false)
          this.photoListCopy.forEach(photoCopy => {
            if (photoCopy.id === id) photo.imgs = photoCopy.imgs
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.photo-wrapper {

  .photo-box {
    border-radius: 9px;
    background: #F5F6FA;
  }

  .photo-none {
    padding: 10px 0;

    .photo-none-img {
      width: 150px;
    }

    .photo-none-text {
      color: #666;
      font-size: 14px;
      font-weight: 400;
    }
  }

  .photo-bottom {
    margin-top: 30px;

    .photo-btn {
      padding: 0;
      width:124px;
      height: 34px;
      color: #666;
      font-size: 16px;
      font-weight: 400;
      line-height: 34px;
      border-radius: 4px;
      border: 1px solid #CCC;
    }
  }
}
</style>
