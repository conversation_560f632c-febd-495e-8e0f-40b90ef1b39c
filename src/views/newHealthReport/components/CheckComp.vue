<template>
  <div class="check-wrapper">
    <div v-if="checkData.length === 0" class="check-none">
      <img src="../images/nodata2.png" alt="nodata" class="check-none-img" />
      <p class="check-none-text">您未上传检查数据</p>
    </div>
    <div v-else>
      <div v-for="(item, index) in checkData" :key="index" class="check-group">
        <h2 class="group-title">{{ item.group_name }}</h2>
        <check-item
          v-for="(metric, ind) in item.metrics"
          :key="ind"
          :metric="metric"
        />
      </div>
      <!-- <div class="check-bottom">
        <van-button class="check-bottom-btn">指标数据不对？</van-button>
        <span class="check-bottom-tips">MMC与医院数据持续对接中，如果有指标数值不符可以向我们反馈</span>
      </div> -->
    </div>
  </div>
</template>

<script>
import CheckItem from '../components/CheckItem.vue'

export default {
  data: () => {
    return {}
  },
  props: {
    checkData: Object
  },
  components: {
    'check-item': CheckItem
  }
}
</script>

<style lang="scss" scoped>
.check-wrapper {

  .check-none {
    padding: 10px 0;

    .check-none-img {
      width: 150px;
    }

    .check-none-text {
      color: #666;
      font-size: 14px;
      font-weight: 400;
    }
  }

  .check-group {
    padding: 15px;
    margin-top: 15px;
    border-radius: 9px;
    background: #F5F6FA;

    &:first-of-type {
      margin-top: 0;
    }

    .group-title {
      font-size: 20px;
      color: #EE7800;
      text-align: left;
      font-weight: 400;
      line-height: 30px;
      position: relative;
      padding-left: 10px;
      margin-bottom: 14px;

      &::after {
        content: '';
        width: 5px;
        height: 100%;
        background: #EE7800;
        transform: translateY(-50%);
        position: absolute;
        top: 50%;
        left: 0;
      }
    }
  }

  .check-bottom {
    display: flex;
    margin-top: 15px;
    align-items: center;
    justify-content: space-between;

    .check-bottom-btn {
      padding: 0;
      width: 124px;
      height: 34px;
      color: #666;
      font-size: 16px;
      font-weight: 400;
      line-height: 34px;
      background: #FFF;
      border-radius: 4px;
      border: 1px solid #CCC;
    }

    .check-bottom-tips {
      width: 216px;
      color: #666;
      font-size: 14px;
      text-align: left;
      font-weight: 400;
      line-height: 20px;
    }
  }
}
</style>
