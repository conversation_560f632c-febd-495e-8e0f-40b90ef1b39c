<template>
  <div class="bg-wrapper">
    <his-tips
      v-if="tipsTitle !== ''"
      :title="tipsTitle"
      :url="tipsUrl"
    />
    <exam-item
      v-for="(item, index) in detailData"
      :itemData="item"
      :itemKey="index"
      :key="index"
      class="group"
    />
    <div v-for="(group, ind) in groupData" :key="ind" class="box">
      <p class="box-name">{{ group.group_name }}</p>
      <exam-item
        v-for="(item, index) in group.metrics"
        :itemData="item"
        :itemKey="index"
        :key="index"
        class="group"
      />
    </div>
  </div>
</template>

<script>
import ExamItem from '../components/ExamItem.vue'
import HisTips from '../components/HisTips.vue'
import { getMetricsDetail } from '@/api/newHealthReport.js'

export default {
  data: () => {
    return {
      metricsCode: '10',
      detailData: [],
      groupData: [],
      tipsTitle: '',
      tipsUrl: ''
    }
  },
  props: {
    propData: {
      type: Object,
      default: () => {
        return {
          param: {
            code: 0
          },
          metrics: {}
        }
      }
    }
  },
  components: {
    'exam-item': ExamItem,
    'his-tips': HisTips
  },
  created() {
    // 初始化
    this.init()
  },
  methods: {
    /**
     * 初始化
     */
    init() {
      let code = ''
      if (this.propData.param && this.propData.param.code) {
        code = this.propData.param.code
      }
      if (code === this.metricsCode) {
        // 详细数据处理
        this.handleData(this.propData)
      } else {
        // 获取详细
        this.getDetail()
      }
    },
    /**
     * 获取详细
     */
    getDetail() {
      getMetricsDetail(this.metricsCode).then(res => {
        if (res.status === 0) {
          // 详细数据处理
          this.handleData(res.data)
          // 保存详细数据
          this.$emit('saveData', this.metricsCode, res.data)
        } else {
          this.$toast(res.msg)
        }
      })
    },
    /**
     * 详细数据处理
     */
    handleData(data) {
      let { metrics, param } = data
      let { group, ...detailData } = metrics || {}
      this.detailData = detailData || []
      this.groupData = group || []
      let title = param.title || {}
      this.tipsTitle = title.title || ''
      this.tipsUrl = title.url || ''
    }
  }
}
</script>

<style lang="scss" scoped>
.bg-wrapper {
  width: 100%;

  .group {
    border-bottom: 1px solid #E8E8E8;
  }

  .box {

    .box-name {
      margin: 14px 0;
      font-size: 20px;
      font-weight: 400;
      color: #EE7800;
      text-align: left;
      padding-left: 4px;
      border-left: 5px solid #FF8100;
    }

    .box-name + .group {
      padding-top: 0;
    }
  }
}
</style>
