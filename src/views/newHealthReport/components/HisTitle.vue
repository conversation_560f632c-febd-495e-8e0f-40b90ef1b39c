<template>
  <div class="his-title">
    <img :src="imgSrc" class="title-icon" >
    <span class="title-name">{{ name }}</span>
  </div>
</template>

<script>
export default {
  data: () => {
    return {}
  },
  props: {
    imgSrc: {
      type: String,
      default: '',
      required: true
    },
    name: {
      type: String,
      default: '',
      required: true
    }
  },
  created() {
    // 初始化
    this.init()
  },
  methods: {
    /**
     * 初始化
     */
    init() {}
  }
}
</script>

<style lang="scss" scoped>
.his-title {
  display: flex;
  align-items: center;

  .title-icon {
    width: 17px;
  }

  .title-name {
    font-size: 18px;
    font-weight: 400;
    color: #F67710;
    margin-left: 6px;
  }
}
</style>
