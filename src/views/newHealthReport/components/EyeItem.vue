<template>
  <div class="group">
    <p class="eye-title">{{ title }}</p>
    <div v-if="hasData === '1' && eyeCheck !== ''" class="eye-item">
      <his-title
        :name="'眼底检查'"
        :imgSrc="require('../images/<EMAIL>')"
      />
      <p class="eye-result">{{ eyeCheck }}</p>
    </div>
    <div v-if="hasData === '1'" class="eye-item">
      <!-- <his-title
        v-if="AIResult"
        :name="'AI分析结果'"
        :imgSrc="require('../images/<EMAIL>')"
      />
      <p v-if="AIResult" class="eye-result">{{ AIResult }}</p> -->
      <div class="eye-img">
        <img v-for="(eyeimg, index) in eyeImgs" @click="showBigImg(index)" :src="eyeimg" :key="index">
      </div>
    </div>
    <div v-if="hasData === '0'" class="unchecked">
      <cc-svg-icon icon-class="cry-face" class="unchecked-cry"/>
      <span>未检查</span>
    </div>
  </div>
</template>

<script>
import { ImagePreview } from 'vant'
import HisTitle from '../components/HisTitle.vue'

export default {
  data: () => {
    return {
      imgInstance: null // 图片预览实例
    }
  },
  props: {
    title: {
      type: String,
      default: '',
      required: true
    },
    hasData: { // 是否检查
      type: String,
      default: '0'
    },
    eyeCheck: { // 眼底检查
      type: String,
      default: ''
    },
    AIResult: { // AI分析结果
      type: String,
      default: ''
    },
    eyeImgs: { // 眼底图片
      type: Array,
      default: function () {
        return []
      }
    }
  },
  components: {
    'his-title': HisTitle
  },
  created() {
    // 初始化
    this.init()
  },
  beforeDestroy() {
    if (this.imgInstance) this.imgInstance.close()
  },
  methods: {
    /**
     * 初始化
     */
    init() {},
    /**
     * 图片放大
     * @param {Number} index 图片起始位置索引
     */
    showBigImg(index) {
      this.imgInstance = ImagePreview({
        images: this.eyeImgs, // 需要预览的图片 URL 数组
        startPosition: index, // 图片起始位置索引
        closeOnPopstate: true // 页面回退时自动关闭
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.group {
  width: 100%;
  text-align: left;
  padding: 14px 0 26px;
  border-bottom: 1px solid #E8E8E8;

  .eye-title {
    color: #333;
    font-size: 20px;
    font-weight: 400;
  }

  .eye-item {
    margin-top: 18px;

    .eye-result {
      color: #333;
      font-size: 18px;
      margin-top: 10px;
      font-weight: 400;
    }

    .eye-img {
      display: flex;
      margin-top: 15px;
      align-items: center;
      justify-content: space-between;

      img {
        width: 114px;
        height: 114px;
      }
    }
  }

  .unchecked {
    color: #CCC;
    display: flex;
    font-size: 18px;
    margin-top: 10px;
    text-align: left;
    font-weight: 400;
    align-items: center;
    justify-content: flex-start;

    .unchecked-cry {
      width: 20px;
      height: 20px;
      margin-right: 2px;
    }
  }
}
</style>
