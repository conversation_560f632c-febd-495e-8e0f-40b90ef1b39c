<template>
  <div class="bg-wrapper">
    <his-tips
      v-if="tipsTitle !== ''"
      :title="tipsTitle"
      :url="tipsUrl"
    />
    <exam-item
      v-for="(item, index) in detailData"
      :itemData="item"
      :itemKey="index"
      :key="index"
      class="group"
    />
    <div
      v-for="(item, index) in hisData"
      v-if="item.content"
      :key="index"
      class="nerve-end"
    >
      <his-title
        :name="item.title"
        :imgSrc="require('../images/<EMAIL>')"
      />
      <p class="nerve-text">{{ item.content }}</p>
    </div>
  </div>
</template>

<script>
import ExamItem from '../components/ExamItem.vue'
import HisTips from '../components/HisTips.vue'
import HisTitle from '../components/HisTitle.vue'
import { getMetricsDetail } from '@/api/newHealthReport.js'

export default {
  data: () => {
    return {
      metricsCode: '11',
      detailData: [],
      hisData: [],
      tipsTitle: '',
      tipsUrl: ''
    }
  },
  props: {
    propData: {
      type: Object,
      default: () => {
        return {
          param: {
            code: 0
          },
          metrics: {}
        }
      }
    }
  },
  components: {
    'exam-item': ExamItem,
    'his-tips': HisTips,
    'his-title': HisTitle
  },
  created() {
    // 初始化
    this.init()
  },
  methods: {
    /**
     * 初始化
     */
    init() {
      let code = 0
      if (this.propData.param && this.propData.param.code) {
        code = this.propData.param.code
      }
      if (code === this.metricsCode) {
        // 详细数据处理
        this.handleData(this.propData)
      } else {
        // 获取详细
        this.getDetail()
      }
    },
    /**
     * 获取详细
     */
    getDetail() {
      getMetricsDetail(this.metricsCode).then(res => {
        if (res.status === 0) {
          // 详细数据处理
          this.handleData(res.data)
          // 保存详细数据
          this.$emit('saveData', this.metricsCode, res.data)
        } else {
          this.$toast(res.msg)
        }
      })
    },
    /**
     * 详细数据处理
     */
    handleData(data) {
      let { metrics, param } = data
      let { his, ...detailData } = metrics || {}
      this.detailData = detailData
      this.hisData = his || []
      let title = param.title || {}
      this.tipsTitle = title.title || ''
      this.tipsUrl = title.url || ''
    }
  }
}
</script>

<style lang="scss" scoped>
.bg-wrapper {
  width: 100%;

  .group {
    border-bottom: 1px solid #E8E8E8;

    &:last-of-type {
      border-color: #fff;
    }
  }

  .nerve-end {
    margin-top: 15px;

    .nerve-text {
      color: #333;
      font-size: 18px;
      margin-top: 5px;
      text-align: left;
      font-weight: 400;
      text-indent: 30px;
      line-height: 30px;
      word-break: break-all;
    }
  }
}
</style>
