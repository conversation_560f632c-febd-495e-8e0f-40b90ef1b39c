<template>
  <div class="group-item">
    <!-- 眼底项 -->
    <div v-if="type === 1" class="group-eye">
      <div class="group-eye-box">
        <p class="eye-name">眼底检查</p>
        <p class="eye-val">{{ value }}</p>
      </div>
      <!-- <div v-if="AIConclusion" class="group-eye-box">
        <p class="eye-name">AI分析结果</p>
        <p class="eye-val">{{ AIConclusion }}</p>
      </div> -->
      <div v-if="AIImgs.lenght !== 0" class="group-eye-img">
        <img
          v-for="(imgSrc, index) in AIImgs"
          @click="showBigImg(index)"
          :key="index"
          :src="imgSrc"
        />
      </div>
      <p class="group-eye-source" v-if="checkDateFrom()">
        数据来源：{{ source }}
      </p>
    </div>
    <!-- 结论项 -->
    <div v-else-if="type === 2" class="group-conclusion">
      <p class="group-conclusion-text">{{ value }}</p>
      <p class="group-conclusion-source" v-if="checkDateFrom()">
        数据来源：{{ source }}
      </p>
    </div>
    <!-- 常规项 -->
    <div v-else class="group-common">
      <div class="item-top">
        <span class="item-name">{{ name }}</span>
        <span class="item-val">
          <b class="item-num">{{ value }}</b>
          <b class="item-unit">{{ unit }}</b>
        </span>
      </div>
      <p v-if="range" class="item-range">参考范围（{{ range }}）</p>
      <p class="item-source" v-if="checkDateFrom()">数据来源：{{ source }}</p>
    </div>
  </div>
</template>

<script>
export default {
  data: () => {
    return {
      name: "",
      value: "",
      unit: "",
      range: "",
      source: "",
      type: "", // 1：眼底项；2：结论项；3：常规项
      AIConclusion: "",
      AIImgs: [],
    };
  },
  props: {
    metric: Object,
  },
  created() {
    // 1：眼底；2：结论
    let typeObj = {
      945: 1,
      946: 1,
      948: 2,
      954: 2,
      925017: 2,
      904033: 2,
      926049: 2,
      930008: 2,
    };
    this.type = typeObj[this.metric.subquestion_id || ""] || 3;
    this.name = this.metric.subquestion_name || "";
    this.value = this.metric.answer_name || "";
    this.unit = this.metric.unit || "";
    this.range = this.metric.reference || "";
    this.source = this.metric.datasource === 1 ? "医院信息系统" : "手动输入";
    // 眼底AI
    let AIData = this.metric.ai || {};
    this.AIConclusion = AIData.conclusion || "";
    this.AIImgs = AIData.img || [];
  },
  methods: {
    /**
     * 显示大图片
     * @param {Number} index 照片数组下标
     */
    showBigImg(index) {
      const UA = navigator.userAgent;
      const isAndroid = UA.indexOf("Android") > -1 || UA.indexOf("Adr") > -1; // android终端
      const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); // ios终端
      let param = JSON.stringify({
        index,
        imgs: this.AIImgs,
      });
      // 原生app方法名称：showLargeImage
      if (isAndroid) {
        console.log("安卓");
        window.android.showLargeImage(param);
      } else if (isIOS) {
        console.log("苹果");
        window.webkit.messageHandlers.showLargeImage.postMessage(param);
      } else {
        Toast({
          message: "无法放大图片",
          type: "html",
          forbidClick: true,
          duration: 1000,
        });
      }
    },
    // <!-- source_type!=7 语音盒子 入口 -->
    checkDateFrom() {
      const source_type = localStorage.getItem("source_type");
      return source_type != 7 ? true : false;
    },
  },
};
</script>

<style lang="scss" scoped>
.group-item {
  color: #333;
  margin-top: 15px;
  text-align: left;
  border-top: 2px solid #e8e8e8;

  &:first-of-type {
    margin-top: 0px;
  }

  .group-eye {
    .group-eye-box {
      color: #333;
      margin-top: 15px;

      .eye-name {
        font-size: 20px;
        font-weight: 400;
        line-height: 30px;
      }

      .eye-val {
        font-size: 18px;
        font-weight: 400;
        line-height: 30px;
      }
    }

    .group-eye-img {
      display: flex;
      margin-top: 15px;
      align-items: center;
      justify-content: space-between;

      img {
        width: 152px;
        height: 152px;
      }
    }

    .group-eye-source {
      color: #999;
      font-size: 14px;
      margin-top: 15px;
      font-weight: 400;
      line-height: 20px;
    }
  }

  .group-conclusion {
    .group-conclusion-text {
      color: #333;
      font-size: 18px;
      margin-top: 15px;
      font-weight: 400;
      line-height: 30px;
    }

    .group-conclusion-source {
      color: #999;
      font-size: 14px;
      margin-top: 15px;
      font-weight: 400;
      line-height: 20px;
    }
  }

  .group-common {
    .item-top {
      display: flex;
      margin-top: 12px;
      align-items: flex-start;
      justify-content: flex-start;

      .item-name {
        width: 60%;
        font-size: 20px;
        font-weight: 400;
        line-height: 30px;
      }

      .item-val {
        width: 40%;
        display: flex;
        font-size: 15px;
        font-weight: 400;
        align-items: baseline;

        .item-num {
          flex: 1;
          font-size: 30px;
          font-weight: 400;
          margin-right: 6px;
        }

        .item-unit {
          word-break: break-all;
        }
      }
    }

    .item-range {
      font-size: 18px;
      margin-top: 5px;
      font-weight: 400;
      line-height: 30px;
    }

    .item-source {
      color: #999;
      font-size: 14px;
      margin-top: 5px;
      font-weight: 400;
      line-height: 20px;
    }
  }
}
</style>
