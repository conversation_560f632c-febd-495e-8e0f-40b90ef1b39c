<template>
  <div class="his-eye">
    <his-tips
      v-if="tipsTitle !== ''"
      :title="tipsTitle"
      :url="tipsUrl"
    />
    <p v-if="checkDate" class="eye-date">检查时间：{{ checkDate }}</p>
    <div class="eye-check">
      <!-- 左眼 -->
      <eye-item
        :hasData="leftData.hasData"
        :title="leftData.name"
        :eyeCheck="leftData.eyeCheck"
        :AIResult="leftData.AIResult"
        :eyeImgs="leftData.eyeImgs"
      />
      <!-- 右眼 -->
      <eye-item
        :hasData="rightData.hasData"
        :title="rightData.name"
        :eyeCheck="rightData.eyeCheck"
        :AIResult="rightData.AIResult"
        :eyeImgs="rightData.eyeImgs"
      />
    </div>
    <div v-if="describt" class="eye-describe">
      <his-title
        :name="'眼底检测描述'"
        :imgSrc="require('../images/<EMAIL>')"
      />
      <p class="describt-text">{{ describt }}</p>
    </div>
    <p class="tips">以上结论仅供参考，具体需要结合临床。</p>
  </div>
</template>

<script>
import EyeItem from '../components/EyeItem.vue'
import HisTips from '../components/HisTips.vue'
import HisTitle from '../components/HisTitle.vue'
import { getMetricsDetail } from '@/api/newHealthReport.js'

export default {
  data: () => {
    return {
      metricsCode: '8',
      checkDate: '',
      describt: '',
      leftData: {
        name: '左眼',
        hasData: '0',
        eyeCheck: '',
        AIResult: '',
        eyeImgs: []
      },
      rightData: {
        name: '右眼',
        hasData: '0',
        eyeCheck: '',
        AIResult: '',
        eyeImgs: []
      },
      tipsTitle: '',
      tipsUrl: ''
    }
  },
  props: {
    propData: {
      type: Object,
      default: () => {
        return {
          param: {
            code: 0
          },
          metrics: {}
        }
      }
    }
  },
  components: {
    'eye-item': EyeItem,
    'his-tips': HisTips,
    'his-title': HisTitle
  },
  created() {
    // 初始化
    this.init()
  },
  methods: {
    /**
     * 初始化
     */
    init() {
      let code = 0
      if (this.propData.param && this.propData.param.code) {
        code = this.propData.param.code
      }
      if (code === this.metricsCode) {
        // 详细数据处理
        this.handleData(this.propData)
      } else {
        // 获取详细
        this.getDetail()
      }
    },
    /**
     * 获取详细
     */
    getDetail() {
      getMetricsDetail(this.metricsCode).then(res => {
        if (res.status === 0) {
          // 详细数据处理
          this.handleData(res.data)
          // 保存详细数据
          this.$emit('saveData', this.metricsCode, res.data)
        } else {
          this.$toast(res.msg)
        }
      })
    },
    /**
     * 详细数据处理
     */
    handleData(data) {
      let { metrics, param } = data
      let data948 = metrics['948'] || {}
      this.checkDate = metrics.visit_at || ''
      this.describt = data948.conclusion || ''
      let data945 = metrics['945'] || {}
      let data946 = metrics['946'] || {}
      let data945AI = data945.ai || {}
      let data946AI = data946.ai || {}
      this.leftData = {
        name: '左眼',
        hasData: data945.has_data || '0',
        eyeCheck: data945.conclusion || '',
        AIResult: data945AI.conclusion || '',
        eyeImgs: data945AI.img || []
      }
      this.rightData = {
        name: '右眼',
        hasData: data946.has_data || '0',
        eyeCheck: data946.conclusion || '',
        AIResult: data946AI.conclusion || '',
        eyeImgs: data946AI.img || []
      }
      let title = param.title || {}
      this.tipsTitle = title.title || ''
      this.tipsUrl = title.url || ''
    }
  }
}
</script>

<style lang="scss" scoped>
.his-eye {
  width: 100%;

  .eye-date {
    color: #999;
    font-size: 15px;
    margin-top: 15px;
    text-align: left;
    font-weight: 400;
    line-height: 20px;
  }

  .eye-describe {
    margin-top: 22px;

    .describt-text {
      color: #333;
      font-size: 18px;
      margin-top: 4px;
      text-align: left;
      font-weight: 400;
      line-height: 30px;
      word-break: break-all;
    }
  }

  .tips {
    color: #999;
    font-size: 13px;
    font-weight: 400;
    text-align: left;
    margin-top: 15px;
    line-height: 20px;
  }
}
</style>
