<template>
  <div class="wrapper">
    <div class="normal">
      <img src="../images/<EMAIL>" class="normal-img">
    </div>
    <div class="info">
      <p class="info-result">上传成功！</p>
      <p class="info-value">
        <span class="info-num">{{ weight }}</span>
        <span class="info-unit">kg</span>
      </p>
      <div class="info-bmi">
        <p class="bmi-box">
          <span class="bmi-name">身高</span>
          <span class="bmi-num">{{ height }}</span>
          <span class="bmi-unit">cm</span>
        </p>
        <p class="bmi-box">
          <span class="bmi-name">体质指数</span>
          <span class="bmi-num">{{ bmi }}</span>
          <span class="bmi-unit">kg/m²</span>
        </p>
      </div>
      <div div class="bmi-status">
        <span class="bmi-status-left">体质</span>
        <span :class="['bmi-status-text', bmiStatusTextClass]">{{ bmiStatusText }}</span>
      </div>
    </div>
    <div class="footer">
      <span @click="goRecord" class="footer-btn">更多体重记录</span>
      <span @click="goHisRecord" class="footer-btn">更多各项历史记录</span>
    </div>
  </div>
</template>

<script>
import { Toast } from 'vant'
import { bmiLastOneData } from '@/api/newHealthReport.js'

export default {
  data: () => {
    return {
      bmi: '',
      bmiStatus: '',
      bmiStatusText: '',
      hasData: 0,
      height: '',
      weight: '',
      isAndroid: false,
      isIOS: false
    }
  },
  computed: {
    /**
     * bmi状态class
     */
    bmiStatusTextClass() {
      let className = ''
      switch (this.bmiStatus) {
        case 2:
          className = 'bmi-status-low'
          break
        case 3:
          className = 'bmi-status-high'
          break
        case 4:
          className = 'bmi-status-higher'
          break
        default:
          className = ''
          break
      }
      return className
    }
  },
  created() {
    const UA = navigator.userAgent
    this.isAndroid = UA.indexOf('Android') > -1 || UA.indexOf('Adr') > -1 // android终端
    this.isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端

    // 获取BMI测量结果
    this.getBMILastOneData()
  },
  methods: {
    /**
     * 获取BMI测量结果
     */
    getBMILastOneData() {
      bmiLastOneData().then(res => {
        if (res.status === 0) {
          let data = res.data || {}
          this.hasData = data.has_data || 0
          this.bmi = this.checkData(data.bmi)
          this.bmiStatus = this.checkData(data.bmi_status)
          this.bmiStatusText = this.checkData(data.bmi_status_text)
          this.height = this.checkData(data.height)
          this.weight = this.checkData(data.weight)
        } else {
          this.$toast(res.msg)
        }
      }).catch((err) => {
        this.$toast(err)
      })
    },
    /**
     * 数据判断
     */
    checkData(data) {
      if (data === null || data === undefined) {
        return ''
      } else {
        return data
      }
    },
    /**
     * 前往原生app运动记录页
     */
    goRecord() {
      // 原生app方法名称：moreRecordAction
      if (this.isAndroid) {
        console.log('安卓')
        window.android.moreRecordAction('3')
      } else if (this.isIOS) {
        console.log('苹果')
        window.webkit.messageHandlers.moreRecordAction.postMessage('3')
      } else {
        Toast({
          message: '暂时无法查看',
          type: 'html',
          forbidClick: true,
          duration: 1000
        })
      }
    },
    /**
     * 前往原生app各项历史记录页
     */
    goHisRecord() {
      // 原生app方法名称：moreOtherRecordAction
      if (this.isAndroid) {
        console.log('安卓')
        window.android.moreOtherRecordAction()
      } else if (this.isIOS) {
        console.log('苹果')
        window.webkit.messageHandlers.moreOtherRecordAction.postMessage('hello')
      } else {
        Toast({
          message: '暂时无法查看',
          type: 'html',
          forbidClick: true,
          duration: 1000
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/assets/scss/newHealthReport/measures/bgResult.scss'
</style>
