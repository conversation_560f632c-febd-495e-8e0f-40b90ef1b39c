<template>
  <div class="wrapper">
    <div class="normal">
      <img src="../images/<EMAIL>" class="normal-img">
    </div>
    <div class="info">
      <p class="info-result">上传成功！</p>
      <p class="info-value">
        <span class="info-num">{{ stepNum }}</span>
        <span class="info-unit">步</span>
      </p>
    </div>
    <div class="footer">
      <span @click="goRecord" class="footer-btn">更多运动记录</span>
      <span @click="goHisRecord" class="footer-btn">更多各项历史记录</span>
    </div>
  </div>
</template>

<script>
import { Toast } from 'vant'
import { motionLastOneData } from '@/api/newHealthReport.js'

export default {
  data: () => {
    return {
      stepNum: '',
      isAndroid: false,
      isIOS: false
    }
  },
  created() {
    const UA = navigator.userAgent
    this.isAndroid = UA.indexOf('Android') > -1 || UA.indexOf('Adr') > -1 // android终端
    this.isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
    // 获取运动步数
    this.getMotionLastOne()
  },
  methods: {
    /**
     * 获取运动步数
     */
    getMotionLastOne() {
      motionLastOneData().then(res => {
        if (res.status === 0) {
          let data = res.data || {}
          this.stepNum = this.checkData(data.num)
        } else {
          this.$toast(res.msg)
        }
      })
    },
    /**
     * 数据判断
     */
    checkData(data) {
      if (data === null || data === undefined) {
        return ''
      } else {
        return data
      }
    },
    /**
     * 前往原生app运动记录页
     */
    goRecord() {
      // 原生app方法名称：moreRecordAction
      if (this.isAndroid) {
        console.log('安卓')
        window.android.moreRecordAction('4')
      } else if (this.isIOS) {
        console.log('苹果')
        window.webkit.messageHandlers.moreRecordAction.postMessage('4')
      } else {
        Toast({
          message: '暂时无法查看',
          type: 'html',
          forbidClick: true,
          duration: 1000
        })
      }
    },
    /**
     * 前往原生app各项历史记录页
     */
    goHisRecord() {
      // 原生app方法名称：moreOtherRecordAction
      if (this.isAndroid) {
        console.log('安卓')
        window.android.moreOtherRecordAction()
      } else if (this.isIOS) {
        console.log('苹果')
        window.webkit.messageHandlers.moreOtherRecordAction.postMessage('hello')
      } else {
        Toast({
          message: '暂时无法查看',
          type: 'html',
          forbidClick: true,
          duration: 1000
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/assets/scss/newHealthReport/measures/bgResult.scss'
</style>
