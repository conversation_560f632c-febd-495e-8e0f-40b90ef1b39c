<template>
  <div class="wrapper">
    <div v-if="status === 1" class="normal">
      <img src="../images/<EMAIL>" class="normal-img">
      <p class="normal-tips">太棒了！请您继续保持。</p>
    </div>
    <div class="info">
      <p :class="['info-result', statusTextClass]">{{ statusText }}{{status===4?'!':''}}</p>
      <p class="info-value">
        <span :class="['info-num', sbpStatusTextClass]">{{ sbp }}</span>
        <span class="info-sep">/</span>
        <span :class="['info-num', dbpStatusTextClass]">{{ dbp }}</span>
        <span class="info-num info-pulse">{{ pulse }}</span>
      </p>
    </div>
    <p v-if="tips" class="tips">{{ tips }}</p>
    <div class="card">
      <div class="card-group">
        <p class="card-up">收缩压</p>
        <p class="card-down">{{ sbp }}</p>
      </div>
      <div class="card-group">
        <p class="card-up">舒张压</p>
        <p class="card-down">{{ dbp }}</p>
      </div>
      <div class="card-group">
        <p class="card-up">脉搏</p>
        <p class="card-down">{{ pulse }}</p>
      </div>
    </div>
    <div class="note">
      <p class="note-title">理想控制目标</p>
      <p class="note-range">{{normal_text}}</p>
      <p class="note-source">{{source_text}}</p>
    </div>
    <div class="footer" v-if="type==1">
      <span @click="goRecord" class="footer-btn">更多血压记录</span>
      <span @click="goHisRecord" class="footer-btn">更多各项历史记录</span>
    </div>
    <div class="footer2" v-if="type==2">
      <span @click="goRemeasure" class="footer-btn">再次测量</span>
      <span @click="goRecord" class="footer-btn">更多血压记录</span>
    </div>
  </div>
</template>

<script>
  import {Toast} from 'vant'
  import {bpStatus} from '@/api/newHealthReport.js'

  export default {
    data: () => {
      return {
        dataId: '',
        hasData: 0,
        sbp: '',
        dbp: '',
        pulse: '',
        statusText: '',
        dbpStatus: 0,
        sbpStatus: 0,
        normal_text: '',
        source_text: '',
        status: 0,
        tips: '',
        isAndroid: false,
        isIOS: false,
        type: 1
      }
    },
    computed: {
      /**
       * 血压整体状态class
       */
      statusTextClass() {
        let className = ''
        switch (this.status) {
          case 2:
            className = 'info-low'
            break
          case 3:
            className = 'info-high'
            break
          case 4:
            className = 'info-higher'
            break
          default:
            className = ''
            break
        }
        return className
      },
      /**
       * 高血压（收缩压）状态class
       */
      sbpStatusTextClass() {
        let className = ''
        switch (this.sbpStatus) {
          case 2:
            className = 'info-low'
            break
          case 3:
            className = 'info-high'
            break
          case 4:
            className = 'info-higher'
            break
          default:
            className = ''
            break
        }
        return className
      },
      /**
       * 低血压（舒张压）状态class
       */
      dbpStatusTextClass() {
        let className = ''
        switch (this.dbpStatus) {
          case 2:
            className = 'info-low'
            break
          case 3:
            className = 'info-high'
            break
          default:
            className = ''
            break
        }
        return className
      }
    },
    created() {
      this.dataId = this.$route.query.id || ''
      this.type = this.$route.query.type || '1'
      const UA = navigator.userAgent
      this.isAndroid = UA.indexOf('Android') > -1 || UA.indexOf('Adr') > -1 // android终端
      this.isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端

      // 获取血压状态
      this.getBpStatus()
    },
    methods: {
      /**
       * 获取血压状态
       */
      getBpStatus() {
        bpStatus(this.dataId).then(res => {
          if (res.status === 0) {
            let data = res.data || {}
            this.hasData = data.has_data || 0
            this.sbp = this.checkData(data.sbp)
            this.dbp = this.checkData(data.dbp)
            this.pulse = this.checkData(data.pulse)
            this.statusText = this.checkData(data.status_text)
            this.source_text = data.source_text
            this.normal_text = data.normal_text
            this.dbpStatus = this.checkData(data.dbp_status)
            this.sbpStatus = this.checkData(data.sbp_status)
            this.status = this.checkData(data.status)
            this.tips = this.checkData(data.tips)
          } else {
            this.$toast(res.msg)
          }
        })
      },
      /**
       * 数据判断
       */
      checkData(data) {
        if (data === null || data === undefined) {
          return ''
        } else {
          return data
        }
      },
      /**
       * 前往原生app运动记录页
       */
      goRecord() {
        // 原生app方法名称：moreRecordAction
        if (this.isAndroid) {
          console.log('安卓')
          window.android.moreRecordAction('1')
        } else if (this.isIOS) {
          console.log('苹果')
          window.webkit.messageHandlers.moreRecordAction.postMessage('1')
        } else {
          Toast({
            message: '暂时无法查看',
            type: 'html',
            forbidClick: true,
            duration: 1000
          })
        }
      },
      /**
       * 前往原生app重新测量页面
       */
      goRemeasure() {
        // 原生app方法名称：moreOtherRecordAction
        if (this.isAndroid) {
          console.log('安卓')
          window.android.reMeasureAction('1')
        } else if (this.isIOS) {
          console.log('苹果')
          window.webkit.messageHandlers.reMeasureAction.postMessage('1')
        } else {
          Toast({
            message: '暂时无法查看',
            type: 'html',
            forbidClick: true,
            duration: 1000
          })
        }
      },
      /**
       * 前往原生app各项历史记录页
       */
      goHisRecord() {
        // 原生app方法名称：moreOtherRecordAction
        if (this.isAndroid) {
          console.log('安卓')
          window.android.moreOtherRecordAction()
        } else if (this.isIOS) {
          console.log('苹果')
          window.webkit.messageHandlers.moreOtherRecordAction.postMessage('hello')
        } else {
          Toast({
            message: '暂时无法查看',
            type: 'html',
            forbidClick: true,
            duration: 1000
          })
        }
      }
    }
  }
</script>

<style lang="scss" scoped>
  @import '@/assets/scss/newHealthReport/measures/bgResult.scss';

  .footer2 {
    display: flex;
    padding: 0 20px;
    margin-top: 60px;
    justify-content: space-between;

    .footer-btn {
      width: 150px;
      padding: 0 10px;
      font-size: 20px;
      font-weight: 400;
      color: #FF7C35;
      line-height: 42px;
      background: #EBEBEB;
      border-radius: 6px;
    }
    .footer-btn:nth-child(2) {
      margin-left: 30px;
      background: #FF8100;
      border-radius: 6px;
      color: #FFFFFF;
    }
  }
</style>
