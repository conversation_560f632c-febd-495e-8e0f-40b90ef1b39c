<template>
  <div class="wrapper">
    <img
      v-for="(imgSrc, index) in imgs"
      :key="index"
      :src="imgSrc"
      @click="showBigImg(index)"
    >
  </div>
</template>

<script>
import { Toast } from 'vant'

export default {
  data: () => {
    return {
      imgs: ''
    }
  },
  created() {
    this.imgs = this.$route.query.imgs || []
  },
  methods: {
    /**
     * 显示大图片
     * @param {Number} index 照片数组下标
     */
    showBigImg(index) {
      const UA = navigator.userAgent
      const isAndroid = UA.indexOf('Android') > -1 || UA.indexOf('Adr') > -1 // android终端
      const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
      let param = JSON.stringify({
        index,
        imgs: this.imgs
      })
      // 原生app方法名称：showLargeImage
      if (isAndroid) {
        console.log('安卓')
        window.android.showLargeImage(param)
      } else if (isIOS) {
        console.log('苹果')
        window.webkit.messageHandlers.showLargeImage.postMessage(param)
      } else {
        Toast({
          message: '无法放大图片',
          type: 'html',
          forbidClick: true,
          duration: 1000
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.wrapper {
  width: 100%;

  img {
    width: 100%;
  }
}
</style>
