<template>
  <div>
    <div class="wrapper" v-if="checkDateFrom()">
      <div class="header">
        <h1 class="hosp-name">{{ hospName }}</h1>
        <p class="visit-at">就诊日期：{{ visitAt }}</p>
        <div class="header-check">
          <span
            >共检查<b class="check-num">{{ count }}</b
            >项</span
          >
          <div @click="takePhoto" class="header-photo">
            <img src="../images/photo.png" class="photo-icon" />
            <span>上传报告</span>
          </div>
        </div>
      </div>
      <div class="tabs">
        <span
          v-for="tab in tabList"
          :key="tab.id"
          @click="changeTab(tab)"
          :class="{ 'tabs-item': true, 'tabs-item-active': tab.id === tabId }"
          >{{ tab.name }}</span
        >
      </div>
      <!-- 检查数据 -->
      <check-comp
        v-if="tabId === '1' && checkData"
        :checkData="checkData"
        class="check-comp"
      />
      <!-- 报告照片 -->
      <photo-comp
        v-if="tabId === '2' && photoData"
        :photoData="photoData"
        @uploadReport="reportUpload"
        class="photo-comp"
      />
    </div>
    <!-- 检查数据 -->
    <template v-else>
      <check-comp
        v-if="tabId === '1' && checkData"
        :checkData="checkData"
        class="check-comp"
      />
    </template>
  </div>
</template>
<script>
import CheckComp from "../components/CheckComp.vue";
import PhotoComp from "../components/PhotoComp.vue";
import {
  reportDetail,
  getUploadReport,
  uploadReport,
} from "@/api/newHealthReport.js";

export default {
  data: () => {
    return {
      reportId: "",
      hospName: "",
      visitAt: "",
      count: "",
      tabList: [
        {
          id: "1",
          name: "检查数据",
        },
        {
          id: "2",
          name: "报告照片",
        },
      ],
      tabId: "1",
      checkData: "",
      photoData: "",
      source_type: 0,
    };
  },
  components: {
    "check-comp": CheckComp,
    "photo-comp": PhotoComp,
  },
  created() {
    const source_type = localStorage.getItem("source_type");
    this.source_type = source_type
    console.log(this.source_type,"source_type")
    let query = this.$route.query;
    this.tabId = query.tabId || "1";
    this.reportId = query.reportId;
    // 初始化
    this.init();
  },
  methods: {
    /**
     * 初始化
     */
    init() {
      // 获取就诊数据详情
      this.getReportDetail();
      // 获取用户上传的检查报告
      this.getUploadReport();
    },
    /**
     * 获取就诊数据详情
     */
    getReportDetail() {
      reportDetail(this.reportId).then((res) => {
        if (res.status === 0) {
          let data = res.data || {};
          this.hospName = data.hospital_name || "";
          this.visitAt = data.visit_at || "";
          this.count = data.count;
          this.checkData = data.details || {};
        } else {
          this.$toast(res.msg);
        }
      });
    },
    /**
     * 获取用户上传的检查报告
     */
    getUploadReport() {
      getUploadReport(this.reportId).then((res) => {
        if (res.status === 0) {
          this.photoData = res.data || [];
        } else {
          this.$toast(res.msg);
        }
      });
    },
    /**
     * 上传检查报告
     * @param {String} id 报告记录id
     * @param {Array} imgs 需要更新的图片地址数组
     */
    reportUpload(id, imgs) {
      let formdata = new FormData();
      formdata.append("record_id", this.reportId);
      formdata.append("id", id);
      let imgArr = imgs.map((imgSrc) => {
        return { url: imgSrc };
      });
      formdata.append("imgs", JSON.stringify(imgArr));

      uploadReport(formdata).then((res) => {
        if (res.status === 0) {
          this.$toast("编辑成功");
        } else {
          this.$toast(res.msg);
        }
      });
    },
    /**
     * 切换tab
     * @param {Object} tab tab数据
     */
    changeTab(tab) {
      this.tabList.forEach((item) => {
        if (item.id === tab.id) this.tabId = item.id;
      });
    },
    /**
     * 拍摄照片
     */
    takePhoto() {
      const UA = navigator.userAgent;
      const isAndroid = UA.indexOf("Android") > -1 || UA.indexOf("Adr") > -1; // android终端
      const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); // ios终端
      // 原生app方法 historyRecordMakePhoto
      if (isAndroid) {
        console.log("安卓");
        window.android.historyRecordMakePhoto();
      } else if (isIOS) {
        console.log("苹果");
        window.webkit.messageHandlers.historyRecordMakePhoto.postMessage(
          "hello"
        );
      } else {
        this.$toast("无法拍照");
      }
    },
    // <!-- source_type!=7 语音盒子 入口 -->
    checkDateFrom() {
      const source_type = localStorage.getItem("source_type");
      return source_type != 7 ? true : false;
    }
  },
};
</script>

<style lang="scss" scoped>
@import "@/assets/scss/newHealthReport/his/hisVisit.scss";
</style>
