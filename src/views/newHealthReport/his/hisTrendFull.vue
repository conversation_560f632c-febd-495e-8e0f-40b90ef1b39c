<template>
  <div class="wrapper">
    <div class="header">
      <p class="header-title">选择你想查看的核心指标</p>
      <div>
        <div class="heard-select">
          <p class="drop-up" @click="selectFlag = !selectFlag">
            <span>{{ name }}</span>
            <van-icon name="arrow-down" />
          </p>
          <div v-show="selectFlag" class="select-drop">
            <p
              v-for="item in coreList"
              :key="item.subquestion_id"
              @click="changeCode(item.subquestion_id)"
            >{{ item.subquestion_name }}</p>
          </div>
          <div
            v-if="selectFlag"
            class="select-mask"
            @click="selectFlag = false"
          ></div>
        </div>
      </div>
    </div>
    <div class="chart">
      <div class="chart-box" ref="chartBox"></div>
    </div>
  </div>
</template>

<script>
import echarts from 'echarts'
import { trendChart } from '@/api/newHealthReport.js'

export default {
  data: () => {
    return {
      code: '',
      name: '',
      coreList: [],
      trendValue: [],
      trendDate: [],
      selectFlag: false
    }
  },
  watch: {
    code(newVal, oldVal) {
      if (oldVal === '') return
      // 获取核心指标趋势图表
      this.getTrendChart()
    }
  },
  created() {
    this.code = this.$route.query.code
    // 初始化
    this.init()
  },
  methods: {
    /**
     * 初始化
     */
    init() {
      // 获取核心指标趋势图表
      this.getTrendChart()
    },
    /**
     * 获取核心指标趋势图表
     */
    getTrendChart() {
      trendChart(this.code).then(res => {
        if (res.status === 0) {
          this.coreList = res.data.metrics
          this.trendValue = res.data.value
          this.trendDate = res.data.date
          this.coreList.forEach(item => {
            if (item.subquestion_id === Number(this.code)) this.name = item.subquestion_name
          })
          // 折线图初始化
          this.$nextTick(() => {
            this.lineChartInit('chartBox', this.trendValue, this.trendDate)
          })
        } else {
          this.$toast(res.msg)
        }
      })
    },
    /**
     * 折线图初始化
     * @param {String} ref DOM
     * @param {Array} yAxisData y轴数据
     * @param {Array} xAxisData x轴数据
     * @param {String} type 类型（0：全部或单日趋势图）
     */
    lineChartInit (ref, yAxisData, xAxisData, type = 0) {
      let myEchart = echarts.init(this.$refs[ref])
      let minData = []
      let maxData = []

      let myOption = {
        tooltip: {
          show: true,
          trigger: 'axis'
        },
        grid: {
          top: '30px',
          bottom: type === 0 ? '-10px' : '5px',
          right: '10px',
          left: '30px',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          axisLabel: {
            interval: 0,
            color: type === 0 ? 'rgba(0, 23, 11, 0)' : 'rgba(0, 23, 11, 1)',
            rotate: 45
          },
          axisTick: {
            show: type !== 1,
            alignWithLabel: true
          },
          axisLine: {
            lineStyle: {
              color: '#ccc'
            }
          },
          data: xAxisData
        },
        yAxis: {
          type: 'value',
          name: 'mmol/L',
          axisLine: {
            lineStyle: {
              color: '#ccc'
            }
          },
          axisLabel: {
            color: '#666'
          },
          splitLine: {
            lineStyle: {
              color: '#eee'
            }
          }
        },
        series: [
          {
            type: 'line',
            symbol: 'circle',
            symbolSize: 9,
            itemStyle: {
              normal: {
                color: '#FF8E00',
                label: {
                  show: true,
                  color: '#333333'
                },
                lineStyle: {
                  color: '#F67710',
                  width: 1
                }
              }
            },
            data: yAxisData
          },
          {
            type: 'line',
            name: 'min',
            stack: '正常',
            symbol: 'none',
            lineStyle: { opacity: 0 },
            data: minData
          },
          {
            type: 'line',
            name: 'max',
            stack: '正常',
            symbol: 'none',
            lineStyle: { opacity: 0 },
            areaStyle: { color: '#B8E986' },
            data: maxData
          }
        ]
      }

      myEchart.setOption(myOption)
    },
    /**
     * 改变核心指标
     */
    changeCode(code) {
      this.code = code
      this.selectFlag = false
    }
  }
}
</script>

<style lang="scss" scoped>
.wrapper {
  width: 100vh;
  height: 100vw;
  transform-origin: 0 0;
  background-color: #fff;
  transform: rotate(90deg);
  position: fixed;
  left: 100%;
  top: 0;

  .header {
    display: flex;
    margin-top: 4vw;
    margin-left: 2vh;
    align-items: center;

    .header-title {
      color: #666;
      font-size: 16px;
      text-align: left;
      font-weight: 400;
      margin-right: 10px;
    }

    .heard-select {
      width: 400px;
      height: 10vw;
      color: #333;
      font-size: 18px;
      font-weight: 400;
      line-height: 10vw;
      border-radius: 3px;
      position: relative;

      .drop-up {
        width: 100%;
        display: flex;
        border-radius: 3px;
        align-items: center;
        justify-content: center;
        border: 1px solid #E8E8E8;
      }

      .select-drop {
        width: 100%;
        border-radius: 3px;
        background-color: #fff;
        border: 1px solid #E8E8E8;
        position: absolute;
        top: 40px;
        left: 0;
        z-index: 999;
      }

      .select-mask {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(255, 255, 255, 0);
        z-index: 998;
      }
    }
  }

  .chart {
    width: 96vh;
    height: 80vw;
    margin: 2vw 2vh 0;

    .chart-box {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
