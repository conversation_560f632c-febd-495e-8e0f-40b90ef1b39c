<template>
  <div class="wrapper">
    <div class="menu">
      <div class="menu-box">
        <div
          v-for="menuItme in menuList"
          :key="menuItme.index"
          @click="changeMenu(menuItme)"
          :class="{'menu-item': true, 'menu-item-active': menuItme.active, 'menu-item-prev': menuItme.prev}"
        >
          <span class="menu-item-text">
            <span v-if="false" class="dot"></span>
            {{menuItme.name}}</span>
          <img
            v-if="menuItme.active"
            src="../images/arrow.png"
            class="menu-item-arrow"
          >
        </div>
      </div>
    </div>
    <div class="content">
      <!-- 院内一般情况 -->
      <his-generally
        v-if="metricsCode === '1'"
        :propData="detailData1"
        @saveData="saveDetailData"
      />
      <!-- 血糖情况 -->
      <his-bg
        v-if="metricsCode === '2'"
        :propData="detailData2"
        @saveData="saveDetailData"
      />
      <!-- 胰岛功能 -->
      <his-insulin
        v-if="metricsCode === '3'"
        :propData="detailData3"
        @saveData="saveDetailData"
      />
      <!-- 胰岛素相关抗体 -->
      <his-insulin-antibody
        v-if="metricsCode === '4'"
        :propData="detailData4"
        @saveData="saveDetailData"
      />
      <!-- 肝肾功能、血脂 -->
      <his-liver
        v-if="metricsCode === '5'"
        :propData="detailData5"
        @saveData="saveDetailData"
      />
      <!-- 糖尿病大血管病变 -->
      <his-vein
        v-if="metricsCode === '6'"
        :propData="detailData6"
        @saveData="saveDetailData"
      />
      <!-- 糖尿病肾病指标 -->
      <his-nephropathy
        v-if="metricsCode === '7'"
        :propData="detailData7"
        @saveData="saveDetailData"
      />
      <!-- 糖尿病眼底病变 -->
      <his-eye
        v-if="metricsCode === '8'"
        :propData="detailData8"
        @saveData="saveDetailData"
      />
      <!-- 糖尿病神经病变 -->
      <his-nerve
        v-if="metricsCode === '9'"
        :propData="detailData9"
        @saveData="saveDetailData"
      />
      <!-- 其他检查 -->
      <his-other
        v-if="metricsCode === '10'"
        :propData="detailData10"
        @saveData="saveDetailData"
      />
      <!-- 腹部B超 -->
      <his-belly
        v-if="metricsCode === '11'"
        :propData="detailData11"
        @saveData="saveDetailData"
      />
    </div>
  </div>
</template>

<script>
import HisBg from '../components/HisBg.vue'
import HisLiver from '../components/HisLiver.vue'
import HisEye from '../components/HisEye.vue'
import HisNerve from '../components/HisNerve.vue'
import HisBelly from '../components/HisBelly.vue'
import HisVein from '../components/HisVein.vue'
import HisInsulin from '../components/HisInsulin.vue'
import HisGenerally from '../components/HisGenerally.vue'
import HisOther from '../components/HisOther.vue'
import HisNephropathy from '../components/HisNephropathy.vue'
import HisInsulinAntibody from '../components/HisInsulinAntibody.vue'
import { getMetricsGroup, getMetricsDetail } from '@/api/newHealthReport.js'

export default {
  data: () => {
    return {
      menuList: [],
      metricsCode: ''
    }
  },
  components: {
    'his-bg': HisBg,
    'his-liver': HisLiver,
    'his-eye': HisEye,
    'his-nerve': HisNerve,
    'his-vein': HisVein,
    'his-belly': HisBelly,
    'his-generally': HisGenerally,
    'his-insulin': HisInsulin,
    'his-other': HisOther,
    'his-insulin-antibody': HisInsulinAntibody,
    'his-nephropathy': HisNephropathy
  },
  created() {
    // 初始化
    this.init()
  },
  methods: {
    /**
     * 初始化
     */
    init() {
      // 获取菜单栏
      this.getMenuList()
      // 获取详细
      this.getDetail()
    },
    /**
     * 获取菜单栏
     */
    getMenuList() {
      getMetricsGroup().then(res => {
        if (res.status === 0) {
          this.menuList = res.data.map((item, index) => {
            item.oIndex = index
            item.active = item.prev = false
            if (index === 0) item.active = true
            return item
          })
        } else {
          this.$toast(res.msg)
        }
      })
    },
    /**
     * 获取详细
     */
    getDetail() {
      getMetricsDetail(this.metricsCode).then(res => {
        if (res.status === 0) {
          let code = res.data.param.code
          this.metricsCode = code
          this[`detailData${code}`] = res.data
        } else {
          this.$toast(res.msg)
        }
      })
    },
    /**
     * 改变菜单
     * @param {Object} menuItem 菜单项
     */
    changeMenu(menuItem) {
      this.menuList.forEach((item, index) => {
        item.active = item.prev = false
        if ((menuItem.oIndex - index) === 1) item.prev = true
        if (menuItem.oIndex === index) {
          menuItem.active = true
          this.metricsCode = item.code
        }
      })
    },
    /**
     * 保存详细数据
     * @param {Number} code 指标组编码
     * @param {Object} data 详细数据
     */
    saveDetailData(code, data) {
      if (!this[`detailData${code}`]) {
        this[`detailData${code}`] = data
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "@/assets/scss/newHealthReport/his/hisSum.scss";
  .menu-item-text{
    position: relative;
  }
  .dot{
    width: 8px;
    height: 8px;
    background:#F5222D;
    position: absolute;
    border-radius: 50%;
    left:-4px;
    top: -5px;
  }
</style>
