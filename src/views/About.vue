<template>
  <div class="about">
    <h1>This is an about page</h1>
    <router-link :to="{path:'/about', query:{page:1}}">About</router-link>

    <cc-svg-icon icon-class="user" class-name="icon-class" />
  </div>
</template>

<script>

export default {
  data() {
    return {}
  },
  watch: {
    // 方法1
    '$route'(to, from) { // 监听路由是否变化
      console.log(to, 'to')
      console.log(from, 'from')
    }
  },
  created() {
    console.log(this.$route.query.page, 'page')
  },
  methods: {}
}
</script>

<style lang="scss" scoped>
  .icon-class {
    color: red;
    width: 48px !important;
    height: 45px !important;
  }

</style>
