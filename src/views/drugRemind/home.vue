<template>
  <div class="main" ref="main" v-if="flag">

    <div  @click="newMiniApp">跳小程序</div>
    <div v-if="source_type == 1 || source_type == 6 || source_type == 3">

      <div class="follow" v-if="subscribe == 0">
        <p class="p1">
          <span>关注“MMC管家”公众号</span><span>获得用药提醒的通知权限</span>
        </p>
        <p class="p2" @click="navUrlToScripe">关注</p>
      </div>
      <div class="follower" v-if="subscribe == 1">
        "MMC管家"公众号会根据你设定的时间提醒你吃药哦
      </div>
    </div>
    <div class="noData" v-if="info.length == 0">
      <div class="sugarTime" v-if="source_type == 1">
        说明：{{ sugarTimes }}
      </div>
      <img
        :class="source_type == 1 || source_type == 6 || source_type == 3 ? 'img1' : 'img11'"
        src="./../../assets/images/yongyaotixing.png"
      />
      <div class="method">按时用药是控制血糖的有效方法</div>
      <div class="remind">现在就添加你的用药提醒吧</div>
      <!-- <img class="img2" v-if="source_type == 1 || source_type == 6" src="./../../assets/images/jiantou.png"/> -->
    </div>

    <div
      :class="source_type == 1 || source_type == 6 || source_type == 3 ? 'wxhasData' : 'apphasData'"
      v-if="info.length > 0"
    >
      <ul v-for="(item, index) in info" :key="index" @click="addDrug(item.id)">
        <li class="title">
          <span
            :class="{
              'first-span': index === 0,
              'first-span-child': index === 0,
            }"
            >最近的提醒时间&nbsp;&nbsp;{{
              item.last_remind_time_new == ""
                ? ""
                : "(" + item.last_remind_time_new + ")"
            }}</span
          ><span :class="{ 'first-span': index === 0 }">{{
            item.use_number == 0 ? "永不提醒" : "每天" + item.use_number + "次"
          }}</span
          ><span :class="{ 'first-span': index === 0 }">{{
            item.remind_frequency == 1
              ? "每天提醒"
              : "每" + item.remind_frequency + "天提醒"
          }}</span>
        </li>
        <li class="li" v-for="(subItem, index) in item.json_drug" :key="index">
          <span>{{ subItem.name }}</span>
          <span>{{ subItem.drug_dose + subItem.unit }}</span>
        </li>
      </ul>
      <div class="sugarTime" v-if="source_type == 1 ">
        <span style="color: #fa5151; margin-right: 4px">*</span
        >提醒你用药的时间为{{ sugarTimes }}
      </div>
    </div>
    <div class="appadd" v-if="source_type == 2 && info.length == 0">
      <div class="add" @click="addDrug(0)">
        <img src="./../../assets/images/jiahao.png" />添加用药提醒
      </div>
      <div class="noadd" @click="noDrug()" v-if="isNoDrug">我没有用药</div>
    </div>
    <div class="wxadd" @click="addDrug(0)" v-else>
      <img src="./../../assets/images/jiahao.png" />添加用药提醒
    </div>
    <!-- 弹窗 -->
    <div class="mask" v-show="remark"></div>
    <div class="deleteDrug" v-show="remark">
      <p>提示</p>
      <p>{{ dataOrigin }}</p>
      <p @click="sure">确定</p>
    </div>
  </div>
</template>

<script>
import {
  getRemindList,
  getRemindTime,
  getRefreshDatas,
  addHealthy,
} from "@/api/drugRemind";
import { isSubscribe } from "@/api/subscribeApi";

import { hideMission, detailMission } from "@/api/guanjia/guoshou/index";

import { Toast, Dialog } from "vant";
import wx from "weixin-js-sdk";

export default {
  data() {
    return {
      info: [],
      subscribe: 0,
      flag: false,
      isNoDrug: false,
      source_type: "", // 1来源是控糖营小程序 2来源是管家app 6控糖助手小程序  3来源是m90控糖营小程序
      sugarTimes: "7月1日-7月21日",
      dataOrigin: "已于MMC控糖助手小程序设置用药提醒，数据已同步",
      remark: false,
    };
  },
  components: {},
  computed: {},
  created() {
    this.source_type = this.$route.query.source_type;
    this.init();
  },
  mounted() {
    // 如果检测到页面是从“往返缓存”中读取的，刷新页面
    if (this.source_type == 2 || this.source_type == 3) {

      window.addEventListener(
        "pageshow",
        (e) => {
          if (
            e.persisted ||
            (window.performance && window.performance.navigation.type == 2)
          ) {
            location.reload();
          }
        },
        false
      );
    }
  },
  methods: {
    newMiniApp(){
      wx.miniProgram.postMessage({ data: {foo: 'bar'} })
      wx.miniProgram.navigateBack({delta: 1})
    },
    // 初始化
    init() {
      let that = this;
      if (this.source_type == 1 || this.source_type == 6 || this.source_type == 3) {
        isSubscribe().then(function (res) {
          if (res.status === 0) {
            that.subscribe = res.data.subscribe;
          } else {
            Toast(res.msg);
          }
        });
        if (this.source_type == 1) {
          getRemindTime().then(function (res) {
            // console.log(res)
            if (res.status === 0) {
              that.sugarTimes = res.data.drug_remind_str;
            } else {
              Toast(res.msg);
            }
          });
        }
      }
      getRefreshDatas({}).then((res) => {
        console.log(res);
        if (res.status == 0) {
          if (res.data.is_owner_remind == 0) {
            that.remark = true;
            that.dataOrigin = res.data.info;
          }
        } else {
          Toast(res.msg);
        }
      });
      getRemindList({})
        .then((res) => {
          // console.log(res)
          if (res.status == 0) {
            that.info = res.data;
            that.flag = true;
          } else {
            Toast(res.msg);
          }
        })
        .then(() => {
          that.$refs.main.style.minHeight = window.innerHeight + "px";
          if (that.info.length > 0) {
            that.$refs.main.style.background = "rgba(242,242,242,1)";
          }
        });

      // 获取用药提醒任务状态
      detailMission().then((res) => {
        if (res.status == 0) {
          this.isNoDrug = res.data;
        } else {
          Toast(res.msg);
        }
      });
    },
    navUrlToScripe() {
      this.noDoubleTap(() => {
        wx.miniProgram.navigateTo({
          url: "/pages/home/<USER>/innerWebView/innerWebView?type=1",
        });
      });
    },
    addDrug(id) {
      if (id == 0 || id == "") {
        if (this.info.length >= 5) {
          Toast("最多只能添加5条用药提醒");
          return false;
        }
      }
      this.noDoubleTap(() => {
        this.$router.push({
          name: "Drug.Add",
          query: { id: id, source_type: this.source_type },
        });
      });
    },
    // 没有在用药
    noDrug() {
      // const UA = navigator.userAgent
      // const isAndroid = UA.indexOf('Android') > -1 || UA.indexOf('Adr') > -1 // android终端
      // const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
      // // 原生app方法名称：jumpAppPopHealthmanager
      // if (isAndroid) {
      //   console.log('安卓')
      //   window.android.jumpAppPopHealthmanager()
      // } else if (isIOS) {
      //   console.log('苹果')
      //   window.webkit.messageHandlers.jumpAppPopHealthmanager.postMessage('')
      // } else {
      //   Toast('跳转失败')
      // }

      Dialog.confirm({
        message:
          "确认没有用药，将为您关闭用药设置任务，您还是可以在“我的”板块中找到此功能!",
      })
        .then(() => {
          hideMission({ mission_type_id: 8 }).then(() => {
            Toast("设置成功");
            this.isNoDrug = false;
          });
        })
        .catch(() => {
          console.log("取消");
        });
    },
    sure() {
      let that = this;
      addHealthy({}).then((res) => {
        console.log(res);
        if (res.status == 0) {
          that.remark = false;
          console.log("success");
        } else {
          Toast(res.msg);
        }
      });
    },
  },
};
</script>

<style lang='scss' scoped>
@import "@/assets/scss/drugRemind/home.scss";
</style>
