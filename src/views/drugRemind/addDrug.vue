<template>
  <div class="main">
    <div class="add">
      <p>服用药品</p>
      <div class="addDom" v-for="(item, index) in drugList" :key="index">
        <div class="drugs">
          <span class="label-name">名称</span>
          <input
            type="text"
            placeholder="请输入"
            v-model="item.name"
            @input="change(index, $event)"
            @click="clickInput"
            maxlength="20"
          />

          <span class="jiantou"><van-icon name="arrow" /></span>
          <p @click="drugStore(index)">
            <img src="./../../assets/images/yaopinku.png" /><span>药品库</span>
          </p>
          <ul class="ul" v-if="search && index == currentIndex">
            <li
              v-for="(item, index) in searchList"
              :key="index"
              @click="searchSure(item)"
            >
              {{ item.general_name
              }}<span v-if="item.drug_name">[{{ item.drug_name }}]</span>
            </li>
          </ul>
        </div>
        <div class="doses">
          <p>剂量</p>
          <div class="chooseDose">
            <van-cell is-link @click="showPopup(item, index)">{{
              item.drug_dose == "" ? "请选择" : item.drug_dose + item.unit
            }}</van-cell>
            <van-popup v-model="showPicker" position="bottom">
              <van-picker
                :columns="columns"
                title="选择剂量"
                show-toolbar
                :value="item.drug_dose"
                v-model="item.drug_dose"
                @cancel="showPicker = false"
                @confirm="onConfirm"
              />
            </van-popup>
          </div>
        </div>
        <div class="delDom" @click="delDom(index)" v-if="index > 0">删除</div>
      </div>
      <div class="btn" @click="addDrug" v-if="drugList.length < 5">
        <img src="./../../assets/images/jiahao.png" />添加药品
      </div>
    </div>
    <div class="drugTime">
      <p class="p">用药时间</p>
      <div class="times">
        <van-cell is-link @click="showDose">{{ drugDose }}</van-cell>
        <van-popup v-model="showTime" position="bottom">
          <van-picker
            :columns="columnTime"
            title="选择每天用药次数"
            show-toolbar
            :value="drugDose"
            v-model="drugDose"
            @cancel="showTime = false"
            @confirm="TimeConfirm"
          />
        </van-popup>
      </div>
      <div class="dayRemind">
        <div
          class="select clearfix"
          v-for="(item, index) in selectTimes"
          :key="index"
          v-if="doseNumber > index"
        >
          <p>第{{ index + 1 }}次</p>
          <van-cell
            is-link
            @click="showTimesSelect(index, selectTimes[index].num)"
            >{{ selectTimes[index].num }}</van-cell
          >
          <van-popup
            v-model="selectTimes[index].showTimeSelect"
            position="bottom"
          >
            <van-picker
              :columns="columnTimes"
              title="选择用药时间"
              show-toolbar
              :value="selectTimes[index].num"
              v-model="selectTimes[index].num"
              @cancel="selectTimes[index].showTimeSelect = false"
              @confirm="TimeConfirms($event, index)"
            />
          </van-popup>
        </div>
      </div>
    </div>
    <div class="drugNumber">
      <p class="p">提醒频次</p>
      <div class="drugNumbertimes">
        <van-cell is-link @click="chooseRate">{{
          rate == 1 ? "每天提醒" : "每" + rate + "天提醒"
        }}</van-cell>
        <van-popup v-model="rateRemind" position="bottom">
          <van-picker
            :columns="rateList"
            title="选择每隔几天提醒"
            show-toolbar
            :value="rate"
            v-model="rate"
            @cancel="rateRemind = false"
            @confirm="rateConfirm"
          />
        </van-popup>
      </div>
    </div>
    <div class="save" @click="saveDrug">保存</div>
    <div class="del" @click="delDrug" v-if="id != 0">删除本次用药提醒</div>
    <!-- 弹窗 -->
    <div class="mask" v-show="remark"></div>
    <div class="deleteDrug" v-show="remark">
      <p>提示</p>
      <p>确定要删除此条用药提醒吗？</p>
      <p>
        <span @click="cancel">取消</span>
        <span @click="sure">确定</span>
      </p>
    </div>
    <div class="mask" v-if="drugRemark"></div>
    <div class="drugModal" v-if="drugRemark">
      <p @click="close" src="./../../assets/images/guanbi.png">关闭</p>
      <div>
        <ul v-for="(item, index) in drugNameList" :key="index">
          <li class="li">{{ item.name }}</li>
          <li
            class="lis"
            v-for="(subItem, index) in item.children"
            @click="chooseLi(subItem)"
            :key="index"
          >
            {{ subItem.general_name
            }}<span v-if="subItem.drug_name">[{{ subItem.drug_name }}]</span>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getDetails,
  addRemind,
  delRemind,
  getDrugList,
} from "@/api/drugRemind";
import { Toast, Dialog } from "vant";
import wx from "weixin-js-sdk";
export default {
  data() {
    return {
      id: "",
      showPicker: false,
      columns: [
        { values: ["0", "1", "2", "3", "4"], defaultIndex: 0 },
        { values: [".0", ".25", ".5", ".75"], defaultIndex: 0 },
        {
          values: ["粒", "片", "袋", "mg", "g", "ug", "U", "ml"],
          defaultIndex: 1,
        },
      ],
      dose: "",
      drugResult: "",
      drugList: [
        {
          name: "",
          drug_dose: "",
          unit: "",
        },
      ],
      currentIndex: "",
      columnTime: [
        {
          values: ["每天1次", "每天2次", "每天3次", "每天4次", "永不提醒"],
          defaultIndex: 2,
        },
      ],
      columnTimes: [
        {
          values: [
            "00",
            "01",
            "02",
            "03",
            "04",
            "05",
            "06",
            "07",
            "08",
            "09",
            "10",
            "11",
            "12",
            "13",
            "14",
            "15",
            "16",
            "17",
            "18",
            "19",
            "20",
            "21",
            "22",
            "23",
          ],
          defaultIndex: 0,
        },
        { values: [":00", ":15", ":30", ":45"], defaultIndex: 0 },
      ],
      drugDose: "每天3次",
      doseNumber: 3,
      selectTimes: [
        { num: "07:00", showTimeSelect: false },
        { num: "11:00", showTimeSelect: false },
        { num: "17:00", showTimeSelect: false },
        { num: "21:00", showTimeSelect: false },
      ],
      showTime: false,
      showTimeSelect: false,
      rateRemind: false,
      rate: "1",
      rateList: [
        { values: ["1", "2", "3", "4", "5", "6", "7"], defaultIndex: 0 },
      ],
      remark: false,
      saveData: {
        data: [],
        id: "",
        use_number: "",
        remind_time: {},
        remind_frequency: "",
      },
      insulin_drug_ids: [31, 32, 33, 34, 35, 36, 51, 53, 54, 55, 56],
      drugNameList: [],
      drugRemark: false,
      search: false,
      searchList: [],
      allDrugList: [],
      drugTypeId: "",
      requestJudge: false,
      source_type: "",
    };
  },
  components: {},
  computed: {},
  created() {
    this.source_type = this.$route.query.source_type;
    this.id = this.$route.query.id;
    // console.log(this.id)
    if (this.id != 0 && this.id != "") {
      this.init();
    }
    this.drugName();
  },
  watch: {},
  methods: {
    // 初始化
    init() {
      let that = this;
      getDetails({ id: this.id }).then((res) => {
        // console.log(res)
        if (res.status == 0) {
          that.drugList = res.data.json_drug;
          that.rate = res.data.remind_frequency;
          this.rateList[0].defaultIndex = Number(res.data.remind_frequency) - 1;
          if (res.data.use_number == 0) {
            that.drugDose = "永不提醒";
            that.doseNumber = 0;
            that.columnTime[0].defaultIndex = 5;
            that.saveData.remind_time = {};
          } else {
            that.doseNumber = res.data.use_number;
            this.columnTime[0].defaultIndex = Number(res.data.use_number) - 1;
            that.drugDose = "每天" + res.data.use_number + "次";
            let timeArr = Object.values(res.data.remind_time);
            timeArr.forEach((item, key) => {
              that.selectTimes[key].num = item;
            });
            // console.log(that.selectTimes)
          }
        } else {
          Toast(res.msg);
        }
      });
    },
    drugName() {
      let that = this;
      getDrugList({}).then((res) => {
        // console.log(res)
        if (res.status == 0) {
          that.drugNameList = res.data[1].concat(res.data[2]);
          Object.values(that.drugNameList).forEach((v, k) => {
            v.children.forEach((item) => {
              that.allDrugList.push(item);
            });
          });
        } else {
          Toast(res.msg);
        }
      });
    },
    // 保存用药提醒
    saveDrug() {
      let name = this.drugList.find((item) => item.name == "");
      let dose1 = this.drugList.find((item) => item.drug_dose == "");
      let dose2 = this.drugList.find((item) => item.drug_dose == "0.0");
      if (name) {
        Toast("请完善药品名");
        return false;
      }
      if (dose1) {
        Toast("请选择药品剂量");
        return false;
      }
      if (dose2) {
        Toast("药品剂量不能为0.0");
        return false;
      }
      Dialog.confirm({
        messageAlign: "center",
        title: "提示",
        message: "确定要保存此条用药提醒吗？",
        confirmButtonColor: "#576B95",
      }).then(() => {
        if (this.drugDose == "每天1次") {
          this.saveData.remind_time = {
            1: this.selectTimes[0].num,
          };
        } else if (this.drugDose == "每天2次") {
          this.saveData.remind_time = {
            1: this.selectTimes[0].num,
            2: this.selectTimes[1].num,
          };
        } else if (this.drugDose == "每天3次") {
          this.saveData.remind_time = {
            1: this.selectTimes[0].num,
            2: this.selectTimes[1].num,
            3: this.selectTimes[2].num,
          };
        } else if (this.drugDose == "每天4次") {
          this.saveData.remind_time = {
            1: this.selectTimes[0].num,
            2: this.selectTimes[1].num,
            3: this.selectTimes[2].num,
            4: this.selectTimes[3].num,
          };
        } else if (this.drugDose == "永不提醒") {
          this.saveData.remind_time = {};
        }
        this.saveData.data = this.drugList;
        this.saveData.id = this.id;
        this.saveData.use_number = this.useNumber(this.drugDose);
        this.saveData.remind_frequency = this.rate;
        // console.log(this.saveData)
        let that = this;
        this.noDoubleTap(() => {
          // console.log('11111')
          addRemind(this.saveData).then((res) => {
            // console.log(res)
            if (res.status == 0) {
              Toast("保存成功");
              if (that.source_type == 1) {
                setTimeout(() => {
                  wx.miniProgram.redirectTo({
                    url: "/pages/my/page/drugRemind/drugRemind",
                  });
                }, 1000);
              } else if (that.source_type == 6) {
                setTimeout(() => {
                  wx.miniProgram.redirectTo({
                    url: "/pages/pressure/page/drugRemind/drugRemind",
                  });
                }, 1000);
              } else {
                setTimeout(() => {
                  // that.$router.push({
                  //   name: 'Drug.Home',
                  //   query: { 'source_type': that.source_type }
                  // })
                  window.history.go(-1);
                }, 1000);
              }
            } else {
              Toast(res.msg);
            }
          });
          setTimeout(() => {
            that.$btnDisabled = true;
          }, 2000);
        }, false);
      });
    },
    // 删除用药提醒
    delDrug() {
      this.remark = true;
    },
    // 关闭删除当前用药提醒
    cancel() {
      this.remark = false;
    },
    sure() {
      let that = this;
      delRemind({ id: this.id }).then((res) => {
        // console.log(res)
        if (res.status == 0) {
          Toast("删除成功");
          if (that.source_type == 1) {
            setTimeout(() => {
              wx.miniProgram.redirectTo({
                url: "/pages/my/page/drugRemind/drugRemind",
              });
            }, 1000);
          } else if (that.source_type == 6) {
            setTimeout(() => {
              wx.miniProgram.redirectTo({
                url: "/pages/pressure/page/drugRemind/drugRemind",
              });
            }, 1000);
          } else {
            setTimeout(() => {
              // that.$router.push({
              //   name: 'Drug.Home',
              //   query: { 'source_type': that.source_type }
              // })
              window.history.go(-1);
            }, 1000);
          }
        } else {
          Toast(res.msg);
        }
      });
      this.remark = false;
    },
    // 添加用药dom
    addDrug() {
      this.drugList.push({
        name: "",
        drug_dose: "",
        unit: "",
      });
    },
    // 删除用药dom
    delDom(index) {
      this.drugList.splice(index, 1);
    },
    // 弹出剂量
    showPopup(item, index) {
      // console.log(item, index, this.drugTypeId)
      let arr = [];
      arr = item.drug_dose.split(".");
      let index1 = this.columns[0].values.indexOf(arr[0]);
      let index2 = this.columns[1].values.indexOf("." + arr[1]);
      let index3 = this.columns[2].values.indexOf(item.unit);
      // console.log(index1, index2, index3)
      this.columns[0].defaultIndex = index1;
      this.columns[1].defaultIndex = index2;
      this.columns[2].defaultIndex = index3;
      this.currentIndex = index;
      if (this.drugTypeId != "") {
        if (this.insulin_drug_ids.includes(this.drugTypeId) == true) {
          this.columns[2].defaultIndex = 6;
        } else {
          this.columns[2].defaultIndex = 3;
        }
      }
      this.showPicker = true;
    },
    // 打开药品库
    drugStore(index) {
      this.drugRemark = true;
      this.currentIndex = index;
    },
    close() {
      this.drugRemark = false;
    },
    clickInput(e) {
      e.stopPropagation();
      e.target.focus();
    },
    // 模糊查询
    change(index, e) {
      this.searchList = [];
      this.currentIndex = index;
      if (e.target.value != "") {
        this.allDrugList.map((item, index) => {
          if (item.general_name.match(e.target.value)) {
            this.searchList.push(item);
          }
        });
      } else {
        this.searchList = [];
        this.search = false;
      }
      if (this.searchList.length > 0) {
        this.search = true;
      }else{
        this.search = false;
      }
    },
    // 模糊查询选择
    searchSure(item) {
      // console.log(item.type_id)
      if (item.drug_name == "") {
        this.drugList[this.currentIndex].name = item.general_name;
      } else {
        this.drugList[this.currentIndex].name =
          item.general_name + "[" + item.drug_name + "]";
      }
      this.drugTypeId = item.type_id;
      // if (this.insulin_drug_ids.includes(item.type_id)==true) {
      //   this.columns[2].defaultIndex = 4
      // } else {
      //   this.columns[2].defaultIndex = 1
      // }
      this.search = false;
    },
    // 选择药物
    chooseLi(item) {
      if (item.drug_name == "") {
        this.drugList[this.currentIndex].name = item.general_name;
      } else {
        this.drugList[this.currentIndex].name =
          item.general_name + "[" + item.drug_name + "]";
      }
      this.drugTypeId = item.type_id;
      // if (this.insulin_drug_ids.includes(item.type_id)==true) {
      //   this.columns[2].defaultIndex = 4
      // } else {
      //   this.columns[2].defaultIndex = 1
      // }
      this.drugRemark = false;
    },
    // 确定
    onConfirm(e) {
      // console.log('this.onAddrConfirm=>e', e.slice(0, 2), e.slice(-1))
      this.drugList[this.currentIndex].drug_dose = e.slice(0, 2).join("");
      this.drugList[this.currentIndex].unit = e.slice(-1).join("");
      this.showPicker = false;
    },
    // 用药时间
    showDose() {
      this.showTime = true;
    },
    TimeConfirm(e) {
      // console.log(e)
      this.drugDose = e.join("");
      let num = this.useNumber(this.drugDose);
      this.doseNumber = num;
      this.showTime = false;
    },
    showTimesSelect(index, value) {
      // console.log(index, value)
      let index1 = this.columnTimes[0].values.indexOf(value.split(":")[0]);
      let index2 = this.columnTimes[1].values.indexOf(
        ":" + value.split(":")[1]
      );
      this.columnTimes[0].defaultIndex = index1;
      this.columnTimes[1].defaultIndex = index2;
      this.selectTimes[index].showTimeSelect = true;
    },
    TimeConfirms(e, index) {
      // console.log(e.join(''), index)
      this.selectTimes[index].num = e.join("");
      this.selectTimes[index].showTimeSelect = false;
      // console.log(this.selectTimes)
    },
    // 提醒频次
    chooseRate() {
      this.rateRemind = true;
    },
    rateConfirm(e) {
      this.rate = e.join("");
      this.rateRemind = false;
    },
    // 每天几次转化
    useNumber(type) {
      let maps = new Map([
        ["每天1次", [1]],
        ["每天2次", [2]],
        ["每天3次", [3]],
        ["每天4次", [4]],
        ["永不提醒", [0]],
        ["default", [""]],
      ]);
      let map = maps.get(type) || maps.get("default");
      let result = map[0];
      return result;
    },
  },
};
</script>

<style lang='scss' scoped>
@import "@/assets/scss/drugRemind/addDrug.scss";
</style>
