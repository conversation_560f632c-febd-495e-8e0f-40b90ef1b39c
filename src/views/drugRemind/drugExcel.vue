<template>
  <div class="main" ref="main" v-if="flag">
    <div class="noData" v-if="info.length == 0">
      <img src="./../../assets/images/wushuju.png" />
      <p>暂时没有数据呢</p>
    </div>
    <div class="hasData" v-if="info.length > 0">
      <div class="title">记录自己{{currentTime}}的用药状况</div>
      <div class="list" v-for="(item, index) in info" :key=index>
        <h2>
          <p class="clearfix" v-if="item.is_on_time == 2"><span class="times">{{item.time}}</span></p>
          <p class="clearfix" v-if="item.is_on_time == 0"><span class="times">{{item.time}}</span><img src="https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/tishi.png" /><span class="excelStatus">未服药</span></p>
          <p class="clearfix" v-if="item.is_on_time == 1"><span class="times">{{item.time}}</span><img src="https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/checked.jpg" /><span class="excelStatus">已服药</span></p>
        </h2>
        <ul v-for="(subItem, ind) in item.list" :key=ind>
          <li>
            <p><span>{{subItem.drug_name}}</span><span>{{subItem.drug_dose + subItem.drug_unit}}</span></p>
            <div v-if="subItem.drug_note != ''">
              <span>注意事项：</span>
              <span>{{subItem.drug_note}}</span>
            </div>
          </li>
        </ul>
        <div class="btn" v-if="item.is_on_time == 2">
          <span @click="setStatus(0, item.id)">忘记服用了</span><span @click="setStatus(1, item.id)">已按时服用</span>
        </div>
      </div>
    </div>
    <div class="wxadd" @click="addDrug(0)" v-if="info.length > 0"><img src="./../../assets/images/jiahao.png">添加用药提醒</div>
  </div>
</template>

<script>
  import { getDrugExcel, setDrugStatus, getRemindList } from '@/api/drugRemind'
  import moment from 'moment'
  import { Toast } from 'vant'

  export default {
    data() {
      return {
        info: [],
        drugList: [],
        source_type: '',
        flag: false,
        currentTime: ''
      }
    },
    components: {},
    computed: {},
    created() {
      this.source_type = this.$route.query.source_type
      let day = new Date()
      this.currentTime = moment(day).format('MM月DD日')
      this.init()
    },
    methods: {
      // 初始化
      init() {
        let that = this
        getDrugExcel({}).then(res => {
          // console.log(res)
          if (res.status == 0) {
            that.info = res.data
            that.flag = true
          } else {
            Toast(res.msg)
          }
        }).then(() => {
          that.$refs.main.style.minHeight = window.innerHeight + 'px'
          if (that.info.length > 0) {
            that.$refs.main.style.background = 'rgba(242,242,242,1)'
          }
        })
        getRemindList({}).then(res => {
          // console.log(res)
          if (res.status == 0) {
            that.drugList = res.data
          } else {
            Toast(res.msg)
          }
        })
      },
      setStatus(statu, ids) {
        let that = this
        let datas = { id: ids, is_on_time: statu }
        setDrugStatus(datas).then(res => {
          // console.log(res)
          if (res.status == 0) {
            that.init()
          } else {
            Toast(res.msg)
          }
        })
      },
      addDrug(id) {
        if (id == 0 || id == '') {
          if (this.drugList.length >= 5) {
            Toast('最多只能添加5条用药提醒')
            return false
          }
        }
        this.noDoubleTap(() => {
          this.$router.push({
            name: 'Drug.Add',
            query: { id: 0, source_type: this.source_type }
          })
        })
      }
    }
  }
</script>

<style lang='scss' scoped>
  @import '@/assets/scss/drugRemind/drugExcel.scss'
</style>
