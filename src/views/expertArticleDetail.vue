<template>
  <div class="content">
    <div class="title">
      <span>{{ title }}</span>
    </div>
    <div class="watchTime">
      <img :src="img">
      <span>{{ author }}</span>
      <P>{{ start_at }}</P>
    </div>
    <!--<div class="articleHead">-->
      <!--<img :src="img" class="logo">-->
      <!--<div>-->
        <!--<p class="name">{{ author }}</p>-->
        <!--<p class="hospId">{{ description }}</p>-->
      <!--</div>-->
    <!--</div>-->
    <div class="article" v-html="content">
      {{ content }}
    </div>
  </div>
</template>
<script>
import { getDetail2 } from '@/api/newDetail'
export default {
  data() {
    return {
      title: '',
      description: '',
      click: '',
      start_at: '',
      content: '',
      author: '',
      img: '',
      id: '',
      type: 2
    }
  },
  created() {
    this.init()
    console.log(2222222222222222)
  },
  methods: {
    async init() {
      console.log(this.type, 1111111111111)
      this.id = this.$route.query.id
      await getDetail2(this.id).then(res => {
        this.title = res.title
        this.description = res.description
        this.click = res.click
        this.start_at = res.real_release_time
        this.author = res.author
        this.img = res.author_logo
        this.content = res.content
      })
      console.log(this.type, 1111111111111)
    }
  }
}
</script>

<style scoped>
  .content{
    width: 375px;
    height: auto;
    padding-bottom: 25px;
  }
  .newsDetail h3{
    height: 12.5px;
    padding-top: 9px;
    padding-left: 12.5px;
    font-size: 17px;
    color:rgba(51,51,51,1);
    font-weight: normal;
  }
  .title{
    width: 375px;
    min-height: 24px;
    margin: 0 auto;
    font-size: 17px;
    line-height: 24px;
    padding-top: 20px;
    margin-bottom: 20px;
  }
  .title span{
    text-align: left!important;
  }

  .watchTime{
    width: 375px;
    height: 30px;
    color: #999999;
    font-size: 12px;
    line-height: 30px;
    margin-bottom: 10px;
  }
  .watchTime img{
    width: 30px;
    height: 30px;
    display: block;
    margin-left: 12px;
    float: left;
  }
  .watchTime span{
    width: 150px;
    height: 30px;
    margin-left: 9px;
    float: left;
    text-align: left;
    line-height: 30px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }
  .watchTime p{
    width: 125px;
    height: 30px;
    float: right;
    margin-right: 12px;
    line-height: 30px;
  }

  .articleHead{
    width: 375px;
    min-height: 40px;
  }
  .logo{
    width: 40px;
    height: 40px;
    margin-left: 12px;
    float: left;
  }
  .articleHead span{
    width: 215px;
    height: 28px;
    line-height: 28px;
    font-size: 19px;
    float: left;
    margin-left: 4px;
    color: #333;
  }
  .articleHead div{
    width: 315px;
    height: 40px;
    font-size: 19px;
    margin-left: 4px;
    float: left;
  }
  .name, .hospId{
    line-height: 20px;
    text-align: left;
    padding-left: 5px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
  .hospId{
    color:rgba(102,102,102,1);
  }
  .article{
    width: 355px;
    height: auto;
    word-break: break-word;
    margin-top: 15px;
    text-align: left;
    padding: 0 10px;
    font-size: 16px;
  }
  .article>>>img{
    max-width: 100%;
    max-height: 100%;
  }
  .article>>>p, .article>>>div{
    width: 100%;
    font-size: 16px;
    color: #333333;
    text-align: left;
    line-height: 26px;
    margin-bottom: 15px;
  }
  .article>>>video{
    margin-bottom: 15px;
    width: 100%;
  }
  .article>>>iframe{
    width: 100%;
    height: 146px;
    display: block;
  }
</style>
