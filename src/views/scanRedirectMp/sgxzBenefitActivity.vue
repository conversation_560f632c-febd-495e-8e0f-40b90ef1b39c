<template>
  <div class="text">
    {{ text }}
  </div>
</template>

<script>
import { Toast } from 'vant';

export default {
  data(){
    return {
      text: ''
    }
  },
  methods:{
    init(){
      var wechatInfo = navigator.userAgent.match(/MicroMessenger\/([\d\.]+)/i) ;
      if( !wechatInfo ) {
        this.text = '请使用微信扫一扫功能进行扫码。'
      } else {
        if( this.compareVersion(wechatInfo[1] , '6.5.6') == -1){
          this.text = '微信版本过低，请升级微信版本后重新扫码。'
        }else{
          this.text = '跳转小程序中，如未正常跳转，请尝试使用微信扫一扫重新扫码或升级微信版本后重新扫码'
        }
      }
    },
    compareVersion(version1, version2) {
      const v1= version1.split('.').map(v => +v) // 转化成数字数组
      const v2= version2.split('.').map(v => +v)
      const b1 = v1.every(v => !isNaN(v)) // 判断数组的每一项是否是数字
      const b2 = v2.every(v => !isNaN(v)) // 如果不是则返回错误信息
      if(!b1 || !b2) {
        this.$toast('版本号含有非数字元素，无法比较大小，尝试直接跳转！')
        return 0  //版本号含有非数字元素，无法比较大小 直接返回0（调用此方法的地方 返回-1会被拦住）通过判断，尝试直接跳转
      }
      const len = Math.max(v1.length, v2.length)
      for (let i = 0; i < len; i++) {
        const num1 = parseInt(v1[i]) || 0
        const num2 = parseInt(v2[i]) || 0
        if (num1 > num2) return 1  // 如果版本号1大于版本号2，则输出1
        if (num1 < num2) return -1 // 反之则输出 -1
      }
      // 如果版本号1 等于版本号2，则输出0
      return 0
    }
  },
  mounted(){
    this.init()
  },
}
</script>

<style>
.text{
  margin-top: 30%;
}
</style>