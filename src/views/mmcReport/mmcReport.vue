<template>
  <div class="wrapper">
    <div class="warning" v-if="show">
      <div class="warningText">
        系统生成检查报告为节选，如有需求请向医师展示详细报告，数据来源于医院HIS导入和人工录入（录入数据用斜体展示），可能存在偏差，仅供参考。
      </div>
      <img
        v-if="display != 1"
        src="@/views/mmcReport/img/close.png"
        alt=""
        @click="closeWarning"
      />
    </div>
    <div class="tab">
      <p class="title">
        <span>
          <img src="@/views/mmcReport/img/baseInfo.png" alt="" />
          <span>患者基本信息</span>
        </span>
        <span class="extend" @click="change('baseInfo')" v-if="display != 1">
          <span>{{ baseInfoExtend ? "收起" : "展开" }}</span>
          <van-icon v-if="baseInfoExtend" name="arrow-up" />
          <van-icon v-if="!baseInfoExtend" name="arrow-down" />
        </span>
        <span v-else></span>
      </p>
      <div class="cellContent" v-if="baseInfoExtend">
        <van-cell title="姓名" :value="personInfo.user_info.name" />
        <van-cell title="性别" :value="sexShow" />
        <van-cell
          title="年龄"
          :value="
            personInfo.user_info.age ? personInfo.user_info.age + '岁' : ''
          "
        />
        <van-cell title="手机号" :value="personInfo.user_info.cell" />
        <van-cell
          title="体重"
          :value="
            personInfo.sign_info.weight
              ? personInfo.sign_info.weight + 'kg'
              : ''
          "
        />
        <van-cell
          title="BMI"
          :value="
            personInfo.sign_info.bmi ? personInfo.sign_info.bmi + 'kg/㎡' : ''
          "
        />
        <van-cell
          title="糖尿病类型"
          :value="personInfo.diabetes_info.diabetes.tnb_type"
        />
        <van-cell
          title="糖尿病病程"
          :value="personInfo.diabetes_info.diabetes.tnb_time"
        />
        <van-cell
          title="加入MMC时间"
          :value="
            (personInfo.user_info.init_created_at
              ? personInfo.user_info.init_created_at
              : '') +
            ' | ' +
            (personInfo.user_info.joind_time
              ? personInfo.user_info.joind_time
              : '')
          "
        />
        <van-cell title="报告生成时间" :value="personInfo.print_date" />
        <van-cell title="简要病史" :value="personInfo.historyIllnessName" />
      </div>
    </div>
    <div class="tab">
      <p class="title">
        <span>
          <img src="@/views/mmcReport/img/bloodPressureInHospital.png" alt="" />
          <span>院内血糖、血压检查</span>
        </span>
        <span
          class="extend"
          @click="change('bpInHospital')"
          v-if="display != 1"
        >
          <span>{{ bpInHospitalExtend ? "收起" : "展开" }}</span>
          <van-icon v-if="bpInHospitalExtend" name="arrow-up" />
          <van-icon v-if="!bpInHospitalExtend" name="arrow-down" />
        </span>
        <span v-else></span>
      </p>
      <div class="cellContent" v-if="bpInHospitalExtend">
        <table class="specialTable">
          <tr>
            <th rowspan="2">项目</th>
            <th colspan="2">结果</th>
            <th rowspan="2">参考值</th>
            <th rowspan="2">单位</th>
          </tr>
          <tr>
            <th>基线</th>
            <th>最新</th>
          </tr>
          <tr>
            <td>葡萄糖0分钟</td>
            <td>
              {{ personInfo.basic_lab["875001"].value }}
            </td>
            <td>
              {{
                personInfo.newes_lab["875001"]
                  ? personInfo.newes_lab["875001"].answer_name
                  : ""
              }}
            </td>
            <td>
              {{
                personInfo.newes_lab["875001"]
                  ? personInfo.newes_lab["875001"].reference
                  : ""
              }}
            </td>
            <td>
              {{
                personInfo.newes_lab["875001"]
                  ? personInfo.newes_lab["875001"].unit
                  : ""
              }}
            </td>
          </tr>
          <tr>
            <td>葡萄糖120分钟</td>
            <td>
              {{ personInfo.basic_lab["875004"].value }}
            </td>
            <td>
              {{
                personInfo.newes_lab["875004"]
                  ? personInfo.newes_lab["875004"].answer_name
                  : ""
              }}
            </td>
            <td>
              {{
                personInfo.newes_lab["875004"]
                  ? personInfo.newes_lab["875004"].reference
                  : ""
              }}
            </td>
            <td>
              {{
                personInfo.newes_lab["875004"]
                  ? personInfo.newes_lab["875004"].unit
                  : ""
              }}
            </td>
          </tr>
          <tr>
            <td>胰岛素0分钟</td>
            <td>
              {{ personInfo.basic_lab["875006"].value }}
            </td>
            <td>
              {{
                personInfo.newes_lab["875006"]
                  ? personInfo.newes_lab["875006"].answer_name
                  : ""
              }}
            </td>
            <td>
              {{
                personInfo.newes_lab["875006"]
                  ? personInfo.newes_lab["875006"].reference
                  : ""
              }}
            </td>
            <td>
              {{
                personInfo.newes_lab["875006"]
                  ? personInfo.newes_lab["875006"].unit
                  : ""
              }}
            </td>
          </tr>
          <tr>
            <td>胰岛素120分钟</td>
            <td>
              {{ personInfo.basic_lab["875009"].value }}
            </td>
            <td>
              {{
                personInfo.newes_lab["875009"]
                  ? personInfo.newes_lab["875009"].answer_name
                  : ""
              }}
            </td>
            <td>
              {{
                personInfo.newes_lab["875009"]
                  ? personInfo.newes_lab["875009"].reference
                  : ""
              }}
            </td>
            <td>
              {{
                personInfo.newes_lab["875009"]
                  ? personInfo.newes_lab["875009"].unit
                  : ""
              }}
            </td>
          </tr>
          <tr>
            <td>C肽0分钟</td>
            <td>
              {{ personInfo.basic_lab["875011"].value }}
            </td>
            <td>
              {{
                personInfo.newes_lab["875011"]
                  ? personInfo.newes_lab["875011"].answer_name
                  : ""
              }}
            </td>
            <td>
              {{
                personInfo.newes_lab["875011"]
                  ? personInfo.newes_lab["875011"].reference
                  : ""
              }}
            </td>
            <td>
              {{
                personInfo.newes_lab["875011"]
                  ? personInfo.newes_lab["875011"].unit
                  : ""
              }}
            </td>
          </tr>
          <tr>
            <td>C肽120分钟</td>
            <td>
              {{ personInfo.basic_lab["875014"].value }}
            </td>
            <td>
              {{
                personInfo.newes_lab["875014"]
                  ? personInfo.newes_lab["875014"].answer_name
                  : ""
              }}
            </td>
            <td>
              {{
                personInfo.newes_lab["875014"]
                  ? personInfo.newes_lab["875014"].reference
                  : ""
              }}
            </td>
            <td>
              {{
                personInfo.newes_lab["875014"]
                  ? personInfo.newes_lab["875014"].unit
                  : ""
              }}
            </td>
          </tr>
        </table>
        <div class="testTime">
          {{ personInfo.newes_lab.tangnailiang_conclusion }}
        </div>
        <div
          class="chart"
          ref="bpInHospitalChart"
          v-show="bpInHospitalShow"
        ></div>
      </div>
    </div>
    <div class="tab">
      <p class="title">
        <span>
          <img src="@/views/mmcReport/img/bloodPressureInHome.png" alt="" />
          <span>家庭血糖近7日监测情况</span>
        </span>
        <span class="extend" @click="change('bpInHome')" v-if="display != 1">
          <span>{{ bpInHomeExtend ? "收起" : "展开" }}</span>
          <van-icon v-if="bpInHomeExtend" name="arrow-up" />
          <van-icon v-if="!bpInHomeExtend" name="arrow-down" />
        </span>
        <span v-else></span>
      </p>
      <div class="cellContent" v-if="bpInHomeExtend">
        <table class="table">
          <tr>
            <th rowspan="2">日期</th>
            <th rowspan="2">凌晨</th>
            <th colspan="2">早餐</th>
            <th colspan="2">午餐</th>
            <th colspan="2">晚餐</th>
            <th rowspan="2">睡前</th>
          </tr>
          <tr>
            <th>空腹</th>
            <th>后</th>
            <th>前</th>
            <th>后</th>
            <th>前</th>
            <th>后</th>
          </tr>
          <tr v-for="(item, index) in personInfo.familyGluData" :key="index">
            <td>{{ item.date }}</td>
            <td
              :class="
                item['8'] < 4.4 && item['8'] > 0
                  ? 'blue'
                  : '' + item['8'] > 6.1
                  ? 'red'
                  : ''
              "
            >
              {{ item["8"] == 0 ? "-" : item["8"] }}
            </td>
            <td
              :class="
                item['1'] < 4.4 && item['1'] > 0
                  ? 'blue'
                  : '' + item['1'] > 6.1
                  ? 'red'
                  : ''
              "
            >
              {{ item["1"] == 0 ? "-" : item["1"] }}
            </td>
            <td
              :class="
                item['2'] < 4.0 && item['2'] > 0
                  ? 'blue'
                  : '' + item['2'] > 7.8
                  ? 'red'
                  : ''
              "
            >
              {{ item["2"] == 0 ? "-" : item["2"] }}
            </td>
            <td
              :class="
                item['3'] < 4.4 && item['3'] > 0
                  ? 'blue'
                  : '' + item['3'] > 6.1
                  ? 'red'
                  : ''
              "
            >
              {{ item["3"] == 0 ? "-" : item["3"] }}
            </td>
            <td
              :class="
                item['4'] < 4.0 && item['4'] > 0
                  ? 'blue'
                  : '' + item['4'] > 7.8
                  ? 'red'
                  : ''
              "
            >
              {{ item["4"] == 0 ? "-" : item["4"] }}
            </td>
            <td
              :class="
                item['5'] < 4.4 && item['5'] > 0
                  ? 'blue'
                  : '' + item['5'] > 6.1
                  ? 'red'
                  : ''
              "
            >
              {{ item["5"] == 0 ? "-" : item["5"] }}
            </td>
            <td
              :class="
                item['6'] < 4.0 && item['6'] > 0
                  ? 'blue'
                  : '' + item['6'] > 7.8
                  ? 'red'
                  : ''
              "
            >
              {{ item["6"] == 0 ? "-" : item["6"] }}
            </td>
            <td
              :class="
                item['7'] < 4.4 && item['7'] > 0
                  ? 'blue'
                  : '' + item['7'] > 6.1
                  ? 'red'
                  : ''
              "
            >
              {{ item["7"] == 0 ? "-" : item["7"] }}
            </td>
          </tr>
        </table>
      </div>
    </div>
    <div class="tab">
      <p class="title">
        <span>
          <img src="@/views/mmcReport/img/BMI.png" alt="" />
          <span>BMI</span>
        </span>
        <span
          class="extend"
          @click="change('BMI')"
          v-if="BMITested && display != 1"
        >
          <span>{{ BMIExtend ? "收起" : "展开" }}</span>
          <van-icon v-if="BMIExtend" name="arrow-up" />
          <van-icon v-if="!BMIExtend" name="arrow-down" />
        </span>
        <span v-else-if="BMITested && display == 1"></span>
        <span class="noTest" v-else>未检查</span>
      </p>
      <div class="cellContent" v-if="BMIExtend && BMITested">
        <div class="chart" ref="BMIChart"></div>
      </div>
    </div>
    <div class="tab">
      <p class="title">
        <span>
          <img src="@/views/mmcReport/img/ACR.png" alt="" />
          <span>尿白蛋白比肌酐（ACR）</span>
        </span>
        <span
          class="extend"
          @click="change('ACR')"
          v-if="ACRTested && display != 1"
        >
          <span>{{ ACRExtend ? "收起" : "展开" }}</span>
          <van-icon v-if="ACRExtend" name="arrow-up" />
          <van-icon v-if="!ACRExtend" name="arrow-down" />
        </span>
        <span v-else-if="ACRTested && display == 1"></span>
        <span class="noTest" v-else>未检查</span>
      </p>
      <div class="cellContent" v-if="ACRExtend && ACRTested">
        <div class="chart" ref="ACRChart"></div>
      </div>
    </div>
    <div class="tab">
      <p class="title">
        <span>
          <img src="@/views/mmcReport/img/bloodFat.png" alt="" />
          <span>血脂</span>
        </span>
        <span
          class="extend"
          @click="change('bloodFat')"
          v-if="bloodFatTested && display != 1"
        >
          <span>{{ bloodFatExtend ? "收起" : "展开" }}</span>
          <van-icon v-if="bloodFatExtend" name="arrow-up" />
          <van-icon v-if="!bloodFatExtend" name="arrow-down" />
        </span>
        <span v-else-if="bloodFatTested && display == 1"></span>
        <span class="noTest" v-else>未检查</span>
      </p>
      <div class="cellContent" v-if="bloodFatExtend && bloodFatTested">
        <div class="chart specialChart" ref="bloodFatChart"></div>
      </div>
    </div>
    <div class="tab">
      <p class="title">
        <span>
          <img src="@/views/mmcReport/img/arteriosclerosis.png" alt="" />
          <span>动脉硬化检测</span>
        </span>
        <span
          class="extend"
          @click="change('AS')"
          v-if="asTested && display != 1"
        >
          <span>{{ ASExtend ? "收起" : "展开" }}</span>
          <van-icon v-if="ASExtend" name="arrow-up" />
          <van-icon v-if="!ASExtend" name="arrow-down" />
        </span>
        <span v-else-if="asTested && display == 1"></span>
        <span class="noTest" v-else>未检查</span>
      </p>
      <div class="cellContent" v-if="ASExtend && asTested">
        <table class="table">
          <tr>
            <th>最近两次检查</th>
            <th>ABI<br />（左）</th>
            <th>ABI<br />（右）</th>
            <th>BaPWV<br />（左）</th>
            <th>BaPWV<br />（右）</th>
          </tr>
          <tr v-for="(item, index) in personInfo.pwv_abi" :key="index">
            <td>{{ item.visit_at }}<br />{{ item.hosp_name }}</td>
            <td>{{ item.labi }}</td>
            <td>{{ item.rabi }}</td>
            <td>{{ item.lbapwv }}</td>
            <td>{{ item.rbapwv }}</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="tab">
      <p class="title">
        <span>
          <img src="@/views/mmcReport/img/visceralFat.png" alt="" />
          <span>内脏脂肪检查</span>
        </span>
        <span
          class="extend"
          @click="change('visceralFat')"
          v-if="visFatTested && display != 1"
        >
          <span>{{ visceralFatExtend ? "收起" : "展开" }}</span>
          <van-icon v-if="visceralFatExtend" name="arrow-up" />
          <van-icon v-if="!visceralFatExtend" name="arrow-down" />
        </span>
        <span v-else-if="visFatTested && display == 1"></span>
        <span class="noTest" v-else>未检查</span>
      </p>
      <div class="cellContent" v-if="visceralFatExtend && visFatTested">
        <table class="table" style="margin-bottom: 0">
          <tr>
            <th>最近两次检查</th>
            <th>内脏脂肪面积<br />（c㎡）</th>
            <th>皮下脂肪面积<br />（c㎡）</th>
          </tr>
          <tr v-for="(item, index) in personInfo.sat_vat" :key="index">
            <td>{{ item.visit_at }}<br />{{ item.hosp_name }}</td>
            <td>{{ item.vat }}</td>
            <td>{{ item.sat }}</td>
          </tr>
        </table>
        <div class="context">
          *注：内脏脂肪型肥胖的判断标准规定是内脏脂肪面积≥100c㎡
        </div>
      </div>
    </div>
    <div class="tab">
      <p class="title">
        <span>
          <img src="@/views/mmcReport/img/diabetic.png" alt="" />
          <span>糖尿病肾病检查</span>
        </span>
        <span
          class="extend"
          @click="change('diabetic')"
          v-if="diabeticTested && display != 1"
        >
          <span>{{ diabeticExtend ? "收起" : "展开" }}</span>
          <van-icon v-if="diabeticExtend" name="arrow-up" />
          <van-icon v-if="!diabeticExtend" name="arrow-down" />
        </span>
        <span v-else-if="diabeticTested && display == 1"></span>
        <span class="noTest" v-else>未检查</span>
      </p>
      <div class="cellContent" v-if="diabeticExtend && diabeticTested">
        <table class="table">
          <tr>
            <th>最近两次检查</th>
            <th>肌酐</th>
            <!-- <th>CG eGFR<br />（ml/min）</th> -->
            <th>EPI eGFR<br />（ml/min）</th>
            <th>MDRD eGFR<br />（ml/min）</th>
          </tr>
          <tr v-for="(item, index) in personInfo.egfr_lab" :key="index">
            <td>{{ item.visit_at }}<br />{{ item.hosp_name }}</td>
            <td>{{ item.answer_name }}</td>
            <!-- <td>{{ item.cg_egfr }}</td> -->
            <td>{{ item.cg_egfr }}</td>
            <td>{{ item.mdrd_egfr }}</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="tab">
      <p class="title">
        <span>
          <img src="@/views/mmcReport/img/neckArteries.png" alt="" />
          <span>颈动脉超声</span>
        </span>
        <span
          class="extend"
          @click="change('neckArteries')"
          v-if="personInfo.newes_lab['925017'] && display != 1"
        >
          <span>{{ neckArteriesExtend ? "收起" : "展开" }}</span>
          <van-icon v-if="neckArteriesExtend" name="arrow-up" />
          <van-icon v-if="!neckArteriesExtend" name="arrow-down" />
        </span>
        <span v-else-if="personInfo.newes_lab['925017'] && display == 1"></span>
        <span class="noTest" v-else>未检查</span>
      </p>
      <div
        class="cellContent"
        v-if="neckArteriesExtend && personInfo.newes_lab['925017']"
      >
        <van-cell
          title="小结"
          :value="
            personInfo.newes_lab['925017']
              ? personInfo.newes_lab['925017'].answer_name
              : ''
          "
        />
      </div>
    </div>
    <div class="tab">
      <p class="title">
        <span>
          <img src="@/views/mmcReport/img/diabeticNerve.png" alt="" />
          <span>糖尿病周围神经病变检测</span>
        </span>
        <span
          class="extend"
          @click="change('diabeticNerve')"
          v-if="personInfo.newes_lab['904033'] && display != 1"
        >
          <span>{{ diabeticNerveExtend ? "收起" : "展开" }}</span>
          <van-icon v-if="diabeticNerveExtend" name="arrow-up" />
          <van-icon v-if="!diabeticNerveExtend" name="arrow-down" />
        </span>
        <span v-else-if="personInfo.newes_lab['904033'] && display == 1"></span>
        <span class="noTest" v-else>未检查</span>
      </p>
      <div
        class="cellContent"
        v-if="diabeticNerveExtend && personInfo.newes_lab['904033']"
      >
        <van-cell
          title="小结"
          :value="
            personInfo.newes_lab['904033']
              ? personInfo.newes_lab['904033'].answer_name
              : ''
          "
        />
      </div>
    </div>
    <div class="tab">
      <p class="title">
        <span>
          <img src="@/views/mmcReport/img/liverFunction.png" alt="" />
          <span>肝功能、肾功能、甲状腺</span>
        </span>
        <span
          class="extend"
          @click="change('liverFunction')"
          v-if="liverFuncTested && display != 1"
        >
          <span>{{ liverFunctionExtend ? "收起" : "展开" }}</span>
          <van-icon v-if="liverFunctionExtend" name="arrow-up" />
          <van-icon v-if="!liverFunctionExtend" name="arrow-down" />
        </span>
        <span v-else-if="liverFuncTested && display == 1"></span>
        <span class="noTest" v-else>未检查</span>
      </p>
      <div class="cellContent" v-if="liverFunctionExtend && liverFuncTested">
        <div class="special">
          <div>丙氨酸氨基转移酶</div>
          <div v-if="personInfo.newes_lab['878030']">
            <span class="number">{{
              personInfo.newes_lab["878030"].answer_name +
              personInfo.newes_lab["878030"].unit
            }}</span>
            <span
              class="range"
              v-if="personInfo.newes_lab['878030'].reference"
              >{{ " (" + personInfo.newes_lab["878030"].reference + ")" }}</span
            >
          </div>
        </div>
        <div class="special">
          <div>天门冬氨酸氨基转移酶</div>
          <div v-if="personInfo.newes_lab['878031']">
            <span class="number">{{
              personInfo.newes_lab["878031"].answer_name +
              personInfo.newes_lab["878031"].unit
            }}</span>
            <span
              class="range"
              v-if="personInfo.newes_lab['878031'].reference"
              >{{ " (" + personInfo.newes_lab["878031"].reference + ")" }}</span
            >
          </div>
        </div>
        <div class="special">
          <div>r-谷氨酰转移酶</div>
          <div v-if="personInfo.newes_lab['878033']">
            <span class="number">{{
              personInfo.newes_lab["878033"].answer_name +
              personInfo.newes_lab["878033"].unit
            }}</span>
            <span
              class="range"
              v-if="personInfo.newes_lab['878033'].reference"
              >{{ " (" + personInfo.newes_lab["878033"].reference + ")" }}</span
            >
          </div>
        </div>
        <div class="special">
          <div>总胆红素</div>
          <div v-if="personInfo.newes_lab['878034']">
            <span class="number">{{
              personInfo.newes_lab["878034"].answer_name +
              personInfo.newes_lab["878034"].unit
            }}</span>
            <span
              class="range"
              v-if="personInfo.newes_lab['878034'].reference"
              >{{ " (" + personInfo.newes_lab["878034"].reference + ")" }}</span
            >
          </div>
        </div>
        <div class="special">
          <div>直接胆红素</div>
          <div v-if="personInfo.newes_lab['878035']">
            <span class="number">{{
              personInfo.newes_lab["878035"].answer_name +
              personInfo.newes_lab["878035"].unit
            }}</span>
            <span
              class="range"
              v-if="personInfo.newes_lab['878035'].reference"
              >{{ " (" + personInfo.newes_lab["878035"].reference + ")" }}</span
            >
          </div>
        </div>
        <div class="testTime padding">
          {{ personInfo.newes_lab.gangongneng_conclusion }}
        </div>
        <div class="special">
          <div>尿素</div>
          <div v-if="personInfo.newes_lab['878036']">
            <span class="number">{{
              personInfo.newes_lab["878036"].answer_name +
              personInfo.newes_lab["878036"].unit
            }}</span>
            <span
              class="range"
              v-if="personInfo.newes_lab['878036'].reference"
              >{{ " (" + personInfo.newes_lab["878036"].reference + ")" }}</span
            >
          </div>
        </div>
        <div class="special">
          <div>肌酐</div>
          <div v-if="personInfo.newes_lab['878037']">
            <span class="number">{{
              personInfo.newes_lab["878037"].answer_name +
              personInfo.newes_lab["878037"].unit
            }}</span>
            <span
              class="range"
              v-if="personInfo.newes_lab['878037'].reference"
              >{{ " (" + personInfo.newes_lab["878037"].reference + ")" }}</span
            >
          </div>
        </div>
        <div class="special">
          <div>尿酸</div>
          <div v-if="personInfo.newes_lab['878038']">
            <span class="number">{{
              personInfo.newes_lab["878038"].answer_name +
              personInfo.newes_lab["878038"].unit
            }}</span>
            <span
              class="range"
              v-if="personInfo.newes_lab['878038'].reference"
              >{{ " (" + personInfo.newes_lab["878038"].reference + ")" }}</span
            >
          </div>
        </div>
        <div class="testTime padding">
          {{ personInfo.newes_lab.shengongneng_conclusion }}
        </div>
        <div class="special">
          <div>促甲状腺素（TSH）</div>
          <div v-if="personInfo.newes_lab['883005']">
            <span class="number">{{
              personInfo.newes_lab["883005"].answer_name +
              personInfo.newes_lab["883005"].unit
            }}</span>
            <span
              class="range"
              v-if="personInfo.newes_lab['883005'].reference"
              >{{ " (" + personInfo.newes_lab["883005"].reference + ")" }}</span
            >
          </div>
        </div>
        <div class="special">
          <div>游离三碘甲状腺原氨酸(FT3)</div>
          <div v-if="personInfo.newes_lab['883003']">
            <span class="number">{{
              personInfo.newes_lab["883003"].answer_name +
              personInfo.newes_lab["883003"].unit
            }}</span>
            <span
              class="range"
              v-if="personInfo.newes_lab['883003'].reference"
              >{{ " (" + personInfo.newes_lab["883003"].reference + ")" }}</span
            >
          </div>
        </div>
        <div class="special">
          <div>游离甲状腺素（FT4）</div>
          <div v-if="personInfo.newes_lab['883004']">
            <span class="number">{{
              personInfo.newes_lab["883004"].answer_name +
              personInfo.newes_lab["883004"].unit
            }}</span>
            <span
              class="range"
              v-if="personInfo.newes_lab['883004'].reference"
              >{{ " (" + personInfo.newes_lab["883004"].reference + ")" }}</span
            >
          </div>
        </div>
        <div class="testTime padding">
          {{ personInfo.newes_lab.qtxt_tsh_ft3_ft4_conclusion }}
        </div>
      </div>
    </div>
    <div class="tab">
      <p class="title">
        <span>
          <img src="@/views/mmcReport/img/retina.png" alt="" />
          <span>糖尿病视网膜疾病筛查</span>
        </span>
        <span
          class="extend"
          @click="change('retina')"
          v-if="retinaTested && display != 1"
        >
          <span>{{ retinaExtend ? "收起" : "展开" }}</span>
          <van-icon v-if="retinaExtend" name="arrow-up" />
          <van-icon v-if="!retinaExtend" name="arrow-down" />
        </span>
        <span v-else-if="retinaTested && display == 1"></span>
        <span class="noTest" v-else>未检查</span>
      </p>
      <div class="cellContent yandi" v-if="retinaExtend && retinaTested">
        <van-cell
          title="左眼"
          :value="setYandiAnswerName('945')"
        />
        <van-cell
          title="右眼"
          :value="setYandiAnswerName('946')"
        />
        <div class="testTime padding">
          {{ personInfo.newes_lab.yandi_conclusion }}
        </div>
      </div>
    </div>
    <div class="tab">
      <p class="title">
        <span>
          <img src="@/views/mmcReport/img/electrocardiogram.png" alt="" />
          <span>心超、心电图</span>
        </span>
        <span
          class="extend"
          @click="change('ED')"
          v-if="edTested && display != 1"
        >
          <span>{{ EDExtend ? "收起" : "展开" }}</span>
          <van-icon v-if="EDExtend" name="arrow-up" />
          <van-icon v-if="!EDExtend" name="arrow-down" />
        </span>
        <span v-else-if="edTested && display == 1"></span>
        <span class="noTest" v-else>未检查</span>
      </p>
      <div class="cellContent" v-if="EDExtend && edTested">
        <div class="special">
          <div>左房内径</div>
          <div>
            <span class="number" v-if="personInfo.newes_lab['930001']">{{
              personInfo.newes_lab["930001"].answer_name +
              personInfo.newes_lab["930001"].unit
            }}</span>
          </div>
        </div>
        <div class="special">
          <div>左室舒张末期内径</div>
          <div>
            <span class="number" v-if="personInfo.newes_lab['930003']">{{
              personInfo.newes_lab["930003"].answer_name +
              personInfo.newes_lab["930003"].unit
            }}</span>
          </div>
        </div>
        <div class="special">
          <div>左室后壁厚度</div>
          <div>
            <span class="number" v-if="personInfo.newes_lab['930004']">{{
              personInfo.newes_lab["930004"].answer_name +
              personInfo.newes_lab["930004"].unit
            }}</span>
          </div>
        </div>
        <div class="special">
          <div>左室收缩末期内径</div>
          <div>
            <span class="number" v-if="personInfo.newes_lab['930006']">{{
              personInfo.newes_lab["930006"].answer_name +
              personInfo.newes_lab["930006"].unit
            }}</span>
          </div>
        </div>
        <div class="special">
          <div>LVEF</div>
          <div>
            <span class="number" v-if="personInfo.newes_lab['930005']">{{
              personInfo.newes_lab["930005"].answer_name +
              personInfo.newes_lab["930005"].unit
            }}</span>
          </div>
        </div>
        <div class="special">
          <div>主动脉根部内径</div>
          <div>
            <span class="number" v-if="personInfo.newes_lab['930007']">{{
              personInfo.newes_lab["930007"].answer_name +
              personInfo.newes_lab["930007"].unit
            }}</span>
          </div>
        </div>
        <div class="special">
          <div>小结</div>
          <div>
            <span class="number" v-if="personInfo.newes_lab['930008']">{{
              personInfo.newes_lab["930008"].answer_name
            }}</span>
          </div>
        </div>
        <div class="testTime padding" v-if="personInfo.newes_lab['930008']">
          {{
            personInfo.newes_lab["930008"].visit_at +
            "于" +
            personInfo.newes_lab["930008"].hosp_name +
            "检查"
          }}
        </div>
        <div v-else class="testTime padding"></div>
        <div class="special">
          <div>心律</div>
          <div>
            <span class="number" v-if="personInfo.newes_lab['952']">{{
              personInfo.newes_lab["952"]
            }}</span>
          </div>
        </div>
        <div class="special">
          <div>心率</div>
          <div>
            <span class="number" v-if="personInfo.newes_lab['952001']"
              >{{ personInfo.newes_lab["952001"].answer_name }} bpm</span
            >
          </div>
        </div>
        <!-- <div class="testTime padding">2021-01-26于系统测试医院检查</div> -->
        <div class="testTime padding" v-if="personInfo.newes_lab['954']">
          {{
            personInfo.newes_lab["954"].visit_at +
            "于" +
            personInfo.newes_lab["954"].hosp_name +
            "检查"
          }}
        </div>
        <!-- <div v-else class="testTime padding"></div> -->
      </div>
    </div>
    <div class="qrcode">
      <div class="qrcodebackground">
        <img src="@/views/mmcReport/img/qrCode.png" alt="" />
        <div>
          <p>扫码关注MMC管家公众号</p>
          <p>查看报告解读、系统学习权威患教知识</p>
        </div>
      </div>
    </div>
    <!-- <div class="share">
      <div @click="shareShow">
        <img src="@/views/mmcReport/img/wechat.png" alt="" />
        <p>分享好友</p>
      </div>
      <div>
        <img src="@/views/mmcReport/img/telphone.png" alt="" />
        <p>免费咨询</p>
      </div>
      <img class="question" src="@/views/mmcReport/img/question.png" alt="" />
    </div>
    <van-popup class="leadShare" v-model="shareShowed">
      <img src="@/views/mmcReport/img/share.png" alt="" />
    </van-popup> -->
    <!-- <van-overlay class="leadShare" :show="shareShowed" :lock-scroll="false">
      <img src="@/views/mmcReport/img/share.png" alt="" />
    </van-overlay> -->
  </div>
</template>
<script>
import echarts from "echarts";
import { reportProduce } from "@/api/saasReport";
export default {
  data() {
    return {
      show: true,
      // shareShowed: false,
      baseInfoExtend: true,
      bpInHospitalExtend: true,
      bpInHomeExtend: true,
      BMIExtend: true,
      ACRExtend: true,
      bloodFatExtend: true,
      ASExtend: true,
      visceralFatExtend: true,
      diabeticExtend: true,
      neckArteriesExtend: true,
      diabeticNerveExtend: true,
      liverFunctionExtend: true,
      retinaExtend: true,
      EDExtend: true,
      personInfo: {
        user_info: {},
        sign_info: {},
        diabetes_info: {
          diabetes: {},
        },
        print_date: "",
        historyIllness: [],
        historyIllnessName: "",
        basic_lab: {
          875001: {},
          875004: {},
          875006: {},
          875009: {},
          875011: {},
          875014: {},
        },
        newes_lab: {
          875001: {},
          875004: {},
          875006: {},
          875009: {},
          875011: {},
          875014: {},
          yandi_list: [],
        },
        familyGluData: [],
        pwv_abi: [],
        sat_vat: [],
        egfr_lab: [],
        hba: {},
        acr: {},
      },
      display: this.$route.query.display || 0,
      bpInHospitalShow: false,
      BMITested: false,
      ACRTested: false,
      bloodFatTested: false,
    };
  },
  computed: {
    // baseInfo() {
    //   return;
    // },
    sexShow() {
      if (this.personInfo.user_info.sex == 0) {
        return "男";
      } else if (this.personInfo.user_info.sex == 1) {
        return "女";
      } else {
        return "";
      }
    },
    asTested() {
      return this.personInfo.pwv_abi.length > 0;
    },
    visFatTested() {
      return this.personInfo.sat_vat.length > 0;
    },
    diabeticTested() {
      return this.personInfo.egfr_lab.length > 0;
    },
    // 颈动脉超声和糖尿病周围神经病变检测字段太少，判断条件省略
    liverFuncTested() {
      return (
        this.personInfo.newes_lab["878030"] ||
        this.personInfo.newes_lab["878031"] ||
        this.personInfo.newes_lab["878033"] ||
        this.personInfo.newes_lab["878034"] ||
        this.personInfo.newes_lab["878035"] ||
        this.personInfo.newes_lab["878036"] ||
        this.personInfo.newes_lab["878037"] ||
        this.personInfo.newes_lab["878038"] ||
        this.personInfo.newes_lab["883005"] ||
        this.personInfo.newes_lab["883003"] ||
        this.personInfo.newes_lab["883004"]
      );
    },
    retinaTested() {
      let obj = this.personInfo.newes_lab.yandi_list[0]
      const list = ['945', '946']
      let status = false
      for (let i = 0; i < list.length; i++) {
        const key = list[i]
        if (obj && obj[key] && obj[key]['answer_name']) {
          status = true
          break
        }
      }
      return status
      // return (
      //   this.personInfo.newes_lab["945"] || this.personInfo.newes_lab["946"]
      // );
    },
    edTested() {
      return (
        this.personInfo.newes_lab["930001"] ||
        this.personInfo.newes_lab["930003"] ||
        this.personInfo.newes_lab["930004"] ||
        this.personInfo.newes_lab["930006"] ||
        this.personInfo.newes_lab["930005"] ||
        this.personInfo.newes_lab["930007"] ||
        this.personInfo.newes_lab["930008"] ||
        this.personInfo.newes_lab["952"] ||
        this.personInfo.newes_lab["952001"]
      );
    },
  },
  mounted() {
    this.getReportProduce();
  },
  methods: {
    getReportProduce() {
      reportProduce({
        template_id: this.$route.query.template_id,
        sign: this.$route.query.sign,
        // template_id: 2,
        // sign: "e7ce34a506bf82a9c854f51aac122208687113ac07580fc68460b767125c0e694746a97f868c5684c89b151271f689bd3c2de65dca5252188d97f398c4ceab4bdd9045d0cecce9744cf1505c28bc80f741947863f4bcac4ff28b67b962a6265b56123473e78a4a728c9017bca30d148d1e4e8b71a4e7cf6ad378fd92135c892e84cde4b36faf3eda2e670ef2b6bf74b0",
      })
        .then((res) => {
          if (res.code == 0) {
            this.personInfo = res.data;
            // 简要病史字符串拼接
            this.personInfo.historyIllness = [];
            if (res.data.diabetes_info.medicals.gaoxueya == 1) {
              this.personInfo.historyIllness.push("高血压");
            }
            if (res.data.diabetes_info.medicals.guanxinbing == 1) {
              this.personInfo.historyIllness.push("冠心病");
            }
            if (res.data.diabetes_info.medicals.miniaoxitong == 1) {
              this.personInfo.historyIllness.push("泌尿系统疾病");
            }
            if (res.data.diabetes_info.medicals.yixianjibing == 1) {
              this.personInfo.historyIllness.push("胰腺疾病");
            }
            if (res.data.diabetes_info.medicals.zhongliu == 1) {
              this.personInfo.historyIllness.push("肿瘤");
            }
            if (res.data.diabetes_info.medicals.ganzangbing == 1) {
              this.personInfo.historyIllness.push("肝脏病史");
            }
            this.personInfo.historyIllnessName =
              this.personInfo.historyIllness.join("、");
            // 家庭血糖近7日监测情况
            let familyGlus = res.data.family_bgbp;
            let familyGlusList = [];
            for (let key in familyGlus.data) {
              familyGlusList.push(familyGlus.data[key]);
            }
            familyGlusList.sort(function (a, b) {
              return b.date < a.date ? 1 : -1;
            });
            familyGlusList.forEach((v, k) => {
              v.date = v.date.slice(5, 10);
            });
            this.personInfo.familyGluData = familyGlusList;
            if (this.personInfo.hba.xAxis.data.length > 0) {
              this.bpInHospitalShow = true;
            }
            if (this.personInfo.bmi.xAxis.data.length > 0) {
              this.BMITested = true;
            }
            if (this.personInfo.acr.xAxis.data.length > 0) {
              this.ACRTested = true;
            }
            if (this.personInfo.bloodFat.xAxis.data.length > 0) {
              this.bloodFatTested = true;
            }
          }
        })
        .then(() => {
          if (this.bpInHospitalShow) {
            this.bpInHospitalChart();
          }
          if (this.BMITested) {
            this.BMIChart();
          }
          if (this.ACRTested) {
            this.ACRChart();
          }
          if (this.bloodFatTested) {
            this.bloodFatChart();
          }
        });
    },
    closeWarning() {
      this.show = false;
    },
    // shareShow() {
    //   this.shareShowed = true;
    // },
    change(e) {
      this[e + "Extend"] = !this[e + "Extend"];
      if (this[e + "Extend"]) {
        this.$nextTick(() => {
          this[e + "Chart"]();
        });
      }
    },
    bpInHospitalChart() {
      let option = {
        title: {
          text: "糖化血红蛋白水平变化趋势",
        },
        animation: false,
        tooltip: {
          trigger: "axis",
        },
        legend: {
          data: ["基线", "糖化血红蛋白水平变化趋势"],
          itemWidth: 10,
          itemHeight: 2,
          left: 10,
          top: 25,
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "2%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          axisLine: {
            show: false,
          },
          axisLabel: {
            rotate: 30,
          },
          axisTick: {
            lineStyle: {
              color: "#000000",
              opacity: 0.25,
            },
          },
          data: this.personInfo.hba.xAxis.data,
          // data: ['2021-09-26','2021-12-23']
        },
        yAxis: {
          type: "value",
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
        },
        series: [
          {
            name: "基线",
            type: "line",
            symbol: "triangle",
            symbolSize: 10,
            itemStyle: {
              color: "#5AD8A6",
            },
            data: this.personInfo.hba.series[0].data,
            // data:[['2021-09-26',23]]
          },
          {
            name: "糖化血红蛋白水平变化趋势",
            type: "line",
            // symbol: "none",
            itemStyle: {
              color: "#5B8FF9",
            },
            data: this.personInfo.hba.series[1].data,
            // data:[['2021-12-23',15],[]]
          },
        ],
      };
      let myEchart = echarts.init(this.$refs.bpInHospitalChart);
      myEchart.setOption(option);
    },
    BMIChart() {
      let option = {
        title: {
          text: "BMI",
        },
        animation: false,
        tooltip: {
          trigger: "axis",
        },
        legend: {
          data: ["基线", "BMI"],
          itemWidth: 10,
          itemHeight: 2,
          left: 10,
          top: 25,
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          axisLine: {
            show: false,
          },
          axisLabel: {
            rotate: 30,
          },
          axisTick: {
            lineStyle: {
              color: "#000000",
              opacity: 0.25,
            },
          },
          data: this.personInfo.bmi.xAxis.data,
        },
        yAxis: {
          type: "value",
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
        },
        series: [
          {
            name: "基线",
            type: "line",
            symbol: "triangle",
            symbolSize: 10,
            itemStyle: {
              color: "#5AD8A6",
            },
            data: this.personInfo.bmi.series[0].data,
          },
          {
            name: "BMI",
            type: "line",
            // symbol: "none",
            itemStyle: {
              color: "#5B8FF9",
            },
            data: this.personInfo.bmi.series[1].data,
          },
        ],
      };
      let myEchart = echarts.init(this.$refs.BMIChart);
      myEchart.setOption(option);
    },
    ACRChart() {
      let option = {
        title: {
          text: "尿白蛋白比肌酐（ACR）",
        },
        animation: false,
        tooltip: {
          trigger: "axis",
        },
        legend: {
          data: ["基线", "尿白蛋白比肌酐（ACR）"],
          itemWidth: 10,
          itemHeight: 2,
          left: 10,
          top: 25,
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          axisLine: {
            show: false,
          },
          axisLabel: {
            rotate: 30,
          },
          axisTick: {
            lineStyle: {
              color: "#000000",
              opacity: 0.25,
            },
          },
          data: this.personInfo.acr.xAxis.data,
        },
        yAxis: {
          type: "value",
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
        },
        series: [
          {
            name: "基线",
            type: "line",
            symbol: "triangle",
            symbolSize: 10,
            itemStyle: {
              color: "#5AD8A6",
            },
            data: this.personInfo.acr.series[0].data,
          },
          {
            name: "尿白蛋白比肌酐（ACR）",
            type: "line",
            // symbol: "none",
            itemStyle: {
              color: "#5B8FF9",
            },
            data: this.personInfo.acr.series[1].data,
          },
        ],
      };
      let myEchart = echarts.init(this.$refs.ACRChart);
      myEchart.setOption(option);
    },
    bloodFatChart() {
      let option = {
        title: {
          text: "血脂",
        },
        animation: false,
        tooltip: {
          trigger: "axis",
        },
        legend: {
          data: [
            "甘油三酯基线",
            "甘油三酯",
            "总胆固醇基线",
            "总胆固醇",
            "低密度脂蛋白胆固醇基线",
            "低密度脂蛋白胆固醇",
          ],
          itemWidth: 10,
          itemHeight: 2,
          left: 10,
          top: 25,
        },
        grid: {
          left: "3%",
          right: "4%",
          top: "30%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          axisLine: {
            show: false,
          },
          axisLabel: {
            rotate: 30,
          },
          axisTick: {
            lineStyle: {
              color: "#000000",
              opacity: 0.25,
            },
          },
          data: this.personInfo.bloodFat.xAxis.data,
        },
        yAxis: {
          type: "value",
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
        },
        series: [
          {
            name: "甘油三酯基线",
            type: "line",
            symbol: "triangle",
            symbolSize: 10,
            itemStyle: {
              color: "#5AD8A6",
            },
            data: this.personInfo.bloodFat.series[0].data,
          },
          {
            name: "甘油三酯",
            type: "line",
            // symbol: "none",
            itemStyle: {
              color: "#5acbd8",
            },
            data: this.personInfo.bloodFat.series[1].data,
          },
          {
            name: "总胆固醇基线",
            type: "line",
            symbol: "triangle",
            symbolSize: 10,
            itemStyle: {
              color: "#F6BD16",
            },
            data: this.personInfo.bloodFat.series[2].data,
          },
          {
            name: "总胆固醇",
            type: "line",
            // symbol: "none",
            itemStyle: {
              color: "#bff616",
            },
            data: this.personInfo.bloodFat.series[3].data,
          },
          {
            name: "低密度脂蛋白胆固醇基线",
            type: "line",
            symbol: "triangle",
            symbolSize: 10,
            itemStyle: {
              color: "#FE628D",
            },
            data: this.personInfo.bloodFat.series[4].data,
          },
          {
            name: "低密度脂蛋白胆固醇",
            type: "line",
            // symbol: "none",
            itemStyle: {
              color: "#fe8562",
            },
            data: this.personInfo.bloodFat.series[5].data,
          },
        ],
      };
      let myEchart = echarts.init(this.$refs.bloodFatChart);
      myEchart.setOption(option);
    },
    setYandiAnswerName(index) {
      let obj = this.personInfo.newes_lab.yandi_list[0]
      let text = ''
      if (obj && obj[index]) {
        const v = obj[index]
        text = v['answer_name']
        if (v.is_ai_record === 1) {
          text = `${text}【AI阅片】`
        }
      }
      return text
    },
  },
};
</script>
<style lang="scss" scoped>
.wrapper {
  text-align: left;
  .warning {
    padding: 10px 15px;
    background: #fffbf1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .warningText {
      font-size: 14px;
      font-weight: 400;
      color: #de8c17;
    }
    img {
      width: 20px;
      margin-left: 8px;
    }
  }
  .tab {
    // background: #ffffff;
    .title {
      height: 30px;
      font-size: 16px;
      display: flex;
      justify-content: space-between;
      border-bottom: 1px solid #eeeeee;
      padding: 17px 0;
      margin: 0 20px;
      span {
        display: flex;
        align-items: center;
        white-space: nowrap;
        img {
          height: 28px;
        }
        span {
          margin-left: 8px;
          font-weight: 500;
          color: #333333;
        }
      }
      .extend {
        font-size: 14px;
        font-weight: 400;
        color: #666666;
        span {
          color: #666666;
        }
      }
      .noTest {
        font-size: 15px;
        font-weight: 400;
        color: #a7a7a7;
        line-height: 21px;
      }
    }
    .cellContent {
      // &.yandi .van-cell__value {
      //   text-align: left;
      // }
      .van-cell {
        padding: 17px 20px;
        font-size: 16px;
        font-weight: 400;
        color: #333333;
        // align-items: center;
      }
      .van-cell::after {
        border-bottom: 1px solid #eeeeee;
        transform: none;
      }
      .van-cell__value {
        font-weight: 400;
        color: #666666;
      }
      .table {
        font-size: 12px;
        width: calc(100% - 40px);
        margin: 16px 20px;
        tr {
          // height: 32px;
          th {
            height: 40px;
            font-size: 12px;
            font-weight: 500;
            color: #333333;
            background: #f7f7f7;
            border: 1px solid #f1f1f1;
            vertical-align: middle;
            text-align: center;
          }
          td {
            height: 32px;
            line-height: 20px;
            border: 1px solid #f1f1f1;
            vertical-align: middle;
            text-align: center;
          }
        }
        .red {
          color: #eb6048;
        }
        .blue {
          color: #318dfe;
        }
      }
      .specialTable {
        font-size: 12px;
        width: calc(100% - 40px);
        margin: 16px 20px;
        tr {
          height: 28px;
          &:nth-child(even) {
            border-bottom: 1px solid #f1f1f1;
          }
          th {
            font-size: 12px;
            font-weight: 500;
            color: #333333;
            background: #f7f7f7;
            border: 1px solid #f1f1f1;
            vertical-align: middle;
            text-align: center;
          }
          td {
            line-height: 20px;
            border-left: 1px solid #f1f1f1;
            border-right: 1px solid #f1f1f1;
            vertical-align: middle;
            text-align: center;
          }
        }
      }
      .testTime {
        font-size: 14px;
        font-weight: 400;
        color: #666666;
        text-align: right;
        margin-right: 20px;
      }
      .padding {
        padding: 15px 0;
      }
      .chart {
        // width: 100%;
        // margin-top: 20px;
        height: 300px;
        width: calc(100% - 40px);
        margin: 16px 20px;
      }
      .specialChart {
        height: 400px;
      }
      .context {
        font-size: 12px;
        font-weight: 400;
        color: #9b9b9b;
        text-align: right;
        margin: 8px 25px 18px 0;
      }
      .special {
        margin: 0 20px;
        padding: 17px 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 16px;
        font-weight: 400;
        color: #333333;
        border-bottom: 1px solid #eeeeee;
        div {
          width: 50%;
          &:nth-child(2) {
            text-align: right;
            .number {
              font-size: 15px;
            }
            .range {
              font-size: 13px;
              color: #999999;
            }
          }
        }
      }
    }
  }
  .qrcode {
    padding: 10px 20px 31px 20px;
    background: #f9f9f9;
    .qrcodebackground {
      display: flex;
      padding: 17px 24px;
      border-radius: 6px;
      background: #ecf3ff;
      img {
        width: 86px;
      }
      div {
        padding-left: 15px;
        padding-top: 7px;
        p {
          &:nth-child(1) {
            font-size: 16px;
            font-weight: 500;
            color: #3682f5;
            line-height: 22px;
          }
          &:nth-child(2) {
            font-size: 14px;
            font-weight: 400;
            color: #333333;
            line-height: 20px;
            margin-top: 8px;
          }
        }
      }
    }
  }
  // .share {
  //   padding: 16px 86px;
  //   // box-shadow: 5px 0px 0px 0px rgba(0, 0, 0, 0.5);
  //   box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.5);
  //   display: flex;
  //   justify-content: space-between;
  //   position: relative;
  //   div {
  //     display: flex;
  //     flex-direction: column;
  //     align-items: center;
  //     img {
  //       width: 50px;
  //     }
  //     p {
  //       font-size: 16px;
  //       font-weight: 400;
  //       color: #333333;
  //       line-height: 22px;
  //       margin-top: 5px;
  //     }
  //   }
  //   .question {
  //     position: absolute;
  //     width: 106px;
  //     top: -21px;
  //     right: 64px;
  //   }
  // }
  // .leadShare {
  //   img {
  //     width: 100%;
  //   }
  // }
  // .van-popup {
  //   width: 100%;
  //   background: none;
  //   top: 30%;
  // }
}
</style>