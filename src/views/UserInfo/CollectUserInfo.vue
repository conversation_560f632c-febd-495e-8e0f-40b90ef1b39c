<template>
  <div>
    <div class="header">
      <img v-if="sourceType==7 && closeTipStatus" @click="closeTip" class="close_tip"
           src="./imgs/<EMAIL>"></img>
      <div class="header-top">
        <span class="go_back" v-if="sourceType==7" @click="goBack"> <van-icon name="arrow-left"/>回到健康助手</span>
      </div>
    </div>
    <div class="wrapper">

      <!-- 1.选择您病情 -->
      <isBg
        v-if="showPageId === 1"
        v-model="hasBg"
        title="您是否在医院确诊过糖尿病？"
        :nowPage="nowPage"
        :sumPage="sumPage"
        step="1"
        @pre-item-base="preItemBase"
        @next-item-state="nextItemState"
      />

      <isBg
        v-if="showPageId === 2"
        v-model="has_pre_bg"
        title="您是否在医院确诊为糖尿病前期？"
        :nowPage="nowPage"
        :sumPage="sumPage"
        step="2"
        @pre-item-base="preItemBase"
        @next-item-state="nextItemState"
      />

      <isBg
        v-if="showPageId === 3"
        v-model="hasBp"
        title="您是否在医院确诊过高血压？"
        :nowPage="nowPage"
        :sumPage="sumPage"
        step="3"
        @pre-item-base="preItemBase"
        @next-item-state="nextItemState"
      />
      <!-- 2.基本信息问卷 -->
      <base-info
        v-if="showPageId === 4"
        :nowPage="nowPage"
        :sumPage="sumPage"
        :heightProp="height"
        :weightProp="weight"
        step="4"
        @last-item-base="preItemBase"
        @next-item-base="nextItemBase"
      />
      <!-- 5.血糖信息收集 -->
      <sugar-info
        v-if="showPageId === 5"
        :nowPage="nowPage"
        :sumPage="sumPage"
        :kfxtProp="kfxt"
        :kfxtDateProp="kfxtDate"
        :chxtProp="chxt"
        :chxtDateProp="chxtDate"
        :thxhdbProp="thxhdb"
        :thxhdbDateProp="thxhdbDate"
        @last-item-sugar="preItemBase"
        @next-item-sugar="nextItemSugar"
      />
      <!--&lt;!&ndash; 6.血压信息收集 &ndash;&gt;-->
      <bp-info
        v-if="showPageId === 6"
        :nowPage="nowPage"
        :sumPage="sumPage"
        :sbpProp="sbp"
        :dbpProp="dbp"
        :pulseProp="pulse"
        :isLastPage="isBpFinish"
        @finish-bp="finishBp"
        @last-item-bp="preItemBase"
        @next-item-bp="nextItemBP"
      />
      <!-- 5.你是否有过以下病史 -->
      <ill-his
        v-if="showPageId === 7"
        :nowPage="nowPage"
        :sumPage="sumPage"
        :illHisProp="illHis"
        :isLastPage="isLastPage"
        @finish-his="finishHis"
        @last-item-his="preItemBase"
        @next-item-his="nextItemHis"
      />
      <!--&lt;!&ndash; 8.目前是否正在使用药物控糖 &ndash;&gt;-->
      <drug-control
        v-if="showPageId === 8"
        :nowPage="nowPage"
        :sumPage="sumPage"
        :drugIdProp="drugId"
        :doseIdProp="doseId"
        @last-item-drug="preItemBase"
        @finish-drug="finishDrug"
      />
      <!-- 9.选择您是否使用降糖药 -->
      <isUseDiabegone
        v-if="showPageId === 9"
        v-model="has_bg_drug"
        title="是否使用降糖药？"
        :nowPage="nowPage"
        :sumPage="sumPage"
        @pre-item-base="preItemBase"
        @next-item-state="nextItemStateFinish"
      />
      <!-- 10.选择您感兴趣的 -->
      <isHealth
        v-if="showPageId === 10"
        v-model="prefer_tag"
        title="您感兴趣的管理方向？"
        :nowPage="nowPage"
        :sumPage="sumPage"
        @pre-item-base="preItemBase"
        @next-item-state="nextItemStateFinish"
      />
    </div>
  </div>
</template>

<script>

import BaseInfo from './components/BaseInfo.vue'
import isBg from './components/question/isBg.vue'
import wx from 'weixin-js-sdk'
import isHealth from './components/question/isHealth'
import isUseDiabegone from './components/question/isUseDiabegone'
import SugarInfo from './components/SugarInfo.vue'
import bpInfo from './components/bpInfo.vue'
import IllHis from './components/IllHis.vue'
import DrugControl from './components/DrugControl.vue'
import {userBasicGet, userBasicSave, nationalLifeLogin, userBasicCheck, judgeController} from '@/api/UserInfo.js'
import {getBanFontSize} from '@/utils/utils.js'

export default {
  data: () => {
    return {
      uid: '', // 用户id
      closeTipStatus: false,
      sourceType: '', // 1-小程序 2-管家app 3-国寿 6控糖助手小程序
      showPageId: 0, // 1:选择您病情 2:基本信息问卷 3:血糖信息收集 4:血压信息收集 5:你是否有过以下病史 6:目前是否正在使用药物控糖
      showPageIdArr: [], // 显示页顺序数组
      isLastPage: false, // 是否是最后一页
      nowPage: 0, // 当前页码
      sumPage: '', // 总共页码
      illType: '', // 病情 1-单纯糖尿病 2-糖尿病+高血压 3-糖尿病+肥胖 4-糖尿病+肥胖+高血压 5-高血压
      height: '', // 身高
      weight: '', // 体重
      illHis: '', // 病史 0-无 1-严重低血糖 2-心梗史 3-冠心病史 4-脑卒中 5-肾病 6-记忆力明显减退或者认知功能障碍 7-眼底增值性病变
      kfxt: '', // 最近一次空腹血糖 值
      kfxtDate: '', // 最近一次空腹血糖 测量时间
      chxt: '', // 您最近的餐后血糖 值
      chxtDate: '', // 您最近的餐后血糖 测量时间
      thxhdb: '', // 糖化血红蛋白 值
      thxhdbDate: '', // 糖化血红蛋白 测量时间
      sbp: '', // 收缩压
      dbp: '', // 舒张压
      pulse: '', // 心率
      drugId: '', // 是否使用胰岛素 1-是 0-否
      doseId: '', // 胰岛素注射频率 1-每天小于等于2次 2-每天大于等于3次
      hasBg: '', // 是否有糖尿病 1-有 0-否
      hasBp: '', // 是否有高血压 1-有 0-否
      hasWeight: '',// 是否超重/肥胖 1-是 0-否
      mmc_has_bp: "",// 新增）MMC是否有高血压1有，0没有。
      mmc_has_bg: "",// （新增）MMC是否有糖尿病1有，0没有。
      has_pre_bg: "", // 新增）是否有糖前期
      has_pre_bg_ext: "", // 备注：（新增）糖前期文案
      prefer_tag: "", //如果为健康
      has_bg_drug: ""
    }
  },
  components: {
    'isBg': isBg,
    'isUseDiabegone': isUseDiabegone,
    'isHealth': isHealth,
    'base-info': BaseInfo,
    'sugar-info': SugarInfo,
    'bp-info': bpInfo,
    'ill-his': IllHis,
    'drug-control': DrugControl
  },
  created() {
    // 判断是否安卓系统微信页面修改了字体
    getBanFontSize()
    let routeQuery = this.$route.query
    this.uid = routeQuery.uid || ''
    this.sourceType = routeQuery.source_type || ''
    this.from_type = routeQuery.from_type || ''
    // 初始化
    this.init()
  },
  computed: {
    isLastPage() {
      return this.nowPage + 1 === this.showPageIdArr.length
    },
    isBpFinish() {
      return this.hasBg == 0 && this.hasBp == 1 && this.has_pre_bg == 0
    }
  },
  watch: {
    hasBg(val, old_val) {
      if (val == 1) {
        this.has_pre_bg = 0
      }
    }
  },
  methods: {
    goBack() {
      this.closeTipStatus = true
    },
    closeTip() {
      this.closeTipStatus = false
    },
    /**
     * 初始化
     */
    async init() {
      let that = this
      console.log(this.mmc_has_bg, 12341234)
      // 国寿版本 3，  4 爱选
      if (this.sourceType === '3' || this.sourceType === '4') {
        // 设置appInfo
        if (this.sourceType === '3') localStorage.setItem('appInfo', '{"zz_app_id": 150001, "zz_app_name": "national_guanjia", "zz_app_version": "2.9.0"}')
        if (this.sourceType === '4') localStorage.setItem('appInfo', '{"zz_app_id": 190001, "zz_app_name": "aixuan_guanjia", "zz_app_version": "2.9.9"}')

        if (this.uid != '') {
          // 用户uid换token
          let loginRes = await nationalLifeLogin(this.uid)
          if (loginRes.status === 0) {
            localStorage.setItem('authorization', loginRes.data.auth.access_token)
          } else {
            this.$toast(loginRes.msg)
            return
          }
        }

        // 用户是否收集过信息接口
        let checkRes = await userBasicCheck()
        if (checkRes.status === 0) {
          // 0-未收集 1-已收集未开始 2-已收集已开始 3，重新收集
          let flag = checkRes.data.flag
          that.getUserBasic(flag)
          // 已收集未开始-跳转目标结果页 已收集已开始-跳转H5首页
          if (flag === 1) {
            this.$router.replace({
              path: '/user/info/goal',
              query: {
                source_type: this.sourceType
              }
            })
          } else if (flag === 2) {
            if (this.from_type != 1) {
              this.$router.replace({
                path: '/user/info/home',
                query: {
                  source_type: this.sourceType
                }
              })
            }

          } else if (flag === 3) {

          }
        } else {
          this.$toast(checkRes.msg)
          return
        }

        // 获取收集的用户信息
      } else if (this.sourceType == 6 || this.sourceType == 7) {
        judgeController().then(res => {
          if (res.status === 0) {
            if (res.data.is_write_basics == 0) {
              that.getUserBasic(0)
            } else {
              that.getUserBasic(1)
            }
          } else {
            this.$toast(res.msg)
          }
        })
      } else {
        // 用户是否收集过信息接口
        let checkRes = await userBasicCheck()
        if (checkRes.status === 0) {
          // 0-未收集 1-已收集未开始 2-已收集已开始 3，重新收集
          let flag = checkRes.data.flag
          that.getUserBasic(flag)
          // 已收集未开始-跳转目标结果页 已收集已开始-跳转H5首页
          if (flag === 1) {
            this.$router.replace({
              path: '/user/info/goal',
              query: {
                source_type: this.sourceType
              }
            })
          } else if (flag === 2) {
            if (this.from_type != 1) {
              this.$router.replace({
                path: '/user/info/home',
                query: {
                  source_type: this.sourceType
                }
              })
            }
          } else if (flag === 3) {

          }
        }
      }
    },
    /**
     * 获取收集的用户信息
     */
    getUserBasic(flg) {
      userBasicGet().then(res => {
        if (res.status === 0) {
          let oData = res.data
          this.illType = oData.disease_tag // 病情 1-单纯糖尿病 2-糖尿病+高血压 3-糖尿病+肥胖 4-糖尿病+肥胖+高血压 5-高血压
          this.height = oData.height // 身高
          this.weight = oData.weight // 体重
          this.illHis = oData.disease_history ? oData.disease_history.join(',') : '' // 病史 0-无 1-严重低血糖 2-心梗史 3-冠心病史 4-脑卒中 5-肾病 6-记忆力明显减退或者认知功能障碍 7-眼底增值性病变
          this.kfxt = oData.fast_bg // 最近一次空腹血糖 值
          this.kfxtDate = oData.fast_bg_time // 最近一次空腹血糖 测量时间
          this.chxt = oData.postprandial_bg // 您最近的餐后血糖 值
          this.chxtDate = oData.postprandial_bg_time // 您最近的餐后血糖 测量时间
          this.thxhdb = oData.hba1c // 糖化血红蛋白 值
          this.thxhdbDate = oData.hba1c_time // 糖化血红蛋白 测量时间
          this.sbp = oData.sbp // 收缩压
          this.dbp = oData.dbp // 舒张压
          this.pulse = oData.pulse // 心率
          this.drugId = flg === 0 ? '' : oData.has_drug // 是否使用胰岛素 1-是 0-否
          this.doseId = oData.drug_ratio // 胰岛素注射频率 1-每天小于等于2次 2-每天大于等于3次
          this.hasBg = flg === 0 ? '' : oData.has_bg // 是否有糖尿病 1-有 0-否
          this.hasBp = flg === 0 ? '' : oData.has_bp // 是否有高血压 1-有 0-否
          this.hasWeight = oData.has_weight // 是否超重/肥胖 1-是 0-否
          this.mmc_has_bp = oData.mmc_has_bp // 新增）MMC是否有高血压1有，0没有。
          this.mmc_has_bg = oData.mmc_has_bg // （新增）MMC是否有糖尿病1有，0没有。
          this.has_pre_bg = flg === 0 ? '' : oData.has_pre_bg // 新增）是否有糖前期
          this.prefer_tag = flg === 0 ? '' : oData.prefer_tag
          this.has_pre_bg_ext = oData.has_pre_bg_ext // 备注：（新增）糖前期文案
          this.has_bg_drug = flg === 0 ? '' : oData.has_bg_drug
          console.log(this.mmc_has_bg, this.mmc_has_bg)
          this.showPageId = 1
          if (this.mmc_has_bg == 1) {
            this.hasBg = this.mmc_has_bg
            if (this.mmc_has_bp == 1) {
              this.hasBp = this.mmc_has_bp
              this.showPageId = 4
              this.showPageIdArr.push(4)
            } else {
              this.showPageId = 3
              this.showPageIdArr.push(3)
            }
          } else {
            this.showPageIdArr.push(1)
          }

        } else {
          this.$toast(res.msg)
        }
      })
    },
    /**
     * 下一项
     * @param {Number} illType 病情类型（1-单纯糖尿病 2-糖尿病+高血压 3-糖尿病+肥胖 4-糖尿病+肥胖+高血压 5-高血压）
     */
    nextItemState(step) {
      console.log(this.showPageIdArr)
      this.showPageIdArr = this.showPageIdArr.slice(0, this.nowPage + 1)

      if (step == 1) {
        if (this.hasBg == 0) {
          this.showPageIdArr.push(2)
        } else {
          if (this.mmc_has_bp == 1) {
            this.hasBp = this.mmc_has_bp
            this.showPageIdArr.push(4)
          } else {
            this.showPageIdArr.push(3)
          }
        }
      } else if (step == 2) {
        if (this.mmc_has_bp == 1) {
          this.hasBp = this.mmc_has_bp
          this.showPageIdArr.push(4)
        } else {
          this.showPageIdArr.push(3)
        }
      } else if (step == 3) {
        this.showPageIdArr.push(4)
      } else if (step == 4) {
        this.showPageIdArr.push(5)
      }

      // 下一个显示页Id
      this.nextShowPageId()
    },
    nextItemStateFinish() {
      // 保存数据
      this.submit()
    },
    preItemBase() {
      // 上一个显示页Id
      this.lastShowPageId()
    },
    /**
     * 药物控糖完成收集
     * @param {Object} drugData 药物控糖
     */
    finishDrug(drugData) {
      for (const key in drugData) {
        this[key] = drugData[key]
      }
      // 保存数据
      this.submit()
    },
    /**
     * 基本信息问卷上一项
     * @param {String} height 身高
     * @param {String} weight 体重
     */
    lastItemBase(height, weight) {
      this.height = height
      this.weight = weight

      // 上一个显示页Id
      this.lastShowPageId()
    },
    /**
     * 基本信息问卷下一项
     * @param {String} height 身高
     * @param {String} weight 体重
     */
    nextItemBase(height, weight) {
      this.showPageIdArr = this.showPageIdArr.slice(0, this.nowPage + 1)
      this.height = height
      this.weight = weight
      let bmi = this.weight / (this.height / 100 * this.height / 100)
      console.log(bmi, 1234123)
      if (this.hasBg == 1 || this.hasBp == 1 || bmi > 24 || this.has_pre_bg == 1) {
        if (this.hasBp == 1) {
          //6.血压信息收集
          this.showPageIdArr.push(6)
        }
        if (this.hasBg == 1 || this.has_pre_bg == 1) {
          //5.血糖信息收集
          this.showPageIdArr.push(5)

          if (this.hasBg == 1) {
            //8.目前是否正在使用药物控糖
            this.showPageIdArr.push(8)
          }

          if (this.has_pre_bg == 1) {
            // 9 是否使用降糖
            this.showPageIdArr.push(9)
          }
        }

        if (this.hasBg == 0 && this.hasBp == 0 && this.has_pre_bg == 0) {
          this.submit()
          return
        }
      } else {
        // 10.选择您感兴趣的
        this.showPageIdArr.push(10)
      }

      // 下一个显示页Id
      this.nextShowPageId()
    },
    /**
     * 血糖信息收集下一项
     * @param {Object} sugarData 血糖信息
     */
    nextItemSugar(sugarData) {
      for (const key in sugarData) {
        this[key] = sugarData[key]
      }

      // 下一个显示页Id
      this.nextShowPageId()
    },
    /**
     * 血压信息收集下一项
     * @param {Object} bpData 血压信息
     */
    nextItemBP(bpData) {

      if (this.hasBg == 0 && this.hasBp == 1 && this.has_pre_bg == 0) {
        this.finishBp()
      } else {
        for (const key in bpData) {
          this[key] = bpData[key]
        }

        // 下一个显示页Id
        this.nextShowPageId()
      }

    }
    ,
    /**
     * 血压完成收集
     */
    finishBp(bpData) {
      console.log(bpData)
      for (const key in bpData) {
        this[key] = bpData[key]
      }

      // 保存数据
      this.submit()
    }
    ,
    /**
     /**
     * 病史下一项
     * @param {String} illHis 病史字符串
     */
    nextItemHis(illHis) {
      console.log(illHis)
      this.illHis = illHis

      // 下一个显示页Id
      this.nextShowPageId()
    }
    ,
    /**
     * 病史完成收集
     * @param {String} illHis 病史字符串
     */
    finishHis(illHis) {
      console.log(illHis)
      this.illHis = illHis

      // 保存数据
      this.submit()
    }
    ,
    /**
     * 保存数据
     */
    submit() {
      this.noDoubleTap(() => {
        userBasicSave({
          'disease_tag': this.illType,
          'height': this.height,
          'weight': this.weight,
          'has_bg': this.hasBg,
          'fast_bg': this.kfxt,
          'fast_bg_time': this.kfxtDate,
          'postprandial_bg': this.chxt,
          'postprandial_bg_time': this.chxtDate,
          'hba1c': this.thxhdb,
          'hba1c_time': this.thxhdbDate,
          'has_bp': this.hasBp,
          'sbp': this.sbp,
          'dbp': this.dbp,
          'pulse': this.pulse,
          'disease_history': this.illHis,
          'has_drug': this.drugId,
          'drug_ratio': this.doseId,
          'has_weight': this.hasWeight,
          'has_pre_bg': this.has_pre_bg,
          'has_bg_drug': this.has_bg_drug,
          'prefer_tag': this.prefer_tag
        }).then(res => {
          console.log(res, 1234)
          if (res.status === 0) {
            localStorage.setItem('finishCollect', 1)
            if (this.sourceType == 6) {
              wx.miniProgram.reLaunch({
                url: '/pages/pressure/page/GoalManage/GoalManage'
              })
            }
            if (this.sourceType == 7) {
              wx.miniProgram.reLaunch({
                url: '/pages/pressure/page/GoalManageTwo/GoalManage'
              })
            } else {
              this.$router.replace({
                path: '/user/info/goal',
                query: {
                  source_type: this.sourceType
                }
              })
            }
          } else {
            this.$toast(res.msg)
          }
        })
      })
    }
    ,
    /**
     * 上一个显示页Id
     */
    lastShowPageId() {
      let nowPageId = this.showPageId
      let length = this.showPageIdArr.length
      console.log(this.showPageIdArr, 12341234)
      let nowIndex = this.showPageIdArr.indexOf(nowPageId)
      console.log(this.showPageId, 12341234)
      console.log(nowIndex, 1234123)

      // 显示页Id 变成 显示页顺序数组 当前下标-1 的 Id
      this.showPageId = this.showPageIdArr[nowIndex - 1]
      // 如果当前页Id的下标 是 显示页顺序数组 倒数第二个 则下一页是最后一页
      if (nowIndex === (length - 1)) this.isLastPage = true
      else this.isLastPage = false
      // 当前页码-1
      this.nowPage--
    }
    ,
    /**
     * 下一个显示页Id
     */
    nextShowPageId() {
      let nowPageId = this.showPageId
      let length = this.showPageIdArr.length
      let nowIndex = this.showPageIdArr.indexOf(nowPageId)

      // 显示页Id 变成 显示页顺序数组 当前下标+1 的 Id
      this.showPageId = this.showPageIdArr[nowIndex + 1]
      // 如果当前页Id的下标 是 显示页顺序数组 倒数第二个 则下一页是最后一页
      if (nowIndex === (length - 1)) this.isLastPage = true
      else this.isLastPage = false
      // 当前页码+1
      this.nowPage++
    }
  }
}
</script>

<style lang="scss" scoped>

.wrapper {
  width: 100%;
  overflow: hidden;
  box-sizing: border-box;
  padding: 26px 50px 50px;
}

.header-top {
  display: flex;
  justify-content: space-between;

  .go_back {
    font-size: 17px;
    font-weight: 400;
    color: #398CFF;
    line-height: 28px;
    padding-top: 20px;
    padding-left: 20px;
  }
}

.close_tip {
  width: 190px;
  height: 62px;
  position: absolute;
  right: 5px;
  top: 0px;
}
</style>
