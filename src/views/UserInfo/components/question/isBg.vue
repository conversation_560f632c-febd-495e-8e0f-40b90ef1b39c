<template>
  <div class="drug-wrapper">
    <div class="header">
      <h1 class="header-title">{{title}}</h1>
      <!--<div class="header-page">-->
      <!--<span class="header-page-now">{{ nowPage }}</span>-->
      <!--<span class="header-page-sum">/{{ sumPage }}</span>-->
      <!--</div>-->
    </div>
    <p class="tips">根据您填写的信息，我们会为您制定贴心计划。</p>
    <div class="content">
      <div class="group">
        <span
          @click="select('value', 1)"
          :class="{'group-btn': true, 'group-btn-active': value === 1}"
        >是</span>
      </div>
      <div class="group">
        <span
          @click="select('value', 0)"
          :class="{'group-btn': true, 'group-btn-active': value === 0}"
        >否</span>
      </div>
    </div>
    <div :class="nowPage==0?'footer footer-left':'footer'">
      <van-button v-if="nowPage!=0"
                  round
                  type="info"
                  @click="preQuestion"
                  class="footer-btn footer-btn-last"
      >
        <span class="footer-btn-text">
          <van-icon name="arrow-left" class="footer-btn-arrow"/>
          上一步
        </span>
      </van-button>
      <van-button
        round
        type="info"
        @click="finish"
        class="footer-btn"
      >
        <span class="footer-btn-text">
          下一步
          <van-icon name="arrow" class="footer-btn-arrow"/>
        </span>
      </van-button>
    </div>
  </div>
</template>

<script>
  import {Toast} from 'vant'

  export default {
    data: () => {
      return {}
    },
    props: {
      step: String,
      title: String,
      value: Number,
      nowPage: Number,
      sumPage: Number,
    },
    created() {
    },
    methods: {
      /**
       * 选择是否
       * @param {Number} drugKey 选项key
       * @param {Number} drugId 选项Id
       */
      select(key, id) {
        if (id === this[key]) {
          this[key] = ''
        } else {
          this[key] = id
        }
      },
      /**
       * 检查数据
       */
      checkData() {
        if (this.value === '') {
          Toast({
            message: '请至少选填一项',
            type: 'html',
            forbidClick: true,
            duration: 1000
          })

          return false
        } else {
          return true
        }
      },
      preQuestion() {
        this.$emit('pre-item-base', this.step)
      },
      /**
       * 完成
       */
      finish() {
        if (!this.checkData()) return

        this.$emit('input', this.value)
        this.$emit('next-item-state', this.step)
      }
    }
  }
</script>

<style lang="scss" scoped>
  .drug-wrapper {
    width: 100%;

    .header {
      display: flex;
      align-items: baseline;
      justify-content: space-between;

      .header-title {
        color: #333;
        font-size: 20px;
        font-weight: 600;
      }

      .header-page {

        .header-page-now {
          color: #333;
          font-size: 30px;
        }

        .header-page-sum {
          color: #999;
          font-size: 16px;
        }
      }
    }

    .tips {
      color: #666;
      font-size: 18px;
      margin-top: 16px;
      text-align: left;
      font-weight: 400;
      line-height: 28px;
    }

    .content {
      margin-top: 50px;

      .group {
        display: flex;
        margin-top: 20px;

        .group-btn {
          width: 100%;
          color: #666;
          font-size: 20px;
          font-weight: 400;
          padding: 10px 12px;
          border-radius: 6px;
          box-sizing: border-box;
          border: 1px solid #CCC;
          background-color: #fff;
        }

        .group-btn-active {
          color: #EE7800;
          background-color: #FFECDE;
          border: 1px solid #EE7800;
        }
      }
    }

    .footer {
      display: flex;
      margin-top: 50px;
      align-items: center;
      justify-content: space-between;

      .footer-btn {
        width: 125px;
        height: 42px;
        color: #fff;
        font-size: 20px;
        border: 1px solid #FF8100;
        background: linear-gradient(90deg, rgba(255, 129, 0, 1) 0%, rgba(253, 154, 87, 1) 100%);

        .footer-btn-text {
          height: 42px;
          display: flex;
          align-items: center;
          justify-content: center;

          .footer-btn-arrow {
            width: 8px;
            display: flex;
            margin-left: 6px;
            align-items: center;
            justify-content: center;
          }
        }
      }


      .footer-btn-last {
        color: #EE7800;
        background: #fff;
        border: 1px solid #EE7800;

        .footer-btn-text {

          .footer-btn-arrow {
            margin-left: 0;
            margin-right: 6px;
          }
        }
      }
    }

    .footer-left{
      justify-content: flex-end;
    }
  }
</style>
