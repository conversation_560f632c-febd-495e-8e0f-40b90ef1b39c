<template>
  <div class="sugar-wrapper">
    <div class="header">
      <h1 class="header-title">请填写任意一项</h1>
      <!--<div class="header-page">-->
        <!--<span class="header-page-now">{{ nowPage }}</span>-->
        <!--<span class="header-page-sum">/{{ sumPage }}</span>-->
      <!--</div>-->
    </div>
    <p class="tips">根据您填写的信息，我们会为您制定贴心计划。</p>
    <div class="content">
      <div class="group">
        <span
          @click="showOverlay(1)"
          :class="{'group-btn': true, 'group-btn-active': sugarId === 1}"
        >空腹血糖<b v-if="kfxt">: {{ kfxt }} mmol/L</b></span>
      </div>
      <div class="group">
        <span
          @click="showOverlay(2)"
          :class="{'group-btn': true, 'group-btn-active': sugarId === 2}"
        >餐后2小时血糖<b v-if="chxt">: {{ chxt }} mmol/L</b></span>
      </div>
      <div class="group">
        <span
          @click="showOverlay(3)"
          :class="{'group-btn': true, 'group-btn-active': sugarId === 3}"
        >糖化血红蛋白<b v-if="thxhdb">: {{ thxhdb }} %</b></span>
      </div>
    </div>
    <div class="footer">
      <van-button
        round
        type="info"
        @click="last"
        class="footer-btn footer-btn-last"
      >
        <span class="footer-btn-text">
          <van-icon name="arrow-left" class="footer-btn-arrow" />
          上一步
        </span>
      </van-button>
      <van-button
        round
        type="info"
        @click="next"
        class="footer-btn"
      >
        <span class="footer-btn-text">
          下一步
          <van-icon name="arrow" class="footer-btn-arrow" />
        </span>
      </van-button>
    </div>
    <!-- 弹框 -->
    <van-overlay
      :show="overlayShow"
      @click="hideOverlay"
      class="overlay-wrapper"
    >
      <div class="overlay-content" @click.stop>
        <div class="overlay-header">
          <van-icon
            name="cross"
            @click="hideOverlay"
            class="overlay-header-close"
          />
          <span v-if="sugarId === 1" class="overlay-header-name">空腹血糖</span>
          <span v-if="sugarId === 2" class="overlay-header-name">餐后2小时血糖</span>
          <span v-if="sugarId === 3" class="overlay-header-name">糖化血红蛋白</span>
        </div>
        <!-- 空腹血糖 -->
        <div v-if="sugarId === 1" class="overlay-group">
          <div class="group-box">
            <van-field
              v-model="kfxt"
              @click="inputClick"
              @focus="kfxtFocus = true"
              @blur="inputBlur('kfxt')"
              @input="inputWatch('kfxt')"
              type="number"
              inputmode="decimal"
              input-align="center"
              :class="{'group-input': true, 'group-input-focus': kfxtFocus}"
            />
            <span class="group-unit">mmol/L</span>
          </div>
          <div class="group-box">
            <div class="group-date">
              <input
                readonly
                type="text"
                @click="inputClick($event, 'kfxtDate')"
                v-model="kfxtDate"
                class="group-date-input"
              >
              <div class="group-date-cont">
                <span v-if="kfxtDate" class="group-date-text">{{ dateFormat(kfxtDate, '/') }}</span>
                <span v-else class="group-date-placeholder">请选择测量时间</span>
                <cc-svg-icon icon-class="date" class="group-date-icon"/>
              </div>
            </div>
          </div>
        </div>
        <!-- 餐后2小时血糖 -->
        <div v-if="sugarId === 2" class="overlay-group">
          <div class="group-box">
            <van-field
              v-model="chxt"
              @click="inputClick"
              @focus="chxtFocus = true"
              @blur="inputBlur('chxt')"
              @input="inputWatch('chxt')"
              type="number"
              inputmode="decimal"
              input-align="center"
              :class="{'group-input': true, 'group-input-focus': chxtFocus}"
            />
            <span class="group-unit">mmol/L</span>
          </div>
          <div class="group-box">
            <div class="group-date">
              <input
                readonly
                type="text"
                v-model="chxtDate"
                @click="inputClick($event, 'chxtDate')"
                class="group-date-input"
              >
              <div class="group-date-cont">
                <span v-if="chxtDate" class="group-date-text">{{ dateFormat(chxtDate, '/') }}</span>
                <span v-else class="group-date-placeholder">请选择测量时间</span>
                <cc-svg-icon icon-class="date" class="group-date-icon"/>
              </div>
            </div>
          </div>
        </div>
        <!-- 糖化血红蛋白 -->
        <div v-if="sugarId === 3" class="overlay-group">
          <div class="group-box">
            <van-field
              v-model="thxhdb"
              @click="inputClick"
              @focus="thxhdbFocus = true"
              @blur="inputBlur('thxhdb')"
              @input="inputWatch('thxhdb')"
              type="number"
              inputmode="decimal"
              input-align="center"
              :class="{'group-input': true, 'group-input-focus': thxhdbFocus}"
            />
            <span class="group-unit">%</span>
          </div>
          <div class="group-box">
            <div class="group-date">
              <input
                readonly
                type="text"
                v-model="thxhdbDate"
                @click="inputClick($event, 'thxhdbDate')"
                class="group-date-input"
              >
              <div class="group-date-cont">
                <span v-if="thxhdbDate" class="group-date-text">{{ dateFormat(thxhdbDate, '/') }}</span>
                <span v-else class="group-date-placeholder">请选择测量时间</span>
                <cc-svg-icon icon-class="date" class="group-date-icon"/>
              </div>
            </div>
          </div>
        </div>
        <div class="overlay-footer">
          <van-button
            round
            type="info"
            @click="overlaySure"
            class="overlay-footer-btn"
          >确定</van-button>
        </div>
      </div>
    </van-overlay>
    <van-popup
      v-model="showDatePicker"
      position="bottom"
      get-container="#app"
    >
      <van-datetime-picker
        v-model="currentDate"
        :formatter="formatter"
        :max-date="maxDate"
        @confirm="datetimeConfirm"
        @cancel="datetimeCancel"
        type="date"
        title="请选择测量时间"
      />
    </van-popup>
  </div>
</template>

<script>
import moment from 'moment'

export default {
  data: () => {
    return {
      kfxt: '', // 空腹血糖 值
      kfxtDate: '', // 空腹血糖 测量时间
      chxt: '', // 餐后2小时血糖 值
      chxtDate: '', // 餐后2小时血糖 测量时间
      thxhdb: '', // 糖化血红蛋白 值
      thxhdbDate: '', // 糖化血红蛋白 测量时间
      limitVal: {
        'kfxt': { max: 33.3, min: 1.1 },
        'chxt': { max: 33.3, min: 1.1 },
        'thxhdb': { max: 20, min: 0 }
      },
      sugarId: '', // 1-空腹血糖 2-餐后2小时血糖 3-糖化血红蛋白
      overlayShow: false,
      clearSugarId: '',
      kfxtFocus: false,
      chxtFocus: false,
      thxhdbFocus: false,
      showDatePicker: false,
      dateKey: '',
      currentDate: new Date(),
      maxDate: new Date()
    }
  },
  props: {
    nowPage: Number,
    sumPage: Number,
    kfxtProp: Number,
    kfxtDateProp: Number,
    chxtProp: Number,
    chxtDateProp: Number,
    thxhdbProp: Number,
    thxhdbDateProp: Number
  },
  created() {
    this.kfxt = this.kfxtProp // 空腹血糖 值
    this.kfxtDate = this.kfxtDateProp // 空腹血糖 测量时间
    this.chxt = this.chxtProp // 餐后2小时血糖 值
    this.chxtDate = this.chxtDateProp // 餐后2小时血糖 测量时间
    this.thxhdb = this.thxhdbProp // 糖化血红蛋白 值
    this.thxhdbDate = this.thxhdbDateProp // 糖化血红蛋白 测量时间
  },
  computed: {
    sugarList() {
      return {
        1: {
          val: this.kfxt,
          date: this.kfxtDate,
          name: '空腹血糖',
          key: 'kfxt'
        },
        2: {
          val: this.chxt,
          date: this.chxtDate,
          name: '餐后2小时血糖',
          key: 'chxt'
        },
        3: {
          val: this.thxhdb,
          date: this.thxhdbDate,
          name: '糖化血红蛋白',
          key: 'thxhdb'
        }
      }
    }
  },
  methods: {
    /**
     * datetime选项格式化函数
     * @param {String} type 类型
     * @param {String} val 原值
     * @return {String} 格式化值
     */
    formatter(type, val) {
      if (type === 'year') {
        return val + '年';
      }
      if (type === 'month') {
        return val + '月';
      }
      if (type === 'day') {
        return val + '日';
      }
      return val;
    },
    /**
     * datetime点击完成按钮时触发的事件
     * @param {Date} value 当前选中的时间
     */
    datetimeConfirm(value) {
      this.showDatePicker = false
      this[this.dateKey] = this.dateFormat(value)
    },
    /**
     * datetime点击取消按钮时触发的事件
     */
    datetimeCancel() {
      this.showDatePicker = false
    },
    /**
     * input点击事件
     * @param {MouseEvent} e 点击事件
     * @param {String} dateKey 日期数据的键名
     */
    inputClick(e, dateKey = '') {
      e.stopPropagation()
      e.target.focus()

      if (dateKey != '') {
        this.showDatePicker = true
        this.dateKey = dateKey
        let currentDate = this.dateFormat(this[dateKey])
        if (currentDate != '') this.currentDate = new Date(currentDate)
        else this.currentDate = new Date()
      }
    },
    /**
     * input输入判断
     * @param {String} key 数据键
     */
    inputWatch(key) {
      let oVal = this[key]
      let limitVal = this.limitVal[key]
      const pointIndex = oVal.indexOf('.')

      if (pointIndex > 0) {
        let start = oVal.slice(0, pointIndex).replace(/\D+/g, '')
        let after = oVal.slice(pointIndex + 1).replace(/\D+/g, '')
        after = after.slice(0, 1)
        oVal = start + '.' + after
        if (start >= limitVal.max) oVal = limitVal.max
      } else {
        oVal = oVal.replace(/\D+/g, '')
        if (oVal > limitVal.max) oVal = limitVal.max
      }

      this[key] = oVal
    },
    /**
     * input失焦判断
     * @param {String} key 数据键
     */
    inputBlur(key) {
      this[`${key}Focus`] = false
      let oVal = this[key]
      let limitVal = this.limitVal[key]

      if (oVal === '') return

      oVal = Number(oVal)
      if (oVal < limitVal.min) oVal = limitVal.min

      this[key] = oVal
    },
    /**
     * 日期格式化
     * @param {String | Date} date 日期
     * @param {String} sep 分隔符
     * @return {String} 格式化日期
     */
    dateFormat(date, sep = '-') {
      if (isNaN(Date.parse(date))) return ''
      return moment(date).format(`YYYY${sep}MM${sep}DD`)
    },
    /**
     * 检查数据
     *  当有一组全填时,返回true,否则返回false
     *  未全填两项的组,两项都清空
     */
    checkData() {
      let isOk = false

      // 有一组两项都填写,既isOk为true
      for (const item of Object.values(this.sugarList)) {
        if (item.val !== '' && item.date !== '') {
          isOk = true
        } else {
          this[item.key] = ''
          this[`${item.key}Date`] = ''
        }
      }

      return isOk
    },
    /**
     * 上一题
     */
    last() {
      this.$emit('last-item-sugar', {
        kfxt: this.kfxt,
        kfxtDate: this.kfxtDate,
        chxt: this.chxt,
        chxtDate: this.chxtDate,
        thxhdb: this.thxhdb,
        thxhdbDate: this.thxhdbDate
      })
    },
    /**
     * 下一题
     */
    next() {
      // 检查数据
      if (!this.checkData()) {
        this.$toast('请填写任意一项血糖信息')
        return
      }

      this.$emit('next-item-sugar', {
        kfxt: this.kfxt,
        kfxtDate: this.kfxtDate,
        chxt: this.chxt,
        chxtDate: this.chxtDate,
        thxhdb: this.thxhdb,
        thxhdbDate: this.thxhdbDate
      })
    },
    /**
     * 显示填写框
     * @param {Number} id 血糖id
     */
    showOverlay(id) {
      // 填写框延时清空存在时 清除
      if (this.clearSugarId) clearTimeout(this.clearSugarId)
      this.sugarId = id
      this.overlayShow = true
    },
    /**
     * 隐藏填写框
     */
    hideOverlay() {
      this.overlayShow = false
      // 遮罩层延时消失, 填写框延时清空
      this.clearSugarId = setTimeout(() => {
        this.sugarId = ''
      }, 300)
    },
    /**
     * 填写确认
     */
    overlaySure() {
      let isOk = true
      let tips = ''
      let item = this.sugarList[this.sugarId]

      if (item.val === '') {
        isOk = false
        tips = `请填写 ${item.name}`
      } else if (item.date === '') {
        isOk = false
        tips = `请填写 ${item.name} 测量时间`
      }

      if (isOk) {
        // 隐藏填写框
        this.hideOverlay()
      } else {
        this.$toast(tips)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.sugar-wrapper {
  width: 100%;

  .header {
    display: flex;
    align-items: baseline;
    justify-content: space-between;

    .header-title {
      color: #333;
      font-size: 20px;
      font-weight: 600;
    }

    .header-page {

      .header-page-now {
        color: #333;
        font-size: 30px;
      }

      .header-page-sum {
        color: #999;
        font-size: 16px;
      }
    }
  }

  .tips {
    color: #666;
    font-size: 18px;
    margin-top: 16px;
    text-align: left;
    font-weight: 400;
    line-height: 28px;
  }

  .content {
    margin-top: 15px;

    .group {
      display: flex;
      margin-top: 20px;

      .group-btn {
        width: 100%;
        color: #666;
        font-size: 20px;
        font-weight: 400;
        padding: 10px 12px;
        border-radius: 6px;
        box-sizing: border-box;
        border: 1px solid #CCC;
        background-color: #fff;
      }

      .group-btn-active {
        color: #EE7800;
        background-color: #FFECDE;
        border: 1px solid #EE7800;
      }
    }
  }

  .footer {
    display: flex;
    margin-top: 50px;
    align-items: center;
    justify-content: space-between;

    .footer-btn {
      width: 125px;
      height: 42px;
      color: #fff;
      font-size: 20px;
      border: 1px solid #FF8100;
      background: linear-gradient(90deg,rgba(255,129,0,1) 0%,rgba(253,154,87,1) 100%);

      .footer-btn-text {
        height: 42px;
        display: flex;
        align-items: center;
        justify-content: center;

        .footer-btn-arrow {
          width: 8px;
          display: flex;
          margin-left: 6px;
          align-items: center;
          justify-content: center;
        }
      }
    }

    .footer-btn-last {
      color: #EE7800;
      background: #fff;
      border: 1px solid #EE7800;

      .footer-btn-text {

        .footer-btn-arrow {
          margin-left: 0;
          margin-right: 6px;
        }
      }
    }
  }

  .overlay-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;

    .overlay-content {
      width: 335px;
      border-radius: 9px;
      background: #fff;

      .overlay-header {
        height: 28px;
        display: flex;
        padding-top: 19px;
        position: relative;
        align-items: center;
        justify-content: center;

        .overlay-header-close {
          color: #CCC;
          font-size: 14px;
          position: absolute;
          top: 50%;
          left: 25px;
        }

        .overlay-header-name {
          color: #000;
          font-size: 20px;
          font-weight: 400;
        }
      }

      .overlay-group {

        .group-box {
          display: flex;
          margin-top: 20px;
          align-items: center;
          justify-content: center;

          .group-date {
            color: #EE7800;
            position: relative;

            .group-date-cont {
              width: 200px;
              height: 40px;
              display: flex;
              color: #ccc;
              font-size: 13px;
              border-radius: 5px;
              align-items: center;
              padding: 0 10px 0 8px;
              box-sizing: border-box;
              border: 1px solid #ccc;
              justify-content: space-between;

              .group-date-text {
                color: #000;
                font-size: 20px;
                font-weight: 400;
              }

              .group-date-placeholder {
                color: #CCC;
                font-size: 20px;
                font-weight: 400;
              }

              .group-date-icon {
                margin-left: 5px;
              }
            }

            .group-date-input {
              width: 200px;
              height: 40px;
              font-size: 13px;
              text-align: center;
              border-radius: 5px;
              border: 1px solid #ffecdd;
              position: absolute;
              top: 0;
              left: 0;
              opacity: 0;

              &:focus + .group-date-cont {
                color: #EE7800;
                border: 1px solid #EE7800;
              }
            }
          }

          .group-input {
            width: 138px;
            height: 40px;
            padding: 5px;
            color: #000;
            font-size: 25px;
            font-weight: 400;
            text-align: center;
            border-radius: 6px;
            border: 1px solid #ccc;
            -webkit-appearance: none;
          }

          .group-input-focus {
            border: 1px solid #EE7800;
          }

          .group-unit {
            color: #333;
            font-size: 15px;
            font-weight: 400;
            margin-left: 6px;
          }
        }
      }

      .overlay-footer {
        margin: 20px 0 26px;

        .overlay-footer-btn {
          width: 125px;
          color: #fff;
          font-size: 20px;
          border: 1px solid #FF8100;
          background: linear-gradient(90deg,rgba(255,129,0,1) 0%,rgba(253,154,87,1) 100%);
        }
      }
    }

  }
}
</style>
