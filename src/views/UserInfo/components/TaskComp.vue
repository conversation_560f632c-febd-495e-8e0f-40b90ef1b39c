<template>
  <div class="task-wrapper">
    <live-comp
      v-if="taskData.id === 1"
      :liveData="taskData"
      @openMask="whereToGo"
      style="margin-bottom: 15px;"
    />
    <div v-else class="task-group">
      <div class="task-top">
        <div class="task-top-left">
          <div class="task-icon">
            <img v-if="imgSrc" :src="imgSrc" >
          </div>
          <div class="task-info">
            <div class="task-info-top">
              <span class="task-info-top-name">{{ taskData.name }}</span>
              <img src="../imgs/<EMAIL>" class="task-info-top-icon">
              <van-icon name="cross" class="task-info-top-cross" />
              <span class="task-info-top-points">{{ taskData.points }}</span>
            </div>
            <p v-if="taskData.joined" class="task-info-bottom">已有{{ taskData.joined }}人报名参加</p>
            <p v-if="taskData.finished" class="task-info-bottom">已有{{ taskData.finished }}人完成该任务</p>
          </div>
        </div>
        <div class="task-btn">
          <done-btn v-if="taskData.status === '1'" @clickDone="clickDone" />
          <undone-btn v-else @clickUndone="clickUndone(taskData.id)" />
        </div>
      </div>
      <div class="task-bottom">
        <div v-if="taskData.id === 2" class="task-bottom-progress">
          <van-progress
            inactive
            :show-pivot="false"
            :percentage="percentage"
            class="task-bottom-progress-comp"
          />
          <span class="task-bottom-progress-num">{{ taskData.current }}/{{ taskData.goal }}</span>
        </div>
        <div v-else class="task-bottom-detail">
          <div
            v-for="(item, index) in taskData.details"
            :key="index"
            class="task-bottom-detail-group"
          >
            <span class="task-bottom-detail-group-name">{{ item.mission_name }}</span>
            <span class="task-bottom-detail-group-points">+{{ item.mission_points }}</span>
            <img v-if="item.mission_status === 0" src="../imgs/<EMAIL>" class="task-bottom-detail-group-img">
            <img v-else src="../imgs/<EMAIL>" class="task-bottom-detail-group-img">
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import DoneBtn from './DoneBtn.vue'
import UndoneBtn from './UndoneBtn.vue'
import LiveComp from './LiveComp.vue'

export default {
  data: () => {
    return {
      imgSrc: '',
      percentage: 0
    }
  },
  components: {
    'done-btn': DoneBtn,
    'undone-btn': UndoneBtn,
    'live-comp': LiveComp
  },
  props: {
    taskData: Object,
    sourceType: String
  },
  created() {
    let id = this.taskData.id
    let goal = this.taskData.goal
    let current = this.taskData.current
    let imgObj = {
      2: require('../imgs/sports.png'),
      3: require('../imgs/eat.png'),
      4: require('../imgs/celiang.png'),
      5: require('../imgs/medicine.png'),
      6: require('../imgs/article.png'),
      7: require('../imgs/Q&A.png'),
      8: require('../imgs/medicine.png'),
      9: require('../imgs/heart.png')
    }

    this.imgSrc = imgObj[id]
    if (id === 2 && goal) {
      this.percentage = current > goal ? 100 : (current / goal) * 100
    }
  },
  methods: {
    /**
     * 点击已完成按钮
     */
    clickDone() {
      console.log('点击已完成按钮')
      // 跳转
      this.whereToGo()
    },
    /**
     * 点击去完成按钮
     */
    clickUndone(id) {
      console.log('点击去完成按钮')
      if (id==3) {
        if (this.sourceType == 3) {
          this.$router.push({
            path: '/cookbook/recipes',
            query: {
              source_type: this.sourceType
            }
          })
        }
      } else {
        // 跳转
        this.whereToGo()
      }
    },
    /**
     * 跳转
     */
    whereToGo() {
      this.$emit('openMask')
    }
  }
}
</script>

<style lang="scss" scoped>
.task-group {
  overflow: hidden;
  padding: 16px 11px 16px 3px;
  border-bottom: 1px solid #F5F6FA;

  &:last-of-type {
    border-bottom: 0;
  }

  .task-top {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .task-top-left {
      display: flex;
      align-items: center;

      .task-icon {
        width: 44px;
        line-height: 0;

        img {
          width: 100%;
        }
      }

      .task-info {
        margin-left: 11px;

        .task-info-top {
          display: flex;
          align-items: center;
          justify-content: flex-start;

          .task-info-top-name {
            color: #333;
            font-size: 20px;
            font-weight: 400;
          }

          .task-info-top-icon {
            width: 24px;
            margin-left: 4px;
          }

          .task-info-top-cross {
            font-size: 13px;
            font-weight: 400;
            color: #F9AB10;
            margin-left: 2px;
          }

          .task-info-top-points {
            font-size: 16px;
            font-weight: 400;
            color: #F9AB10;
            margin-left: 2px;
          }
        }

        .task-info-bottom {
          color: #666;
          font-size: 16px;
          margin-top: 5px;
          text-align: left;
          font-weight: 400;
          line-height: 22px;
        }
      }
    }

    .task-btn {
      width: 78px;
      line-height: 0;
    }
  }

  .task-bottom {
    margin-top: 5px;
    padding-left: 58px;

    .task-bottom-progress {
      display: flex;
      align-items: center;
      justify-content: flex-start;

      .task-bottom-progress-comp {
        width: 70px;
        height: 6px;
      }

      .task-bottom-progress-num {
        color: #999;
        font-size: 12px;
        font-weight: 400;
        margin-left: 8px;
      }
    }

    .task-bottom-detail {
      display: flex;
      align-items: center;

      .task-bottom-detail-group {
        display: flex;
        margin-left: 8px;
        align-items: center;

        &:first-of-type {
          margin-left: 0;
        }

        .task-bottom-detail-group-points,
        .task-bottom-detail-group-name {
          color: #999;
          font-size: 15px;
          font-weight: 400;
          line-height: 21px;
        }

        .task-bottom-detail-group-points {
          margin-left: 2px;
        }

        .task-bottom-detail-group-img {
          width: 15px;
          margin-left: 5px;
        }
      }
    }
  }
}
</style>
