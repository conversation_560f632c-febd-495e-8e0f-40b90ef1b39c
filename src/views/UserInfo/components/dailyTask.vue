<template>
  <div>
    <!-- 今日食谱 -->
    <div class="RecipesBox" @click="bindNavigateTo('/pages/home/<USER>/recipes/recipes','recipes')" data-type="2">
      <div class="goModalDetailBox">
        去查看
        <img src="../imgs/jinru.png"></img>
      </div>
      <div class="boxLeft">
        <div class="p1">
          <div v-if="index_info.is_read_recipes == 0" class="immature">已更新</div>
          今日食谱
          <div class="getHealthPowerBox">
            <img src="../imgs/jiankangli.png"></img>
            <span>+</span>
            5
          </div>
        </div>
        <div class="p2">定时进餐 定量而食</div>
        <div class="p6">
          完成任务
          ({{ task_info.task_food }}/1)
          <img v-if="task_info.task_food>0"
                 src="https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/wechat/zzmedSugarController/1.5.1/yiwancheng.png"></img>
        </div>
      </div>
    </div>

    <!-- 控糖知识 -->
    <div class="knowledgeBox" @click="bindNavigateTo('/pages/home/<USER>/myArticle/myArticle')">
      <div class="goModalDetailBox">
        去阅读
        <img src="../imgs/jinru.png"></img>
      </div>
      <div class="boxLeft">
        <div class="p1">
          <div v-if="index_info.new_article == 1" class="immature">已更新</div>
          健康知识
          <div class="getHealthPowerBox" v-if="index_info.new_article == 1||task_info.task_article==1">
            <img src="../imgs/jiankangli.png"></img>
            <span>+</span>
            5
          </div>
        </div>
        <div class="p2">{{ index_info.latest_article.title }}</div>
        <!-- <div class="p6" v-if="{{index_info.new_article == 1||task_info.task_article==1}}"> -->
        <div class="p6">
          完成任务
          ({{ task_info.task_article }}/1)
          <img v-if="task_info.task_article>0"
               src="https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/wechat/zzmedSugarController/1.5.1/yiwancheng.png"></img>
        </div>
      </div>
    </div>

    <!-- 今日运动 -->
    <div class="motionBox" @click="bindNavigateTo('/pages/home/<USER>/motion/motion')" data-type="3">
      <div class="goModalDetailBox">
        去运动
        <img src="../imgs/jinru.png"></img>
      </div>
      <div class="boxLeft">
        <div class="p1">今日运动
          <div class="getHealthPowerBox">
            <img src="../imgs/jiankangli.png"></img>
            <span>+</span>
            5
          </div>
        </div>
        <div class="p2">规律运动 有宜控糖</div>
        <div class="progressBox">
          <div class="progress">
            <span :style="{ width: finishStep + '%' }"></span>
          </div>
          <div class="progressNum">{{ index_info.today_step }}/{{ index_info.step_target }}</div>
        </div>
        <div class="p6">
          完成任务
          ({{ task_info.task_motion }}/1)
          <img v-if="task_info.task_motion>0"
               src="https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/wechat/zzmedSugarController/1.5.1/yiwancheng.png"></img>
        </div>
      </div>
    </div>

    <!-- 测量 -->
    <div class="pressureBox" @click="bindNavigateTo('/pages/pressure/page/BsPressureRecord/BsPressureRecord')">
      <div class="goModalDetailBox">
        去测量
        <img src="../imgs/jinru.png"></img>
      </div>
      <div class="boxLeft">
        <div class="p1">测量
          <div class="getHealthPowerBox">
            <img src="../imgs/jiankangli.png"></img>
            <span>+</span>
            15 / 次
          </div>
        </div>
        <div class="p2">每日测量 守住健康</div>
        <div class="p6">
          完成任务
          ({{ task_info.task_bg }}/2)
          <img v-if="task_info.task_bg>1"
               src="https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/wechat/zzmedSugarController/1.5.1/yiwancheng.png"></img>
        </div>
      </div>
    </div>

    <!-- 用药管理 -->
    <div class="drugBox" @click="bindNavigateTo('/pages/pressure/page/drugRemind/drugRemind')">
      <div class="goModalDetailBox">
        去设置
        <img src="../imgs/jinru.png"></img>
      </div>
      <div class="boxLeft">
        <div class="p1">用药管理
          <div class="getHealthPowerBox">
            <img src="../imgs/jiankangli.png"></img>
            <span>+</span>
            5
          </div>
        </div>
        <div class="p2">按时吃药 谨遵医嘱</div>
        <div class="p6">
          完成任务
          ({{ task_info.task_drug }}/1)
          <img v-if="task_info.task_drug>0"
               src="https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/wechat/zzmedSugarController/1.5.1/yiwancheng.png"></img>
        </div>
      </div>
    </div>

  </div>
</template>

<script>
import wx from "weixin-js-sdk";
import {readFood} from "../../../api/weekHealthReport";

export default {
  data: () => {
    return {}
  },
  props: {
    task_info: Object,
    index_info: Object
  },
  created() {
  },
  computed: {
    /**
     * 页脚跳转路由数据
     */
    finishStep() {
      let step = (parseInt(this.index_info.today_step) / parseInt(this.index_info.step_target)).toFixed(2) * 100
      if (step > 100) {
        return 100
      }
      return step
    }
  },
  methods: {

    bindNavigateTo(url, type = false) {
      if (type === 'recipes') {
        this.noDoubleTap(() => {
          readFood().then(res=>{
            wx.miniProgram.navigateTo({
              url: url
            })
          })

        })
      } else {
        this.noDoubleTap(() => {
          wx.miniProgram.navigateTo({
            url: url
          })
        })
      }

    }
  }
}
</script>


<style lang="scss" scoped>
.knowledgeBox {
  width: 304px;
  height: 128px;
  background: url(https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/wechat/zzmedSugarController/2.2.0/sugarControl%402x.png);
  background-size: cover;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 21px 0 18px;
  position: relative;
}

.drugBox {
  width: 304px;
  height: 128px;
  background: url(https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/wechat/zzmedSugarController/2.2.0/drugs%402x.png);
  background-size: cover;
  margin: 14px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 21px 0 18px;
  position: relative;
}

.pressureBox {
  width: 304px;
  height: 128px;
  background: url(https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/wechat/zzmedSugarController/2.2.0/measures%402x.png);
  background-size: cover;
  margin: 14px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 21px 0 18px;
  position: relative;
}

.RecipesBox {
  width: 304px;
  height: 128px;
  background: url(https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/wechat/zzmedSugarController/2.2.0/foods%402x.png);
  background-size: cover;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 21px 0 18px;
  position: relative;
  margin-bottom: 15px;
}

.motionBox {
  width: 304px;
  height: 128px;
  background: url(https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/wechat/zzmedSugarController/2.2.0/sports%402x.png);
  background-size: cover;
  margin: 15px 0 15px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 21px 0 18px;
  position: relative;
}

.goModalDetailBox {
  position: absolute;
  right: 21px;
  top: 12px;
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #EE671F;
  line-height: 20px;
}

.goModalDetailBox img {
  width: 21px;
  height: 21px;
  margin-left: 6px;
}

.p1 {
  color: #333;
  font-weight: 600;
  font-size: 22px;
  line-height: 38px;
  margin-bottom: 3px;
  display: flex;
  align-items: center;
}

.p2 {
  color: #585858;
  font-size: 15px;
  width: 175px;
  line-height: 21px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  text-align: left;
}

.p6 {
  display: flex;
  align-items: center;
  margin-top: 4px;
  color: #999;
  font-size: 15px;
  line-height: 21px;
}

.getHealthPowerBox {
  display: flex;
  align-items: center;
  margin-left: 8px;
  color: #F67B28;
  font-size: 16px;
  line-height: 18px;
  font-weight: 400;
}

.getHealthPowerBox span {
  font-size: 13px;
  margin: 0 3px;
}

.getHealthPowerBox img {
  width: 17px;
  height: 20px;
  margin-right: 3px;
}


.progressBox {
  display: flex;
  align-items: center;
  text-align: left;
  margin-top: 4px;
  margin-bottom: 6px;
}

.progress {
  width: 70px;
  height: 6px;
  border-radius: 3px;
  background: #E8E8E8;
  position: relative;
  margin-right: 8px;
}

.progress span {
  position: absolute;
  height: 6px;
  border-radius: 3px;
  background: #EE7800;
  max-width: 70px;
}

.progressNum {
  color: #999;
  font-size: 12px;
}

.p6 img {
  width: 15px;
  height: 15px;
  margin-left: 6px;
}

.immature {
  width: 52px;
  height: 20px;
  background: #FA5151;
  border-radius: 3px;
  color: #fff;
  text-align: center;
  line-height: 20px;
  font-size: 13px;
  margin-right: 6px;
}
</style>
