<template>
  <div class="gift-progress" :class="{'gift-progress2': statusProp === 3}">
    <div class="progress-tips" v-if="statusProp != 3" :style="{ left: progress }">即将拿到</div>
    <div class="progress-out">
      <div class="progress-in" :style="{ width: progress }"></div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      progress: '0%'
    };
  },
  props: {
    progressProp: {
      type: String,
      default: "0%"
    },
    statusProp: {
      type: Number,
      default: 0
    }
  },
  mounted() {
    setTimeout(() => {
      this.progress = this.progressProp;
    }, 1000);
  }
}
</script>

<style lang="scss" scoped>
.gift-progress {
  width: 100%;
  padding-top: 26px;
  position: relative;

  .progress-tips {
    width: 56px;
    height: 18px;
    line-height: 18px;
    text-align: center;
    background: #e02e24;
    border-radius: 2px;
    font-size: 12px;
    font-weight: 500;
    color: #ffffff;
    position: absolute;
    top: 0;
    transform: translateX(-50%);
    transition: all 3s ease;

    &::before {
      content: "";
      display: block;
      width: 0;
      height: 0;
      border-top: 5px solid #e02e24;
      border-left: 6px solid transparent;
      border-right: 6px solid transparent;
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translate(-50%, 90%);
    }
  }

  .progress-out {
    width: 100%;
    height: 12px;
    background: #fee5b8;
    border-radius: 6px;
  }

  .progress-in {
    height: 12px;
    background: linear-gradient(270deg, #ff9400 0%, #fa4506 100%);
    border-radius: 6px;
    transition: all 3s ease;
  }
}

.gift-progress2 {
  padding-top: 0;
}
</style>
