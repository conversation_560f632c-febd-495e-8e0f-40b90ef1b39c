<template>
  <div class="live-group">
    <div class="live-head" @click="goLive">
      <img v-if="liveData.bg_img" :src="liveData.bg_img">
    </div>
    <div class="live-info">
      <div class="live-info-left">
        <p class="live-info-left-top">已有{{ liveData.joined }}人报名参加</p>
        <p class="live-info-left-bottom">完成任务毅志力+{{ liveData.points }}</p>
      </div>
      <div class="live-info-right">
        <done-btn v-if="liveData.status === 1" @clickDone="clickDone" />
        <undone-btn v-else @clickUndone="clickUndone" />
      </div>
    </div>
  </div>
</template>

<script>
import DoneBtn from './DoneBtn.vue'
import UndoneBtn from './UndoneBtn.vue'

export default {
  data: () => {
    return {}
  },
  components: {
    'done-btn': DoneBtn,
    'undone-btn': UndoneBtn
  },
  props: {
    liveData: Object
  },
  methods: {
    /**
     * 点击已完成按钮
     */
    clickDone() {
      console.log('点击已完成按钮')
      // 跳转
      this.whereToGo()
    },
    /**
     * 点击去完成按钮
     */
    clickUndone() {
      console.log('点击去完成按钮')
      // 跳转
      this.whereToGo()
    },
    /**
     * 跳转
     */
    whereToGo() {
      this.$emit('openMask')
    },
    /**
     * 跳转直播
     */
    goLive() {
      if (!this.liveData.url) return
      window.location.href = this.liveData.url
    }
  }
}
</script>

<style lang="scss" scoped>
.live-group {
  height: 148px;
  background: #fff;
  border-radius: 8px;
  margin-top: 15px;
  box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.1);

  .live-head {
    height: 91px;
    overflow: hidden;

    img {
      width: 100%;
    }
  }

  .live-info {
    height: 57px;
    display: flex;
    align-items: center;
    padding: 0 11px 0 21px;
    justify-content: space-between;

    .live-info-left {
      text-align: left;

      .live-info-left-top {
        color: #666;
        font-size: 14px;
        font-weight: 400;
      }

      .live-info-left-bottom {
        color: #999;
        font-size: 12px;
        margin-top: 4px;
        font-weight: 400;
      }
    }

    .live-info-right {
      width: 78px;
      line-height: 0;
    }
  }
}
</style>
