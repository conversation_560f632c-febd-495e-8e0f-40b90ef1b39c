<template>
  <van-button
    round
    type="info"
    @click="undone"
    class="undone-btn"
  >
  <span class="undone-btn-box">
    <span class="undone-btn-name">去完成</span>
    <van-icon name="arrow"  class="undone-btn-icon" />
  </span>
  </van-button>
</template>

<script>
export default {
  methods: {
    /**
     * 点击去完成按钮
     */
    undone() {
      this.$emit('clickUndone')
    }
  }
}
</script>

<style lang="scss" scoped>
.undone-btn {
  border: 0;
  padding: 0;
  width: 100%;
  height: 33px;
  color: #FFF;
  font-weight: 600;
  line-height: 33px;
  background: linear-gradient(90deg, rgba(255, 129, 0, 1) 0%, rgba(253, 154, 87, 1) 100%);

  .undone-btn-box {
    height: 33px;
    display: flex;
    align-items: center;
    justify-content: center;

    .undone-btn-name {
      font-size: 15px;
      line-height: 16px;
    }
  }
}
</style>
