<template>
  <div class="tab-wrapper">
    <div
      v-for="(tab, index) in tabList"
      :key="tab.id"
      @click="changeTab(tab.id)"
      :style="tabList.length === 1 ? singleStyle : ''"
      :class="{'group': true, 'group-active': tab.active}"
    >
      <img :src="tab.active ? tab.activeIcon : tab.icon" class="group-icon">
      <span class="group-name">{{ tab.name }}</span>
      <div v-if="tab.active && index !== 0" class="group-triangle group-triangle-left"></div>
      <div v-if="tab.active && index !== (tabList.length - 1)" class="group-triangle group-triangle-right"></div>
    </div>
  </div>
</template>

<script>
export default {
  data: () => {
    return {
      activeId: '',
      singleStyle: 'background: linear-gradient(360deg,rgba(255, 237, 208, 1) 0%,rgba(255, 222, 181, 1) 100%);'
    }
  },
  props: {
    tabList: Array,
    selectTabId: Number
  },
  watch: {
    activeId(newVal, oldVal) {
      for (const tab of this.tabList) {
        if (tab.id === newVal) this.$set(tab, 'active', true)
        else this.$set(tab, 'active', false)
      }
    },
    selectTabId(newVal, oldVal) {
      this.activeId = newVal
    }
  },
  created() {
    this.activeId = this.selectTabId
  },
  methods: {
    /**
     * 改变tab
     */
    changeTab(tabId) {
      if (tabId === this.activeId) return

      this.$emit('changeTabId', tabId)
    }
  }
}
</script>

<style lang="scss" scoped>
.tab-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .group {
    flex: 1;
    height: 44px;
    display: flex;
    margin-left: 1px;
    align-items: center;
    justify-content: center;
    border-radius: 5px 5px 0 0;
    /*box-shadow: 0px 2px 7px 0px rgba(0,0,0,0.1);*/
    background: linear-gradient(360deg,rgba(255, 237, 208, 1) 0%,rgba(255, 222, 181, 1) 100%);
    border-image: linear-gradient(180deg, rgba(255, 233, 197, 1), rgba(255, 206, 147, 1)) 1 1;

    &:first-of-type {
      margin-left: 0;
    }

    .group-icon {
      width: 18px;
    }

    .group-name {
      font-size: 20px;
      font-weight: 400;
      color: #9D4F08;
      margin-left: 5px;
      line-height: 28px;
      /*text-shadow: 0px 2px 7px rgba(0, 0, 0, 0.1);*/
    }
  }

  .group-active {
    position: relative;
    background: #fff;

    .group-name {
      font-size: 22px;
      font-weight: 600;
      color: #EE7800;
      margin-left: 6px;
      line-height: 30px;
    }

    .group-triangle {
      width: 0;
      height: 0;
      border-bottom: 44px solid #fff;
      position: absolute;
      top: 0;
      z-index: 1;
    }

    .group-triangle-right {
      right: 0;
      transform: translateX(18px);
      border-right: 20px solid transparent;
    }

    .group-triangle-left {
      left: 0;
      transform: translateX(-18px);
      border-left: 20px solid transparent;
    }
  }
}
</style>
