<template>
  <div class="bp-wrapper">
    <div class="header">
      <h1 class="header-title">请填写血压信息</h1>
      <!--<div class="header-page">-->
        <!--<span class="header-page-now">{{ nowPage }}</span>-->
        <!--<span class="header-page-sum">/{{ sumPage }}</span>-->
      <!--</div>-->
    </div>
    <p class="tips">根据您填写的信息，我们会为您制定贴心计划。</p>
    <div class="content">
      <div class="group">
        <span class="group-name">血压</span>
        <van-field
          v-model="sbp"
          @click="inputClick"
          @focus="sbpFocus = true"
          @blur="inputBlur('sbp')"
          @input="inputWatch('sbp')"
          type="digit"
          input-align="center"
          placeholder="高压"
          :class="{'group-input-bp': true, 'group-input-focus': sbpFocus}"
        />
        <span>/</span>
        <van-field
          v-model="dbp"
          @click="inputClick"
          @focus="dbpFocus = true"
          @blur="inputBlur('dbp')"
          @input="inputWatch('dbp')"
          type="digit"
          input-align="center"
          placeholder="低压"
          :class="{'group-input-bp': true, 'group-input-focus': dbpFocus}"
        />
        <span class="group-unit">mmHg</span>
      </div>
      <!-- <div class="group">
        <span class="group-name">心率</span>
        <span class="group-sub">（选填）</span>
        <input
          type="number"
          v-model="pulse"
          @input="inputWatch('pulse')"
          class="group-input"
        >
        <span class="group-unit">次</span>
      </div> -->
    </div>
    <div class="footer">
      <van-button
        round
        type="info"
        @click="last"
        class="footer-btn footer-btn-last"
      >
        <span class="footer-btn-text">
          <van-icon name="arrow-left" class="footer-btn-arrow" />
          上一步
        </span>
      </van-button>
      <van-button
        v-if="!isLastPage"
        round
        type="info"
        @click="next"
        class="footer-btn"
      >
        <span class="footer-btn-text">
          下一步
          <van-icon name="arrow" class="footer-btn-arrow" />
        </span>
      </van-button>
      <van-button
        v-else
        round
        type="info"
        @click="finish"
        class="footer-btn"
      >
        <span class="footer-btn-text">
          完成
          <van-icon name="arrow" class="footer-btn-arrow" />
        </span>
      </van-button>
    </div>
  </div>
</template>

<script>
import { Toast } from 'vant'

export default {
  data: () => {
    return {
      sbp: '', // 收缩压
      dbp: '', // 舒张压
      pulse: '', // 心率
      limitVal: {
        'sbp': { max: 299, min: 0 },
        'dbp': { max: 255, min: 0 },
        'pulse': { max: 255, min: 0 }
      },
      sbpFocus: false,
      dbpFocus: false
    }
  },
  props: {
    nowPage: Number,
    sumPage: Number,
    sbpProp: String,
    dbpProp: String,
    pulseProp: String,
    isLastPage: Boolean
  },
  computed: {
    isFill() {
      let isOk = true
      let tips = '请填写'

      if (this.sbp === '') {
        isOk = false
        tips += '高压'
      } else if (this.dbp === '') {
        isOk = false
        tips += '低压'
      }

      if (!isOk) {
        Toast({
          message: tips,
          type: 'html',
          forbidClick: true,
          duration: 1000
        })
      }

      return isOk
    }
  },
  created() {
    this.sbp = this.sbpProp // 收缩压
    this.dbp = this.dbpProp // 舒张压
    this.pulse = this.pulseProp // 心率
  },
  methods: {
    /**
     * input点击事件
     * @param {MouseEvent} e 点击事件
     */
    inputClick(e) {
      e.stopPropagation()
      e.target.focus()
    },
    /**
     * input输入判断
     * @param {String} key 数据键
     */
    inputWatch(key) {
      let oVal = this[key]
      let limitVal = this.limitVal[key]

      if (oVal === '') return

      oVal = Number(oVal)
      if (oVal > limitVal.max) oVal = limitVal.max
console.log(oVal)
      this[key] = oVal
    },
    /**
     * input失焦判断
     * @param {String} key 数据键
     */
    inputBlur(key) {
      this[`${key}Focus`] = false
      let oVal = this[key]
      let limitVal = this.limitVal[key]

      if (oVal === '') return

      oVal = Number(oVal)
      if (oVal < limitVal.min) oVal = limitVal.min

      this[key] = oVal
    },
    /**
     * 上一题
     */
    last() {
      this.$emit('last-item-bp', {
        sbp: this.sbp,
        dbp: this.dbp,
        pulse: this.pulse
      })
    },
    /**
     * 下一题
     */
    next() {
      if (!this.isFill) return

      this.$emit('next-item-bp', {
        sbp: this.sbp,
        dbp: this.dbp,
        pulse: this.pulse
      })
    },
    /**
     * 完成
     */
    finish() {
      if (!this.isFill) return

      this.$emit('finish-bp', {
        sbp: this.sbp,
        dbp: this.dbp,
        pulse: this.pulse
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.bp-wrapper {
  width: 100%;

  .header {
    display: flex;
    align-items: baseline;
    justify-content: space-between;

    .header-title {
      color: #333;
      font-size: 20px;
      font-weight: 600;
    }

    .header-page {

      .header-page-now {
        color: #333;
        font-size: 30px;
      }

      .header-page-sum {
        color: #999;
        font-size: 16px;
      }
    }
  }

  .tips {
    color: #666;
    font-size: 18px;
    margin-top: 16px;
    text-align: left;
    font-weight: 400;
    line-height: 28px;
  }

  .content {
    padding: 100px 0;

    .group {
      display: flex;
      margin-top: 30px;
      align-items: center;
      justify-content: space-between;

      &:first-of-type {
        margin-top: 0;
      }

      .group-name {
        color: #333;
        font-size: 18px;
        font-weight: 400;

      }

      .group-sub {
        color: #999;
        font-size: 15px;
        font-weight: 400;
      }

      .group-input-bp,
      .group-input {
        padding: 0;
        width: 140px;
        height: 40px;
        color: #000;
        font-size: 20px;
        font-weight: 400;
        line-height: 40px;
        text-align: center;
        border-radius: 6px;
        border: 1px solid #ccc;
        -webkit-appearance: none;
      }

      .group-input-focus {
        border: 1px solid #EE7800;
      }

      .group-input-bp {
        width: 74px;
      }

      .group-unit {
        color: #333;
        font-size: 15px;
        font-weight: 400;
      }
    }
  }

  .footer {
    display: flex;
    margin-top: 50px;
    align-items: center;
    justify-content: space-between;

    .footer-btn {
      width: 125px;
      height: 42px;
      color: #fff;
      font-size: 20px;
      border: 1px solid #FF8100;
      background: linear-gradient(90deg,rgba(255,129,0,1) 0%,rgba(253,154,87,1) 100%);

      .footer-btn-text {
        height: 42px;
        display: flex;
        align-items: center;
        justify-content: center;

        .footer-btn-arrow {
          width: 8px;
          display: flex;
          margin-left: 6px;
          align-items: center;
          justify-content: center;
        }
      }
    }

    .footer-btn-last {
      color: #EE7800;
      background: #fff;
      border: 1px solid #EE7800;

      .footer-btn-text {

        .footer-btn-arrow {
          margin-left: 0;
          margin-right: 6px;
        }
      }
    }
  }
}
</style>
