<template>
  <div class="drug-wrapper">
    <div class="header">
      <h1 v-if="pageId === 1" class="header-title">您目前是否使用胰岛素？</h1>
      <h1 v-if="pageId === 2" class="header-title">您目前胰岛素的使用情况？</h1>
      <!--<div class="header-page">-->
        <!--<span class="header-page-now">{{ nowPage }}</span>-->
        <!--<span class="header-page-sum">/{{ sumPage }}</span>-->
      <!--</div>-->
    </div>
    <p class="tips">根据您填写的信息，我们会为您制定贴心计划。</p>
    <div v-if="pageId === 1" class="content">
      <div class="group">
        <span
          @click="selectDrug('drugId', 0)"
          :class="{'group-btn': true, 'group-btn-active': drugId === 0}"
        >没有使用</span>
      </div>
      <div class="group">
        <span
          @click="selectDrug('drugId', 1)"
          :class="{'group-btn': true, 'group-btn-active': drugId === 1}"
        >正在使用</span>
      </div>
    </div>
    <div v-if="pageId === 2" class="content">
      <div class="group">
        <span
          @click="selectDrug('doseId', 1)"
          :class="{'group-btn': true, 'group-btn-active': doseId === 1}"
        >每天注射2次及以下</span>
      </div>
      <div class="group">
        <span
          @click="selectDrug('doseId', 2)"
          :class="{'group-btn': true, 'group-btn-active': doseId === 2}"
        >每天注射3次及以上</span>
      </div>
    </div>
    <div class="footer">
      <van-button
        round
        type="info"
        @click="last"
        class="footer-btn footer-btn-last"
      >
        <span class="footer-btn-text">
          <van-icon name="arrow-left" class="footer-btn-arrow" />
          上一步
        </span>
      </van-button>
      <van-button
        v-if="drugId === 1 && pageId === 1"
        round
        type="info"
        @click="pageId = 2"
        class="footer-btn"
      >
        <span class="footer-btn-text">
          下一步
          <van-icon name="arrow" class="footer-btn-arrow" />
        </span>
      </van-button>
      <van-button
        v-else
        round
        type="info"
        @click="finish"
        class="footer-btn"
      >
        <span class="footer-btn-text">
          完成
          <van-icon name="arrow" class="footer-btn-arrow" />
        </span>
      </van-button>
    </div>
  </div>
</template>

<script>
import { Toast } from 'vant'

export default {
  data: () => {
    return {
      drugId: '', // 是否使用胰岛素 1-是 0-否
      doseId: '', // 胰岛素注射频率 1-每天小于等于2次 2-每天大于等于3次
      pageId: 1
    }
  },
  props: {
    nowPage: Number,
    sumPage: Number,
    drugIdProp: Number,
    doseIdProp: Number,
    isLastPage: Boolean
  },
  created() {
    this.drugId = this.drugIdProp
    this.doseId = this.doseIdProp
  },
  methods: {
    /**
     * 选择是否药物控糖
     * @param {Number} drugKey 选项key
     * @param {Number} drugId 选项Id
     */
    selectDrug(drugKey, drugId) {
      if (drugId === this[drugKey]) {
        this[drugKey] = ''
      } else {
        this[drugKey] = drugId
      }
    },
    /**
     * 上一题
     */
    last() {
      let pageId = this.pageId
      if (pageId === 1) {
        this.$emit('last-item-drug', {
          drugId: this.drugId,
          doseId: this.doseId
        })
      } else if (pageId === 2) {
        this.pageId = 1
      }
    },
    /**
     * 完成
     */
    finish() {
      let hasToast = false
      // 当第一页时，没有选择
      if (this.pageId === 1 && this.drugId === '') hasToast = true
      // 当第二页时，没有选择
      if (this.pageId === 2 && this.doseId === '') hasToast = true

      if (hasToast) {
        Toast({
          message: '请至少选填一项',
          type: 'html',
          forbidClick: true,
          duration: 1000
        })

        return
      }

      this.$emit('finish-drug', {
        drugId: this.drugId,
        doseId: this.doseId
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.drug-wrapper {
  width: 100%;

  .header {
    display: flex;
    align-items: baseline;
    justify-content: space-between;

    .header-title {
      color: #333;
      font-size: 20px;
      font-weight: 600;
    }

    .header-page {

      .header-page-now {
        color: #333;
        font-size: 30px;
      }

      .header-page-sum {
        color: #999;
        font-size: 16px;
      }
    }
  }

  .tips {
    color: #666;
    font-size: 18px;
    margin-top: 16px;
    text-align: left;
    font-weight: 400;
    line-height: 28px;
  }

  .content {
    margin-top: 50px;

    .group {
      display: flex;
      margin-top: 20px;

      .group-btn {
        width: 100%;
        color: #666;
        font-size: 20px;
        font-weight: 400;
        padding: 10px 12px;
        border-radius: 6px;
        box-sizing: border-box;
        border: 1px solid #CCC;
        background-color: #fff;
      }

      .group-btn-active {
        color: #EE7800;
        background-color: #FFECDE;
        border: 1px solid #EE7800;
      }
    }
  }

  .footer {
    display: flex;
    margin-top: 50px;
    align-items: center;
    justify-content: space-between;

    .footer-btn {
      width: 125px;
      height: 42px;
      color: #fff;
      font-size: 20px;
      border: 1px solid #FF8100;
      background: linear-gradient(90deg,rgba(255,129,0,1) 0%,rgba(253,154,87,1) 100%);

      .footer-btn-text {
        height: 42px;
        display: flex;
        align-items: center;
        justify-content: center;

        .footer-btn-arrow {
          width: 8px;
          display: flex;
          margin-left: 6px;
          align-items: center;
          justify-content: center;
        }
      }
    }

    .footer-btn-last {
      color: #EE7800;
      background: #fff;
      border: 1px solid #EE7800;

      .footer-btn-text {

        .footer-btn-arrow {
          margin-left: 0;
          margin-right: 6px;
        }
      }
    }
  }
}
</style>
