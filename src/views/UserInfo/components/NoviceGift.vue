<template>
  <div v-if="showGift" class="gift-wrapper">
    <header class="gift-header">
      <span class="gift-header-left">新手礼</span>
      <span class="gift-header-right" @click="goRule">详细规则<van-icon name="arrow" /></span>
    </header>
    <div class="gift-content" @click="goGift">
      <div class="gift-up">
        <div class="gift-up-left">
          <img src="../imgs/weishengzhi.png" />
        </div>
        <div class="gift-up-right">
          <p class="gift-up-right-p1">马卡龙色高颜值抽纸<br>纯竹工坊竹 蓝色独角兽抽纸巾</p>
          <p class="gift-up-right-p2">包邮</p>
        </div>
      </div>
      <p class="gift-p1" v-if="activityStatus === 3">恭喜你免费拿到手啦，现在去兑换吧</p>
      <p class="gift-p" v-else>已获得{{ ktj }}健康金，还剩<span class="gift-p-num">{{ price }}</span>健康金免费</p>
      <gift-progress :progressProp="giftProgress" :statusProp="activityStatus" />
      <div v-if="activityStatus === 3" class="gift-btn">兑换</div>
      <p class="gift-expire">{{ expiredDate }}</p>
    </div>
  </div>
</template>

<script>
import dayjs from "dayjs";
import { noviceActivityStatus, noviceActivitySave, noviceActivityInfo } from '@/api/UserInfo.js'
import giftProgress from "./giftProgress.vue"
export default {
  data: () => {
    return {
      sourceType: '',
      ktj: '',
      price: '',
      giftProgress: '0%',
      expiredDate: '',
      dateLine: '',
      expiredIntervalId: '',
      showGift: true,
      activityStatus: 0 // 0、无活动，1、进行中，2、已兑换，3、已完成，4、已过期，5、未开通
    }
  },
  components: {
    'gift-progress': giftProgress
  },
  created() {
    this.sourceType = this.$route.query.source_type
    // 初始化
    this.init()
  },
  methods: {
    /**
     * 初始化
     */
    async init() {
      // 新手礼包-状态
      const statusRes = await noviceActivityStatus()
      if (statusRes.status === 0) {
        // 0、无活动，1、进行中，2、已兑换，3、已完成，4、已过期，5、未开通
        this.activityStatus = statusRes.data.status
        // 如果 status 为0、2、4，隐藏新手礼包入口
        if (
          this.activityStatus === 0 ||
          this.activityStatus === 2 ||
          this.activityStatus === 4
        ) {
          this.showGift = false
          return
        }
      } else {
        this.$toast(statusRes.msg)
        return
      }

      // 如果 status 为5，调用添加一条记录接口
      if (this.activityStatus === 5) {
        // 新手礼包-添加一条记录
        noviceActivitySave().then(res => {
          if (res.status === 0) {
            // 添加成功，获取新手礼包-基础信息
            this.getNoviceActivityInfo()
          } else {
            this.$toast(res.msg)
          }
        })
      }

      // 如果 status 为1、3，调用基础信息接口
      if (this.activityStatus === 1 || this.activityStatus === 3) {
        // 获取新手礼包-基础信息
        this.getNoviceActivityInfo()
      }
    },
    /**
     * 获取新手礼包-基础信息
     */
    getNoviceActivityInfo() {
      // 新手礼包-基础信息
      noviceActivityInfo().then(res => {
        if (res.status === 0) {
          const resData = res.data
          this.ktj = resData.balance // 健康金余额
          this.price = resData.price - resData.balance // 活动所需健康金
          this.giftProgress = resData.progress.complete_rate + "%" // 新手礼任务进度
          this.dateLine = resData.progress.finish_time // 活动结束时间

          // 倒计时循环
          this.countdownInterval()
        } else {
          this.$toast(res.msg)
        }
      })
    },
    /**
     * 倒计时循环
     */
    countdownInterval() {
      this.expiredIntervalId = setInterval(() => {
        // 倒计时
        this.expiredDate = this.countdown();
      }, 1000);
    },
    /**
     * 倒计时
     */
    countdown() {
      const endDate = dayjs(this.dateLine);
      const _ms = endDate.diff(dayjs());
      if (_ms > 0) {
        const _dd = endDate.diff(dayjs(), "day");
        const _hh = endDate.diff(dayjs(), "hour");
        const _mm = endDate.diff(dayjs(), "minute");
        const _ss = endDate.diff(dayjs(), "second");

        // 转换
        const hh = _hh - _dd * 24;
        const mm = _mm - _hh * 60;
        const ss = _ss - _mm * 60;
        const ms = _ms - _ss * 1000;

        // 格式化
        const DD = ("00" + _dd).slice(-2);
        const HH = ("00" + hh).slice(-2);
        const MM = ("00" + mm).slice(-2);
        const SS = ("00" + ss).slice(-2);
        const MS = ("000" + ms).slice(-3);

        return `${_dd}天${HH}小时${MM}分${SS}秒后过期`;
      } else {
        // 清除倒计时循环
        clearInterval(expiredIntervalId);
        return `已过期`;
      }
    },
    /**
     * 跳转新手礼页
     */
    goGift() {
      this.$router.push({
        path: '/china_life_sugar/home/<USER>',
        query: {
          'source_type': this.sourceType
        }
      })
    },
    /**
     * 跳转详细规则
     */
    goRule() {
      this.$router.push({
        path: '/china_life_sugar/home/<USER>/rule',
        query: {
          'source_type': this.sourceType
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.gift-wrapper {
  width: 100%;
  border-radius: 9px;

  .gift-header {
    height: 34px;
    padding: 0 15px;
    background: #FFDBAA;
    border-top-left-radius: 9px;
    border-top-right-radius: 9px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .gift-header-left {
      font-size: 18px;
      font-weight: 600;
      color: #5D330E;
      line-height: 25px;
    }

    .gift-header-right {
      font-size: 15px;
      font-weight: 400;
      color: #5D330E;
      display: flex;
      align-items: center;
    }
  }

  .gift-content {
    padding: 18px 20px;
    background: #FFF4E2;
    border: 2px solid #FFDA9A;
    border-bottom-left-radius: 9px;
    border-bottom-right-radius: 9px;

    .gift-up {
      display: flex;
      align-items: center;
      justify-content: flex-start;

      .gift-up-left {
        width: 74px;
        height: 74px;
        border-radius: 8px;
        padding: 0 6px;
        box-sizing: border-box;
        background: #FFFFFF;
        border: 1px solid #FEC682;
        display: flex;
        align-items: center;

        img {
          width: 100%;
        }
      }

      .gift-up-right {
        text-align: left;
        margin-left: 18px;

        .gift-up-right-p1 {
          font-size: 15px;
          font-weight: 400;
          color: #5D330E;
          line-height: 21px;
        }

        .gift-up-right-p2 {
          font-size: 14px;
          font-weight: 400;
          color: #9A724C;
          line-height: 20px;
          margin-top: 14px;
        }
      }
    }

    .gift-p {
      font-size: 16px;
      font-weight: 500;
      color: #5D330E;
      line-height: 22px;
      margin-top: 18px;
      margin-bottom: 8px;

      .gift-p-num {
        font-size: 20px;
        color: #DF2D24;
      }
    }

    .gift-p1 {
      font-size: 17px;
      font-weight: 600;
      color: #333333;
      line-height: 24px;
      margin-top: 18px;
      margin-bottom: 8px;
    }

    .gift-btn {
      height: 48px;
      font-size: 20px;
      font-weight: 500;
      margin-top: 18px;
      color: #FFFFFF;
      line-height: 48px;
      border-radius: 22px;
      background-image: url("../imgs/exchange-bg.png");
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;
    }

    .gift-expire {
      font-size: 13px;
      font-weight: 600;
      color: #8F5806;
      line-height: 18px;
      margin-top: 14px;
    }
  }
}
</style>
