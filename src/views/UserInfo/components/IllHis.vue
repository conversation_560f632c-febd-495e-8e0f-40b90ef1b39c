<template>
  <div class="his-wrapper">
    <div class="header">
      <h1 class="header-title">你是否有过以下病史</h1>
      <!--<div class="header-page">-->
        <!--<span class="header-page-now">{{ nowPage }}</span>-->
        <!--<span class="header-page-sum">/{{ sumPage }}</span>-->
      <!--</div>-->
    </div>
    <p class="tips">根据您填写的信息，我们会为您制定贴心计划。</p>
    <div class="content">
      <div v-for="(hisArr, index) in hisList1" :key="index" class="group">
        <span
          v-for="item in hisArr"
          :key="item.id"
          @click="selectHis(item.id)"
          :class="{'group-btn': true, 'group-btn-half': true, 'group-btn-active': hisIdArr.indexOf(item.id) !== -1}"
        >{{ item.name }}</span>
      </div>
      <div v-for="item in hisList2" :key="item.id" class="group">
        <span
          @click="selectHis(item.id)"
          :class="{'group-btn': true, 'group-btn-active': hisIdArr.indexOf(item.id) !== -1}"
        >{{ item.name }}</span>
      </div>
    </div>
    <div class="footer">
      <van-button
        round
        type="info"
        @click="last"
        class="footer-btn footer-btn-last"
      >
        <span class="footer-btn-text">
          <van-icon name="arrow-left" class="footer-btn-arrow" />
          上一步
        </span>
      </van-button>
      <van-button
        v-if="!isLastPage"
        round
        type="info"
        @click="next"
        class="footer-btn"
      >
        <span class="footer-btn-text">
          下一步
          <van-icon name="arrow" class="footer-btn-arrow" />
        </span>
      </van-button>
      <van-button
        v-else
        round
        type="info"
        @click="finish"
        class="footer-btn"
      >
        <span class="footer-btn-text">
          完成
          <van-icon name="arrow" class="footer-btn-arrow" />
        </span>
      </van-button>
    </div>
  </div>
</template>

<script>
import { Toast } from 'vant'

export default {
  data: () => {
    return {
      hisList1: [
        [
          {
            name: '无',
            id: '0'
          },
          {
            name: '严重低血糖',
            id: '1'
          }
        ],
        [
          {
            name: '心梗史',
            id: '2'
          },
          {
            name: '冠心病史',
            id: '3'
          }
        ],
        [
          {
            name: '脑卒中',
            id: '4'
          },
          {
            name: '肾病',
            id: '5'
          }
        ]
      ],
      hisList2: [
        {
          name: '眼底增值性病变并激光治疗',
          id: '6'
        },
        {
          name: '记忆力明显减退或者认知功能障碍',
          id: '7'
        }
      ],
      hisIdArr: [] // 病史 0-无 1-严重低血糖 2-心梗史 3-冠心病史 4-脑卒中 5-肾病 6-记忆力明显减退或者认知功能障碍 7-眼底增值性病变
    }
  },
  props: {
    nowPage: Number,
    sumPage: Number,
    illHisProp: {
      type: String,
      default: ''
    },
    isLastPage: Boolean
  },
  created() {
    this.hisIdArr = this.illHisProp.split(',')
  },
  methods: {
    /**
     * 选择病史
     * @param {Number} hisId 病史id
     */
    selectHis(hisId) {
      let hisIndex = this.hisIdArr.indexOf(hisId)
      // 如果点击 无-0
      if (hisId === '0') {
        if (hisIndex === -1) {
          this.hisIdArr = [hisId]
        } else {
          this.hisIdArr = []
        }
      } else {
        if (this.hisIdArr.indexOf('0') !== -1) this.hisIdArr = []

        if (hisIndex === -1) {
          this.hisIdArr = [hisId, ...this.hisIdArr]
        } else {
          this.hisIdArr.splice(hisIndex, 1)
        }
      }
    },
    /**
     * 检查数据
     */
    checkData() {
      if (this.hisIdArr.length === 0) {
        Toast({
          message: '请至少选填一项',
          type: 'html',
          forbidClick: true,
          duration: 1000
        })

        return false
      } else {
        return true
      }
    },
    /**
     * 下一题
     */
    last() {
      if (!this.checkData()) return

      this.$emit('last-item-his', this.hisIdArr.join(','))
    },
    /**
     * 下一题
     */
    next() {
      if (!this.checkData()) return

      this.$emit('next-item-his', this.hisIdArr.join(','))
    },
    /**
     * 完成
     */
    finish() {
      if (!this.checkData()) return

      this.$emit('finish-his', this.hisIdArr.join(','))
    }
  }
}
</script>

<style lang="scss" scoped>
.his-wrapper {
  width: 100%;

  .header {
    display: flex;
    align-items: baseline;
    justify-content: space-between;

    .header-title {
      color: #333;
      font-size: 20px;
      font-weight: 600;
    }

    .header-page {

      .header-page-now {
        color: #333;
        font-size: 30px;
      }

      .header-page-sum {
        color: #999;
        font-size: 16px;
      }
    }
  }

  .tips {
    color: #666;
    font-size: 18px;
    margin-top: 16px;
    text-align: left;
    font-weight: 400;
    line-height: 28px;
  }

  .content {

    .group {
      display: flex;
      margin-top: 20px;
      align-items: center;
      justify-content: space-between;

      .group-btn {
        width: 100%;
        font-size: 20px;
        font-weight: 400;
        color: #EE7800;
        line-height: 28px;
        padding: 10px 12px;
        border-radius: 6px;
        box-sizing: border-box;
        border:1px solid #EE7800;
      }

      .group-btn-half {
        width: 130px;
      }

      .group-btn-active {
        background: #FFECDE;
      }
    }
  }

  .footer {
    display: flex;
    margin-top: 50px;
    align-items: center;
    justify-content: space-between;

    .footer-btn {
      width: 125px;
      height: 42px;
      color: #fff;
      font-size: 20px;
      border: 1px solid #FF8100;
      background: linear-gradient(90deg,rgba(255,129,0,1) 0%,rgba(253,154,87,1) 100%);

      .footer-btn-text {
        height: 42px;
        display: flex;
        align-items: center;
        justify-content: center;

        .footer-btn-arrow {
          width: 8px;
          display: flex;
          margin-left: 6px;
          align-items: center;
          justify-content: center;
        }
      }
    }

    .footer-btn-last {
      color: #EE7800;
      background: #fff;
      border: 1px solid #EE7800;

      .footer-btn-text {

        .footer-btn-arrow {
          margin-left: 0;
          margin-right: 6px;
        }
      }
    }
  }
}
</style>
