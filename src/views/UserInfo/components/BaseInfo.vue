<template>
  <div class="base-wrapper">
    <div class="header">
      <h1 class="header-title">基本信息</h1>
      <!--<div class="header-page">-->
        <!--<span class="header-page-now">{{ nowPage }}</span>-->
        <!--<span class="header-page-sum">/{{ sumPage }}</span>-->
      <!--</div>-->
    </div>
    <p class="tips">根据您填写的信息，我们会为您制定贴心计划。</p>
    <div class="content">
      <div class="group">
        <span class="group-name">身高</span>
        <van-field
          v-model="height"
          @click="inputClick"
          @focus="heightFocus = true"
          @blur="inputBlur('height')"
          @input="inputWatch('height')"
          type="number"
          inputmode="decimal"
          input-align="center"
          :class="{'group-input': true, 'group-input-focus': heightFocus}"
        />
        <span class="group-unit">cm</span>
      </div>
      <div class="group">
        <span class="group-name">体重</span>
        <van-field
          v-model="weight"
          @click="inputClick"
          @focus="weightFocus = true"
          @blur="inputBlur('weight')"
          @input="inputWatch('weight')"
          type="number"
          inputmode="decimal"
          input-align="center"
          :class="{'group-input': true, 'group-input-focus': weightFocus}"
        />
        <span class="group-unit">kg</span>
      </div>
    </div>
    <div :class="nowPage==0?'footer footer-left':'footer'">
      <van-button
        round
        v-if="nowPage!=0"
        type="info"
        @click="last"
        class="footer-btn footer-btn-last"
      >
        <span class="footer-btn-text">
          <van-icon name="arrow-left" class="footer-btn-arrow" />
          上一步
        </span>
      </van-button>
      <van-button
        round
        type="info"
        @click="next"
        class="footer-btn"
      >
        <span class="footer-btn-text">
          下一步
          <van-icon name="arrow" class="footer-btn-arrow" />
        </span>
      </van-button>
    </div>
  </div>
</template>

<script>
import { Toast } from 'vant'

export default {
  data: () => {
    return {
      height: '',
      weight: '',
      heightFocus: false,
      weightFocus: false,
      limitVal: {
        'height': { max: 299, min: 11 },
        'weight': { max: 255, min: 11 }
      }
    }
  },
  props: {
    step: String,
    nowPage: Number,
    sumPage: Number,
    heightProp: String,
    weightProp: String
  },
  created() {
    this.height = this.heightProp // 身高
    this.weight = this.weightProp // 体重
  },
  methods: {
    /**
     * input点击事件
     * @param {MouseEvent} e 点击事件
     */
    inputClick(e) {
      e.stopPropagation()
      e.target.focus()
    },
    /**
     * input输入判断
     * @param {String} key 数据键
     */
    inputWatch(key) {
      let oVal = this[key]
      let limitVal = this.limitVal[key]
      const pointIndex = oVal.indexOf('.')

      if (pointIndex > 0) {
        let start = oVal.slice(0, pointIndex).replace(/\D+/g, '')
        let after = oVal.slice(pointIndex + 1).replace(/\D+/g, '')
        after = after.slice(0, 1)
        oVal = start + '.' + after
        if (start >= limitVal.max) oVal = limitVal.max
      } else {
        oVal = oVal.replace(/\D+/g, '')
        if (oVal > limitVal.max) oVal = limitVal.max
      }

      this[key] = oVal
    },
    /**
     * input失焦判断
     * @param {String} key 数据键
     */
    inputBlur(key) {
      this[`${key}Focus`] = false
      let oVal = this[key]
      let limitVal = this.limitVal[key]

      if (oVal === '') return

      oVal = Number(oVal)
      if (oVal < limitVal.min) oVal = limitVal.min

      this[key] = oVal
    },
    /**
     * 上一题
     */
    last() {
      this.$emit('last-item-base', this.height, this.weight)
    },
    /**
     * 下一题
     */
    next() {
      if (this.height === '') {
        Toast({
          message: '请填写身高',
          type: 'html',
          forbidClick: true,
          duration: 1000
        })

        return
      }

      if (this.weight === '') {
        Toast({
          message: '请填写体重',
          type: 'html',
          forbidClick: true,
          duration: 1000
        })

        return
      }

      this.$emit('next-item-base', this.height, this.weight)
    }
  }
}
</script>

<style lang="scss" scoped>
.base-wrapper {
  width: 100%;

  .header {
    display: flex;
    align-items: baseline;
    justify-content: space-between;

    .header-title {
      color: #333;
      font-size: 20px;
      font-weight: 600;
    }

    .header-page {

      .header-page-now {
        color: #333;
        font-size: 30px;
      }

      .header-page-sum {
        color: #999;
        font-size: 16px;
      }
    }
  }

  .tips {
    color: #666;
    font-size: 18px;
    margin-top: 16px;
    text-align: left;
    font-weight: 400;
    line-height: 28px;
  }

  .content {
    padding: 100px 0;

    .group {
      display: flex;
      margin-top: 30px;
      align-items: center;
      justify-content: space-between;

      &:first-of-type {
        margin-top: 0;
      }

      .group-name {
        color: #333;
        font-size: 18px;
        font-weight: 400;
      }

      .group-input {
        width: 187px;
        height: 40px;
        padding: 5px;
        color: #000;
        font-size: 25px;
        font-weight: 400;
        border-radius: 6px;
        border: 1px solid #ccc;
        -webkit-appearance: none;
      }

      .group-input-focus {
        border: 1px solid #EE7800;
      }

      .group-unit {
        color: #333;
        font-size: 15px;
        font-weight: 400;
      }
    }
  }

  .footer {
    display: flex;
    margin-top: 50px;
    align-items: center;
    justify-content: space-between;

    .footer-btn {
      width: 125px;
      height: 42px;
      color: #fff;
      font-size: 20px;
      border: 1px solid #FF8100;
      background: linear-gradient(90deg,rgba(255,129,0,1) 0%,rgba(253,154,87,1) 100%);

      .footer-btn-text {
        height: 42px;
        display: flex;
        align-items: center;
        justify-content: center;

        .footer-btn-arrow {
          width: 8px;
          display: flex;
          margin-left: 6px;
          align-items: center;
          justify-content: center;
        }
      }
    }

    .footer-btn-last {
      color: #EE7800;
      background: #fff;
      border: 1px solid #EE7800;

      .footer-btn-text {

        .footer-btn-arrow {
          margin-left: 0;
          margin-right: 6px;
        }
      }
    }
  }
  .footer-left{
    justify-content: flex-end;
  }
}
</style>
