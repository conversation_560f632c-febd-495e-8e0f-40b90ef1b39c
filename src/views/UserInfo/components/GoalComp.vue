<template>
  <div class="comp-wrapper">
    <div class="top">
      <div class="top-left">
        <span class="top-left-name">{{ userData.name }}</span>
        <span class="top-left-unit">({{ userData.unit }})</span>
      </div>
      <div class="top-right" v-if="userData.idealType != 2">
        <span class="top-right-name">当前：</span>
        <span v-if="userData.value" class="top-right-value">{{ userData.value }}</span>
        <span v-else class="top-right-novalue">未填</span>
      </div>
    </div>
    <div v-if="userData.hasIdeal" class="bottom">
      <div class="bottom-progress">
        <div v-if="userData.idealType === 1" class="progress-box">
          <div class="progress-abnormal progress-low">
            低
            <span
              v-if="userData.value && (idealStatus === 1 || idealStatus === 2)"
              :style="progressStyle"
              class="progress-value"
            ></span>
          </div>
          <div class="progress-normal">
            正常
            <span
              v-if="userData.value && idealStatus === 3"
              :style="progressStyle"
              class="progress-value"
            ></span>
            <span class="progress-ideal progress-ideal-min">{{ userData.min }}</span>
            <span class="progress-ideal progress-ideal-max">{{ userData.max }}</span>
          </div>
          <div class="progress-abnormal progress-high">
            高
            <span
              v-if="userData.value && (idealStatus === 4 || idealStatus === 5)"
              :style="progressStyle"
              class="progress-value"
            ></span>
          </div>
        </div>
        <!-- BMI -->
        <div v-if="userData.idealType === 3" class="progress-box">
          <div class="progress-abnormal progress-low">
            偏瘦
            <span
              v-if="userData.value && (bmiIdealStatus === 2 || bmiIdealStatus === 1)"
              :style="progressStyleBmi"
              class="progress-value"
            ></span>
          </div>
          <div class="progress-normal-left">
            正常
            <span
              v-if="userData.value && bmiIdealStatus === 3"
              :style="progressStyleBmi"
              class="progress-value"
            ></span>
            <span class="progress-ideal progress-ideal-min">{{ userData.min }}</span>
            <span class="progress-ideal progress-ideal-center">{{ userData.center }}</span>
          </div>
          <div class="progress-normal-right">
            超重
            <span
              v-if="userData.value && bmiIdealStatus === 4"
              :style="progressStyleBmi"
              class="progress-value"
            ></span>
            <span class="progress-ideal progress-ideal-max">{{ userData.max }}</span>
          </div>
          <div class="progress-abnormal progress-high">
            肥胖
            <span
              v-if="userData.value && (bmiIdealStatus === 5 ||  bmiIdealStatus === 6)"
              :style="progressStyleBmi"
              class="progress-value"
            ></span>
          </div>
        </div>
        <!-- 糖化血红蛋白 -->
        <div v-if="userData.idealType === 2" class="progress-box progress-box2">
          <div class="left">
            {{ userData.value == '' ? '未填' : '当前：' + userData.value }}
          </div>
          <div class="right">
            理想：小于{{ userData.max }}
          </div>
          <!-- <div class="progress-normal">
            <span
              v-if="userData.value && idealStatus === 2"
              :style="progressStyle"
              class="progress-value"
            >{{ userData.value }}</span>
            <span class="progress-ideal progress-ideal-max">理想：小于{{ userData.max }}</span>
          </div> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data: () => {
    return {
      idealStatus: '', // 1-低；2-理想；3-高
      bmiIdealStatus: '', // 1-低；2-理想；3-胖 4肥胖
      progressStyle: '' // 理想值位置样式
    }
  },
  props: {
    userData: Object,
    chooseType: Number
  },
  created() {
    let userData = this.userData
    console.log(this.chooseType)
    // hasIdeal：1-有理想值；0-无理想值
    if (userData.hasIdeal === 1) {
      let value = Number(userData.value) // 当前值
      let max = Number(userData.max) // 最大理想值
      let min = Number(userData.min) // 最小理想值
      let center = Number(userData.center) // bmi偏胖
      let up = Number(userData.up) // 最大极限值
      let down = Number(userData.down) // 最小极限值
      // 如果当前值 大于 最大理想值，idealStatus = 3
      // 如果当前值 小于 最小理想值，idealStatus = 1
      // 如果当前值在理想值范围内，idealStatus = 2
      if (userData.idealType == 1) {
        if (value >= up) this.idealStatus = 5
        else if (value < up && value >= max) this.idealStatus = 4
        else if (value < max && value >= min) this.idealStatus = 3
        else if (value < min && value >= down) this.idealStatus = 2
        else this.idealStatus = 1

        let leftVal = ''
        let rightVal = ''
        if (this.idealStatus === 1) {
          this.progressStyle = 'left: 0;transform: translate(-50%, -42%);'
        } else if (this.idealStatus === 2) {
          leftVal = down
          rightVal = min
          this.progressStyle = `left: ${((value - leftVal) / (rightVal - leftVal)) * 100}%;transform: translate(-50%, -42%);`
        } else if (this.idealStatus === 3) {
          leftVal = min
          rightVal = max
          this.progressStyle = `left: ${((value - leftVal) / (rightVal - leftVal)) * 100}%;transform: translate(-50%, -42%);`
        } else if (this.idealStatus === 4) {
          leftVal = max
          rightVal = up
          this.progressStyle = `left: ${((value - leftVal) / (rightVal - leftVal)) * 100}%;transform: translate(-50%, -42%);`
        } else if (this.idealStatus === 5) {
          this.progressStyle = 'left:98%;transform: translate(-50%, -42%);'
        }
      } else if (userData.idealType == 3) {
        // bmi单独处理
        if (value >= up) this.bmiIdealStatus = 6
        else if (value < up && value >= max) this.bmiIdealStatus = 5
        else if (value < max && value >= center) this.bmiIdealStatus = 4
        else if (value < center && value >= min) this.bmiIdealStatus = 3
        else if (value < min && value >= down) this.bmiIdealStatus = 2
        else this.bmiIdealStatus = 1
        // bmi单独处理
        let leftBmi = ''
        let rightBmi = ''
        if (this.bmiIdealStatus === 1) {
          this.progressStyleBmi = 'left: 0;transform: translate(-50%, -42%);'
        } else if (this.bmiIdealStatus === 2) {
          leftBmi = down
          rightBmi = min
          this.progressStyleBmi = `left: ${((value - leftBmi) / (rightBmi - leftBmi)) * 100}%;transform: translate(-50%, -42%);`
        } else if (this.bmiIdealStatus === 3) {
          leftBmi = min
          rightBmi = center
          this.progressStyleBmi = `left: ${((value - leftBmi) / (rightBmi - leftBmi)) * 100}%;transform: translate(-50%, -42%);`
        } else if (this.bmiIdealStatus === 4) {
          leftBmi = center
          rightBmi = max
          this.progressStyleBmi = `left: ${((value - leftBmi) / (rightBmi - leftBmi)) * 100}%;transform: translate(-50%, -42%);`
        } else if (this.bmiIdealStatus === 5) {
          leftBmi = max
          rightBmi = up
          this.progressStyleBmi = `left: ${((value - leftBmi) / (rightBmi - leftBmi)) * 100}%;transform: translate(-50%, -42%);`
        } else if (this.bmiIdealStatus === 6) {
          this.progressStyleBmi = 'left:98%;transform: translate(-50%, -42%);'
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.comp-wrapper {
  padding: 0 24px;
  border-radius: 8px;
  border: 1px solid #E8E8E8;

  .top {
    height: 60px;
    line-height: 60px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .top-left {

      .top-left-name {
        color: #333;
        font-size: 20px;
        font-weight: 600;
      }

      .top-left-unit {
        color: #666;
        font-size: 15px;
        font-weight: 400;
        margin-left: 4px;
      }
    }

    .top-right {
      font-size: 18px;
      font-weight: 400;
      color: rgba(102,102,102,1);
    }
  }

  .bottom {
    padding: 5px 0 10px;
    border-top: 1px solid #E8E8E8;
    .top-right-novalue{
      color: #666;
      font-size: 17px;
      font-weight: 400;
    }
    .bottom-ideal {
      color: #666;
      font-size: 18px;
      font-weight: 400;
      color:rgba(51,51,51,1);
      text-align: left;
    }

    .bottom-progress {
      margin-top: 10px;
      padding: 10px 0 25px;
      .progress-box {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        .progress-abnormal {
          flex: 1;
          height: 21px;
          line-height: 21px;
          font-size:16px;
          font-weight:400;
          color:rgba(255,255,255,1);
          position: relative;
          background:rgba(121,177,234,1);
          border-radius:11px 0px 0px 11px;
          .progress-value {
            width: 21px;
            height: 25px;
            display: block;
            background: url('~@/assets/images/biaochi.png') no-repeat;
            background-size: 21px 25px;
            position: absolute;
            top: 0;
            z-index: 1;
          }
        }
        .progress-normal-left{
          flex: 1;
          height: 21px;
          line-height: 21px;
          font-size:16px;
          font-weight:400;
          position: relative;
          background:#62D26F;
          color:rgba(255,255,255,1);
          .progress-ideal {
            color: #666;
            font-size: 15px;
            font-weight: 400;
            position: absolute;
            top: 30px;
          }
          .progress-ideal-min {
            left: 0;
            transform: translateX(-50%);
          }
          .progress-ideal-center {
            right: 0;
            transform: translateX(50%);
          }
        }
        .progress-normal-right{
          flex: 1;
          height: 21px;
          line-height: 21px;
          font-size:16px;
          font-weight:400;
          position: relative;
          background:#FFB44C;
          color:rgba(255,255,255,1);
          .progress-ideal {
            color: #666;
            font-size: 15px;
            font-weight: 400;
            position: absolute;
            top: 30px;
          }
          .progress-ideal-max {
            right: 0;
            transform: translateX(50%);
          }
        }
        .progress-low {
          background:rgba(121,177,234,1);
          border-radius:11px 0px 0px 11px;
        }
        .progress-high {
          background:rgba(255,149,133,1);
          border-radius:0px 11px 11px 0px;
        }
        .progress-normal {
          width: 50%;
          height: 21px;
          line-height: 21px;
          font-size:16px;
          font-weight:400;
          color:rgba(255,255,255,1);
          position: relative;
          background:rgba(98,210,111,1);

          .progress-ideal {
            color: #666;
            font-size: 15px;
            font-weight: 400;
            position: absolute;
            top: 30px;
          }

          .progress-ideal-min {
            left: 0;
            transform: translateX(-50%);
          }
          .progress-ideal-center {
            right: 0;
            transform: translateX(50%);
          }
          .progress-ideal-max {
            right: 0;
            transform: translateX(50%);
          }
        }

        .progress-value {
          width: 21px;
          height: 25px;
          display: block;
          background: url('~@/assets/images/biaochi.png') no-repeat;
          background-size: 21px 25px;
          position: absolute;
          top: 0;
          z-index: 1;
        }
      }

      .progress-box2 {
        width: 100%;
        height: 82px;
        background: url('~@/assets/images/color-line.png') no-repeat;
        background-size: cover;
        margin-top: -20px;
        .left{
          width: 50%;
          height: 82px;
          line-height: 82px;
          float: left;
          font-size:18px;
          font-weight:400;
          color:rgba(51,51,51,1);
        }
        .right{
          width: 50%;
          height: 82px;
          line-height: 82px;
          float: left;
          font-size:18px;
          font-weight:400;
          color:rgba(0,0,0,1);
        }
      }
    }
  }
}

</style>
