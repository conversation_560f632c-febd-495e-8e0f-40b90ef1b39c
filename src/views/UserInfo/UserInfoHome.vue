<template>
  <div class="wrapper">
    <!-- <div class="top" @click="openUrl()">
      <img src="@/views/UserInfo/imgs/<EMAIL>" alt="">
      <span class="tip"> 轻松管理您的血糖</span>
      <span class="btn">立即打开</span>
    </div> -->
    <div class="header">
      <div class="header-top">
        <div class="header-top-left">
          <img src="@/views/UserInfo/imgs/<EMAIL>">
        </div>
        <div class="header-top-right">
          <div @click="dialogGo" class="header-top-say">
            <img src="@/views/UserInfo/imgs/<EMAIL>">
            <div class="header-top-box">
              <p class="header-top-text">{{ headerTips }}</p>
              <p class="header-top-text">{{ headerTips }}</p>

            </div>
          </div>
          <div class="header-top-btn">
            <span
              @click="goStock"
              class="header-top-go"
            >
              有问题，去专家知识库
              <van-icon name="arrow" class="header-top-icon"/>
            </span>
          </div>
        </div>
      </div>
      <div class="header-bottom">
        <div class="header-bottom-tab">
          <tab-list
            v-if="tabList.length"
            :selectTabId="selectTabId"
            :tabList="tabList"
            @changeTabId="changeTabId"
          />
        </div>
        <div @click="goManage" class="header-bottom-content">
          <div v-for="item in tabContentList" :key="item.id">
            <div
              v-if="item.id === selectTabId"
              class="header-bottom-content-group"
            >
              <p class="group-title">{{ item.name }}<span class="group-unit">{{ item.unit }}</span></p>
              <div class="group-values">
                <div
                  v-for="(val, index) in item.values"
                  :key="index"
                  class="group-values-box"
                >
                  <span v-if="val.subtitle" class="group-values-subtitle">{{ val.subtitle }}</span>
                  <span class="group-values-goal">{{ val.goal }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 新手礼 -->
    <novice-gift v-if="showNoviceGift" class="novice-gift"/>
    <div class="content">
      <div class="phase">
        <p class="phase-title">
          <span class="phase-title-name">健康任务</span>
          <span class="phase-title-date">{{ phaseData.peroid.name }} {{ phaseData.peroid.date }}</span>
        </p>
        <p v-if="phaseData.mission_status" class="phase-tips">
          <van-icon name="info" class="phase-tips-icon"/>
          <span class="phase-tips-text">你今日已完成{{ phaseData.mission_status.done_jobs }}个任务，还有{{ phaseData.mission_status.undone_jobs }}个任务未完成</span>
        </p>
        <div v-if="phaseImg" class="phase-img">
          <img :src="phaseImg" class="phase-bg">
          <div class="phase-info">
            <div @click="goIntegral" class="phase-info-left">
              <img src="@/views/UserInfo/imgs/<EMAIL>" class="phase-info-left-img">
              <span class="phase-info-left-balance">{{ phaseData.balance }}</span>
            </div>
            <div class="phase-info-right">
              <img
                @click="whereToGo"
                src="@/views/UserInfo/imgs/btn_lottery.png"
                class="phase-info-right-lottery"
              >
              <img
                @click="goLeaderboard"
                src="@/views/UserInfo/imgs/btn_ranking_list.png"
                class="phase-info-right-leaderboard"
              >
            </div>
          </div>
        </div>
      </div>
      <!-- TODO 直播 2.9.0这期不用 -->
      <!-- <div class="live">
        <live-comp
          v-for="item in liveList"
          :key="item.id"
          @openMask="whereToGo"
          :liveData="item"
        />
      </div> -->
      <!-- 任务列表 -->
      <div class="task">
        <task-comp
          v-for="item in taskList"
          :key="item.id"
          @openMask="whereToGo"
          :taskData="item"
          :sourceType="sourceType"
        />
      </div>
    </div>
    <div class="kefu" v-if="showKeFuImg" @click="goKeFu">
      <img src="./imgs/kefu-icon.png">
    </div>
    <!-- 联系客服二维码 -->
    <van-overlay :show="showKeFu">
      <div class="kefu-wrapper" v-if="showKeFu">
        <div :class="{'kefu-box': true, 'kefu-hide': hideKeFu}">
          <img src="./imgs/kefu-tanchuang1.png" class="kefu-bg">
          <img
            src="./imgs/kefu-close.png"
            @click="kefuHide"
            class="kefu-close"
          >
          <img src="https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/guoshou/erwema.png" class="kefu-QR">
          <span v-if="kefulink == 0" class="reback" @click="linkKeFu">让客服主动联系我</span>
          <span v-else class="reback">客服会主动添加您微信，请留意</span>
        </div>
      </div>
    </van-overlay>
    <!-- 打开浏览器提示层 -->
    <van-overlay :show="showBrowser" @click="showBrowser = false">
      <div class="browser-wrapper">
        <img src="./imgs/openBrowser.png">
      </div>
    </van-overlay>
  </div>
</template>

<script>
  import TabList from './components/TabList.vue'
  import TaskComp from './components/TaskComp.vue'
  import LiveComp from './components/LiveComp.vue'
  import NoviceGift from './components/NoviceGift.vue'
  import {
    userIndexTarget,
    missionProgress,
    missionOperationProgress,
    nationalLifeLogin,
    userProgress,
    nationalUserStore,
    userBasicCheck
  } from '@/api/UserInfo.js'
  import {getBanFontSize} from '@/utils/utils.js'

  export default {
    data: () => {
      return {
        uid: '', // 用户id
        sourceType: '', // 1-小程序 2-管家app 3-国寿 4-爱选 5-控糖管家
        authorization: '', // 用户token
        reportId: '', // 周报id
        goType: '', // 跳转目的地 0-不跳转 1-周报 2-跳转预约门诊 3-调取授权 4-跳转测量页面 5-跳转推荐文章列表 6-跳转饮食打卡 7-跳转亲情绑定 8-滑动到底部
        appInfo: {
          zz_app_id: '',
          zz_app_name: '',
          zz_app_version: ''
        },
        kefulink: 0, // 点击客服联系我 0-没点 1-已点
        tabList: [],
        tabContentList: [],
        selectTabId: 1,
        headerTips: '',
        phaseData: {
          balance: '',
          mission_status: '',
          peroid: {
            date: '',
            index: '',
            name: '',
            weeks: []
          }
        },
        liveList: [],
        taskList: [],
        showNoviceGift: false, // 显示新手礼模块
        showBrowser: false, // 打开浏览器提示层
        showKeFu: false, // 联系客服二维码模块显示flag
        showKeFuImg: true, // 联系客服的点击按钮显示隐藏
        hideKeFu: false, // 联系客服二维码消失动画flag
        phaseMaps: [
          [
            require('@/views/UserInfo/imgs/maps/map1-1.png'),
            require('@/views/UserInfo/imgs/maps/map1-2.png'),
            require('@/views/UserInfo/imgs/maps/map1-3.png'),
            require('@/views/UserInfo/imgs/maps/map1-4.png'),
            require('@/views/UserInfo/imgs/maps/map1-5.png'),
            require('@/views/UserInfo/imgs/maps/map1-6.png'),
            require('@/views/UserInfo/imgs/maps/map1-7.png')
          ],
          [
            require('@/views/UserInfo/imgs/maps/map2-1.png'),
            require('@/views/UserInfo/imgs/maps/map2-2.png'),
            require('@/views/UserInfo/imgs/maps/map2-3.png'),
            require('@/views/UserInfo/imgs/maps/map2-4.png')
          ],
          [
            require('@/views/UserInfo/imgs/maps/map3-1.png'),
            require('@/views/UserInfo/imgs/maps/map3-2.png'),
            require('@/views/UserInfo/imgs/maps/map3-3.png'),
            require('@/views/UserInfo/imgs/maps/map3-4.png')
          ],
          [
            require('@/views/UserInfo/imgs/maps/map4-1.png'),
            require('@/views/UserInfo/imgs/maps/map4-2.png'),
            require('@/views/UserInfo/imgs/maps/map4-3.png'),
            require('@/views/UserInfo/imgs/maps/map4-4.png'),
            require('@/views/UserInfo/imgs/maps/map4-5.png')
          ]
        ],
        phaseImg: ''
      }
    },
    components: {
      'tab-list': TabList,
      'task-comp': TaskComp,
      'live-comp': LiveComp,
      'novice-gift': NoviceGift,
    },
    created() {
      let routeQuery = this.$route.query
      this.uid = routeQuery.uid || ''
      this.sourceType = routeQuery.source_type || ''
      this.$router.replace({
        path: '/china_life_sugar/open_serve/myself',
        query: {
          uid: this.uid,
          source_type: this.sourceType
        }
      })
      return

      // 判断是否安卓系统微信页面修改了字体
      getBanFontSize()


      if (this.sourceType === '4') {
        this.showKeFuImg = false
        this.showKeFu = false
      } else {
        this.showKeFu = false
        this.showKeFuImg = true
      }
      // 国寿版本 跳转下载；如果在微信环境，则不跳转
      this.goDownload()
      // // 如果客服二维码已经显示， 则重加载时隐藏
      // if (this.showKeFuImg === false) {
      //   this.showKeFu = false
      // } else {
      //   // if (sessionStorage.getItem('kefu') === '1') {
      //   //   this.showKeFu = false
      //   // } else {
      //   //   this.showKeFu = true
      //   // }
      // }
      // 初始化
      this.init()
    },
    methods: {
      openUrl() {
        const UA = navigator.userAgent
        const isAndroid = UA.indexOf('Android') > -1 || UA.indexOf('Adr') > -1 // android终端
        const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端

        // 原生app方法名称：jumpToReport
        if (isAndroid) {
          this.openApp(
            'https://a.app.qq.com/o/simple.jsp?pkgname=com.zhizhong.mmcassistant',
            'guanjia://jp.app/openwith',
            '',
            (flag) => {
              if (!flag) {
                console.log('唤起超时') // 你的唤起失败操作
              }
            })
        } else if (isIOS) {
          this.openApp(
            'https://itunes.apple.com/us/app/mmc管家/id1198430389?l=zh&amp;ls=1&amp;mt=8',
            'https://itunes.apple.com/us/app/mmc管家/id1198430389?l=zh&amp;ls=1&amp;mt=8',
            '',
            (flag) => {
              if (!flag) {
                console.log('唤起超时') // 你的唤起失败操作
              }
            })
        } else {
          this.$toast('打开失败')
        }
      },
      openApp(openUrl, appUrl, action, callback) {
        // 检查app是否打开
        function checkOpen(cb) {
          let _clickTime = +(new Date())
          let flag = false

          function check(elsTime) {
            if (elsTime > 3000 || document.hidden || document.webkitHidden) {
              flag = true
              cb(flag)
            } else {
              cb(flag)
            }
          }

          // 启动间隔20ms运行的定时器，并检测累计消耗时间是否超过3000ms，超过则结束
          let _count = 0
          let intHandle = setInterval(function () {
            _count++
            var elsTime = +(new Date()) - _clickTime
            if (_count >= 100 || elsTime > 3000) {
              clearInterval(intHandle)
              check(elsTime)
            }
          }, 20)
        }

        // 在iframe 中打开APP
        var ifr = document.createElement('a')
        ifr.href = appUrl
        ifr.style.display = 'none'
        let event = document.createEvent('MouseEvents')
        event.initEvent('click', true, true)
        ifr.dispatchEvent(event)
        window.location.href = openUrl
        if (callback) {
          checkOpen(function (opened) {
            callback && callback(opened)
          })
        }
        document.body.appendChild(ifr)
        setTimeout(function () {
          document.body.removeChild(ifr)
        }, 2000)
      },
      /**
       * 初始化
       */
      async init() {
        let that = this
        // 国寿版本
        if (this.sourceType === '3' || this.sourceType === '4') {
          // 设置appInfo
          if (this.sourceType === '3') localStorage.setItem('appInfo', '{"zz_app_id": 150001, "zz_app_name": "national_guanjia", "zz_app_version": "2.9.0"}')
          if (this.sourceType === '4') localStorage.setItem('appInfo', '{"zz_app_id": 190001, "zz_app_name": "aixuan_guanjia", "zz_app_version": "2.9.9"}')

          if (this.uid != '') {
            // 用户uid换token
            let loginRes = await nationalLifeLogin(this.uid)
            if (loginRes.status === 0) {
              localStorage.setItem('authorization', loginRes.data.auth.access_token)
              this.showNoviceGift = true

              that.$router.replace({
                path: '/user/info/home',
                query: {
                  source_type: this.sourceType
                }
              })
            } else {
              this.$toast(loginRes.msg)
              return
            }
          } else {
            this.showNoviceGift = true
          }
        }

        this.kefulink = localStorage.getItem('kefulink') || 0
        this.authorization = localStorage.getItem('authorization')
        this.appInfo = JSON.parse(localStorage.getItem('appInfo'))
        let finishCollect = localStorage.getItem('finishCollect') || 0
        if (finishCollect == 1) this.showKeFu = false

        // 1代表已填写没有开始履约，2代表已填写已开始履约，3代表已填写需要重新填写。
        this.getUserBasicCheck().then((flg) => {
          if (flg == 3) {
            that.$router.replace({
              path: '/user/info/collect',
              query: {
                source_type: this.sourceType,
                uid: this.uid
              }
            })
            return
          }
        })

        // 获取用户理想目标
        this.getUserIndexTarget()
        // 获取阶段、积分
        this.getUserProgress()
        // 获取任务列表
        this.getMissionProgress()
        // 获取任务列表-运营任务（直播）
        // this.getMissionOperationProgress()
      },
      async getUserBasicCheck() {
        // 用户是否收集过信息接口
        let checkRes = await userBasicCheck()
        if (checkRes.status === 0) {
          return checkRes.data.flag;
        } else {
          this.$toast(checkRes.msg)
          return
        }
      },
      /**
       * 获取用户理想目标
       */
      getUserIndexTarget() {
        userIndexTarget().then(res => {
          if (res.status === 0) {
            let data = res.data
            this.headerTips = data.tips
            let dataArr = [
              {
                hasData: data.has_bg,
                tabList: {
                  id: 1,
                  name: '控糖', icon: require('@/views/UserInfo/imgs/<EMAIL>'),
                  activeIcon: require('@/views/UserInfo/imgs/<EMAIL>')
                },
                tabContentList: {
                  id: 1,
                  name: '你的理想血糖目标',
                  unit: 'mmol/L',
                  values: [
                    {
                      subtitle: '空腹',
                      goal: data.fast_bg_target
                    },
                    {
                      subtitle: '餐后',
                      goal: data.postprandial_bg_target
                    }
                  ]
                }
              },
              {
                hasData: data.has_bp,
                tabList: {
                  id: 2,
                  name: '控压',
                  icon: require('@/views/UserInfo/imgs/<EMAIL>'),
                  activeIcon: require('@/views/UserInfo/imgs/<EMAIL>')
                },
                tabContentList: {
                  id: 2,
                  name: '你的理想血压目标',
                  unit: 'mmHg',
                  values: [
                    {
                      subtitle: '收缩压',
                      goal: data.sbp_target
                    },
                    {
                      subtitle: '舒张压',
                      goal: data.dbp_target
                    }
                  ]
                }
              },
              {
                hasData: data.has_weight,
                tabList: {
                  id: 3,
                  name: '减重',
                  icon: require('@/views/UserInfo/imgs/<EMAIL>'),
                  activeIcon: require('@/views/UserInfo/imgs/<EMAIL>')
                },
                tabContentList: {
                  id: 3,
                  name: '你的理想体质指数(BMI)目标',
                  unit: 'kg/m²',
                  values: [
                    {
                      subtitle: '',
                      goal: data.bmi_target
                    }
                  ]
                }
              }, {
                hasData: data.has_health,
                tabList: {
                  id: 4,
                  name: data.has_health_ext,
                  icon: require('@/views/UserInfo/imgs/<EMAIL>'),
                  activeIcon: require('@/views/UserInfo/imgs/<EMAIL>')
                },
                tabContentList: {
                  id: 4,
                  name: '你的理想体质指数(BMI)目标',
                  unit: 'kg/m²',
                  values: [
                    {
                      subtitle: '',
                      goal: data.bmi_target
                    }
                  ]
                }
              },
              {
                hasData: data.has_pre_bg,
                tabList: {
                  id: 5,
                  name: '控糖', icon: require('@/views/UserInfo/imgs/<EMAIL>'),
                  activeIcon: require('@/views/UserInfo/imgs/<EMAIL>')
                },
                tabContentList: {
                  id: 5,
                  name: '你的理想血糖目标',
                  unit: 'mmol/L',
                  values: [
                    {
                      subtitle: '空腹',
                      goal: data.fast_bg_target
                    },
                    {
                      subtitle: '餐后',
                      goal: data.postprandial_bg_target
                    }
                  ]
                }
              }
            ]
            this.goType = data.type
            this.reportId = data.tips_jump_id

            dataArr.forEach(item => {
              if (item.hasData === 1) {
                this.tabList.push(item.tabList)
                this.tabContentList.push(item.tabContentList)
              }
            })
            if (this.tabList.length > 0) {
              this.selectTabId = this.tabList[0].id
            }

          } else {
            // 用户还未收集过信息
            if (res.status === 202007) {
              this.$router.replace({
                path: '/user/info/kefu',
                query: {
                  source_type: this.sourceType,
                  uid: this.uid
                }
              })
            } else {
              this.$toast(res.msg)
            }
          }
        })
      },
      /**
       * 获取任务列表
       */
      getMissionProgress() {
        missionProgress().then(res => {
          if (res.status === 0) {
            this.taskList = res.data
          } else {
            this.$toast(res.msg)
          }
        })
      },
      /**
       * 获取任务列表-运营任务（直播）
       */
      getMissionOperationProgress() {
        missionOperationProgress().then(res => {
          if (res.status === 0) {
            this.liveList = res.data
          } else {
            this.$toast(res.msg)
          }
        })
      },
      /**
       * 获取阶段、积分
       */
      getUserProgress() {
        userProgress().then(res => {
          if (res.status === 0) {
            this.phaseData = res.data
            this.phaseData.peroid.weeks.forEach((item, index) => {
              if (item.is_current === 1) {
                this.phaseImg = this.phaseMaps[this.phaseData.peroid.index][index]
              }
            })
          } else {
            this.$toast(res.msg)
          }
        })
      },
      /**
       * 改变tabId
       * @param {Number} tabId tabId
       */
      changeTabId(tabId) {
        this.selectTabId = tabId
      },
      /**
       * 去专家知识库
       */
      goStock() {
        console.log('去专家知识库')
        // 跳转
        this.whereToGo()
      },
      /**
       * 显示遮罩提示
       */
      whereToGo() {
        // 小程序和国寿-显示遮罩层
        if (this.sourceType === '1' || this.sourceType === '3' || this.sourceType === '4') {
          this.showBrowser = true
        }
      },
      /**
       * 跳转下载
       */
      goDownload() {
        // 如果在微信环境，则不跳转
        const ua = window.navigator.userAgent.toLowerCase()
        if (/micromessenger/i.test(ua)) return false

        window.location.href = 'https://static.zz-med.com/app/download2.html?type=' + this.sourceType
      },
      /**
       * 跳转健康金
       */
      goIntegral() {
        window.location.href = this.baseUrl + `/static/guanjia/prizes/Integral_description.html`
      },
      /**
       * 跳转排行榜
       */
      goLeaderboard() {
        window.location.href = this.baseUrl + `/static/guanjia/prizes/rank-list.html?authorization=${this.authorization}&appId=${this.appInfo.zz_app_id}&appName=${this.appInfo.zz_app_name}&appVersion=${this.appInfo.zz_app_version}`
      },
      /**
       * 跳转目标管理页
       */
      goManage() {
        this.$router.push({
          path: '/user/info/goal',
          query: {
            source_type: this.sourceType
          }
        })
      },
      /**
       * 点击头部对话框
       */
      dialogGo() {
        switch (this.goType) {
          case 1:
            // 跳转周报页
            this.goWeekReport()
            break
          case 5:
          case 6:
          case 7:
            // 显示前往app的提示浮层
            this.showBrowser = true
            break
          case 8:
            // 滑动到底部
            this.goBottom()
            break
          default:
            console.log('暂不跳转')
        }
      },
      /**
       * 跳转周报页
       */
      goWeekReport() {
        if (!this.reportId) return

        this.$router.push({
          path: '/week/report',
          query: {
            id: this.reportId,
            source_type: this.sourceType
          }
        })
      },
      /**
       * 滑动到底部
       */
      goBottom() {
        window.scrollTo(0, document.documentElement.clientHeight);
      },
      /**
       * 客服二维码弹窗消失
       */
      kefuHide() {
        this.hideKeFu = true
        sessionStorage.setItem('kefu', 1)
        setTimeout(() => {
          this.showKeFu = false
          // 消失动画flag清除
          this.hideKeFu = false
        }, 300)
      },
      /**
       * 让客服主动联系我
       */
      linkKeFu() {
        // 已经点击
        if (this.kefulink == 1) return

        nationalUserStore({uid: this.uid}).then(res => {
          if (res.status === 0) {

            // 客服二维码弹窗消失
            this.kefuHide()

            localStorage.setItem('kefulink', 1)
            this.kefulink = 1

            this.showToast = true
            setTimeout(() => {
              this.showToast = false
            }, 1200)
          } else {
            this.$toast(res.msg)
            localStorage.setItem('kefulink', 0)
            this.kefulink = 0
          }
        }).catch(err => {
          this.$toast(err)
          localStorage.setItem('kefulink', 0)
          this.kefulink = 0
        })
      },
      /**
       * 跳转客服链接
       */
      goKeFu() {
        window.location.href = 'http://kefu.zz-med.com/api/mobileweb/home?channel_id=10517&channel_key=1051700fo&wechatapp_id=199431&key=34388sx4x'
      }
    }
  }
</script>

<style lang="scss" scoped>
  .wrapper {
    width: 100%;
    overflow: hidden;
    background: #F5F6FA;

    .top {
      display: inline-block;
      height: 30px;
      margin: 15px 20px;
      width: 100%;
      img {
        width: 35px;
        height: 35px;
        float: left;
      }
      .tip {
        margin-left: 10px;
        font-size: 17px;
        font-weight: 500;
        color: #333333;
        line-height: 43px;
        float: left;
      }
      .btn {
        float: right;
        margin-top: 8px;
        background: linear-gradient(270deg, #FF8100 0%, #FD9A57 100%);
        border-radius: 14px;
        font-size: 13px;
        width: 80px;
        margin-right: 40px;
        height: 28px;
        font-weight: 500;
        color: #FFFFFF;
        line-height: 28px;
      }
    }

    .header {
      padding: 20px 15px 0;
      box-sizing: border-box;
      background: url('~@/views/UserInfo/imgs/<EMAIL>') no-repeat;
      background-size: 100% 276px;

      .header-top {
        display: flex;
        margin: 0 auto;
        width: fit-content;
        justify-content: flex-start;

        .header-top-left {
          width: 88px;
          font-size: 0;

          img {
            width: 100%;
          }
        }

        .header-top-right {

          .header-top-say {
            width: 282px;
            font-size: 0;
            position: relative;
            margin-left: -30px;

            img {
              width: 100%;
            }

            .header-top-box {
              width: 224px;
              height: 48px;
              overflow: hidden;
              position: absolute;
              top: 13px;
              right: 13px;

              .header-top-text {
                color: #000;
                font-size: 18px;
                text-align: left;
                font-weight: 400;
                line-height: 26px;
                animation: textScroll 5s linear 0s infinite;
                -o-animation: textScroll 5s linear 0s infinite;
                -webkit-animation: textScroll 5s linear 0s infinite;
              }

              @keyframes textScroll {
                0% {
                  transform: translate(0, 0);
                  -o-transform: translate(0, 0);
                  -webkit-transform: translate(0, 0);
                }

                100% {
                  transform: translate(0, -100%);
                  -o-transform: translate(0, -100%);
                  -webkit-transform: translate(0, -100%);
                }
              }

              @-webkit-keyframes textScroll {
                0% {
                  transform: translate(0, 0);
                  -o-transform: translate(0, 0);
                  -webkit-transform: translate(0, 0);
                }

                100% {
                  transform: translate(0, -100%);
                  -o-transform: translate(0, -100%);
                  -webkit-transform: translate(0, -100%);
                }
              }
            }
          }

          .header-top-btn {
            height: 30px;
            margin-top: 13px;
            line-height: 19px;

            .header-top-go {
              color: #FFF;
              font-size: 15px;
              font-weight: 400;
              padding: 5px 10px;
              border-radius: 16px;
              border: 1px solid #FFF;

              .header-top-icon {
                font-size: 10px;
                margin-left: 2px;
                vertical-align: baseline;
              }
            }
          }
        }
      }

      .header-bottom {

        .header-bottom-tab {
          height: 44px;
        }

        .header-bottom-content {
          height: 86px;
          margin-top: -0.5px;
          box-sizing: border-box;
          padding: 12px 30px 20px;
          background-color: #fff;
          border-radius: 0 0 5px 5px;
          /*box-shadow: 0px 2px 7px 0px rgba(0, 0, 0, 0.1);*/

          .header-bottom-content-group {
            text-align: left;

            .group-title {
              color: #333;
              font-size: 16px;
              font-weight: 400;
              line-height: 22px;

              .group-unit {
                font-size: 12px;
                color: #9B9B9B;
                font-weight: 400;
                margin-left: 5px;
                line-height: 17px;
                /*text-shadow: 0px 2px 7px rgba(0, 0, 0, 0.1);*/
              }
            }

            .group-values {
              display: flex;
              margin-top: 10px;
              align-items: center;
              justify-content: space-between;

              .group-values-box {
                display: flex;
                align-items: center;
                justify-content: flex-start;

                .group-values-subtitle {
                  padding: 0 4px;
                  font-size: 14px;
                  font-weight: 400;
                  color: #9B9B9B;
                  line-height: 20px;
                  margin-right: 7px;
                  border-radius: 2px;
                  border: 1px solid #CCC;
                  /*text-shadow: 0px 2px 7px rgba(0, 0, 0, 0.1);*/
                  /*box-shadow: 0px 2px 7px 0px rgba(0, 0, 0, 0.1);*/
                }

                .group-values-goal {
                  color: #666;
                  font-size: 22px;
                  font-weight: 500;
                  /*text-shadow: 0px 2px 7px rgba(0, 0, 0, 0.1);*/
                }
              }
            }
          }
        }
      }
    }

    .novice-gift {
      width: 345px;
      margin: 14px auto 0;
    }

    .content {
      margin-top: 20px;
      padding-top: 15px;
      background: #FFF;
      border-radius: 9px 9px 0 0;

      .phase {

        .phase-title {
          color: #333;
          font-size: 20px;
          text-align: left;
          font-weight: 400;

          .phase-title-name {
            margin-left: 31px;
          }

          .phase-title-date {
            font-size: 16px;
            font-weight: 400;
            margin-left: 21px;
          }
        }

        .phase-tips {
          width: 100%;
          height: 33px;
          font-size: 15px;
          color: #DC8535;
          margin-top: 11px;
          font-weight: 400;
          line-height: 33px;
          background: #FFFBF1;

          .phase-tips-text,
          .phase-tips-icon {
            vertical-align: middle;
          }

          .phase-tips-text {
            margin-left: 5px;
          }
        }

        .phase-img {
          line-height: 0;
          overflow: hidden;
          border-radius: 9px;
          position: relative;
          margin: 15px 15px 0;

          .phase-bg {
            width: 100%;
          }

          .phase-info {
            width: 100%;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: absolute;
            top: 0;
            left: 0;

            .phase-info-left {
              background: #FFF;
              padding: 7px 17px 7px 11px;
              border-radius: 0 19px 19px 0;
              box-shadow: 0px 2px 4px 0px rgba(238, 120, 0, 0.2);

              .phase-info-left-img {
                width: 24px;
                vertical-align: middle;
              }

              .phase-info-left-balance {
                font-size: 20px;
                font-weight: 600;
                color: #9D4F08;
                margin-left: 5px;
                vertical-align: middle;
              }
            }

            .phase-info-right {

              .phase-info-right-lottery {
                width: 40px;
              }

              .phase-info-right-leaderboard {
                width: 47px;
                margin-left: 20px;
                margin-right: 12px;
              }
            }
          }
        }
      }

      .task {
        padding: 6px 15px 0;
      }
    }

    .kefu {
      line-height: 0;
      border-radius: 50%;
      position: fixed;
      right: 10px;
      bottom: 50px;

      img {
        width: 55px;
      }
    }

    .kefu-wrapper {
      width: 100%;
      height: 100%;

      .kefu-box {
        width: 335px;
        line-height: 0;
        overflow: hidden;
        transform-origin: 100% 100%;
        transform: translate(50%, 50%);
        position: fixed;
        bottom: 50%;
        right: 50%;

        .kefu-bg {
          width: 100%;
        }

        .kefu-close {
          width: 28px;
          position: absolute;
          top: 10px;
          right: 17px;
        }

        .kefu-QR {
          width: 181px;
          transform: translateX(-50%);
          position: absolute;
          bottom: 53px;
          left: 50%;
        }

        .reback {
          width: fit-content;
          position: absolute;
          left: 50%;
          transform: translateX(-50%);
          bottom: 30px;
          font-size: 16px;
          text-decoration: underline;
          color: #0c91e5;
        }
      }

      .kefu-hide {
        animation: kefuHide .5s ease;
        animation-fill-mode: forwards;

        @keyframes kefuHide {
          0% {
            opacity: 1;
            right: 50%;
            bottom: 50%;
            border-radius: 0;
            transform: scale(1) translate(50%, 50%);
          }

          100% {
            opacity: 0;
            right: 5%;
            bottom: 10%;
            border-radius: 50%;
            transform: scale(0) translate(50%, 50%);
          }
        }
      }
    }

    .browser-wrapper {
      padding-left: 20px;

      img {
        width: 100%;
      }
    }
  }
</style>
