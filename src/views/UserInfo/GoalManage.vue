<template>
  <div class="wrapeper">
    <div class="header" >
      <img v-if="sourceType==7 && closeTipStatus" @click="closeTip" class="close_tip" src="./imgs/<EMAIL>"></img>
      <div :class="sourceType==7?'header-top':'header-top header-top-two'">
        <span class="go_back" v-if="sourceType==7" @click="goBack"> <van-icon name="arrow-left"/>回到健康助手</span>
        <span
          @click="adjustGoal"
          class="header-top-btn"
        >
          重新调整管理方向
          <van-icon name="arrow" class="header-top-icon"/>
        </span>
      </div>
      <div class="header-card">
        <div class="header-card-top">
          <img src="./imgs/profeesor.png" class="header-card-top-img"/>
          <p class="header-card-top-text">根据您填写的信息，我们为您制定了管理计划。</p>
        </div>
        <div class="header-card-bottom">
          <div class="header-card-bottom-tab" v-if="has_health==1">
            <img src="./imgs/xuetang-icon.png" class="card-bottom-tab-icon">
            <span class="card-bottom-tab-name">{{ has_health_ext }}</span>
          </div>
          <div class="header-card-bottom-tab" v-if="has_bg==1 ||has_pre_bg==1">
            <img src="./imgs/xuetang-icon.png" class="card-bottom-tab-icon">
            <span class="card-bottom-tab-name">控糖</span>
          </div>
          <div class="header-card-bottom-tab" v-if="has_bp==1">
            <img src="./imgs/xueya-icon.png" class="card-bottom-tab-icon">
            <span class="card-bottom-tab-name">控压</span>
          </div>
          <div class="header-card-bottom-tab" v-if="has_weight==1">
            <img src="./imgs/tizhong-icon.png" class="card-bottom-tab-icon">
            <span class="card-bottom-tab-name">减重</span>
          </div>
        </div>
      </div>
    </div>
    <div v-if="sourceType==7 && readyStatus === 1" class="report_detail_status" @click="detailStatus = !detailStatus">
      {{ detailStatus ? '收起' : '显示' }}我的详细健康数据<b v-if="detailStatus == true">
      <van-icon name="arrow-down"/>
    </b>
      <b v-if="detailStatus == false">
        <van-icon name="arrow-up"/>
      </b>
    </div>
    <div v-if="(sourceType==7 && readyStatus === 0) || (sourceType!=7)" style="height: 10px"></div>
    <div
      v-if="(detailStatus &&(sourceType==7 && readyStatus === 1)) || (sourceType!=7) || (sourceType==7&& readyStatus === 0)">
      <div class="content">
        <p class="range">{{ disease_tag_ext }}</p>
        <p class="title">综合管理目标</p>
        <div
          v-for="(item, index) in childData"
          :key="index"
          class="group"
        >
          <goal-comp :userData="item" :chooseType="questionType"/>
        </div>
      </div>
      <p class="footer-tips" v-if="copy_words_origin!=''">
        <span>以上控制目标出自</span>
        <span>{{ copy_words_origin }}</span>
        <i v-if="isOpen == false">{{ copyWords }}</i>
        <b v-if="isOpen == true" @click="isOpen = !isOpen">点击查看更多
          <van-icon name="arrow-down"/>
        </b>
        <b v-if="isOpen == false" @click="isOpen = !isOpen">点击收起详情
          <van-icon name="arrow-up"/>
        </b>
      </p>
    </div>
    <div class="daily_task" v-if="sourceType==7 && readyStatus === 1">
      <dailyTask :index_info="index_info" :task_info="task_info"></dailyTask>
    </div>
    <div v-if="readyStatus === 0" class="footer-btn">

      <van-button v-if="sourceType==7" @click="readyBack" class="footer-btn-ready">生成我的个性化管理方案</van-button>
      <van-button v-else @click="readyGO" class="footer-btn-ready">开始管理计划</van-button>
    </div>
    <div v-if="readyStatus === 1 && hasStart === 0 &&( sourceType != 6 && sourceType != 7) " class="footer-date">
      <img src="./imgs/footer-goal.png" class="footer-date-img"/>
      <span class="footer-date-tips">{{ tips }}</span>
    </div>
  </div>
</template>

<script>
import {Dialog} from 'vant'
import GoalComp from './components/GoalComp.vue'
import dailyTask from './components/dailyTask.vue'
import {userTargetGet, userReadyData, userBasicCheck, dailyTask as dailyTaskApi} from '@/api/UserInfo.js'
import {getBanFontSize} from '@/utils/utils.js'
import wx from 'weixin-js-sdk'

export default {
  data: () => {
    return {
      disease_tag_ext: '',
      closeTipStatus: false,
      sourceType: '', // 1-小程序 2-管家app 3-国寿
      goalList: [],
      tips: '', // 底部提示
      copyWords: '', // 底部话术
      isAdjust: '', // 是否重新调整管理方向 0-不能 1-能调整
      hasStart: '', // 是否已开始 1-已开始 0-未开始
      readyStatus: '', // 是否已准备好 1-已准备好 0-未准备好
      isOpen: true,
      questionType: '',
      childData: [],
      index_info: {},
      task_info: {},
      detailStatus: false
    }
  },
  components: {
    'dailyTask': dailyTask,
    'goal-comp': GoalComp
  },
  created() {
    let that = this
    // 判断是否安卓系统微信页面修改了字体
    getBanFontSize()
    this.sourceType = this.$route.query.source_type || ''
    // 获取目标管理数据
    this.getUserTarget()
    if (this.sourceType != 6) {
      // 1代表已填写没有开始履约，2代表已填写已开始履约，3代表已填写需要重新填写。
      this.getUserBasicCheck().then((flg) => {
        if (flg == 3) {
          that.$router.replace({
            path: '/user/info/collect',
            query: {
              source_type: this.sourceType
            }
          })
          return
        }
      })
    }
    if (this.sourceType == 7) {
      // 首页index接口，不需要登录
      dailyTaskApi().then(function (res) {
        if (res.status != 0) {

        } else {
          that.index_info = res.data
          that.task_info = res.data.health_task_info
        }
      })
    }
  },
  methods: {
    async getUserBasicCheck() {
      // 用户是否收集过信息接口
      let checkRes = await userBasicCheck()
      if (checkRes.status === 0) {
        return checkRes.data.flag;
      } else {
        this.$toast(checkRes.msg)
        return
      }
    },
    /**
     * 获取目标管理数据
     */
    getUserTarget() {
      userTargetGet().then(res => {
        if (res.status === 0) {
          console.log(res, 'data')
          // 数据处理
          this.handleData(res.data)
        } else {
          this.$toast(res.msg)
        }
      })
    },
    /**
     * 数据处理
     * @param {Object} data 数据
     */
    handleData(data) {
      this.tips = data.tip
      this.copyWords = data.copy_words
      this.disease_tag_ext = data.disease_tag_ext
      this.has_bg = data.has_bg
      this.has_bp = data.has_bp
      this.has_pre_bg = data.has_pre_bg
      this.copy_words_origin = data.copy_words_origin
      this.has_weight = data.has_weight
      this.isAdjust = data.is_adjust
      this.readyStatus = data.ready_status

      this.hasStart = data.has_start
      this.questionType = data.disease_tag

      this.has_health = data.has_health
      this.has_health_ext = data.has_health_ext
      let arrType = {
        0: [1, 2, 3],
        1: [1, 2, 3, 4, 5, 10],
        2: [1, 2, 3, 4, 5, 12, 13, 10],
        3: [1, 2, 3, 4, 5, 10],
        4: [1, 2, 3, 4, 5, 12, 13, 10],
        5: [1, 2, 3, 8, 9],
        6: [1, 2, 3, 4, 5, 10],
        7: [1, 2, 3, 4, 5, 12, 13, 10],
        8: [1, 2, 3, 6, 7, 11],
        9: [1, 2, 3, 6, 7, 8, 9, 11],
        10: [1, 2, 3, 6, 7, 11],
        11: [1, 2, 3, 6, 7, 11],
        12: [1, 2, 3, 6, 7, 8, 9, 11],
        13: [1, 2, 3, 6, 7, 8, 9, 11],
        14: [1, 2, 3, 8, 9],
        15: [1, 2, 3, 8, 9],
        16: [1, 2, 3],
        17: [1, 2, 3]
      }
      let chooseArr = arrType[this.questionType]
      // console.log(chooseArr)
      this.goalList = [
        {
          name: '身高',
          unit: 'cm',
          value: String(data.height),
          hasIdeal: 0,
          id: 1
        },
        {
          name: '体重',
          unit: 'kg',
          value: String(data.weight || ''),
          max: String(data.weight_target).split('~')[1],
          min: String(data.weight_target).split('~')[0],
          up: Number(String(data.weight_target).split('~')[1]) + 25,
          down: '0',
          target: String(data.weight_target || ''),
          hasIdeal: 1,
          idealType: 1,
          id: 2
        },
        {
          name: 'BMI',
          unit: 'kg/m',
          value: String(data.bmi || ''),
          target: String(data.bmi_target || ''),
          max: '28',
          min: '18.5',
          center: '23.9',
          up: '35',
          down: '5',
          hasIdeal: 1,
          idealType: 3,
          id: 3
        },
        {
          name: '空腹血糖',
          unit: 'mmol/L',
          value: String(data.fast_bg || ''),
          max: '7.0',
          min: '4.4',
          up: '33.3',
          target: String(data.fast_bg_target || ''),
          down: '1.1',
          hasIdeal: 1,
          idealType: 1,
          id: 4
        },
        {
          name: '非空腹血糖',
          unit: 'mmol/L',
          value: String(data.postprandial_bg || ''),
          max: '10.0',
          min: '4.4',
          up: '33.3',
          target: String(data.postprandial_bg_target || ''),
          down: '1.1',
          hasIdeal: 1,
          idealType: 1,
          id: 5
        },
        {
          name: '空腹血糖',
          unit: 'mmol/L',
          value: String(data.fast_bg || ''),
          max: '6.1',
          min: '3.9',
          up: '33.3',
          target: String(data.fast_bg_target || ''),
          down: '1.1',
          hasIdeal: 1,
          idealType: 1,
          id: 6
        },
        {
          name: '非空腹血糖',
          unit: 'mmol/L',
          value: String(data.postprandial_bg || ''),
          max: '7.8',
          min: '3.9',
          up: '33.3',
          target: String(data.postprandial_bg_target || ''),
          down: '1.1',
          hasIdeal: 1,
          idealType: 1,
          id: 7
        },
        {
          name: '收缩压',
          unit: 'mmHg',
          value: String(data.sbp || ''),
          max: '135',
          min: '90',
          up: '255',
          target: String(data.sbp_target || ''),
          down: '0',
          hasIdeal: 1,
          idealType: 1,
          id: 8
        },
        {
          name: '舒张压',
          unit: 'mmHg',
          value: String(data.dbp || ''),
          max: '85',
          min: '60',
          up: '299',
          down: '0',
          target: String(data.dbp_target || ''),
          hasIdeal: 1,
          idealType: 1,
          id: 9
        },
        {
          name: '糖化血红蛋白',
          unit: '%',
          value: String(data.hba1c || ''),
          max: '7.0',
          min: '0',
          target: String(data.hba1c_target || ''),
          up: '78.2',
          down: '0',
          hasIdeal: 1,
          idealType: 2,
          id: 10
        },
        {
          name: '糖化血红蛋白',
          unit: '%',
          value: String(data.hba1c || ''),
          max: '5.7',
          min: '0',
          target: String(data.hba1c_target || ''),
          up: '78.2',
          down: '0',
          hasIdeal: 1,
          idealType: 2,
          id: 11
        },
        {
          name: '收缩压',
          unit: 'mmHg',
          value: String(data.sbp || ''),
          max: '130',
          min: '90',
          up: '255',
          target: String(data.sbp_target || ''),
          down: '0',
          hasIdeal: 1,
          idealType: 1,
          id: 12
        },
        {
          name: '舒张压',
          unit: 'mmHg',
          value: String(data.dbp || ''),
          max: '80',
          min: '60',
          up: '299',
          down: '0',
          target: String(data.dbp_target || ''),
          hasIdeal: 1,
          idealType: 1,
          id: 13
        },
      ]
      let that = this
      this.goalList.forEach((v, k) => {
        if (chooseArr.includes(v.id)) {
          that.childData.push(v)
        }
      })
    },

    /**
     * 目标调整提示
     */
    adjustGoal() {
      if (this.isAdjust === 1) {
        Dialog.confirm({
          messageAlign: 'left',
          title: '确定调整管理方向？',
          message: '确认后您将会重新填写基础信息，我们再次计算新的管理计划，新计划会在下周执行。',
        }).then(() => {
          this.$router.push({
            path: '/user/info/collect?from_type=1',
            query: {
              source_type: this.sourceType
            }
          })
        }).catch(() => {
          // on cancel
          console.log('取消调整')
        })
      } else {
        Dialog.alert({
          messageAlign: 'left',
          title: '确定调整管理方向？',
          message: '您本周已经调整过您的管理目标，不宜频繁调整哦~',
        }).then(() => {
          // on confirm
          console.log('不宜频繁调整哦~')
        })
      }
    },
    goBack() {
      this.closeTipStatus = true
    },
    closeTip() {
      this.closeTipStatus = false
    },
    /**
     * 我准备好了
     */
    async readyBack() {
      // 防重点击
      this.noDoubleTap(() => {
        userReadyData(1).then(res => {
          if (res.status === 0) {
            this.readyStatus = 1
          } else {
            this.$toast(res.msg)
          }
        })
      })
    },
    /**
     * 我准备好了
     */
    async readyGO() {
      let that = this
      // 防重点击
      this.noDoubleTap(() => {
        userReadyData(1).then(res => {
          if (res.status === 0) {
            if (that.sourceType == 6 || that.sourceType == 7) {
              wx.miniProgram.redirectTo({
                url: '/pages/home/<USER>/home/<USER>'
              })
            }
            that.readyStatus = 1
            // 已开始-跳转首页
            if (that.hasStart === 1) {
              if (that.sourceType == 5) {
                wx.miniProgram.redirectTo({
                  url: '/pages/home/<USER>/home/<USER>'
                })
              } else {
                this.jumpHome()
              }
            }
          } else {
            this.$toast(res.msg)
          }
        })
      })
    },
    /**
     * 跳转首页
     */
    jumpHome() {
      // 管家app进入,跳转到管家app首页
      if (this.sourceType === '2') {
        // 前往管家app首页
        this.goGuanJia()
      } else if (this.sourceType === '5') {
        // 前往管家app首页
        this.noDoubleTap(() => {
          wx.miniProgram.navigateTo({
            url: '/pages/home/<USER>/home/<USER>'
          })
        })
      } else {
        // 跳转H5首页
        this.$router.push({
          path: '/user/info/home',
          query: {
            source_type: this.sourceType
          }
        })
      }
    },
    /**
     * 前往管家app首页
     */
    goGuanJia() {
      const UA = navigator.userAgent
      const isAndroid = UA.indexOf('Android') > -1 || UA.indexOf('Adr') > -1 // android终端
      const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
      // 原生app方法名称：jumpAppOpenHealthmanager
      if (isAndroid) {
        console.log('安卓')
        window.android.jumpAppOpenHealthmanager()
      } else if (isIOS) {
        console.log('苹果')
        window.webkit.messageHandlers.jumpAppOpenHealthmanager.postMessage('')
      } else {
        this.$toast('跳转失败')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.wrapeper {
  width: 100%;
  overflow: hidden;
  padding-top: 15px;
  padding-bottom: 60px;
  box-sizing: border-box;
  background: #F5F6FA;

  .header {
    margin: 0 15px;

    .header-top {
      display: flex;
      justify-content: space-between;

      .go_back {
        font-size: 17px;
        font-weight: 400;
        color: #398CFF;
        line-height: 28px;
      }

      .header-top-btn {
        width: 150px;
        height: 31px;
        line-height: 32px;
        color: #999;
        font-size: 15px;
        font-weight: 400;
        border-radius: 16px;
        border: 1px solid #E8E8E8;

        .header-top-icon {
          font-size: 10px;
          vertical-align: baseline;
        }
      }
    }

    .header-top-two {
      justify-content: flex-end !important;
    }

    .header-card {
      height: 132px;
      margin-top: 20px;
      background: #fff;
      border-radius: 8px;
      padding: 0 10px 15px 10px;
      box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.1);

      .header-card-top {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .header-card-top-img {
          width: 78px;
          margin-top: -27px;
        }

        .header-card-top-text {
          color: #000;
          font-size: 18px;
          text-align: left;
          font-weight: 400;
          line-height: 26px;
          margin-left: 14px;
          padding-top: 10px;
        }
      }

      .header-card-bottom {
        height: 48px;
        display: flex;
        margin-top: 10px;
        border-radius: 9px;
        align-items: center;
        background: #FFFBF1;
        justify-content: space-between;

        .header-card-bottom-tab {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: center;
          border-right: 2px solid #FFECDE;

          .card-bottom-tab-icon {
            width: 16px;
          }

          .card-bottom-tab-name {
            font-size: 20px;
            font-weight: 400;
            color: #DC8535;
            margin-left: 4px;
          }
        }

        .header-card-bottom-tab:last-child {
          border-right: 0;
        }
      }
    }
  }

  .content {
    background: #fff;
    border-radius: 9px;
    margin: 10px 15px 0;
    padding-bottom: 12px;
    box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.1);

    .range {
      height: 57px;
      color: #fff;
      font-size: 25px;
      font-weight: 400;
      line-height: 57px;
      border-radius: 8px 8px 0px 0px;
      background: linear-gradient(158deg, rgba(255, 194, 72, 1) 0%, rgba(252, 167, 54, 1) 100%);
    }

    .title {
      height: 60px;
      color: #333;
      font-size: 26px;
      font-weight: bold;
      line-height: 60px;
      letter-spacing: 2px;
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-image: linear-gradient(180deg, rgba(250, 191, 12, 1) 0%, rgba(242, 139, 5, 1) 100%);
    }

    .group {
      padding: 0 10px;
      margin-top: 15px;

      &:first-of-type {
        margin-top: 0;
      }
    }
  }

  .footer-tips {
    font-size: 15px;
    font-weight: 400;
    color: #DC8535;
    text-align: left;
    padding: 5px 15px;
    line-height: 24px;
    background: url('~@/assets/images/information.png') no-repeat 21px 6px #FFFBF1;
    background-size: 20px 20px;
    margin-top: 20px;

    span {
      display: block;
      text-indent: 2em;
    }

    i {
      display: block;
      font-style: normal;
      text-indent: 2em;
    }

    b {
      display: block;
      cursor: pointer;
      font-style: normal;
      text-align: center;
      height: 24px;
      font-size: 14px;
      font-weight: 400;
      color: rgba(153, 153, 153, 1);
      margin: 4px 0 8px 0;

      i {
        display: inline;
        text-indent: 0;
        margin-left: 2px;
      }
    }
  }

  .footer-btn {
    width: 100%;
    height: 50px;
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 99;

    .footer-btn-ready {
      width: 100%;
      height: 100%;
      color: #FFF;
      font-size: 18px;
      font-weight: 500;
      background: linear-gradient(90deg, rgba(248, 135, 92, 1) 0%, rgba(246, 161, 66, 1) 100%);
    }
  }

  .footer-date {
    position: relative;

    .footer-date-img {
      width: 100%;
      vertical-align: top;
    }

    .footer-date-tips {
      width: 171px;
      font-size: 25px;
      color: #EE7800;
      text-align: left;
      font-weight: bold;
      line-height: 39px;
      text-shadow: 0px 2px 10px rgba(0, 0, 0, 0.1);
      position: absolute;
      top: 24%;
      right: 6%;
    }
  }
}

.daily_task {
  display: flex;
  justify-content: center;
  align-content: center;
  padding-top: 15px;
}

.report_detail_status {
  padding-top: 15px;
  font-size: 15px;
  font-weight: 400;
  color: #398CFF;
  line-height: 21px;
}

.close_tip {
  width: 190px;
  height: 62px;
  position: absolute;
  right: 0px;
  top: 0px;
}
</style>
