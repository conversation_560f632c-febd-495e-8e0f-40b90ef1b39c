<template>
  <div class="wrapper">
    <header class="header">
      <img src="./imgs/kefu-header.png" >
    </header>
    <div class="content">
      <div class="text">
        <p>我是您的控糖客服</p>
        <p>将协助您开启{{sourceType==4?'':'糖安宝安心'}}控糖计划</p>
        <p>是您控糖路上的重要帮手</p>
      </div>
      <div class="qrcode" v-if="sourceType!=4">
        <img src="https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/guoshou/erwema.png" >
      </div>
      <div class="chahua" v-if="sourceType==4">
        <img src="./imgs/<EMAIL>" >
      </div>
      <div class="tips" v-if="sourceType!=4">
        <p>长按二维码</p>
        <p>添加客服</p>
      </div>
    </div>
    <footer class="footer">
      <van-button v-if="sourceType!=4" round class="footer-btn" @click="showPop = true">已添加客服，去填写个人信息</van-button>

      <van-button v-if="sourceType==4" round class="footer-btn" @click="goCollect">去填写个人信息</van-button>
    </footer>
    <!-- 弹框 -->
    <van-overlay :show="showPop" @click="showPop = false">
      <div class="pop-wrapper" @click.stop>
        <div class="pop-content">
          <div class="pop-header">
            <div class="pop-header-title">您添加过客服了吗？</div>
            <van-icon name="cross" class="pop-header-close" @click="showPop = false" />
          </div>
          <div class="pop-text">客服会一步一步的带您体验控糖服务，是您控糖路上的重要伙伴，请确保已添加哦。</div>
          <div class="pop-btn">
            <van-button class="pop-btn-undo" @click="showPop = false" >未添加</van-button>
            <van-button class="pop-btn-undo pop-btn-done" @click="goCollect">已添加</van-button>
          </div>
          <div v-if="kefulink == 0" class="pop-tips" @click="linkKeFu">让客服主动联系我</div>
          <div v-else class="pop-tips">客服会主动添加您微信，请留意</div>
        </div>
      </div>
    </van-overlay>
    <div class="pop-toast" v-if="showToast">
      <van-icon name="success" class="pop-toast-icon" />
      <p class="pop-toast-text">客服会主动添加您微信，请留意</p>
    </div>
  </div>
</template>

<script>
  import { nationalUserStore } from '@/api/UserInfo.js'
  import { getBanFontSize } from '@/utils/utils.js'
  export default {
    data: () => {
      return {
        uid: '', // 用户id
        sourceType: '', // 1-小程序 2-管家app 3-国寿 4-爱选
        showPop: false, // 弹框flag
        showToast: false, // 提示flag
        kefulink: 0 // 点击客服联系我 0-没点 1-已点
      }
    },
    created() {
      let routeQuery = this.$route.query
      this.sourceType = routeQuery.source_type || ''
      this.uid = routeQuery.uid || ''
      this.kefulink = localStorage.getItem('kefulink') || 0
      // 判断是否安卓系统微信页面修改了字体
      getBanFontSize()
    },
    methods: {
      /**
       * 让客服主动联系我
       */
      linkKeFu() {
        // 已经点击
        if (this.kefulink == 1) return

        nationalUserStore({ uid: this.uid }).then(res => {
          if (res.status === 0) {
            localStorage.setItem('kefulink', 1)
            this.kefulink = 1

            this.showToast = true
            setTimeout(() => {
              // 前往信息收集页
              this.goCollect()
            }, 1000)
          } else {
            this.$toast(res.msg)
            localStorage.setItem('kefulink', 0)
            this.kefulink = 0
          }
        }).catch(err => {
          this.$toast(err)
          localStorage.setItem('kefulink', 0)
          this.kefulink = 0
        })
      },
      /**
       * 前往信息收集页
       */
      goCollect() {
        this.$router.push({
          path: '/user/info/collect',
          query: {
            source_type: this.sourceType,
            uid: this.uid
          }
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  .wrapper {
    overflow: hidden;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;

    .header {
      width: 100%;
      line-height: 0;
      text-align: center;

      img {
        width: 100%;
      }
    }

    .content {
      margin-top: 40px;
      text-align: center;

      .text {
        font-size: 17px;
        font-weight: 400;
        color: #424656;
        line-height: 27px;
      }

      .qrcode {
        line-height: 0;
        margin-top: 10px;

        img {
          width: 155px;
        }
      }

      .chahua{
        line-height: 0;
        margin-top: 10px;
        img{
          width: 315px;
          height: 184px;
        }
      }

      .tips {
        margin-top: 6px;
        font-size: 16px;
        font-weight: 400;
        color: #A1A4AB;
        line-height: 22px;
      }
    }

    .footer {
      width: 100%;
      text-align: center;
      padding-bottom: 20px;
      position: fixed;
      bottom: 0;
      left: 0;

      .footer-btn {
        width: 325px;
        height: 52px;
        font-size: 20px;
        font-weight: 500;
        color: #FFFFFF;
        background: #FF7E00;
        border-radius: 32px;
      }
    }

    .pop-wrapper {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;

      .pop-content {
        width: 325px;
        border-radius: 9px;
        padding: 0 26px 22px;
        background: #FFFFFF;
        box-sizing: border-box;

        .pop-header {
          margin-top: 31px;
          position: relative;

          .pop-header-title {
            font-size: 20px;
            font-weight: 500;
            color: #333333;
            line-height: 28px;
          }

          .pop-header-close {
            font-size: 22px;
            color: #EAEAEA;
            transform: translateY(-50%);
            position: absolute;
            right: 0;
            top: 50%;
          }
        }

        .pop-text {
          font-size: 18px;
          font-weight: 400;
          color: #333333;
          line-height: 25px;
          margin-top: 28px;
          text-align: left;
        }

        .pop-btn {
          margin-top: 52px;
          display: flex;
          align-items: center;
          justify-content: space-between;

          .pop-btn-undo {
            width: 124px;
            height: 42px;
            line-height: 42px;
            font-size: 20px;
            font-weight: 400;
            color: #FF7C35;
            border-radius: 5px;
            border: 1px solid #FF7C35;
          }

          .pop-btn-done {
            color: #FFFFFF;
            background: linear-gradient(270deg, #FF8100 0%, #FD9A57 100%);
          }
        }

        .pop-tips {
          width: fit-content;
          margin: 23px auto 0;
          font-size: 17px;
          font-weight: 400;
          color: #435680;
          line-height: 24px;
          border-bottom: 1px solid #435680;
        }
      }
    }

    .pop-toast {
      width: 171px;
      height: 143px;
      border-radius: 9px;
      background: rgba(0, 0, 0, 0.8);
      transform: translate(-50%, -50%);
      position: fixed;
      top: 50%;
      left: 50%;
      z-index: 1;

      .pop-toast-icon {
        font-size: 44px;
        color: #FFFFFF;
        margin-top: 29px;
      }

      .pop-toast-text {
        font-size: 14px;
        font-weight: 400;
        margin: 0 8px;
        color: #FFFFFF;
        line-height: 20px;
      }
    }
  }
</style>
