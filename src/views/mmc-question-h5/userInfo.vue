<template>
  <div class="user-box">
    <div class="title">
      <h2>{{ $t('mmcQuestionH5.realName') }}</h2>
      <p>{{ $t('mmcQuestionH5.realInfoNmae') }}</p>
    </div>
    <div class="user-info">
      <div class="intro">
        <span class="name">{{ visitUser.name }}</span>
        <span class="label">{{ $t('mmcQuestionH5.labelName') }}</span>
      </div>
      <img src="./images/woman-icon.png" class="pic" v-if="false" />
      <img src="./images/man-icon.png" class="pic" />
    </div>
    <div class="user-info">
        <div class="intro">
          <span class="name">{{ visitUser.cell }}</span>
          <span class="label">{{ $t('mmcQuestionH5.MobileName') }}</span>
        </div>
        <img src="./images/phone-icon.png" class="pic" />
      </div>
      <div class="user-info">
        <div class="intro">
          <span class="name">{{ visitUser.sfzid }}</span>
          <span class="label">{{ $t('mmcQuestionH5.identityIdName') }}</span>
        </div>
        <img src="./images/card-icon.png" class="pic" />
      </div>
      <div class="user-info">
        <div class="intro">
          <span class="name">{{ visitUser.barcode }}</span>
          <span class="label">MMC code</span>
        </div>
        <img src="./images/pass-icon.png" class="pic" />
      </div>
    <div class="start-btn">
      <span @click="goTo">{{ $t('mmcQuestionH5.startTopicName') }}</span>
    </div>
  </div>
</template>

<script>
import { Toast } from 'vant'
import { api } from '@/api/common/mmcQuestion.js';
export default {
  data() {
    return {
      visitUser: {},
    }
  },
  methods: {
    goTo() {
      this.$router.push({
        path: '/mmc/question/survey',
        query: {
          user_id: this.$route.query.user_id,
          visit_level: this.$route.query.visit_level,
          group: this.$route.query.group,
          groupName: this.$route.query.groupName,
        },
      })
    },
    getUserInfo() {
      api.visitBasicinfo({
        language: localStorage.getItem('lang') == 'zh_HK' ? 'simple_tradition' : ''
      }).then((res) => {
        this.visitUser = res.data.userInfo
      }).catch((err) => {
        Toast({
          message: `${err}`,
          type: 'html',
          forbidClick: true,
          duration: 1000
        })
      })
    }
  },
  created() {
    this.getUserInfo()
    if(localStorage.getItem('lang') == 'zh_HK'){
      document.title = '確認個人資訊'
    }
  }
}
</script>

<style lang="scss" scoped>
.user-box{
  padding: 20px;
  .title{
    display: flex;
    align-items: flex-start;
    flex-direction: column;
    margin-bottom: 15px;
    h2{
      font-size: 17px;
      font-weight: bold;
      color: #333;
      line-height: 28px;
    }
    p{
      font-size: 14px;
      color: #9b9b9b;
      margin-top: 5px;
      font-weight: 500;
    }
  }
  .user-info{
    display: flex;
    background-color:#f7f7f7;
    padding: 12px;
    border-radius: 6px;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    .intro{
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      .name{
        color:#000;
        font-size: 16px;
      }
      .label{
        color:#9b9b9b;
        font-size: 14px;
        font-weight: 500;
        margin-top:8px
      }
    }
    .pic{
      border-radius: 50%;
      width: 68px;
    }
  }
  .start-btn{
    width: 175px;
    height: 54px;
    text-align: center;
    background: url(./images/border-icon.png) no-repeat;
    background-size: cover;
    margin: 30px auto;
    display: flex;
    align-items: center;
    justify-content: center;
    span{
      font-weight: bold;
      text-align: center;
      background: -webkit-linear-gradient(left, #e57262, #ef994b);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      font-size: 18px;
    }
  }
}
</style>