<template>
  <div class="wrap">
    <div v-for="(item,index) in visitList" :key="index" style="padding-top: 12px;">
      <h4 class="manage-time">{{ $t('mmcQuestionH5.manageTimeName') }}: {{item.visit_start_date }} ~ {{ item.visit_end_date }}</h4>
      <div class="box">
        <div class="interview-info">
          <div class="interview-title">
            <span class="title">第{{ item.visit_level }}{{ $t('mmcQuestionH5.interviewName') }}</span>
            <span class="time">{{ item.visit_at }}</span>
          </div>
          <van-button plain class="btn" type="info" @click="goTo(item)">{{ $t('mmcQuestionH5.fillOutName') }}</van-button>
        </div>
        <div class="interview-cart">
          <div class="phase" v-for="(k, Kindex) in item.qss" :key="Kindex" @click="goToItems(item,k)">
            <span>{{k.name}}</span>
            <span>{{k.percent }}%</span>
            <span v-if="k.percent != 100" class="red"></span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {api} from '@/api/common/mmcQuestion.js';
import { Toast, Locale } from 'vant'
import language from '../../i18n/index'
import { getUrlParams } from '@/utils/utils.js'
export default {
  data() {
    return {
      user_id: null,
      visitList: [],
      lang: '',
      // langType:'zh_CN'
    }
  },
  created() {
    // 咖啡与茶问卷填写完成后 统一返回到此页面 做判断 （ from == coffeeTea 说明是咖啡与茶问卷返回的 ）
    let url = location.href
    let from = getUrlParams(url,'from')
    let isMini = navigator.userAgent.indexOf('miniProgram') > -1
    if(from && from == 'coffeeTea' && isMini){
      this.$router.replace('/ihecMiniwechat/questionnaireList')
      return
    }
    //zh_CN中文
    //zh-HK//繁体
    const messages = language._vm.messages
    const localLangType = localStorage.getItem('lang')
    const langType = this.$route.query.langType || localLangType || 'zh_CN'
    switch (langType) {
      case 'zh_CN':
          this.lang = 'zh_CN'
        break;
      case 'zh_HK':
        this.lang = 'zh_HK'
        break;
      case 'zh_TW':
        this.lang = 'zh_HK'
        break;
      default:
         this.lang = 'zh_CN'
        break;
    }
    this.$i18n.locale = this.lang
    Locale.use(this.lang, messages[this.lang])
    localStorage.setItem('lang', this.lang)
    //隐藏token
    // let url = location.href
    let accessToken = getUrlParams(url,'access-token')
    if(accessToken){
      let url = `${this.$route.path}?refresh=true`
      this.$router.replace(url)
    }
  },
  methods: {
    goTo(item) {
      if (item.visit_level == 1) {
        this.$router.push({
          path: '/mmc/question/userInfo',
          query: {
            user_id: this.user_id,
            visit_level:item.visit_level,
            group: item.qss[0].qss_type,
            groupName: item.qss[0].name
          },
        })
      } else {
        this.$router.push({
          path: '/mmc/question/survey',
          query: {
            user_id: this.user_id,
            visit_level: item.visit_level,
            group: item.qss[0].qss_type,
            groupName: item.qss[0].name
          },
        })
      }
    },
    goToItems(item, k) {
      if (item.visit_level == 1 && k.qss_type == "basic_info") {
        this.$router.push({
          path: '/mmc/question/userInfo',
          query: {
            user_id: this.user_id,
            visit_level: item.visit_level,
            group: k.qss_type,
            groupName: k.name
          },
        })
      } else {
        this.$router.push({
          path: '/mmc/question/survey',
          query: {
            user_id: this.user_id,
            visit_level: item.visit_level,
            group: k.qss_type,
            groupName: k.name
          },
        })
      }
    },
    getVisitList() {
      api.visitQuestionList({
        language: this.lang == 'zh_HK' ? 'simple_tradition':'' 
      }).then((res) => {
        if(res.status == 0){
          this.visitList = res.data.list;
          this.user_id = res.data.user_id
          if(this.visitList.length == 0){
            this.$router.push({
              path: '/mmc/question/notLogin',
              query: {
                noData: true
              }
            })
          }
        } else {
          this.$toast(res.msg)
        }
      }).catch((err) => {
        Toast({
          message: `${err}`,
          type: 'html',
          forbidClick: true,
          duration: 1000
        })
      })
    }
  },
  mounted() {
    this.getVisitList()
  }
}
</script>
<style lang="scss" scoped>
.wrap {
  min-height: calc(100vh - 10px);
  background-color: #f3f5f9;
  padding: 0px 12px 10px 12px;

  .manage-time {
    font-size: 15px;
    font-weight: 500;
    color: #878F99;
    text-align: left;
  }

  .box {
    background-color: #fff;
    border-radius: 8px;
    padding: 18px 10px;
    margin-top: 12px;
    margin-bottom: 20px;
    .interview-info {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .interview-title {
        display: flex;
        flex-direction: column;

        .title {
          color: #0A0A0A;
          font-weight: 400;
          font-size: 16px;
        }

        .time {
          color: #878F99;
          font-size: 16px;
          margin-top: 10px;
          text-align: left;
          font-weight: 400;
        }
      }

      .btn {
        width: 66px;
        height: 28px;
        border: 1px solid #3388FF;
        border-radius: 4px;
        display: flex;
        padding: 0px;
        align-items: center;
        justify-content: center;
        color: #3388FF;
      }
    }

    .interview-cart {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;

      .phase {
        width: 47%;
        border: 1px solid #E5E5E5;
        border-radius: 4px;
        background-clip: border-box;
        background: #fff;
        height: 42px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 14px;
        position: relative;

        span:nth-child(1) {
          font-weight: 500;
          color: #5A6266;
          margin-left: 8px;
          font-size: 16px;
        }

        span:nth-child(2) {
          color: #878F99;
          font-size: 14px;
          font-weight: 500;
          margin-right: 5px;
        }

        .red {
          position: absolute;
          width: 10px;
          height: 10px;
          border-radius: 50%;
          background: red;
          top: 0;
          right: -1px;
          z-index: 50;
        }
      }
    }
  }
}</style>