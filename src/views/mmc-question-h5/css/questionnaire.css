

/* 单选多选页 */
.white {
  background: #fff !important; 
  padding: 12px 15px 0px  15px;
}

.hide {
  display: none; }

.JS-qss-form .title-box {
  overflow: hidden;
  margin-bottom: 8px; }
.JS-qss-form .title-box .qss-no {
  overflow: hidden;
  line-height: 1.01333rem;
  margin-bottom: 0.13333rem; }
.JS-qss-form .title-box .qss-no .num {
  color: #ff831b;
  font-size: 25px;
  font-weight: bold;
  float: left; }
.JS-qss-form  .title-box .qss-no .qss-progress {
  float: right;
  color: #333;
  font-size: 15px;
  display: block; }
.JS-qss-form .qss-title {
  color: #ff8e00;
  font-size: 18px;
  font-weight: normal;
  margin-bottom: 12px;
  display: block;
  font-weight: 500;
  text-align:left;
}
.JS-qss-form  .qss-title label {
  font-weight: 500;
  position: relative;
  width: 50px;
  height: 22px;
  top:-2px;
  color: #ff8e00;
  background: url(../images/border-line.png) no-repeat;
  background-size: 100%;
  text-align: center;
  line-height: 22px;
  font-size: 12px;
  margin-right: 4px;
  margin-top: 2px;
  display: inline-block;
  /* float: left;  */
}
.JS-qss-form .form-box .JS-select-option-box {
  position: relative;
  overflow: hidden;
  font-size: 0.38889rem !important;
  color: #666; }
.JS-qss-form .form-box .JS-select-option-box.vegetables-fruits-first-option {
  padding-top: 45px;
}
.JS-qss-form .form-box .JS-select-option-box .JS-option {
  line-height: 1.8;
  background: #f7f7f7;
  margin-bottom: 12px;
  padding: 10px 0px;
  border-radius: 0.08rem;
  text-align: left;
  font-size: 15px;
  font-weight: 500;
  -webkit-background-clip: border-box;
  background-clip: border-box; }
.JS-qss-form .form-box .JS-select-option-box .class-vegetables-fruits-tips {
  position: absolute;
  top: 6px;
  text-align: left;
  line-height: 1.3;
}
.JS-qss-form .form-box .JS-select-option-box .class-vegetables-fruits-tips span {
  color: #ff8e00;
}
.JS-qss-form .form-box .JS-select-option-box .JS-option.hide-this-option {
  display: none;
}
.JS-qss-form .form-box .JS-select-option-box .JS-option.disabled-this-option {
  opacity: 0.7;
}
.JS-qss-form .form-box .JS-select-option-box .JS-option.disabled-this-option.JS-selected {
  opacity: 1;
}
.JS-qss-form .form-box .JS-select-option-box .JS-option .option-qss-no {
  padding-left: 12px; }
.JS-qss-form .form-box .JS-multi-option-box {
  font-size: 0.38889rem !important;
  color: #666; }
.JS-qss-form .form-box .JS-multi-option-box .JS-option {
  line-height: 1.8;
  background: #f7f7f7;
  margin-bottom: 12px;
  padding: 10px 0px;
  border-radius: 0.08rem;
  text-align: left;
  font-size: 15px;
  font-weight: 500;
  -webkit-background-clip: border-box;
  background-clip: border-box; }
.JS-qss-form .form-box .JS-multi-option-box .JS-option .option-qss-no {
  padding-left: 12px; }
.JS-qss-form .form-box .JS-check-option-box {
  font-size: 15px !important;
  color: #666; }
.JS-qss-form .form-box .JS-check-option-box .JS-option {
  display: flex;
  flex-direction: column;
  line-height: 1.8;
  background: #f7f7f7;
  margin-bottom: 12px;
  padding: 10px 12px;
  border-radius: 0.08rem;
  text-align: left;
  background-clip: border-box;
  overflow: hidden;
  clear: both; }
.JS-qss-form .form-box .JS-check-option-box .JS-option .option-qss-no {
  text-align: left;
  float: left; }
.JS-qss-form .form-box .JS-check-option-box .JS-option .JS-sub-box {
  float: left; }
.JS-qss-form .form-box  .JS-selected:not(.JS-option-hidden) {
  background: #ff8e00;
  color: #fff;
  position: relative; }
.JS-qss-form .form-box .JS-selected:not(.JS-option-hidden):after {
  content: "";
  background: url(../images/check-icon.png) right center no-repeat;
  width: 0.44444rem;
  height: 0.44444rem;
  background-size: 0.44444rem 0.44444rem;
  right: 0.21333rem;
  position: absolute;
  top: 0.4rem; }
.JS-sub-result{
  text-align: left;
  font-size: 15px;
  font-weight: 500;
  padding:0 0.05rem;
  padding-left:0.3rem;
}


.JS-selected:not(.JS-option-hidden) {
  background: orange;
}

.JS-option-hidden {
  display: none !important;
}
.JS-option-item {
  padding: 5px 0px;
}


.JS-scroll-select-box {
  display: none;
}

.qss-input .JS-input{
  border: 1px solid #ddd;
  padding: 5px 10px;
  border-radius: 0.10667rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  -moz-background-clip: border-box;
  line-height: 30px;
  font-size: 0.38889rem !important;
  width: 95%;
  text-align: center;
  -webkit-appearance: none;
}
.input-group-box .input-unit-pre{
  width:51% !important;
}
.input-group-box .JS-sub-input{
  margin-bottom:0.04rem;
  -webkit-appearance: none;
}
.input-group-box .JS-sub-input:not(.input-unit-pre){
  width:65% !important;;
}
.qss-input:not(.input-group-box) .JS-sub-input{
  width:75% !important;
}
.qss-input .JS-sub-input-unit {
  /* width: 24%; */
  float: right;
  line-height: 33px;
}
.input-div{
  padding: 6px 13px;
  height:30px;
}
.qss-input .JS-input-error{
  color: red;
  font-size: 0.38rem;
  padding: 0.02rem 0.12rem;
  /* border: 1px solid orange; */
  width: 100%;
  display: block;
  margin-top: 0.15rem;
  text-align: left;
}
.qss-input .JS-input-sub-label{
  float: left;
  width: 24%;
  line-height: 31px;
  font-size: 14px;
}
.qss-input .JS-input-sub-label+.input-text+.JS-sub-input-error{
  padding-left: 24%;
}
.qss-input .JS-input-sub-label+.input-text+.JS-sub-input-unit+.JS-sub-input-error{
  padding-left: 24%;
}
.form-box .JS-sub-input-error{
  color: red;
  font-size: 0.25rem;
  padding: 0 0.32rem;
}
.qss-input .JS-sub-input-error{
  color: red;
  font-size: 0.35rem;
  padding: 0.08rem 0.32rem;
  padding-left: 0px;
  /*padding-left: 1.7rem;*/
}
.medical-box {
  text-align: center;
  width: 65%;
  margin: 0 auto;
  overflow: hidden; }
.medical-box .JS-qss {
  color: #ff8e00;
  height: 18px;
  font-size: 16px;
  padding: 10px 45px;
  background:white;
  font-weight: 500;
}
.medical-box .JS-qss-next {
  border-radius: 0.66667rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  border: 1px solid #ff8000;
  display: inline-block; }

.medical-box .JS-qss-prev {
  border-radius: 0.66667rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  border: 1px solid #ff8000;
  display: inline-block; }

#treelist_dummy {
  width: 100%;
  padding: 0.4rem 0;
  position: absolute;
  left: 0;
  opacity: 0; }

.android-ics.light .dwv {
  border-bottom: 0px;
  color: #000;
  line-height: 1.06667rem;
  height: 1.06667rem;
  color: #000; }
.android-ics.light .dw {
  width: 80% !important;
  margin: 0 auto; }
.android-ics.light .dw .dwwol {
  width: 100%;
  left: 0;
  border-top: 1px solid #ddd;
  border-bottom: 1px solid #ddd; }
.android-ics.light .dw .dwc {
  width: 90%;
  padding: 0; }
.android-ics.light .dw .dwwb {
  display: none; }
.android-ics.light .dw .dwwr {
  width: 100% !important;
  margin: 0 auto; }
.android-ics.light .dw .dwwr .dwwc {
  width: 100%; }
.android-ics.light .dw .dwwr .dwwc table {
  width: 100%; }
.android-ics.light .dw .dwwr .dwwc .dww {
  width: 100%;
  min-width: 80% !important; }
.android-ics.light .dw .dwwr .dww .dw-li.dw-hl {
  background: none !important; }
.android-ics.light .dw .dwbc {
  line-height: 1.06667rem;
  height: 1.06667rem; }
.android-ics.light .dw .dwbc .dwb {
  line-height: 1.06667rem;
  height: 1.06667rem; }
.android-ics.light .dw .dwbc .dwb-a {
  background: none;
  color: #ff8e00; }

/* 填空 */
.m-title {
  margin-bottom: 0.32rem; }
.m-title span {
  float: left;
  display: inline-block; }
.m-title .border {
  position: relative;
  width: 1.35185rem;
  height: 0.56481rem;
  color: #ff8e00;
  background: url(../images/border-line.png) no-repeat;
  background-size: 100%;
  text-align: center;
  line-height: 0.56481rem;
  font-size: 0.33333rem;
  margin-right: 0.10667rem;
  margin-top: 0.05333rem; }
.m-title h3 {
  color: #ff8e00;
  font-size: 0.48148rem;
  font-weight: normal; }

.medical-fill .list li {
  width: 100%;
  margin-bottom: 0.32rem; }
.inputgroup-label{
  font-size: 0.39rem;
  padding: 0.15rem;
  color: #a59292;
  text-align: left;
}
.medical-fill .list li .input {
  padding: 0.13333rem 0.26667rem;
  border: 1px solid #ddd;
  -webkit-border-radius: 0.10667rem;
  -moz-border-radius: 0.10667rem;
  border-radius: 0.10667rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  width: 100%;
  line-height: 0.69333rem;
  font-size: 0.38889rem; }

/* 弹窗 */
.hm-pop {
  position: fixed;
  width: 100%;
  left: 0;
  top: 0;
  bottom: 0;
  height: 100%;
  z-index:0;
}
.hm-pop .hm-pop-shade {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: none; }
.hm-pop .hm-pop-block {
  position: absolute;
  left: 50%;
  top: 60%;
  background-color: #fff;
  width: 80%;
  padding-top: 10px;
  /*margin-left: -40%;*/
  z-index: 100100;
  border-radius: .2rem;
  /*overflow: hidden;*/
  -webkit-transform: translate(-50%, -350%);
  transform: translate(-50%, -350%);
  -webkit-transform-origin: center;
  transform-origin: center;
  -webkit-transtion: all .5s;
  transition: all .5s; }
.hm-pop .hm-pop-block.more {
  top: 66%;
}
.hm-pop .hm-pop-block .hm-pop-head {
  padding: 15px 10px;
  color: #000;
  font-size: 18px;
  text-align: center; }
.hm-pop .hm-pop-body {
  margin-bottom: 0.48rem; }
.hm-pop .hm-pop-body .qss-input {
  width: 90%;
  margin: 0 auto; }
.hm-pop .hm-pop-body .qss-input .input-text {
  border: 1px solid #ddd;
  padding: 6px 12px;
  border-radius: 0.10667rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  line-height: 24px;
  height: 24px;
  font-size: 15px !important;
  width: 90%; }
.qss-input .input-group-box .input-text {
  width: 75%;
}
.qss-input-unit {
  text-align: left;
}
.qss-input.qss-input-unit .JS-input {
  width: 70%;
}
.qss-input-unit > .unit {
  display: inline-block;
  width: 20%;
  padding-left: 6px;
}
.hm-pop .hm-pop-close {
  border-top: 1px solid #ddd;
  height: 38px; }
.hm-pop .hm-pop-close a {
  color: #333;
  font-size: 0.38889rem;
  width: 48%;
  text-align: center;
  display: inline-block;
  float: left;
  line-height: 38px;
  border-right: 1px solid #ddd; }
.hm-pop .hm-pop-close a:last-child {
  border-right: 0px; }
.hm-pop .hm-pop-close .sure {
  color: #ff8e00; }

.JS-LOADING-BOX .JS-LOADING-BG{
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0px;
  left: 0px;
  background: rgba(255,255,255,0.5);
  z-index: 100;
  /* background: url(../images/loading.gif) no-repeat;
  background-size: 100%; */
}
.JS-LOADING-BOX .JS-LOADING-CON{
  width: 100px;
  position: fixed;
  left: 45%;
  top: 50%;
}
.x-footer {
  font-size: 0.29333rem; }

.JS-option{
  cursor: pointer;
  padding-right: 40px !important;
}

.JS-qss-form-readme span{
  font-size: 0.30rem;
  padding: 0.15rem;
  color: #ff8e00;
}

.tpisImg{
  max-width: 100%;
  border: none;
  vertical-align: middle;
}

.no-form-box > div {
  pointer-events: none;
}