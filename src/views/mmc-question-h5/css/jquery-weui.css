/** 
* jQuery WeUI V1.2.0 
* By 言川
* http://lihongxun945.github.io/jquery-weui/
 */

.preloader {
  width: 20px;
  height: 20px;
  -webkit-transform-origin: 50%;
  transform-origin: 50%;
  -webkit-animation: preloader-spin 1s steps(12, end) infinite;
  animation: preloader-spin 1s steps(12, end) infinite;
}

.preloader:after {
  display: block;
  width: 100%;
  height: 100%;
  content: "";
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg%20viewBox%3D'0%200%20120%20120'%20xmlns%3D'http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg'%20xmlns%3Axlink%3D'http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink'%3E%3Cdefs%3E%3Cline%20id%3D'l'%20x1%3D'60'%20x2%3D'60'%20y1%3D'7'%20y2%3D'27'%20stroke%3D'%236c6c6c'%20stroke-width%3D'11'%20stroke-linecap%3D'round'%2F%3E%3C%2Fdefs%3E%3Cg%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(30%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(60%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(90%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(120%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(150%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.37'%20transform%3D'rotate(180%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.46'%20transform%3D'rotate(210%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.56'%20transform%3D'rotate(240%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.66'%20transform%3D'rotate(270%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.75'%20transform%3D'rotate(300%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.85'%20transform%3D'rotate(330%2060%2C60)'%2F%3E%3C%2Fg%3E%3C%2Fsvg%3E");
  background-repeat: no-repeat;
  background-position: 50%;
  background-size: 100%;
}

@-webkit-keyframes preloader-spin {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes preloader-spin {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

label>* {
  pointer-events: none;
}

html {
  font-size: 20px;
}

body {
  font-size: 16px;
}

.weui-mask {
  opacity: 0;
  -webkit-transition-duration: .3s;
  transition-duration: .3s;
  visibility: hidden;
}

.weui-mask.weui-mask--visible {
  opacity: 1;
  visibility: visible;
}

.weui-prompt-input {
  padding: 4px 6px;
  border: 1px solid #ccc;
  box-sizing: border-box;
  height: 2em;
  width: 80%;
  margin-top: 10px;
}


@keyframes preloader-spin {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

.weui-tab__bd-item.weui-pull-to-refresh {
  position: absolute;
  top: 50px;
}

.toolbar {
  position: relative;
  width: 100%;
  font-size: .85rem;
  line-height: 1.5;
  color: #3d4145;
  background: #f7f7f8;
}

.toolbar:before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: auto;
  right: auto;
  height: 1px;
  width: 100%;
  background-color: #d9d9d9;
  display: block;
  z-index: 15;
  -webkit-transform-origin: 50% 0%;
  transform-origin: 50% 0%;
}

@media only screen and (-webkit-min-device-pixel-ratio: 2) {
  .toolbar:before {
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
  }
}

@media only screen and (-webkit-min-device-pixel-ratio: 3) {
  .toolbar:before {
    -webkit-transform: scaleY(0.33);
    transform: scaleY(0.33);
  }
}

.toolbar .toolbar-inner {
  height: 46px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  text-align: center;
}

.toolbar .title {
  position: absolute;
  display: block;
  width: 100%;
  padding: 0;
  font-size: .85rem;
  font-weight: normal;
  line-height: 2.2;
  color: #3d4145;
  text-align: center;
  white-space: nowrap;
}

.toolbar .picker-button {
  position: absolute;
  right: 0;
  box-sizing: border-box;
  height: 44px;
  line-height: 44px;
  color: #04BE02;
  z-index: 1;
  padding: 0 .5rem;
}

/* === Columns Picker === */

.weui-picker-modal {
  width: 100%;
  position: absolute;
  bottom: 0;
  text-align: center;
  border-radius: 0;
  opacity: 0.6;
  color: #3d4145;
  -webkit-transition-duration: .3s;
  transition-duration: .3s;
  height: 13rem;
  background: #EFEFF4;
  -webkit-transform: translate3d(0, 100%, 0);
  transform: translate3d(0, 100%, 0);
  -webkit-transition-property: opacity, -webkit-transform;
  transition-property: opacity, -webkit-transform;
  transition-property: transform, opacity;
  transition-property: transform, opacity, -webkit-transform;
}

.weui-picker-modal.picker-modal-inline {
  height: 10.8rem;
  opacity: 1;
  position: static;
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.weui-picker-modal.picker-modal-inline .toolbar {
  display: none;
}

.weui-picker-modal.picker-columns-single .picker-items-col {
  width: 100%;
}

.weui-picker-modal.weui-picker-modal-visible {
  opacity: 1;
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.weui-picker-modal .picker-modal-inner {
  position: relative;
  height: 10.8rem;
}

.weui-picker-modal .picker-columns {
  width: 100%;
  height: 13rem;
  z-index: 11500;
}

.weui-picker-modal .picker-columns.picker-modal-inline,
.popover .weui-picker-modal .picker-columns {
  height: 10rem;
}

@media (orientation: landscape) and (max-height: 415px) {
  .weui-picker-modal .picker-columns:not(.picker-modal-inline) {
    height: 10rem;
  }
}

.weui-picker-modal .popover.popover-picker-columns {
  width: 14rem;
}

.weui-picker-modal .picker-items {
  display: -webkit-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  width: 100%;
  padding: 0;
  text-align: right;
  font-size: 1rem;
  font-weight: normal;
  -webkit-mask-box-image: -webkit-linear-gradient(bottom, transparent, transparent 5%, white 20%, white 80%, transparent 95%, transparent);
  -webkit-mask-box-image: linear-gradient(to top, transparent, transparent 5%, white 20%, white 80%, transparent 95%, transparent);
}

.weui-picker-modal .bar+.picker-items {
  height: 10.8rem;
}

.weui-picker-modal .picker-items-col {
  overflow: hidden;
  position: relative;
  max-height: 100%;
}

.weui-picker-modal .picker-items-col.picker-items-col-left {
  text-align: left;
}

.weui-picker-modal .picker-items-col.picker-items-col-center {
  text-align: center;
}

.weui-picker-modal .picker-items-col.picker-items-col-right {
  text-align: right;
}

.weui-picker-modal .picker-items-col.picker-items-col-divider {
  color: #3d4145;
  display: -webkit-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  -webkit-align-items: center;
  align-items: center;
}

.weui-picker-modal .picker-items-col-wrapper {
  -webkit-transition: 300ms;
  transition: 300ms;
  -webkit-transition-timing-function: ease-out;
  transition-timing-function: ease-out;
}

.weui-picker-modal .picker-item {
  height: 32px;
  line-height: 32px;
  padding: 0 10px;
  white-space: nowrap;
  position: relative;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #9b9b9b;
  left: 0;
  top: 0;
  width: 100%;
  box-sizing: border-box;
  -webkit-transition: 300ms;
  transition: 300ms;
}

.picker-items-col-absolute .weui-picker-modal .picker-item {
  position: absolute;
}

.weui-picker-modal .picker-item.picker-item-far {
  pointer-events: none;
}

.weui-picker-modal .picker-item.picker-selected {
  color: #3d4145;
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
  -webkit-transform: rotateX(0deg);
  transform: rotateX(0deg);
}

.weui-picker-modal .picker-center-highlight {
  height: 32px;
  box-sizing: border-box;
  position: absolute;
  left: 0;
  width: 100%;
  top: 50%;
  margin-top: -16px;
  pointer-events: none;
}

.weui-picker-modal .picker-center-highlight:before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: auto;
  right: auto;
  height: 1px;
  width: 100%;
  background-color: #D9D9D9;
  display: block;
  z-index: 15;
  -webkit-transform-origin: 50% 0%;
  transform-origin: 50% 0%;
}

@media only screen and (-webkit-min-device-pixel-ratio: 2) {
  .weui-picker-modal .picker-center-highlight:before {
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
  }
}

@media only screen and (-webkit-min-device-pixel-ratio: 3) {
  .weui-picker-modal .picker-center-highlight:before {
    -webkit-transform: scaleY(0.33);
    transform: scaleY(0.33);
  }
}

.weui-picker-modal .picker-center-highlight:after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  right: auto;
  top: auto;
  height: 1px;
  width: 100%;
  background-color: #D9D9D9;
  display: block;
  z-index: 15;
  -webkit-transform-origin: 50% 100%;
  transform-origin: 50% 100%;
}

@media only screen and (-webkit-min-device-pixel-ratio: 2) {
  .weui-picker-modal .picker-center-highlight:after {
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
  }
}

@media only screen and (-webkit-min-device-pixel-ratio: 3) {
  .weui-picker-modal .picker-center-highlight:after {
    -webkit-transform: scaleY(0.33);
    transform: scaleY(0.33);
  }
}

.weui-picker-modal .picker-3d .picker-items {
  overflow: hidden;
  -webkit-perspective: 1200px;
  perspective: 1200px;
}

.weui-picker-modal .picker-3d .picker-items-col,
.weui-picker-modal .picker-3d .picker-items-col-wrapper,
.weui-picker-modal .picker-3d .picker-item {
  -webkit-transform-style: preserve-3d;
  transform-style: preserve-3d;
}

.weui-picker-modal .picker-3d .picker-items-col {
  overflow: visible;
}

.weui-picker-modal .picker-3d .picker-item {
  -webkit-transform-origin: center center -110px;
  transform-origin: center center -110px;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-transition-timing-function: ease-out;
  transition-timing-function: ease-out;
}

.weui-picker-overlay,
.weui-picker-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 0;
  width: 100%;
  z-index: 1000;
}

.city-picker .picker-items-col {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  max-width: 7rem;
}

.weui-picker-container .weui-cells {
  margin: 0;
  text-align: left;
}

.datetime-picker .picker-item {
  text-overflow: initial;
}

.weui-select-modal {
  height: auto;
}

.weui-select-modal .weui-cells {
  margin: 0;
  text-align: left;
  overflow-y: auto;
  overflow-x: hidden;
  max-height: 16rem;
}

.weui-select-modal .weui-cells:after {
  display: none;
}

/* === Calendar === */

.weui-picker-calendar {
  background: #fff;
  height: 15rem;
  width: 100%;
  overflow: hidden;
}

.weui-picker-calendar .picker-modal-inner {
  overflow: hidden;
  height: 12.8rem;
}

.picker-calendar-week-days {
  height: .9rem;
  background: #f7f7f8;
  display: -webkit-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  font-size: 11px;
  box-sizing: border-box;
  position: relative;
}

.picker-calendar-week-days:after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  right: auto;
  top: auto;
  height: 1px;
  width: 100%;
  background-color: #c4c4c4;
  display: block;
  z-index: 15;
  -webkit-transform-origin: 50% 100%;
  transform-origin: 50% 100%;
}

@media only screen and (-webkit-min-device-pixel-ratio: 2) {
  .picker-calendar-week-days:after {
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
  }
}

@media only screen and (-webkit-min-device-pixel-ratio: 3) {
  .picker-calendar-week-days:after {
    -webkit-transform: scaleY(0.33);
    transform: scaleY(0.33);
  }
}

.picker-calendar-week-days .picker-calendar-week-day {
  -webkit-flex-shrink: 1;
  -ms-flex: 0 1 auto;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  width: 14.28571429%;
  width: calc(100% / 7);
  line-height: 17px;
  text-align: center;
}

.picker-calendar-week-days+.picker-calendar-months {
  height: 11.9rem;
}

.picker-calendar-months {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
}

.picker-calendar-months-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  -webkit-transition: 300ms;
  transition: 300ms;
}

.picker-calendar-month {
  display: -webkit-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -ms-flex-direction: column;
  -webkit-flex-direction: column;
  flex-direction: column;
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
}

.picker-calendar-row {
  height: 16.66666667%;
  height: calc(100% / 6);
  display: -webkit-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-flex-shrink: 1;
  -ms-flex: 0 1 auto;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  width: 100%;
  position: relative;
}

.picker-calendar-row:after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  right: auto;
  top: auto;
  height: 1px;
  width: 100%;
  background-color: #ccc;
  display: block;
  z-index: 15;
  -webkit-transform-origin: 50% 100%;
  transform-origin: 50% 100%;
}

@media only screen and (-webkit-min-device-pixel-ratio: 2) {
  .picker-calendar-row:after {
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
  }
}

@media only screen and (-webkit-min-device-pixel-ratio: 3) {
  .picker-calendar-row:after {
    -webkit-transform: scaleY(0.33);
    transform: scaleY(0.33);
  }
}

.weui-picker-modal .picker-calendar-row:last-child:after {
  display: none;
}

.picker-calendar-day {
  -webkit-flex-shrink: 1;
  -ms-flex: 0 1 auto;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  display: -webkit-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  -webkit-align-items: center;
  align-items: center;
  box-sizing: border-box;
  width: 14.28571429%;
  width: calc(100% / 7);
  text-align: center;
  color: #3d4145;
  font-size: 15px;
  cursor: pointer;
}

.picker-calendar-day.picker-calendar-day-prev,
.picker-calendar-day.picker-calendar-day-next {
  color: #ccc;
}

.picker-calendar-day.picker-calendar-day-disabled {
  color: #d4d4d4;
  cursor: auto;
}

.picker-calendar-day.picker-calendar-day-today span {
  background: #e3e3e3;
}

.picker-calendar-day.picker-calendar-day-selected span {
  background: #04BE02;
  color: #fff;
}

.picker-calendar-day span {
  display: inline-block;
  border-radius: 100%;
  width: 30px;
  height: 30px;
  line-height: 30px;
}

.picker-calendar-month-picker,
.picker-calendar-year-picker {
  display: -webkit-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  -webkit-align-items: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  width: 50%;
  max-width: 200px;
  -webkit-flex-shrink: 10;
  -ms-flex: 0 10 auto;
  -webkit-flex-shrink: 10;
  -ms-flex-negative: 10;
  flex-shrink: 10;
}

.picker-calendar-month-picker a.icon-only,
.picker-calendar-year-picker a.icon-only {
  min-width: 36px;
}

.picker-calendar-month-picker span,
.picker-calendar-year-picker span {
  -webkit-flex-shrink: 1;
  -ms-flex: 0 1 auto;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  position: relative;
  overflow: hidden;
  text-overflow: ellipsis;
}

.popover .picker-calendar .picker-calendar-week-days,
.picker-calendar.picker-modal-inline .picker-calendar-week-days {
  background: none;
}

.popover .picker-calendar .toolbar:before,
.picker-calendar.picker-modal-inline .toolbar:before,
.popover .picker-calendar .picker-calendar-week-days:before,
.picker-calendar.picker-modal-inline .picker-calendar-week-days:before {
  display: none;
}

.popover .picker-calendar .toolbar:after,
.picker-calendar.picker-modal-inline .toolbar:after,
.popover .picker-calendar .picker-calendar-week-days:after,
.picker-calendar.picker-modal-inline .picker-calendar-week-days:after {
  display: none;
}

.popover .picker-calendar .toolbar~.picker-modal-inner .picker-calendar-months:before,
.picker-calendar.picker-modal-inline .toolbar~.picker-modal-inner .picker-calendar-months:before,
.popover .picker-calendar .picker-calendar-week-days~.picker-calendar-months:before,
.picker-calendar.picker-modal-inline .picker-calendar-week-days~.picker-calendar-months:before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: auto;
  right: auto;
  height: 1px;
  width: 100%;
  background-color: #c4c4c4;
  display: block;
  z-index: 15;
  -webkit-transform-origin: 50% 0%;
  transform-origin: 50% 0%;
}

@media only screen and (-webkit-min-device-pixel-ratio: 2) {
  .popover .picker-calendar .toolbar~.picker-modal-inner .picker-calendar-months:before,
  .picker-calendar.picker-modal-inline .toolbar~.picker-modal-inner .picker-calendar-months:before,
  .popover .picker-calendar .picker-calendar-week-days~.picker-calendar-months:before,
  .picker-calendar.picker-modal-inline .picker-calendar-week-days~.picker-calendar-months:before {
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
  }
}

@media only screen and (-webkit-min-device-pixel-ratio: 3) {
  .popover .picker-calendar .toolbar~.picker-modal-inner .picker-calendar-months:before,
  .picker-calendar.picker-modal-inline .toolbar~.picker-modal-inner .picker-calendar-months:before,
  .popover .picker-calendar .picker-calendar-week-days~.picker-calendar-months:before,
  .picker-calendar.picker-modal-inline .picker-calendar-week-days~.picker-calendar-months:before {
    -webkit-transform: scaleY(0.33);
    transform: scaleY(0.33);
  }
}

.picker-calendar-month-picker,
.picker-calendar-year-picker {
  display: block;
  line-height: 2.2rem;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.picker-calendar-month-picker a.icon-only,
.picker-calendar-year-picker a.icon-only {
  float: left;
  width: 25%;
  height: 2.2rem;
  line-height: 2rem;
}

.picker-calendar-month-picker .current-month-value,
.picker-calendar-year-picker .current-month-value,
.picker-calendar-month-picker .current-year-value,
.picker-calendar-year-picker .current-year-value {
  float: left;
  width: 50%;
  height: 2.2rem;
}

i.icon {
  display: inline-block;
  vertical-align: middle;
  background-size: 100% auto;
  background-position: center;
  background-repeat: no-repeat;
  font-style: normal;
  position: relative;
}

i.icon.icon-next,
i.icon.icon-prev {
  width: 0.75rem;
  height: 0.75rem;
}

i.icon.icon-next {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D'http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg'%20viewBox%3D'0%200%2015%2015'%3E%3Cg%3E%3Cpath%20fill%3D'%2304BE02'%20d%3D'M1%2C1.6l11.8%2C5.8L1%2C13.4V1.6%20M0%2C0v15l15-7.6L0%2C0L0%2C0z'%2F%3E%3C%2Fg%3E%3C%2Fsvg%3E");
}

i.icon.icon-prev {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D'http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg'%20viewBox%3D'0%200%2015%2015'%3E%3Cg%3E%3Cpath%20fill%3D'%2304BE02'%20d%3D'M14%2C1.6v11.8L2.2%2C7.6L14%2C1.6%20M15%2C0L0%2C7.6L15%2C15V0L15%2C0z'%2F%3E%3C%2Fg%3E%3C%2Fsvg%3E");
}

.weui-actionsheet {
  z-index: 10000;
}

.weui-popup__overlay,
.weui-popup__container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
}

.weui-popup__overlay {
  background-color: rgba(0, 0, 0, 0.6);
  opacity: 0;
  -webkit-transition: opacity .3s;
  transition: opacity .3s;
}

.weui-popup__container {
  display: none;
}

.weui-popup__container.weui-popup__container--visible {
  display: block;
}

.weui-popup__container .weui-cells {
  margin: 0;
  text-align: left;
}

.weui-popup__modal {
  width: 100%;
  position: absolute;
  z-index: 100;
  bottom: 0;
  border-radius: 0;
  opacity: 0.6;
  color: #3d4145;
  -webkit-transition-duration: .3s;
  transition-duration: .3s;
  height: 100%;
  background: #EFEFF4;
  -webkit-transform: translate3d(0, 100%, 0);
  transform: translate3d(0, 100%, 0);
  -webkit-transition-property: opacity, -webkit-transform;
  transition-property: opacity, -webkit-transform;
  transition-property: transform, opacity;
  transition-property: transform, opacity, -webkit-transform;
  overflow-x: hidden;
  overflow-y: auto;
}

.popup-bottom .weui-popup__modal {
  height: auto;
}

.weui-popup__modal .toolbar {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  z-index: 1;
}

.weui-popup__modal .modal-content {
  height: 100%;
  padding-top: 2.2rem;
  overflow: auto;
  box-sizing: border-box;
}

.weui-popup__container--visible .weui-popup__overlay {
  opacity: 1;
}

.weui-popup__container--visible .weui-popup__modal {
  opacity: 1;
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.weui-toptips {
  z-index: 100;
  opacity: 0;
  -webkit-transition: opacity .3s;
  transition: opacity .3s;
}

.weui-toptips.weui-toptips_visible {
  opacity: 1;
}

.weui-panel .weui-media-box__title-after {
  color: #9b9b9b;
  font-size: .65rem;
  float: right;
}

/*huangming 2018-07-13*/
.weui-picker-modal{
	height: 18em;
}
.weui-picker-container{
  touch-action: pan-y; 
}
.toolbar,.toolbar .title{font-size: 17px;line-height: 44px;box-sizing:border-box;padding: 0;margin:0;}
.weui-picker-modal .picker-items{
  font-size: 17px;
}