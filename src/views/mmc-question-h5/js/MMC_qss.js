import { QssEvent } from './MMC_qss_event';
import { api } from '../../../api/common/mmcQuestion.js';
window.HM = (function ($) {
    var Tools = function () {
        this.init();
    };

    Tools.prototype = {
        ready: function (callback) {
            $(function () {
                callback.call(this);
            });
        },
        init: function () { },
        formSelect: function (selectorStr) {
            $(selectorStr).each(function () {
                var that = $(this);
                var selectLabel = that.find(".JS-sel-label");
                var select = that.find("select");
                setValue(selectLabel, select);
                select.on("change", function () {
                    setValue(selectLabel, select);
                });
            });
            function setValue(label, select) {
                if (select.find("option:selected").text() == "请选择") {
                    return;
                }
                label.text(select.find("option:selected").text());
            }
        },
        listSelect: function (selectorStr) {
            $(selectorStr).each(function () {
                var that = $(this);
                var options = that.children("li");
                options.on("click", function () {
                    $(this).addClass("option-selected");
                    $(this).siblings("li").removeClass("option-selected");
                });
            });
        },
        listSelect: function (selectorStr) {
            $(selectorStr).each(function () {
                var that = $(this);
                var options = that.children("li");
                options.on("click", function () {
                    $(this).addClass("option-selected");
                    // $(this).siblings("li").removeClass("option-selected");
                });
            });
        },
        popWin: function (el, options) {
            var pop = new popWin(el, options);
            pop.show();
            return pop;
        }
    };

    var popWin = function (el, options) {
        this.el = el;
        this.default = {
            shadeClick: false,
        };
        this.default = $.extend(this.default, options || {});
        this.init();
    };

    popWin.prototype = {
        init: function () {
            var that = this;
            this.body = $("body");
            this.close = this.el.find(".hm-pop-close .canle");
            this.shade = this.el.find(".hm-pop-shade");
            this.pop = this.el.find(".hm-pop-block");
            this.close.on("click", function () {
                that.closeWin();
            });
            this._shadeClick();
        },
        _shadeClick: function () {
            var that = this;
            if (that.default.shadeClick) {
                that.shade.on("click", function () {
                    that.closeWin();
                });
            }
        },
        _stopMove: function () {
            this.body.css("overflow", "hidden");
            this.body.on('touchmove', function (event) { event.preventDefault(); }, false);
        },
        _doMove: function () {
            this.body.css("overflow", "auto");
            this.body.off('touchmove');
        },
        show: function () {
            this.el.css("zIndex", "99");
            this.shade.show();
            this.pop.css("transform", "translate(-50%,-90%)");
            this._stopMove();
        },
        closeWin: function () {
            this.pop.css("transform", "translate(-50%,-350%)");
            this.shade.hide();
            this.el.css("zIndex", "-100000");
            this._doMove();
        }
    };

    return new Tools();

})(jQuery || window.jQuery);
var QSS = function () {
    this.visitLevel = 1;
    this.percent = 0;
    this.groupQuestions = [];
    this.formEle = null;
    this.answers = {};
    this.current_qno = 0;//主题计数标志
    this.current_related_qno = -999999;//子题计数标志
    this.user_id = '';
    this.visit_level = 1;
    this.group = 1;
    this.groupName = '';
    this.related_questions = {};//关联题集
    this.main_question_length = 0;
    this.http_lock = false;
    this.questions = [];//当前题目列表
    this.percent = 0;//当前答题进度
    this.extra_answers = [];
    this.const = {
        UNSETANSWERID: "",//未设置答案的题目的默认答案ID
        UNSETANSWERNAME: "",//未设置答案的题目的默认答案ID
        OPTION_TYPE_CHECK: "check",//option类型 check题目   
        OPTION_TYPE_SELECT: "select",//option类型 select题目
        OPTION_TYPE_MULTI: "multi",//option类型 multi题目
        OPTION_TYPE_RESET: "reset",//option类型 reset按钮
        RELATED_INDEX_NULL: -999999,//关联题索引 无关联题
        ANSWER_LIMITER: '+-+-+-+-+-+',//答案分隔符
    };
    this.isFinished = false;
    this.editable_status = 1;
    window.a = this;
}
QSS.prototype = {
    init: function (formEle, userId, group, visitLevel, isLoadingShow, groupName, vue_this) {
        this.user_id = userId;
        this.group = group;
        this.groupName = groupName;
        this.visit_level = visitLevel;
        this.formEle = formEle;
        this.isLoadingShow = isLoadingShow
        this.vue_this = vue_this;
        this.getQss(this.user_id, this.visit_level, this.group, true);
        this._bindEvent();
        this.qssEvent = new QssEvent(this);

    },
    //获取题目
    getQss: function (userId, visitLevel, groupId, isInit) {
        
            var params = {
                // user_id: userId,
                visit_level: visitLevel,
                qss_type: groupId,
                language: localStorage.getItem('lang') == 'zh_HK' ? 'simple_tradition' : ''
            }
        var context = this;
        context._showLoading()
        api.questionList({
            ...params
            }).then((res) => {
                if (res.status === 0) {
                    context.groupQuestions = res['data'];
                    console.log(context.groupQuestions, 1234134)
                    context.percent = context.groupQuestions['qss']['percent'];
                    context.questions = context.groupQuestions['qss']['list'];
                    context.extra_answers = [];
                    //初始化时 从未完成的题目开始
                    if (isInit !== undefined) context.current_qno = context.groupQuestions['qss']['start'];
                    if (context.groupQuestions['name']) window.document.title = context.groupQuestions['name'];
                    if (context.current_qno > context.questions.length - 1) {
                        this._goComplete()
                    } else {
                        context.createForm();
                    }
                    context._removeLoading();
                } else {
                    context._tips(res.msg);
                }
            }).catch(() => {
                context._removeLoading();
                context._tips("网络错误 请稍后再试");
            })
    },

    //下一题
    nextQss: function () {
        this.editable_status = 1;
        //保存当前题目
        //this._getAnswers();
        //获取下一题
        this.getQss(this.user_id, this.visit_level, this.group);
    },
    //上一题
    prevQss: function () {
        //获取上一题
        this.getQss(this.user_id, this.visit_level, this.group);
    },
    setBasicQss: function () {

    },
    setQss: function (callback) {
        var context = this;
        var answers = context._getAllAnswers();
        var currentTopic = context._getKeyByNo(context.questions, context.current_qno);
        if (context._isSelected()) {
            //检测是否有提交的前置回调进行数据处理
            var currentQssItem = context._getKeyByNo(context.questions, context.current_qno);
            if (currentQssItem['before']) {
                answers = context._invokeCallback(currentQssItem['before'], answers);
            }
            let params = answers.reduce((acc, curr) => {
                const foundIndex = acc.findIndex(item => item.question_type === curr.question_type);
                if (foundIndex !== -1 && curr.question_type === 'multi') {
                    acc[foundIndex].answer_id.push(curr.answer_id);
                    acc[foundIndex].answer_name.push(curr.answer_name);
                } else {
                    acc.push({
                        chapter_id: curr.chapter_id,
                        template_id: curr.template_id,
                        question_id: curr.question_id,
                        subquestion_id: curr.subquestion_id,
                        question_type: curr.question_type,
                        answer_id: curr.question_type === 'multi' ? [curr.answer_id] : curr.answer_id,
                        answer_name: curr.question_type === 'multi' ? [curr.answer_name] : curr.answer_name,
                        is_sub: curr.is_sub,
                        uuid: curr.uuid
                    });
                }
                return acc;
            }, []);
            params.forEach(item => {
                if (item.question_type === 'multi') {
                    item.answer_id = item.answer_id.filter(id => id !== '');
                    item.answer_name = item.answer_name.filter(id => id !== '');
                }
                
            });   
            if (currentTopic.type == 'set') {
                params.forEach((item) => {
                    if (item.question_type == 'check' && !item.answer_name) {
                        item.answer_name = '否'
                    }
                })
            }
            if (params.some((i) => i.answer_id == 38101 || i.answer_id == 38102)) {
                let seenAnswerIds = {};
                params = params.filter(item => {
                    const { answer_id, answer_name } = item;
                    if (!(answer_id in seenAnswerIds) || (answer_id in seenAnswerIds && answer_name !== "")) {
                        seenAnswerIds[answer_id] = true; 
                        return true; 
                    }
                    return false; 
                });
            }
            if (params.some((i) => i.answer_id == '38103' && i.answer_name)) {
                params.forEach((k, index) => {
                    if (k.answer_id != 567005 && k.answer_id != 567006 && k.answer_id != 38103) {
                        k.answer_id = ''
                        k.answer_name = ''
                    }
                })
            }
            if (params.some((i) => i.answer_id == '51261' && i.answer_name == '是')){
                params.forEach((k, index) => {
                    if (k.answer_id == '51262') {
                        k.answer_name = '是'
                    }
                })
            }else if (params.some((i) => i.answer_id == '51262' && i.answer_name == '否')) {
                params.forEach((k, index) => {
                    if (k.answer_id == '51263') {
                        if (k.answer_id == '51261' && k.answer_name == '是') {
                            k.answer_id = '51264'
                            k.answer_name = '否'
                        } else {
                            k.answer_id = '51264'
                            k.answer_name = '否'
                        }
                    }
                    
                })
            }
            const hasIsAnswer = params.some(item => item.question_type === 'check' && item.answer_name === '是');
            if (hasIsAnswer) {
                const resetData = params.find(item => item.question_type === 'reset');
                if (resetData) {
                    resetData.answer_id = 0;
                }
            }
            api.questionSave(
                {
                    qss_type: this.group,
                    visit_level: this.visit_level,
                    data: params
                }
            ).then((res) => {
                if (res.status === 0) {
                    if (callback) callback.call(context);
                } else {
                    context._tips(res.msg);
                }
            }).catch(() => {
                context._tips("网络错误 请稍后再试");
            })
            // //对于多选 单选等提醒 用户选择了答案 点击下一题 才会提交答案
            // this._httpPost("/V2/Hospital/Question/save", {
            //     data: answers,
            //     user_id: context.user_id,
            //     visit_level: context.visit_level,
            //     gid: context.group
            // }, function (response) {
            //     context.isFinished = response['data']['isFinished'];
            //     if (callback) callback.call(context);
            // });
            // if (callback) callback.call(context);
        } else {
            if (callback) callback.call(context);
        }

    },
    //根据问题生成表单
    createForm: function () {

        var context = this;
        var formItem = "";
        // if(context.current_related_qno>=0){
        //     //关联题
        //     var item = this._getRelatedQss(context.current_related_qno);
        // }else{
        //     var item = this.groupQuestions['questions'][context.current_qno];
        // }
        var item = context._getKeyByNo(context.questions, context.current_qno);
        if (!item) {
            console.error("无效的题目:" + context._getKeyByNo(context.related_questions, context.current_related_qno));
            return;
        }
        var inputType = item['type'];
        var labelHtml = context._create_title(item, context.current_qno);
        //题集
        if (!this["_create_" + inputType]) {
            console.error("无效的题目类型:" + inputType);
            return;
        }
        if (item['question_id'] === 'birthplace') {
            formItem += this["_create_birthplace"](item, labelHtml);
        } else {
            console.log(inputType)
            formItem += this["_create_" + inputType](item, labelHtml);
        }

        //题目图例
        if (item['tips']) {
            $('.JS-qss-form-tips').html("<img class='tpisImg' src='" + '/static/img/'+item['tips'] + "'/>");
        } else {
            $('.JS-qss-form-tips').html("");
        }

        if (item['readme']) {
            $('.JS-qss-form-readme').html("<span> " + item['readme'] + "</span>");
        } else {
            $('.JS-qss-form-readme').html("");
        }
        if (!item.is_editable) {
            formItem = formItem.replace(/class='form-box'/g, "class='form-box no-form-box'");
        }
        this.formEle.html(formItem);
        this._onEditableClick();
        //进度
        context._changeProgress(this.percent);

        $('body').animate({ 'scrollTop': 0 });
        //初始化插件 主题为date
        $('.JS-qss-input-date').each(function () {
            var inputEle = $(this);
            var opt = {};
            opt.datetime = {};
            opt.default = {
                theme: 'default',
                display: 'bottom',
                mode: 'scroller',
                dateFormat: 'yy年mm月dd日',
                dateOrder: 'yymmdd',
                dayText: '日', monthText: '月', yearText: '年',
                lang: 'zh',
                stepMinute: 10,
                showNow: true,
                nowText: "当前",
                //startYear: currYear,
                //endYear: currYear+1,
                maxDate: new Date(),
                onSelect: function (value) {
                    //点击确定触发的事件
                    console.log(value);
                    inputEle.val(value);
                    console.log(inputEle.val())
                }
            };

            var optDateTime = $.extend(opt['datetime'], opt['default']);
            $(this).mobiscroll(optDateTime).date(optDateTime);
        });

        $('.JS-qss-input-time').each(function () {
            var inputEle = $(this);
            var opt = {};
            // var subInputQssName = $(this).data('result-name') ? $(this).data('result-name') : false;
            opt.datetime = {};
            opt.default = {
                theme: 'default',
                display: 'bottom',
                mode: 'scroller',
                timeWheels: 'HHii',
                timeFormat: 'HH:ii',
                hourText: '时',
                minuteText: '分',
                lang: 'zh',
                stepMinute: 1,
                showNow: true,
                nowText: "当前",
                // headerText: subInputQssName,
                //maxDate: new Date(),
                onSelect: function (value) {
                    //点击确定触发的事件
                    console.log(value);
                    inputEle.val(value);
                    console.log(inputEle.val())
                }
            };
            var optDateTime = $.extend(opt['datetime'], opt['default']);
            $(this).mobiscroll(optDateTime).time(optDateTime);
        });

        $('.JS-qss-input-scroll').each(function () {
            var inputEle = $(this);
            inputEle.off('click').click(function () {

                inputEle.siblings('.JS-scroll-select-box').mobiscroll('show').treelist({
                    theme: "default",
                    lang: "zh",
                    defaultValue: [Math.floor(1)],
                    cancelText: '取消',
                    headerText: function (valueText) {
                        return "请选择";
                    },
                    onSelect: function (value) {
                        $("[id^='mobiscroll'][type='text']").hide();
                        var valueInfo = value.split(context.const.ANSWER_LIMITER);
                        inputEle.val(valueInfo[0]);
                        inputEle.data('answer-name', valueInfo[1]);
                        inputEle.data('answer-id', valueInfo[0]);
                        inputEle.html(valueInfo[1])
                    }
                });
                $("[id^='mobiscroll'][type='text']").click();
            });

        });

        /* ----- 针对出生地的特殊处理 ---- */
        if (item['question_id'] == 'birthplace') {
            if (!item['birthplace_type'] || item['birthplace_type'] == 1) {
                $('#birthplace_type').val(1);
            } else {
                $('#birthplace_type').val(2);
            }
            changeBirthplaceInput(item['birthplace_type']);
        }
        $('#birthplace_type').change(function () {
            changeBirthplaceInput($(this).val());
        });
        function changeBirthplaceInput(type) {
            if (type == 2) {
                $('#birthplace-select-context').hide();
                $('#birthplace-input-context').show();
            } else {
                $('#birthplace-select-context').show();
                $('#birthplace-input-context').hide();
            }
        }
        /* ----- 针对出生地的特殊处理 ---- */

        //修复回车事件
        context._onEnterClick();
    },
    //生成题集
    _create_set: function (item) {
        console.log(item, '生成提及')
        var context = this;
        var formItem = "";
        var isMulti = item['radio'] === true ? "JS-radio" : "";
        formItem += "<div class='JS-set-box " + isMulti + "'>" + this._create_title(item, context.current_qno);
        for (var i = 0; i < item['set_ids'].length; i++) {
            var subQuestion = context._getQssQuestion(item['set_ids'][i]);
            if (!subQuestion) {
                console.error("无效的题目:" + item['set_ids'][i]);
                return;
            }
            if (!this["_create_" + subQuestion['type']]) {
                console.error("无效的题目类型:" + subQuestion['type']);
                return;
            }
            formItem += this["_create_" + subQuestion['type']](subQuestion, '', i + 1);
        }
        formItem += "</div>";
        return formItem;
    },
    //生成输入
    _create_input: function (item, labelHtml) {
        console.log(item, '生成输入')
        var inputType = item['inputtype'] ? item['inputtype'] : 'text';
        var inputPattern = inputType == 'number' ? " pattern='[0-9]*' " : "";
        var inputTypeFixed = item['input_fixed'] ? item['input_fixed'] : '';
        if (inputType == 'number') {
            if (inputTypeFixed == 'input_fixed') {
                inputPattern = " pattern='number' ";
            }
        }
        var inputPlaceHolder = (item['placeholder'] ? (" placeholder='" + item['placeholder'] + "'") : "");
        var input = "<div class='qss-input'>" +
            "<input class='JS-qss-item JS-input input-text' type=" + inputType + " name='" + item['question_id'] + "' " +
            inputPattern +
            inputPlaceHolder +
            " data-uuid='" + this._getQssUniqId() + "' " +
            this._getQssBasicAttr(item) +
            " data-answer-id='" + item['answer_id'] + "'" +
            " value='" + (item['answer_name'] !== undefined ? item['answer_name'] : '') + "'/>" +
            "<span class='JS-input-error'></span></div>";
        return "<div class='form-box'>" + labelHtml + input + "</div>";
    },
    //主题数组组合元素
    _create_input_item: function (item, labelHtml) {
        console.log(item, 'gggg')
        var inputName = item['name'] ? item['name'] : item['label'];
        var placeHolder = item['placeholder'] ? item['placeholder'] : "";
        var extraClassFlg = item['type'] == 'date' ? "JS-qss-input-date" : "";
        console.log(extraClassFlg,'dddss')
        var inputType = item['inputtype'] ? item['inputtype'] : "text";
        var unit_html = "";
        var qss_input_cls = "";
        if (item['type'] === 'time' && item['rule'] === 'time') {
            extraClassFlg += ' JS-qss-input-time'
        }
        if (item.subquestion_id === 567001 || item.subquestion_id === 567002 || item.subquestion_id === 567005 || item.subquestion_id === 567006) {
            unit_html = "<span class='unit'>"+ item['unit'] +"</span>";
            qss_input_cls = "qss-input-unit"
        }
        var inputPattern = inputType == 'number' ? " pattern='[0-9]*' " : '';
        var inputTypeFixed = item['input_fixed'] ? item['input_fixed'] : '';
        if (inputType == 'number') {
            if (inputTypeFixed == 'input_fixed') {
                inputPattern = " pattern='number' ";
            }
        }
        var input = "<div class='qss-input "+ qss_input_cls +"'>" +
            "<input class='JS-qss-item JS-input input-text " + extraClassFlg + "' type=" + inputType + " name='" + item['question_id'] + "' " +
            inputPattern +
            " data-uuid='" + this._getQssUniqId() + "' " +
            this._getQssBasicAttr(item) +
            " data-answer-id='" + item['answer_id'] + "'" +
            " placeholder='" + placeHolder + "' " +
            " value='" + ((item['answer_name'] !== undefined && item['answer_name'] !== this.const.UNSETANSWERNAME) ? item['answer_name'] : '') + "'/>" +
            unit_html +
            "<span class='JS-input-error'></span></div>";
        return "<div class='form-box'><div class='inputgroup-label'>" + inputName + ":</div>" + labelHtml + input + "</div>";
    },
    //生成输入组合
    _create_inputgroup: function (item, labelHtml) {
        console.log(item,'lllooo')
        var context = this;
        var inputHtml = "";
        var option;
        for (var optionId in item['option']) {
            option = item['option'][optionId];
            option['template_id'] = item.option[optionId]['template_id'];
            option['chapter_id'] = item.option[optionId]['chapter_id'];
            option['question_id'] = item.option[optionId]['question_id'];
            option['input_fixed'] = item.option[optionId]['input_fixed'] ? item.option[optionId]['input_fixed'] : '';
            option['subquestion_id'] = item.option[optionId]['subquestion_id'];
            option['answer_id'] = item.option[optionId]['answer_id'];
            option['type'] = option['type'] ? option['type'] : 'input';
            inputHtml += context._create_input_item(option, "");
        }
        return "<div class='form-box'>" + labelHtml + inputHtml + "</div>";
    },
    //生成时间选择插件
    _create_date: function (item, labelHtml) {
        var input = "<div class='qss-input'>" +
            "<input class='JS-qss-item JS-qss-input-date JS-input input-text' type=text name='" + item['question_id'] + "' " +
            this._getQssBasicAttr(item) +
            " data-answer-id='" + item['answer_id'] + "'" +
            " value='" + (item['answer_name'] !== undefined ? item['answer_name'] : '') + "'" +
            "' <span class='JS-input-error'></span></div>";
        return "<div class='form-box'>" + labelHtml + input + "</div>";
    },
    _create_time: function (item, labelHtml) {
        var input = "<div class='qss-input'>" +
            "<input class='JS-qss-item JS-qss-input-time JS-input input-text' type=text name='" + item['question_id'] + "' " +
            this._getQssBasicAttr(item) +
            " data-answer-id='" + item['answer_id'] + "'" +
            " value='" + (item['answer_name'] !== undefined ? item['answer_name'] : '') + "'" +
            "' <span class='JS-input-error'></span></div>";
        return "<div class='form-box'>" + labelHtml + input + "</div>";
    },
    //生成check类型题目元素
    _create_check: function (item, labelHtml, qssIndex) {
        var context = this;
        var currentTopic = context._getKeyByNo(context.questions, context.current_qno);
        var answers = this._getDefaultAnswer(item);
        if (currentTopic.type == 'set' && !item.answer_id) {
            item.option.forEach((i) => {
                if (i.answer_name == '否') {
                    item.temporarily = i.answer_id
                }
            })
            var check = "<div class='JS-qss-item JS-check-item check-box  'data-selected-id='" + (item.selected || '') + "' data-uuid='" + this._getQssUniqId() + "' name='' " + this._getQssBasicAttr(item, 'check') + " " + this._getOptionAttr(item.temporarily,answers) + " >";
        } else {
            var check = "<div class='JS-qss-item JS-check-item check-box  'data-selected-id='" + (item.selected || '') + "' data-uuid='" + this._getQssUniqId() + "' name='' " + this._getQssBasicAttr(item, 'check') + " " + this._getAnswerAttr(answers) + " >";
        }
        var option = this._create_option(item, this.const.OPTION_TYPE_CHECK, qssIndex);
        var checkEnd = "</div>";
        //var label="<label><span>"+item['no']+"、</span></label>";
        var label = "";
        return "<div class='form-box'>" + label + check + option + checkEnd + "</div>";
    },
    //生成单选题目元素
    _create_select: function (item, labelHtml) {
        var answers = this._getDefaultAnswer(item);
        var select = "<div class='JS-qss-item select-box JS-select-item' data-uuid='" + this._getQssUniqId() + "' name='' " + this._getQssBasicAttr(item) + " " + this._getAnswerAttr(answers) + " >";
        var option = this._create_option(item, this.const.OPTION_TYPE_SELECT);
        var selectEnd = "</div>";
        return "<div class='form-box'>" + labelHtml + select + option + selectEnd + "</div>";
    },
    _create_birthplace: function (item, labelHtml) {
        if (item['question_id'] != 'birthplace') {
            return '';
        }
        var answers = this._getDefaultAnswer(item);
        var select = "<ul class='JS-scroll-select-box' data-uuid='" + this._getQssUniqId() + "' name='' " + this._getQssBasicAttr(item) + " " + this._getAnswerAttr(answers) + " >";
        var options = item['option'];
        var option = "";
        var optionLabel = "";
        var value = item['answer_name'];
        var valueLabel = "";
        var callback = "";
        var answerId = "";
        var answerName = "";
        var optionValue = "";
        var tmpOption = ""
        for (var optionId in options) {
            tmpOption = options[optionId];
            answerId = tmpOption['answer_id'] ? tmpOption['answer_id'] : optionId;
            answerName = tmpOption['name'] ? tmpOption['name'] : tmpOption['label'];
            callback = tmpOption['callback'] ? tmpOption['callback'] : "";
            optionLabel = tmpOption['label'] ? tmpOption['label'] : tmpOption['name'];
            //value = answerId + this.const.ANSWER_LIMITER + answerName+this.const.ANSWER_LIMITER+options[optionId]['label']+this.const.ANSWER_LIMITER+callback;
            //value = answerName;
            optionValue = answerId + this.const.ANSWER_LIMITER + answerName;
            option += "<li data-val='" + optionValue + "'>" + optionLabel + "</li>";
            if (value == answerId) valueLabel = optionLabel;
        }
        var selectEnd = "</ul>";

        var typeSelect = "<div id='birthplace-type-select' class='qss-input'>" +
            "<select class='JS-input input-text' name='birthplace_type' id='birthplace_type'><option value='1'>国内</option><option value='2'>其他国家</option></select></div><br/>";
        
        var input = "<div id='birthplace-select-context' class='qss-input'>" +
            "<div class='JS-qss-item JS-input input-text JS-qss-input-scroll input-div' name='" + item['question_id'] + "' " +
            " data-uuid='" + this._getQssUniqId() + "' " +
            this._getQssBasicAttr(item) +
            " data-answer-id='" + item['birthplace'] + "'" +
            " data-answer-name='" + item['birthplace'] + "'" +
            " data-birthplace-type='1'" +
            " >" + valueLabel + "</div>" +
            "<span class='JS-input-error'></span>" + select + option + selectEnd + "</div>";

        var input2 = "<div id='birthplace-input-context' class='qss-input'>" +
            "<input id='birthplace-input' class='JS-qss-item JS-input input-text' name='birthplace_abroad' type='text' value='" + item['birthplace_abroad'] + "' placeholder='请输入出生地' " +
            " data-uuid='" + this._getQssUniqId() + "' " +
            this._getQssBasicAttr(item) +
            " data-answer-id='" + item['birthplace_abroad'] + "'" +
            " data-answer-name='" + item['birthplace_abroad'] + "'" +
            " data-birthplace-type='2'" +
            "></div>";

        var html = labelHtml + typeSelect + input + input2;
        return "<div class='form-box'>" + html + "</div>";
    },
    //生成主题 下拉选择元素
    _create_scroll: function (item, labelHtml) {
        var answers = this._getDefaultAnswer(item);
        var select = "<ul class='JS-scroll-select-box' data-uuid='" + this._getQssUniqId() + "' name='' " + this._getQssBasicAttr(item) + " " + this._getAnswerAttr(answers) + " >";
        var options = item['option'];
        var option = "";
        var optionLabel = "";
        var value = item['answer_name'];
        var valueLabel = "";
        var callback = "";
        var answerId = "";
        var answerName = "";
        var optionValue = "";
        var tmpOption = ""
        for (var optionId in options) {
            tmpOption = options[optionId];
            answerId = tmpOption['answer_id'] ? tmpOption['answer_id'] : optionId;
            answerName = tmpOption['name'] ? tmpOption['name'] : tmpOption['label'];
            callback = tmpOption['callback'] ? tmpOption['callback'] : "";
            optionLabel = tmpOption['label'] ? tmpOption['label'] : tmpOption['name'];
            //value = answerId + this.const.ANSWER_LIMITER + answerName+this.const.ANSWER_LIMITER+options[optionId]['label']+this.const.ANSWER_LIMITER+callback;
            //value = answerName;
            optionValue = answerId + this.const.ANSWER_LIMITER + answerName;
            option += "<li data-val='" + optionValue + "'>" + optionLabel + "</li>";
            if (value == answerId) {
                valueLabel = optionLabel;
            } else {
                valueLabel = value
            }
        }
        var selectEnd = "</ul>";
        var input = "<div class='qss-input'>" +
            "<div class='JS-qss-item JS-input input-text JS-qss-input-scroll input-div' name='" + item['question_id'] + "' " +
            " data-uuid='" + this._getQssUniqId() + "' " +
            this._getQssBasicAttr(item) +
            " data-answer-id='" + item['answer_id'] + "'" +
            " data-answer-name='" + item['answer_name'] + "'" +
            " >" + valueLabel + "</div>" +
            "<span class='JS-input-error'></span>" + select + option + selectEnd + "</div>";

        //var label=this._create_title(item['no'],item['name'],item['progress'],item['type']);
        return "<div class='form-box'>" + labelHtml + input + "</div>";
    },
    //生成多选
    _create_multi: function (item, labelHtml) {
        var answers = this._getDefaultAnswer(item);
        var select = "<div class=' multi-box JS-multi-item' data-uuid='" + this._getQssUniqId() + "' name='' " + this._getQssBasicAttr(item) + " " + this._getAnswerAttr(answers) + " >";
        var option = this._create_option(item, this.const.OPTION_TYPE_MULTI);
        var selectEnd = "</div>";
        return "<div class='form-box'>" + labelHtml + select + option + selectEnd + "</div>";
    },
    //生成select子题
    _create_subselect: function (item) {

    },
    //生成select的option
    _create_option: function (item, type, qssIndex) {
        if (type == this.const.OPTION_TYPE_MULTI) {
            return this._create_multi_option(item);
        }
        var options = item['option'];
        var selectedFlg = "";
        var hiddenFlg = "";
        var subFlg = "";
        var subItem = "";
        var option = {};
        var optionType = "JS-" + type + "-option";
        var optionTypeBox = "JS-" + type + "-option-box";
        var optionStr = "<div class='" + optionTypeBox + "'>";
        var optionLabel = "";
        var optionCallback = "";
        var answerId = "";
        var answerName = "";
        var opUUID = "";
        var relatedQids = "";
        var label = "";
        var index = 0;
        var isSelected = false;
        var class_vegetables_fruits_tips = ''
        var vegetables_fruits_first_option = ''
        for (var optionId in options) {
            isSelected = false;
            option = options[optionId];
            opUUID = this._getQssUniqId();
            answerId = item['option_answer_id'] ? item['option_answer_id'] : option['answer_id'];

            answerName = option['name'] ? option['name'] : option['label'];
            if (item['selectedName']) {
                //根据selectedName标识选中项
                selectedFlg = (item['answer_name'] == item['selectedName'] && item['answer_name'] == answerName) ? "JS-selected" : "";
            } else {
                //根据selectedId标识选中项
                selectedFlg = answerId && (item['answer_id'] == answerId || (item['answer_id'] === undefined && item['default'] && item['default'] == answerId)) ? "JS-selected" : "";
            }
            if (item['type'] == this.const.OPTION_TYPE_CHECK || item['type'] == this.const.OPTION_TYPE_RESET) {
                if (item['selectedName']) {
                    hiddenFlg = item['selectedName'] == answerName ? "" : "JS-option-hidden";
                } else {
                    hiddenFlg = item['selected'] == answerId ? "" : "JS-option-hidden";
                }
                optionLabel = item['name'];
            } else {
                hiddenFlg = "";
                optionLabel = option['label'] ? option['label'] : option['name'];
            }
            //若无答案 则默认是隐藏项被选择
            if (item['selectedName']) {
                if (hiddenFlg && (item['answer_name'] === undefined || item['answer_name'] == '' || item['answer_name'] == answerName) && !selectedFlg && item['default'] === undefined) {
                    selectedFlg = "JS-selected";
                }
            } else {
                if (hiddenFlg && (item['answer_id'] === undefined || item['answer_id'] == '') && !selectedFlg && item['default'] === undefined) {
                    selectedFlg = "JS-selected";
                }
            }

            // 蔬菜水果特殊处理
            var class_vegetables_fruits = { cls: '', tips: '', disabled: 0 }
            if (item.question_id === 978 || item.question_id === 979) {
                class_vegetables_fruits = this._getVegetablesFruitsCls(item, options[optionId])
            }
            if (class_vegetables_fruits.tips) {
                class_vegetables_fruits_tips = class_vegetables_fruits.tips
            }
            if (class_vegetables_fruits.cls !== 'hide-this-option') {
                index++;
            }
            if (optionId == 0 && (item.question_id === 978 || item.question_id === 979)) {
                vegetables_fruits_first_option = 'vegetables-fruits-first-option'
            }
            // console.warn('class_vegetables_fruits: ', class_vegetables_fruits, optionId)

            isSelected = selectedFlg && !hiddenFlg;
            subFlg = option['sub'] ? "JS-option-sub-item" : "";
            subItem = option['sub'] ? ("  data-sub-question='" + option['sub'] + "'") : "";
            relatedQids = option['related'] ? JSON.stringify(option['related']) : "";
            label = "<label class='option-qss-no'><span class='num'>" + (qssIndex !== undefined ? qssIndex : index) + "、</span>" + optionLabel + "</label>";
            optionCallback = option['callback'] ? (" data-callback='" + option['callback'] + "'") : "";
            //label =this._create_title(item['no'],optionLabel,item['progress'],item['type']);
            var subQssStr = "";
            if (subFlg) {
                //生成子题元素
                var subQuestion = this._getQssQuestion(option['sub']);
                console.log(subQuestion, 'subQuestion');
                subQssStr += this._create_sub_qss(subQuestion, option, opUUID, isSelected, item['answer_id'],answerId);
            }
            optionStr += ("<div class='JS-option " + class_vegetables_fruits.cls + " " + hiddenFlg + " " + selectedFlg + " " + subFlg + " " + optionType + "' " +
                " data-uuid='" + opUUID + "'" +
                " data-selected-id='" + (item.selected || '') + "'" +
                optionCallback +
                (relatedQids ? " data-related='" + relatedQids + "'" : "") +
                subItem +
                this._getOptionAnswerAttr(answerId, answerName) +
                ">" + label + subQssStr + "</div>");
        }
        // optionStr += "</div>";
        if (class_vegetables_fruits_tips) {
            optionStr = optionStr.replace(optionTypeBox, `${optionTypeBox} ${vegetables_fruits_first_option}`)
        }
        optionStr += `${class_vegetables_fruits_tips}</div>`;
        return optionStr;
    },
    //生成子题元素
    _create_sub_qss: function (item, parentOption, parentUUID, isParentSelected, parentId, selectId) {
        var subQssStr = "<div class='JS-sub-box' data-parent='" + parentUUID + "'>";
        var subQuesstion = this._getQssQuestion(parentOption['sub']);
        var subResultHtml = "";
        var option = "";
        if (!subQuesstion) {
            console.error("无效的题目:" + parentOption['sub']);
            return false;
        }
        //输入组
        var templateId = item['template_id'];
        var chapterId = item['chapter_id'];
        var questionId = item['question_id'];
        //构造输入元素
        var inputQuestion = { 'template_id': templateId, 'question_id': questionId, 'type': 'input', 'chapter_id': chapterId };
        if (subQuesstion['type'] == 'inputgroup') {
            var inputGroups = subQuesstion['option'];
            for (var optionId in inputGroups) {
                option = inputGroups[optionId];
                inputQuestion['subquestion_id'] = option['subquestion_id'];
                inputQuestion['template_id'] = option['template_id'];
                inputQuestion['chapter_id'] = option['chapter_id'];
                if (option['question_id'] !== undefined) inputQuestion['question_id'] = option['question_id'];
                var subAnswers = { id: option['answer_id'], name: option['answer_name'] };
                if (subAnswers['id'] !== this.const.UNSETANSWERID && subAnswers['name'] !== this.const.UNSETANSWERNAME) {
                    //睡眠时间答案显示fix
                    var _optionUnit = option['unit'];
                    if (subAnswers['id'] == '567001') {
                        // _optionUnit = (_optionUnit + inputGroups['567002']["answer_name"] + inputGroups['567002']["unit"]);
                    }
                    //睡眠时间 分钟答案  已经合并到上一个显示
                    if (subAnswers['id'] != '567002') {
                        // subResultHtml += this._getSubResHtml(option['label'], subAnswers['name'], _optionUnit);
                    }
                    //subResultHtml += ("<div class='JS-sub-result'>["+subAnswers['name']+":"+option['label']+"]</div>");
                }
                
                subQssStr += ("<div class='JS-qss-item JS-option-hidden'" +
                    " data-uuid='" + this._getQssUniqId() + "'" +
                    " data-parent='" + parentUUID + "'" +
                    this._getQssBasicAttr(inputQuestion) +
                    this._getAnswerAttr(subAnswers, parentId == selectId, option['question_id']) +
                    "></div>")
            }
        }
        if (subQuesstion['type'] == 'inputgroup') {
            var subAnswers = this._getDefaultAnswer(subQuesstion);
            //子题答案显示
            // console.error('subQuesstion: ', subQuesstion)
            /* `inputgroup` 类型题目处理
            var question_id = subQuesstion['question_id']
            if (question_id === 518) { // 肿瘤信息特殊处理
                subResultHtml = this._getSubResHtmlInputGroup(subQuesstion);
            } else { // 其它题目走老逻辑
                subResultHtml = (subAnswers['id'] == this.const.UNSETANSWERID || subAnswers['name'] == this.const.UNSETANSWERNAME) ? "" : this._getSubResHtml(subQuesstion['name'], subAnswers['name'], subAnswers['unit']);
            }
            */
            subResultHtml = this._getSubResHtmlInputGroup(subQuesstion);
            // subResultHtml = (subAnswers['id'] == this.const.UNSETANSWERID || subAnswers['name'] == this.const.UNSETANSWERNAME) ? "" : this._getSubResHtml(subQuesstion['name'], subAnswers['name'], subAnswers['unit']);
            subQssStr += "<div class='JS-qss-item JS-option-hidden' " +
                " data-parent='" + parentUUID + "'" +
                this._getQssBasicAttr(subQuesstion) +
                this._getAnswerAttr(subAnswers, parentId == selectId, option['question_id']) + ">" +
                "</div>";
        } else {
            var subAnswers = this._getDefaultAnswer(subQuesstion);
            //子题答案显示
            subResultHtml = (subAnswers['id'] == this.const.UNSETANSWERID || subAnswers['name'] == this.const.UNSETANSWERNAME) ? "" : this._getSubResHtml(subQuesstion['name'], subAnswers['name'], subAnswers['unit']);
            subQssStr += "<div class='JS-qss-item JS-option-hidden' " +
                " data-parent='" + parentUUID + "'" +
                this._getQssBasicAttr(subQuesstion) +
                this._getAnswerAttr(subAnswers, parentId == selectId, option['question_id']) + ">" +
                "</div>";
        }
        if (!isParentSelected) subResultHtml = "";
        subQssStr += subResultHtml;
        return subQssStr + "</div>";
    },
    _create_multi_option: function (item) {
        var type = this.const.OPTION_TYPE_MULTI;
        var options = item['option'];
        var selectedFlg = "";
        var hiddenFlg = "";
        var subFlg = "";
        var subItem = "";
        var option = {};
        var optionType = "JS-" + type + "-option";
        var optionTypeBox = "JS-" + type + "-option-box";
        var optionStr = "<div class='" + optionTypeBox + "'>";
        var optionLabel = "";
        var optionCallback = "";
        var answerId = "";
        var answerName = "";
        var selectedAnswerId = "";
        var selectedAnswerName = "";
        var opUUID = "";
        var relatedQids = "";
        var label = "";
        var itemAnswers = [];
        var index = 0;
        var isSelected = false;
        for (var optionId in options) {
            index++;
            option = options[optionId];
            opUUID = this._getQssUniqId();
            itemAnswers = item['answer_id'] ? item['answer_id'] : [];

            answerId = option['answer_id'];

            if (option['type'] !== undefined && option['question_id']) {
                //选项是个题目 目前支持者check类型
                var optionQssItem = option;
                answerName = option['answer_name'];
                selectedFlg = (answerId !== undefined && answerId == option['selected']) ? "JS-selected" : "";
                selectedAnswerId = option['selected'];
                selectedAnswerName = option['option'][selectedAnswerId]['label'];
            } else {
                var optionQssItem = item;
                // answerId = item['option_answer_id'] ? item['option_answer_id'] : (optionId);
                answerName = option['name'] ? option['name'] : option['label'];
                selectedFlg = ($.inArray(String(answerId), itemAnswers) >= 0 || (item['answer_id'] === undefined && item['default'] && item['default'] == answerId)) ? "JS-selected" : "";
                selectedAnswerId = answerId;
                selectedAnswerName = answerName;
            }
            if (item['type'] == this.const.OPTION_TYPE_RESET) {
                hiddenFlg = item['selected'] == answerId ? "" : "JS-option-hidden";
                optionLabel = item['name'];
            } else {
                hiddenFlg = "";
                optionLabel = option['label'] ? option['label'] : option['name'];
            }
            isSelected = selectedFlg && !hiddenFlg;
            subFlg = option['sub'] ? "JS-option-sub-item" : "";
            subItem = option['sub'] ? ("  data-sub-question='" + option['sub'] + "'") : "";
            relatedQids = option['related'] ? JSON.stringify(option['related']) : "";
            label = "<label class='option-qss-no'><span class='num'>" + index + "、</span>" + optionLabel + "</label>";
            optionCallback = option['callback'] ? (" data-callback='" + option['callback'] + "'") : "";
            //label =this._create_title(item['no'],optionLabel,item['progress'],item['type']);
            var subQssStr = "";
            if (subFlg) {
                //生成子题元素
                subQssStr += this._create_sub_qss(item, option, opUUID, isSelected);
            }
            if (option['type'] == this.const.OPTION_TYPE_RESET) {
                var optionAnswerId = (selectedFlg || option['answer_id']) ? answerId : this.const.UNSETANSWERID;
                var optionAnswerName = (selectedFlg || option['answer_id']) ? answerName : this.const.UNSETANSWERNAME;
            } else {
                var optionAnswerId = selectedFlg ? answerId : this.const.UNSETANSWERID;
                var optionAnswerName = selectedFlg ? answerName : this.const.UNSETANSWERNAME;
            }
            optionStr += ("<div class='JS-qss-item JS-option " + hiddenFlg + " " + selectedFlg + " " + subFlg + " " + optionType + "' " +
                " data-uuid='" + opUUID + "'" +
                " data-selected-answer-id='" + selectedAnswerId + "'" +
                " data-selected-answer-name='" + selectedAnswerName + "'" +
                this._getQssBasicAttr(optionQssItem) +
                optionCallback +
                (relatedQids ? " data-related='" + relatedQids + "'" : "") +
                subItem +
                this._getOptionAnswerAttr(optionAnswerId, optionAnswerName) +
                ">" + label + subQssStr + "</div>");
        }
        optionStr += "</div>";
        return optionStr;
    },
    //生成清除答案的元素
    _create_reset: function (item, labelHtml, qssIndex) {
        var check = "<div class='JS-qss-item  JS-reset-box JS-check-item check-box' data-uuid='" + this._getQssUniqId() + "' name='' " + this._getQssBasicAttr(item, 'check') + " " + this._getAnswerAttr({
            id: item['answer_id'],
            name: item['answer_name']
        }) + " >";
        var option = this._create_option(item, this.const.OPTION_TYPE_CHECK, qssIndex);
        var checkEnd = "</div>";
        var label = "";
        return "<div class='form-box'>" + label + check + option + checkEnd + "</div>";
    },
    _create_title: function (item,index) {
        var lang = localStorage.getItem('lang') == 'zh_HK' ? 'zh_HK' : ''
        var no = index + 1
        var type = item['type'];
        var name = item['name'];
        var qType = "";
        if (type == 'set') {
            qType = item['radio'] === true ? (lang ? '單選題' : "单选题") : (lang ? '多选題' : "多选题") ;
        }
        if (type == 'multi') {
            qType = lang ? '多选題' : "多选题";
        }
        if (type == 'select' || type == 'scroll') {
            qType = lang ? '單選題' : "单选题";
        }
        if (type == 'input' || type == 'inputgroup') {
            //return "<div class='qss-title'>"+name+"</div>";
            qType = lang ? '填空題' : "填空题";
        }
        if (type == 'date') {
            qType = lang ? '填空題' : "填空题";
        }
        var html = "<div class='title-box'>" +
            "<label class='qss-no'><span class='num'>" + no + "、</span><span class='qss-progress'>" + (lang ? '填寫進度' : "填写进度")+":" + (this.percent) + "%<div class='qss-progress-bar-box'><div class='qss-progress-bar'></div></div></span></label> " +
            "</div>" +
            "<div class='qss-title'><label>" + qType + "</label>" + name  + "</div>";
        return html
    },
    //生成子题输入表单
    _create_sub_input: function (item) {
        var unit = (item['unit'] ? ("<div class='JS-sub-input-unit'>" + item['unit']) + "</div>" : "");
        var inputClass = "";
        if (unit) {
            inputClass = " JS-sub-input input-unit-pre";
        } else {
            inputClass = " JS-sub-signle-input";
        }
        var placeHolder = item['placeholder'] ? item['placeholder'] : "";
        var inputType = item['inputtype'] ? item['inputtype'] : 'text';
        var inputPattern = inputType == 'number' ? " pattern='[0-9]*' " : "";
        var inputTypeFixed = item['input_fixed'] ? item['input_fixed'] : '';
        if (inputType == 'number') {
            if (inputTypeFixed == 'input_fixed') {
                inputPattern = " pattern='number' ";
            }
        }
        var input = "<div class='qss-input'>" +
            "<input class='JS-input input-text " + inputClass + "' type=" + inputType + " name='" + item['question_id'] + "' " + " placeholder='" + placeHolder + "' " +
            inputPattern +
            this._getQssBasicAttr(item) + " data-answer-id='" + item['answer_id'] + "' >" + unit + "<span class='JS-sub-input-error'></div>";
        return "<div class='form-box'>" + input + "</span></div>";
    },
    //生成子题输入组
    _create_sub_inputgroup: function (item) {
        var inputOptions = item['option'];
        var input = "<div class='qss-input input-group-box'>";
        var option = "";
        var inputType = "";
        var placeholder = "";
        //所有答案
        var allAnswers = this._getAllAnswers();
        var value = "";
        for (var optionId in inputOptions) {
            value = '';
            option = inputOptions[optionId];
            if (option['question_id']) item['question_id'] = option['question_id'];
            item['subquestion_id'] = option['subquestion_id'];
            item['rule'] = option['rule'];
            item['input_fixed'] = option['input_fixed'] ? option['input_fixed'] : '';
            item['unit'] = option['unit'];
            for (var i = 0; i < allAnswers.length; i++) {
                if (allAnswers[i]['answer_name'] != this.const.UNSETANSWERNAME && allAnswers[i]['template_id'] == item['template_id'] && allAnswers[i]['question_id'] == item['question_id'] && allAnswers[i]['subquestion_id'] == item['subquestion_id']) {
                    value = allAnswers[i]['answer_name'];
                }
            }
            placeholder = option['placeholder'] ? option['placeholder'] : "";
            inputType = option['inputtype'] ? option['inputtype'] : "text";
            var inputPattern = inputType == 'number' ? " pattern='[0-9]*' " : "";
            var inputTypeFixed = item['input_fixed'] ? item['input_fixed'] : '';
            if (inputType == 'number') {
                if (inputTypeFixed == 'input_fixed') {
                    inputPattern = " pattern='number' ";
                }
            }

            var unit = (item['unit'] ? ("<div class='JS-sub-input-unit'>" + item['unit']) + "</div>" : "");
            var dateInput = "";
            if (option['type'] !== undefined) {
                if (option['type'] == 'date') dateInput = "JS-input-group-sub-date";
                if (option['type'] == 'time') dateInput = "JS-input-group-sub-time";
                if (option['type'] == 'select') dateInput = "JS-input-group-sub-select";
            }
            input += ("<div class='JS-input-sub-label'>" + (option['label'] ? option['label'] + ':' : '&nbsp;') + "</div><input class='JS-input " + dateInput + "  JS-sub-input input-text " + (unit ? "input-unit-pre" : "") + "' " +
                "type=" + inputType + " name='" + option['answer_id'] + "' " +
                inputPattern +
                this._getQssBasicAttr(item) +
                " data-answer-id='" + option['answer_id'] + "' " +
                " placeholder='" + placeholder + "'" +
                " data-result-name='" + option['label'] + "'" +
                " value='" + value + "'" +
                ">" + unit + "<div class='JS-sub-input-error'></div>");
        }
        return input + "</div>";
    },
    //子题下拉
    _create_sub_select: function (item) {
        var answers = this._getDefaultAnswer(item);
        var select = "<ul class='JS-sub-select-box' data-uuid='" + this._getQssUniqId() + "' name='' " + this._getQssBasicAttr(item) + " " + this._getAnswerAttr(answers) + " >";
        var options = item['option'];
        var option = "";
        var optionLabel = "";
        var value = "";
        var callback = "";
        var answerId = "";
        var answerName = "";
        var tmpOption =""
        for (var optionId in options) {
            tmpOption = options[optionId];
            answerId = tmpOption['answer_id'] ? tmpOption['answer_id'] : optionId;
            answerName = tmpOption['name'] ? tmpOption['name'] : tmpOption['label'];
            callback = tmpOption['callback'] ? tmpOption['callback'] : "";
            optionLabel = tmpOption['label'] ? tmpOption['label'] : tmpOption['name'];
            value = answerId + this.const.ANSWER_LIMITER + answerName + this.const.ANSWER_LIMITER + tmpOption['label'] + this.const.ANSWER_LIMITER + callback;
            option += "<li data-val='" + value + "'>" + optionLabel + "</li>";
        }
        var selectEnd = "</ul>";
        //var label=this._create_title(item['no'],item['name'],item['progress'],item['type']);
        return "<div class='form-box'>" + select + option + selectEnd + "</div>";
    },
    //子题时间选择控件
    _create_sub_date: function (item) {
        var answers = this._getDefaultAnswer(item);
        var input = "<ul class='JS-sub-date-box' data-uuid='" + this._getQssUniqId() + "' name='' " + this._getQssBasicAttr(item) + " " + this._getAnswerAttr(answers) + " ></div>";
        return item;
    },
    _create_sub_date1: function (item) {
        var answers = this._getDefaultAnswer(item);
        var input = "<ul class='JS-sub-date-box' data-uuid='" + this._getQssUniqId() + "' name='' " + this._getQssBasicAttr(item) + " " + this._getAnswerAttr(answers) + " ></div>";
        return item;
    },
    _getQssItemNum: function () {
        var main_num = this.current_qno;
        var sub_num = this.current_related_qno == this.const.RELATED_INDEX_NULL ? "" : ("-" + (this.current_related_qno + 1));
        return main_num + "" + sub_num;
    },
    _getQssBasicAttr: function (item) {
        var dataStr = " data-template-id='" + item['template_id'] + "'" +
            " data-chapter-id='" + item['chapter_id'] + "'" +
            " data-question-type='" + item['type'] + "'" +
            " data-question-id='" + item['question_id'] + "'" +
            " data-question-no='" + item['no'] + "'" +
            " data-sub-question-id='" + item['subquestion_id'] + "' ";
        if (item['rule']) {
            dataStr += (" data-rule='" + item['rule'] + "'");
        }
        if (item['unit']) {
            dataStr += (" data-unit='" + item['unit'] + "'");
        }
        return dataStr;
    },
    _getAnswerAttr: function (answers, selectId, question_id) {
        console.log(answers,'saipaio')
        if (question_id == 561) {
            if (selectId) {
                return " data-answer-id='" + answers.id + "' data-answer-name='" + answers.name + "' data-answer-value='" + answers.value + "'";
            } else {
                return " data-answer-id='" + '' + "' data-answer-name='" + '' + "' data-answer-value='" + answers.value + "'";
            }
        } else {
            return " data-answer-id='" + answers.id + "' data-answer-name='" + answers.name + "' data-answer-value='" + answers.value + "'";
        }
    },
    _getOptionAttr: function (id,answers) {
        return " data-answer-id='" + id + "' data-answer-name='" + answers.name + "' data-answer-value='" + answers.value + "'";
    },
    _getOptionAnswerAttr: function (answerId, answerName) {
        return (" data-answer-id='" + answerId + "' data-answer-name='" + answerName + "'");
    },
    _httpPost: function (url, params, successCallback, errorCallback) {
        var context = this;
        if (context.http_lock) {
            //context._tips("正在处理中...");
            return false;
        }
        context.http_lock = true;
        context._showLoading();
        $.ajax({
            type: "post",
            url: url,
            data: params,
            success: function (response) {
                context.http_lock = false;
                context._removeLoading();
                if (successCallback) {
                    successCallback.call(context, response);
                }
            },
            timeout: 10000,
            error: function (err) {
                context.http_lock = false;
                context._removeLoading();
                if (errorCallback) {
                    errorCallback.call(context, err);
                } else {
                    context._tips("网络错误 请稍后再试");
                }
            },
        });
    },
    _bindEvent: function () {
        this._onSelectOptionSelect();
        this._onNextQss();
        this._onPrevQss();
        this._onCheckOptionSelect();
        this._onMultiOptionSelect();
    },
    _onEditableClick() {
        var context = this;
        if (this.editable_status) {
            this.editable_status = 0
            $('body').delegate('.JS-qss-form .no-form-box', 'click', function () {
                var lang = localStorage.getItem('lang') == 'zh_HK' ? 'zh_HK' : ''
                var msg = lang ? '該疾病資訊已由醫生核對，如有調整請至醫生處修改' : "该疾病信息已由医生核对，如有调整请至医生处修改";
                
                context.vue_this.$toast(msg);
            })
        }
    },
    //题目下拉选择事件
    _onSelectOptionSelect: function () {
        var context = this;
        $('body').delegate('.JS-select-option', 'click', function () {
            // if (Array.from(this.classList).includes('disabled-this-option')) return false;
            var clickObj = $(this);
            var checkItem = $(this).parents('.JS-select-item');
            var optionSelected = checkItem.find('.JS-selected');
            var optionUnSelected = checkItem.find('.JS-select-option').not('.JS-selected');


            var questionNo = checkItem.data('question-no');
            if (clickObj.hasClass('JS-selected')) {
                //有子题 需要编辑子题
                if (clickObj.hasClass('JS-option-sub-item')) {
                    //创建子题选择
                    context._showSubQss($(this), $(this).data('sub-question'), function () {
                        //子题完成后 父题答案变更
                        //optionSelected.removeClass('JS-selected');
                        //clickObj.addClass('JS-selected');
                        optionSelected = checkItem.find('.JS-selected');
                        checkItem.data('answer-id', optionSelected.data('answer-id'));
                        checkItem.data('answer-name', optionSelected.data('answer-name'));
                    }, function () {
                        //取消选择 nothing
                        console.log('nothing');
                    });
                }
                return false;
            }

            //选项会触发子题 只有子题填写完成 本题才会更改完成
            if (clickObj.hasClass('JS-option-sub-item') && !clickObj.hasClass('JS-selected')) {
                //创建子题选择
                context._showSubQss($(this), $(this).data('sub-question'), function () {
                    //子题完成后 父题答案变更
                    //cancel 移除子题答案
                    if (optionSelected.hasClass('JS-option-sub-item')) {
                        context._clearSubQss(optionSelected.data('uuid'));
                    }
                    //cancel 清除关联题集
                    if (optionSelected.data('related')) {
                        context._clearRelatedQss(questionNo, optionSelected.data('related'));
                    }
                    //select 添加关联题集
                    if (optionUnSelected.data('related')) {
                        context._addRelatedQss(questionNo, optionUnSelected.data('related'));
                    }
                    optionSelected.removeClass('JS-selected');
                    clickObj.addClass('JS-selected');
                    optionSelected = checkItem.find('.JS-selected');
                    checkItem.data('answer-id', optionSelected.data('answer-id'));
                    checkItem.data('answer-name', optionSelected.data('answer-name'));
                }, function () {
                    //取消选择 nothing
                    console.log('nothing');
                });
            } else {
                //无子题触发
                //cancel 移除子题答案
                if (optionSelected.hasClass('JS-option-sub-item')) {
                    context._clearSubQss(optionSelected.data('uuid'));
                }
                //cancel 清除关联题集
                if (optionSelected.data('related')) {
                    context._clearRelatedQss(questionNo, optionSelected.data('related'));
                }
                //select 添加关联题集
                if (optionUnSelected.data('related')) {
                    context._addRelatedQss(questionNo, optionUnSelected.data('related'));
                }
                optionSelected.removeClass('JS-selected');
                $(this).addClass('JS-selected');
                optionSelected = checkItem.find('.JS-selected');
                checkItem.data('answer-id', optionSelected.data('answer-id'));
                checkItem.data('answer-name', optionSelected.data('answer-name'));
                if (optionSelected.data('callback')) {
                    //选择后 还有JS回调事件
                    context._invokeCallback(optionSelected.data('callback'))
                }
                //context._chooseOption($(this));
            }
        });
        return;
    },
    _onMultiOptionSelect: function () {
        var context = this;
        $('body').delegate('.JS-multi-option', 'click', function () {
         
            //必须要选一个 不能全部取消
            var isSelected = $(this).hasClass('JS-selected') && $(this).is(':visible');//当前已选中
            var isOtherSelected = $(this).parents('.JS-multi-option-box').find('div.JS-option.JS-multi-option.JS-selected:visible').length == 1;//当前是唯一一个选中的
            if (isSelected && isOtherSelected) {//禁止取消
                return;
            }

            //已选以上皆无  选中其他项 恢复以上皆无项选中状态
            //var hasNoAll=$(this).parents('.JS-multi-option-box').find('label:contains("以上皆无")').length > 0;
            //var isNotNoAll=$(this).find('label:contains("以上皆无")').length==0;//
            var hasResetBtn = $(this).parents('.JS-multi-option-box').find('[data-callback^="reset_"]').length > 0;
            var isResetBtn = $(this).find('[data-callback^="reset_"]').length == 0;//
            var resetOption = $(this).siblings('[data-callback^="reset_"]');

            //选项变更
            context._multiOptionChange($(this));

            //reset选项按钮处理
            if (!isSelected && isResetBtn && hasResetBtn) {//选中的操作 当前不是以上皆无  其他项有以上皆无  则恢复以上皆无为未选中
                //var div=$(this).parents('.JS-multi-option-box').find('label:contains("以上皆无")').parent();
                //var div = $(this).parents('.JS-multi-option-box').find('[data-callback^="reset_"]');
                //置空reset的答案
                if (resetOption.hasClass('JS-selected')) {
                    resetOption.click();
                    //resetOption.removeClass('JS-selected');
                }
            }

        });
    },
    _onCheckOptionSelect: function () {
        var context = this;
        var lang = localStorage.getItem('lang') == 'zh_HK' ? 'zh_HK' : ''
        var nameCom = lang ? '以上皆無' : '以上皆无'

        $('body').delegate('.JS-check-option', 'click', function () {
            //必须要选一个 不能全部取消
            var isSelected = $(this).hasClass('JS-selected') && $(this).is(':visible');//当前已选中
            var isOtherSelected = $(this).parents('.JS-set-box').find('div.JS-option.JS-check-option.JS-selected:visible').length == 1;//当前是唯一一个选中的
            if (isSelected && isOtherSelected) {//禁止取消
                var checkItem = $(this).parents('.JS-check-item');
                context._checkOptionChange(checkItem, false,'');
                return;
            }
            //已选以上皆无  选中其他项 恢复以上皆无项选中状态
            var hasNoAll = $(this).parents('.JS-set-box').find("label:contains(" + nameCom +")").length > 0;
            var isNotNoAll = $(this).find("label:contains(" + nameCom +")").length == 0;//
            if (!isSelected && isNotNoAll && hasNoAll) {//选中的操作 当前不是以上皆无  其他项有以上皆无  则恢复以上皆无为未选中
                var div = $(this).parents('.JS-set-box').find("label:contains(" + nameCom +")").parent();
                div.removeClass('JS-selected');
                div.next().removeClass('JS-selected');
            }
            //点击以上皆无 恢复其他已选中项
            if (!$(this).hasClass('JS-selected') && $(this).find("label:contains(" + nameCom +")").length) {
                var callback = $(this).data('callback');
                callback = callback ? callback : 'reset_option';
                context._invokeCallback(callback);
                $(this).addClass('JS-selected');
                $(this).next().addClass('JS-selected');
                return;
            }

            var setBox = $(this).parents('.JS-set-box');
            var checkItem = $(this).parents('.JS-check-item');
            //判断是否题集 且是否是单选
            if (setBox.length > 0 && setBox.hasClass('JS-radio')) {
                //清除其他题目的答案
                setBox.find('.JS-check-item').each(function () {
                    context._checkOptionChange($(this), true, checkItem.data('selectedId'));
                });
            }
            context._checkOptionChange(checkItem, null, checkItem.data('selectedId'));
        });
    },
    //回车事件响应
    _onEnterClick: function () {
        $('.JS-input').bind('keydown', function (event) {
            if (event.keyCode == '13') {
                //回车
                return false;
            }
        });
        this.formEle.submit(function () {
            return false;
        });
    },
    _checkOptionChange: function (qssItemObj, isCancel,selectedId) {
        var context = this;
        var checkItem = qssItemObj;
        var checkOptionBox = checkItem.children('.JS-check-option-box');
        if (isCancel) {
            var optionSelected = checkOptionBox.children(':not(.JS-option-hidden)');
            var optionUnSelected = checkOptionBox.children('.JS-option-hidden');
        } else if (isCancel === false) {
            //重复点击 子题显示
            var optionSelected = checkOptionBox.children('.JS-selected');
            var optionUnSelected = optionSelected.length == 0 ? checkOptionBox.children('.JS-option-hidden') : checkOptionBox.children('.JS-check-option').not('.JS-selected');
            if (optionSelected.hasClass('JS-option-sub-item')) {
                //创建子题选择
                context._showSubQss(checkItem, optionSelected.data('sub-question'), function () {
                    //子题完成
                    checkItem.data('answer-id', optionSelected.data('answer-id'));
                    checkItem.data('answer-name', optionSelected.data('answer-name'));
                    //已选择项是否有回调处理
                    if (optionSelected.data('callback')) {
                        context._invokeCallback(optionSelected.data('callback'), optionSelected);
                    }
                }, function () {
                    //取消选择 nothing
                    console.log('nothing');
                });
            }
            return;
        } else {
            var optionSelected = checkOptionBox.children('.JS-selected');
            var optionUnSelected = optionSelected.length == 0 ? checkOptionBox.children('.JS-option-hidden:first') : checkOptionBox.children('.JS-check-option').not('.JS-selected');
        }
        var questionNo = checkItem.data('question-no');
        //移除子题答案
        if (optionSelected.hasClass('JS-option-sub-item')) {
            context._clearSubQss(optionSelected.data('uuid'));
        }
        // if(optionSelected.data('related')){
        //     //清除关联题集
        //     context._clearRelatedQss(questionNo,optionSelected.data('related'));
        // }
        // if(optionUnSelected.data('related')){
        //     //添加关联题集
        //     context._addRelatedQss(questionNo,optionUnSelected.data('related'));
        // }
        if (optionSelected.hasClass('JS-option-hidden') && optionSelected.parents('.JS-reset-box').length == 0) {
            //有选项被选择 需要置空reset按钮
            var resetSelected = $('.JS-reset-box .JS-selected');
            if (!resetSelected.hasClass("JS-option-hidden")) {
                resetSelected.click();
            }
        }
        //选项会触发子题 只有子题填写完成 本题才会更改完成
        if (optionUnSelected.hasClass('JS-option-sub-item')) {
            //创建子题选择
            context._showSubQss(checkItem, optionUnSelected.data('sub-question'), function () {
                //子题完成
                if (checkItem.data('selectedId') && checkItem.data('selectedId') == 51262) {
                    optionSelected.parent().parent().parent().parent().find('.JS-qss-item').find('.JS-option').removeClass('JS-selected')
                } else {
                    optionSelected.removeClass('JS-selected');
                }
                optionUnSelected.addClass('JS-selected');
                console.log('checkItem: ', checkItem)
                optionSelected = checkItem.find('.JS-selected');
                checkItem.data('answer-id', optionSelected.data('answer-id'));
                checkItem.data('answer-name', optionSelected.data('answer-name'));
                //已选择项是否有回调处理
                if (optionSelected.data('callback')) {
                    context._invokeCallback(optionSelected.data('callback'), optionSelected);
                }
            }, function () {
                //取消选择 nothing
                console.log('nothing');
            });
        } else {
            //无子题触发
            if (selectedId && (selectedId == '51261' || selectedId == '51263' || selectedId == '51262')) {
                var t1 = checkItem.parent().find('.JS-qss-item').attr('data-selected-id') != '51262'
                var t2 = selectedId == '51261' || selectedId == '51263'
                if (t1 && t2) {
                    optionSelected.removeClass('JS-selected');
                }
                if (checkItem.parent().find('.JS-qss-item').attr('data-selected-id') == '51262' && t2) {
                    optionSelected.removeClass('JS-selected');
                }
            } else {
                optionSelected.removeClass('JS-selected');
            }
            optionUnSelected.addClass('JS-selected');
            optionSelected = checkItem.find('.JS-selected');
            checkItem.data('answer-id', optionSelected.data('answer-id'));
            checkItem.data('answer-name', optionSelected.data('answer-name'));
            if (optionSelected.data('callback')) {
                //选择后 还有JS回调事件
                context._invokeCallback(optionSelected.data('callback'), optionSelected);
            }
            //context._chooseOption($(this));
        }
    },
    _multiOptionChange: function (qssItemObj, isCancel) {
        var checkItem = qssItemObj;
        var option = qssItemObj;
        var questionNo = checkItem.data('question-no');
        //reset按钮
        var resetOption = qssItemObj.siblings('[data-callback^="reset_"]');
        var context = this;
        if (isCancel) {
            //取消选择
            if (!option.hasClass('JS-selected')) return;
        }
        if (isCancel === false && option.hasClass('JS-selected')) {
            //非取消 选中后 重复点击 子题弹框编辑
            if (option.hasClass('JS-option-sub-item')) {
                //创建子题选择
                context._showSubQss(option, option.data('sub-question'), function () {
                    //子题完成后 父题答案变更
                    option.addClass('JS-selected');
                    checkItem.data('answer-id', option.data('selected-answer-id'));
                    checkItem.data('answer-name', option.data('selected-answer-name'));
                }, function () {
                    //取消选择 nothing
                    console.log('nothing');
                });
            }
            return;
        }
        if (option.hasClass('JS-selected')) {
            //cancel
            //移除子题答案
            if (option.hasClass('JS-option-sub-item')) {
                context._clearSubQss(option.data('uuid'));
            }
            if (option.data('related')) {
                //清除关联题集
                //context._clearRelatedQss(questionNo,option.data('related'));
            }
            if (option.data('related')) {
                //添加关联题集
                //context._addRelatedQss(questionNo,option.data('related'));
            }
            option.removeClass('JS-selected');
            checkItem.data('answer-id', context.const.UNSETANSWERID);
            checkItem.data('answer-name', context.const.UNSETANSWERNAME);
        } else {
            //select
            //选项会触发子题 只有子题填写完成 本题才会更改完成
            if (option.hasClass('JS-option-sub-item')) {
                //创建子题选择
                context._showSubQss(option, option.data('sub-question'), function () {
                    //子题完成后 父题答案变更
                    option.addClass('JS-selected');
                    checkItem.data('answer-id', option.data('selected-answer-id'));
                    checkItem.data('answer-name', option.data('selected-answer-name'));
                    //reset按钮触发
                    if (resetOption.length > 0 && resetOption.hasClass('JS-selected')) {
                        resetOption.click();
                    }
                }, function () {
                    //取消选择 nothing
                    console.log('nothing');
                });
            } else {
                //无子题触发
                option.addClass('JS-selected');
                checkItem.data('answer-id', option.data('selected-answer-id'));
                checkItem.data('answer-name', option.data('selected-answer-name'));
                if (option.data('callback')) {
                    //选择后 还有JS回调事件
                    context._invokeCallback(option.data('callback'), option);
                    option.addClass('JS-selected');
                }
                //context._chooseOption($(this));
            }
        }
    },
    //下一题
    _onNextQss: function () {
        var context = this;
        $('.JS-qss-next').off('click').click(function () {
            console.log('next-page:');
            var item = context._getKeyByNo(context.questions, context.current_qno);
            if (context._validate() !== true) return false;
            //save
            if (!item.is_editable) {
                context.current_qno++;
                context.nextQss();
            } else {
                context.setQss(function () {
                    context.current_qno++;
                    context.nextQss();
                    return;
                });
            }
        });
    },
    //上一题
    _onPrevQss: function () {
        var context = this;
        $('.JS-qss-prev').off('click').click(function () {
            console.log('prev-page');
            if (context.current_qno > 0) {
                context.current_qno--;
                context.prevQss();
            } else {
                //回首页
                context._goHome();
                return;
            }
        });
    },
    //判断是否选择了答案 只有选择了答案的题目才会提交 否则下一题 不会提交
    _isSelected: function () {
        //当前题目为set集合 必须有一个选中的 才会保存答案 否则跳过
        if ($('.JS-qss-form .JS-set-box').length > 0) {
            return $('.JS-selected:visible').length >= 1;
        }

        //多选
        if ($('.JS-multi-option-box').length > 0) {
            return $('.JS-selected:visible').length >= 1;
        }

        //单选
        if ($('.JS-select-option-box').length > 0) {
            return $('.JS-selected:visible').length >= 1;
        }

        // 输入 或输入选择类型 的主题 需要选择或者填写答案才会保存
        // var answers = this._getAllAnswers();
        // var questionType = "";
        // for(var i=0;i<answers.length;i++){
        //     questionType=answers[i]['question_type'];
        //     if(!answers[i]['is_sub']&&(questionType=='scroll'||questionType=='input')){
        //         if(answers[i]['answer_id']==''||answers[i]['answer_name']==''||answers[i]['answer_name']==this.const.UNSETANSWERNAME){
        //             return false;
        //         }
        //     }
        // }

        return true;
    },
    //子题选择确认
    _subConfirm: function (pannel, subQuestion, parentQssEle) {
        var context = this;
        var subInputType = subQuestion['type'];
        var subBox = parentQssEle.find('.JS-sub-box');
        if (subInputType == 'select' || subInputType == 'date' || subInputType == 'date1') {
            //如果选项有回调
            if (pannel['answer-callback']) {
                context._invokeCallback(pannel['answer-callback'], subBox);
                return false;
            }
            var subEle = subBox.find('.JS-qss-item');
            subEle.data('answer-id', pannel['answer-id']);
            subEle.data('answer-name', pannel['answer-name']);
            context._showSubResult(subQuestion, [{ 'name': subQuestion['name'], 'value': pannel['answer-name'] }], subBox);
        }
        if (subInputType == 'input') {
            pannel.find('.JS-sub-input-error').html("");
            var subEle = subBox.find('.JS-qss-item');
            var inputEle = pannel.find('.JS-input');
            //validate
            if (inputEle.data('rule')) {
                var checkMsg = context._validateSingle(inputEle.val(), inputEle.data('rule'));
                if (checkMsg !== true) {
                    pannel.find('.JS-sub-input-error').html(checkMsg);
                    return false;
                }
            }
            //单位显示
            var unit = inputEle.data('unit');
            subEle.data('answer-id', inputEle.data('answer-id'));
            subEle.data('answer-name', inputEle.val());
            context._showSubResult(subQuestion, [{ name: subQuestion['name'], value: inputEle.val(), unit: unit }], subBox);
            //if(pannel)pannel.remove();
        }

        if (subInputType == 'inputgroup') {
            var results = [];
            var checkStatus = true;
            // pannel.find('.JS-input').each(function (index) {
            pannel.find('.JS-input').not('.dw-hsel').each(function (index) {
                //睡眠子题临时处理


                //无效 --
                // var qid = subQuestion['question_id'];
                // if (qid == '567' || qid == '561') {
                //     var subQuestionId = $(this).data('sub-question-id');
                //     inputQssItem = $('[data-question-id="' + qid + '"][data-sub-question-id="' + subQuestionId + '"]');
                // } else {
                //     var inputQssItem = subBox.find('.JS-qss-item').eq(index);
                // }
                var inputQssItem = subBox.find('.JS-qss-item').eq(index);
                //validate
                if ($(this).data('rule')) {
                    var checkMsg = context._validateSingle($(this).val(), $(this).data('rule'));
                    if (checkMsg !== true) {
                        pannel.find('.JS-sub-input-error').eq(index).html(checkMsg);
                        checkStatus = false;
                        return false;
                    } else {
                        pannel.find('.JS-sub-input-error').eq(index).html("");
                    }
                }
                inputQssItem.data('answer-id', $(this).data('answer-id'));
                inputQssItem.data('answer-name', $(this).val());
                results.push({
                    'name': $(this).data('result-name'),
                    'value': $(this).val(),
                    'unit': $(this).data('unit')
                });
            });
            if (!checkStatus) return false;
            context._showSubResult(subQuestion, results, subBox);
        }
        return true;
    },
    //子题取消
    _subCancel: function (pannel, subQuestion, parentQssEle) {
        var context = this;
        if (!pannel) return;
        //移除选中的子题
        // pannel.find('.JS-selected').each(function(){
        //     context._removeOption($(this));
        // });
        //移除弹框
        pannel.remove();
    },
    //显示子题答题面板
    _showSubQss: function (parentQssEle, qid, subConfirmCallback, subCancelCallback, customeQssType) {
        var context = this;
        //获取题目定义
        var subQuestion = $.extend(true, {}, context._getQssQuestion(qid));
        var qssType = customeQssType !== undefined ? customeQssType : subQuestion['type'];
        subQuestion['type'] = qssType;
        //创建题目表单html
        var createMethod = "_create_sub_" + qssType;
        var formHtml = context[createMethod](subQuestion);
        //面板显示
        context._showPannel(subQuestion, subQuestion['name'], formHtml, qssType, function (pannel, popObj) {
            //子题确认 子题答案变更同步
            if (context._subConfirm(pannel, subQuestion, parentQssEle)) {
                //子题确认后 父题的回调逻辑
                if (subConfirmCallback) subConfirmCallback.call(context);
                if (popObj && popObj['closeWin'] !== undefined) popObj.closeWin();
            }
        }, function (pannel, popObj) {
            //取消选择
            if (subCancelCallback) subCancelCallback.call(context);
            context._subCancel(pannel, subQuestion, parentQssEle);
        })
        //子题事件处理
        $('.JS-input-group-sub-date').each(function () {
            var inputEle = $(this);
            var opt = {};
            var currYear = new Date().getFullYear();
            var subInputQssName = $(this).data('result-name') ? $(this).data('result-name') : false;
            opt.datetime = {};
            opt.default = {
                theme: 'default',
                display: 'bottom',
                mode: 'scroller',
                dateFormat: 'yy年mm月',
                dateOrder: 'yymm',
                monthText: '月', yearText: '年',
                lang: 'zh',
                stepMinute: 10,
                showNow: true,
                nowText: "当前",
                headerText: subInputQssName,
                //endYear:currYear,
                maxDate: new Date(),
                //startYear: currYear,
                //endYear: currYear+1,
                onSelect: function (value) {
                    //点击确定触发的事件
                    console.log(value);
                    inputEle.val(value);
                    console.log(inputEle.val())
                }
            };

            var optDateTime = $.extend(opt['datetime'], opt['default']);
            $(this).mobiscroll(optDateTime).date(optDateTime);
        });
        //
        $('.JS-input-group-sub-time').each(function () {
            // var inputEle = $(this);
            // var opt={};
            // var subInputQssName=$(this).data('result-name')?$(this).data('result-name'):false;
            // opt.datetime = {};
            //
            // inputEle.datetimePicker({
            //     title: subInputQssName,
            //     //time: true,
            //     value: "2018-12-23",
            //     onChange: function (picker, values, displayValues) {
            //         //console.log(values);
            //         inputEle.val(values);
            //     },
            //     //自定义解析初始值
            //
            // });
            // var u = navigator.userAgent;
            // var isAndroid = u.indexOf('Android') > -1 || u.indexOf('Adr') > -1;
            // if(isAndroid){
            var inputEle = $(this);
            var opt = {};
            var currYear = new Date().getFullYear();
            var subInputQssName = $(this).data('result-name') ? $(this).data('result-name') : false;
            opt.datetime = {};
            opt.default = {
                theme: 'default',
                display: 'bottom',
                mode: 'scroller',
                timeWheels: 'HHii',
                timeFormat: 'HH:ii',
                hourText: '时',
                minuteText: '分',
                lang: 'zh',
                stepMinute: 1,
                showNow: true,
                nowText: "当前",
                headerText: subInputQssName,
                //maxDate: new Date(),
                onSelect: function (value) {
                    //点击确定触发的事件
                    console.log(value);
                    inputEle.val(value);
                    console.log(inputEle.val())
                }
            };
            var optDateTime = $.extend(opt['datetime'], opt['default']);
            $(this).mobiscroll(optDateTime).time(optDateTime);
            // }
            // else{
            //     return false
            // }
        });
        $('.JS-input-group-sub-select').each(function (i, el) {
            // console.log('index:', i, ' / el:', el)
            // console.log('context: ', context)
            // console.log('subQuestion: ', subQuestion)
            var defaultSelection = ''
            var subQuestionId = el.dataset.subQuestionId
            var list = subQuestion.option.filter(item => {
                // console.log('item: ', item)
                item.subquestion_id == subQuestionId
                if (item.subquestion_id == subQuestionId) {
                    return item
                }
            })
            console.log('list: ', list)
            console.warn($(el).attr('data-result-name'))
            list[0].option.forEach(item => {
                item.text = item.label
                item.value = item.answer_id

                if (list[0].answer_id == item.answer_id) {
                    defaultSelection = item.text
                }
            })
            console.log('defaultSelection: ', defaultSelection)
            var opt = {
                theme: "default",
                lang: "zh",
                // onSet: function (ev, inst) {
                //     div.setVal('');
                // },
                defaultSelection,
                data: list[0].option,
                // data: [{ value: 55, text: '鄂城' },
                //     { value: 56, text: '梁子湖' },
                //     { value: 57, text: '华容' }],
                cancelText: '取消',
                inputClass: 'JS-input JS-input-group-sub-select  JS-sub-input input-text',
                headerText: function (valueText) {
                    return list[0].name;
                },
                // onCancel: function (valueText, inst) {
                //     if (cancelCallback) cancelCallback.call(context, pannel);
                // },
                onSelect: function (valueText, inst) {
                    $(el).prev().attr('data-answer-id', inst._tempValue);
                }
            };
            $(this).mobiscroll().select(opt);
            console.warn('$(this)', $(this).mobiscroll().select(opt))
            console.warn($(el).attr('data-result-name'))
            console.log($(el).prev()[0], $(el).prev()[0].value);
            $(el).prev()[0].value = '';
            // $(el).prev()[0].value = $(el).val();
            $(el).prev().attr('data-result-name', $(el).attr('data-result-name'));
        });
    },
    //显示子题结果
    _showSubResult: function (subQuestion, results, subBox) {
        subBox.find('.JS-sub-result').remove();
        //中午睡眠时间答案显示fix
        var lang = localStorage.getItem('lang') == 'zh_HK' ? 'zh_HK' : ''
        var hour = lang ? '小時' : '小时'
        var evening = lang ? '晚上從' : '晚上从'
        var day = lang ? '一天內睡覺累計' : '一天内睡觉累计'
        if (subQuestion.question_id === 567 && subQuestion.option.length === 4) {
            results[0]['unit'] = (hour + results[1]['value'] + results[1]['unit']);

            results[2].name = evening
            results[2].value = results[2].value + '睡到' + results[3].value
            results[2].unit = ''

            delete results[1];
            delete results[3];
        }
        if (subQuestion.question_id === 567 && subQuestion.option.length === 2) {
            results[0].name = day
            results[0].unit = (hour + results[1].value + results[1].unit)
            delete results[1]
        }
        console.log(results)
        console.log(subBox)
        for (var i in results) {
            console.log(results, 123455431)
            if (results[i]['value']) { // 有值才回显
                subBox.append(this._getSubResHtml(results[i]['name'], results[i]['value'], results[i]['unit']));
            }
            // subBox.append(this._getSubResHtml(results[i]['name'], results[i]['value'], results[i]['unit']));
        }
    },
    //获取子题显示html
    _getSubResHtml: function (answerName, questionName, answerUnit) {
        return "<div class='JS-sub-result'>[" + answerName + ":" + questionName + (answerUnit ? answerUnit : "") + "]</div>";
    },
    _getSubResHtmlInputGroup(subQuesstion) {
        var res = []
        var option = JSON.parse(JSON.stringify(subQuesstion.option))

        var lang = localStorage.getItem('lang') == 'zh_HK' ? 'zh_HK' : ''
        var hour = lang ? '小時' : '小时'
        var evening = lang ? '晚上從' : '晚上从'
        var day = lang ? '一天內睡覺累計' : '一天内睡觉累计'

        if (option[0].subquestion_id === 567001) {
            option[0].unit = (hour + option[1].answer_name + option[1].unit)

            if (option[2].subquestion_id === 567003) {
                option[2].label = evening
                option[2].answer_name = option[2].answer_name + '睡到' + option[3].answer_name
                option[2].unit = ''
            }

            delete option[1]
            delete option[3]
        }
        if (option[0].subquestion_id === 567005) {
            option[0].label = day
            option[0].unit = (hour + option[1].answer_name + option[1].unit)
            delete option[1]
        }
        option.forEach(item => {
            if (item.answer_name) {
                res.push({
                    label: item.label,
                    answer_name: item.answer_name,
                    unit: item.unit,
                })
            }
        })
        if (res.length > 0) {
            var str = ''
            res.forEach(item => {
                let label = item.label ? item.label + ':' : ''
                let value = item.answer_name || ''
                let unit = item.unit || ''
                str += `<div class="JS-sub-result">[${label}${value}${unit}]</div>`;
            })
            return str
        }
        return ''
    },
    //获取子题对象
    _getQssQuestion: function (qid) {
        return this.groupQuestions['subquestions'][qid];
    },
    //显示答题面板
    _showPannel: function (subQuestion, title, html, type, confirmCallback, cancelCallback) {
        var lang = localStorage.getItem('lang') == 'zh_HK' ? 'zh_HK' : ''
        var context = this;
        if (type == 'select') {
            var pannel = "";
            var htmlObj = $(html).find('ul');
            htmlObj.mobiscroll('show').treelist({
                theme: "default",
                lang: "zh",
                defaultValue: [Math.floor(2)],
                cancelText: '取消',
                setText: lang ? '確認' : "确认",
                headerText: function (valueText) {
                    return title;
                },
                onCancel: function (valueText, inst) {
                    if (cancelCallback) cancelCallback.call(context, pannel);
                },
                onSelect: function (valueText, inst) {
                    var answers = valueText.split(context.const.ANSWER_LIMITER);
                    var pannel = {
                        'answer-id': answers[0],
                        'answer-name': answers[1],
                        'answer-label': answers[2],
                        'answer-callback': answers[3]
                    };
                    if (confirmCallback) confirmCallback.call(context, pannel);
                }
            }).click();
        } else if (type == 'date') {
            var pannel = "";
            //var htmlObj = $(html).find('ul');
            //var inputEle = $(this);
            var currYear = new Date().getFullYear();
            var opt = {};
            opt.datetime = {};
            opt.default = {
                theme: 'default',
                display: 'bottom',
                mode: 'scroller',
                dateFormat: 'yy年mm月',
                dateOrder: 'yymm',
                monthText: '月', yearText: '年',
                lang: 'zh',
                stepMinute: 10,
                showNow: true,
                nowText: "当前",
                headerText: title ? title : false,
                //startYear: currYear,
                //endYear: currYear,
                maxDate: new Date(),
                onSelect: function (value) {
                    //点击确定触发的事件
                    console.log(value);
                    var pannel = {
                        'answer-id': html['answer_id'],
                        'answer-name': value,
                    };
                    console.log(pannel);
                    if (confirmCallback) confirmCallback.call(context, pannel);
                }
            };

            var optDateTime = $.extend(opt['datetime'], opt['default']);
            $("<div class=''>xx</div>").mobiscroll('show').date(optDateTime).click();
        } else if (type == 'date1') {
            var pannel = "";
            var currYear = new Date().getFullYear();
            var opt = {};
            opt.datetime = {};
            opt.default = {
                theme: 'default',
                display: 'bottom',
                mode: 'scroller',
                dateFormat: 'yy年mm月dd日',
                dateOrder: 'yymmdd',
                dayText: '日', monthText: '月', yearText: '年',
                lang: 'zh',
                stepMinute: 10,
                showNow: true,
                nowText: "当前",
                headerText: title ? title : false,
                maxDate: new Date(),
                onSelect: function (value) {
                    //点击确定触发的事件
                    console.log(value);
                    var pannel = {
                        'answer-id': html['answer_id'],
                        'answer-name': value,
                    };
                    console.log(pannel);
                    if (confirmCallback) confirmCallback.call(context, pannel);
                }
            };
            var optDateTime = $.extend(opt['datetime'], opt['default']);
            $("<div class=''>xx</div>").mobiscroll('show').date(optDateTime).click();
        }
        else {
            var top_subquestion_ids = [567004]
            var cls = top_subquestion_ids.includes(subQuestion.subquestion_id) ? 'more' : ''
            var popBox = '<div class="hm-pop JS-pop JS-pannel-box" data-type="' + type + '">' +
                '<div class="hm-pop-shade"></div>' +
                '  <div class="hm-pop-block ' + cls + '">' +
                '  <div class="hm-pop-head">' + title + '</div>' +
                '  <div class="hm-pop-body">' +
                html +
                '  </div>' +
                '  <div class="hm-pop-close">' +
                '    <a href="javascript:void(0);" class="canle JS-pannel-button-cancel">' + (lang ? '取消' : "取消") +'</a>' +
                '    <a href="javascript:void(0);" class="sure JS-pannel-button-confirm">' + (lang ? '確認' : "确认") +'</a>' +
                '     </div>' +
                '     </div>' +
                '  ' +
                '</div>';

            $('.JS-pannel-box').remove();
            var pannel = $(popBox);
            $('body').append(pannel);
            var popObj = HM.popWin(pannel, { shadeClick: false });
            //关闭 取消事件
            pannel.find('.JS-pannel-button-cancel').click(function () {
                if (cancelCallback) cancelCallback.call(context, pannel, popObj);
            });
            pannel.find('.JS-pannel-button-confirm').click(function () {
                if (confirmCallback) confirmCallback.call(context, pannel, popObj);
            });
        }
    },
    //选择答案
    _chooseOption: function (selectItemEle) {
        var answerInfo = {};
        answerInfo['template_id'] = selectItemEle.data('template-id');
        answerInfo['chapter_id'] = selectItemEle.data('chapter-id');
        answerInfo['question_id'] = selectItemEle.data('question-id');
        answerInfo['subquestion_id'] = selectItemEle.data('sub-question-id');
        answerInfo['answer_id'] = selectItemEle.data('answer-id');
        answerInfo['answer_name'] = selectItemEle.data('answer-name');
        console.log('add answer:' + selectItemEle.data('answer-name'));
        selectItemEle.addClass('JS-selected');
    },
    //移除答案
    _removeOption: function (selectItemEle) {
        var answerInfo = {};
        answerInfo['template_id'] = selectItemEle.data('template-id');
        answerInfo['chapter_id'] = selectItemEle.data('chapter-id');
        answerInfo['question_id'] = selectItemEle.data('question-id');
        answerInfo['subquestion_id'] = selectItemEle.data('sub-question-id');
        answerInfo['answer_id'] = selectItemEle.data('answer-id');
        answerInfo['answer_name'] = selectItemEle.data('answer-name');
        selectItemEle.removeClass('JS-selected');
        //如果选项有子题 同时移除子题
        var subQuestionId = selectItemEle.data('sub-question');
        console.log(subQuestionId);
        console.log($(".JS-qss-item[data-parent='" + selectItemEle.data('uuid') + "']"));
        if (subQuestionId) {
            $(".JS-qss-item[data-parent='" + selectItemEle.data('uuid') + "']").show();
        }
    },
    //获取问题的答案
    _getDefaultAnswer: function (question) {
        var answerId = this.const.UNSETANSWERID;
        var answerName = this.const.UNSETANSWERNAME;
        var answerUnit = question['unit'];
        if (question['answer_id'] === undefined) {
            answerId = this.const.UNSETANSWERID;
            anserwerName = this.const.UNSETANSWERNAME;
        } else {
            answerId = question['answer_id'];
            answerName = question['answer_name'];
        }
        /*
        if (question.question_id === 518) { // 肿瘤题目特殊处理
            question.option.forEach(item => {
                if (item.answer_id) {

                }
            })
        }
        */
        //下拉或多选
        return {
            id: answerId,
            name: answerName,
            value: answerName,
            unit: answerUnit
        }
    },
    //获取当前页面问题答案
    _getAllAnswers: function () {
        var answers = [];
        var indexAnswers = {};
        var context = this;
        $('.JS-qss-item').each(function () {
            // 针对【出生地】逻辑的特殊处理
            if ($(this).data('question-id') == 'birthplace') {
                if ($(this).data('birthplace-type') != $('#birthplace_type').val()) {
                    return true;
                }
            }
            var is_sub = $(this).parents('.JS-sub-box').length == 0 ? false : true;
            var iputType = $(this).data('question-type');
            //输入类型 answer_name=input.val
            if ((iputType == 'input' || iputType == 'date' || iputType == 'time' || iputType == 'scroll') && $(this).parents('.JS-sub-box').length == 0) {
                var answer_name = $(this).is('input') ? $(this).val() : $(this).data('answer-name');
            } else {
                var answer_name = $(this).data('answer-name');
            }
            //无效--
            // var subQssId = $(this).data('sub-question-id');
            // if ($(this).data('question-id') == '567' && (subQssId == '567003' || subQssId == '567004') && answer_name) {
            //     //睡眠时间 中文分号切换修正
            //     answer_name = (answer_name + "").replace("：", ":");
            // }
            let answer_id = $(this).data('answer-id')
            let subquestion_id = $(this).data('sub-question-id')
            //无效
            // if ((subQssId == '518002' || subQssId == '518007' || subQssId == '518050' || subQssId == '518013' || subQssId == '518020'
            //     || subQssId == '518027' || subQssId == '518034' || subQssId == '518040' || subQssId == '518045'
            //     || subQssId == '518055')) {
            //     answers.push({
            //         'template_id': $(this).data('template-id'),
            //         'question_id': $(this).data('question-id'),
            //         'subquestion_id': subQssId,
            //         'question_type': $(this).data('question-type'),
            //         'answer_id': subQssId,
            //         'answer_name': '',
            //         'is_sub': is_sub,
            //         'uuid': $(this).data('uuid')
            //     });
            //     //睡眠时间 中文分号切换修正
            //     if (answer_name == '肝脏') {
            //         answer_name = 1
            //     } else if (answer_name == '胰腺') {
            //         answer_name = 2
            //     } else if (answer_name == '胃') {
            //         answer_name = 3
            //     } else if (answer_name == '结直肠') {
            //         answer_name = 4
            //     } else if (answer_name == '乳腺') {
            //         answer_name = 5
            //     } else if (answer_name == '宫颈') {
            //         answer_name = 6
            //     } else if (answer_name == '卵巢') {
            //         answer_name = 7
            //     } else if (answer_name == '子宫内膜') {
            //         answer_name = 8
            //     } else if (answer_name == '前列腺') {
            //         answer_name = 9
            //     } else if (answer_name == '膀胱') {
            //         answer_name = 10
            //     } else if (answer_name == '肾脏') {
            //         answer_name = 11
            //     } else if (answer_name == '淋巴瘤') {
            //         answer_name = 12
            //     } else if (answer_name == '白血病') {
            //         answer_name = 13
            //     } else if (answer_name == '肺部') {
            //         answer_name = 14
            //     } else if (answer_name == '甲状腺') {
            //         answer_name = 15
            //     } else {
            //         answer_id = subQssId + 2
            //         subquestion_id = subQssId + 2
            //     }
            // }

            var data = {
                'chapter_id': $(this).data('chapter-id'),
                'template_id': $(this).data('template-id'),
                'question_id': $(this).data('question-id'),
                'subquestion_id': subquestion_id,
                'question_type': $(this).data('question-type'),
                'answer_id': answer_id,
                'answer_name': answer_name,
                'is_sub': is_sub,
                'uuid': $(this).data('uuid')
            };
            if ($(this).data('rule') !== undefined) {
                data['rule'] = $(this).data('rule');
            }
            // 针对出生地特殊处理
            /* 无效--
            // if ($(this).data('question-id') == 'birthplace') {
            //     data['birthplace_type'] = $(this).data('birthplace-type');
            // }
            // if ($(this).data('question-type') == 'multi') {
            //     data['option_answer_id'] = $(this).data('selected-answer-id');
            // }
            // if ($(this).data('rule') !== undefined) {
            //     data['rule'] = $(this).data('rule');
            // }
            */
            var key = data['template_id'] + '-' + data['question_id'] + data['subquestion_id'];
            if (indexAnswers[key] === undefined) {
                answers.push(data);
                indexAnswers[key] = data;
            } else {
                if (indexAnswers[key]['answer_id'] == context.const.UNSETANSWERID) {
                    answers.push(data);
                    indexAnswers[key] = data;
                } else {
                    if (data['option_answer_id'] || data['answer_id'] != context.const.UNSETANSWERID) {
                        answers.push(data);
                        indexAnswers[key] = data;
                    }
                }

            }
        });
        var extraAnswers = context.extra_answers || {};
        answers = answers.concat(extraAnswers);
        //遍历多选题目
        return answers;
    },
    // _parseQuestions: function () {
    //     var questions = {};
    //     for (var i = 0; i < this.groupQuestions['questions'].length; i++) {
    //         var question = this.groupQuestions['questions'][i];
    //         if (question['question_id']) {
    //             var qid = question['template_id'] + '-' + question['question_id'] + question['subquestion_id'];

    //         }
    //     }
    // },
    //生成每个题目的UUID
    _getQssUniqId: function () {
        return Math.random() * 100000000000;
    },
    //清除子题答案
    _clearSubQss: function (parentUUID) {
        var subQssItem = $(".JS-qss-item[data-parent='" + parentUUID + "']");
        subQssItem.data('answer-id', this.const.UNSETANSWERID);
        subQssItem.data('answer-name', this.const.UNSETANSWERNAME);
        subQssItem.siblings('.JS-sub-result').remove();
    },
    //清除关联题集
    _clearRelatedQss: function (qno, relatedQuestions) {
        //清除题目答案
        for (var key in this.related_questions) {
            if (key.substr(0, 2) == qno + '-') {
                delete this.related_questions[key];
            }
        }
    },
    //添加关联题集
    _addRelatedQss: function (qno, relatedQuestions) {
        console.log(qno)
        console.log('添加关联题')
        for (var i = 0; i < relatedQuestions.length; i++) {
            this.related_questions[qno + '-' + i] = relatedQuestions[i];
        }
        if (this.current_related_qno == this.const.RELATED_INDEX_NULL) this.current_related_qno = -1;
    },
    //根据顺序获取元素
    _getKeyByNo: function (object, No) {
        var step = 0;
        for (var key in object) {
            if (step == No) break;
            step++;
        }
        return object[key];
    },
    //获取关联子题
    _getRelatedQss: function (related_qno) {
        var qid = this._getKeyByNo(this.related_questions, related_qno);
        //var qid = this.related_questions[key];
        return this._getQssQuestion(qid);
    },
    _getNextRelatedQssNo: function () {
        if (this.current_related_qno == this.const.RELATED_INDEX_NULL) {
            return false;
        }

    },
    /*
    // _getObjLength: function (obj) {
    //     var i = 0;
    //     for (var key in obj) {
    //         i++;
    //     }
    //     return i;
    // },
    //关联题是否填写完毕
    // _isRelatedQssOver: function () {
    //     return true;
    //     var relatedQssLength = this._getObjLength(this.related_questions);
    //     if (relatedQssLength == 0) {
    //         //无关联题
    //         this.current_related_qno = this.const.RELATED_INDEX_NULL;
    //         return true;
    //     }
    //     if (this.current_related_qno != this.const.RELATED_INDEX_NULL && this.current_related_qno + 1 < relatedQssLength) {
    //         return false;
    //     }
    //     return true;
    // },
    //主题是否完毕
    // _isMainQssOver: function () {
    //     // if(this.current_qno<this._getObjLength(this.groupQuestions['questions'])){
    //     //     return false;
    //     // }
    //     if (this.current_qno + 1 < this._getObjLength(this.questions)) {
    //         return false;
    //     }
    //     return true;
    // },
    */
    //提示
    _tips: function (msg) {
        var html = (
            "<div class='QSS-TIPS-BOX' style='width:100%;height:0.8rem;padding:0;position: fixed;bottom:1.6rem;z-index:9999999;overflow:hidden;'>" +
            "<div class='QSS-TIPS-BG' style='width:50%;position:relative;left:25%;background:rgba(0,0,0,0.65);border-radius:0.5rem;height:100%;'></div>" +
            "<div class='QSS-TIPS-MSG' style='position:absolute;top:0;left:25%;width:50%;height:100%;text-align:center;line-height:0.8rem;color:white;font-size:0.32rem;'>" + msg + "</div>" +
            "</div>"
        );
        $('.QSS-TIPS-BOX').remove();
        $('body').append(html);
        setTimeout(function () {
            $('.QSS-TIPS-BOX').remove();
        }, 1200);
    },
    //回首页
    _goHome: function () {
        window.location.href = "/mmc/question/index";
    },
    //完成答题页面
    _goComplete: function () {
        this._changeProgress(this.progress);
        // window.location.href = "/v2/question?user_id=" + this.user_id + "&complete=1&group=" + this.group + "&visit_level=" + this.visit_level;
        window.location.href = `/mmc/question/middleStatusPage?visit_level=${this.visit_level}&qss_type=${this.group}&user_id=${this.user_id}&groupName=${this.groupName}`
    },

    _invokeCallback: function (event, params) {
        if (event.indexOf('=') >= 0) {
            //回调存在参数
            var eventName = event.substr(0, event.indexOf('='));
            var eventParamsStr = event.substr(event.indexOf('=') + 1);
            var eventParams = eventParamsStr ? eventParamsStr.split(',') : [];
        } else {
            var eventName = event;
            var eventParams = [];
        }
        var eventCallback = this.qssEvent['_event_' + eventName];
        if (!eventCallback) {
            console.error(eventName + "回调不存在");
            return;
        }
        return eventCallback.call(this.qssEvent, params, eventParams);
    },
    //值批量校验
    _validate: function () {
        console.log('验证了吗')
        var answers = this._getAllAnswers();
        var validator = new Validator();
        console.log(answers,'验证结果')
        for (var i = 0; i < answers.length; i++) {
            if (answers[i]['rule'] && !answers[i]['is_sub']) {
                var checkMsg = validator.check(answers[i]['answer_name'], answers[i]['rule']);
                if (checkMsg !== true) {
                    $('.JS-input-error').html('')
                    if (answers[i]['question_type'] == 'input' || answers[i]['question_type'] == 'scroll') {
                        //错误显示
                        // $("[data-uuid='" + answers[i]['uuid'] + "']").next('.JS-input-error').html(checkMsg);
                        var el = $("[data-uuid='" + answers[i]['uuid'] + "']").next('.JS-input-error')
                        if (el.length === 0) {
                            el = $("[data-uuid='" + answers[i]['uuid'] + "']").next('.unit').next('.JS-input-error')
                        }
                        el.html(checkMsg);
                        return false;
                    }
                    return false;
                }
            }
        }
        return true;
    },
    //单个值校验
    _validateSingle: function (value, rule) {
        var validator = new Validator();
        return validator.check(value, rule);
    },
    _showLoading: function () {
        var imgSrc = require('../images/loading.gif')
        var html = `<div class='JS-LOADING-BOX'><div class='JS-LOADING-BG'></div><div class='JS-LOADING-CON'><img src=${imgSrc}></div></div>`;
        $('.JS-LOADING-BOX').remove();
        $('body').append(html);
    },
    _removeLoading: function () {
        $('.JS-LOADING-BOX').remove();
    },
    _changeProgress: function (progress) {
        $('.qss-progress-bar').css('width', progress + "%");
    },
    _getQuerys: function (key) {
        var search = window.location.search;
        if (!search) {
            var querys = {};
        } else {
            var querys = {};
            searchStr = search.substr(1);
            searchArr = searchStr.split("&")
            for (var i = 0; i < searchArr.length; i++) {
                searchItem = searchArr[i].split('=');
                querys[searchItem[0]] = searchItem[1];
            }
        }
        return key ? querys[key] : querys;
    },
    _getVegetablesFruitsCls(item, option) {
        var o = {
            cls: '',
            tips: '',
            disabled: 0,
        }
        if (option.no_select_hidden) {
            /*
            if (option.answer_id == item.answer_id) {
                return 'disabled-this-option'
            } else {
                return 'hide-this-option'
            }
            */
            if (option.answer_id == item.answer_id) {
                o.disabled = 1
                o.tips = `<div class="class-vegetables-fruits-tips">您之前的选择为：<span>${item.answer_name}</span>，您还可以根据需要，重新选择以下选项</div>`
            }
            o.cls = 'hide-this-option'
        }
        return o
    },
};

var Validator = function () {

}
Validator.prototype = {
    'check': function (value, rule) {
        var checkStatus = ValidatorRule.check(value, rule);
        if (checkStatus !== true) {
            return this.getMessage(checkStatus);
        }
        return true;
    },
    'getMessage': function (error) {
        var lang = localStorage.getItem('lang') == 'zh_HK' ? 'zh_HK' : ''
        var rule = error;
        var msg, temp;
        var ruleList = rule[2];
        switch (rule[0]) {
            case 'required':
                if ($.inArray('int', ruleList) >= 0) {
                    //整数必填
                    msg = lang ? '請填寫有效的整數' : "请填写有效的整数";
                } else if ($.inArray('number', ruleList) >= 0) {
                    //数字必填
                    msg = lang ? '請填寫有效的數字' : "请填写有效的数字";
                } else if ($.inArray('chinese', ruleList) >= 0) {
                    //汉字必填
                    msg = lang ? '請填寫有效的漢字' : "请填写有效的汉字";
                }
                if (!msg) msg = lang ? '請填寫' : "请填写";
                break;
            case 'int':
                msg = lang ? '輸入值應當為有效的整數' : "输入值应当为有效的整数";
                break;
            case 'number':
                msg = lang ? '輸入值應當為有效的數字' : "输入值应当为有效的数字";
                break;
            case 'decimal':
                msg = lang ? '請輸入1位小數' : '请输入1位小数';
                break;
            case 'decimal2':
                msg = lang ? '請輸入2位小數' : '请输入2位小数';
                break;
            case 'decimal3':
                msg = lang ? '請輸入3位小數' : '请输入3位小数';
                break;
            case 'range':
                temp = rule[1].split(',');
                if (temp[0] == '') {
                    msg = (lang ? '輸入值應當小於' : '输入值应当小于') + temp[1];
                } else if (temp[1] == '') {
                    msg = (lang ? '輸入值應當大於' : '输入值应当大于') + temp[0];
                } else {
                    msg = (lang ? '輸入值應當在' : '输入值应当在') + temp[0] + '至' + temp[1] + '之间';
                }
                break;
            case 'min':
                msg = (lang ? '輸入值不得低於' : '输入值不得低于') + rule[1];
                break;
            case 'max':
                msg = (lang ? '輸入值不得超過' : '输入值不得超过') + rule[1];
                break;
            case 'length':
                temp = rule[1].split(',');
                if (temp.length == 1) {
                    msg = (lang ? '輸入值的長度應當為' : '输入值的长度应当为') + temp[0] + '个字符';
                } else {
                    if (temp[0] == "") {
                        msg = (lang ? '輸入值的長度應當小於' : '输入值的长度应当小于') + temp[1] + '个字符';
                    } else if (temp[1] == "") {
                        msg = (lang ? '輸入值的長度應當大於' : '输入值的长度应当大于') + temp[0] + '个字符';
                    } else {
                        msg = (lang ? '輸入值的長度應當在' : '输入值的长度应当在') + temp[0] + '至' + temp[1] + '个字符之间';
                    }
                }
                break;
            case 'chinese':
                temp = rule[1].split(',');
                if (temp.length == 1) {
                    msg = (lang ? '輸入值的應當為' : '输入值的应当为') + temp[0] + '个汉字';
                } else {
                    if (temp[0] == "") {
                        msg = (lang ? '輸入值的長度應當小於' : '输入值的长度应当小于') + temp[1] + '个汉字';
                    } else if (temp[1] == "") {
                        msg = (lang ? '輸入值的長度應當大於' : '输入值的长度应当大于') + temp[0] + '个汉字';
                    } else {
                        msg = (lang ? '輸入值的長度應當在' : '输入值的长度应当在') + temp[0] + '至' + temp[1] + '个汉字之间';
                    }
                }
                break;
            case 'time':
                msg = lang ? '請輸入XX:XX格式的時間' : "请输入XX:XX格式的时间";
                break;
            case 'callback':
                msg = rule[1];
                break;
            default:
                msg = lang ? '請填寫正確的值' : "请填写正确的值";
        }

        return msg;
    }
}

//表单验证规则
var ValidatorRule = {

    'check': function (value, rule, validator) {

        var ruleList, _ruleArr, _ruleName, _rule, status, _isMultiCheck;
        var context = this;
        rule = rule || '';
        ruleList = rule.split('|');
        for (var i = 0; i < ruleList.length; i++) {
            _ruleArr = ruleList[i].split('=');  //length=3
            _ruleName = _ruleArr[0];             //规则名称
            _rule = _ruleArr[1] || true;     //规则数据
            _isMultiCheck = false;

            //以逗号隔开的多值检测
            if (_ruleName.indexOf('multi_') === 0) {
                _ruleName = _ruleName.replace('multi_', "");
                _isMultiCheck = true;
            }
            if (this[_ruleName]) {

                // 值为空 但未设置验证规则为必填和callback  则验证通过
                if (_ruleName != 'required' && _ruleName != 'callback' && (value == '' || value == undefined)) {
                    continue;
                }
                //执行检验
                if (_isMultiCheck) {
                    var values = value.split(',');
                    for (var k = 0; k < values.length; k++) {
                        status = context[_ruleName](values[k], _rule, validator);
                        if (status !== true) {
                            //验证失败
                            return [_ruleName, _ruleName == 'callback' ? status : _rule];
                        }
                    }
                    ;
                } else {
                    status = context[_ruleName](value, _rule, validator);
                    if (status !== true) {
                        //验证失败
                        return [_ruleName, _ruleName == 'callback' ? status : _rule, ruleList];
                    }
                }
            }
        }
        //验证通过
        return true;
    },

    // 必填项
    'required': function (value, rule) {

        if (rule) {
            return value !== '' && value !== undefined;
        }

        return true;
    },

    // 整数
    'int': function (value, rule) {

        if (rule) {
            if (isNaN(parseInt(value)) || !value.match(/^[-]?\d+$/)) {
                return false;
            }
        }

        return true;
    },

    // 小数后一位
    'decimal': function (value, rule) {
        if (rule) {
            if (isNaN(parseInt(value)) || !value.match(/^\d*\.{0,1}\d{0,1}$/)) {
                return false;
            }
        }
        return true;
    },

    // 小数后两位
    'decimal2': function (value, rule) {
        if (rule) {
            if (isNaN(parseInt(value)) || !value.match(/^(([1-9]{1}\d*)|(0{1}))(\.\d{0,2})?$/)) {
                return false;
            }
        }
        return true;
    },

    // 小数后三位
    'decimal3': function (value, rule) {
        if (rule) {
            if (isNaN(parseInt(value)) || !value.match(/^(([1-9]{1}\d*)|(0{1}))(\.\d{0,3})?$/)) {
                return false;
            }
        }
        return true;
    },
    //金额
    'price': function (value, rule) {
        if (rule) {
            valueFloat = parseFloat(value);
            if (isNaN(valueFloat) || valueFloat < 0 || value.match(/\.\d{3,}$/)) {
                return false;
            }
        }
        return true;
    },

    // 数字
    'number': function (value, rule) {
        if (rule) {
            if (isNaN(parseFloat(value)) || !value.match(/^[-]?\d+(\.\d+)?$/)) {
                return false;
            }
        }
        return true;
    },

    // 长度限制
    'length': function (value, rule) {

        var lengthArr = rule.split(',');
        var minLength = parseInt(lengthArr[0]);
        var maxLength = parseInt(lengthArr[1]);
        if (lengthArr.length == 1) {
            maxLength = minLength;
        }
        if (!isNaN(minLength) && minLength && value.length < minLength) {
            return false;
        }

        if (!isNaN(maxLength) && maxLength && value.length > maxLength) {
            return false;
        }
        return true;
    },

    // 数值限制
    'range': function (value, rule) {

        var rulehArr = rule.split(',');
        var min = parseInt(rulehArr[0]);
        var max = parseInt(rulehArr[1]);

        if (!this.number(value, true)) {
            return false;
        }

        if (!isNaN(min) && value < min) {
            return false;
        }

        if (!isNaN(max) && value > max) {
            return false;
        }

        return false;
        return true;
    },
    //最小值限制
    'min': function (value, rule) {
        var min = parseFloat(rule);
        if (!this.number(value, true)) {
            return false;
        }
        if (!isNaN(min) && value < min) {
            return false;
        }

        return true;
    },
    //最大值限制
    'max': function (value, rule) {
        var max = parseFloat(rule);
        if (!this.number(value, true)) {
            return false;
        }
        if (!isNaN(max) && value > max) {
            return false;
        }

        return true;
    },

    // 时间早于
    'earlier': function (value, rule, validator) {

        var valueDate = this._getDate_(value);
        var compareValue, compareValueDate;
        if (rule === true) {
            compareValue = true;
            compareValueDate = new Date();
        } else {
            compareValue = validator.getValue(rule);
            compareValueDate = this._getDate_(compareValue);
        }

        return compareValue == '' ? true : valueDate.getTime() < compareValueDate.getTime();
    },

    // 时间晚于
    'later': function (value, rule, validator) {

        var valueDate = this._getDate_(value);
        var compareValue, compareValueDate;
        if (rule === true) {
            compareValue = true;
            compareValueDate = new Date();
        } else {
            compareValue = validator.getValue(rule);
            compareValueDate = this._getDate_(compareValue);
        }


        return compareValue == '' ? true : valueDate.getTime() > compareValueDate.getTime();
    },

    // 姓名,2-5位汉字
    'name': function (value, rule) {

        return this.length(value, '2,5') && this.pattern(value, /^[\u4e00-\u9fa5]*$/);
    },
    //汉字 多少位汉字输入
    'chinese': function (value, rule) {
        return this.length(value, rule) && this.pattern(value, /^[\u4e00-\u9fa5]*$/);
    },
    //时间输入
    'time': function (value, rule) {
        var patt = /^(0[0-9]?|1[0-9]?|2[0-3]?)(\:|:)[0-5][0-9]?$/;
        return value.match(patt) ? true : false;
    },

    // 邮箱
    'email': function (value, rule) {

        return this.pattern(value, /^(\w)+(\.\w+)*@(\w)+((\.\w+)+)$/);
    },

    // 手机
    'mobile': function (value, rule) {

        return this.pattern(value, /^1[3578]\d{9}$/);
    },

    // 电话
    'tel': function (value, rule) {

        return this.mobile(value, rule) || this.pattern(value, /^(\d+-)*\d{6,10}$/);
    },
    //身份证号码
    'id_card': function (value, rule) {
        return /^(\d{15}|\d{18}|\d{17}[a-zA-Z]{1})$/.test(value);
    },

    //密码确认
    'cpassword': function (value, rule) {
        return value == $('input[name=' + rule + ']').val();
    },

    //弱密码校验
    'weakpassword': function (value, rule) {
        //不能含三个(含3个）以上连续相同数字
        var patt = /(\d)\1\1/;
        return !patt.test(value ? value : "");
    },
    //协议确认
    'protocol': function (value, rule) {
        return value === 'true';
    },

    // 正则
    'pattern': function (value, rule) {

        return value.match(rule) ? true : false;
    },

    //中文姓名
    cnname: function (value, rule) {
        var patt = /^[\u4e00-\u9fa5\.]{2,25}$/;
        return patt.test(value ? value : "");
    },

    // 回调
    'callback': function (value, rule, validator) {

        return window[rule](value, validator, this);
    }
};


export {
    QSS
}