
export var QssEvent = function (qssObj) {
    this.Qss=qssObj;
    window.bbb = this;
}

QssEvent.prototype={

    //自定义回调事件 清理答案
    _event_reset_option:function(selectedOption){
        var context = this.Qss;
        var answers = context._getAllAnswers();
        for(var i=0;i<answers.length;i++){
            var item = answers[i];
            var uuid = item['uuid'];
            var qssItem = $('.JS-qss-item[data-uuid=\'' + uuid + '\']');
            if(item['is_sub'])continue;
            if(item['question_type']=='check'){
                //其余答案清空
                context._checkOptionChange(qssItem, true);
            }
            var optionCallback = qssItem.data('callback') ? qssItem.data('callback') : "";
            if(item['question_type']=='multi'&&uuid!==selectedOption.data('uuid')){
                //除了已选中外 其余答案清空
                console.log('RESET:' + uuid+' '+answers[i]['answer_id']);
                context._multiOptionChange(qssItem,true);
            }
        }
        return;
        $('.JS-option').each(function(){
            var qssItem = $(this).parents('JS-qss-item');
            var qssType = qssItem.data('question-type');
            if(qssType=='check'){
                context._checkOptionChange(qssItem, true);
            }else if(qssType=='multi'){

            }else{
                if($(this).hasClass('JS-selected')&&!$(this).hasClass('JS-option-hidden')&&!$(this).data('callback')){
                    $(this).click();
                }
            }
        });
    },

    //自定义回调事件 显示子题输入面板
    _event_show_sub_input:function(subBox){
        var context = this.Qss;
        var parentQssEle = subBox.parents('.JS-qss-item');
        var checkItem = subBox.find('.JS-qss-item');
        var qid = checkItem.data('question-no');
        var selectedItem = subBox.parents(".JS-option").not('.JS-option-hidden');
        var unSelectedItem = subBox.parents(".JS-option-hidden");
        context._showSubQss(parentQssEle,qid,function(){
            //子题完成后 父题答案变更
            //optionSelected.removeClass('JS-selected');
            selectedItem.addClass('JS-selected');
            unSelectedItem.removeClass('JS-selected');
            parentQssEle.data('answer-id',selectedItem.data('answer-id'));
            parentQssEle.data('answer-name',selectedItem.data('answer-name'));

            //子题完成后 触发父题回调
            if(selectedItem.data('callback')){
                context._invokeCallback(selectedItem.data('callback'),selectedItem);
            }
            // optionSelected = checkItem.find('.JS-selected');
            // checkItem.data('answer-id', optionSelected.data('answer-id'));
            // checkItem.data('answer-name', optionSelected.data('answer-name'));
        },function(){

        },"input");
    },
    /*   暂时注释无用
    //自定义回调事件 饮酒题目答案选项回调
    _event_yinjiu:function(checkOption){
        var context = this.Qss;
        //该题目的每个答案选项对应着两道题目的选项
        var answerMap={
            //从不饮酒
            '1':[
                {'template_id':'25','question_id':'553','sub_question_id':'553','answer_id':'51299','answer_name':'现在不'},
                {'template_id':'25','question_id':'554','sub_question_id':'554','answer_id':'51302','answer_name':'从不饮酒'},
                //清除饮酒的其他答案
                {'template_id':'25','question_id':'555','sub_question_id':'555','answer_id':context.const.UNSETANSWERID,'answer_name':context.const.UNSETANSWERNAME,'handle':'remove_related'},//戒酒多少年
                {'template_id':'25','question_id':'557','sub_question_id':'557','answer_id':context.const.UNSETANSWERID,'answer_name':context.const.UNSETANSWERNAME,'handle':'remove_related'},//习惯性饮酒多少年
                {'template_id':'25','question_id':'556','sub_question_id':'*','answer_id':context.const.UNSETANSWERID,'answer_name':context.const.UNSETANSWERNAME,'handle':'remove_related'},//饮酒量

            ],
            //偶尔（少于每周一次)
            '2':[
                {'template_id':'25','question_id':'553','sub_question_id':'553','answer_id':'51300','answer_name':'偶尔（少于每周一次)'},
                {'template_id':'25','question_id':'554','sub_question_id':'554','answer_id':'51303','answer_name':'过去偶尔饮酒(少于每周一次)'},
                //清除戒酒的时间答案
                {'template_id':'25','question_id':'555','sub_question_id':'555','answer_id':context.const.UNSETANSWERID,'answer_name':context.const.UNSETANSWERNAME,'handle':'remove_related'},//戒酒多少年
                {'template_id':'25','question_id':'557','sub_question_id':'557','answer_id':context.const.UNSETANSWERID,'answer_name':context.const.UNSETANSWERNAME,'handle':'remove_related'},//习惯性饮酒多少年
                {'template_id':'25','question_id':'556','sub_question_id':'*','answer_id':context.const.UNSETANSWERID,'answer_name':context.const.UNSETANSWERNAME,'handle':'remove_related'},//饮酒量
            ],
            //每周或几乎每周饮酒
            '3':[
                {'template_id':'25','question_id':'553','sub_question_id':'553','answer_id':'51301','answer_name':'是，每周或几乎每周'},
                //清空答案
                {'template_id':'25','question_id':'555','sub_question_id':'555','answer_id':context.const.UNSETANSWERID,'answer_name':context.const.UNSETANSWERNAME,'handle':'remove_related'},//戒酒多少年
                {'template_id':'25','question_id':'554','sub_question_id':'554','answer_id':context.const.UNSETANSWERID,'answer_name':context.const.UNSETANSWERNAME,'handle':'remove_related'},//过去饮酒
            ],
            //过去偶尔饮酒（少于每周一次），现在已经戒酒
            '4':[
                {'template_id':'25','question_id':'553','sub_question_id':'553','answer_id':'51299','answer_name':'现在不'},
                {'template_id':'25','question_id':'554','sub_question_id':'554','answer_id':'51303','answer_name':'过去偶尔饮酒(少于每周一次)'},
                //清除答案
                {'template_id':'25','question_id':'555','sub_question_id':'555','answer_id':context.const.UNSETANSWERID,'answer_name':context.const.UNSETANSWERNAME,'handle':'remove_related'},//戒酒多少年
                {'template_id':'25','question_id':'557','sub_question_id':'557','answer_id':context.const.UNSETANSWERID,'answer_name':context.const.UNSETANSWERNAME,'handle':'remove_related'},//习惯性饮酒多少年
                {'template_id':'25','question_id':'556','sub_question_id':'*','answer_id':context.const.UNSETANSWERID,'answer_name':context.const.UNSETANSWERNAME,'handle':'remove_related'},//饮酒量

            ],
            //过去每周饮酒，现在已经戒酒
            '5':[
                {'template_id':'25','question_id':'553','sub_question_id':'553','answer_id':'51299','answer_name':'现在不'},
                {'template_id':'25','question_id':'554','sub_question_id':'554','answer_id':'51304','answer_name':'过去习惯每周饮酒'},
            ],
            //现在偶尔，过去从不饮酒
            '6':[
                {'template_id':'25','question_id':'553','sub_question_id':'553','answer_id':'51300','answer_name':'偶尔（少于每周一次)'},
                {'template_id':'25','question_id':'554','sub_question_id':'554','answer_id':'51302','answer_name':'从不饮酒'},
                //清除答案
                {'template_id':'25','question_id':'555','sub_question_id':'555','answer_id':context.const.UNSETANSWERID,'answer_name':context.const.UNSETANSWERNAME,'handle':'remove_related'},//戒酒多少年
                {'template_id':'25','question_id':'557','sub_question_id':'557','answer_id':context.const.UNSETANSWERID,'answer_name':context.const.UNSETANSWERNAME,'handle':'remove_related'},//习惯性饮酒多少年
                {'template_id':'25','question_id':'556','sub_question_id':'*','answer_id':context.const.UNSETANSWERID,'answer_name':context.const.UNSETANSWERNAME,'handle':'remove_related'},//饮酒量

            ],
            //现在偶尔，过去习惯每天饮酒
            '7':[
                {'template_id':'25','question_id':'553','sub_question_id':'553','answer_id':'51300','answer_name':'偶尔（少于每周一次)'},
                {'template_id':'25','question_id':'554','sub_question_id':'554','answer_id':'51304','answer_name':'过去习惯每周饮酒'},
            ],
        };
        var answerId = checkOption.data('answer-id');
        var mapAnswers=answerMap[answerId];
        context.extra_answers = mapAnswers;
        window.b = checkOption;
    },

    //自定义回调事件 抽烟题目答案选项回调
    _event_chouyan:function(checkOption){
        var context = this.Qss;
        //该题目的每个答案选项对应着两道题目的选项
        var answerMap={
            //从不抽烟选项
            '1':[
                {'template_id':'25','question_id':'547','sub_question_id':'547','answer_id':'51288','answer_name':'现在不'},
                {'template_id':'25','question_id':'548','sub_question_id':'548','answer_id':'51291','answer_name':'从不抽烟'},
                //清除掉其余抽烟选项的答案
                {'template_id':'25','question_id':'550','sub_question_id':'*','answer_id':context.const.UNSETANSWERID,'answer_name':context.const.UNSETANSWERNAME,'handle':'remove_related'},//抽烟量
                {'template_id':'25','question_id':'549','sub_question_id':'549','answer_id':context.const.UNSETANSWERID,'answer_name':context.const.UNSETANSWERNAME,'handle':'remove_related'},//戒烟时间
                {'template_id':'25','question_id':'552','sub_question_id':'552','answer_id':context.const.UNSETANSWERID,'answer_name':context.const.UNSETANSWERNAME,'handle':'remove_related'},//吸烟时间
            ],
            //偶尔抽烟
            '2':[
                {'template_id':'25','question_id':'547','sub_question_id':'547','answer_id':'51289','answer_name':'偶尔（少于每天一次或每周少于7支)'},
                {'template_id':'25','question_id':'548','sub_question_id':'548','answer_id':'51292','answer_name':'过去偶尔吸烟（少于每天一次或每周少于7支）'},
                //清除戒烟时间的答案
                {'template_id':'25','question_id':'549','sub_question_id':'549','answer_id':context.const.UNSETANSWERID,'answer_name':context.const.UNSETANSWERNAME,'handle':'remove_related'},//戒烟时间
                {'template_id':'25','question_id':'550','sub_question_id':'*','answer_id':context.const.UNSETANSWERID,'answer_name':context.const.UNSETANSWERNAME,'handle':'remove_related'},//抽烟量
                {'template_id':'25','question_id':'552','sub_question_id':'552','answer_id':context.const.UNSETANSWERID,'answer_name':context.const.UNSETANSWERNAME,'handle':'remove_related'},//吸烟时间

            ],
            //每天或几乎每天都抽
            '3':[
                {'template_id':'25','question_id':'547','sub_question_id':'547','answer_id':'51290','answer_name':'每天或几乎每天都抽'},
                //清除戒烟时间的答案
                {'template_id':'25','question_id':'548','sub_question_id':'548','answer_id':context.const.UNSETANSWERID,'answer_name':context.const.UNSETANSWERNAME,'handle':'remove_related'},//过去抽烟
                {'template_id':'25','question_id':'549','sub_question_id':'549','answer_id':context.const.UNSETANSWERID,'answer_name':context.const.UNSETANSWERNAME,'handle':'remove_related'},//戒烟时间
            ],
            //过去偶尔抽烟（少于每天一次或每周少于7支），现在已经戒烟
            '4':[
                {'template_id':'25','question_id':'547','sub_question_id':'547','answer_id':'51288','answer_name':'现在不'},
                {'template_id':'25','question_id':'548','sub_question_id':'548','answer_id':'51292','answer_name':'过去偶尔吸烟（少于每天一次或每周少于7支）'},
                //清除其余答案
                {'template_id':'25','question_id':'549','sub_question_id':'549','answer_id':context.const.UNSETANSWERID,'answer_name':context.const.UNSETANSWERNAME,'handle':'remove_related'},//戒烟时间
                {'template_id':'25','question_id':'550','sub_question_id':'*','answer_id':context.const.UNSETANSWERID,'answer_name':context.const.UNSETANSWERNAME,'handle':'remove_related'},//抽烟量
                {'template_id':'25','question_id':'552','sub_question_id':'552','answer_id':context.const.UNSETANSWERID,'answer_name':context.const.UNSETANSWERNAME,'handle':'remove_related'},//吸烟时间

            ],
            //过去每天抽烟，现在已经戒烟
            '5':[
                {'template_id':'25','question_id':'547','sub_question_id':'547','answer_id':'51288','answer_name':'现在不'},
                {'template_id':'25','question_id':'548','sub_question_id':'548','answer_id':'51293','answer_name':'过去习惯每天吸烟'},
            ],
            //现在偶尔，过去从不抽烟
            '6':[
                {'template_id':'25','question_id':'547','sub_question_id':'547','answer_id':'51289','answer_name':'偶尔（少于每天一次或每周少于7支)'},
                {'template_id':'25','question_id':'548','sub_question_id':'548','answer_id':'51291','answer_name':'从不吸烟'},
                //清除其余答案
                {'template_id':'25','question_id':'549','sub_question_id':'549','answer_id':context.const.UNSETANSWERID,'answer_name':context.const.UNSETANSWERNAME,'handle':'remove_related'},//戒烟时间
                {'template_id':'25','question_id':'550','sub_question_id':'*','answer_id':context.const.UNSETANSWERID,'answer_name':context.const.UNSETANSWERNAME,'handle':'remove_related'},//抽烟量
                {'template_id':'25','question_id':'552','sub_question_id':'552','answer_id':context.const.UNSETANSWERID,'answer_name':context.const.UNSETANSWERNAME,'handle':'remove_related'},//吸烟时间

            ],
            //现在偶尔，过去习惯每天抽烟
            '7':[
                {'template_id':'25','question_id':'547','sub_question_id':'547','answer_id':'51289','answer_name':'偶尔（少于每天一次或每周少于7支)'},
                {'template_id':'25','question_id':'548','sub_question_id':'548','answer_id':'51293','answer_name':'过去习惯每天抽烟'},
            ],

        };
        var answerId = checkOption.data('answer-id');
        var mapAnswers=answerMap[answerId];
        context.extra_answers = mapAnswers;
    },

    //自定义回调事件 生育史怀孕史答案选项回调
    _event_shengyu:function(checkOption){
        var context = this.Qss;
        //该题目的每个答案选项对应着两道题目的选项
        var answerMap={
            //是  需要清除其他几个题目无(妊娠史) 无(怀孕史) 的答案选项 related=24-538-538001,24-538-538002,24-538-538003
            '1':[
                {'template_id':'24','question_id':'539','sub_question_id':'539','answer_id':'-9999999','answer_name':'__UNSET__'},
                {'template_id':'24','question_id':'541','sub_question_id':'541','answer_id':'-9999999','answer_name':'__UNSET__'},
                {'template_id':'24','question_id':'542','sub_question_id':'542','answer_id':'-9999999','answer_name':'__UNSET__'},
                {'template_id':'24','question_id':'543','sub_question_id':'543','answer_id':'-9999999','answer_name':'__UNSET__'},
            ],
            //否 需要设置其他几个题目无(妊娠史) 无(怀孕史) 的答案选项
            '2':[
                {'template_id':'24','question_id':'539','sub_question_id':'539','answer_id':'539001','answer_name':'不适用（无妊娠史）'},
                {'template_id':'24','question_id':'541','sub_question_id':'541','answer_id':'541001','answer_name':'不适用（无妊娠史）'},
                {'template_id':'24','question_id':'542','sub_question_id':'542','answer_id':'542001','answer_name':'不适用（无妊娠史）'},
                {'template_id':'24','question_id':'543','sub_question_id':'543','answer_id':'543001','answer_name':'不适用（无生育史）'},
            ],
        };
        var answerId = checkOption.data('answer-id');
        var mapAnswers=answerMap[answerId];
        context.extra_answers = mapAnswers;
    },

    //自定义回调事件 亲属是否有得糖尿病 只要有亲属选择了得糖尿病 就要置有亲属得过糖尿病的答案选项
    _event_tnbqs:function(checkOption){
        var context = this.Qss;
        // var answerItem = {
        //     'template_id':'22','question_id':'517','sub_question_id':'517'
        // };
        var relatedQss = {
            '25':['24-1','24-2','24-3','24-4','24-5','24-6','24-7','24-8','24-9','24-10'],
            '24':['25-1','25-2','25-3','25-4','25-5','25-6','25-7','25-8','25-9','25-10']
            };
        if(checkOption.data('answer-name')=='是'){
            // answerItem['answer_id'] = '51197';
            // answerItem['answer_name'] = '是';
            this._set_extra_answer('22','517','517','51197','是');
            //如果 亲属患病为否 本道题更改为 是 后 需要把另一道题的亲属 全部置否
            var familyQss=context.groupQuestions['subquestions']['24-15'];
            var currentQssItem = checkOption.parents('.JS-qss-item');
            var currentQssNoInfo = currentQssItem.data('question-no');
            var currentQssNo = currentQssNoInfo.split('-')[0];
            if(familyQss['answer_name']=='否'){
                var relatedQssList = relatedQss[currentQssNo];
                for(var i=0;i<relatedQssList.length;i++){
                    var item = context.groupQuestions['subquestions'][relatedQssList[i]];
                    this._set_extra_answer(item['template_id'],item['question_id'],item['sub_question_id'],item['option_answer_id'],'否');
                }
            }
        }
    }
    ,
    //自定义回调事件 设置冠心病亲属患病
    _event_gxb_set:function(checkOption){
        var context = this.Qss;
        //var resetBtn = $('[data-question-id="519"]');
        //设置亲属患病为是
        this._set_extra_answer('22','519','519','51199','是');
        return;
    },
    //自定义回调事件 清理冠心病亲属是患病
    _event_reset_gxboption:function(selectedOption){
        var context = this.Qss;
        this._event_reset_option(selectedOption);
        //清空亲属的答案
        var items = $('[data-question-id="520"]');
        items.data('answer-id','-9999999');
        items.data('answer-name','__UNSET__');
        //设置亲属患病为否
        this._set_extra_answer('22','519','519','51200','否');
        return;
    },

    //自定义回调事件 设置脑卒中亲属患病
    _event_ncz_set:function(checkOption){
        var context = this.Qss;
        //设置亲属患病为是
        this._set_extra_answer('22','521','521','51211','是');
        return;
    },
    //自定义回调事件 清理脑卒中亲属是患病
    _event_reset_nczoption:function(selectedOption){
        var context = this.Qss;
        this._event_reset_option(selectedOption);
        //清空亲属的答案
        var items = $('[data-question-id="522"]');
        items.data('answer-id','-9999999');
        items.data('answer-name','__UNSET__');
        //亲属无患病
        //设置亲属患病为否
        this._set_extra_answer('22','521','521','51212','否');
        return;
    },

    //自定义回调事件 设置高血压亲属患病
    _event_gxy_set:function(checkOption){
        var context = this.Qss;
        var resetBtn = $('[data-question-id="523"]');
        //设置亲属患病为是
        this._set_extra_answer('22','523','523','51223','是');
        return;
    },
    //自定义回调事件 清理高血压亲属是患病
    _event_reset_gxyoption:function(selectedOption){
        var context = this.Qss;
        this._event_reset_option(selectedOption);
        //清空亲属的答案
        var items = $('[data-question-id="524"]');
        items.data('answer-id','-9999999');
        items.data('answer-name','__UNSET__');
        //亲属无患病
        //设置亲属患病为否
        this._set_extra_answer('22','523','523','51224','否');
        return;
    },
    //自定义回调事件 设置肥胖症亲属患病
    _event_fpz_set:function(checkOption){
        var context = this.Qss;
        //设置亲属患病为是
        this._set_extra_answer('22','525','525','51235','是');
        return;
    },
    //自定义回调事件 清理肥胖症亲属是患病
    _event_reset_fpzoption:function(selectedOption){
        var context = this.Qss;
        this._event_reset_option(selectedOption);
        //清空亲属的答案
        var items = $('[data-question-id="526"]');
        items.data('answer-id','-9999999');
        items.data('answer-name','__UNSET__');
        //亲属无患病
        //设置亲属患病为否
        this._set_extra_answer('22','525','525','51236','否');
        return;
    },

    //自定义回调事件 设置高脂血亲属患病
    _event_gzx_set:function(checkOption){
        var context = this.Qss;
        //设置亲属患病为是
        this._set_extra_answer('22','527','527','51247','是');
        return;
    },
    //自定义回调事件 清理高脂血亲属是患病
    _event_reset_gzxoption:function(selectedOption){
        var context = this.Qss;
        this._event_reset_option(selectedOption);
        //清空亲属的答案
        var items = $('[data-question-id="528"]');
        items.data('answer-id','-9999999');
        items.data('answer-name','__UNSET__');
        //亲属无患病
        //设置亲属患病为否
        this._set_extra_answer('22','527','527','51248','否');
        return;
    },

    //自定义回调事件 清理糖尿病亲属患病
    _event_reset_tnboption:function(selectedOption){
        var context = this.Qss;
        var answers = context._getAllAnswers();
        for(var i=0;i<answers.length;i++){
            var item = answers[i];
            var uuid = item['uuid'];
            var qssItem=$('.JS-qss-item[data-uuid=\''+uuid+'\']');
            if(item['is_sub'])continue;
            if(item['question_type']=='check'){
                //其余答案清空
                context._checkOptionChange(qssItem, true);
            }
            if(item['question_type']=='multi'){
                //启用答案清空
                context._multiOptionChange(qssItem,true);
            }
        }
        //亲属是否患糖尿病 如果恶性肿瘤和糖尿病答案都为以上皆无 则亲属患病为否
        if(context.groupQuestions['subquestions']['25-11']['answer_id']==='99'){
            this._set_extra_answer('22','517','517','51198','否');
            //同时 为了兼容PC端完成度计算异常 需要删除相关的亲属答案为否的选项
            this._set_extra_answer('22', '518','*', context.const.UNSETANSWERID, context.const.UNSETANSWERNAME, 'remove_related');
        }
        return;
    },
    //自定义回调事件 清理恶性肿瘤亲属患病
    _event_reset_exzloption:function(selectedOption){
        var context = this.Qss;
        var answers = context._getAllAnswers();
        for(var i=0;i<answers.length;i++){
            var item = answers[i];
            var uuid = item['uuid'];
            var qssItem=$('.JS-qss-item[data-uuid=\''+uuid+'\']');
            if(item['is_sub'])continue;
            if(item['question_type']=='check'){
                //其余答案清空
                context._checkOptionChange(qssItem, true);
            }
            if(item['question_type']=='multi'){
                //启用答案清空
                context._multiOptionChange(qssItem,true);
            }
        }
        //亲属是否患糖尿病 如果恶性肿瘤和糖尿病答案都为以上皆无 则亲属患病为否
        if(context.groupQuestions['subquestions']['24-11']['answer_id']==='99'){
            this._set_extra_answer('22','517','517','51198','否')
            //同时 为了兼容PC端完成度计算异常 需要删除相关的亲属答案为否的选项
            this._set_extra_answer('22', '518','*', context.const.UNSETANSWERID, context.const.UNSETANSWERNAME, 'remove_related');
        }
        return;
    },
    //
    _event_reset_related:function(option,eventParams){
        //eventParams:  21-56-23,22-56-*,...   tid-qid-sqid
        console.log("REST RELATED:");
        console.log(eventParams)
        var context = this.Qss;
        if(!eventParams)return;
        var unsetAnswers = [];
        for(var i=0;i<eventParams.length;i++){
            var qssInfo = eventParams[i].split('-');
            unsetAnswers.push({
                'template_id':qssInfo[0],
                'question_id':qssInfo[1],
                'sub_question_id':qssInfo[2],
                'answer_id':context.const.UNSETANSWERID,
                'answer_name':context.const.UNSETANSWERNAME,
                'handle':'remove_related'
            });
        }
        context.extra_answers = context.extra_answers ? context.extra_answers : [];
        console.log(context.extra_answers);
        context.extra_answers = context.extra_answers.concat(unsetAnswers);
    },


    //前置回调：糖尿病答案存储前置操作
    _event_before_tnb_submit:function(answers){
        //如果  亲属是否患病答案已经为否 而该题又选择了以上皆无 为了防止 亲属答案全部被置否 此题不用保存
        var context=this.Qss;
        var relatedQss = context.groupQuestions['subquestions']['24-15'];
        var isChoosedMember=false;//是否选择了亲属成员
        for(var i=0;i<answers.length;i++){
            if(answers[i]['question_type']=='check'&&answers[i]['answer_name']=='是'){
                isChoosedMember=true;
                break;
            }
        }
        //如果选择了亲属 把其余的亲属为否 清掉
        var response = [];
        if(isChoosedMember){
            for(var i=0;i<answers.length;i++){
                if(answers[i]['question_type']!='check'||answers[i]['answer_name']=='是'){
                    response.push(answers[i]);
                }else{
                    //清除答案
                    var tmpAnswer = answers[i];
                    tmpAnswer['answer_id']=context.const.UNSETANSWERID;
                    tmpAnswer['answer_name']=context.const.UNSETANSWERID;
                    response.push(tmpAnswer);
                }
            }
        }else{
            //亲属是否患病答案已经为否 而该题又选择了以上皆无 为了防止 亲属答案全部被置否 此题不用保存
            response = relatedQss['answer_name']=='否'&&!isChoosedMember?[]:answers;
        }
        return response;
    },

    */

    //设置关联或需要特殊处理的答案
    _set_extra_answer:function(tid,qid,sqid,answer_id,answer_name,handle){
        var context=this.Qss;
        handle=handle?handle:false;
        context.extra_answers = context.extra_answers ? context.extra_answers : [];
        var hasFind=false;
        for(var i=0;i<context.extra_answers.length;i++){
            if(context.extra_answers[i]['template_id']===tid&&context.extra_answers[i]['question_id']===qid&&context.extra_answers[i]['sub_question_id']===sqid){
                context.extra_answers[i]['answer_id']=answer_id;
                context.extra_answers[i]['answer_name']=answer_name;
                hasFind = true;
                break;
            }
        }
        if(!hasFind){
            var answerItem = {
                'template_id':tid,'question_id':qid,'sub_question_id':sqid,'answer_id':answer_id,'answer_name':answer_name
            };
            if(handle)answerItem['handle']=handle;
            context.extra_answers = context.extra_answers.concat(answerItem);
        }
    }


}
