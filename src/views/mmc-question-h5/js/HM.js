window.HM = (function($){
	var Tools = function(){
    	this.init();
	};

	Tools.prototype = {
		ready: function(callback){
			$(function(){
				callback.call(this);
			});
		},
		init: function(){},
	    formSelect: function(selectorStr){
	    	$(selectorStr).each(function(){
	    		var that = $(this);
	    		var selectLabel = that.find(".JS-sel-label");
	    		var select = that.find("select");
	    		setValue(selectLabel,select);
	    		select.on("change",function(){
	    			setValue(selectLabel,select);
	    		});
	    	});
	    	function setValue(label,select){
	    		if(select.find("option:selected").text() == "请选择"){
	    			return;
	    		}
	    		label.text(select.find("option:selected").text());
	    	}
	    },
			listSelect: function(selectorStr){
				$(selectorStr).each(function(){
					var that = $(this);
					var options = that.children("li");
					options.on("click",function(){
						$(this).addClass("option-selected");
						$(this).siblings("li").removeClass("option-selected");
					});
				});
			},
			listSelect: function(selectorStr){
				$(selectorStr).each(function(){
					var that = $(this);
					var options = that.children("li");
					options.on("click",function(){
						$(this).addClass("option-selected");
						// $(this).siblings("li").removeClass("option-selected");
					});
				});
			},
			popWin: function(el,options){
					var pop = new popWin(el,options);
					pop.show();
					return pop;
			}
	};

	var popWin = function(el,options){
			this.el = el;
			this.default = {
					shadeClick: false,
			};
			this.default = $.extend(this.default,options||{});
			this.init();
	};

	popWin.prototype = {
		init: function(){
			 var that = this;
			 this.body = $("body");
			 this.close = this.el.find(".hm-pop-close .canle");
			 this.shade = this.el.find(".hm-pop-shade");
			 this.pop = this.el.find(".hm-pop-block");
			 this.close.on("click",function(){
					that.closeWin();
			 });
			 this._shadeClick();
		},
		_shadeClick: function(){
			 var that = this;
			 if(that.default.shadeClick){
				 	that.shade.on("click",function(){
	 					that.closeWin();
	 			 });
			 }
		},
		_stopMove: function(){
			 this.body.css("overflow","hidden");
			 this.body.on('touchmove',function(event) { event.preventDefault(); }, false);
		},
		_doMove: function(){
			 this.body.css("overflow","auto");
			 this.body.off('touchmove');
		},
		show: function(){
			 this.el.css("zIndex","99");
			 this.shade.show();
			 this.pop.css("transform","translate(-50%,-90%)");
			 this._stopMove();
		},
		closeWin: function(){
			 this.pop.css("transform","translate(-50%,-350%)");
			 this.shade.hide();
			 this.el.css("zIndex","-100000");
			 this._doMove();
		}
	};

	return new Tools();

})(jQuery || window.jQuery);
