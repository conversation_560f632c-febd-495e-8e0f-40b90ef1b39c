<template>
  <div class="middleStatusPage">
    <img class="completeImg" src="./images/complete-bg.png" alt="" />
    <div class="mainText" v-if="!(undoList.length == 0 && coffeeTeaUrl)">
      {{$t('mmcQuestionH5.youComplete')}}<span class="mainName">{{groupName || ''}}</span>{{$t('mmcQuestionH5.fillQuestionnaire')}}
    </div>
    <template v-if="!isLoadingShow">
      <!-- 最后一个模块 -->
      <div class="tipsOuter" v-if="isLast">
        <!-- 有未完成模块 -->
        <div v-if="undoList.length > 0">
          <span class="middleTips">
            {{$t('mmcQuestionH5.pleaseClick')}}<span class="questionnaireName">“{{$t('mmcQuestionH5.continueImprove')}}”</span>{{$t('mmcQuestionH5.fillIn')}}{{$t('mmcQuestionH5.unfinished')}}
            <span class="questionnaireName" @click="goQuestionnaire(item)" v-for="(item,index) in undoList">{{index != undoList.length - 1 ? item.name + '、' : item.name}}</span>
            {{$t('mmcQuestionH5.questionnaire')}}或{{$t('mmcQuestionH5.returnQuestionnaireList')}}
          </span>
          <div class="endBtnOuter">
            <van-button class="coffeeTeaBtn" plain round type="info" @click="handleReturnList('showPop')">{{$t('mmcQuestionH5.returnQuestionnaireList')}}</van-button>
            <van-button class="coffeeTeaBtn" plain round type="info" @click="handleContinue">{{$t('mmcQuestionH5.continueImprove')}}</van-button>
          </div>
        </div>
        <!-- 全部完成 -->
        <div v-else>
          <!-- 有咖啡与茶问卷的url -->
          <div class="coffeeTeaTips" v-if="coffeeTeaUrl">
            <span>{{$t('mmcQuestionH5.coffeeTeaTipsTitle')}}</span> <br />
            <span>{{$t('mmcQuestionH5.coffeeTeaTipsHi')}}</span> <br />
            <span>{{$t('mmcQuestionH5.coffeeTeaTipsText')}}</span>
            <div class="endBtnOuter">
              <van-button class="coffeeTeaBtn" plain round type="info" @click="handleFinished">{{$t('mmcQuestionH5.inconvenienceBtn')}}</van-button>
              <van-button class="coffeeTeaBtn" plain round type="info" @click="goWjx">{{$t('mmcQuestionH5.okBtn')}}</van-button>
            </div>
          </div>
          <!-- 无url -->
          <van-button v-else class="btn" plain round type="info" @click="handleFinished">{{$t('mmcQuestionH5.finishBtnText')}}</van-button>
        </div>
      </div>
      <!-- 非最后一个模块 -->
      <div class="tipsOuter" v-else>
        <span class="middleTips">
          {{$t('mmcQuestionH5.pleaseClick')}}<span class="questionnaireName">“{{$t('mmcQuestionH5.continueBtnText')}}”</span>{{$t('mmcQuestionH5.fillIn')}}<span class="questionnaireName" @click="handleContinue">{{nextGroupName}}</span>{{$t('mmcQuestionH5.questionnaire')}}
        </span>
        <van-button class="btn" plain round type="info" @click="handleContinue">{{$t('mmcQuestionH5.continueBtnText')}}</van-button>
        <div class="goListBtn" @click="handleReturnList">{{$t('mmcQuestionH5.returnQuestionnaireList')}}</div>
      </div>
    </template>
    <template v-if="isLoadingShow">
      <van-loading size="50px" vertical color="#3388FF">加载中</van-loading>
    </template>
    <div class="bottomText">
      <span>{{$t('mmcQuestionH5.middleBottomTips1')}}</span> <br />
      <span>{{$t('mmcQuestionH5.middleBottomTips2')}}</span>
    </div>
  </div>
</template>

<script>
import { api } from '@/api/common/mmcQuestion.js'
import { getUrlParams } from '@/utils/utils.js'
import { Dialog } from 'vant'
export default {
  data(){
    return{
      pageStatus: '',  //页面状态 中间页/结束页  continue/end
      undoList: [],
      visit_level: '',
      qss_type: '',
      user_id: '',
      coffeeTeaUrl: '',
      lang: '',
      nextGroupId: '',
      nextGroupName: '',
      groupName: '',
      isLast: null,
      isLoadingShow: true
    }
  },
  methods: {
    init(){
      this.isLoadingShow = true
      let url = location.href
      this.visit_level = getUrlParams(url,'visit_level')
      this.qss_type = getUrlParams(url,'qss_type')
      this.user_id = getUrlParams(url,'user_id')
      this.lang = localStorage.getItem('lang') == 'zh_CN' ? '' : 'simple_tradition'
      this.groupName = decodeURIComponent(getUrlParams(url,'groupName'))
      this.getMiddleStatus()
      if(localStorage.getItem('lang') == 'zh_HK'){
        document.title = 'MMC患者訪視問卷'
      }
    },
    handleReturnList(showPop){
      // 咖啡与茶问卷的url 如果存在就弹窗询问 否则直接回到列表页
      if(showPop && this.coffeeTeaUrl){
        Dialog.confirm({
          closeOnClickOverlay: true,
          className: 'mmcMiddleDialog',
          confirmButtonColor: '#3296fa',
          message: `${this.$t('mmcQuestionH5.coffeeTeaTipsTitle')}\n${this.$t('mmcQuestionH5.coffeeTeaTipsHi')}\n${this.$t('mmcQuestionH5.coffeeTeaTipsText')}`,
          confirmButtonText: this.$t('mmcQuestionH5.okBtn'),
          cancelButtonText: this.$t('mmcQuestionH5.inconvenienceBtn')
        }).then(res=>{
          location.href = this.coffeeTeaUrl
        }).catch(err=>{
          this.handleFinished()
        })
      }else{
        this.handleFinished()
      }
    },
    goQuestionnaire(item){
      this.$router.push({
        path: '/mmc/question/survey',
        query: {
          user_id: this.user_id,
          visit_level: this.visit_level,
          group: item.qss_type,
          groupName: item.name,
        }
      })
    },
    goWjx(){
      if(this.coffeeTeaUrl){
        location.href = this.coffeeTeaUrl
      }else{
        this.$toast('暂未配置该问卷')
      }
    },
    handleContinue(){
      let obj = {
        qss_type: this.undoList.length > 0 ? this.undoList[0].qss_type : this.nextGroupId,
        name: this.undoList.length > 0 ? this.undoList[0].name : this.nextGroupName
      }
      this.goQuestionnaire(obj)
    },
    handleFinished(){
      let isMini = navigator.userAgent.indexOf('miniProgram') > -1
      if(isMini){
        this.$router.push('/ihecMiniwechat/questionnaireList')
      }else{
        this.$router.push('/mmc/question/index')
      }
    },
    getMiddleStatus(){
      let obj = {
        visit_level: this.visit_level,
        qss_type: this.qss_type,
        language: this.lang
      }
      api.getMiddleStatus(obj).then(res=>{
        if(res.status == 0){
          let {is_last,undo_list,coffee_tea_url,next_qss_type,next_qss_name} = res.data
          this.undoList = undo_list
          this.coffeeTeaUrl = coffee_tea_url
          this.isLast = is_last
          if(is_last){
            if(undo_list.length == 0){
              //最后一个并且前几个全部填完
              this.pageStatus = 'end'
            }else{
              //最后一个 但前几个还未完成
              this.pageStatus = 'continue'
            }
          }else{
            this.pageStatus = 'continue'
            this.nextGroupId = next_qss_type
            this.nextGroupName = next_qss_name
          }
          this.isLoadingShow = false
        }else{
          this.isLoadingShow = false
        }
      })
    }
  },
  mounted() {
    this.init()
  }
};
</script>

<style lang="scss">
.middleStatusPage {
  position: relative;
  height: 100vh;
  .completeImg {
    width: 210px;
    height: 140px;
    margin-top: 66px;
  }
  .mainText {
    margin-top: 45px;
    margin-bottom: 100px;
    font-size: 16px;
    color: #333333;
    font-weight: bold;
    .mainName{
      color: #ff8e00;
      font-weight: bold;
    }
  }
  .tipsOuter {
    font-size: 14px;
    padding: 0 30px;
    .middleTips{
      line-height: 20px;
    }
  }
  .coffeeTeaTips{
    font-size: 16px;
    color: #333333;
    line-height: 25px;
    // padding: 45px 30px 0;
    text-align: left;
    margin-top: 36px;
  }
  .endBtnOuter{
    display: flex;
    justify-content: space-between;
    margin-top: 36px;
    .coffeeTeaBtn{
      width: 150px;
      color: #ff8e00;
      border-color: #ff8e00;
      font-size: 16px;
    }
  }
  .questionnaireName {
    color: #ff8e00;
  }
  .goListBtn{
    color: #ff8e00;
    font-size: 16px;
    margin-top: 36px;
  }
  .btn {
    width: 200px;
    height: 38px;
    color: #ff8e00;
    border-color: #ff8e00;
    font-size: 16px;
    margin-top: 36px;
  }
  .bottomText {
    width: 100%;
    position: absolute;
    bottom: 20px;
    color: #999999;
    font-size: 12px;
    left: 50%;
    transform: translate(-50%, 0);
    line-height: 20px;
  }
}
.mmcMiddleDialog{
  .van-dialog__message{
    text-align: left;
  }
}
</style>