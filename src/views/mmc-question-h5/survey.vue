<template>
  <div class="wrap">
    <van-loading class="loading" v-if="isLoadingShow" size="50px" vertical color="#3388FF">加载中</van-loading>
    <div class="x-container white questionList" v-if="!isLoadingShow">
      <!-- QSS Form Section -->
     <form class="JS-qss-form"> </form>
        <div class="JS-qss-form-readme"></div>
        <div class="JS-qss-form-tips" style="padding-top:50px;"></div>
        <div class="JS-sub-form-box" style="display: none;"></div>
        <div v-show="isShowBtn" class='medical-box' style="width:100%;position: fixed;bottom:30px;left:0px;overflow: inherit;">
        <div class="" style="width:50%;float:left;text-align:center;cursor:pointer;"><span class="JS-qss-prev JS-qss">{{ $t('mmcQuestionH5.PreviousQuestion') }}</span></div>
        <div class="" style="width:50%;float:left;text-align:center;cursor:pointer;"><span class="JS-qss-next JS-qss">{{ $t('mmcQuestionH5.nextQuestion') }}</span></div>
        </div>
    </div>
  </div>
</template>
<script>
import { QSS } from './js/MMC_qss.js';

export default {
  data() {
    return {
      isLoadingShow: false,
      docmHeight: document.documentElement.clientHeight || document.body.clientHeight,
      showHeight: document.documentElement.clientHeight || document.body.clientHeight,
      isShowBtn: true
    }
  },
  watch: {
    //监听显示高度
    showHeight: function () {
      if (this.docmHeight > this.showHeight) {
        //隐藏
        this.isShowBtn = false
      } else {
        //显示
        this.isShowBtn = true
      }
    }
  },
  mounted() {
    this.initializeQSS();
     window.onresize = () => {
      return (() => {
        this.showHeight = document.documentElement.clientHeight || document.body.clientHeight;
      })()
    }
    if(localStorage.getItem('lang') == 'zh_HK'){
      document.title = 'MMC患者訪視問卷'
    }
  },
  methods: {
    initializeQSS() {
      var qssHandle = new QSS();
      var userId = this.$route.query.user_id;
      var group = this.$route.query.group;
      var visitLevel = this.$route.query.visit_level;
      var isLoadingShow = this.isLoadingShow
      var groupName = this.$route.query.groupName;
      qssHandle.init($('.JS-qss-form'), userId, group, visitLevel, isLoadingShow,groupName, this);
    }
  }
};
</script>

<style scoped>
/* @import './css/all.css'; */
@import './css/jquery-weui.css';
@import './css/questionnaire.css';

.pop {
  width: 100%;
  height: 100%;
  position: fixed;
  left: 0;
  top: -80px;
}

.questionList {
  padding-bottom:70px;
  /* height: 90vh; */
  overflow: auto;
}



.pop {
  width: 100%;
  height: 100%;
  position: fixed;
  left: 0;
  top: -80px;
}

.questionList {
  /* height: 90vh; */
  overflow: auto;
}</style>

<style>

.qss-progress-bar-box {
  height: 4px;
  background: #f7f7f7;
  ;
  border-radius: 10px;
  position: relative;
  overflow: hidden;
  bottom: 5px;
}

.qss-progress-bar {
  position: absolute;
  top: 0px;
  left: 0px;
  background: #ff8e17;
  width: 1%;
  height: 100%;
  border-radius: 10px;
  /* transition: 0.5s; */
}


</style>