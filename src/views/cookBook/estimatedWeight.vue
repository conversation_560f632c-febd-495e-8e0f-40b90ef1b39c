<template>
  <div class="main" ref="height">
    <div class="leftBox">
      <div :class="['menu',  menuActive == item ? 'menuActive' : '']"
        v-for="(item, index) in classifyList" :key="index"
        @click="checkMenu(item)">{{item}}</div>
    </div>
    <div class="rightBox">
      <div class="food" v-for="(item, index) in detailList" :key="index">
        <img :src="item.image_url+'?x-oss-process=image/resize,l_209,h_106'" class="foodImg">
        <div class="foodIntroduce">{{item.descrption}}</div>
      </div>
    </div>
  </div>
</template>

<script>
import { getFoodList } from '@/api/cookBook'
import { getBanFontSize } from '@/utils/utils.js'
export default {
  data() {
    return {
      classifyList: [],
      detailList: [],
      menuActive: '',
      datas: {}
    }
  },
  created() {
    // 判断是否安卓系统微信页面修改了字体
    getBanFontSize()
    this.init()
  },
  methods: {
    init() {
      let that = this
      getFoodList({}).then(res => {
        console.log(res)
        if (res.status === 0) {
           this.datas = res.data
          that.classifyList = Object.keys(that.datas)
          that.menuActive = that.classifyList[0]
          that.detailList = res.data[that.classifyList[0]]
        }
      }).then(res => {
        that.$refs.height.style.height = window.innerHeight + 'px'
      })
    },
    checkMenu(item) {
      this.menuActive = ''
      this.detailList = []
      this.detailList = this.datas[item]
      this.menuActive = item
    }
  }
}
</script>

<style lang="scss" scoped>
  @import "@/assets/scss/cookBook/estimatedWeight.scss";
</style>
