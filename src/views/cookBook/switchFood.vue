<template>
  <div>
    <div class="changeFoodType" v-if="replaceFoodList.length > 0">
        <div class="foodTypeBox" v-for="(item, index) in replaceFoodList" :key='index' v-if="isFlg(item.replace_list, item.row)">
            <div class="foodModelTitle">
                <div class="foodIcon"></div>
                <div class="foodType">
                  <span v-if="item.type!=0">{{item.name}}</span>
                  <span v-if="item.type==0">{{item.name}} 可替换食材</span>
                </div>
            </div>
            <div class="foodImgBox" v-if="item.type==0 && item.replace_list.length>0">
                <div class="foodImg" @click="checkFood(item.food_id, subItem.food_id)" v-for="(subItem, index) in item.replace_list" :key='index' v-if="index < 4 || item.showMore == 1">
                    <div class="checkImg" v-show="item.replace_food_id == subItem.food_id">
                      <img src="https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/check.png" />
                    </div>
                    <img :src="subItem.image_url=='' ? 'https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/morencai.png' : subItem.image_url + '?x-oss-process=img/resize,h_100'"/>
                    <span>{{subItem.name}}</span>
                </div>
            </div>
            <div class="lookMore" v-if="item.type==0 && item.replace_list.length>4"
                  @click="lookMore(item.food_id)">{{item.showMore==1?'收起':'查看更多'}}
                <img v-if="item.showMore !=1" src="https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/xiala.png" />
                <img v-else src="https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/shang.png" />
            </div>
            <div v-if="item.type == 1">
                <div v-for="(subItem, index) in item.row" :key='index' v-if="subItem.replace_list.length>0">
                    <div class="itemTitle">
                        <div class="foodIcon1"></div>
                        <div class="foodType1">
                          <span>[{{subItem.name}}]</span>
                          可替换食材
                        </div>
                    </div>
                    <div class="itemFoodImg">
                        <div class="foodImg" @click="checkFood(subItem.food_id, subSubItem.food_id)" v-for="(subSubItem, index) in subItem.replace_list" :key='index' v-if="index < 4 || subItem.showMore == 1">
                            <div class="checkImg" v-show='subItem.replace_food_id == subSubItem.food_id'>
                                <img src="https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/check.png" />
                            </div>
                            <img :src="subSubItem.image_url=='' ? 'https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/morencai.png' : subSubItem.image_url + '?x-oss-process=img/resize,h_100'"/>
                            <span>{{subSubItem.name}}</span>
                        </div>
                    </div>
                    <div class="lookMore" v-if="subItem.replace_list.length>4"
                      @click="lookMore(subItem.food_id)">
                      {{subItem.showMore==1?'收起':'查看更多'}}
                      <img v-if="item.showMore !=1" src="https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/xiala.png" />
                      <img v-else src="https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/shang.png" />
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="changeFoodBtn" @click="changeFoodBtn">确定</div>
    <!-- breakfast 早餐 -->
    <!-- addition 加餐 -->
    <!-- lunch 午餐 -->
    <!-- dinner 晚餐 -->
  </div>
</template>

<script>
import { deepCopy } from '@/utils/utils'
import { changeFood, resetFood } from '@/api/cookBook'
import { Toast } from 'vant'
import { getBanFontSize } from '@/utils/utils.js'
export default {
  data() {
    return {
      currentId: 0,
      currentName: 0,
      replaceFoodArea: '',
      replaceFoodName: '',
      replaceFoodList: [],
      gifShow: true,
      foodTime: '',
      foodId: '',
      source_type: ''
    }
  },
  created() {
    // 判断是否安卓系统微信页面修改了字体
    getBanFontSize()
    this.foodTime = this.$route.query.name
    this.foodId = this.$route.query.foodId
    this.source_type = this.$route.query.source_type
    this.init()
  },
  computed: {

  },
  methods: {
    init() {
      let datas = { name: this.foodTime, id: this.foodId }
      let that = this
      changeFood(datas).then(res => {
        // console.log(res)
        if (res.status == 0) {
          that.replaceFoodList = res.data
        }
      })
    },
    lookMore(id) {
      let replaceList = this.replaceFoodList
      replaceList.forEach((item, key) => {
        if (item.row !== undefined) {
          item.row.forEach((subItem, key) => {
            if (subItem.food_id == id) {
              if (subItem.showMore == 1) {
                this.$set(subItem, 'showMore', 0)
              } else {
                this.$set(subItem, 'showMore', 1)
              }
            }
          })
        } else {
          if (item.food_id == id) {
            if (item.showMore == 1) {
              this.$set(item, 'showMore', 0)
            } else {
              this.$set(item, 'showMore', 1)
            }
          }
        }
      })
    },
    checkFood(id, childId) {
      this.gifShow = false
      let replaceList = this.replaceFoodList
      replaceList.forEach((item, key) => {
        if (item.row !== undefined) {
          item.row.forEach((subItem, index) => {
              if (subItem.food_id == id) {
                  if (subItem.replace_food_id == childId) {
                    this.$set(subItem, 'replace_food_id', '')
                  } else {
                    this.$set(subItem, 'replace_food_id', childId)
                  }
              }
          })
        } else {
          if (item.food_id == id) {
            if (item.replace_food_id == childId) {
                this.$set(item, 'replace_food_id', '')
            } else {
                this.$set(item, 'replace_food_id', childId)
            }
          }
        }
      })
    },
    changeFoodBtn() {
      let that = this
      let resData = deepCopy(that.replaceFoodList)
      resData.forEach(function (item) {
        if (item.row !== undefined) {
          item.row.forEach(function (subItem) {
            delete subItem['replace_list']
            delete subItem['image_url']
            if (subItem.replace_food_id == undefined) {
              that.$set(subItem, 'replace_food_id', '')
            }
          })
        } else {
          delete item['image_url']
          delete item['replace_list']
          if (item.replace_food_id == undefined) {
              that.$set(item, 'replace_food_id', '')
          }
        }
      })
      // console.log(resData)
      let reqData = { name: this.foodTime, id: this.foodId, data: resData }
      this.noDoubleTap(() => {
        resetFood(reqData).then(function (res) {
            if (res.status === 0) {
              Toast('替换成功')
              setTimeout(() => {
                // that.$router.push({
                //   name: 'Cookbook.Recipes',
                //   query: { 'source_type': that.source_type }
                // })
                window.history.go(-1)
              }, 1000)
            } else {
              Toast(res.msg)
            }
        })
        setTimeout(() => {
          that.$btnDisabled = true
        }, 2000)
      }, false)
    },
    isFlg(list, row) {
      if (row != undefined) {
        if (row.length > 0) {
          return true
        }
      }
     if (list != undefined) {
         if (list.length > 0) {
            return true
         }
      }
      return false
    }
  }
}
</script>

<style lang="scss" scoped>
  @import "@/assets/scss/cookBook/switchFood.scss";
</style>
