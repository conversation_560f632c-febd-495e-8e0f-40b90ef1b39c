<template>
  <div class="main">
    <img :src="info.img_url" class="foodImg" />
    <div class="titleBox">
        <img class="titleImg1" src="https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/element.png" />
        <div class="title">三大营养元素</div>
    </div>
    <div class="elementBox">
        <div class="elementTop">
            <div>热量</div>
            <div>{{info.rineng}}</div>
            千卡
        </div>
        <div class="elementMid">每100g(可食部分）</div>
        <div class="elementBottom" v-if="info.component.length != 0">
            <div class="proteinBox">
                <div class="proteinNum">
                    <span>{{info.component[0].value}}</span>
                    {{info.component[0].unit}}
                </div>
                <div class="protein">{{info.component[0].nutrientName}}</div>
            </div>
            <div class="fatBox">
                <div class="fatNum">
                    <span>{{info.component[1].value}}</span>
                    {{info.component[1].unit}}
                </div>
                <div class="fat">{{info.component[1].nutrientName}}</div>
            </div>
            <div class="sugarBox">
                <div class="sugarNum">
                    <span>{{info.component[2].value}}</span>
                    {{info.component[2].unit}}
                </div>
                <div class="sugar">{{info.component[2].nutrientName}}</div>
            </div>
        </div>
    </div>
    <div class="titleBox" v-if="info.cooking_steps.length>0">
        <img class="titleImg2" src="https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/zuofan%402x.png" />
        <div class="title">烹饪步骤</div>
    </div>
    <div class="cookStepBox"  v-if="info.cooking_steps.length>0">
        <div v-for="(item, index) in info.cooking_steps" :key='index'>{{item}}</div>
    </div>
    <div class="titleBox">
        <img class="titleImg3" src="https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/more%402x.png" />
        <div class="title">更多营养元素</div>
    </div>
    <div class="moreElementBox">
        <div class="moreElementTitle">
            <div>营养元素</div>
            <div>每100g含量</div>
            <div>备注</div>
        </div>
        <div class="morenElement" v-for="(item, index) in info.nutrient" :key='index'>
            <div>{{item.nutrientName}}</div>
            <div>{{item.value}}{{item.unit}}</div>
            <div>{{item.remark}}</div>
        </div>
    </div>
  </div>
</template>

<script>
import { foodDetail } from '@/api/cookBook'
import { getBanFontSize } from '@/utils/utils.js'
export default {
  data() {
    return {
      info: {
        component: [],
        cooking_steps: '',
        gl: '',
        img_url: '',
        lowZhif: '',
        name: '',
        nutrient: [],
        rineng: ''
      },
      foodId: ''
    }
  },
  created() {
    // 判断是否安卓系统微信页面修改了字体
    getBanFontSize()
    this.foodId = this.$route.query.foodId
    this.init()
  },
  computed: {

  },
  methods: {
    init() {
      let that = this
      let datas = { id: this.foodId }
      foodDetail(datas).then(res => {
        // console.log(res)
        if (res.status == 0) {
          that.info = res.data
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  @import "@/assets/scss/cookBook/foodDetail.scss";
</style>
