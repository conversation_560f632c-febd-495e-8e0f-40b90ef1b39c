<template>
  <div>
    <div class="main" ref="main">
      <div class="calendarTitle">{{year}}年{{month}}月</div>
      <!-- 日历 -->
      <div v-if="days == 7" class="calendarBox sevenDays"
           style="background-image: url('https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/wechat/sugarController/riqibg1.png')">
        <div class="calendar">
          <div class="weekBox">
            <div>日</div>
            <div>一</div>
            <div>二</div>
            <div>三</div>
            <div>四</div>
            <div>五</div>
            <div>六</div>
          </div>
          <div class="dayBox">
            <div v-for="(item, index) in weekDateList" @click='changeCandleDate(item.day)'
                 :class="{active1:String(selectDay) == String(item.day)}" :key='index'>
              {{item.dateNum}}
            </div>
          </div>
        </div>
        <div @click="changeMonth(7)">
          <img src="https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/xiala.png" class="xiaLa">
        </div>
      </div>
      <!-- 日历 -->
      <div v-if="days == 31" ref="month" class="calendarBox monthDays"
           style="background-image: url('https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/wechat/sugarController/riqibg2.png')">
        <div class="calendar calendarMonth">
          <div class='header'>
            <div v-for="(item, index) in weekList" :key=index>{{item}}</div>
          </div>
          <div class='date-box'>
            <div v-for="(item, index) in dateArr" :key=index>
              <div :class="['date-head', String(selectDay) == String(item.day) ? 'active' : '']"
                   @click="changeCandleDate(item.day)">
                <div :class="selectDay == item.day ? 'active' : ''">{{item.dateNum}}</div>
              </div>
            </div>
          </div>
        </div>
        <div @click="changeMonth(31)" class="more">
          <img src="https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/shang.png" class="xiaLa xiaLaMonth"/>
        </div>
      </div>
      <div class="foodRemind" v-if="tip_str != ''">
        {{tip_str}}
      </div>
      <div class="pressureResult">
        <div class="dataBox">
          <div class="kcalTips">
            <img src="https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/huo%402x.png" class="kcalIcon">
            <div class="kcalWords">
              <div>今日总热卡量</div>
              <div>基于你的信息评估</div>
            </div>
          </div>
          <div class="kcalNum ">
            约
            <span>{{ hot_card == null ? '' : hot_card}}</span>
            <div>千卡</div>
          </div>
        </div>
        <div class="dataBox">
          <div class="weightTips">
            <img src="https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/tizhong%402x.png" class="weightIcon">
            <div>体重指数</div>
          </div>
          <div class="weightNum">
            BMI
            <span>{{ bmi == null ? '' : fixed(bmi) }}</span>
            <div :class="bmicolor(bmi)">{{bmiName}}</div>
          </div>
        </div>
      </div>
      <div class="titleBox">
        <div>星期{{week(weekNum)}}</div>
        <div data-url="/pages/home/<USER>/estimatedWeight/estimatedWeight" @click="bindNavigateTo">
          <img src="https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/call.png" class="call"/>
          估算重量
        </div>
      </div>
      <!-- 早餐 -->
      <div class="eatTimeBox breakfastBox" v-if="breakfast.length > 0">
        <div class="foodTitle">
          <img src="https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/zaocan%402x.png">
          <span>早餐</span>
          <div @click="openFoodModel('breakfast')" v-if="can_replace_breakfast==1">替换</div>
        </div>
        <div class="foodBox">
          <div class="food" v-for="(item, index) in breakfast" :key="index" @click="bindFoodDetail(item.food_id)">
            <img
              :src="item.image_url=='' ? 'https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/morencai.png': item.image_url+'?x-oss-process=image/resize,h_100'"/>
            <span>{{item.name}}</span>
            <div class="foodNum" v-if="item.type!=1">
              {{replaceValue(item.can_transform, unitType, item.weight)}}{{replaceType(item.can_transform, unitType,
              item.unit)}}
            </div>
            <div class="foodNum" v-if="item.type==1">
              <div v-for="(subItem, index) in item.row" :key='index'>
                {{subItem.name}}
                {{replaceValue(subItem.can_transform, unitType,
                subItem.weight)}}{{replaceType(subItem.can_transform,unitType,subItem.unit)}}
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 加餐 -->
      <div class="eatTimeBox snackBox" v-if="addition.length > 0">
        <div class="foodTitle">
          <img src="https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/jiacan%402x.png">
          <span>加餐</span>
          <div @click="openFoodModel('addition')">替换</div>
        </div>
        <div class="foodBox">
          <div class="food" v-for="(item, index) in addition" :key="index" @click="bindFoodDetail(item.food_id)">
            <img
              :src="item.image_url=='' ? 'https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/morencai.png' : item.image_url + '?x-oss-process=image/resize,h_100'"/>
            <span>{{item.name}}</span>
            <div class="foodNum" v-if="item.type != 1">
              {{replaceValue(item.can_transform,unitType,item.weight)}}{{replaceType(item.can_transform,unitType,item.unit)
              }}
            </div>
            <div class="foodNum" v-if="item.type == 1">
              <div v-for="(subItem, index) in item.row" :key='index'>
                {{subItem.name}}
                {{replaceValue(subItem.can_transform,unitType,subItem.weight)}}{{replaceType(subItem.can_transform,unitType,subItem.unit)}}
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 午餐 -->
      <div class="eatTimeBox lunchBox" v-if="lunch.length > 0">
        <div class="foodTitle">
          <img src="https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/wucan%402x.png">
          <span>午餐</span>
          <div @click="openFoodModel('lunch')" data-name="lunch">替换</div>
        </div>
        <div class="foodBox">
          <div class="food" v-for='(item, index) in lunch' :key='index' @click="bindFoodDetail(item.food_id)">
            <img
              :src="item.image_url=='' ? 'https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/morencai.png' : item.image_url+'?x-oss-process=image/resize,h_100'"/>
            <span>{{item.name}}</span>
            <div class="foodNum" v-if="item.type != 1">
              {{replaceValue(item.can_transform,unitType,item.weight)}}{{replaceType(item.can_transform,unitType,item.unit)}}
            </div>
            <div class="foodNum" v-if="item.type==1">
              <div v-for="(subItem, index) in item.row" :key='index'>
                {{subItem.name}}{{replaceValue(subItem.can_transform,unitType,subItem.weight)}}{{replaceType(subItem.can_transform,unitType,subItem.unit)
                }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 晚餐 -->
      <div class="eatTimeBox dinnerBox" v-if="dinner.length > 0">
        <div class="foodTitle">
          <img src="https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/wancan%402x.png"/>
          <span>晚餐</span>
          <div @click="openFoodModel('dinner')">替换</div>
        </div>
        <div class="foodBox">
          <div class="food" v-for='(item, index) in dinner' :key='index' @click="bindFoodDetail(item.food_id)">
            <img
              :src="item.image_url==''?'https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/morencai.png':item.image_url+'?x-oss-process=image/resize,h_100'"/>
            <span>{{item.name}}</span>
            <div class="foodNum" v-if="item.type!=1">
              {{replaceValue(item.can_transform,unitType,item.weight)}}{{replaceType(item.can_transform,unitType,item.unit)}}
            </div>
            <div class="foodNum" v-if="item.type==1">
              <div v-for="(subItem, index) in item.row" :key='index'>
                {{subItem.name}}{{replaceValue(subItem.can_transform,unitType,subItem.weight)}}{{replaceType(subItem.can_transform,unitType,subItem.unit)}}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="footer">
        <div>本食谱是基于你个人的健康数据评估计算得出的平衡膳食，供你参考。</div>
        <div>以下人群不适用于该产品，小于18岁、孕妇、严重肝肾功能不全、蛋白尿、痛风、癌症等。</div>
      </div>
      <!-- <div class="clockIn" v-show="today == selectDay">
        <p class="status" v-if="breakfast_status == 2 || addition_status == 2 || lunch_status == 2 || dinner_status == 2" @click="foodCheck">

      <div class="clockIn" v-show="(today == selectDay && source_type !=5)">
        <p class="status"
           v-if="breakfast_status == 2 || addition_status == 2 || lunch_status == 2 || dinner_status == 2"
           @click="foodCheck">
          <i>早餐</i>
          <span v-show="breakfast_status == 2"
                style="background:url('https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/blank.jpg') no-repeat center center;background-size:100% 100%;"></span>
          <span v-show="breakfast_status != 2"
                style="background:url('https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/checked.jpg') no-repeat center center;background-size:100% 100%;"></span>
          <i>加餐</i>
          <span v-show="addition_status == 2"
                style="background:url('https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/blank.jpg') no-repeat center center;background-size:100% 100%;"></span>
          <span v-show="addition_status != 2"
                style="background:url('https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/checked.jpg') no-repeat center center;background-size:100% 100%;"></span>
          <i>午餐</i>
          <span v-show="lunch_status == 2"
                style="background:url('https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/blank.jpg') no-repeat center center;background-size:100% 100%;"></span>
          <span v-show="lunch_status != 2"
                style="background:url('https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/checked.jpg') no-repeat center center;background-size:100% 100%;"></span>
          <i>晚餐</i>
          <span v-show="dinner_status == 2"
                style="background:url('https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/blank.jpg') no-repeat center center;background-size:100% 100%;"></span>
          <span v-show="dinner_status != 2"
                style="background:url('https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/checked.jpg') no-repeat center center;background-size:100% 100%;"></span>
        </p>
        <p class="status" v-if="today != selectDay && breakfast_status  != 2 && lunch_status  !=2 && dinner_status != 2 && addition_status !=2">
          <i>早餐</i>
          <span v-show="breakfast_status == 2"
                style="background:url('https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/blank.jpg') no-repeat center center;background-size:100% 100%;"></span>
          <span v-show="breakfast_status != 2"
                style="background:url('https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/checked.jpg') no-repeat center center;background-size:100% 100%;"></span>
          <i>加餐</i>
          <span v-show="addition_status == 2"
                style="background:url('https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/blank.jpg') no-repeat center center;background-size:100% 100%;"></span>
          <span v-show="addition_status != 2"
                style="background:url('https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/checked.jpg') no-repeat center center;background-size:100% 100%;"></span>
          <i>午餐</i>
          <span v-show="lunch_status == 2"
                style="background:url('https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/blank.jpg') no-repeat center center;background-size:100% 100%;"></span>
          <span v-show="lunch_status != 2"
                style="background:url('https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/checked.jpg') no-repeat center center;background-size:100% 100%;"></span>
          <i>晚餐</i>
          <span v-show="dinner_status == 2"
                style="background:url('https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/blank.jpg') no-repeat center center;background-size:100% 100%;"></span>
          <span v-show="dinner_status != 2"
                style="background:url('https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/checked.jpg') no-repeat center center;background-size:100% 100%;"></span>
        </p>
        <div class="clearfix checkBtn">
          <p class="clockOut"
             v-if="breakfast_status == 2 || addition_status == 2 || lunch_status == 2 || dinner_status == 2"
             @click="foodCheck">饮食打卡></p>
          <p class="clock" v-else>打卡完成</p>
        </div>
      </div> -->
      <!-- <div class="otherClock" v-show="today != selectDay && breakfast_status  != 2 && lunch_status  !=2 && dinner_status != 2 && addition_status !=2 ">
      </div>
      <div class="otherClock"
           v-show="today != selectDay && breakfast_status  != 2 && lunch_status  !=2 && dinner_status != 2 && addition_status !=2 && source_type!=5 ">
        已打卡
      </div> -->
    </div>
    <div class="mask" v-if="remarkTips"></div>
    <div class="remarkTipsModal" v-if="remarkTips">
      <img class="imgTips1" @click="closeTips" src="https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/closeTips.png" />
      <div class="contentTips">
        <img class="imgTips2" src="https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/goods%402x.png" />
        <div class="titleTips1" v-if="finish_day == 1">完成</div>
        <div class="titleTips1" v-if="finish_day > 1">连续完成{{finish_day}}天</div>
        <div class="titleTips2">【今日食谱】</div>
        <div class="titleTips3">
          <span class="spanTips1"></span>
          <span class="spanTips2">已获得健康金</span>
          <img class="imgTips3" src="https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/wechat/sugarControllerVIP/V1.3.0/coin.png" />
          <span class="spanTips3">+10</span>
          <span class="spanTips4"></span>
        </div>
        <div class="btnTips" @click="closeTips">我知道了</div>
      </div>
    </div>
    <!-- <div class="additionalModal" v-if="additionalCheck">
    <div class="mask" v-if="remark" @click="close"></div>
    <div class="additionalModal" v-if="additionalCheck ">
      <h2><span></span><span>{{currentCheckTime}}饮食打卡</span><span @click="close"></span></h2>
      <p class="title">记录也是一种健康，让自己的饮食更规律</p>
      <div class="detail clearfix" v-for="(item, index) in checkList" :key=index>
        <p class="left"><img :src="item.url"/><span>{{item.time}}</span></p>
        <p class="smile right" v-if="todayHours>=index && item.status != 2">
          <img src="https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/Rounded.png"/><span>已打卡</span>
        </p>
        <p class="right" v-if="todayHours>=index && item.status == 2" @click="checkIn(item.param)"><span
          class="buka">打卡</span></p>
        <p class="right" v-if="todayHours<index"><span class="waiting">还没到就餐时间</span></p>
      </div>
    </div> -->
  </div>
</template>

<script>
  import {getNowTime} from '@/utils/utils'
  import {getCookBook, getDateInit, checkInFood, getCheckInDetails, getFirstOpen} from '@/api/cookBook'
  import {Toast} from 'vant'
  import moment from 'moment'
  import {getBanFontSize} from '@/utils/utils.js'

  export default {
    data() {
      return {
        lang: '',
        bmi: '',
        hot_card: '',
        color: '',
        weekDay: '',
        weekDayName: '',
        unitType: 1,
        currentDate: '',
        allDateList: [],
        recipesList: [],
        weekDateList: [],
        type: 0,
        bmiName: '正常',
        changeBday: false,
        days: 7,
        year: '',
        month: '',
        selectDay: '',
        today: '',
        weekList: ['日', '一', '二', '三', '四', '五', '六'],
        dateArr: [],
        switchUserInfo: false,
        isCheck: false,
        changeFoodModel: false,
        breakfast: [],
        lunch: [],
        dinner: [],
        addition: [],
        userInfo: {},
        bday: '',
        height: '',
        weight: '',
        can_replace_breakfast: 0,
        weekNum: 1,
        heightBg: 1,
        windowHeight: 2000,
        windowWidth: 0,
        lookAtMore: '查看更多',
        currentId: '',
        clickDisable: false,
        result: ['d', 'a'],
        remark: false,
        checkTime: '',
        additionalCheck: false,
        source_type: '',
        breakfast_status: 0,
        addition_status: 0,
        lunch_status: 0,
        dinner_status: 0,
        checkList: [{
          time: '早餐',
          param: 'breakfast_status',
          url: 'https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/zaocan%402x.png',
          status: ''
        },
          {
            time: '加餐',
            param: 'addition_status',
            url: 'https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/jiacan%402x.png',
            status: ''
          },
          {
            time: '午餐',
            param: 'lunch_status',
            url: 'https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/wucan%402x.png ',
            status: ''
          },
          {
            time: '晚餐',
            param: 'dinner_status',
            url: 'https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/wancan%402x.png',
            status: ''
          }
        ],
        todayHours: 0,
        currentCheckTime: '',
        tip_str: '', // 如果用户在月报中选择了加强饮食，在未来4周内，食谱出现这句话。
        remarkTips: false,
        finish_day: 1,
        isOrNoStatus: 0,
      }
    },
    created() {
      // 判断是否安卓系统微信页面修改了字体
      getBanFontSize()
      // this.selectDay = getNowTime().ctsDay
      // this.today = getNowTime().ctsDay
      let currentDay = new Date()
      this.selectDay = moment(currentDay).format('YYYY-MM-DD')
      // console.log(currentDay, this.selectDay)
      this.today = moment(currentDay).format('YYYY-MM-DD')
      this.source_type = this.$route.query.source_type
      // this.isOrNoStatus = this.$route.query.status
      // console.log(this.selectDay, this.today)
      this.init()
      this.dateInit()
    },
    computed: {},
    mounted() {
      // 如果检测到页面是从“往返缓存”中读取的，刷新页面
      if (this.source_type == 2) {
        window.addEventListener('pageshow', (e) => {
          if (e.persisted || (window.performance && window.performance.navigation.type == 2)) {
            location.reload()
          }
        }, false)
      }
    },
    methods: {
      init(currentTime, initType) {
        let that = this
        if (currentTime == undefined) {
          currentTime = this.selectDay
        }
        getFirstOpen({date: this.selectDay}).then(res => {
          if (res.status == 0) {
            that.isOrNoStatus = res.data.status
          } else {
            Toast(res.msg)
          }
        })
        getDateInit({}).then(res => {
          if (res.status == 0) {
            // console.log(res)
            if (res.data.bday === '0000-00-00') {
              that.bday = '1970-01-01'
              that.changeBday = true
              that.initBoay = true
            } else {
              that.bday = res.data.bday
              that.changeBday = false
              that.initBoay = false
            }
            that.weekDate(res.data.week_first_day)
            that.bmi = res.data.bmi
            that.bmiName = res.data.weight_name
            that.height = res.data.height
            that.weight = res.data.weight
            that.hot_card = res.data.hot_card
          } else {
            Toast(res.msg)
          }
        })
        let datas = {assign_at: currentTime}
        getCookBook(datas).then(res => {
          // console.log(res)
          if (res.status == 0) {
            that.selectDay = currentTime
            that.currentId = res.data.id
            that.breakfast = res.data.breakfast
            that.lunch = res.data.lunch
            that.can_replace_breakfast = res.data.can_replace_breakfast
            that.dinner = res.data.dinner
            that.addition = res.data.addition
            that.weekNum = res.data.week
            that.tip_str = res.data.tip_str
            that.breakfast_status = res.data.breakfast_status
            that.addition_status = res.data.addition_status
            that.lunch_status = res.data.lunch_status
            that.dinner_status = res.data.dinner_status
            that.checkList[0].status = res.data.breakfast_status
            that.checkList[1].status = res.data.addition_status
            that.checkList[2].status = res.data.lunch_status
            that.checkList[3].status = res.data.dinner_status
          } else {
            Toast(res.msg)
          }
        }).then(res => {
          if(initType != 'click') {
            getCheckInDetails({type: 1, date: this.selectDay}).then(res => {
              console.log(res)
              if (res.status == 0) {
                that.finish_day = res.data.finish_day
                if(that.isOrNoStatus == 0) {
                  that.remarkTips = true
                } else {
                  that.remarkTips = false
                }
              } else {
                Toast(res.msg)
              }
            })
          }
        })
      },
      // JS获取指定日期的前一天，后一天
      getNextDate(date, day) {
        let dd = new Date(date)
        dd.setDate(dd.getDate() + day)
        let y = dd.getFullYear()
        let m = dd.getMonth() + 1 < 10 ? '0' + (dd.getMonth() + 1) : dd.getMonth() + 1
        let d = dd.getDate() < 10 ? '0' + dd.getDate() : dd.getDate()
        return y + '-' + m + '-' + d
      },
      /**
       * 返回的数据处理
       */
      weekDate(firstDay) {
        let totalDays = 7
        let list = []
        for (let i = 0; i < totalDays; i++) {
          let data = this.getNextDate(firstDay, i)
          list[i] = {
            day: data,
            dateNum: parseInt(data.substring(8))
          }
        }
        // console.log(list)
        this.weekDateList = list
      },
      // 切换日期
      changeCandleDate(days) {
        // 点击防重
        if (this.clickLimit()) return false
        this.init(days, 'click')
      },
      changeMonth(day) {
        let that = this
        if (day == 7) {
          this.days = 31
        } else if (day == 31) {
          let info = []
          this.days = 7
          that.weekDateList.forEach((v, k) => {
            if (v.day == that.selectDay) {
              info = v
            }
          })
          if (info.length == 0) {
            that.init(that.today, 'click')
          }
        }
      },
      // 估算重量
      bindNavigateTo() {
        this.noDoubleTap(() => {
          this.$router.push({
            name: 'Cookbook.Estimated.Weight',
            query: {'source_type': this.source_type}
          })
        })
      },
      // 食物详情
      bindFoodDetail(id) {
        console.log(id)
        if (id != undefined && id != '' && id > 0) {
          this.noDoubleTap(() => {
            this.$router.push({
              name: 'Cookbook.Food.Detail',
              query: {foodId: id, 'source_type': this.source_type}
            })
          })
        } else {
          Toast('暂无该食材的详情')
        }
      },
      // 食物替换
      openFoodModel(times) {
        this.$router.push({
          name: 'Cookbook.Switch.Food',
          query: {name: times, foodId: this.currentId, 'source_type': this.source_type}
        })
      },
      week(val) {
        let num = parseInt(val)
        let actions = {
          0: ['日'],
          1: ['一'],
          2: ['二'],
          3: ['三'],
          4: ['四'],
          5: ['五'],
          6: ['六'],
          'default': ['空']
        }
        var action = actions[num] || actions['default']
        var result = action[0]
        return result
      },
      dateInit(setYear, setMonth) {
        // 全部时间的月份都是按0~11基准，显示月份才+1
        let dateArr = [] // 需要遍历的日历数组数据
        let arrLen = 0 // dateArr的数组长度
        let now = new Date()
        let year = now.getFullYear()
        let nextYear = 0
        let month = now.getMonth() // 没有+1方便后面计算当月总天数
        this.year = year
        this.month = month + 1
        let nextMonth = (month + 1) > 11 ? 1 : (month + 1)
        let startWeek = new Date(year, (month), 1).getDay() // 目标月1号对应的星期
        let dayNums = new Date(year, nextMonth, 0).getDate() // 获取目标月有多少天
        let obj = {}
        let num = 0
        if (month + 1 > 11) {
          nextYear = year + 1
          dayNums = new Date(nextYear, nextMonth, 0).getDate()
        }
        arrLen = startWeek + dayNums
        for (let i = 0; i < arrLen; i++) {
          if (i >= startWeek) {
            num = i - startWeek + 1
            var m = month + 1 < 10 ? '0' + (month + 1) : month + 1
            var d = num < 10 ? '0' + num : num
            obj = {
              day: year + '-' + m + '-' + d,
              dateNum: num,
              weight: 5
            }
          } else {
            obj = {}
          }
          dateArr[i] = obj
        }
        this.dateArr = dateArr
      },
      replaceType(replaceType, type, value) {
        if (replaceType == 0) {
          return value
        } else {
          if (type == 1) {
            return value
          } else {
            return '两'
          }
        }
      },
      replaceValue(replaceType, type, value) {
        if (replaceType == 0) {
          return value
        } else {
          if (type == 1) {
            return value
          } else {
            let res = parseInt(value / 50)
            if (res == 0) {
              return 1
            } else {
              return res
            }
          }
        }
      },
      fixed(value) {
        if (value != null && value != '' && value != undefined) {
          return value.toFixed(1)
        } else {
          return value
        }
      },
      bmicolor(bmi) {
        if (bmi < 18.5) {
          return 'color1'
        } else if (bmi > 18.5 && bmi < 23.9) {
          return 'color2'
        } else if (bmi > 23.9 && bmi < 28) {
          return 'color3'
        } else if (bmi > 28) {
          return 'color4'
        }
      },
      // 打卡
      foodCheck() {
        let myDate = new Date()
        let h = myDate.getHours()
        this.currentCheckTime = moment(this.selectDay).format('MM月DD日')
        this.remark = true
        this.additionalCheck = true
        // console.log(this.checkList)
        if (h < 4) {
          Toast('早上四点以后可以打早餐卡')
          return false
        } else if (h >= 4 && h < 8) {
          this.checkTime = '早餐'
          this.todayHours = 0
        } else if (h >= 8 && h < 10) {
          this.checkTime = '加餐'
          this.todayHours = 1
        } else if (h >= 10 && h < 15) {
          this.checkTime = '午餐'
          this.todayHours = 2
        } else if (h >= 15) {
          this.checkTime = '晚餐'
          this.todayHours = 3
        }
      },
      /**
       * 完成任务分享弹框
       */
      completeDietTask() {
        const UA = navigator.userAgent
        const isAndroid = UA.indexOf('Android') > -1 || UA.indexOf('Adr') > -1 // android终端
        const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
        // 原生app方法名称：completeDietTask
        if (isAndroid) {
          console.log('安卓')
          window.android.completeDietTask()
        } else if (isIOS) {
          console.log('苹果')
          window.webkit.messageHandlers.completeDietTask.postMessage('')
        } else {
          this.$toast('跳转失败')
        }
      },
      // 关闭打卡
      close() {
        this.remark = false
        this.additionalCheck = false
        this.replacementCard = false
        this.todayHours = 0

        if (this.source_type == 2) {

          if (this.checkList[0].status != 2 && this.checkList[1].status != 2 && this.checkList[2].status != 2 && this.checkList[3].status != 2) {
            //完成任务分享弹框
            this.completeDietTask()
          }

        }
      },
      // 打卡
      // checkIn(param) {
      //   let that = this
      //   let datas = {
      //     name: param,
      //     status: 1
      //   }
      //   checkInFood(datas).then(res => {
      //     console.log(res)
      //     if (res.status == 0) {
      //       Toast('打卡成功')
      //       that.init()
      //       that.dateInit()
      //     } else {
      //       Toast(res.msg)
      //     }
      //   })
      // },
      closeTips() {
        this.remarkTips = false
      },
      /**
       * 点击防重
       */
      clickLimit() {
        if (this.clickDisable) {
          return true
        } else {
          this.clickDisable = true
          setTimeout(() => {
            this.clickDisable = false
          }, 600)
          return false
        }
      }
    }
  }
</script>

<style lang="scss" scoped>
  @import "@/assets/scss/cookBook/recipes.scss";
</style>
