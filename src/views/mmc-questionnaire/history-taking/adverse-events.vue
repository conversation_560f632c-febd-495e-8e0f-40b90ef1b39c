<template>
  <div class="adverse-events">
    <div class="container">
      <div>
        <comp-questionnaire-list :list="question_list" />
      </div>
    </div>

    <van-overlay :show="show_loading" class-name="mmc-questionnaire-loading-overlay">
      <van-loading type="spinner" />
    </van-overlay>
  </div>
</template>

<script>
import api from '@/api/mmc-questionnaire.js';
import compQuestionnaireList from '../components/comp-questionnaire-list.vue';
import mixin from '../common/mixins/mixin';

export default {
  name: 'adverseEvents',
  components: {
    compQuestionnaireList,
  },
  data() {
    return {
      user_id: '',
      visit_level: '',
      sex: '',
      hosp_id: '',
      show_loading: false,
      question_list: [
        {
          id: 1,
          composing: 'block',
          title: null,
          children: [
            {
              subquestion_id: '437',
              subquestion_name: '1、有无心脑血管事件发生？',
              show: true,
              inline_check: true,
              btn_group: ['有', '无'],
              checked: false,
              datetime_key: null,
              datetime_value: null,
            },
            {
              subquestion_id: '438',
              subquestion_name: '2、有无低血糖至急诊就诊？',
              show: true,
              inline_check: true,
              btn_group: ['有', '无'],
              checked: false,
              datetime_key: null,
              datetime_value: null,
            },
            {
              subquestion_id: '439',
              subquestion_name: '3、有无新发恶性肿瘤？（参考病史采集）',
              show: true,
              inline_check: true,
              btn_group: ['有', '无'],
              checked: false,
              datetime_key: null,
              datetime_value: null,
            },
            {
              subquestion_id: '440',
              subquestion_name: '4、有无住院',
              show: true,
              inline_check: true,
              btn_group: ['有', '无'],
              checked: false,
              datetime_key: null,
              datetime_value: null,
              children: [
                {
                  option_id: '43601',
                  option_name: '因以上事件(问题1,2,3中的事件)住院',
                  checked: false,
                },
                {
                  option_id: '43602',
                  option_name: '因其他事件住院',
                  checked: false,
                },
              ],
            },
            {
              subquestion_id: '435',
              subquestion_name: '5、是否经过眼底激光治疗？',
              show: true,
              inline_check: true,
              btn_group: ['有', '无'],
              checked: false,
              datetime_key: null,
              datetime_value: null,
            },
            {
              subquestion_id: '441',
              subquestion_name: '6、是否有医生认为需要补充的不良事件（至急诊或需要住院的尿路感染，新发骨质疏松，骨折等）',
              show: true,
              inline_check: true,
              btn_group: ['有', '无'],
              checked: false,
              datetime_key: null,
              datetime_value: null,
            },
            {
              question_id: null,
              subquestion_id: '',
              subquestion_name: '7、主要不良事件列表',
              show: true,
              table_name: 'adverse-events',
              inline_check: null,
              checked: null,
              datetime_key: null,
              datetime_value: null,
              data: {
                thead: ['不良事件名称', '开始时间', '当前状态', '缓解/死亡时间', '创建时间', '最后更新时间', '最后记录人'],
                tbody: [],
              },
            },
          ],
        },
      ],
    };
  },
  mixins: [mixin],
  created() {
    const {
      user_id,
      visit_level,
      sex,
      hosp_id,
    } = this.$route.query;
    this.user_id = user_id;
    this.visit_level = visit_level;
    this.sex = sex;
    this.hosp_id = hosp_id;

    this.show_loading = true;

    this.getAdverseEventsTable();

    this.getAdverseEventsData();
  },
  methods: {
    // 获取不良事件表
    async getAdverseEventsTable() {
      const res = await api.adverseEventList(this.user_id);

      if (res.status === 0) {
        const list = res.data || [];

        this.$set(this.question_list[0].children[6].data, 'tbody', list);
      }
    },
    // 获取不良事件数据
    async getAdverseEventsData() {
      const res = await api.qssInfo({
        sex: this.sex,
        user_id: this.user_id,
        hosp_id: this.hosp_id,
        visit_level: this.visit_level,
        template_id: 54,
      }).finally(() => {
        this.show_loading = false;
      });

      if (res.status === 0) {
        const list = res.data.question_data || [];

        this.setQuestionList(list);
      }
    },
    // 设置题目
    setQuestionList(list) {
      if (list.length > 0) {
        list.forEach(item => {
          // 第 1 题
          if (item.subquestion_id === '437') {
            this.setInlineCheckbox(item, 0, 0);
          }
          // 第 2 题
          if (item.subquestion_id === '438') {
            this.setInlineCheckbox(item, 0, 1);
          }
          // 第 3 题
          if (item.subquestion_id === '439') {
            this.setInlineCheckbox(item, 0, 2);
          }
          // 第 4 题
          if (item.subquestion_id === '440') {
            this.setInlineCheckbox(item, 0, 3);
            
            const answer = this.checkAnswerValue(item);
            if (!answer || answer.answer_value === '无') {
              this.question_list[0].children[3].children = [];
            }
          }
          if (item.subquestion_id === '436') {
            const questions = ['43601', '43602'];
            this.setQuestionCheckedStatus(item, questions, 0, 3);
          }
          // 第 5 题
          if (item.subquestion_id === '435') {
            this.setInlineCheckbox(item, 0, 4);
          }
          // 第 6 题
          if (item.subquestion_id === '441') {
            this.setInlineCheckbox(item, 0, 5);
          }
        })
      }
    },
  },
};
</script>

<style scoped lang="scss">
.adverse-events {
  padding: 0 15px;
}
</style>
