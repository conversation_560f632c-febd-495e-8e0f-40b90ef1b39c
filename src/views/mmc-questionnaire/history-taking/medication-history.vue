<template>
  <div class="medication-history">
    <van-collapse v-model="activeNames">
      <van-collapse-item title="降糖药物" name="1">
        <comp-questionnaire-list :list="[question_list[0]]" />
      </van-collapse-item>
      <van-collapse-item title="其他药物" name="2">
        <comp-questionnaire-list :list="[question_list[1]]" />
      </van-collapse-item>
    </van-collapse>

    <van-overlay :show="show_loading" class-name="mmc-questionnaire-loading-overlay">
      <van-loading type="spinner" />
    </van-overlay>
  </div>
</template>

<script>
import api from '@/api/mmc-questionnaire.js';
import compQuestionnaireList from '../components/comp-questionnaire-list.vue';
import mixin from '../common/mixins/mixin';

export default {
  name: 'medicationHistory',
  components: {
    compQuestionnaireList,
  },
  data() {
    return {
      user_id: '',
      visit_level: '',
      sex: '',
      hosp_id: '',
      show_loading: false,
      activeNames: ['1', '2'],
      question_list: [
        {
          id: 1,
          composing: 'block',
          title: null,
          children: [
            {
              subquestion_id: '374',
              subquestion_name: '1、您本次就诊之前是否正在使用非胰岛素类降糖药物?',
              show: true,
              inline_check: true,
              checked: false,
              datetime_key: null,
              datetime_value: null,
            },
            {
              subquestion_id: '530',
              subquestion_name: '2、您糖尿病确诊至今，是否使用过胰岛素？',
              show: true,
              inline_check: null,
              checked: null,
              datetime: null,
              children: [
                {
                  option_id: '51259',
                  option_name: '是,目前还在使用',
                  checked: false,
                },
                {
                  option_id: '51260',
                  option_name: '否',
                  checked: false,
                },
                {
                  option_id: '53001',
                  option_name: '是,目前已停用',
                  checked: false,
                },
                {
                  option_id: null,
                  option_name: null,
                  checked: false,
                },
              ],
            },
            {
              subquestion_id: '970',
              subquestion_name: '3、本次就诊是否有降糖药物或其他药物的调整?',
              show: true,
              inline_check: true,
              checked: false,
              datetime_key: null,
              datetime_value: null,
            },
            {
              question_id: null,
              subquestion_id: '',
              subquestion_name: '4、降糖药物列表',
              show: true,
              table_name: 'medication-history-hypoglycemic',
              inline_check: null,
              checked: null,
              datetime_key: null,
              datetime_value: null,
              data: {
                list: [],
                used: [],
                all: [],
                stop: [],
              },
            },
          ],
        },
        {
          id: 2,
          composing: 'block',
          title: null,
          children: [
            {
              subquestion_id: '975',
              subquestion_name: '1、您本次就诊之前是否正在使用其他药物?',
              show: true,
              inline_check: true,
              checked: false,
              datetime_key: null,
              datetime_value: null,
            },
            {
              question_id: null,
              subquestion_id: '',
              subquestion_name: '2、其他药物列表',
              show: true,
              table_name: 'medication-history-other',
              inline_check: null,
              checked: null,
              datetime_key: null,
              datetime_value: null,
              data: {
                list: [],
                used: [],
                all: [],
                stop: [],
              },
            },
          ],
        },
      ],
    }
  },
  mixins: [mixin],
  created() {
    const {
      user_id,
      visit_level,
      sex,
      hosp_id,
    } = this.$route.query;
    this.user_id = user_id;
    this.visit_level = visit_level;
    this.sex = sex;
    this.hosp_id = hosp_id;

    this.getCurrentVisit();

    this.getmMdicationHistoryData();
  },
  methods: {
    // 获取最新访视
    async getCurrentVisit() {
      this.show_loading = true;

      const res = await api.getCurrentVisitLevel(this.user_id).finally(() => {
        this.show_loading = false;
      });

      if (res.status === 0) {
        const current_visit = res.data.current_visit;

        if (current_visit * 1 === this.visit_level * 1) {
          this.getHypoglycemicDrugList('9999-12-30');
        } else {
          this.getDrugDateLine();
        }
      }
    },
    // 获取用药访视基线日期
    async getDrugDateLine() {
      const res = await api.getDrugDateBaseLine({
        user_id: this.user_id,
        visit_level: this.visit_level,
      });

      if (res.status === 0) {
        this.getHypoglycemicDrugList(res.data);
      }
    },
    // 获取降糖用药列表
    async getHypoglycemicDrugList(date) {
      const res1 = await api.getDrugList({
        user_id: this.user_id,
        dead_line: date,
        category_id: 1,
      });

      if (res1.status === 0) {
        this.setMedicationHistoryHypoglycemicTableData(res1.data, 0, 3);
      }
      
      const res2 = await api.getDrugList({
        user_id: this.user_id,
        dead_line: date,
        category_id: 2,
      });

      if (res2.status === 0) {
        this.setMedicationHistoryHypoglycemicTableData(res2.data, 1, 1);
      }
    },
    // 获取用药史数据
    async getmMdicationHistoryData() {
      const res = await api.qssInfo({
        sex: this.sex,
        user_id: this.user_id,
        hosp_id: this.hosp_id,
        visit_level: this.visit_level,
        template_id: 23,
      });

      if (res.status === 0) {
        const list = res.data.question_data || [];

        this.setQuestionList(list);
      }
    },
    // 设置题目
    setQuestionList(list) {
      if (list.length > 0) {
        list.forEach(item => {
          // 降糖药物
          // 第 1 题
          if (item.subquestion_id === '374') {
            this.setInlineCheckbox(item, 0, 0);
          }
          // 第 2 题
          if (item.subquestion_id === '530') {
            const questions = ['51259', '51260', '53001'];
            this.setQuestionCheckedStatus(item, questions, 0, 1);
          }
          if (item.question_id === '530') {
            this.setHypoglycemicInputValue(item, 0, 1);
          }
          // 第 3 题
          if (item.subquestion_id === '970') {
            this.setInlineCheckbox(item, 0, 2);
          }

          // 其他药物
          if (item.subquestion_id === '975') {
            this.setInlineCheckbox(item, 1, 0);
          }
        })
      }
    },
    // 降糖药物
    setHypoglycemicInputValue(item, i, j) {
      const answer = this.checkAnswerValue(item);
      if (answer) {
        let value = answer.answer_value;

        if (item.subquestion_id === '51259001') {
          value = `已使用了${value}年`;
          this.question_list[i].children[j].children[0].value1 = value;
        }
        if (item.subquestion_id === '51259002') {
          value = `${value}个月`;
          this.question_list[i].children[j].children[0].value2 = value;
        }

        if (item.subquestion_id === '530' && item.subquestion_id === '530') {
          const checked1 = this.question_list[i].children[j].children[0].checked;
          const checked2 = this.question_list[i].children[j].children[2].checked;

          if (checked1 || checked2) {
            const value1 = this.question_list[i].children[j].children[0].value1;
            const value2 = this.question_list[i].children[j].children[0].value2;
            this.question_list[i].children[j].children[3].value = value1 + value2;
          }
        }
      }
    },
    // 降糖药物
    setMedicationHistoryHypoglycemicTableData(data, i, j) {
      const { used, all, stop } = data;
      this.$set(this.question_list[i].children[j].data, 'list', used);
      this.$set(this.question_list[i].children[j].data, 'used', used);
      this.$set(this.question_list[i].children[j].data, 'all', all);
      this.$set(this.question_list[i].children[j].data, 'stop', stop);
    },
  }
}
</script>

<style scoped lang="scss">
// .medication-history {}
</style>
