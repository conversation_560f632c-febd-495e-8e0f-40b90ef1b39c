<template>
  <div class="menstrual-obstetrical-history">
    <div class="container">
      <div>
        <comp-questionnaire-list :list="question_list" />
      </div>
    </div>

    <van-overlay :show="show_loading" class-name="mmc-questionnaire-loading-overlay">
      <van-loading type="spinner" />
    </van-overlay>
  </div>
</template>

<script>
import api from '@/api/mmc-questionnaire.js';
import compQuestionnaireList from '../components/comp-questionnaire-list.vue';
import mixin from '../common/mixins/mixin';

export default {
  name: 'menstrualObstetricalHistory',
  components: {
    compQuestionnaireList,
  },
  data() {
    return {
      show_loading: false,
      question_list: [
        {
          id: 1,
          composing: 'block',
          title: null,
          children: [
            {
              subquestion_id: '533',
              subquestion_name: '1、月经史：初潮：#岁',
              subquestion_name_format: [
                {
                  key: 'normal',
                  value: '1、月经史：初潮：',
                },
                {
                  key: 'input',
                  value: '',
                },
                {
                  key: 'normal',
                  value: '岁',
                },
              ],
              show: true,
              inline_check: null,
              inline_input: true,
              checked: null,
              datetime_key: null,
              datetime_value: null,
            },
            {
              subquestion_id: '534',
              subquestion_name: '2、请问您还有月经吗？',
              show: true,
              inline_check: null,
              checked: null,
              datetime_key: null,
              datetime_value: null,
              children: [
                {
                  option_id: '51261',
                  option_name: '是',
                  checked: false,
                  prefix: '最近一次月经来潮的时间：',
                },
                {
                  option_id: '51261',
                  option_name: '否',
                  checked: false,
                  prefix: '绝经年龄：',
                },
              ],
            },
            {
              subquestion_id: '949',
              subquestion_name: '3、绝经原因?',
              show: true,
              inline_check: null,
              checked: null,
              datetime_key: null,
              datetime_value: null,
              children: [
                {
                  option_id: '52798',
                  option_name: '自然原因',
                  checked: false,
                },
                {
                  option_id: '52799',
                  option_name: '特殊原因',
                  checked: false,
                },
              ],
            },
            {
              subquestion_id: '535',
              subquestion_name: '4、原发性闭经？',
              show: true,
              inline_check: true,
              checked: false,
              datetime_key: null,
              datetime_value: null,
            },
            {
              subquestion_id: '536',
              subquestion_name: '5、继发性闭经？',
              show: true,
              inline_check: true,
              checked: false,
              datetime_key: null,
              datetime_value: null,
            },
            {
              subquestion_id: '537',
              subquestion_name: '6、月经稀发？',
              show: true,
              inline_check: true,
              checked: false,
              datetime_key: null,
              datetime_value: null,
            },
            {
              subquestion_id: '538',
              subquestion_name: '7、足月产#个， 早产#个， 流产#次， 存活#个',
              subquestion_name_format: [
                {
                  key: 'normal',
                  value: '7、足月产',
                },
                {
                  key: 'input',
                  value: '',
                },
                {
                  key: 'normal',
                  value: '个， 早产',
                },
                {
                  key: 'input',
                  value: '',
                },
                {
                  key: 'normal',
                  value: '个， 流产',
                },
                {
                  key: 'input',
                  value: '',
                },
                {
                  key: 'normal',
                  value: '次， 存活',
                },
                {
                  key: 'input',
                  value: '',
                },
                {
                  key: 'normal',
                  value: '个',
                },
              ],
              show: true,
              inline_check: null,
              inline_input: true,
              checked: null,
              datetime_key: null,
              datetime_value: null,
            },
            {
              subquestion_id: '539',
              subquestion_name: '8、病理妊娠？',
              show: true,
              inline_check: null,
              checked: null,
              datetime_key: null,
              datetime_value: null,
              children: [
                {
                  option_id: '51269',
                  option_name: '是',
                  checked: false,
                },
                {
                  option_id: '51270',
                  option_name: '否',
                  checked: false,
                },
                {
                  option_id: '539001',
                  option_name: '不适用(无妊娠史)',
                  checked: false,
                },
              ],
            },
            {
              subquestion_id: '541',
              subquestion_name: '9、妊高症？',
              show: true,
              inline_check: null,
              checked: null,
              datetime_key: null,
              datetime_value: null,
              children: [
                {
                  option_id: '51273',
                  option_name: '是',
                  checked: false,
                },
                {
                  option_id: '51274',
                  option_name: '否',
                  checked: false,
                },
                {
                  option_id: '541001',
                  option_name: '不适用(无妊娠史)',
                  checked: false,
                },
              ],
            },
            {
              subquestion_id: '542',
              subquestion_name: '10、GDM史？',
              show: true,
              inline_check: null,
              checked: null,
              datetime_key: null,
              datetime_value: null,
              children: [
                {
                  option_id: '51275',
                  option_name: '是',
                  checked: false,
                },
                {
                  option_id: '51276',
                  option_name: '否',
                  checked: false,
                },
                {
                  option_id: '542001',
                  option_name: '不适用(无妊娠史)',
                  checked: false,
                },
              ],
            },
            {
              subquestion_id: '543',
              subquestion_name: '11、您生孩子时的分娩方式？（可多选）',
              show: true,
              inline_check: null,
              checked: null,
              datetime_key: null,
              datetime_value: null,
              children: [
                {
                  option_id: '51277',
                  option_name: '顺产',
                  checked: false,
                },
                {
                  option_id: '51278',
                  option_name: '剖腹产',
                  checked: false,
                },
                {
                  option_id: '51279',
                  option_name: '不清楚',
                  checked: false,
                },
                {
                  option_id: '543001',
                  option_name: '不适用(无生育史)',
                  checked: false,
                },
              ],
            },
            {
              subquestion_id: '544',
              subquestion_name: '12、您出生时是？（本人）',
              show: true,
              inline_check: null,
              checked: null,
              datetime_key: null,
              datetime_value: null,
              children: [
                {
                  option_id: '51280',
                  option_name: '顺产',
                  checked: false,
                },
                {
                  option_id: '51281',
                  option_name: '剖腹产',
                  checked: false,
                },
                {
                  option_id: '51282',
                  option_name: '不清楚',
                  checked: false,
                },
              ],
            },
            {
              subquestion_id: '545',
              subquestion_name: '13、您是早产儿吗？（本人）',
              show: true,
              inline_check: true,
              checked: false,
              datetime_key: null,
              datetime_value: null,
            },
            {
              subquestion_id: '540',
              subquestion_name: '14、巨大儿（本人）？',
              show: true,
              inline_check: true,
              checked: false,
              datetime_key: null,
              datetime_value: null,
            },
            {
              subquestion_id: '546',
              subquestion_name: '15、您是通过以下哪种喂养方式长大的？（本人）',
              show: true,
              inline_check: null,
              checked: null,
              datetime_key: null,
              datetime_value: null,
              children: [
                {
                  option_id: '51285',
                  option_name: '母乳喂养',
                  checked: false,
                },
                {
                  option_id: '51286',
                  option_name: '人工喂养',
                  checked: false,
                },
                {
                  option_id: '51287',
                  option_name: '混合喂养',
                  checked: false,
                },
                {
                  option_id: '51288',
                  option_name: '不清楚',
                  checked: false,
                },
              ],
            },
          ],
        },
      ],
    };
  },
  mixins: [mixin],
  created() {
    this.getMenstrualObstetricalHistoryData();
  },
  methods: {
    async getMenstrualObstetricalHistoryData() {
      const {
        user_id,
        visit_level,
        sex,
        hosp_id,
      } = this.$route.query;

      this.show_loading = true;

      const res = await api.qssInfo({
        sex,
        user_id,
        hosp_id,
        visit_level,
        template_id: 24,
      }).finally(() => {
        this.show_loading = false;
      });

      if (res.status === 0) {
        const list = res.data.question_data || [];

        this.setQuestionList(list);
      }
    },
    // 设置题目
    setQuestionList(list) {
      if (list.length > 0) {
        list.forEach(item => {
          // 第 1 题
          if (item.subquestion_id === '533') {
            this.setTitleInput(item, 0, 0, 1);
          }
          // 第 2 题
          if (item.subquestion_id === '534') {
            const questions = ['51261', '51262'];
            this.setQuestionCheckedStatus(item, questions, 0, 1);
          }
          if (item.subquestion_id === '51261001') {
            this.setMultiInputValue(item, 0, 1, 0);
          }
          if (item.subquestion_id === '51262001') {
            this.setMultiInputValue(item, 0, 1, 1);
          }
          // 第 3 题
          if (item.subquestion_id === '949') {
            const questions = ['52798', '52799'];
            this.setQuestionCheckedStatus(item, questions, 0, 2);
          }
          if (item.subquestion_id === '52799001') {
            this.setMultiInputValue(item, 0, 2, 1);
          }
          // 第 4 题
          if (item.subquestion_id === '535') {
            this.setInlineCheckbox(item, 0, 3);
          }
          // 第 5 题
          if (item.subquestion_id === '536') {
            this.setInlineCheckbox(item, 0, 4);
          }
          // 第 6 题
          if (item.subquestion_id === '537') {
            this.setInlineCheckbox(item, 0, 5);
          }
          // 第 7 题
          if (item.subquestion_id === '538001') {
            this.setTitleInput(item, 0, 6, 1);
          }
          if (item.subquestion_id === '538002') {
            this.setTitleInput(item, 0, 6, 3);
          }
          if (item.subquestion_id === '538003') {
            this.setTitleInput(item, 0, 6, 5);
          }
          if (item.subquestion_id === '538004') {
            this.setTitleInput(item, 0, 6, 7);
          }
          // 第 8 题
          if (item.subquestion_id === '539') {
            const questions = ['51269', '51270', '539001'];
            this.setQuestionCheckedStatus(item, questions, 0, 7);
          }
          // 第 9 题
          if (item.subquestion_id === '541') {
            const questions = ['51273', '51274', '541001'];
            this.setQuestionCheckedStatus(item, questions, 0, 8);
          }
          // 第 10 题
          if (item.subquestion_id === '542') {
            const questions = ['51275', '51276', '542001'];
            this.setQuestionCheckedStatus(item, questions, 0, 9);
          }
          // 第 11 题
          if (item.subquestion_id === '543') {
            const questions = ['51277', '51278', '51279', '543001'];
            this.setQuestionCheckedStatus(item, questions, 0, 10, 'all');
          }
          // 第 12 题
          if (item.subquestion_id === '544') {
            const questions = ['51280', '51281', '51282'];
            this.setQuestionCheckedStatus(item, questions, 0, 11);
          }
          // 第 13 题
          if (item.subquestion_id === '545') {
            this.setInlineCheckbox(item, 0, 12);
          }
          // 第 14 题
          if (item.subquestion_id === '540') {
            this.setInlineCheckbox(item, 0, 13);
          }
          // 第 15 题
          if (item.subquestion_id === '546') {
            const questions = ['51285', '51286', '51287', '51288'];
            this.setQuestionCheckedStatus(item, questions, 0, 14);
          }
        })
      }
    },
    // 设置标题填空题
    setTitleInput(item, i, j, k) {
      const answer = this.checkAnswerValue(item);
      if (answer) {
        this.question_list[i].children[j].subquestion_name_format[k].value = answer.answer_value;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.menstrual-obstetrical-history {
  padding: 0 15px;
}
</style>
