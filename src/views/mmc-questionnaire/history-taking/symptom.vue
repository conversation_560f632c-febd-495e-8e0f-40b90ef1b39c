<template>
  <div class="symptom">
    <div class="container">
      <ul class="question">
        <li v-for="question in question_list" :key="question.id">
          <div>
            <p class="h2" v-text="question.title"></p>
            <ul :class="setQuestionItemClass(question)">
              <li v-for="item in question.children" :key="item.option_id" :class="setItemClass(item)">
                <span class="icon"></span>
                <span class="text option" v-text="item.option_name"></span>
              </li>
            </ul>
          </div>
        </li>
      </ul>
    </div>

    <van-overlay :show="show_loading" class-name="mmc-questionnaire-loading-overlay">
      <van-loading type="spinner" />
    </van-overlay>
  </div>
</template>

<script>
import api from '@/api/mmc-questionnaire.js';

export default {
  data() {
    return {
      show_loading: false,
      count: 7,
      question_list: [
        {
          id: 1,
          type: 'checkbox',
          composing: 'block',
          title: '1、您近三个月有如下情况吗？',
          questions: ['443', '444', '445', '977', '456', '461', '460'],
          children: [
            {
              option_id: '443',
              option_name: '多饮',
              checked: false,
            },
            {
              option_id: '444',
              option_name: '多食',
              checked: false,
            },
            {
              option_id: '445',
              option_name: '多尿',
              checked: false,
            },
            {
              option_id: '977',
              option_name: '感染',
              checked: false,
            },
            {
              option_id: '456',
              option_name: '低血糖',
              checked: false,
            },
            {
              option_id: '461',
              option_name: '手脚麻木',
              checked: false,
            },
            {
              option_id: '460',
              option_name: '肢体疼痛或感觉异常',
              checked: false,
            },
          ]
        },
        {
          id: 2,
          type: 'checkbox',
          composing: 'inline',
          title: '2、您近三个月是否有视力模糊？',
          questions: ['976'],
          children: [
            {
              option_id: '97601',
              option_name: '是',
              checked: false,
            },
            {
              option_id: '97602',
              option_name: '否',
              checked: false,
            },
            {
              option_id: '97603',
              option_name: '失明',
              checked: false,
            },
          ]
        },
        {
          id: 3,
          type: 'checkbox',
          composing: 'inline',
          title: '3、您近三个月是否有下肢水肿？',
          questions: ['473'],
          children: [
            {
              option_id: '51020',
              option_name: '是',
              checked: false,
            },
            {
              option_id: '51021',
              option_name: '否',
              checked: false,
            },
            {
              option_id: '51022',
              option_name: '不详',
              checked: false,
            },
          ]
        },
        {
          id: 4,
          type: 'checkbox',
          composing: 'inline',
          title: '4、您近三个月是否有间歇性跛行？',
          questions: ['463'],
          children: [
            {
              option_id: '50990',
              option_name: '是',
              checked: false,
            },
            {
              option_id: '50991',
              option_name: '否',
              checked: false,
            },
            {
              option_id: '50992',
              option_name: '不详',
              checked: false,
            },
          ]
        },
        {
          id: 5,
          type: 'checkbox',
          composing: 'block',
          title: '5、您近一年是否有体重明显下降？',
          questions: ['447'],
          children: [
            {
              option_id: '50940',
              option_name: '近一年体重无下降',
              checked: false,
            },
            {
              option_id: '50941',
              option_name: '近一年体重下降<5斤',
              checked: false,
            },
            {
              option_id: '50942',
              option_name: '近一年体重下降5-10斤',
              checked: false,
            },
            {
              option_id: '50943',
              option_name: '近一年体重下降>10斤',
              checked: false,
            },
          ]
        },
      ]
    }
  },
  created() {
    this.getSymptomData();
  },
  methods: {
    // 获取症状数据
    async getSymptomData() {
      const {
        user_id,
        visit_level,
        sex,
        hosp_id,
      } = this.$route.query;

      this.show_loading = true;

      const res = await api.qssInfo({
        sex,
        user_id,
        hosp_id,
        visit_level,
        template_id: 20,
      }).finally(() => {
        this.show_loading = false;
      });

      if (res.status === 0) {
        const list = res.data.question_data || [];

        this.setQuestionList(list);
      }

    },
    // 设置题目
    setQuestionList(list) {
      if (list.length > 0) {
        list.forEach(item => {
          // 设置第 1 题
          const first = ['443', '444', '445', '977', '456', '461', '460'];
          for (let i = 0; i < first.length; i++) {
            if (item.subquestion_id === first[i]) {
              if (item.answer && item.answer[0]) {
                const answer = item.answer[0].answer_value === '是';
                this.question_list[0].children[i].checked = answer;

                if (!answer) {
                  this.count--;
                }
              }
            }
          }
          if (this.count === 0) {
            this.question_list[0].children.unshift({
              option_id: null,
              option_name: '以下皆无',
              checked: true,
            });
          }
  
          // 设置第 2-4 题
          const others = ['976', '473', '463', '447'];
          others.forEach((question, index) => {
            if (item.subquestion_id === question) {
              if (item.answer && item.answer[0]) {
                const answer = item.answer[0].answer_id;
                this.question_list[index + 1].children.forEach(item => {
                  if (item.option_id === answer) {
                    item.checked = true;
                  }
                })
              }
            }
          })
        })
      }
    },
    setQuestionItemClass(question) {
      return `question-item ${question.composing}`
    },
    setItemClass(item) {
      return `item ${item.checked && 'checked'}`
    },
  }
}
</script>

<style scoped lang="scss">
.symptom {
  @import "../common/styles/common.scss";
}
.container {
  padding: 0 15px;
}
</style>