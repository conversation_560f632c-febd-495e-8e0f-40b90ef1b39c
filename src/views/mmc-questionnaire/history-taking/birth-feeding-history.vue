<template>
  <div class="birth-feeding-history">
    <div class="container">
      <div>
        <comp-questionnaire-list :list="question_list" />
      </div>
    </div>

    <van-overlay :show="show_loading" class-name="mmc-questionnaire-loading-overlay">
      <van-loading type="spinner" />
    </van-overlay>
  </div>
</template>

<script>
import api from '@/api/mmc-questionnaire.js';
import compQuestionnaireList from '../components/comp-questionnaire-list.vue';
import mixin from '../common/mixins/mixin';

export default {
  name: 'menstrualObstetricalHistory',
  components: {
    compQuestionnaireList,
  },
  data() {
    return {
      show_loading: false,
      question_list: [
        {
          id: 1,
          composing: 'block',
          title: null,
          children: [
            {
              subquestion_id: '544',
              subquestion_name: '1、您出生时是？（本人）',
              show: true,
              inline_check: null,
              checked: null,
              datetime_key: null,
              datetime_value: null,
              children: [
                {
                  option_id: '51280',
                  option_name: '顺产',
                  checked: false,
                },
                {
                  option_id: '51281',
                  option_name: '剖腹产',
                  checked: false,
                },
                {
                  option_id: '51282',
                  option_name: '不清楚',
                  checked: false,
                },
              ],
            },
            {
              subquestion_id: '545',
              subquestion_name: '2、您是早产儿吗？（本人）',
              show: true,
              inline_check: true,
              checked: false,
              datetime_key: null,
              datetime_value: null,
            },
            {
              subquestion_id: '540',
              subquestion_name: '3、巨大儿（本人）？',
              show: true,
              inline_check: true,
              checked: false,
              datetime_key: null,
              datetime_value: null,
            },
            {
              subquestion_id: '546',
              subquestion_name: '4、您是通过以下哪种喂养方式长大的？（本人）',
              show: true,
              inline_check: null,
              checked: null,
              datetime_key: null,
              datetime_value: null,
              children: [
                {
                  option_id: '51285',
                  option_name: '母乳喂养',
                  checked: false,
                },
                {
                  option_id: '51286',
                  option_name: '人工喂养',
                  checked: false,
                },
                {
                  option_id: '51287',
                  option_name: '混合喂养',
                  checked: false,
                },
                {
                  option_id: '51288',
                  option_name: '不清楚',
                  checked: false,
                },
              ],
            },
          ],
        },
      ],
    };
  },
  mixins: [mixin],
  created() {
    this.getMenstrualObstetricalHistoryData();
  },
  methods: {
    async getMenstrualObstetricalHistoryData() {
      const {
        user_id,
        visit_level,
        sex,
        hosp_id,
      } = this.$route.query;

      this.show_loading = true;

      const res = await api.qssInfo({
        sex,
        user_id,
        hosp_id,
        visit_level,
        template_id: 24,
      }).finally(() => {
        this.show_loading = false;
      });

      if (res.status === 0) {
        const list = res.data.question_data || [];
        this.setQuestionList(list);
      }
    },
    // 设置题目
    setQuestionList(list) {
      if (list.length > 0) {
        list.forEach(item => {
       
          // 第 1 题
          if (item.subquestion_id === '544') {
            const questions = ['51280', '51281', '51282'];
            this.setQuestionCheckedStatus(item, questions, 0, 0);
          }
          // 第 2 题
          if (item.subquestion_id === '545') {
            this.setInlineCheckbox(item, 0, 1);
          }
          // 第 3 题
          if (item.subquestion_id === '540') {
            this.setInlineCheckbox(item, 0, 2);
          }
          // 第 4 题
          if (item.subquestion_id === '546') {
            const questions = ['51285', '51286', '51287', '51288'];
            this.setQuestionCheckedStatus(item, questions, 0, 3);
          }
        })
      }
    },
    // 设置标题填空题
    setTitleInput(item, i, j, k) {
      const answer = this.checkAnswerValue(item);
      if (answer) {
        this.question_list[i].children[j].subquestion_name_format[k].value = answer.answer_value;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.birth-feeding-history {
  padding: 0 15px;
}
</style>
