<template>
  <div class="family-history">
    <van-collapse v-model="activeNames">
      <van-collapse-item title="肿瘤和糖尿病" name="1">
        <comp-questionnaire-list :list="question_list.slice(0, 1)" />
      </van-collapse-item>
      <van-collapse-item title="心血管疾病" name="2">
        <comp-questionnaire-list :list="question_list.slice(1, 4)" />
      </van-collapse-item>
      <van-collapse-item title="肥胖和高脂血症" name="3">
        <comp-questionnaire-list :list="question_list.slice(4)" />
      </van-collapse-item>
    </van-collapse>

    <van-overlay :show="show_loading" class-name="mmc-questionnaire-loading-overlay">
      <van-loading type="spinner" />
    </van-overlay>
  </div>
</template>

<script>
import api from '@/api/mmc-questionnaire.js';
import compQuestionnaireList from '../components/comp-questionnaire-list.vue';
import mixin from '../common/mixins/mixin';

export default {
  name: 'familyHistory',
  components: {
    compQuestionnaireList,
  },
  data() {
    return {
      show_loading: false,
      activeNames: ['1', '2', '3'],
      question_list: [
        {
          id: 1,
          composing: 'block',
          // title: null,
          children: [
            {
              subquestion_id: '517',
              subquestion_name: '1、在您的血缘亲属中（包括已过世的），是否有人患有（或患过）恶性肿瘤或糖尿病？',
              show: true,
              inline_check: true,
              checked: false,
              datetime_key: null,
              datetime_value: null,
            },
            {
              question_id: '518',
              subquestion_id: '',
              subquestion_name: '2、如果是，请完成下表（可多选）若其中患病者为儿子、女儿、兄弟、姐妹，请详细指出各选项的患病者个数，肿瘤部位1. 肝脏 2. 胰腺  3. 胃 4. 结直肠 5. 乳腺 6. 宫颈 7. 卵巢 8. 子宫内膜 9. 前列腺 10. 膀胱 11. 肾脏12. 淋巴瘤 13. 白血病 14. 肺部 15. 甲状腺 “如果为其他部位，请在“其他"列填写部位名称”）',
              show: true,
              table_name: 'family-history-cancer',
              inline_check: null,
              checked: null,
              datetime_key: null,
              datetime_value: null,
              data: {
                tbody: [
                  ['父亲', '', 'disabled', '', '', ''],
                  ['母亲', '', 'disabled', '', '', ''],
                  ['儿子', '', '', '', '', ''],
                  ['女儿', '', '', '', '', ''],
                  ['兄弟', '', '', '', '', ''],
                  ['姐妹', '', '', '', '', ''],
                  ['祖父', '', 'disabled', '', '', ''],
                  ['祖母', '', 'disabled', '', '', ''],
                  ['外祖父', '', 'disabled', '', '', ''],
                  ['外祖母', '', 'disabled', '', '', ''],
                ],
              },
            },
            {
              question_id: '518',
              subquestion_id: '',
              subquestion_name: '',
              show: true,
              table_name: 'family-history-diabetes',
              inline_check: null,
              checked: null,
              datetime_key: null,
              datetime_value: null,
              data: {
                tbody: [
                  ['父亲', '', 'disabled'],
                  ['母亲', '', 'disabled'],
                  ['儿子', '', ''],
                  ['女儿', '', ''],
                  ['兄弟', '', ''],
                  ['姐妹', '', ''],
                  ['祖父', '', 'disabled'],
                  ['祖母', '', 'disabled'],
                  ['外祖父', '', 'disabled'],
                  ['外祖母', '', 'disabled'],
                ],
              },
            },
          ]
        },
        {
          id: 2,
          composing: 'block',
          // title: null,
          children: [
            {
              subquestion_id: '519',
              subquestion_name: '1、在您的血缘亲属中（包括已过世的），是否有人患有（或患过）冠心病？',
              show: true,
              inline_check: true,
              checked: false,
              datetime_key: null,
              datetime_value: null,
            },
            {
              subquestion_id: '520',
              subquestion_name: '2、请问该亲属是您的?（可多选）',
              show: true,
              inline_check: null,
              checked: null,
              datetime: null,
              children: [
                {
                  option_id: '51201',
                  option_name: '父亲',
                  checked: false,
                },
                {
                  option_id: '51202',
                  option_name: '母亲',
                  checked: false,
                },
                {
                  option_id: '51203',
                  option_name: '儿子',
                  checked: false,
                },
                {
                  option_id: '51204',
                  option_name: '女儿',
                  checked: false,
                },
                {
                  option_id: '51205',
                  option_name: '亲兄弟',
                  checked: false,
                },
                {
                  option_id: '51206',
                  option_name: '亲姐妹',
                  checked: false,
                },
                {
                  option_id: '51207',
                  option_name: '祖父',
                  checked: false,
                },
                {
                  option_id: '51208',
                  option_name: '祖母',
                  checked: false,
                },
                {
                  option_id: '51209',
                  option_name: '外祖父',
                  checked: false,
                },
                {
                  option_id: '51210',
                  option_name: '外祖母',
                  checked: false,
                },
              ],
            },
          ]
        },
        {
          id: 3,
          composing: 'block',
          // title: null,
          children: [
            {
              subquestion_id: '521',
              subquestion_name: '3、在您的血缘亲属中（包括已过世的），是否有人患有（或患过）脑卒中（脑梗塞或脑出血）',
              show: true,
              inline_check: true,
              checked: false,
              datetime_key: null,
              datetime_value: null,
            },
            {
              subquestion_id: '522',
              subquestion_name: '4、请问该亲属是您的?（可多选）',
              show: true,
              inline_check: null,
              checked: null,
              datetime: null,
              children: [
                {
                  option_id: '51213',
                  option_name: '父亲',
                  checked: false,
                },
                {
                  option_id: '51214',
                  option_name: '母亲',
                  checked: false,
                },
                {
                  option_id: '51215',
                  option_name: '儿子',
                  checked: false,
                },
                {
                  option_id: '51216',
                  option_name: '女儿',
                  checked: false,
                },
                {
                  option_id: '51217',
                  option_name: '亲兄弟',
                  checked: false,
                },
                {
                  option_id: '51218',
                  option_name: '亲姐妹',
                  checked: false,
                },
                {
                  option_id: '51219',
                  option_name: '祖父',
                  checked: false,
                },
                {
                  option_id: '51220',
                  option_name: '祖母',
                  checked: false,
                },
                {
                  option_id: '51221',
                  option_name: '外祖父',
                  checked: false,
                },
                {
                  option_id: '51222',
                  option_name: '外祖母',
                  checked: false,
                },
              ],
            },
          ]
        },
        {
          id: 4,
          composing: 'block',
          // title: null,
          children: [
            {
              subquestion_id: '523',
              subquestion_name: '5、在您的血缘亲属中（包括已过世的），是否有人患有（或患过）高血压？',
              show: true,
              inline_check: true,
              checked: false,
              datetime_key: null,
              datetime_value: null,
            },
            {
              subquestion_id: '524',
              subquestion_name: '6、请问该亲属是您的?（可多选）',
              show: true,
              inline_check: null,
              checked: null,
              datetime: null,
              children: [
                {
                  option_id: '51225',
                  option_name: '父亲',
                  checked: false,
                },
                {
                  option_id: '51226',
                  option_name: '母亲',
                  checked: false,
                },
                {
                  option_id: '51227',
                  option_name: '儿子',
                  checked: false,
                },
                {
                  option_id: '51228',
                  option_name: '女儿',
                  checked: false,
                },
                {
                  option_id: '51229',
                  option_name: '亲兄弟',
                  checked: false,
                },
                {
                  option_id: '51230',
                  option_name: '亲姐妹',
                  checked: false,
                },
                {
                  option_id: '51231',
                  option_name: '祖父',
                  checked: false,
                },
                {
                  option_id: '51232',
                  option_name: '祖母',
                  checked: false,
                },
                {
                  option_id: '51233',
                  option_name: '外祖父',
                  checked: false,
                },
                {
                  option_id: '51234',
                  option_name: '外祖母',
                  checked: false,
                },
              ],
            },
          ]
        },
        {
          id: 5,
          composing: 'block',
          // title: null,
          children: [
            {
              subquestion_id: '525',
              subquestion_name: '1、在您的血缘亲属中（包括已过世的），是否有人患有（或患过）肥胖症？',
              show: true,
              inline_check: true,
              checked: false,
              datetime_key: null,
              datetime_value: null,
            },
            {
              subquestion_id: '526',
              subquestion_name: '2、请问该亲属是您的?（可多选）',
              show: true,
              inline_check: null,
              checked: null,
              datetime: null,
              children: [
                {
                  option_id: '51237',
                  option_name: '父亲',
                  checked: false,
                },
                {
                  option_id: '51238',
                  option_name: '母亲',
                  checked: false,
                },
                {
                  option_id: '51239',
                  option_name: '儿子',
                  checked: false,
                },
                {
                  option_id: '51240',
                  option_name: '女儿',
                  checked: false,
                },
                {
                  option_id: '51241',
                  option_name: '亲兄弟',
                  checked: false,
                },
                {
                  option_id: '51242',
                  option_name: '亲姐妹',
                  checked: false,
                },
                {
                  option_id: '51243',
                  option_name: '祖父',
                  checked: false,
                },
                {
                  option_id: '51244',
                  option_name: '祖母',
                  checked: false,
                },
                {
                  option_id: '51245',
                  option_name: '外祖父',
                  checked: false,
                },
                {
                  option_id: '51246',
                  option_name: '外祖母',
                  checked: false,
                },
              ],
            },
          ]
        },
        {
          id: 6,
          composing: 'block',
          // title: null,
          children: [
            {
              subquestion_id: '527',
              subquestion_name: '3、在您的血缘亲属中（包括已过世的），是否有人患有（或患过）高脂血症？',
              show: true,
              inline_check: true,
              checked: false,
              datetime_key: null,
              datetime_value: null,
            },
            {
              subquestion_id: '528',
              subquestion_name: '4、请问该亲属是您的?（可多选）',
              show: true,
              inline_check: null,
              checked: null,
              datetime: null,
              children: [
                {
                  option_id: '51249',
                  option_name: '父亲',
                  checked: false,
                },
                {
                  option_id: '51250',
                  option_name: '母亲',
                  checked: false,
                },
                {
                  option_id: '51251',
                  option_name: '儿子',
                  checked: false,
                },
                {
                  option_id: '51252',
                  option_name: '女儿',
                  checked: false,
                },
                {
                  option_id: '51253',
                  option_name: '亲兄弟',
                  checked: false,
                },
                {
                  option_id: '51254',
                  option_name: '亲姐妹',
                  checked: false,
                },
                {
                  option_id: '51255',
                  option_name: '祖父',
                  checked: false,
                },
                {
                  option_id: '51256',
                  option_name: '祖母',
                  checked: false,
                },
                {
                  option_id: '51257',
                  option_name: '外祖父',
                  checked: false,
                },
                {
                  option_id: '51258',
                  option_name: '外祖母',
                  checked: false,
                },
              ],
            },
          ]
        },
      ],
    }
  },
  mixins: [mixin],
  created() {
    this.getFamilyHistoryData();
  },
  methods: {
    // 获取家族史数据
    async getFamilyHistoryData() {
      const {
        user_id,
        visit_level,
        sex,
        hosp_id,
      } = this.$route.query;

      this.show_loading = true;

      const res = await api.qssInfo({
        sex,
        user_id,
        hosp_id,
        visit_level,
        template_id: 22,
      }).finally(() => {
        this.show_loading = false;
      });

      if (res.status === 0) {
        const list = res.data.question_data || [];

        this.setQuestionList(list);
      }

    },
    // 设置题目
    setQuestionList(list) {
      if (list.length > 0) {
        list.forEach(item => {
          // 肿瘤和糖尿病
          // 第 1 题
          if (item.subquestion_id === '517') {
            this.setInlineCheckbox(item, 0, 0);
          }
          // 第 2 题
          if (item.question_id === '518') {
            this.setFamilyHistoryCancerTableData(item, 0, 1);
          }

          // 心血管疾病
          // 第 1 题
          if (item.subquestion_id === '519') {
            this.setInlineCheckbox(item, 1, 0);
          }
          // 第 2 题
          if (item.subquestion_id === '520') {
            const questions = ['51201', '51202', '51203', '51204', '51205', '51206', '51207', '51208', '51209', '51210'];
            this.setQuestionCheckedStatus(item, questions, 1, 1, 'all');
          }
          // 第 3 题
          if (item.subquestion_id === '521') {
            this.setInlineCheckbox(item, 2, 0);
          }
          // 第 4 题
          if (item.subquestion_id === '522') {
            const questions = ['51213', '51214', '51215', '51216', '51217', '51218', '51219', '51220', '51221', '51222'];
            this.setQuestionCheckedStatus(item, questions, 2, 1, 'all');
          }
          // 第 5 题
          if (item.subquestion_id === '523') {
            this.setInlineCheckbox(item, 3, 0);
          }
          // 第 6 题
          if (item.subquestion_id === '524') {
            const questions = ['51225', '51226', '51227', '51228', '51229', '51230', '51231', '51232', '51233', '51234'];
            this.setQuestionCheckedStatus(item, questions, 3, 1, 'all');
          }
          
          // 肥胖和高脂血症
          // 第 1 题
          if (item.subquestion_id === '525') {
            this.setInlineCheckbox(item, 4, 0);
          }
          // 第 2 题
          if (item.subquestion_id === '526') {
            const questions = ['51237', '51238', '51239', '51240', '51241', '51242', '51243', '51244', '51245', '51246'];
            this.setQuestionCheckedStatus(item, questions, 4, 1, 'all');
          }
          // 第 3 题
          if (item.subquestion_id === '527') {
            this.setInlineCheckbox(item, 5, 0);
          }
          // 第 4 题
          if (item.subquestion_id === '528') {
            const questions = ['51249', '51250', '51251', '51252', '51253', '51254', '51255', '51256', '51257', '51258'];
            this.setQuestionCheckedStatus(item, questions, 5, 1, 'all');
          }

        })
      }
    },
    // 家族史肿瘤
    setFamilyHistoryCancerTableData(item, i, j) {
      const answer = this.checkAnswerValue(item);
      const value = answer ? this.transformDiseaseValue(answer.answer_value, item.subquestion_id) : '';

      switch (item.subquestion_id) {
        case '518001':
          this.$set(this.question_list[i].children[1].data.tbody[0], 1, value);
          this.$set(this.question_list[i].children[1].data.tbody[0], 2, 'disabled');
          break;
        case '518002':
          this.$set(this.question_list[i].children[1].data.tbody[0], 3, value);
          break;
        case '518003':
          this.$set(this.question_list[i].children[1].data.tbody[0], 4, value);
          break;
        case '518004':
          this.$set(this.question_list[i].children[1].data.tbody[0], 5, value);
          break;

        case '518005':
          this.$set(this.question_list[i].children[2].data.tbody[0], 1, value);
          this.$set(this.question_list[i].children[2].data.tbody[0], 2, 'disabled');
          break;

        case '518006':
          this.$set(this.question_list[i].children[1].data.tbody[1], 1, value);
          this.$set(this.question_list[i].children[1].data.tbody[1], 2, 'disabled');
          break;
        case '518007':
          this.$set(this.question_list[i].children[1].data.tbody[1], 3, value);
          break;
        case '518008':
          this.$set(this.question_list[i].children[1].data.tbody[1], 4, value);
          break;
        case '518009':
          this.$set(this.question_list[i].children[1].data.tbody[1], 5, value);
          break;

        case '518010':
          this.$set(this.question_list[i].children[2].data.tbody[1], 1, value);
          this.$set(this.question_list[i].children[2].data.tbody[1], 2, 'disabled');
          break;

        case '518011':
          this.$set(this.question_list[i].children[1].data.tbody[2], 1, value);
          break;
        case '518012':
          this.$set(this.question_list[i].children[1].data.tbody[2], 2, value);
          break;
        case '518013':
          this.$set(this.question_list[i].children[1].data.tbody[2], 3, value);
          break;
        case '518014':
          this.$set(this.question_list[i].children[1].data.tbody[2], 4, value);
          break;
        case '518015':
          this.$set(this.question_list[i].children[1].data.tbody[2], 5, value);
          break;

        case '518016':
          this.$set(this.question_list[i].children[2].data.tbody[2], 1, value);
          break;
        case '518017':
          this.$set(this.question_list[i].children[2].data.tbody[2], 2, value);
          break;

        case '518018':
          this.$set(this.question_list[i].children[1].data.tbody[3], 1, value);
          break;
        case '518019':
          this.$set(this.question_list[i].children[1].data.tbody[3], 2, value);
          break;
        case '518020':
          this.$set(this.question_list[i].children[1].data.tbody[3], 3, value);
          break;
        case '518021':
          this.$set(this.question_list[i].children[1].data.tbody[3], 4, value);
          break;
        case '518022':
          this.$set(this.question_list[i].children[1].data.tbody[3], 5, value);
          break;

        case '518023':
          this.$set(this.question_list[i].children[2].data.tbody[3], 1, value);
          break;
        case '518024':
          this.$set(this.question_list[i].children[2].data.tbody[3], 2, value);
          break;

        case '518025':
          this.$set(this.question_list[i].children[1].data.tbody[4], 1, value);
          break;
        case '518026':
          this.$set(this.question_list[i].children[1].data.tbody[4], 2, value);
          break;
        case '518027':
          this.$set(this.question_list[i].children[1].data.tbody[4], 3, value);
          break;
        case '518028':
          this.$set(this.question_list[i].children[1].data.tbody[4], 4, value);
          break;
        case '518029':
          this.$set(this.question_list[i].children[1].data.tbody[4], 5, value);
          break;

        case '518030':
          this.$set(this.question_list[i].children[2].data.tbody[4], 1, value);
          break;
        case '518031':
          this.$set(this.question_list[i].children[2].data.tbody[4], 2, value);
          break;

        case '518032':
          this.$set(this.question_list[i].children[1].data.tbody[5], 1, value);
          break;
        case '518033':
          this.$set(this.question_list[i].children[1].data.tbody[5], 2, value);
          break;
        case '518034':
          this.$set(this.question_list[i].children[1].data.tbody[5], 3, value);
          break;
        case '518035':
          this.$set(this.question_list[i].children[1].data.tbody[5], 4, value);
          break;
        case '518036':
          this.$set(this.question_list[i].children[1].data.tbody[5], 5, value);
          break;

        case '518037':
          this.$set(this.question_list[i].children[2].data.tbody[5], 1, value);
          break;
        case '518038':
          this.$set(this.question_list[i].children[2].data.tbody[5], 2, value);
          break;

        case '518039':
          this.$set(this.question_list[i].children[1].data.tbody[6], 1, value);
          this.$set(this.question_list[i].children[1].data.tbody[6], 2, 'disabled');
          break;
        case '518040':
          this.$set(this.question_list[i].children[1].data.tbody[6], 3, value);
          break;
        case '518041':
          this.$set(this.question_list[i].children[1].data.tbody[6], 4, value);
          break;
        case '518042':
          this.$set(this.question_list[i].children[1].data.tbody[6], 5, value);
          break;

        case '518043':
          this.$set(this.question_list[i].children[2].data.tbody[6], 1, value);
          this.$set(this.question_list[i].children[2].data.tbody[6], 2, 'disabled');
          break;

        case '518044':
          this.$set(this.question_list[i].children[1].data.tbody[7], 1, value);
          this.$set(this.question_list[i].children[1].data.tbody[7], 2, 'disabled');
          break;
        case '518045':
          this.$set(this.question_list[i].children[1].data.tbody[7], 3, value);
          break;
        case '518046':
          this.$set(this.question_list[i].children[1].data.tbody[7], 4, value);
          break;
        case '518047':
          this.$set(this.question_list[i].children[1].data.tbody[7], 5, value);
          break;

        case '518048':
          this.$set(this.question_list[i].children[2].data.tbody[7], 1, value);
          this.$set(this.question_list[i].children[2].data.tbody[7], 2, 'disabled');
          break;

        case '518049':
          this.$set(this.question_list[i].children[1].data.tbody[8], 1, value);
          this.$set(this.question_list[i].children[1].data.tbody[8], 2, 'disabled');
          break;
        case '518050':
          this.$set(this.question_list[i].children[1].data.tbody[8], 3, value);
          break;
        case '518051':
          this.$set(this.question_list[i].children[1].data.tbody[8], 4, value);
          break;
        case '518052':
          this.$set(this.question_list[i].children[1].data.tbody[8], 5, value);
          break;

        case '518053':
          this.$set(this.question_list[i].children[2].data.tbody[8], 1, value);
          this.$set(this.question_list[i].children[2].data.tbody[8], 2, 'disabled');
          break;

        case '518054':
          this.$set(this.question_list[i].children[1].data.tbody[9], 1, value);
          this.$set(this.question_list[i].children[1].data.tbody[9], 2, 'disabled');
          break;
        case '518055':
          this.$set(this.question_list[i].children[1].data.tbody[9], 3, value);
          break;
        case '518056':
          this.$set(this.question_list[i].children[1].data.tbody[9], 4, value);
          break;
        case '518057':
          this.$set(this.question_list[i].children[1].data.tbody[9], 5, value);
          break;

        case '518058':
          this.$set(this.question_list[i].children[2].data.tbody[9], 1, value);
          this.$set(this.question_list[i].children[2].data.tbody[9], 2, 'disabled');
          break;
        
        default:
          break;
      }
    },
    // 转换名称
    transformDiseaseValue(value, subquestion_id) {
      const list = ['518002', '518003', '518007', '518008', '518013', '518014', '518020', '518021', '518027', '518028', '518034', '518035', '518040', '518041', '518045', '518046', '518050', '518051', '518055', '518056'];
      if (!list.includes(subquestion_id)) return value;

      switch (value) {
        case '1':
          return '肝脏';
        case '2':
          return '胰腺';
        case '3':
          return '胃';
        case '4':
          return '结直肠';
        case '5':
          return '乳腺';
        case '6':
          return '宫颈';
        case '7':
          return '卵巢';
        case '8':
          return '子宫内膜';
        case '9':
          return '前列腺';
        case '10':
          return '膀胱';
        case '11':
          return '肾脏';
        case '12':
          return '淋巴瘤';
        case '13':
          return '白血病';
        case '14':
          return '肺部';
        case '15':
          return '甲状腺';

        default:
          break;
      }
    },
  }
}
</script>

<style scoped lang="scss">
// .family-history {}
</style>
