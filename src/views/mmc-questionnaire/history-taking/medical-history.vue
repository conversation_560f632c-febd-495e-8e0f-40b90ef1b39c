<template>
  <div class="medical-history">
    <van-collapse v-model="activeNames">
      <van-collapse-item title="疾病史" name="1">
        <comp-questionnaire-list :list="question_list.slice(0, 17)" />
      </van-collapse-item>
      <van-collapse-item title="外伤史/手术史" name="2">
        <comp-questionnaire-list :list="question_list.slice(17)" />
      </van-collapse-item>
    </van-collapse>

    <van-overlay :show="show_loading" class-name="mmc-questionnaire-loading-overlay">
      <van-loading type="spinner" />
    </van-overlay>
  </div>
</template>

<script>
import api from '@/api/mmc-questionnaire.js';
import compQuestionnaireList from '../components/comp-questionnaire-list.vue';
import mixin from '../common/mixins/mixin';

export default {
  name: 'medicalHistory',
  components: {
    compQuestionnaireList,
  },
  data() {
    return {
      show_loading: false,
      activeNames: ['1', '2'],
      question_list: [
        {
          id: 1,
          composing: 'block',
          title: 'A) 糖尿病',
          children: [
            {
              subquestion_id: '947',
              subquestion_name: '1、您是否有明确诊断的糖尿病？',
              show: true,
              inline_check: true,
              checked: false,
              datetime_key: '首次诊断糖尿病的时间：',
              datetime_value: null,
            },
            {
              subquestion_id: '969',
              subquestion_name: '2、糖尿病类型？',
              show: true,
              inline_check: null,
              checked: null,
              datetime: null,
              children: [
                {
                  option_id: '52838',
                  option_name: '1型糖尿病',
                  checked: false,
                },
                {
                  option_id: '52839',
                  option_name: '2型糖尿病',
                  checked: false,
                },
                {
                  option_id: '52840',
                  option_name: '妊娠期糖尿病',
                  checked: false,
                },
                {
                  option_id: '52841',
                  option_name: '其他特殊类型糖尿病',
                  checked: false,
                },
              ],
            },
          ]
        },
        {
          id: 2,
          composing: 'block',
          title: 'B) 高血压',
          children: [
            {
              subquestion_id: '476',
              subquestion_name: '3、您是否有明确诊断的高血压?',
              show: true,
              inline_check: true,
              checked: false,
              datetime_key: '首次诊断高血压的时间：',
              datetime_value: null,
            },
            {
              subquestion_id: '477',
              subquestion_name: '4、您在本中心第一次就诊之前是否正在服用降压药？',
              show: true,
              inline_check: true,
              checked: false,
              datetime_key: null,
              datetime_value: null,
            }
          ]
        },
        {
          id: 3,
          composing: 'block',
          title: 'C) 高血脂',
          children: [
            {
              subquestion_id: '478',
              subquestion_name: '5、您现在或曾经是否患有高血脂?',
              show: true,
              inline_check: true,
              checked: false,
              datetime_key: '首次诊断高血脂的时间：',
              datetime_value: null,
            },
            {
              subquestion_id: '479',
              subquestion_name: '6、您在本中心第一次就诊之前是否正在服用降脂药？',
              show: true,
              inline_check: true,
              checked: false,
              datetime_key: null,
              datetime_value: null,
            }
          ]
        },
        {
          id: 4,
          composing: 'block',
          title: 'D) 高尿酸血症',
          children: [
            {
              subquestion_id: '480',
              subquestion_name: '7、您现在或曾经是否患有高尿酸?',
              show: true,
              inline_check: true,
              checked: false,
              datetime_key: '首次诊断高尿酸血症的时间：',
              datetime_value: null,
            },
            {
              subquestion_id: '481',
              subquestion_name: '8、您是否因尿酸升高，出现过痛风发作？',
              show: true,
              inline_check: true,
              checked: false,
              datetime_key: null,
              datetime_value: null,
            },
            {
              subquestion_id: '482',
              subquestion_name: '9、您在本中心第一次就诊之前是否正在服用降尿酸的药物？',
              show: true,
              inline_check: true,
              checked: false,
              datetime_key: null,
              datetime_value: null,
            },
          ]
        },
        {
          id: 5,
          composing: 'block',
          title: 'E) 冠心病',
          children: [
            {
              subquestion_id: '483',
              subquestion_name: '10、您是否患有冠心病？',
              show: true,
              inline_check: true,
              checked: false,
              datetime_key: '首次诊断冠心病的时间：',
              datetime_value: null,
            },
            {
              subquestion_id: '484',
              subquestion_name: '11、您的冠心病类型？',
              show: true,
              inline_check: null,
              checked: null,
              datetime_key: null,
              datetime_value: null,
              children: [
                {
                  option_id: '51045',
                  option_name: '心绞痛型',
                  checked: false,
                },
                {
                  option_id: '51046',
                  option_name: '心肌梗死型',
                  checked: false,
                },
                {
                  option_id: '51047',
                  option_name: '无症状性心肌缺血型',
                  checked: false,
                },
                {
                  option_id: '51048',
                  option_name: '心力衰竭和心律失常型',
                  checked: false,
                },
                {
                  option_id: '51049',
                  option_name: '猝死型',
                  checked: false,
                },
                {
                  option_id: '52849',
                  option_name: '不详',
                  checked: false,
                },
              ],
            },
            {
              subquestion_id: '485',
              subquestion_name: '12、您是否进行了冠状动脉造影？',
              show: true,
              inline_check: null,
              checked: null,
              datetime_key: null,
              datetime_value: null,
              children: [
                {
                  option_id: '51050',
                  option_name: '是',
                  checked: false,
                },
                {
                  option_id: '51051',
                  option_name: '否',
                  checked: false,
                },
                {
                  option_id: '51052',
                  option_name: '不清楚',
                  checked: false,
                },
              ],
            },
            {
              subquestion_id: '486',
              subquestion_name: '13、您是否进行以下治疗(可多选)?',
              show: true,
              inline_check: null,
              checked: null,
              datetime_key: null,
              datetime_value: null,
              children: [
                {
                  option_id: '51053',
                  option_name: '溶栓治疗',
                  checked: false,
                },
                {
                  option_id: '51054',
                  option_name: '介入治疗',
                  checked: false,
                },
                {
                  option_id: '51055',
                  option_name: '冠脉搭桥术',
                  checked: false,
                },
                {
                  option_id: '51056',
                  option_name: '药物治疗',
                  checked: false,
                },
                {
                  option_id: '51057',
                  option_name: ' 其它治疗',
                  checked: false,
                },
                {
                  option_id: '51058',
                  option_name: '否',
                  checked: false,
                },
              ],
            },
          ]
        },
        {
          id: 6,
          composing: 'block',
          title: 'F) 脑卒中（不包括腔隙性脑梗）',
          children: [
            {
              subquestion_id: '487',
              subquestion_name: '14、您是否患有脑卒中病史？',
              show: true,
              inline_check: true,
              checked: false,
              datetime_key: '首次发作时间：',
              datetime_value: null,
            },
            {
              subquestion_id: '488',
              subquestion_name: '15、您是否进行如下治疗(可多选)?',
              show: true,
              inline_check: null,
              checked: null,
              datetime_key: null,
              datetime_value: null,
              children: [
                {
                  option_id: '51061',
                  option_name: '外科手术',
                  checked: false,
                },
                {
                  option_id: '51062',
                  option_name: '脑血管成形术(介入治疗)',
                  checked: false,
                },
                {
                  option_id: '51063',
                  option_name: '溶栓',
                  checked: false,
                },
                {
                  option_id: '51064',
                  option_name: '药物治疗',
                  checked: false,
                },
                {
                  option_id: '51065',
                  option_name: '其它治疗',
                  checked: false,
                },
                {
                  option_id: '51066',
                  option_name: '否',
                  checked: false,
                },
              ],
            },
            {
              subquestion_id: '489',
              subquestion_name: '16、医生是否告知如下诊断？(可多选)',
              show: true,
              inline_check: null,
              checked: null,
              datetime_key: null,
              datetime_value: null,
              children: [
                {
                  option_id: '51067',
                  option_name: '蛛网膜下腔出血',
                  checked: false,
                },
                {
                  option_id: '51068',
                  option_name: '脑实质出血',
                  checked: false,
                },
                {
                  option_id: '51069',
                  option_name: '脑梗塞',
                  checked: false,
                },
                {
                  option_id: '51070',
                  option_name: '脑栓塞',
                  checked: false,
                },
                {
                  option_id: '51071',
                  option_name: ' 其它类型脑卒中',
                  checked: false,
                },
              ],
            },
          ]
        },
        {
          id: 7,
          composing: 'block',
          title: 'G) 充血性心力衰竭',
          children: [
            {
              subquestion_id: '490',
              subquestion_name: '17、您是否患有充血性心力衰竭？',
              show: true,
              inline_check: true,
              checked: false,
              datetime_key: '发作次数(输入数字)：',
              datetime_value: null,
            },
          ]
        },
        {
          id: 8,
          composing: 'block',
          title: 'H) 外周动脉疾病',
          children: [
            {
              subquestion_id: '491',
              subquestion_name: '18、您是否患有外周动脉疾病？',
              show: true,
              inline_check: true,
              checked: false,
              datetime_key: '首次诊断外周动脉疾病的时间：',
              datetime_value: null,
            },
          ]
        },
        {
          id: 9,
          composing: 'block',
          title: 'I) 肿瘤',
          children: [
            {
              subquestion_id: '492',
              subquestion_name: '19、您是否有过肿瘤病史？',
              show: true,
              inline_check: true,
              checked: false,
              datetime_key: '首次诊断肿瘤的时间：',
              datetime_value: null,
            },
            {
              subquestion_id: '493',
              subquestion_name: '20、该肿瘤是否为恶性？（如有记录，请拍照）',
              show: true,
              inline_check: null,
              checked: null,
              datetime_key: null,
              datetime_value: null,
              children: [
                {
                  option_id: '51078',
                  option_name: '是',
                  checked: false,
                },
                {
                  option_id: '51079',
                  option_name: '否',
                  checked: false,
                },
                {
                  option_id: '51080',
                  option_name: '不清楚',
                  checked: false,
                },
              ],
            },
            {
              subquestion_id: '494',
              subquestion_name: '21、恶性肿瘤原发部位（可多选）？',
              show: true,
              inline_check: null,
              checked: null,
              datetime_key: null,
              datetime_value: null,
              children: [
                {
                  option_id: '51081',
                  option_name: '肝脏',
                  checked: false,
                },
                {
                  option_id: '51082',
                  option_name: '胰腺',
                  checked: false,
                },
                {
                  option_id: '51083',
                  option_name: '胃',
                  checked: false,
                },
                {
                  option_id: '51084',
                  option_name: '结直肠',
                  checked: false,
                },
                {
                  option_id: '51085',
                  option_name: '乳腺',
                  checked: false,
                },
                {
                  option_id: '51086',
                  option_name: '宫颈',
                  checked: false,
                },
                {
                  option_id: '51087',
                  option_name: '卵巢',
                  checked: false,
                },
                {
                  option_id: '51088',
                  option_name: '子宫内膜',
                  checked: false,
                },
                {
                  option_id: '51089',
                  option_name: '前列腺',
                  checked: false,
                },
                {
                  option_id: '51090',
                  option_name: '膀胱',
                  checked: false,
                },
                {
                  option_id: '51091',
                  option_name: '肾脏',
                  checked: false,
                },
                {
                  option_id: '51092',
                  option_name: '淋巴瘤',
                  checked: false,
                },
                {
                  option_id: '51093',
                  option_name: '白血病',
                  checked: false,
                },
                {
                  option_id: '51094',
                  option_name: '肺部',
                  checked: false,
                },
                {
                  option_id: '51095',
                  option_name: '甲状腺',
                  checked: false,
                },
                {
                  option_id: '51096',
                  option_name: '其他',
                  checked: false,
                },
              ],
            },
            {
              subquestion_id: '495',
              subquestion_name: '22、您是否进行以下治疗，选择已做治疗（可多选，如有记录，请拍照）',
              show: true,
              inline_check: null,
              checked: null,
              datetime_key: null,
              datetime_value: null,
              children: [
                {
                  option_id: '51097',
                  option_name: '手术治疗',
                  checked: false,
                },
                {
                  option_id: '51098',
                  option_name: '放疗',
                  checked: false,
                },
                {
                  option_id: '51099',
                  option_name: '化疗',
                  checked: false,
                },
                {
                  option_id: '51100',
                  option_name: '介入治疗',
                  checked: false,
                },
                {
                  option_id: '51101',
                  option_name: '其他治疗',
                  checked: false,
                },
                {
                  option_id: '51102',
                  option_name: '否',
                  checked: false,
                },
                {
                  option_id: '51103',
                  option_name: '不清楚',
                  checked: false,
                },
              ],
            },
          ]
        },
        {
          id: 10,
          composing: 'block',
          title: 'J) 甲状腺疾病',
          children: [
            {
              subquestion_id: '496',
              subquestion_name: '23、您现在或曾经是否患有甲状腺疾病么？',
              show: true,
              inline_check: null,
              checked: null,
              datetime_key: null,
              datetime_value: null,
              children: [
                {
                  option_id: '51104',
                  option_name: '是',
                  unit: '首次诊断甲状腺疾病的时间：',
                  checked: false,
                },
                {
                  option_id: '51105',
                  option_name: '否',
                  checked: false,
                },
                {
                  option_id: '51105',
                  option_name: '不清楚',
                  checked: false,
                },
              ],
            },
            {
              subquestion_id: '497',
              subquestion_name: '24、请回答您患有相关甲状腺疾病的类型（可多选）？',
              show: true,
              inline_check: null,
              checked: null,
              datetime_key: null,
              datetime_value: null,
              children: [
                {
                  option_id: '51107',
                  option_name: '甲状腺功能亢进症',
                  checked: false,
                },
                {
                  option_id: '51108',
                  option_name: '甲状腺结节',
                  checked: false,
                },
                {
                  option_id: '51109',
                  option_name: '甲状腺功能减退症',
                  checked: false,
                },
                {
                  option_id: '51110',
                  option_name: '亚急性甲状腺炎',
                  checked: false,
                },
                {
                  option_id: '51111',
                  option_name: '桥本甲状腺炎',
                  checked: false,
                },
                {
                  option_id: '51112',
                  option_name: '甲状腺相关眼病',
                  checked: false,
                },
                {
                  option_id: '51113',
                  option_name: '其他',
                  checked: false,
                },
              ],
            },
            {
              subquestion_id: '498',
              subquestion_name: '25、如果您被诊断过“甲状腺结节”，请指出甲状腺结节的具体类型？',
              show: true,
              inline_check: null,
              checked: null,
              datetime_key: null,
              datetime_value: null,
              children: [
                {
                  option_id: '51114',
                  option_name: '甲状腺腺瘤',
                  checked: false,
                },
                {
                  option_id: '51115',
                  option_name: '甲状腺癌',
                  checked: false,
                },
                {
                  option_id: '51116',
                  option_name: '结节性甲状腺肿',
                  checked: false,
                },
                {
                  option_id: '51117',
                  option_name: '不清楚',
                  checked: false,
                },
              ],
            },
            {
              subquestion_id: '499',
              subquestion_name: '26、您是否对相关甲状腺疾病进行治疗(可多选)?',
              show: true,
              inline_check: null,
              checked: null,
              datetime_key: null,
              datetime_value: null,
              children: [
                {
                  option_id: '51118',
                  option_name: '优甲乐或甲状腺素片',
                  checked: false,
                },
                {
                  option_id: '51119',
                  option_name: '丙基硫氧嘧啶',
                  checked: false,
                },
                {
                  option_id: '51120',
                  option_name: '甲巯咪唑',
                  checked: false,
                },
                {
                  option_id: '51121',
                  option_name: '硒酵母',
                  checked: false,
                },
                {
                  option_id: '51122',
                  option_name: '糖皮质激素',
                  checked: false,
                },
                {
                  option_id: '51123',
                  option_name: '131I同位素治疗',
                  checked: false,
                },
                {
                  option_id: '51124',
                  option_name: '手术',
                  checked: false,
                },
                {
                  option_id: '51125',
                  option_name: '其他治疗',
                  checked: false,
                },
                {
                  option_id: '51126',
                  option_name: '否',
                  checked: false,
                },
              ],
            },
          ]
        },
        {
          id: 11,
          composing: 'block',
          title: 'K) 肝脏疾病',
          children: [
            {
              subquestion_id: '500',
              subquestion_name: '27、您现在或曾经是否患有肝脏疾病么？',
              show: true,
              inline_check: null,
              checked: null,
              datetime_key: null,
              datetime_value: null,
              children: [
                {
                  option_id: '51127',
                  option_name: '是',
                  checked: false,
                },
                {
                  option_id: '51128',
                  option_name: '否',
                  checked: false,
                },
                {
                  option_id: '51129',
                  option_name: '不清楚',
                  checked: false,
                },
              ],
            },
            {
              subquestion_id: '501',
              subquestion_name: '28、您患有的肝脏疾病类型(可多选)?',
              show: true,
              inline_check: null,
              checked: null,
              datetime_key: null,
              datetime_value: null,
              children: [
                {
                  option_id: '51130',
                  option_name: '脂肪肝',
                  checked: false,
                },
                {
                  option_id: '51131',
                  option_name: '乙肝',
                  checked: false,
                },
                {
                  option_id: '51132',
                  option_name: '丙肝',
                  checked: false,
                },
                {
                  option_id: '51133',
                  option_name: '其他类型的病毒性肝炎',
                  checked: false,
                },
                {
                  option_id: '51134',
                  option_name: '肝硬化',
                  checked: false,
                },
                {
                  option_id: '51135',
                  option_name: '自身免疫性肝病',
                  checked: false,
                },
                {
                  option_id: '51136',
                  option_name: '血吸虫肝病',
                  checked: false,
                },
                {
                  option_id: '51137',
                  option_name: '其他肝脏疾病(如肝脏良性肿瘤)',
                  checked: false,
                },
              ],
            },
          ]
        },
        {
          id: 12,
          composing: 'block',
          title: 'L) 胆道疾病',
          children: [
            {
              subquestion_id: '502',
              subquestion_name: '29、您现在或曾经是否患有胆道疾病么？',
              show: true,
              inline_check: null,
              checked: null,
              datetime_key: null,
              datetime_value: null,
              children: [
                {
                  option_id: '51138',
                  option_name: '是',
                  checked: false,
                },
                {
                  option_id: '51139',
                  option_name: '否',
                  checked: false,
                },
                {
                  option_id: '51140',
                  option_name: '不清楚',
                  checked: false,
                },
              ],
            },
            {
              subquestion_id: '503',
              subquestion_name: '30、您患有的胆道疾病类型(可多选)?',
              show: true,
              inline_check: null,
              checked: null,
              datetime_key: null,
              datetime_value: null,
              children: [
                {
                  option_id: '51141',
                  option_name: '胆囊炎',
                  checked: false,
                },
                {
                  option_id: '51142',
                  option_name: '胆结石',
                  checked: false,
                },
                {
                  option_id: '51143',
                  option_name: '胆囊息肉',
                  checked: false,
                },
                {
                  option_id: '51144',
                  option_name: '其他胆道疾病',
                  checked: false,
                },
              ],
            },
          ]
        },
        {
          id: 13,
          composing: 'block',
          title: 'M) 胰腺疾病',
          children: [
            {
              subquestion_id: '504',
              subquestion_name: '31、您现在或曾经是否患有胰腺疾病么？',
              show: true,
              inline_check: null,
              checked: null,
              datetime_key: null,
              datetime_value: null,
              children: [
                {
                  option_id: '51145',
                  option_name: '是',
                  checked: false,
                },
                {
                  option_id: '51146',
                  option_name: '否',
                  checked: false,
                },
                {
                  option_id: '51147',
                  option_name: '不清楚',
                  checked: false,
                },
              ],
            },
            {
              subquestion_id: '505',
              subquestion_name: '32、您患有的胰腺疾病类型?(可多选)',
              show: true,
              inline_check: null,
              checked: null,
              datetime_key: null,
              datetime_value: null,
              children: [
                {
                  option_id: '51148',
                  option_name: '急性胰腺炎',
                  unit: '首次诊断急性胰腺炎的时间：',
                  checked: false,
                },
                {
                  option_id: '51149',
                  option_name: '慢性胰腺炎',
                  unit: '首次诊断慢性胰腺炎的时间：',
                  checked: false,
                },
                {
                  option_id: '51150',
                  option_name: '其他胰腺疾病',
                  checked: false,
                },
              ],
            },
          ]
        },
        {
          id: 14,
          composing: 'block',
          title: 'N) 胃肠道疾病',
          children: [
            {
              subquestion_id: '506',
              subquestion_name: '33、您现在或曾经是否患有胃肠道疾病么？',
              show: true,
              inline_check: null,
              checked: null,
              datetime_key: null,
              datetime_value: null,
              children: [
                {
                  option_id: '51151',
                  option_name: '是',
                  checked: false,
                },
                {
                  option_id: '51152',
                  option_name: '否',
                  checked: false,
                },
                {
                  option_id: '51153',
                  option_name: '不清楚',
                  checked: false,
                },
              ],
            },
            {
              subquestion_id: '507',
              subquestion_name: '34、您患有的胃肠道疾病类型?',
              show: true,
              inline_check: null,
              checked: null,
              datetime_key: null,
              datetime_value: null,
              children: [
                {
                  option_id: '51154',
                  option_name: '慢性胃肠炎',
                  checked: false,
                },
                {
                  option_id: '51155',
                  option_name: '胃十二指肠溃疡',
                  checked: false,
                },
                {
                  option_id: '51156',
                  option_name: '其他胃肠疾病',
                  checked: false,
                },
              ],
            },
          ]
        },
        {
          id: 15,
          composing: 'block',
          title: 'O) 泌尿系统疾病',
          children: [
            {
              subquestion_id: '508',
              subquestion_name: '35、您现在或曾经是否患有泌尿系统疾病？',
              show: true,
              inline_check: null,
              checked: null,
              datetime_key: null,
              datetime_value: null,
              children: [
                {
                  option_id: '51157',
                  option_name: '是',
                  checked: false,
                },
                {
                  option_id: '51158',
                  option_name: '否',
                  checked: false,
                },
                {
                  option_id: '51159',
                  option_name: '不清楚',
                  checked: false,
                },
              ],
            },
            {
              subquestion_id: '509',
              subquestion_name: '36、您患有的泌尿系统疾病类型(可多选)?',
              show: true,
              inline_check: null,
              checked: null,
              datetime_key: null,
              datetime_value: null,
              children: [
                {
                  option_id: '51160',
                  option_name: '肾结石',
                  checked: false,
                },
                {
                  option_id: '51161',
                  option_name: '肾囊肿',
                  checked: false,
                },
                {
                  option_id: '51162',
                  option_name: '慢性肾炎',
                  unit: '首次诊断慢性肾炎的时间：',
                  checked: false,
                },
                {
                  option_id: '51163',
                  option_name: '肾病综合征',
                  unit: '首次诊断肾病综合征的时间：',
                  checked: false,
                },
                {
                  option_id: '51164',
                  option_name: '其它泌尿系统疾病',
                  checked: false,
                },
              ],
            },
          ]
        },
        {
          id: 16,
          composing: 'block',
          title: 'P) 呼吸系统疾病',
          children: [
            {
              subquestion_id: '510',
              subquestion_name: '37、您现在或曾经是否患有呼吸系统疾病么？',
              show: true,
              inline_check: null,
              checked: null,
              datetime_key: null,
              datetime_value: null,
              children: [
                {
                  option_id: '51165',
                  option_name: '是',
                  checked: false,
                },
                {
                  option_id: '51166',
                  option_name: '否',
                  checked: false,
                },
                {
                  option_id: '51167',
                  option_name: '不清楚',
                  checked: false,
                },
              ],
            },
            {
              subquestion_id: '511',
              subquestion_name: '38、您患有的呼吸系统疾病类型?(可多选)',
              show: true,
              inline_check: null,
              checked: null,
              datetime_key: null,
              datetime_value: null,
              children: [
                {
                  option_id: '50925',
                  option_name: '阻塞性睡眠呼吸暂停低通气综合征',
                  checked: false,
                },
                {
                  option_id: '51168',
                  option_name: '慢性支气管炎',
                  checked: false,
                },
                {
                  option_id: '51169',
                  option_name: '肺气肿',
                  checked: false,
                },
                {
                  option_id: '51170',
                  option_name: '其他呼吸系统疾病',
                  checked: false,
                },
              ],
            },
          ]
        },
        {
          id: 17,
          composing: 'block',
          title: 'Q) 其他疾病',
          children: [
            {
              subquestion_id: '973',
              subquestion_name: '39、是否有其他疾病么？',
              show: true,
              inline_check: true,
              checked: false,
              datetime_key: null,
              datetime_value: null,
            },
            {
              question_id: '971',
              subquestion_id: '',
              subquestion_name: '40、其他疾病（除以上疾病）',
              show: true,
              table_name: 'medical-history',
              inline_check: null,
              checked: null,
              datetime_key: null,
              datetime_value: null,
              data: {
                thead: ['疾病名称', '诊断日期', '医院名称'],
                tbody: [[],[],[]],
              },
            },
          ]
        },
        {
          id: 18,
          composing: 'block',
          title: 'A) 外伤史',
          children: [
            {
              subquestion_id: '513',
              subquestion_name: '1、您是否有外伤史？',
              show: true,
              inline_check: true,
              checked: false,
              datetime_key: null,
              datetime_value: null,
            },
            {
              subquestion_id: '514',
              subquestion_name: '2、您的外伤部位(可多选)',
              show: true,
              inline_check: null,
              checked: null,
              datetime_key: null,
              datetime_value: null,
              children: [
                {
                  option_id: '51177',
                  option_name: '头颈部',
                  checked: false,
                },
                {
                  option_id: '51178',
                  option_name: '胸部',
                  checked: false,
                },
                {
                  option_id: '51179',
                  option_name: '腹部',
                  checked: false,
                },
                {
                  option_id: '51180',
                  option_name: '上下肢',
                  checked: false,
                },
                {
                  option_id: '51181',
                  option_name: '手足',
                  checked: false,
                },
                {
                  option_id: '52797',
                  option_name: '腰背部',
                  checked: false,
                },
              ],
            },
          ]
        },
        {
          id: 19,
          composing: 'block',
          title: 'B) 手术史',
          children: [
            {
              subquestion_id: '515',
              subquestion_name: '3、您是否接受过手术治疗？',
              show: true,
              inline_check: true,
              checked: false,
              datetime_key: null,
              datetime_value: null,
            },
            {
              subquestion_id: '516',
              subquestion_name: '4、手术部位（可多选）',
              show: true,
              inline_check: null,
              checked: null,
              datetime_key: null,
              datetime_value: null,
              children: [
                {
                  option_id: '51184',
                  option_name: '肝脏',
                  checked: false,
                },
                {
                  option_id: '51185',
                  option_name: '胰腺',
                  checked: false,
                },
                {
                  option_id: '51186',
                  option_name: '胃',
                  checked: false,
                },
                {
                  option_id: '51187',
                  option_name: '结直肠',
                  checked: false,
                },
                {
                  option_id: '51188',
                  option_name: '乳腺',
                  checked: false,
                },
                {
                  option_id: '51189',
                  option_name: '宫颈',
                  checked: false,
                },
                {
                  option_id: '51190',
                  option_name: '子宫内膜',
                  checked: false,
                },
                {
                  option_id: '51191',
                  option_name: '卵巢',
                  checked: false,
                },
                {
                  option_id: '51192',
                  option_name: '膀胱',
                  checked: false,
                },
                {
                  option_id: '51193',
                  option_name: '前列腺',
                  checked: false,
                },
                {
                  option_id: '51194',
                  option_name: '肺',
                  checked: false,
                },
                {
                  option_id: '51195',
                  option_name: '肾脏',
                  checked: false,
                },
                {
                  option_id: '51196',
                  option_name: '其他',
                  checked: false,
                },
                {
                  option_id: '52880',
                  option_name: '其他2',
                  checked: false,
                },
                {
                  option_id: '52881',
                  option_name: '其他3',
                  checked: false,
                },
              ],
            },
          ]
        },
      ],
    }
  },
  mixins: [mixin],
  created() {
    this.getMedicalHistoryData();
  },
  methods: {
    // 获取用药史数据
    async getMedicalHistoryData() {
      const {
        user_id,
        visit_level,
        sex,
        hosp_id,
      } = this.$route.query;

      this.show_loading = true;

      const res = await api.qssInfo({
        sex,
        user_id,
        hosp_id,
        visit_level,
        template_id: 21,
      }).finally(() => {
        this.show_loading = false;
      });

      if (res.status === 0) {
        const list = res.data.question_data || [];

        this.setQuestionList(list);
      }

    },
    // 设置题目
    setQuestionList(list) {
      if (list.length > 0) {
        list.forEach(item => {
          // 设置第 A 题
          // 第 1 题
          if (item.subquestion_id === '947') {
            const answer = this.checkAnswerValue(item);
            if (answer) {
              const checked = answer.answer_value === '是' ? 1 : 0;
              this.question_list[0].children[0].checked = checked;
            } else {
              this.question_list[0].children[0].checked = -1;
            }
          }
          if (item.subquestion_id === '52795001') {
            const answer = this.checkAnswerValue(item);
            if (answer) {
              this.question_list[0].children[0].datetime_value = answer.answer_value;
            }
          }
          // 第 2 题
          if (item.subquestion_id === '969') {
            const questions = ['52838', '52839', '52840', '52841'];
            this.setQuestionCheckedStatus(item, questions, 0, 1);
          }
          if (item.subquestion_id === '52841001') {
            this.setMultiInputValue(item, 0, 1, 3);
          }

          // 设置第 B 题
          // 第 3 题
          if (item.subquestion_id === '476') {
            const answer = this.checkAnswerValue(item);
            if (answer) {
              const checked = answer.answer_value === '是' ? 1 : 0;
              this.question_list[1].children[0].checked = checked;
            } else {
              this.question_list[1].children[0].checked = -1;
            }
          }
          if (item.subquestion_id === '51029001') {
            const answer = this.checkAnswerValue(item);
            if (answer) {
              this.question_list[1].children[0].datetime_value = answer.answer_value;
            }
          }
          // 第 4 题
          if (item.subquestion_id === '477') {
            const answer = this.checkAnswerValue(item);
            if (answer) {
              const checked = answer.answer_value === '是' ? 1 : 0;
              this.question_list[1].children[1].checked = checked;
            } else {
              this.question_list[1].children[1].checked = -1;
            }
          }

          // 设置第 C 题
          // 第 5 题
          if (item.subquestion_id === '478') {
            const answer = this.checkAnswerValue(item);
            if (answer) {
              const checked = answer.answer_value === '是' ? 1 : 0;
              this.question_list[2].children[0].checked = checked;
            } else {
              this.question_list[2].children[0].checked = -1;
            }
          }
          if (item.subquestion_id === '51033001') {
            const answer = this.checkAnswerValue(item);
            if (answer) {
              this.question_list[2].children[0].datetime_value = answer.answer_value;
            }
          }
          // 第 6 题
          if (item.subquestion_id === '479') {
            const answer = this.checkAnswerValue(item);
            if (answer) {
              const checked = answer.answer_value === '是' ? 1 : 0;
              this.question_list[2].children[1].checked = checked;
            } else {
              this.question_list[2].children[1].checked = -1;
            }
          }

          // 设置第 D 题
          // 第 7 题
          if (item.subquestion_id === '480') {
            this.setInlineCheckbox(item, 3, 0);
          }
          if (item.subquestion_id === '51037001') {
            this.setInlineCheckboxDatetime(item, 3, 0);
          }
          // 第 8 题
          if (item.subquestion_id === '481') {
            this.setInlineCheckbox(item, 3, 1);
          }
          // 第 9 题
          if (item.subquestion_id === '482') {
            this.setInlineCheckbox(item, 3, 2);
          }

          // 设置第 E 题
          // 第 10 题
          if (item.subquestion_id === '483') {
            this.setInlineCheckbox(item, 4, 0);
          }
          if (item.subquestion_id === '51043001') {
            this.setInlineCheckboxDatetime(item, 4, 0);
          }
          // 第 11 题
          if (item.subquestion_id === '484') {
            const questions = ['51045', '51046', '51047', '51048', '51049', '52849'];
            this.setQuestionCheckedStatus(item, questions, 4, 1);
          }
          // 第 12 题
          if (item.subquestion_id === '485') {
            const questions = ['51050', '51051', '51052'];
            this.setQuestionCheckedStatus(item, questions, 4, 2);
          }
          // 第 13 题
          if (item.subquestion_id === '486') {
            const questions = ['51053', '51054', '51055', '51056', '51057', '51058'];
            this.setQuestionCheckedStatus(item, questions, 4, 3, 'all');
          }
          if (item.subquestion_id === '51057001') {
            this.setMultiInputValue(item, 4, 3, 4);
          }

          // 设置第 F 题
          // 第 14 题
          if (item.subquestion_id === '487') {
            this.setInlineCheckbox(item, 5, 0);
          }
          if (item.subquestion_id === '51059001') {
            this.setInlineCheckboxDatetime(item, 5, 0);
          }
          // 第 15 题
          if (item.subquestion_id === '488') {
            const questions = ['51061', '51062', '51063', '51064', '51065', '51066'];
            this.setQuestionCheckedStatus(item, questions, 5, 1, 'all');
          }
          if (item.subquestion_id === '51065001') {
            this.setMultiInputValue(item, 5, 1, 4);
          }
          // 第 16 题
          if (item.subquestion_id === '489') {
            const questions = ['51067', '51068', '51069', '51070', '51071'];
            this.setQuestionCheckedStatus(item, questions, 5, 2, 'all');
          }

          // 设置第 G 题
          // 第 17 题
          if (item.subquestion_id === '490') {
            this.setInlineCheckbox(item, 6, 0);
          }
          if (item.subquestion_id === '51072001') {
            this.setInlineCheckboxDatetime(item, 6, 0);
          }

          // 设置第 H 题
          // 第 18 题
          if (item.subquestion_id === '491') {
            this.setInlineCheckbox(item, 7, 0);
          }
          if (item.subquestion_id === '51074001') {
            this.setInlineCheckboxDatetime(item, 7, 0);
          }

          // 设置第 I 题
          // 第 19 题
          if (item.subquestion_id === '492') {
            this.setInlineCheckbox(item, 8, 0);
          }
          if (item.subquestion_id === '51076001') {
            this.setInlineCheckboxDatetime(item, 8, 0);
          }
          // 第 20 题
          if (item.subquestion_id === '493') {
            const questions = ['51078', '51079', '51080'];
            this.setQuestionCheckedStatus(item, questions, 8, 1, 'all');
          }
          // 第 21 题
          if (item.subquestion_id === '494') {
            const questions = ['51081', '51082', '51083', '51084', '51085', '51086', '51087', '51088', '51089', '51090', '51091', '51092', '51093', '51094', '51095', '51096'];
            this.setQuestionCheckedStatus(item, questions, 8, 2, 'all');
          }
          if (item.subquestion_id === '51096001') {
            this.setMultiInputValue(item, 8, 2, 15);
          }
          // 第 22 题
          if (item.subquestion_id === '495') {
            const questions = ['51097', '51098', '51099', '51100', '51101', '51102', '51103'];
            this.setQuestionCheckedStatus(item, questions, 8, 3, 'all');
          }
          if (item.subquestion_id === '51101001') {
            this.setMultiInputValue(item, 8, 3, 4);
          }

          // 设置第 J 题
          // 第 23 题
          if (item.subquestion_id === '496') {
            const questions = ['51104', '51105', '51106'];
            this.setQuestionCheckedStatus(item, questions, 9, 0);
          }
          if (item.subquestion_id === '51104001') {
            // this.setMultiInputValue(item, 9, 0, 0);
          }
          // 第 24 题
          if (item.subquestion_id === '497') {
            const questions = ['51107', '51108', '51109', '51110', '51111', '51112', '51113'];
            this.setQuestionCheckedStatus(item, questions, 9, 1, 'all');
          }
          if (item.subquestion_id === '51107001') {
            this.setSpecialInputValue(item, 9, 1, 0);
          }
          if (item.subquestion_id === '51108001') {
            this.setSpecialInputValue(item, 9, 1, 1);
          }
          if (item.subquestion_id === '51109001') {
            this.setSpecialInputValue(item, 9, 1, 2);
          }
          if (item.subquestion_id === '51110001') {
            this.setSpecialInputValue(item, 9, 1, 3);
          }
          if (item.subquestion_id === '51111001') {
            this.setSpecialInputValue(item, 9, 1, 4);
          }
          if (item.subquestion_id === '51112001') {
            this.setSpecialInputValue(item, 9, 1, 5);
          }
          if (item.subquestion_id === '51113001') {
            this.setSpecialInputValue(item, 9, 1, 6);
          }
          if (item.subquestion_id === '51113002') {
            this.setSpecialInputValue(item, 9, 1, 6);
          }
          
          
          
          // 第 25 题
          if (item.subquestion_id === '498') {
            const questions = ['51114', '51115', '51116', '51117'];
            this.setQuestionCheckedStatus(item, questions, 9, 2);
          }
          // 第 26 题
          if (item.subquestion_id === '499') {
            const questions = ['51118', '51119', '51120', '51121', '51122', '51123', '51124', '51125', '51126'];
            this.setQuestionCheckedStatus(item, questions, 9, 3, 'all');
          }
          if (item.subquestion_id === '51125001') {
            this.setMultiInputValue(item, 9, 3, 7);
          }

          // 设置第 K 题
          // 第 27 题
          if (item.subquestion_id === '500') {
            const questions = ['51127', '51128', '51129'];
            this.setQuestionCheckedStatus(item, questions, 10, 0);
          }
          // 第 28 题
          if (item.subquestion_id === '501') {
            const questions = ['51130', '51131', '51132', '51133', '51134', '51135', '51136', '51137'];
            this.setQuestionCheckedStatus(item, questions, 10, 1, 'all');
          }
          if (item.subquestion_id === '51130001') {
            // this.setMultiInputValue(item, 10, 1, 0);
            this.setSpecialInputValue(item, 10, 1, 0);
          }
          if (item.subquestion_id === '51131001') {
            // this.setMultiInputValue(item, 10, 1, 1);
            this.setSpecialInputValue(item, 10, 1, 1);
          }
          if (item.subquestion_id === '51132001') {
            // this.setMultiInputValue(item, 10, 1, 2);
            this.setSpecialInputValue(item, 10, 1, 2);
          }

          if (item.subquestion_id === '51133001') {
            this.setSpecialInputValue(item, 10, 1, 3);
          }
          if (item.subquestion_id === '51133002') {
            this.setSpecialInputValue(item, 10, 1, 3);
          }

          if (item.subquestion_id === '51134001') {
            // this.setMultiInputValue(item, 10, 1, 4);
            this.setSpecialInputValue(item, 10, 1, 4);
          }
          if (item.subquestion_id === '51135001') {
            // this.setMultiInputValue(item, 10, 1, 5);
            this.setSpecialInputValue(item, 10, 1, 5);
          }
          if (item.subquestion_id === '51136001') {
            // this.setMultiInputValue(item, 10, 1, 6);
            this.setSpecialInputValue(item, 10, 1, 6);
          }

          if (item.subquestion_id === '51137001') {
            this.setSpecialInputValue(item, 10, 1, 7);
          }
          if (item.subquestion_id === '51137002') {
            this.setSpecialInputValue(item, 10, 1, 7);
          }
          
          // 设置第 L 题
          // 第 29 题
          if (item.subquestion_id === '502') {
            const questions = ['51138', '51139', '51140'];
            this.setQuestionCheckedStatus(item, questions, 11, 0);
          }
          // 第 30 题
          if (item.subquestion_id === '503') {
            const questions = ['51141', '51142', '51143', '51144'];
            this.setQuestionCheckedStatus(item, questions, 11, 1, 'all');
          }
          if (item.subquestion_id === '51144001') {
            this.setMultiInputValue(item, 11, 1, 3);
          }

          // 设置第 M 题
          // 第 31 题
          if (item.subquestion_id === '504') {
            const questions = ['51145', '51146', '51147'];
            this.setQuestionCheckedStatus(item, questions, 12, 0);
          }
          // 第 32 题
          if (item.subquestion_id === '505') {
            const questions = ['51148', '51149', '51150'];
            this.setQuestionCheckedStatus(item, questions, 12, 1, 'all');
          }
          if (item.subquestion_id === '51148001') {
            this.setSpecialInputValue(item, 12, 1, 0);
          }
          if (item.subquestion_id === '51149001') {
            this.setSpecialInputValue(item, 12, 1, 1);
          }
          if (item.subquestion_id === '51150001') {
            this.setMultiInputValue(item, 12, 1, 2);
          }

          // 设置第 N 题
          // 第 33 题
          if (item.subquestion_id === '506') {
            const questions = ['51151', '51152', '51153'];
            this.setQuestionCheckedStatus(item, questions, 13, 0);
          }
          // 第 34 题
          if (item.subquestion_id === '507') {
            const questions = ['51154', '51155', '51156'];
            this.setQuestionCheckedStatus(item, questions, 13, 1, 'all');
          }
          if (item.subquestion_id === '51156001') {
            this.setMultiInputValue(item, 13, 1, 2);
          }

          // 设置第 O 题
          // 第 35 题
          if (item.subquestion_id === '508') {
            const questions = ['51157', '51158', '51159'];
            this.setQuestionCheckedStatus(item, questions, 14, 0);
          }
          // 第 36 题
          if (item.subquestion_id === '509') {
            const questions = ['51160', '51161', '51162', '51163', '51164'];
            this.setQuestionCheckedStatus(item, questions, 14, 1, 'all');
          }
          if (item.subquestion_id === '51162001') {
            this.setSpecialInputValue(item, 14, 1, 2);
          }
          if (item.subquestion_id === '51163001') {
            this.setSpecialInputValue(item, 14, 1, 3);
          }
          if (item.subquestion_id === '51164001') {
            this.setMultiInputValue(item, 14, 1, 4);
          }

          // 设置第 P 题
          // 第 37 题
          if (item.subquestion_id === '510') {
            const questions = ['51165', '51166', '51167'];
            this.setQuestionCheckedStatus(item, questions, 15, 0);
          }
          // 第 38 题
          if (item.subquestion_id === '511') {
            const questions = ['50925', '51168', '51169', '51170'];
            this.setQuestionCheckedStatus(item, questions, 15, 1, 'all');
          }
          if (item.subquestion_id === '51170001') {
            this.setMultiInputValue(item, 15, 1, 3);
          }

          // 设置第 Q 题
          // 第 39 题
          if (item.subquestion_id === '973') {
            this.setInlineCheckbox(item, 16, 0);
          }
          // 第 40 题
          if (item.question_id === '971') {
            this.setMedicalHistoryTableData(item, 16, 1);
          }

          // 设置第 A 题
          // 第 1 题
          if (item.subquestion_id === '513') {
            this.setInlineCheckbox(item, 17, 0);
          }
          // 第 2 题
          if (item.subquestion_id === '514') {
            const questions = ['51177', '51178', '51179', '51180', '51181', '52797'];
            this.setQuestionCheckedStatus(item, questions, 17, 1, 'all');
          }

          // 设置第 B 题
          // 第 3 题
          if (item.subquestion_id === '515') {
            this.setInlineCheckbox(item, 18, 0);
          }
          // 第 4 题
          if (item.subquestion_id === '516') {
            const questions = ['51184', '51185', '51186', '51187', '51188', '51189', '51190', '51191', '51192', '51193', '51194', '51195', '51196', '52880', '52881'];
            this.setQuestionCheckedStatus(item, questions, 18, 1, 'all');
          }
          if (item.subquestion_id !== '516' && item.question_id === '516') {
            this.seOperationInputValue(item, 18, 1, 0);
          }

        })
      }
    },
    // 设置其它疾病
    setMedicalHistoryTableData(item, i, j) {
      const answer = this.checkAnswerValue(item);
      const value = answer ? answer.answer_value : '';
      switch (item.subquestion_id) {
        case '971001':
          this.$set(this.question_list[i].children[j].data.tbody[0], 0, value);
          break;
        case '971002':
          this.$set(this.question_list[i].children[j].data.tbody[0], 1, value);
          break;
        case '971003':
          this.$set(this.question_list[i].children[j].data.tbody[0], 2, value);
          break;
        case '971004':
          this.$set(this.question_list[i].children[j].data.tbody[1], 0, value);
          break;
        case '971005':
          this.$set(this.question_list[i].children[j].data.tbody[1], 1, value);
          break;
        case '971006':
          this.$set(this.question_list[i].children[j].data.tbody[1], 2, value);
          break;
        case '971007':
          this.$set(this.question_list[i].children[j].data.tbody[2], 0, value);
          break;
        case '971008':
          this.$set(this.question_list[i].children[j].data.tbody[2], 1, value);
          break;
        case '971009':
          this.$set(this.question_list[i].children[j].data.tbody[2], 2, value);
          break;
        default:
          break;
      }
    },
    // 手术部位
    seOperationInputValue(item, i, j) {
      const answer = this.checkAnswerValue(item);
      if (answer) {
        const prefix = item.subquestion_id.slice(0, 5);
        const suffix = item.subquestion_id.slice(-1);

        const children = this.question_list[i].children[j].children;

        children.forEach((v, n) => {
          if (v.option_id === prefix) {
            let initial = this.question_list[i].children[j].children[n].value || '';
            let value = answer.answer_value;
            let num = this.question_list[i].children[j].children[n].num;
            
            if (suffix === '1') {
              this.question_list[i].children[j].children[n].value1 = '手术名称：' + value;
            }

            if (suffix === '2') {
              this.question_list[i].children[j].children[n].value2 = '手术时间：' + value;
            }

            const value1 = this.question_list[i].children[j].children[n].value1;
            const value2 = this.question_list[i].children[j].children[n].value2;

            let result = '';
            if (value1) {
              result = value1;
            }
            if (value2) {
              result = value2;
            }
            if (value1 && value2) {
              result = `${value1} ${value2}`;
            }
            this.question_list[i].children[j].children[n].value = result;
          }
        })
      }
    },
  }
}
</script>

<style scoped lang="scss">
// .medical-history {}
</style>