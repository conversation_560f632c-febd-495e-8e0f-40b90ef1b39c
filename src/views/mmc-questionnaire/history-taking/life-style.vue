<template>
  <div class="life-style">
    <van-collapse v-model="activeNames">
      <van-collapse-item title="吸烟情况" name="1">
        <comp-questionnaire-list :list="question_list.slice(0, 1)" />
      </van-collapse-item>
      <van-collapse-item title="饮酒情况" name="2">
        <comp-questionnaire-list :list="question_list.slice(1, 2)" />
      </van-collapse-item>
      <van-collapse-item title="饮食情况" name="3">
        <comp-questionnaire-list :list="question_list.slice(2, 3)" />
      </van-collapse-item>
      <van-collapse-item title="体力活动与睡眠情况" name="4">
        <comp-questionnaire-list :list="question_list.slice(3, 4)" />
      </van-collapse-item>
    </van-collapse>

    <van-overlay :show="show_loading" class-name="mmc-questionnaire-loading-overlay">
      <van-loading type="spinner" />
    </van-overlay>
  </div>
</template>

<script>
import api from '@/api/mmc-questionnaire.js';
import compQuestionnaireList from '../components/comp-questionnaire-list.vue';
import mixin from '../common/mixins/mixin';

export default {
  name: 'lifeStyle',
  components: {
    compQuestionnaireList,
  },
  data() {
    return {
      sleepType : false,
      show_loading: false,
      activeNames: ['1', '2', '3', '4'],
      question_list: [
        {
          id: 1,
          composing: 'block',
          title: null,
          children: [
            {
              subquestion_id: '547',
              subquestion_name: '1、请问您现在抽烟吗？',
              show: true,
              inline_check: null,
              checked: null,
              datetime: null,
              children: [
                {
                  option_id: '51288',
                  option_name: '现在不',
                  checked: false,
                },
                {
                  option_id: '51289',
                  option_name: '偶尔(少于每天一次或每周少于7支)',
                  checked: false,
                },
                {
                  option_id: '51290',
                  option_name: '是,每天或几乎每天',
                  checked: false,
                },
              ],
            },
            {
              subquestion_id: '548',
              subquestion_name: '2、请问您过去的吸烟习惯？',
              show: true,
              inline_check: null,
              checked: null,
              datetime: null,
              children: [
                {
                  option_id: '51291',
                  option_name: '从不吸烟',
                  checked: false,
                },
                {
                  option_id: '51292',
                  option_name: '过去偶尔吸烟(少于每天一次或每周少于7支)',
                  checked: false,
                },
                {
                  option_id: '51293',
                  option_name: '过去习惯每天吸烟',
                  checked: false,
                },
              ],
            },
            {
              subquestion_id: '549',
              subquestion_name: '3、请问您戒烟#年#月了？',
              subquestion_name_format: [
                {
                  key: 'normal',
                  value: '3、请问您戒烟',
                },
                {
                  key: 'input',
                  value: '',
                },
                {
                  key: 'normal',
                  value: '年',
                },
                {
                  key: 'input',
                  value: '',
                },
                {
                  key: 'normal',
                  value: '月了？',
                },
              ],
              show: true,
              inline_check: null,
              inline_input: true,
              checked: null,
              datetime_key: null,
              datetime_value: null,
            },
            {
              subquestion_id: '550',
              subquestion_name: '4、您现在或在戒烟前，吸烟的量是多少？',
              show: true,
              inline_check: null,
              checked: null,
              datetime: null,
              children: [
                {
                  option_id: '51294',
                  option_name: '香烟',
                  suffix: '支/天',
                  checked: false,
                },
                {
                  option_id: '51295',
                  option_name: '雪茄',
                  suffix: '支/天',
                  checked: false,
                },
                {
                  option_id: '51296',
                  option_name: '手卷烟',
                  suffix: '两/月',
                  checked: false,
                },
                {
                  option_id: '51329',
                  option_name: '电子烟',
                  suffix: 'ml/周',
                  checked: false,
                },
              ],
            },
            {
              subquestion_id: '552',
              subquestion_name: '5、您习惯性每天吸烟#年#月了？',
              subquestion_name_format: [
                {
                  key: 'normal',
                  value: '5、您习惯性每天吸烟',
                },
                {
                  key: 'input',
                  value: '',
                },
                {
                  key: 'normal',
                  value: '年',
                },
                {
                  key: 'input',
                  value: '',
                },
                {
                  key: 'normal',
                  value: '月了？',
                },
              ],
              show: true,
              inline_check: null,
              inline_input: true,
              checked: null,
              datetime_key: null,
              datetime_value: null,
            },
          ],
        },
        {
          id: 2,
          composing: 'block',
          title: null,
          children: [
            {
              subquestion_id: '553',
              subquestion_name: '1、请问您现在饮酒吗？',
              show: true,
              inline_check: null,
              checked: null,
              datetime: null,
              children: [
                {
                  option_id: '51299',
                  option_name: '现在不',
                  checked: false,
                },
                {
                  option_id: '51300',
                  option_name: '偶尔(少于每周一次)',
                  checked: false,
                },
                {
                  option_id: '51301',
                  option_name: '是,每周或几乎每周',
                  checked: false,
                },
              ],
            },
            {
              subquestion_id: '554',
              subquestion_name: '2、请问您过去的饮酒习惯？',
              show: true,
              inline_check: null,
              checked: null,
              datetime: null,
              children: [
                {
                  option_id: '51302',
                  option_name: '从不饮酒',
                  checked: false,
                },
                {
                  option_id: '51303',
                  option_name: '过去偶尔饮酒(少于每周一次)',
                  checked: false,
                },
                {
                  option_id: '51304',
                  option_name: '过去习惯每周饮酒',
                  checked: false,
                },
              ],
            },
            {
              subquestion_id: '555',
              subquestion_name: '3、请问您戒酒#年#月了？',
              subquestion_name_format: [
                {
                  key: 'normal',
                  value: '3、请问您戒酒',
                },
                {
                  key: 'input',
                  value: '',
                },
                {
                  key: 'normal',
                  value: '年',
                },
                {
                  key: 'input',
                  value: '',
                },
                {
                  key: 'normal',
                  value: '月了？',
                },
              ],
              show: true,
              inline_check: null,
              inline_input: true,
              checked: null,
              datetime_key: null,
              datetime_value: null,
            },
            {
              subquestion_id: '556',
              subquestion_name: '4、请问您现在或在戒酒前，饮酒的量和频率是怎样的？',
              show: true,
              inline_check: null,
              checked: null,
              datetime_key: null,
              datetime_value: null,
              children: [
                {
                  option_id: '51305',
                  option_name: '白酒(威士忌):',
                  checked: false,
                },
                {
                  option_id: '51306',
                  option_name: '黄酒(米酒):',
                  checked: false,
                },
                {
                  option_id: '51307',
                  option_name: '葡萄酒:',
                  checked: false,
                },
                {
                  option_id: '51308',
                  option_name: '啤酒(750ml/瓶):',
                  checked: false,
                },
                {
                  option_id: '51309',
                  option_name: '其他种类酒',
                  checked: false,
                },
              ],
            },
            {
              subquestion_id: '557',
              subquestion_name: '5、您习惯性地饮酒#年#月了？',
              subquestion_name_format: [
                {
                  key: 'normal',
                  value: '5、您习惯性地饮酒',
                },
                {
                  key: 'input',
                  value: '',
                },
                {
                  key: 'normal',
                  value: '年',
                },
                {
                  key: 'input',
                  value: '',
                },
                {
                  key: 'normal',
                  value: '月了？',
                },
              ],
              show: true,
              inline_check: null,
              inline_input: true,
              checked: null,
              datetime_key: null,
              datetime_value: null,
            },
          ],
        },
        {
          id: 3,
          composing: 'block',
          title: null,
          children: [
            {
              subquestion_id: '979',
              subquestion_name: '1、您平均每天吃多少新鲜蔬菜（按生重）？',
              show: true,
              inline_check: null,
              checked: null,
              datetime: null,
              children: [
                {
                  option_id: '97901',
                  option_name: '少于200克',
                  checked: false,
                },
                // {
                //   option_id: '97902',
                //   option_name: '200克-400克(不包括400克)',
                //   checked: false,
                // },
                // {
                //   option_id: '97903',
                //   option_name: '400克-600克(不包括600克)',
                //   checked: false,
                // },
                {
                  option_id: '97905',
                  option_name: '200克-300克(不包括300克)',
                  checked: false,
                },
                {
                  option_id: '97906',
                  option_name: '300克-400克(不包括400克)',
                  checked: false,
                },
                {
                  option_id: '97907',
                  option_name: '400克-500克(不包括500克)',
                  checked: false,
                },
                {
                  option_id: '97908',
                  option_name: '500克-600克(不包括600克)',
                  checked: false,
                },
                {
                  option_id: '97904',
                  option_name: '600克及以上',
                  checked: false,
                },
              ],
            },
            {
              subquestion_id: '978',
              subquestion_name: '2、您平均每天吃多少新鲜水果？',
              show: true,
              inline_check: null,
              checked: null,
              datetime: null,
              children: [
                {
                  option_id: '97801',
                  option_name: '少于200克',
                  checked: false,
                },
                // {
                //   option_id: '97802',
                //   option_name: '200克-400克(不包括400克)',
                //   checked: false,
                // },
                // {
                //   option_id: '97803',
                //   option_name: '400克-600克(不包括600克)',
                //   checked: false,
                // },
                {
                  option_id: '97805',
                  option_name: '200克-300克(不包括300克)',
                  checked: false,
                },
                {
                  option_id: '97806',
                  option_name: '300克-400克(不包括400克)',
                  checked: false,
                },
                {
                  option_id: '97807',
                  option_name: '400克-500克(不包括500克)',
                  checked: false,
                },
                {
                  option_id: '97808',
                  option_name: '500克-600克(不包括600克)',
                  checked: false,
                },
                {
                  option_id: '97804',
                  option_name: '600克及以上',
                  checked: false,
                },
              ],
            },
            {
              subquestion_id: '980',
              subquestion_name: '3、您平均每天一共吃多少新鲜蔬菜（按生重）和新鲜水果？',
              show: true,
              inline_check: null,
              checked: null,
              datetime: null,
              children: [
                {
                  option_id: '98001',
                  option_name: '少于500克',
                  checked: false,
                },
                {
                  option_id: '98002',
                  option_name: '500克及以上',
                  checked: false,
                },
              ],
            },
            {
              subquestion_id: '982',
              subquestion_name: '4、您平均每周吃几次的鱼类？［指各种未经特殊加工(如腌/晒等)的新鲜或冷冻的鲜淡水鱼、海水鱼等。］',
              show: true,
              inline_check: null,
              checked: null,
              datetime: null,
              children: [
                {
                  option_id: '98201',
                  option_name: '小于或等于1次',
                  checked: false,
                },
                {
                  option_id: '98202',
                  option_name: '大于或等于2次,至少2次每次大于或等于100克(按生重计)',
                  checked: false,
                },
                {
                  option_id: '98203',
                  option_name: '大于或等于2次,每次小于100克或仅有1次大于或等于100克 (按生重计)',
                  checked: false,
                },
              ],
            },
            {
              subquestion_id: '981',
              subquestion_name: '5、您平均每天吃多少豆制品？(豆制品包括大豆、绿豆、豌豆等,以及豆浆、豆奶、豆腐等,腐乳不算在内。不同豆制品的重量都折算成北豆腐的重量，具体详见豆制品折算表)',
              show: true,
              inline_check: null,
              checked: null,
              datetime: null,
              children: [
                {
                  option_id: '98101',
                  option_name: '少于100克',
                  checked: false,
                },
                {
                  option_id: '98102',
                  option_name: '100克-250克(不包括250克)',
                  checked: false,
                },
                {
                  option_id: '98103',
                  option_name: '250克-400克(不包括400克)',
                  checked: false,
                },
                {
                  option_id: '98104',
                  option_name: '400克-500克(不包括500克)',
                  checked: false,
                },
                {
                  option_id: '98105',
                  option_name: '500克及以上',
                  checked: false,
                },
              ],
            },
            {
              subquestion_id: '983',
              subquestion_name: '6、您平均每日的食盐量是多少？',
              show: true,
              inline_check: null,
              checked: null,
              datetime: null,
              children: [
                {
                  option_id: '98301',
                  option_name: '少于4克',
                  checked: false,
                },
                {
                  option_id: '98302',
                  option_name: '4克-6克(包括4克和6克)',
                  checked: false,
                },
                {
                  option_id: '98303',
                  option_name: '6克-8克(不包括6克)',
                  checked: false,
                },
                {
                  option_id: '98304',
                  option_name: '大于8克',
                  checked: false,
                },
                {
                  option_id: '98305',
                  option_name: '不清楚',
                  checked: false,
                },
              ],
            },
            {
              subquestion_id: '984',
              subquestion_name: '7、您喝含糖饮料吗？（如果汁、可乐、雪碧等，1瓶/杯按250毫升计）',
              show: true,
              inline_check: null,
              checked: null,
              datetime: null,
              children: [
                {
                  option_id: '98401',
                  option_name: '从不喝或很少喝(1个月1-3瓶/杯)',
                  checked: false,
                },
                {
                  option_id: '98402',
                  option_name: '每周1-2瓶/杯',
                  checked: false,
                },
                {
                  option_id: '98403',
                  option_name: '每周3-4瓶/杯',
                  checked: false,
                },
                {
                  option_id: '98404',
                  option_name: '每周5瓶/杯或更多',
                  checked: false,
                },
              ],
            },
          ],
        },{
          id: 4,
          composing: 'block',
          title: null,
          children: [
            {
              subquestion_id: '560',
              subquestion_name: '1、请问您是否还在工作（全职或兼职）？',
              show: true,
              inline_check: true,
              checked: false,
              datetime_key: null,
              datetime_value: null,
            },
            {
              subquestion_id: '561',
              subquestion_name: '2、您工作时：',
              show: true,
              inline_check: null,
              checked: null,
              datetime: null,
              children: [
                {
                  option_id: '51312',
                  option_name: '不太活动(办公室等)',
                  checked: false,
                },
                {
                  option_id: '51313',
                  option_name: '轻度活动(流水线工作等)',
                  checked: false,
                },
                {
                  option_id: '51314',
                  option_name: '中度活动(搬运工等)',
                  checked: false,
                },
                {
                  option_id: '51315',
                  option_name: '重度活动(炼钢、农业等)',
                  checked: false,
                },
                {
                  option_id: null,
                  option_name: null,
                  checked: false,
                },
              ],
            },
            {
              subquestion_id: '381',
              subquestion_name: '3、您的工作性质是什么？',
              show: true,
              inline_check: null,
              checked: false,
              datetime: null,
              children: [
                {
                  option_id: '38101',
                  option_name: '只上日班',
                  checked: false,
                },
                {
                  option_id: '38102',
                  option_name: '只上夜班',
                  checked: false,
                },
                {
                  option_id: '38103',
                  option_name: '日夜轮班',
                  checked: false,
                },
                {
                  option_id: null,
                  option_name: null,
                  checked: false,
                },
              ],
            },
            {
              subquestion_id: '562',
              subquestion_name: '4、请提供您在过去的七天里面，剧烈体力活动的情况（剧烈活动是指令您觉得呼吸吃力的很多活动，例如举重物,打篮球，游泳，跑步等。请您只计算每次持续十分钟或十分钟以上的活动，询问工作以外业余时间里的剧烈体力活动情况）',
              show: true,
              inline_check: null,
              checked: null,
              datetime: null,
              children: [
                {
                  option_id: '51316',
                  option_name: '有',
                  checked: false,
                },
                {
                  option_id: '51317',
                  option_name: '无',
                  checked: false,
                },
                {
                  option_id: null,
                  option_name: null,
                  checked: false,
                },
              ],
            },
            {
              subquestion_id: '563',
              subquestion_name: '5、请提供您在过去的七天里面，中等强度体力活动的情况（中等强度的体力活动指令您呼吸稍比正常吃力的活动，例如：缓步跑，打乒乓球，耍太极等，但不包括步行。请您只计算每次持续十分钟或十分钟以上的活动，询问工作以外业余时间里的中等强度体力活动情况）',
              show: true,
              inline_check: null,
              checked: null,
              datetime: null,
              children: [
                {
                  option_id: '51318',
                  option_name: '有',
                  checked: false,
                },
                {
                  option_id: '51319',
                  option_name: '无',
                  checked: false,
                },
                {
                  option_id: null,
                  option_name: null,
                  checked: false,
                },
              ],
            },
            {
              subquestion_id: '564',
              subquestion_name: '6、请提供您在过去的七天里面，步行活动的情况（包括任何形式的步行。请您只计算每次持续十分钟或十分钟以上的步行）',
              show: true,
              inline_check: null,
              checked: null,
              datetime: null,
              children: [
                {
                  option_id: '51320',
                  option_name: '有',
                  checked: false,
                },
                {
                  option_id: '51321',
                  option_name: '无',
                  checked: false,
                },
                {
                  option_id: null,
                  option_name: null,
                  checked: false,
                },
              ],
            },
            {
              subquestion_id: '565',
              subquestion_name: '7、请提供您在过去一周工作日中，坐着的情况（包含工作及休闲时的坐着）',
              show: true,
              inline_check: null,
              checked: null,
              datetime: null,
              children: [
                {
                  option_id: '51322',
                  option_name: '有',
                  checked: false,
                },
                {
                  option_id: '51323',
                  option_name: '无',
                  checked: false,
                },
                {
                  option_id: null,
                  option_name: null,
                  checked: false,
                },
              ],
            },
            {
              subquestion_id: '566',
              subquestion_name: '8、请提供您在过去一周周末中，坐着的情况（包含工作及休闲时的坐着）',
              show: true,
              inline_check: null,
              checked: null,
              datetime: null,
              children: [
                {
                  option_id: '51324',
                  option_name: '有',
                  checked: false,
                },
                {
                  option_id: '51325',
                  option_name: '无',
                  checked: false,
                },
                {
                  option_id: null,
                  option_name: null,
                  checked: false,
                },
              ],
            },
            {
              subquestion_id: '567',
              subquestion_name: '9、在过去的七天里，您每天睡眠情况（时间记录请采用24小时制）',
              show: true,
              inline_check: null,
              checked: null,
              datetime: null,
              children: [
                {
                  option_id: '51326',
                  option_name: '睡眠感觉良好',
                  checked: false,
                },
                {
                  option_id: '51327',
                  option_name: '睡眠感觉不好',
                  checked: false,
                },
                {
                  option_id: '51328',
                  option_name: '借助安眠药等助眠',
                  checked: false,
                },
                {
                  option_id: null,
                  option_name: null,
                  checked: false,
                },
                {
                  option_id: null,
                  option_name: null,
                  checked: false,
                },
              ],
            },
          ],
        },
      ],
    }
  },
  mixins: [mixin],
  created() {
    this.getLifeStyleData();
  },
  methods: {
    // 获取生活方式数据
    async getLifeStyleData() {
      const {
        user_id,
        visit_level,
        sex,
        hosp_id,
      } = this.$route.query;

      this.show_loading = true;

      const res = await api.qssInfo({
        sex,
        user_id,
        hosp_id,
        visit_level,
        template_id: 25,
      }).finally(() => {
        this.show_loading = false;
      });

      if (res.status === 0) {
        const list = res.data.question_data || [];

        this.setQuestionList(list);
      }

    },
    // 设置题目
    setQuestionList(list) {
      if (list.length > 0) {
        list.forEach(item => {
          // 吸烟情况
          // 第 1 题
          if (item.subquestion_id === '547') {
            const questions = ['51288', '51289', '51290'];
            this.setQuestionCheckedStatus(item, questions, 0, 0);
          }
          // 第 2 题
          if (item.subquestion_id === '548') {
            const questions = ['51291', '51292', '51293'];
            this.setQuestionCheckedStatus(item, questions, 0, 1);
          }
          // 第 3 题
          if (item.subquestion_id === '549') {
            this.setTitleInput(item, 0, 2, 1);
          }
          if (item.subquestion_id === '549001') {
            this.setTitleInput(item, 0, 2, 3);
          }
          // 第 4 题
          if (item.subquestion_id === '550') {
            const questions = ['51294', '51295', '51296', '51329'];
            this.setQuestionCheckedStatus(item, questions, 0, 3, 'all');
          }
          if (item.subquestion_id === '51294001') {
            this.setMultiInputValue(item, 0, 3, 0);
          }
          if (item.subquestion_id === '51295001') {
            this.setMultiInputValue(item, 0, 3, 1);
          }
          if (item.subquestion_id === '51296001') {
            this.setMultiInputValue(item, 0, 3, 2);
          }
          if (item.subquestion_id === '51329001') {
            this.setMultiInputValue(item, 0, 3, 3);
          }
          // 第 5 题
          if (item.subquestion_id === '552') {
            this.setTitleInput(item, 0, 4, 1);
          }
          if (item.subquestion_id === '552001') {
            this.setTitleInput(item, 0, 4, 3);
          }

          // 饮酒情况
          // 第 1 题
          if (item.subquestion_id === '553') {
            const questions = ['51299', '51300', '51301'];
            this.setQuestionCheckedStatus(item, questions, 1, 0);
          }
          // 第 2 题
          if (item.subquestion_id === '554') {
            const questions = ['51302', '51303', '51304'];
            this.setQuestionCheckedStatus(item, questions, 1, 1);
          }
          // 第 3 题
          if (item.subquestion_id === '555') {
            this.setTitleInput(item, 1, 2, 1);
          }
          if (item.subquestion_id === '555001') {
            this.setTitleInput(item, 1, 2, 3);
          }
          // 第 4 题
          if (item.subquestion_id === '556') {
            const questions = ['51305', '51306', '51307', '51308', '51309'];
            this.setQuestionCheckedStatus(item, questions, 1, 3, 'all');
          }
          if (item.subquestion_id !== '556' && item.question_id === '556') {
            this.setDrinkingInputValue(item, 1, 3, 0);
          }
          // 第 5 题
          if (item.subquestion_id === '557') {
            this.setTitleInput(item, 1, 4, 1);
          }
          if (item.subquestion_id === '557001') {
            this.setTitleInput(item, 1, 4, 3);
          }

          // 饮食情况
          // 第 1 题
          if (item.subquestion_id === '979') {            
            const questions = ['97901', '97905', '97906', '97907', '97908', '97904','97902','97903'];
            this.setQuestionCheckedStatus(item, questions, 2, 0);
          }
          // 第 2 题
          if (item.subquestion_id === '978') {            
            const questions = ['97801', '97805', '97806', '97807', '97808', '97804','97802','97803'];
            this.setQuestionCheckedStatus(item, questions, 2, 1);
          }
          // 第 3 题
          if (item.subquestion_id === '980') {
            const questions = ['98001', '98002'];
            this.setQuestionCheckedStatus(item, questions, 2, 2);
          }
          // 第 4 题
          if (item.subquestion_id === '982') {
            const questions = ['98201', '98202', '98203'];
            this.setQuestionCheckedStatus(item, questions, 2, 3);
          }
          // 第 5 题
          if (item.subquestion_id === '981') {
            const questions = ['98101', '98102', '98103', '98104', '98105'];
            this.setQuestionCheckedStatus(item, questions, 2, 4);
          }
          // 第 6 题
          if (item.subquestion_id === '983') {
            const questions = ['98301', '98302', '98303', '98304', '98305'];
            this.setQuestionCheckedStatus(item, questions, 2, 5);
          }
          // 第 7 题
          if (item.subquestion_id === '984') {
            const questions = ['98401', '98402', '98403', '98404'];
            this.setQuestionCheckedStatus(item, questions, 2, 6);
          }

          // 体力活动与睡眠情况
          // 第 1 题
          if (item.subquestion_id === '560') {
            this.setInlineCheckbox(item, 3, 0);
          }
          // 第 2 题
          if (item.subquestion_id === '561') {
            const questions = ['51312', '51313', '51314', '51315'];
            this.setQuestionCheckedStatus(item, questions, 3, 1);
          }
          if (item.subquestion_id !== '561' && item.question_id === '561') {
            this.setActivitySleepInputValue(item, 3, 1);
          }
          // 第 3 题
          if (item.subquestion_id === '381') {
            const questions = ['38101', '38102', '38103']; 
            this.setQuestionCheckedStatus(item, questions, 3, 2);
            if(item.answer.length!=0 && item.answer[0].answer_id == '38103'){
              this.sleepType = true
            }else {
              this.sleepType = false
            }
          }          
          if (item.subquestion_id !== '381' && item.question_id === '381') {
            this.setActivitySleepInputValue(item, 3, 2);
          }
          if (item.subquestion_id === '562') {
            const questions = ['51316','51317', '38102','38103'];
            this.setQuestionCheckedStatus(item, questions, 3, 3);
          }
          if (item.subquestion_id !== '562' && item.question_id === '562') {
            this.setActivitySleepInputValue(item, 3, 3);
          }
          // 第 4 题
          if (item.subquestion_id === '563') {
            const questions = ['51318', '51319'];
            this.setQuestionCheckedStatus(item, questions, 3, 4);
          }
          if (item.subquestion_id !== '563' && item.question_id === '563') {
            this.setActivitySleepInputValue(item, 3, 4);
          }
          // 第 5 题
          if (item.subquestion_id === '564') {
            const questions = ['51320', '51321'];
            this.setQuestionCheckedStatus(item, questions, 3, 5);
          }
          if (item.subquestion_id !== '564' && item.question_id === '564') {
            this.setActivitySleepInputValue(item, 3, 5);
          }
          // 第 6 题
          if (item.subquestion_id === '565') {
            const questions = ['51322', '51323'];
            this.setQuestionCheckedStatus(item, questions, 3, 6);
          }
          if (item.subquestion_id !== '565' && item.question_id === '565') {
            this.setActivitySleepInputValue(item, 3, 6);
          }
          // 第 7 题
          if (item.subquestion_id === '566') {
            const questions = ['51324', '51325'];
            this.setQuestionCheckedStatus(item, questions, 3, 7);
          }
          if (item.subquestion_id !== '566' && item.question_id === '566') {
            this.setActivitySleepInputValue(item, 3, 7);
          }
          if (item.subquestion_id === '567') {
            const questions = ['51326', '51327', '51328'];
            this.setQuestionCheckedStatus(item, questions, 3, 8);
          }
          if (item.subquestion_id !== '567' && item.question_id === '567') {
            this.setSleepPastSevenInputValue(item, 3, 8,this.sleepType);
          }

        })
      }
    },
    // 设置标题填空题
    setTitleInput(item, i, j, k) {
      const answer = this.checkAnswerValue(item);
      if (answer) {
        this.question_list[i].children[j].subquestion_name_format[k].value = answer.answer_value;
      }
    },
    // 饮酒情况
    setDrinkingInputValue(item, i, j) {
      const answer = this.checkAnswerValue(item);
      if (answer) {
        const prefix = item.subquestion_id.slice(0, 5);

        const children = this.question_list[i].children[j].children;

        let unit = '';
        if (['51305001', '51306001', '51307001', '51309002'].includes(item.subquestion_id)) { 
          unit = '两/次';
        }
        if (['51305002', '51306002', '51307002', '51308002', '51309003'].includes(item.subquestion_id)) { 
          unit = '次/周';
        }
        if (item.subquestion_id === '51308001') {
          unit = '瓶/次';
        }

        children.forEach((v, n) => {
          if (v.option_id === prefix) {
            let initial = this.question_list[i].children[j].children[n].value || '';
            let value = answer.answer_value;
            let num = this.question_list[i].children[j].children[n].num;

            if (num) {
              this.question_list[i].children[j].children[n].num = num++;
              value = `${initial} ${value}${unit}`;
            } else {
              this.question_list[i].children[j].children[n].num = 1;

              value = value + unit;
            }

            this.question_list[i].children[j].children[n].value = value;
          }
        })
      }
    },
    // 体力活动与睡眠情况
    setActivitySleepInputValue(item, i, j) {
      const answer = this.checkAnswerValue(item);
      if (answer) {
        let value = answer.answer_value;

        const arr1 = ['561001', '51316001', '51318001', '51320001', '51322001', '51324001'];
        if (arr1.includes(item.subquestion_id)) {
          value = `每周${value}天`;
          this.question_list[i].children[j].children[0].value1 = value;
        }

        const arr2 = ['561002', '51316002', '51318002', '51320002', '51322002', '51324002'];
        if (arr2.includes(item.subquestion_id)) {
          value = `每天${value}小时`;
          this.question_list[i].children[j].children[0].value2 = value;
        }

        let m = 0;
        if (item.question_id === '561') m = 4;
        if (item.question_id === '562') m = 2;
        if (item.question_id === '563') m = 2;
        if (item.question_id === '564') m = 2;
        if (item.question_id === '565') m = 2;
        if (item.question_id === '566') m = 2;
        // const k = this.question_list[i].children[j].children.findIndex(v => v.checked);
        // if (k >= 0) {
          const value1 = this.question_list[i].children[j].children[0].value1;
          const value2 = this.question_list[i].children[j].children[0].value2;

          let result = '';
          if (value1 && value2) {
            result = `${value1}，${value2}`;
          } else if (!value1 && value2) {
            result = value2;
          } else if (!value2 && value1) {
            result = value1;
          }
          
          this.question_list[i].children[j].children[m].value = result;
        // }
      }
    },
    // 过去七天睡眠情况
    setSleepPastSevenInputValue(item, i, j,type) {
      let result = '';
      const answer = this.checkAnswerValue(item);
      if (answer) {
        const value = answer.answer_value;

        if (item.subquestion_id === '567001') {
          result = `每天中午睡<i>${value}</i>小时，`;
        }
        if (item.subquestion_id === '567002') {
          result = `<i>${value}</i>分钟`;

        }
        if (item.subquestion_id === '567003') {
          result = `晚上从<i>${value}</i>睡到`;
        }
        if (item.subquestion_id === '567004') {
          result = `<i>${value}</i>`;
        }
        if (item.subquestion_id === '567005') {
          result = `通常一天内您睡觉累计有<i>${value}</i>小时，`;
        }
        if (item.subquestion_id === '567006') {
          result = `<i>${value}</i>分钟`;
        }
      } else {
        if (item.subquestion_id === '567001') {
          result = `每天中午睡<i>&nbsp;&nbsp;&nbsp;&nbsp;</i>小时，`;
        }
        if (item.subquestion_id === '567002') {
          result = `<i>&nbsp;&nbsp;&nbsp;&nbsp;</i>分钟`;
        }
        if (item.subquestion_id === '567003') {
          result = `晚上从<i>&nbsp;&nbsp;&nbsp;&nbsp;</i>睡到`;
        }
        if (item.subquestion_id === '567004') {
          result = `<i>&nbsp;&nbsp;&nbsp;&nbsp;</i>`;
        }
        if (item.subquestion_id === '567005') {
          result = `通常一天内您睡觉累计有<i>&nbsp;&nbsp;&nbsp;&nbsp;</i>小时，`;
        }
        if (item.subquestion_id === '567006') {
          result = `<i>&nbsp;&nbsp;&nbsp;&nbsp;</i>分钟`;
        }
      }

      if (item.subquestion_id === '567001') {
        this.question_list[i].children[j].children[0].value1 = result;
      }
      if (item.subquestion_id === '567002') {
        this.question_list[i].children[j].children[0].value2 = result;
      }
      if (item.subquestion_id === '567003') {
        this.question_list[i].children[j].children[0].value3 = result;
      }
      if (item.subquestion_id === '567004') {
        this.question_list[i].children[j].children[0].value4 = result;
      }
      if (item.subquestion_id === '567005') {
        this.question_list[i].children[j].children[0].value5 = result;
      }
      if (item.subquestion_id === '567006') {
        this.question_list[i].children[j].children[0].value6 = result;
      }

      const value1 = this.question_list[i].children[j].children[0].value1;
      const value2 = this.question_list[i].children[j].children[0].value2;
      const value3 = this.question_list[i].children[j].children[0].value3;
      const value4 = this.question_list[i].children[j].children[0].value4;
      const value5 = this.question_list[i].children[j].children[0].value5;
      const value6 = this.question_list[i].children[j].children[0].value6;

      if(type){
        if (value5 && value6) {
          this.question_list[i].children[j].children[3].value = `<span>${value5 + value6}</span>`;
          this.question_list[i].children[j].children[4].value = null;
        }
      } else {
        if (value1 && value2) {
          this.question_list[i].children[j].children[3].value = `<span>${value1 + value2}</span>`;
        }
        if (value3 && value4) {
          this.question_list[i].children[j].children[4].value = `<span>${value3 + value4}</span>`;
        }
      }
    },
  }
}
</script>

<style scoped lang="scss">
// .life-style {}
</style>
