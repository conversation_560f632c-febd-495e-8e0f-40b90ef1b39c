<template>
  <div class="mmc-questionnaire">
    <div class="tabs">
      <van-tabs class="navbar-main" :ellipsis="false" v-model="active_main" @change="handleMainChange">
        <van-tab v-for="main in navbar_list" :key="main.name" :title="main.title" :name="main.name" />
      </van-tabs>

      <div v-for="(main, index) in navbar_list" :key="main.title">
        <van-tabs class="navbar-sub" :ref="`sub_tab_${index}`" v-show="active_main === index" type="card"
          :ellipsis="false" v-model="active_sub" @click="handleSubChange">
          <van-tab v-for="sub in main.children" :key="sub.name" :title="sub.title" :name="sub.name" />
        </van-tabs>
      </div>
    </div>
    <div class="container">
      <component :is="component" />
    </div>
  </div>
</template>

<script>
export default {
  components: {
    'base-info': () => import('./personal-info/base-info.vue'),
    'symptom': () => import('./history-taking/symptom.vue'),
    'medical-history': () => import('./history-taking/medical-history.vue'),
    'family-history': () => import('./history-taking/family-history.vue'),
    'medication-history': () => import('./history-taking/medication-history.vue'),
    'birth-feeding-history': () => import('./history-taking/birth-feeding-history.vue'),
    'menstrual-obstetrical-history': () => import('./history-taking/menstrual-obstetrical-history.vue'),
    'life-style': () => import('./history-taking/life-style.vue'),
    'adverse-events': () => import('./history-taking/adverse-events.vue'),
    'sugar-tolerance-exam': () => import('./laboratory-collect/sugar-tolerance-exam.vue'),
    'common-biochemistry-exam': () => import('./laboratory-collect/common-biochemistry-exam.vue'),
    'endocrine-related-hormone': () => import('./laboratory-collect/endocrine-related-hormone.vue'),
    'renal-function-exam': () => import('./laboratory-collect/renal-function-exam.vue'),
    'eye-examine': () => import('./assist-examine/eye-examine.vue'),
    'nerve-transmit': () => import('./assist-examine/nerve-transmit.vue'),
    'outside-vascular-exam': () => import('./assist-examine/outside-vascular-exam.vue'),
    'cardiac-exam': () => import('./assist-examine/cardiac-exam.vue'),
    'electrocardiogram': () => import('./assist-examine/electrocardiogram.vue'),
  },
  data() {
    return {
      component: '',
      active_main: 0,
      active_sub: 0,
      navbar_list: [
        {
          title: '基本信息',
          name: 0,
          children: [
            {
              title: '生命体征和人体测量学',
              name: 0,
              component: 'base-info',
            },
          ],
        },
        {
          title: '病史采集',
          name: 1,
          children: [
            {
              title: '症状',
              name: 0,
              component: 'symptom',
            },
            {
              title: '个人病史',
              name: 1,
              component: 'medical-history',
            },
            {
              title: '家族史',
              name: 2,
              component: 'family-history',
            },
            {
              title: '用药史',
              name: 3,
              component: 'medication-history',
            },
            {
              title: '出生喂养史',
              name: 4,
              component: 'birth-feeding-history',
            },
            {
              title: '月经生育史',
              name: 5,
              component: 'menstrual-obstetrical-history',
            },
            {
              title: '生活方式',
              name: 6,
              component: 'life-style',
            },
            {
              title: '不良事件表',
              name: 7,
              component: 'adverse-events',
            },
          ],
        },
        {
          title: '实验室检查',
          name: 2,
          children: [
            {
              title: '糖耐量试验',
              name: 0,
              component: 'sugar-tolerance-exam',
            },
            {
              title: '常规及生化检测',
              name: 1,
              component: 'common-biochemistry-exam',
            },
            {
              title: '内分泌相关激素',
              name: 2,
              component: 'endocrine-related-hormone',
            },
            {
              title: '肾功能检测',
              name: 3,
              component: 'renal-function-exam',
            },
          ],
        },
        {
          title: '辅助检查',
          name: 3,
          children: [
            {
              title: '眼底检查',
              name: 0,
              component: 'eye-examine',
            },
            {
              title: '神经传导',
              name: 1,
              component: 'nerve-transmit',
            },
            {
              title: '外周血管检查',
              name: 2,
              component: 'outside-vascular-exam',
            },
            {
              title: '心脏检查',
              name: 3,
              component: 'cardiac-exam',
            },
            {
              title: '心电图',
              name: 4,
              component: 'electrocardiogram',
            },
          ],
        },
      ],
    };
  },
  beforeRouteEnter(to, from, next) {
    localStorage.setItem('access-token', to.query.access_token);
    next();
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      let { main, sub, sex } = this.$route.query;

      if (main && sub) {
        main *= 1;
        sub *= 1;
      } else {
        main = 2;
        sub = 0;
      }

      this.setNavbarList(sex);

      this.active_main = main;
      this.active_sub = sub;
      this.setComponent(main, sub);

      this.setActiveSubTab(sub);
    },
    setNavbarList(sex) {
      if (sex === '1') {
        this.navbar_list[1].children.splice(4, 1);
      } else if (sex === '0') {
        this.navbar_list[1].children.splice(5, 1);
      }
    },
    handleMainChange(name) {
      this.active_sub = 0;
      this.setComponent(name, 0);

      this.setActiveSubTab(0);
    },
    handleSubChange(name) {
      this.active_sub = name;
      this.setComponent(this.active_main, name);
    },
    setComponent(main, sub) {
      const _sub = this.navbar_list[main].children.findIndex(item => item.name === sub);

      this.component = this.navbar_list[main].children[_sub].component;
    },
    setActiveSubTab(sub) {
      setTimeout(() => {
        this.$nextTick(() => {
          const ref = `sub_tab_${this.active_main}`;
          this.$refs[ref][0].scrollTo(sub);
        })
      }, 100);
    },
  },
};
</script>

<style scoped lang="scss">
.mmc-questionnaire {
  text-align: left;
  > .tabs {
    padding: 6px 15px 15px;
    border-bottom: 1px solid #eee;
    ::v-deep .van-tabs {
      &.navbar-main {
        margin-bottom: 9px;
        .van-tab {
          font-size: 18px;
          color: #3f4447;
        }
        .van-tab--active {
          color: #0a0a0a;
          .van-tab__text {
            font-weight: bold;
          }
        }
        .van-tabs__line {
          background-color: #ee7800;
        }
      }
      &.navbar-sub {
        .van-tabs__nav--card {
          margin: 0;
          padding: 0;
          border: none;
          .van-tab {
            flex: 0 0 auto;
            margin: 0 6px;
            padding: 0 15px;
            border: none;
            border-radius: 15px;
            font-size: 15px;
            color: #3f4447;
            background-color: #eee;
            &.van-tab--active {
              color: #fff;
              background-color: #f7830d;
            }
          }
        }
      }
    }
  }
  > .container {
    // padding: 0 0 30px;
    padding-bottom: 30px;
    ::v-deep .van-collapse {
      .van-cell__title {
        font-size: 17px;
        color: #333;
      }
      .van-collapse-item__wrapper {
        background-color: #fcfcfc;
        .van-collapse-item__content {
          padding: 0 15px;
          background-color: #fcfcfc;
        }
      }
      &.van-hairline--top-bottom::after
      // &.van-hairline-unset--top-bottom::after
      {
        border: none;
      }
    }
  }
  ::v-deep .mmc-questionnaire-loading-overlay {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(0, 0, 0, 0);
  }
}
</style>