<template>
  <div class="outside-vascular-exam">
    <comp-questionnaire-list :list="question_list.slice(0, 1)" />
    <van-overlay :show="show_loading" class-name="mmc-questionnaire-loading-overlay">
      <van-loading type="spinner" />
    </van-overlay>
  </div>
</template>

<script>
import api from '@/api/mmc-questionnaire.js';
import compQuestionnaireList from '../components/comp-questionnaire-list.vue';
import mixin from '../common/mixins/mixin';
export default {
  data() {
    return {
      show_loading: false,
      question_list: [
        {
          id: 1,
          composing: 'block',
          title: null,
          children: [
            {
              question_id: '1',
              subquestion_id: '',
              subquestion_name: '1、颈动脉超声',
              show: true,
              table_name: 'carotid-ultrasound',
              data: [
                { title: '右颈总', first: '', second: '' },
                { title: '右颈内', first: '', second: '' },
                { title: '左颈总', first: '', second: '' },
                { title: '左颈内', first: '', second: '' },
              ],
              textareavalue: null,
            },
            {
              question_id: '',
              subquestion_id: '',
              subquestion_name: '2、PWV+ABI+TBI',
              show: true,
              table_name: 'PWV-ABI-TBI',
              data: [
                { title: '左踝臂指数（LABI）', first: '' },
                { title: '右踝臂指数（RABI）', first: '' },
                { title: '左肱踝脉搏波传导速度（LBAPWV）', first: '' },
                { title: '右肱踝脉搏波传导速度（RBAPWV）', first: '' },
              ],
            },
          ],
        },
      ],
    };
  },
  components: {
    compQuestionnaireList,
  },
  mixins: [mixin],
  created() {
    this.getSymptomData();
  },
  methods: {
    async getSymptomData() {
      const { user_id, visit_level, sex, hosp_id } = this.$route.query;

      this.show_loading = true;
      
      let res = await api.qssInfo({
        user_id,
        visit_level,
        template_id: 47,
        sex,
        hosp_id,
      }).finally(() => {
        this.show_loading = false;
      });
      if (res.status === 0) {
        const list = res.data.question_data || [];
        this.setQuestionList(list);
      }
    },
    // 设置题目
    setQuestionList(list) {
      if (list.length > 0) {
        list.forEach((item) => {
          // 设置表格
          switch (item.subquestion_id) {
            case '925001':
              this.setTableValue(item, 0, 0, 0, 'first');
              break;
            case '925004':
              this.setTableValue(item, 0, 0, 0, 'second');
              break;
            case '925005':
              this.setTableValue(item, 0, 0, 1, 'first');
              break;
            case '925008':
              this.setTableValue(item, 0, 0, 1, 'second');
              break;
            case '925009':
              this.setTableValue(item, 0, 0, 2, 'first');
              break;
            case '925012':
              this.setTableValue(item, 0, 0, 2, 'second');
              break;
            case '925013':
              this.setTableValue(item, 0, 0, 3, 'first');
              break;
            case '925016':
              this.setTableValue(item, 0, 0, 3, 'second');
              break;
            case '925017':
              this.setTextareaValue(item, 0, 0);
              break;
            case '929001':
              this.setTableValue(item, 0, 1, 0, 'first');
              break;
            case '929002':
              this.setTableValue(item, 0, 1, 1, 'first');
              break;
            case '929003':
              this.setTableValue(item, 0, 1, 2, 'first');
              break;
            case '929004':
              this.setTableValue(item, 0, 1, 3, 'first');
              break;
            default:
              break;
          }
        });
      }
    },
    setTableValue(item, i, j, k, h) {
      const answer = this.checkAnswerValue(item);
      if (answer) {
        this.question_list[i].children[j].data[k][h] = answer.answer_value;
      }
    },
    setTextareaValue(item, i, j) {
      const answer = this.checkAnswerValue(item);
      if (answer) {
        this.question_list[i].children[j].textareavalue =
          '最后结论:' + answer.answer_value.replace(/[\n\r]/g, '<br/>');
      } else {
        this.question_list[i].children[j].textareavalue = '最后结论:暂无数据';
      }
    },
  },
};
</script>

<style scoped lang="scss">
.outside-vascular-exam {
  padding: 0 15px;
}
</style>