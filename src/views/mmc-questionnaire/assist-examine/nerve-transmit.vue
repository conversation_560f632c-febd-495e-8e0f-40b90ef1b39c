<template>
  <div class="nerve-transmit">
    <comp-questionnaire-list :list="question_list.slice(0, 1)" />
    <van-overlay :show="show_loading" class-name="mmc-questionnaire-loading-overlay">
      <van-loading type="spinner" />
    </van-overlay>
  </div>
</template>

<script>
import api from '@/api/mmc-questionnaire.js';
import compQuestionnaireList from '../components/comp-questionnaire-list.vue';
import mixin from '../common/mixins/mixin';
export default {
  data() {
    return {
      show_loading: false,
      question_list: [
        {
          id: 1,
          type: 'checkbox',
          composing: 'block',
          title: null,
          children: [
            {
              subquestion_id: '904033',
              subquestion_name: '1、最后结论',
              show: true,
              inline_check: null,
              checked: null,
              datetime: null,
              textarea: true,
              textareavalue: null,
            },
          ],
        },
      ],
    };
  },
  components: {
    compQuestionnaireList,
  },
  mixins: [mixin],
  created() {
    this.getSymptomData();
  },
  methods: {
    async getSymptomData() {
      const { user_id, visit_level, sex, hosp_id } = this.$route.query;

      this.show_loading = true;
      
      let res = await api.qssInfo({
        user_id,
        visit_level,
        template_id: 38,
        sex,
        hosp_id,
      }).finally(() => {
        this.show_loading = false;
      });
      if (res.status === 0) {
        const list = res.data.question_data || [];
        this.setQuestionList(list);
      }
    },
    // 设置题目
    setQuestionList(list) {
      if (list.length > 0) {
        list.forEach((item) => {
          // 设置第 3 题
          if (item.subquestion_id === '904033') {
            this.setTextareaValue(item, 0, 0);
          }
        });
      }
    },
    setTextareaValue(item, i, j) {
      const answer = this.checkAnswerValue(item);
      if (answer) {
        this.question_list[i].children[j].textareavalue =
          answer.answer_value.replace(/[\n\r]/g, '<br/>');
      } else {
        this.question_list[i].children[j].textareavalue = '暂无数据';
      }
    },
  },
};
</script>

<style scoped lang="scss">
.nerve-transmit {
  padding: 0 15px;
}
</style>