<template>
  <div class="electrocardiogram">
    <comp-questionnaire-list :list="question_list.slice(0, 1)" />
    <van-overlay :show="show_loading" class-name="mmc-questionnaire-loading-overlay">
      <van-loading type="spinner" />
    </van-overlay>
  </div>
</template>

<script>
import api from '@/api/mmc-questionnaire.js';
import compQuestionnaireList from '../components/comp-questionnaire-list.vue';
import mixin from '../common/mixins/mixin';
export default {
  data() {
    return {
      show_loading: false,
      question_list: [
        {
          id: 1,
          type: 'checkbox',
          composing: 'block',
          title: null,
          children: [
            {
              subquestion_id: '952',
              subquestion_name: '1、心律',
              show: true,
              inline_check: null,
              checked: null,
              datetime: null,
              children: [
                {
                  option_id: '52803',
                  option_name: '窦性',
                  checked: false,
                },
                {
                  option_id: '52804',
                  option_name: '房性',
                  checked: false,
                },
                {
                  option_id: '52805',
                  option_name: '室性',
                  checked: false,
                },
                {
                  option_id: '52806',
                  option_name: '交界性',
                  checked: false,
                },
                {
                  option_id: null,
                  option_name: null,
                  checked: false,
                },
              ],
            },
            {
              subquestion_id: '954',
              subquestion_name: '2、心电图诊断:',
              show: true,
              inline_check: null,
              checked: null,
              datetime: null,
              textarea: true,
              textareavalue: null,
            },
          ],
        },
      ],
    };
  },
  components: {
    compQuestionnaireList,
  },
  mixins: [mixin],
  created() {
    this.getSymptomData();
  },
  methods: {
    async getSymptomData() {
      const { user_id, visit_level, sex, hosp_id } = this.$route.query;

      this.show_loading = true;

      let res = await api.qssInfo({
        user_id,
        visit_level,
        template_id: 53,
        sex,
        hosp_id,
      }).finally(() => {
        this.show_loading = false;
      });
      if (res.status === 0) {
        const list = res.data.question_data || [];
        this.setQuestionList(list);
      }
    },
    // 设置题目
    setQuestionList(list) {
      if (list.length > 0) {
        list.forEach((item) => {
          // 设置第 1 题
          if (item.subquestion_id === '952') {
            const questions = ['52803', '52804', '52805', '52806'];
            this.setQuestionCheckedStatus(item, questions, 0, 0);
          }
          if (item.subquestion_id === '952001') {
            const answer = this.checkAnswerValue(item);
            if (answer) {
              this.setInputValue(`心率 ${answer.answer_value} 次／分`, 0, 0, 4);
            }
          }
          // 设置第 2 题
          if (item.subquestion_id === '954') {
            this.setTextareaValue(item, 0, 1);
          }
        });
      }
    },
    setInputValue(item, i, j, k) {
      this.question_list[i].children[j].children[k].value = item;
    },
    setTextareaValue(item, i, j) {
      const answer = this.checkAnswerValue(item);
      if (answer) {
        this.question_list[i].children[j].textareavalue =
          answer.answer_value.replace(/[\n\r]/g, '<br/>');
      } else {
        this.question_list[i].children[j].textareavalue = '暂无数据';
      }
    },
  },
};
</script>

<style scoped lang="scss">
.electrocardiogram {
  padding: 0 15px;
}
</style>