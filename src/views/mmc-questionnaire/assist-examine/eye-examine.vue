<template>
  <div class="eye-examine">
    <comp-questionnaire-list :list="question_list.slice(0, 1)" />
    <van-overlay :show="show_loading" class-name="mmc-questionnaire-loading-overlay">
      <van-loading type="spinner" />
    </van-overlay>
  </div>
</template>

<script>
import api from '@/api/mmc-questionnaire.js';
import compQuestionnaireList from '../components/comp-questionnaire-list.vue';
import mixin from '../common/mixins/mixin';
export default {
  data() {
    return {
      show_loading: false,
      eyeResult: {
        left_img: [],
        result: {},
        right_img: [],
        view_reuslt: {},
      },
      question_list: [
        {
          id: 1,
          type: 'checkbox',
          composing: 'block',
          title: null,
          children: [
            {
              subquestion_id: '945',
              subquestion_name:
                '1、眼底检查（左眼） 此处仅填写有资质的医生出具的结果（不是AI的结果）',
              show: true,
              inline_check: null,
              checked: null,
              datetime: null,
              children: [
                {
                  option_id: '52781',
                  option_name: '无明显视网膜病变',
                  checked: false,
                },
                {
                  option_id: '52782',
                  option_name: '轻度非增殖期视网膜病变',
                  checked: false,
                },
                {
                  option_id: '52783',
                  option_name: '中度非增殖期视网膜病变',
                  checked: false,
                },
                {
                  option_id: '52784',
                  option_name: '重度非增殖期视网膜病变',
                  checked: false,
                },
                {
                  option_id: '52785',
                  option_name: '增殖期视网膜病变',
                  checked: false,
                },
                {
                  option_id: '52870',
                  option_name: '无法判断',
                  checked: false,
                },
                {
                  option_id: null,
                  option_name: null,
                  checked: false,
                },
                {
                  option_id: null,
                  option_name: null,
                  checked: false,
                },
              ],
            },
            {
              subquestion_id: '946',
              subquestion_name:
                '2、眼底检查（右眼） 此处仅填写有资质的医生出具的结果（不是AI的结果）',
              show: true,
              inline_check: null,
              checked: null,
              datetime: null,
              children: [
                {
                  option_id: '52788',
                  option_name: '无明显视网膜病变',
                  checked: false,
                },
                {
                  option_id: '52789',
                  option_name: '轻度非增殖期视网膜病变',
                  checked: false,
                },
                {
                  option_id: '52790',
                  option_name: '中度非增殖期视网膜病变',
                  checked: false,
                },
                {
                  option_id: '52791',
                  option_name: '重度非增殖期视网膜病变',
                  checked: false,
                },
                {
                  option_id: '52792',
                  option_name: '增殖期视网膜病变',
                  checked: false,
                },
                {
                  option_id: '52871',
                  option_name: '无法判断',
                  checked: false,
                },
                {
                  option_id: null,
                  option_name: null,
                  checked: false,
                },
                {
                  option_id: null,
                  option_name: null,
                  checked: false,
                },
              ],
            },
            {
              subquestion_id: '948',
              subquestion_name:
                '3、眼底检测描述 此处仅填写有资质的医生出具的结果（不是AI的结果）',
              show: true,
              inline_check: null,
              checked: null,
              datetime: null,
              textarea: true,
              textareavalue: null,
            },
            {
              subquestion_id: '948',
              subquestion_name: '4、眼底图片：',
              show: true,
              inline_check: null,
              checked: null,
              datetime: null,
              img: true,
              leftimglist: [],
              rightimglist: [],
              result: {},
            },
          ],
        },
      ],
    };
  },
  components: {
    compQuestionnaireList,
  },
  mixins: [mixin],
  created() {
    this.geteEyeResult();
    // this.getSymptomData();
  },
  methods: {
    async geteEyeResult() {
      const { user_id, visit_level, record_id } = this.$route.query;
      let res = await api.eyeResult({
        user_id,
        visit_level,
        record_id,
      });
      if (res.status === 0) {
        this.eyeResult = res.data;
        this.getSymptomData();
      }
    },
    async getSymptomData() {
      const { user_id, visit_level, sex, hosp_id } = this.$route.query;

      this.show_loading = true;
      
      let res = await api.qssInfo({
        user_id,
        visit_level,
        template_id: 37,
        sex,
        hosp_id,
      }).finally(() => {
        this.show_loading = false;
      });
      if (res.status === 0) {
        const list = res.data.question_data || [];
        this.setQuestionList(list);
      }
    },
    // 设置题目
    setQuestionList(list) {
      if (list.length > 0) {
        list.forEach((item) => {
          // 设置第 1 题
          if (item.subquestion_id === '945') {
            const questions = [
              '52781',
              '52782',
              '52783',
              '52784',
              '52785',
              '52870',
            ];
            this.setQuestionCheckedStatus(item, questions, 0, 0);
            if (this.eyeResult.result.left_eye_dignosis) {
              this.setInputValue(
                `AI分析结果（左眼）：<i>${this.eyeResult.result.left_eye_dignosis}</i>本结果仅供参考`,
                0,
                0,
                6
              );
            } else {
              this.setInputValue(`AI分析结果（左眼）：`, 0, 0, 6);
            }
            if (this.eyeResult.view_reuslt.left_trans_result) {
              this.setInputValue(
                `人工阅片评估结果（左眼）：<i>${this.eyeResult.view_reuslt.left_trans_result}</i>`,
                0,
                0,
                7
              );
            }
          }
          // 设置第 2 题
          if (item.subquestion_id === '946') {
            const questions = [
              '52788',
              '52789',
              '52790',
              '52791',
              '52792',
              '52871',
            ];
            this.setQuestionCheckedStatus(item, questions, 0, 1);
            if (this.eyeResult.result.right_eye_dignosis) {
              this.setInputValue(
                `AI分析结果（右眼）：<i>${this.eyeResult.result.right_eye_dignosis}</i>本结果仅供参考`,
                0,
                1,
                6
              );
            } else {
              this.setInputValue(`AI分析结果（右眼）：`, 0, 1, 6);
            }
            if (this.eyeResult.view_reuslt.right_trans_result) {
              this.setInputValue(
                `人工阅片评估结果（右眼）：<i>${this.eyeResult.view_reuslt.right_trans_result}</i>`,
                0,
                1,
                7
              );
            }
          }
          // 设置第 3 题
          if (item.subquestion_id === '948') {
            this.setTextareaValue(item, 0, 2);
            this.question_list[0].children[3].leftimglist =
              this.eyeResult.left_img;
            this.question_list[0].children[3].rightimglist =
              this.eyeResult.right_img;
            this.question_list[0].children[3].result = this.eyeResult.result;
          }
        });
      }
    },
    setInputValue(item, i, j, k) {
      this.question_list[i].children[j].children[
        k
      ].value = `<span>${item}</span>`;
    },
    setTextareaValue(item, i, j) {
      const answer = this.checkAnswerValue(item);
      if (answer) {
        this.question_list[i].children[j].textareavalue =
          answer.answer_value.replace(/[\n\r]/g, '<br/>');
      } else {
        this.question_list[i].children[j].textareavalue = '暂无数据';
      }
    },
  },
};
</script>

<style scoped lang="scss">
.eye-examine {
  padding: 0 15px;
}
</style>