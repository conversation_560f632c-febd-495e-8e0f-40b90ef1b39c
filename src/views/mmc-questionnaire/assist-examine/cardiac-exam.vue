<template>
  <div class="cardiac-exam">
    <comp-questionnaire-list :list="question_list.slice(0, 1)" />
    <van-overlay :show="show_loading" class-name="mmc-questionnaire-loading-overlay">
      <van-loading type="spinner" />
    </van-overlay>
  </div>
</template>

<script>
import api from '@/api/mmc-questionnaire.js';
import compQuestionnaireList from '../components/comp-questionnaire-list.vue';
import mixin from '../common/mixins/mixin';
export default {
  data() {
    return {
      show_loading: false,
      question_list: [
        {
          id: 1,
          composing: 'block',
          title: null,
          children: [
            {
              question_id: '1',
              subquestion_id: '',
              subquestion_name: '1、心超',
              show: true,
              table_name: 'cardiac-exam',
              data: [
                { title: '左房内径', first: '' },
                { title: '室间隔厚度', first: '' },
                { title: '左室舒张末期内径', first: '' },
                { title: '左室后壁厚度', first: '' },
                { title: 'LVEF', first: '' },
                { title: '左室收缩末期内径', first: '' },
                { title: '主动脉根部内径', first: '' },
              ],
              textareavalue: null,
            },
          ],
        },
      ],
    };
  },
  components: {
    compQuestionnaireList,
  },
  mixins: [mixin],
  created() {
    this.getSymptomData();
  },
  methods: {
    async getSymptomData() {
      const { user_id, visit_level, sex, hosp_id } = this.$route.query;

      this.show_loading = true;

      let res = await api.qssInfo({
        user_id,
        visit_level,
        template_id: 48,
        sex,
        hosp_id,
      }).finally(() => {
        this.show_loading = false;
      });
      if (res.status === 0) {
        const list = res.data.question_data || [];
        this.setQuestionList(list);
      }
    },
    // 设置题目
    setQuestionList(list) {
      if (list.length > 0) {
        list.forEach((item) => {
          // 设置表格
          switch (item.subquestion_id) {
            case '930001':
              console.log(1);
              this.setTableValue(item, 0, 0, 0, 'mm');
              break;
            case '930002':
              this.setTableValue(item, 0, 0, 1, 'mm');
              break;
            case '930003':
              this.setTableValue(item, 0, 0, 2, 'mm');
              break;
            case '930004':
              this.setTableValue(item, 0, 0, 3, 'mm');
              break;
            case '930005':
              this.setTableValue(item, 0, 0, 4, '%');
              break;
            case '930006':
              this.setTableValue(item, 0, 0, 5, 'mm');
              break;
            case '930007':
              this.setTableValue(item, 0, 0, 6, 'mm');
              break;
            case '930008':
              this.setTextareaValue(item, 0, 0);
              break;
            default:
              break;
          }
        });
      }
    },
    setTableValue(item, i, j, k, h) {
      const answer = this.checkAnswerValue(item);
      if (answer) {
        this.question_list[i].children[j].data[k].first =
          answer.answer_value + h;
      }
    },
    setTextareaValue(item, i, j) {
      const answer = this.checkAnswerValue(item);
      if (answer) {
        this.question_list[i].children[j].textareavalue =
          '心超最后结论:' + answer.answer_value.replace(/[\n\r]/g, '<br/>');
      } else {
        this.question_list[i].children[j].textareavalue = '心超最后结论:暂无数据';
      }
    },
  },
};
</script>

<style scoped lang="scss">
.cardiac-exam {
  padding: 0 15px;
}
</style>