<template>
  <div class="base-info">
    <div class="container">
      <div>
        <comp-questionnaire-list :list="question_list" />
      </div>
    </div>

    <van-overlay :show="show_loading" class-name="mmc-questionnaire-loading-overlay">
      <van-loading type="spinner" />
    </van-overlay>
  </div>
</template>

<script>
import api from '@/api/mmc-questionnaire.js';
import compQuestionnaireList from '../components/comp-questionnaire-list.vue';
import mixin from '../common/mixins/mixin';

export default {
  name: 'baseInfo',
  components: {
    compQuestionnaireList,
  },
  data() {
    return {
      show_loading: false,
      question_list: [
        {
          id: 1,
          composing: 'block',
          title: null,
          children: [
            {
              subquestion_id: null,
              subquestion_name: '1、血压#收缩压#舒张压mmHg',
              subquestion_name_format: [
                {
                  key: 'normal',
                  value: '1、血压',
                },
                {
                  key: 'input',
                  value: '',
                },
                {
                  key: 'normal',
                  value: ' ',
                },
                {
                  key: 'input',
                  value: '',
                },
                {
                  key: 'normal',
                  value: 'mmHg',
                },
              ],
              show: true,
              inline_check: null,
              inline_input: true,
              checked: null,
              datetime_key: null,
              datetime_value: null,
            },
            {
              subquestion_id: null,
              subquestion_name: '心率#num bpm',
              subquestion_name_format: [
                {
                  key: 'normal',
                  value: '2、心率',
                },
                {
                  key: 'input',
                  value: '',
                },
                {
                  key: 'normal',
                  value: 'bpm',
                },
              ],
              show: true,
              inline_check: null,
              inline_input: true,
              checked: null,
              datetime_key: null,
              datetime_value: null,
            },
            {
              subquestion_id: null,
              subquestion_name: '身高#num cm',
              subquestion_name_format: [
                {
                  key: 'normal',
                  value: '3、身高',
                },
                {
                  key: 'input',
                  value: '',
                },
                {
                  key: 'normal',
                  value: 'cm',
                },
              ],
              show: true,
              inline_check: null,
              inline_input: true,
              checked: null,
              datetime_key: null,
              datetime_value: null,
            },
            {
              subquestion_id: null,
              subquestion_name: '体重#num kg',
              subquestion_name_format: [
                {
                  key: 'normal',
                  value: '4、体重',
                },
                {
                  key: 'input',
                  value: '',
                },
                {
                  key: 'normal',
                  value: 'kg',
                },
              ],
              show: true,
              inline_check: null,
              inline_input: true,
              checked: null,
              datetime_key: null,
              datetime_value: null,
            },
            {
              subquestion_id: null,
              subquestion_name: '体质指数BMI#num kg/m²',
              subquestion_name_format: [
                {
                  key: 'normal',
                  value: '5、体质指数BMI',
                },
                {
                  key: 'input',
                  value: '',
                },
                {
                  key: 'normal',
                  value: 'kg/m²',
                },
              ],
              show: true,
              inline_check: null,
              inline_input: true,
              checked: null,
              datetime_key: null,
              datetime_value: null,
            },
            {
              subquestion_id: null,
              subquestion_name: '头围#num cm',
              subquestion_name_format: [
                {
                  key: 'normal',
                  value: '6、头围',
                },
                {
                  key: 'input',
                  value: '',
                },
                {
                  key: 'normal',
                  value: 'cm',
                },
              ],
              show: true,
              inline_check: null,
              inline_input: true,
              checked: null,
              datetime_key: null,
              datetime_value: null,
            },
            {
              subquestion_id: null,
              subquestion_name: '颈围#num cm',
              subquestion_name_format: [
                {
                  key: 'normal',
                  value: '7、颈围',
                },
                {
                  key: 'input',
                  value: '',
                },
                {
                  key: 'normal',
                  value: 'cm',
                },
              ],
              show: true,
              inline_check: null,
              inline_input: true,
              checked: null,
              datetime_key: null,
              datetime_value: null,
            },
            {
              subquestion_id: null,
              subquestion_name: '腰围#num cm',
              subquestion_name_format: [
                {
                  key: 'normal',
                  value: '8、腰围',
                },
                {
                  key: 'input',
                  value: '',
                },
                {
                  key: 'normal',
                  value: 'cm',
                },
              ],
              show: true,
              inline_check: null,
              inline_input: true,
              checked: null,
              datetime_key: null,
              datetime_value: null,
            },
            {
              subquestion_id: null,
              subquestion_name: '臀围#num cm',
              subquestion_name_format: [
                {
                  key: 'normal',
                  value: '9、臀围',
                },
                {
                  key: 'input',
                  value: '',
                },
                {
                  key: 'normal',
                  value: 'cm',
                },
              ],
              show: true,
              inline_check: null,
              inline_input: true,
              checked: null,
              datetime_key: null,
              datetime_value: null,
            },
            {
              subquestion_id: null,
              subquestion_name: '腹部内脂肪面积（VFA）#num cm²',
              subquestion_name_format: [
                {
                  key: 'normal',
                  value: '10、腹部内脂肪面积（VFA）',
                },
                {
                  key: 'input',
                  value: '',
                },
                {
                  key: 'normal',
                  value: 'cm²',
                },
              ],
              show: true,
              inline_check: null,
              inline_input: true,
              checked: null,
              datetime_key: null,
              datetime_value: null,
            },
            {
              subquestion_id: null,
              subquestion_name: '皮下脂肪面积（SFA）#num cm²',
              subquestion_name_format: [
                {
                  key: 'normal',
                  value: '11、皮下脂肪面积（SFA）',
                },
                {
                  key: 'input',
                  value: '',
                },
                {
                  key: 'normal',
                  value: 'cm²',
                },
              ],
              show: true,
              inline_check: null,
              inline_input: true,
              checked: null,
              datetime_key: null,
              datetime_value: null,
            },
          ],
        },
      ],
    };
  },
  mixins: [mixin],
  created() {
    this.getSymptomData();
  },
  methods: {
    async getSymptomData() {
      const { user_id, hosp_id, visit_level } = this.$route.query;

      this.show_loading = true;

      let res = await api.answerSign({
        user_id,
        hosp_id,
        visit_level,
      }).finally(() => {
        this.show_loading = false;
      });

      if (res.status === 0) {
        const object = res.data || [];
        this.setQuestionList(object);
      }
    },
    // 设置题目
    setQuestionList(object) {
      for (const i in object) {
        if (i === 'sbp') {
          this.setTitleInput(object[i], 0, 0, 1);
        }
        if (i === 'dbp') {
          this.setTitleInput(object[i], 0, 0, 3);
        }
        if (i === 'pulse') {
          this.setTitleInput(object[i], 0, 1, 1);
        }
        if (i === 'height') {
          this.setTitleInput(object[i], 0, 2, 1, 'fixedOne');
        }
        if (i === 'weight') {
          this.setTitleInput(object[i], 0, 3, 1, 'fixedOne');
        }
        if (i === 'bmi') {
          this.setTitleInput(object[i], 0, 4, 1, 'fixedOne');
        }
        if (i === 'head_circumference') {
          this.setTitleInput(object[i], 0, 5, 1);
        }
        if (i === 'neck_circumference') {
          this.setTitleInput(object[i], 0, 6, 1);
        }
        if (i === 'waistline') {
          this.setTitleInput(object[i], 0, 7, 1);
        }
        if (i === 'hips') {
          this.setTitleInput(object[i], 0, 8, 1);
        }
        if (i === 'vat') {
          this.setTitleInput(object[i], 0, 9, 1);
        }
        if (i === 'sat') {
          this.setTitleInput(object[i], 0, 10, 1);
        }
      }
    },
    // 设置标题填空题
    setTitleInput(item, i, j, k, h) {
      this.question_list[i].children[j].subquestion_name_format[k].value =
        this.formatVal(item, h);
    },
    // 身高体重bmi  四舍五入保留一位小数，展示
    formatVal(val, valFixedOne) {
      let result = val;
      if (val === 0 || val === null) {
        result = '';
      } else {
        if (valFixedOne === 'fixedOne') {
          result = (Math.round(Number(val) * 10) / 10).toFixed(1);
        }
      }
      return result;
    },
  },
};
</script>

<style scoped lang="scss">
.base-info {
  padding: 0 15px;
}
</style>
