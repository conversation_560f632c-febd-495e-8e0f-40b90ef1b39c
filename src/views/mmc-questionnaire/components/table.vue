<template>
  <div class="mmc-questionnaire-table" v-if="data.table_name">
    <!-- 病史采集-个人病史-疾病史 -->
    <div class="table-medical-history" v-if="data.table_name === 'medical-history'">
      <table aria-label="table">
        <thead>
          <tr>
            <th v-for="val in data.data.thead" :key="val"><div class="cell">{{ val }}</div></th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="tr in data.data.tbody" :key="tr">
            <td v-for="val in tr" :key="val"><div class="cell">{{ val }}</div></td>
          </tr>
        </tbody>
      </table>
    </div>
    <!-- 病史采集-家族史-肿瘤和糖尿病 -->
    <div class="table-family-history-cancer" v-if="data.table_name === 'family-history-cancer'">
      <table aria-label="table">
        <thead>
          <tr>
            <th colspan="6"><div class="cell">肿瘤家族史:</div></th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td><div class="cell"></div></td>
            <td><div class="cell">是否患病</div></td>
            <td><div class="cell">患者人数</div></td>
            <td><div class="cell">部位1</div></td>
            <td><div class="cell">部位2</div></td>
            <td><div class="cell">其他</div></td>
          </tr>
          <tr v-for="tr in data.data.tbody" :key="tr">
            <td v-for="val in tr" :key="val" :class="val === 'disabled' ? 'disabled' : ''"><div class="cell">{{ val | filterFamilyHistoryCancerValue }}</div></td>
          </tr>
        </tbody>
      </table>
    </div>
    <div class="table-family-history-diabetes" v-if="data.table_name === 'family-history-diabetes'">
      <table aria-label="table">
        <thead>
          <tr>
            <th colspan="3"><div class="cell">糖尿病家族史:</div></th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td><div class="cell"></div></td>
            <td><div class="cell">是否患病</div></td>
            <td><div class="cell">患者人数</div></td>
          </tr>
          <tr v-for="tr in data.data.tbody" :key="tr">
            <td v-for="val in tr" :key="val" :class="val === 'disabled' ? 'disabled' : ''"><div class="cell">{{ val | filterFamilyHistoryCancerValue }}</div></td>
          </tr>
        </tbody>
      </table>
    </div>
    <!-- 病史采集-用药史-降糖药物 -->
    <div class="table-medication-history-hypoglycemic" v-if="data.table_name === 'medication-history-hypoglycemic'">
      <button @click="handleOpenHypoglycemic" class="btn">{{ medication_history_hypoglycemic_options[medication_history_hypoglycemic_index].name }}<van-icon name="arrow-down" /></button>
      <van-action-sheet v-model="medication_history_hypoglycemic_show" :actions="medication_history_hypoglycemic_options" @select="handleSelectHypoglycemic" />
      <table aria-label="table">
        <thead>
          <tr>
            <th><div class="cell">药物名称</div></th>
            <th><div class="cell">类型</div></th>
            <th><div class="cell">每日总剂量</div></th>
            <th><div class="cell">用法用量</div></th>
            <th><div class="cell">开始日期</div></th>
            <th><div class="cell">最新调整日期</div></th>
            <th><div class="cell">结束日期</div></th>
            <th><div class="cell">正在使用</div></th>
            <th><div class="cell">备注</div></th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="item in data.data.list" :key="item">
            <td>
              <div class="cell">
                <span>{{ item.general_name }}</span>
              </div>
            </td>
            <td><div class="cell">{{ item.type_name }}</div></td>
            <td>
              <div class="cell">
                <span v-if="item.drug_dose > 0">{{ item.drug_dose }}{{ item.unit }}<span v-if="item.drug_times > 0"> ({{ item.drug_times }} 次)</span></span>
                <span v-if="item.week_dose > 0">每周 {{ item.week_dose }}{{ item.unit }}<span v-if="item.drug_times > 0"> ({{ item.drug_times }} 次)</span></span>
                <span v-if="item.month_dose > 0">每月 {{ item.month_dose }}{{ item.unit }}<span v-if="item.drug_times > 0"> ({{ item.drug_times }} 次)</span></span>
              </div>
            </td>
            <td>
              <div class="cell space-around">
                <span v-if="item.usage_dosage_morning > 0">早 {{ item.usage_dosage_morning + item.unit }}</span>
                <br v-if="item.usage_dosage_morning > 0" />
                <span v-if="item.usage_dosage_noon > 0">中 {{ item.usage_dosage_noon + item.unit }}</span>
                <br v-if="item.usage_dosage_noon > 0">
                <span v-if="item.usage_dosage_night > 0">晚 {{ item.usage_dosage_night + item.unit }}</span>
                <br v-if="item.usage_dosage_night > 0">
                <span v-if="item.usage_dosage_bedtime > 0">睡前 {{ item.usage_dosage_bedtime + item.unit }}</span>
                <br v-if="item.usage_dosage_bedtime > 0">
                <span v-if="item.usage_dosage_other !== '' && item.usage_dosage_other[0].dose > 0">
                  {{ item.usage_dosage_other[0].text }}
                  {{ item.usage_dosage_other[0].dose + item.unit }}
                </span>
              </div>
            </td>
            <td>
              <div class="cell">
                <span v-if="item.start_date !== '0000-00-00' && item.start_date !== '0001-01-01'">
                  {{ item.start_date }}
                </span>
              </div>
            </td>
            <td>
              <div class="cell">
                <span v-if="item.adjust_date !== '0000-00-00' && item.adjust_date !== '0001-01-01'">
                  {{ item.adjust_date }}
                </span>
              </div>
            </td>
            <td>
              <div class="cell">
                <span v-if="item.end_date != '0000-00-00' && item.end_date != '0001-01-01'">
                  {{ item.end_date }}
                </span>
              </div>
            </td>
            <td>
              <div class="cell">
                {{ item.is_used == 1 ? '是': '否' }}
              </div>
            </td>
            <td>
              <div class="cell">
                <span class="detailShow" v-if="item.remark">
                  {{ item.remark }}
                </span>
              </div>
            </td>
          </tr>

          <tr v-if="data.data.list.length === 0"><td colspan="9"><div class="cell">暂无数据</div></td></tr>
        </tbody>
      </table>
    </div>
    <!-- 病史采集-用药史-其他药物 -->
    <div class="table-medication-history-hypoglycemic" v-if="data.table_name === 'medication-history-other'">
      <button @click="handleOpenHypoglycemicOther" class="btn">{{ medication_history_hypoglycemic_options[medication_history_other_index].name }}<van-icon name="arrow-down" /></button>
      <van-action-sheet v-model="medication_history_other_show" :actions="medication_history_hypoglycemic_options" @select="handleSelectHypoglycemicOther" />
      <table aria-label="table">
        <thead>
          <tr>
            <th><div class="cell">药物名称</div></th>
            <th><div class="cell">类型</div></th>
            <th><div class="cell">每日总剂量</div></th>
            <th><div class="cell">用法用量</div></th>
            <th><div class="cell">开始日期</div></th>
            <th><div class="cell">最新调整日期</div></th>
            <th><div class="cell">结束日期</div></th>
            <th><div class="cell">正在使用</div></th>
            <th><div class="cell">备注</div></th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="item in data.data.list" :key="item">
            <td>
              <div class="cell">
                <span>{{ item.general_name }}</span>
              </div>
            </td>
            <td><div class="cell">{{ item.type_name }}</div></td>
            <td>
              <div class="cell">
                <span v-if="item.drug_dose > 0">{{ item.drug_dose }}{{ item.unit }}<span v-if="item.drug_times > 0"> ({{ item.drug_times }} 次)</span></span>
                <span v-if="item.week_dose > 0">每周 {{ item.week_dose }}{{ item.unit }}<span v-if="item.drug_times > 0"> ({{ item.drug_times }} 次)</span></span>
                <span v-if="item.month_dose > 0">每月 {{ item.month_dose }}{{ item.unit }}<span v-if="item.drug_times > 0"> ({{ item.drug_times }} 次)</span></span>
              </div>
            </td>
            <td>
              <div class="cell space-around">
                <span v-if="item.usage_dosage_morning > 0">早 {{ item.usage_dosage_morning + item.unit }}</span>
                <br v-if="item.usage_dosage_morning > 0">
                <span v-if="item.usage_dosage_noon > 0">中 {{ item.usage_dosage_noon + item.unit }}</span>
                <br v-if="item.usage_dosage_noon > 0">
                <span v-if="item.usage_dosage_night > 0">晚 {{ item.usage_dosage_night + item.unit }}</span>
                <br v-if="item.usage_dosage_night > 0">
                <span v-if="item.usage_dosage_bedtime > 0">睡前 {{ item.usage_dosage_bedtime + item.unit }}</span>
                <br v-if="item.usage_dosage_bedtime > 0">
                <span v-if="item.usage_dosage_other !== '' && item.usage_dosage_other[0].dose > 0">
                  {{ item.usage_dosage_other[0].text }}
                  {{ item.usage_dosage_other[0].dose + item.unit }}
                </span>
              </div>
            </td>
            <td>
              <div class="cell">
                <span v-if="item.start_date !== '0000-00-00' && item.start_date !== '0001-01-01'">
                  {{ item.start_date }}
                </span>
              </div>
            </td>
            <td>
              <div class="cell">
                <span v-if="item.adjust_date !== '0000-00-00' && item.adjust_date !== '0001-01-01'">
                  {{ item.adjust_date }}
                </span>
              </div>
            </td>
            <td>
              <div class="cell">
                <span v-if="item.end_date != '0000-00-00' && item.end_date != '0001-01-01'">
                  {{ item.end_date }}
                </span>
              </div>
            </td>
            <td>
              <div class="cell">
                {{ item.is_used == 1 ? '是': '否' }}
              </div>
            </td>
            <td>
              <div class="cell">
                <span class="detailShow" v-if="item.remark">
                  {{ item.remark }}
                </span>
              </div>
            </td>
          </tr>

          <tr v-if="data.data.list.length === 0"><td colspan="9"><div class="cell">暂无数据</div></td></tr>
        </tbody>
      </table>
    </div>
    <!-- 病史采集-不良事件表-主要不良事件列表 -->
    <div class="table-adverse-events" v-if="data.table_name === 'adverse-events'">
      <table aria-label="table">
        <thead>
          <tr>
            <th v-for="val in data.data.thead" :key="val"><div class="cell">{{ val }}</div></th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="item in data.data.tbody" :key="item">
            <td><div class="cell">{{ item.type_names }}</div></td>
            <td><div class="cell">{{ item.adverse_date }}</div></td>
            <td><div class="cell">{{ formatAdverseEventsType(item.adverse_flag) }}</div></td>
            <td><div class="cell">{{ item.adverse_flag_date === '0001-01-01' ? '' : item.adverse_flag_date }}</div></td>
            <!-- <td>
              <div class="cell">
                <template v-if="item.event_imgs.length > 0">
                    <span v-for="(it, idx) in item.event_imgs" :key="idx" class="img_div">
                      <img :src="urlStart + '/' + it.id + '?is_thumbnail=0'" class="img" v-if="idx === item.event_imgs.length - 1" alt="" />
                    </span>
                </template>
                <span style="display: inline-block">{{ item.remark.length > 5 ? item.remark.substr(0, 5) + '...' : item.remark }}</span>
              </div>
            </td> -->
            <td><div class="cell">{{ item.created_at.slice(0, 10) }}</div></td>
            <td><div class="cell">{{ item.updated_at.slice(0, 10) }}</div></td>
            <td><div class="cell">{{ item.record_name }}</div></td>
          </tr>

          <tr v-if="data.data.tbody.length === 0"><td colspan="8"><div class="cell">暂无数据</div></td></tr>
        </tbody>
      </table>
    </div>
    <!-- 实验室检查-糖耐量试验-糖耐量试验+胰岛素/C肽释放试验 -->
    <div class="sugar-tolerance-exam" v-if="data.table_name === 'sugar-tolerance-exam'">
      <table aria-label="table">
        <thead>
          <tr>
            <th></th>
            <th><div class="cell">0分钟</div></th>
            <th><div class="cell">120分钟</div></th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="tr in data.data" :key="tr">
            <td><div class="cell">{{ tr.title }}</div></td>
            <td><div class="cell">{{ tr.first }}</div></td>
            <td><div class="cell">{{ tr.second }}</div></td>
          </tr>
        </tbody>
      </table>
    </div>
    <!-- 实验室检查-常规及生化检测-血常规 -->
    <div class="routine-blood-test" v-if="data.table_name === 'routine-blood-test'">
      <table aria-label="table">
        <thead>
          <tr>
            <th><div class="cell">指标</div></th>
            <th><div class="cell">测定值</div></th>
            <th><div class="cell">单位</div></th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="tr in data.data" :key="tr">
            <td><div class="cell">{{ tr.title }}</div></td>
            <td><div class="cell">{{ tr.first }}</div></td>
            <td><div class="cell">{{ tr.second }}</div></td>
          </tr>
        </tbody>
      </table>
    </div>
    <!-- 实验室检查-常规及生化检测-尿常规 -->
    <div class="routine-urine-test" v-if="data.table_name === 'routine-urine-test'">
      <table aria-label="table">
        <thead>
          <tr>
            <th><div class="cell">指标</div></th>
            <th><div class="cell">测定值</div></th>
            <th><div class="cell">单位</div></th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="tr in data.data" :key="tr">
            <td><div class="cell">{{ tr.title }}</div></td>
            <td><div class="cell">{{ tr.first }}</div></td>
            <td><div class="cell">{{ tr.second }}</div></td>
          </tr>
        </tbody>
      </table>
    </div>
    <!-- 实验室检查-常规及生化检测-血生化及血脂 -->
    <div class="blood-biochemistry-lipids" v-if="data.table_name === 'blood-biochemistry-lipids'">
      <table aria-label="table">
        <thead>
          <tr>
            <th><div class="cell">指标</div></th>
            <th><div class="cell">测定值</div></th>
            <th><div class="cell">单位</div></th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="tr in data.data" :key="tr">
            <td><div class="cell">{{ tr.title }}</div></td>
            <td><div class="cell">{{ tr.first }}</div></td>
            <td><div class="cell">{{ tr.second }}</div></td>
          </tr>
        </tbody>
      </table>
    </div>
    <!-- 实验室检查-内分泌相关激素-胰岛素相关抗体 -->
    <div class="insulin-related-antibody" v-if="data.table_name === 'insulin-related-antibody'">
      <table aria-label="table">
        <thead>
          <!-- <tr>
            <th><div class="cell">指标</div></th>
            <th><div class="cell">测定值</div></th>
            <th><div class="cell">单位</div></th>
          </tr> -->
        </thead>
        <tbody>
          <tr v-for="tr in data.data" :key="tr">
            <td><div class="cell">{{ tr.title }}</div></td>
            <td><div class="cell">{{ tr.first }}</div></td>
          </tr>
        </tbody>
      </table>
    </div>
    <!-- 实验室检查-内分泌相关激素-甲状腺功能 -->
    <div class="thyroid-function" v-if="data.table_name === 'thyroid-function'">
      <table aria-label="table">
        <thead>
          <!-- <tr>
            <th><div class="cell">指标</div></th>
            <th><div class="cell">测定值</div></th>
            <th><div class="cell">单位</div></th>
          </tr> -->
        </thead>
        <tbody>
          <tr v-for="tr in data.data" :key="tr">
            <td><div class="cell">{{ tr.title }}</div></td>
            <td><div class="cell">{{ tr.first }}</div></td>
          </tr>
        </tbody>
      </table>
    </div>
    <!-- 实验室检查-内分泌相关激素-甲状腺功能 -->
    <div class="renal-function-test" v-if="data.table_name === 'renal-function-test'">
      <table aria-label="table">
        <thead>
          <!-- <tr>
            <th><div class="cell">指标</div></th>
            <th><div class="cell">测定值</div></th>
            <th><div class="cell">单位</div></th>
          </tr> -->
        </thead>
        <tbody>
          <tr v-for="tr in data.data" :key="tr">
            <td><div class="cell">{{ tr.title }}</div></td>
            <td><div class="cell">{{ tr.first }}</div></td>
          </tr>
        </tbody>
      </table>
    </div>
    <!-- 辅助检查-外周血管检查-颈动脉超声 -->
    <div class="carotid-ultrasound" v-if="data.table_name === 'carotid-ultrasound'">
      <table aria-label="table">
        <thead>
          <tr>
            <th colspan="6"><div class="cell">颈A超声</div></th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td><div class="cell"></div></td>
            <td><div class="cell">IMT 内-中膜厚度（mm）</div></td>
            <td><div class="cell">斑块（mm）</div></td>
          </tr>
          <tr v-for="tr in data.data" :key="tr">
            <td><div class="cell">{{ tr.title }}</div></td>
            <td><div class="cell">{{ tr.first }}</div></td>
            <td><div class="cell">{{ tr.second }}</div></td>
          </tr>
        </tbody>
      </table>
      <div class="textarea-div">
        <span class="textarea" v-html="formatInnerHTML(data.textareavalue)"></span>
      </div>
    </div>
    <!-- 辅助检查-外周血管检查-PWV-ABI-TBI -->
    <div class="PWV-ABI-TBI" v-if="data.table_name === 'PWV-ABI-TBI'">
      <table aria-label="table">
        <thead>
          <tr>
            <th colspan="6"><div class="cell">主要参数</div></th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="tr in data.data" :key="tr">
            <td><div class="cell">{{ tr.title }}</div></td>
            <td><div class="cell">{{ tr.first }}</div></td>
          </tr>
        </tbody>
      </table>
    </div>
    <!-- 辅助检查-外周血管检查-心超 -->
    <div class="cardiac-exam" v-if="data.table_name === 'cardiac-exam'">
      <table aria-label="table">
        <thead>
          <tr>
            <th colspan="6"><div class="cell">心超</div></th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="tr in data.data" :key="tr">
            <td><div class="cell">{{ tr.title }}</div></td>
            <td><div class="cell">{{ tr.first }}</div></td>
          </tr>
        </tbody>
      </table>
      <div class="textarea-div">
        <span class="textarea" v-html="formatInnerHTML(data.textareavalue)"></span>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    data: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      medication_history_hypoglycemic_show: false,
      medication_history_hypoglycemic_index: 0,
      medication_history_other_show: false,
      medication_history_other_index: 0,
      medication_history_hypoglycemic_options: [
        { name: '正在使用的用药', value: 0 },
        { name: '全部用药', value: 1 },
        { name: '已停用药物', value: 2 },
      ],
    }
  },
  filters: {
    filterFamilyHistoryCancerValue(val) {
      return val === 'disabled' ? '-' : val;
    },
  },
  methods: {
    /**
     * 用药史 start
     */
    handleOpenHypoglycemic() {
      this.medication_history_hypoglycemic_show = true;
    },
    handleOpenHypoglycemicOther() {
      this.medication_history_other_show = true;
    },
    handleSelectHypoglycemic(o) {
      this.medication_history_hypoglycemic_show = false;
      this.medication_history_hypoglycemic_index = o.value;
      this.setSelectHypoglycemicList(o);
    },
    handleSelectHypoglycemicOther(o) {
      this.medication_history_other_show = false;
      this.medication_history_other_index = o.value;
      this.setSelectHypoglycemicList(o);      
    },
    setSelectHypoglycemicList(o) {
      if (o.value === 0) {
        this.data.data.list = this.data.data.used || [];
      } else if (o.value === 1) {
        this.data.data.list = this.data.data.all || [];
      } else {
        this.data.data.list = this.data.data.stop || [];
      }
    },
    /**
     * 用药史 end
     */

    /**
     * 不良事件表 start
     */
    formatAdverseEventsType(type) {
      switch(type) {
        case 0:
          return '新增';
        case 1:
          return '仍存在';
        case 2:
          return '已缓解';
        case 3:
          return '已死亡';
        case 4:
          return '不详';
        default:
          return '';
      }
    },
    /**
     * 不良事件表 end
     */
    formatInnerHTML(value) {
      if (value) {
        return value
          .replace(/</g, '&lt;')
          .replace(/&lt;br\/>/g, '<br/>')
          .replace(/&lt;i>/g, '<i>')
          .replace(/&lt;span>/g, '<span>')
          .replace(/&lt;\/i>/g, '</i>')
          .replace(/&lt;\/span>/g, '</span>');
      } else {
        return value;
      }
    },
  }
};
</script>
<style lang="scss" scoped>
@import '../common/styles/common.scss';
.table-family-history-cancer,
.table-family-history-diabetes {
  width: 100%;
  overflow-x: auto;
  tbody tr td {
    .cell {
      min-height: 45px;
    }
    &.disabled {
      background-color: #d1d1d1;
      .cell {
        color: #fff;
      }
    }
  }
}
.table-family-history-cancer {
  table {
    width: 160vw;
    tbody tr td {
      &:nth-of-type(1),
      &:nth-of-type(4),
      &:nth-of-type(5),
      &:nth-of-type(6) {
        width: 17%;
      }
      &:nth-of-type(2),
      &:nth-of-type(3) {
        width: 16%;
      }
    }
  }
}
.table-family-history-diabetes {
  table {
    width: 100%;
    tbody tr td {
      &:nth-of-type(1) {
        width: 30%;
      }
      &:nth-of-type(2),
      &:nth-of-type(3) {
        width: 35%;
      }
    }
  }
}
.table-medication-history-hypoglycemic {
  overflow-x: auto;
  .btn {
    height: 37px;
    padding: 0 12px;
    font-size: 15px;
    color: #3F4447;
    border: 1px solid #C0C6CC;
    border-radius: 6px;
    background-color: #fff;
    i {
      margin-left: 6px;
    }
  }
  table {
    width: 300vw;
    margin-top: 15px;
    tbody tr td {
      &:nth-of-type(1) {
        width: 15%;
      }
      &:nth-of-type(2) {
        width: 12%;
      }
      &:nth-of-type(3) {
        width: 12%;
      }
      &:nth-of-type(4) {
        width: 12%;
      }
      &:nth-of-type(5) {
        width: 11%;
      }
      &:nth-of-type(6) {
        width: 11%;
      }
      &:nth-of-type(7) {
        width: 11%;
      }
      &:nth-of-type(8) {
        width: 8%;
      }
      &:nth-of-type(9) {
        width: 8%;
      }
    }
  }
}
.table-adverse-events {
  overflow-x: auto;
  table {
    width: 220vw;
  }
}
.sugar-tolerance-exam{
  table {
    width: 100%;
    tbody tr td {
      &:nth-of-type(1),
      &:nth-of-type(2),
      &:nth-of-type(3) {
        width: 33%;
      }
    }
  }
}
.routine-blood-test{
  table {
    width: 100%;
    tbody tr td {
      &:nth-of-type(1){
        width: 40%;
      }
      &:nth-of-type(2),
      &:nth-of-type(3) {
        width: 30%;
      }
    }
  }
}
.routine-urine-test{
  table {
    width: 100%;
    tbody tr td {
      &:nth-of-type(1){
        width: 40%;
      }
      &:nth-of-type(2),
      &:nth-of-type(3) {
        width: 30%;
      }
    }
  }
}
.blood-biochemistry-lipids{
  table {
    width: 100%;
    tbody tr td {
      &:nth-of-type(1){
        width: 40%;
      }
      &:nth-of-type(2),
      &:nth-of-type(3) {
        width: 30%;
      }
    }
  }
}
.insulin-related-antibody{
  table {
    width: 100%;
    tbody tr td {
      &:nth-of-type(1),
      &:nth-of-type(2) {
        width: 50%;
      }
    }
  }
}
.thyroid-function{
  table {
    width: 100%;
    tbody tr td {
      &:nth-of-type(1),
      &:nth-of-type(2) {
        width: 50%;
      }
    }
  }
}
.renal-function-test{
  table {
    width: 100%;
    tbody tr td {
      &:nth-of-type(1),
      &:nth-of-type(2) {
        width: 50%;
      }
    }
  }
}
.carotid-ultrasound {
  table {
    width: 100%;
    tbody tr td {
      &:nth-of-type(1),
      &:nth-of-type(2),
      &:nth-of-type(3) {
        width: 33%;
      }
    }
  }
}
.PWV-ABI-TBI {
  table {
    width: 100%;
    tbody tr td {
      &:nth-of-type(1),
      &:nth-of-type(2) {
        width: 50%;
      }
    }
  }
}
.cardiac-exam {
  table {
    width: 100%;
    tbody tr td {
      &:nth-of-type(1),
      &:nth-of-type(2) {
        width: 50%;
      }
    }
  }
}
</style>