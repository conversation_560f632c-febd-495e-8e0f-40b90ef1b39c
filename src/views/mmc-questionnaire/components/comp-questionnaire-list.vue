<template>
  <ul class="topic">
    <li v-for="topic in list" :key="topic.id">
      <p class="h1" v-if="topic.title" v-text="topic.title"></p>
      <ul class="question">
        <li v-for="question in topic.children" :key="question.subquestion_id">
          <div v-if="question.show">
            <p class="h2" v-if="!question.inline_input">
              {{ question.subquestion_name }}
              <span
                :class="`checkbox ${setCheckboxClass(question.checked)}`"
                v-if="question.inline_check"
              >
                <span class="yes">{{
                  setInlineCheckText(question.btn_group, 0)
                }}</span
                ><span class="no">{{
                  setInlineCheckText(question.btn_group, 1)
                }}</span>
              </span>
            </p>
            <p class="h2 inline-input" v-if="question.inline_input">
              <span
                v-for="(item, index) in question.subquestion_name_format"
                :key="index"
                :class="item.key"
                >{{ item.value }}</span
              >
            </p>
            <div v-if="question.datetime_value" class="datetime">
              <span class="key" v-text="question.datetime_key"></span>
              <span class="value" v-text="question.datetime_value"></span>
            </div>
            <ul
              v-if="question.children"
              :class="setQuestionItemClass(question)"
            >
            
            <div v-for="(item,index) in question.children" :key="index">
              <div v-if='item.checked&&item.show_id=="97802"||item.checked&&item.show_id=="97803"||item.checked&&item.show_id=="97902"||item.checked&&item.show_id=="97903"' >
                您之前的选择为：<span style="color: #F7830D;">{{ item.show_name }}</span>
              </div>
            </div>
              <li
                v-for="item in question.children"
                :key="item.option_id"
                :class="setItemClass(item)"
              >
                <span class="icon" v-if="item.option_id"></span>
                <span :class="`text ${item.option_id && 'option'}`"
                  >{{ item.option_name }}
                  <span
                    v-if="item.value"
                    class="value"
                    v-html="formatInnerHTML(item.value)"
                  ></span
                ></span>
              </li>
            </ul>
            <div v-if="question.textarea" class="textarea-div">
              <span
                class="textarea"
                v-html="formatInnerHTML(question.textareavalue)"
              ></span>
            </div>
            <div v-if="question.img">
              <div
                v-if="
                  question.rightimglist.length == 0 &&
                  question.leftimglist.length == 0
                "
                class="eyeResult"
              >
                暂无数据
              </div>
              <div v-else>
                <div v-if="question.rightimglist[0]">
                  <div class="title">右眼第一组</div>
                  <img class="eye-img" :src="question.rightimglist[0]" alt="" />
                </div>
                <div v-if="question.rightimglist[1]">
                  <div class="title">右眼第二组</div>
                  <img class="eye-img" :src="question.rightimglist[1]" alt="" />
                </div>
                <div v-if="question.leftimglist[0]">
                  <div class="title">左眼第一组</div>
                  <img class="eye-img" :src="question.leftimglist[0]" alt="" />
                </div>
                <div v-if="question.leftimglist[1]">
                  <div class="title">左眼第二组</div>
                  <img class="eye-img" :src="question.leftimglist[1]" alt="" />
                </div>
                <div class="eyeResult" v-if="question.result !=''">
                  <div>
                    <span class="text-mut">人工智能评估建议：</span>
                    <span>本结果仅供参考</span>
                  </div>
                  <div>
                    <span
                      >转诊建议：{{ question.result.recom_transfer }}</span
                    >
                    <br />
                    <span>复查建议：{{ question.result.recom_reexam }}</span>
                    <br />
                  </div>
                </div>
              </div>
            </div>
            <MMCQuestionTable :data="question"></MMCQuestionTable>
          </div>
        </li>
      </ul>
    </li>
  </ul>
</template>

<script>
import MMCQuestionTable from './table.vue';
export default {
  props: {
    list: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  components: {
    MMCQuestionTable,
  },
  methods: {
    setQuestionItemClass(question) {
      return `question-item ${question.composing}`;
    },
    setItemClass(item) {
      return `item ${item.checked && 'checked'}`;
    },
    setCheckboxClass(checked) {
      if (checked === 1) return 'yes';
      if (checked === 0) return 'no';
    },
    formatInnerHTML(value) {
      if (value) {
        return value
          .replace(/</g, '&lt;')
          .replace(/&lt;br\/>/g, '<br/>')
          .replace(/&lt;i>/g, '<i>')
          .replace(/&lt;span>/g, '<span>')
          .replace(/&lt;\/i>/g, '</i>')
          .replace(/&lt;\/span>/g, '</span>');
      } else {
        return value;
      }
    },
    setInlineCheckText(btn_group, i) {
      let text = i === 0 ? '是' : '否';
      return btn_group ? btn_group[i] : text;
    },
  },
};
</script>

<style lang="scss" scoped>
@import '../common/styles/common.scss';
</style>
