.h1, .h2 {
  margin: 12px 0;
  font-size: 17px;
  font-weight: bold;
}
.h1 {
  margin-bottom: 0;
  color: #F7830D;
}
.h2 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  line-height: 24px;
  color: #0A0A0A;
  .checkbox {
    display: flex;
    align-items: center;
    flex-shrink: 0;
    width: 56px;
    height: 21px;
    margin-left: 9px;
    &.yes {
      .yes {
        color: #fff;
        background-color: #F7830D;
      }
      .no {
        border: 1px solid #C0C6CC;
      }
    }
    &.no {
      .no {
        color: #fff;
        background-color: #F7830D;
      }
      .yes {
        border: 1px solid #C0C6CC;
      }
    }
    &:not(.yes, .no) {
      span {
        border: 1px solid #C0C6CC;
        &.no {
          border-left: 0;
        }
      }
    }
    span {
      font-size: 15px;
      width: 50%;
      height: 21px;
      line-height: 21px;
      text-align: center;
      box-sizing: border-box;
      color: #878F99;
      &.yes {
        border-radius: 4px 0 0 4px;
      }
      &.no {
        border-radius: 0 4px 4px 0;
      }
    }
  }
  &.inline-input {
    display: block;
    // justify-content: flex-start;
    span {
      display: inline-block;
      font-weight: bold;
      &.input {
        min-width: 24px;
        padding: 0 5px;
        // align-self: flex-end;
        text-align: center;
        vertical-align: bottom;
        color: #F7830D;
        border-bottom: 2px solid #0A0A0A;
      }
      &.normal{
        min-width: 5px;
      }
    }
  }
}
.datetime {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  span {
    font-size: 15px;
    color: #3F4447;
    &.value {
      color: #F7830D;
      border-bottom: 1px solid #0A0A0A;
    }
  }
}
.textarea-div {
  margin: 6px 0;
  .textarea {
    font-size: 15px;
    color: #F7830D;
    border-bottom: 1px solid #0A0A0A;
  }
}
.title {
  font-size: 16px;
  margin-bottom: 6px;
}
.eye-img {
  width: 100%;
}
.eyeResult {
  font-size: 16px;
  .text-mut {
    color: #f28b05;
  }
}
.question {
  > li {
    &:last-of-type > div {
      border: none;
    }
    > div {
      // margin-bottom: 12px;
      overflow: hidden;
      border-bottom: 1px solid #eee;
    }
  }
  .question-item {
    &.inline {
      display: flex;
      align-items: center;
      .item {
        margin-left: 20px;
        &:first-of-type {
          margin-left: 0;
        }
      }
    }
    .item {
      display: flex;
      align-items: center;
      margin: 6px 0;
      &.checked {
        .icon {
          border-color:  #F7830D;
          background: #F7830D url('../common/images/checked.png') no-repeat center center/100% 100%;
        }
        .text {
          color: #3F4447;
        }
      }
      .icon {
        flex-shrink: 0;
        width: 14px;
        height: 14px;
        border-radius: 50%;
        border: 1px solid #ccc;
        background-color: #fff;
      }
      .text {
        line-height: 1.6;
        font-size: 15px;
        color: #999;
        &.option {
          margin-left: 8px;
        }
        .value {
          color: #F7830D;
          border-bottom: 1px solid #0A0A0A;
          ::v-deep span {
            color: #3F4447;
            border-bottom: 1px solid #fff;
            i {
              padding: 0 5px;
              font-style: normal;
              color: #F7830D;
              border-bottom: 1px solid #0A0A0A;
            }
          }
        }
      }
    }
  }
}
.mmc-questionnaire-table {
  margin-bottom: 12px;
  table {
    width: 100%;
    // border: 1px solid #F1F1F1;
    th, td {
      vertical-align: top;
      font-size: 14px;
      text-align: center;
      border: 1px solid #F1F1F1;
    }
    th {
      background-color: #F5F5F5;
      .cell {
        padding: 12px 6px;
        font-weight: bold;
      }
    }
    td {
      .cell {
        min-height: 65px;
        line-height: 1.5;
      }
    }
    .cell {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 9px 6px;
      box-sizing: border-box;
      word-break: break-all;
      &.space-around {
        display: block;
      }
      // line-height: 1.5;
    }
  }
}