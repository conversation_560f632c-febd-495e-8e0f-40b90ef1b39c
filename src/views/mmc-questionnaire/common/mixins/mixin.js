export default {
  methods: {
    // 设置选择题
    setInlineCheckbox(item, i, j) {
      const answer = this.checkAnswerValue(item);
      let checked = -1;
      if (answer) {
        const arr = ['是', '有'];
        checked = arr.includes(answer.answer_value) ? 1 : 0;
      }
      this.question_list[i].children[j].checked = checked;
    },
    // 设置选择题时间
    setInlineCheckboxDatetime(item, i, j) {
      const answer = this.checkAnswerValue(item);
      if (answer) {
        this.question_list[i].children[j].datetime_value = answer.answer_value;
      }
    },
    // 多选/单选
    checkAnswerValue(item, type) {
      if (item.answer && item.answer[0] && item.answer[0].answer_value) {
        return type === 'all' ? item.answer : item.answer[0];
      }
      return false;
    },
    // 多选题输入内容
    setMultiInputValue(item, i, j, k) {
      const answer = this.checkAnswerValue(item);
      if (answer) {
        let value = answer.answer_value;

        const prefix = this.question_list[i].children[j].children[k].prefix;
        const suffix = this.question_list[i].children[j].children[k].suffix;
        if (prefix) value = prefix + value;
        if (suffix) value = value + suffix;

        this.question_list[i].children[j].children[k].value = value;
      }
    },
    // 设置特殊题目的输入值
    setSpecialInputValue(item, i, j, k) {
      const answer = this.checkAnswerValue(item);
      if (answer) {
        let initial = this.question_list[i].children[j].children[k].value || '';
        // let value = answer.answer_value;
        let value = `<i>${answer.answer_value}</i>`;
        
        const list = ['51162001','51163001','51148001','51149001','51130001', '51131001', '51132001', '51133001', '51134001', '51135001', '51136001','51107001', '51108001', '51109001', '51110001', '51111001', '51112001'];
        if (list.includes(item.subquestion_id)) {
          const str = '首次诊断时间：';
          value = str + value;
        }
        if (item.subquestion_id === '51133002') {
          value = value + ' ' + initial;
        }
        if (item.subquestion_id === '51113002') {
          const str = '首次诊断时间：';
          value = initial + ' ' + str + value;
        }
        if (item.subquestion_id === '51137002') {
          const str = '首次诊断时间：';
          value = initial + ' '  + str + value;
        }
        
        // if (item.subquestion_id === '51184001') {
        //   const str = '手术名称：';
        //   value = str + value;
        // }
        // if (item.subquestion_id === '51184002') {
        //   value = initial + ' 手术时间：' + value;
        // }

        // this.question_list[i].children[j].children[k].value = value;
        this.question_list[i].children[j].children[k].value = `<span>${value}</span>`;
      }
    },
    // 设置选中状态
    setQuestionCheckedStatus(question, list, i, j, type) {
      const answers = this.checkAnswerValue(question, type);
      const setStatus = (s, t, index) => {
        if (s === t) {
          if(t=='97902'||t=='97903'||t=='97802'||t=='97803'){
            let newList = {
              checked:true,
              show_id:question.answer[0].answer_id,
              show_name:question.answer[0].answer_value
            }
            this.question_list[i].children[j].children.push(newList)
          }else{
            this.question_list[i].children[j].children[index].checked = true;
          }
        }
      }

      if (type === 'all') {
        if (answers && answers[0].answer_id) {
          list.forEach((item, index) => {
            answers.forEach((answer) => {
              setStatus(item, answer.answer_id, index);
            })
          })
        }
      } else {
        if (answers.answer_id) {
          list.forEach((item, index) => {
            setStatus(item, answers.answer_id, index);
          })
        }
      }
    },
  }
}