<template>
  <div class="renal-function-exam">
    <comp-questionnaire-list :list="question_list.slice(0, 1)" />
    <van-overlay
      :show="show_loading"
      class-name="mmc-questionnaire-loading-overlay"
    >
      <van-loading type="spinner" />
    </van-overlay>
  </div>
</template>

<script>
import api from '@/api/mmc-questionnaire.js';
import compQuestionnaireList from '../components/comp-questionnaire-list.vue';
import mixin from '../common/mixins/mixin';
export default {
  data() {
    return {
      show_loading: false,
      question_list: [
        {
          id: 1,
          composing: 'block',
          title: null,
          children: [
            {
              question_id: '1',
              subquestion_id: '',
              subquestion_name: '1、肾功能检测',
              show: true,
              table_name: 'renal-function-test',
              data: [
                { title: '尿液肌酐', first: '' },
                { title: '尿微量白蛋白', first: '' },
                { title: '尿白蛋白/肌酐', first: '' },
              ],
            },
          ],
        },
      ],
    };
  },
  components: {
    compQuestionnaireList,
  },
  mixins: [mixin],
  created() {
    this.getSymptomData();
  },
  methods: {
    async getSymptomData() {
      const { user_id, visit_level, sex, hosp_id } = this.$route.query;

      this.show_loading = true;

      let res = await api
        .qssInfo({
          user_id,
          visit_level,
          template_id: 36,
          sex,
          hosp_id,
        })
        .finally(() => {
          this.show_loading = false;
        });
      if (res.status === 0) {
        const list = res.data.question_data || [];
        this.setQuestionList(list);
      }
    },
    // 设置题目
    setQuestionList(list) {
      if (list.length > 0) {
        list.forEach((item) => {
          // 设置表格
          switch (item.subquestion_id) {
            case '901002':
              this.setTableValue(item, 0, 0, 0);
              break;
            case '901005':
              this.setTableValue(item, 0, 0, 1);
              break;
            case '901013':
              this.setTableValue(item, 0, 0, 2);
              break;
            default:
              break;
          }
        });
      }
    },
    setTableValue(item, i, j, k, h) {
      const answer = this.checkAnswerValue(item);
      if (answer) {
        this.question_list[i].children[j].data[k].first =
          answer.answer_value + answer.unit;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.renal-function-exam {
  padding: 0 15px;
}
</style>