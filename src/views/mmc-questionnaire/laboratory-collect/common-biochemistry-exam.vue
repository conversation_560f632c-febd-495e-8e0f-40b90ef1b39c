<template>
  <div class="outside-vascular-exam">
    <comp-questionnaire-list :list="question_list.slice(0, 1)" />
    <van-overlay
      :show="show_loading"
      class-name="mmc-questionnaire-loading-overlay"
    >
      <van-loading type="spinner" />
    </van-overlay>
  </div>
</template>

<script>
import api from '@/api/mmc-questionnaire.js';
import compQuestionnaireList from '../components/comp-questionnaire-list.vue';
import mixin from '../common/mixins/mixin';
export default {
  data() {
    return {
      show_loading: false,
      question_list: [
        {
          id: 1,
          composing: 'block',
          title: null,
          children: [
            {
              question_id: '1',
              subquestion_id: '',
              subquestion_name: '血常规',
              show: true,
              table_name: 'routine-blood-test',
              data: [
                { title: '血红蛋白 (Hb)', first: '', second: '' },
                { title: '红细胞计数 (RBC)', first: '', second: '' },
                { title: '白细胞计数 (WBC)', first: '', second: '' },
                { title: '血小板计数 (PLT)', first: '', second: '' },
                { title: '红细胞比容 (HCT)', first: '', second: '' },
                { title: '平均红细胞体积 (MCV)', first: '', second: '' },
                { title: '平均血红蛋白量 (MCH)', first: '', second: '' },
                { title: '血小板平均体积 (MPV)', first: '', second: '' },
              ],
            },
            {
              question_id: '2',
              subquestion_id: '',
              subquestion_name: '尿常规',
              show: true,
              table_name: 'routine-urine-test',
              data: [
                { title: '蛋白质 (PRO)', first: '', second: '' },
                { title: '葡萄糖 (GLU)', first: '', second: '' },
                { title: '白细胞 (LEU)', first: '', second: '' },
                { title: '潜血 (ERY)', first: '', second: '' },
                { title: '亚硝酸盐 (NIT)', first: '', second: '' },
                { title: '尿酮体 (KET)', first: '', second: '' },
              ],
            },
            {
              question_id: '3',
              subquestion_id: '',
              subquestion_name: '血生化及血脂',
              show: true,
              table_name: 'blood-biochemistry-lipids',
              data: [
                { title: '丙氨酸氨基转移酶 (ALT)', first: '', second: '' },
                { title: '天门冬氨酸氨基转移酶 (AST)', first: '', second: '' },
                { title: '碱性磷酸酶 (ALP)', first: '', second: '' },
                { title: 'r-谷氨酰基转移酶 (γ-GT)', first: '', second: '' },
                { title: '白蛋白 (ALB)', first: '', second: '' },
                { title: '尿素 (BUN)', first: '', second: '' },
                { title: '肌酐 (CR)', first: '', second: '' },
                { title: '尿酸 (UA)', first: '', second: '' },
                { title: '甘油三酯 (TG)', first: '', second: '' },
                { title: '总胆固醇 (TC)', first: '', second: '' },
                { title: '高密度脂蛋白胆固醇 (HDL-c)', first: '', second: '' },
                { title: '低密度脂蛋白胆固醇 (LDL-c)', first: '', second: '' },
              ],
            },
          ],
        },
      ],
    };
  },
  components: {
    compQuestionnaireList,
  },
  mixins: [mixin],
  created() {
    this.getSymptomData();
  },
  methods: {
    async getSymptomData() {
      const { user_id, visit_level, sex, hosp_id } = this.$route.query;

      this.show_loading = true;

      let res = await api
        .qssInfo({
          user_id,
          visit_level,
          template_id: 31,
          sex,
          hosp_id,
        })
        .finally(() => {
          this.show_loading = false;
        });
      if (res.status === 0) {
        const list = res.data.question_data || [];
        this.setQuestionList(list);
      }
    },
    // 设置题目
    setQuestionList(list) {
      if (list.length > 0) {
        list.forEach((item) => {
          // 设置表格
          switch (item.subquestion_id) {
            case '878001':
              this.setTableValue(item, 0, 0, 0);
              break;
            case '878002':
              this.setTableValue(item, 0, 0, 1);
              break;
            case '878003':
              this.setTableValue(item, 0, 0, 2);
              break;
            case '878004':
              this.setTableValue(item, 0, 0, 3);
              break;
            case '878005':
              this.setTableValue(item, 0, 0, 4, '878005');
              break;
            case '878006':
              this.setTableValue(item, 0, 0, 5);
              break;
            case '878007':
              this.setTableValue(item, 0, 0, 6);
              break;
            case '878008':
              this.setTableValue(item, 0, 0, 7);
              break;
            case '878009':
              this.setTableValue(item, 0, 1, 0);
              break;
            case '878010':
              this.setTableValue(item, 0, 1, 1);
              break;
            case '878011':
              this.setTableValue(item, 0, 1, 2);
              break;
            case '878012':
              this.setTableValue(item, 0, 1, 3);
              break;
            case '878013':
              this.setTableValue(item, 0, 1, 4);
              break;
            case '878014':
              this.setTableValue(item, 0, 1, 5);
              break;
            case '878030':
              this.setTableValue(item, 0, 2, 0);
              break;
            case '878031':
              this.setTableValue(item, 0, 2, 1);
              break;
            case '878032':
              this.setTableValue(item, 0, 2, 2);
              break;
            case '878033':
              this.setTableValue(item, 0, 2, 3);
              break;
            case '878077':
              this.setTableValue(item, 0, 2, 4);
              break;
            case '878036':
              this.setTableValue(item, 0, 2, 5);
              break;
            case '878037':
              this.setTableValue(item, 0, 2, 6);
              break;
            case '878038':
              this.setTableValue(item, 0, 2, 7);
              break;
            case '878039':
              this.setTableValue(item, 0, 2, 8);
              break;
            case '878040':
              this.setTableValue(item, 0, 2, 9);
              break;
            case '878041':
              this.setTableValue(item, 0, 2, 10);
              break;
            case '878042':
              this.setTableValue(item, 0, 2, 11);
              break;
            default:
              break;
          }
        });
      }
    },
    setTableValue(item, i, j, k, h) {
      const answer = this.checkAnswerValue(item);
      if (answer) {
        this.question_list[i].children[j].data[k].first = answer.answer_value;
      }
      if (h === '878005') {
        let unit = answer ? answer.unit : item.unit;
        this.question_list[i].children[j].data[k].second =
          unit == '1' || unit == '' ? '参考值0.380-0.508' : unit;
      } else {
        this.question_list[i].children[j].data[k].second = answer
          ? answer.unit
          : item.unit;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.outside-vascular-exam {
  padding: 0 15px;
}
</style>