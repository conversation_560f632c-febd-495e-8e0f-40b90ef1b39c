<template>
  <div class="outside-vascular-exam">
    <comp-questionnaire-list :list="question_list.slice(0, 1)" />
    <van-overlay :show="show_loading" class-name="mmc-questionnaire-loading-overlay">
      <van-loading type="spinner" />
    </van-overlay>
  </div>
</template>

<script>
import api from '@/api/mmc-questionnaire.js';
import compQuestionnaireList from '../components/comp-questionnaire-list.vue';
import mixin from '../common/mixins/mixin';
export default {
  data() {
    return {
      show_loading: false,
      question_list: [
        {
          id: 1,
          composing: 'block',
          title: null,
          children: [
            {
              question_id: '1',
              subquestion_id: '',
              subquestion_name: '1、糖耐量试验+胰岛素/C肽释放试验',
              show: true,
              table_name: 'sugar-tolerance-exam',
              data: [
                { title: '葡萄糖', first: '', second: '' },
                { title: '胰岛素 (µIU/mL)', first: '', second: '' },
                { title: 'C肽 (µg/L)', first: '', second: '' },
              ],
            },
            {
              subquestion_id: null,
              subquestion_name: '糖化血红蛋白#num %',
              subquestion_name_format: [
                {
                  key: 'normal',
                  value: '2、糖化血红蛋白',
                },
                {
                  key: 'input',
                  value: '',
                },
                {
                  key: 'normal',
                  value: '%',
                },
              ],
              show: true,
              inline_check: null,
              inline_input: true,
            },
          ],
        },
      ],
      questionItem_875001_unit: '',
      questionItem_875004_unit: '',
      questionItem_875006_unit: '',
      questionItem_875009_unit: '',
      questionItem_875011_unit: '',
      questionItem_875014_unit: '',
    };
  },
  components: {
    compQuestionnaireList,
  },
  mixins: [mixin],
  created() {
    this.getSymptomData();
  },
  computed: {
    pttUnit() {
      if (
        String(this.questionItem_875001_unit).trim() == '' &&
        String(this.questionItem_875004_unit).trim() == ''
      ) {
        return '';
      } else {
        if (String(this.questionItem_875001_unit).trim() != '') {
          return String(this.questionItem_875001_unit).trim();
        } else {
          return String(this.questionItem_875004_unit).trim();
        }
      }
    },
    ydsUnit() {
      if (
        String(this.questionItem_875006_unit).trim() == '' &&
        String(this.questionItem_875009_unit).trim() == ''
      ) {
        return '';
      } else {
        if (String(this.questionItem_875006_unit).trim() != '') {
          return String(this.questionItem_875006_unit).trim();
        } else {
          return String(this.questionItem_875009_unit).trim();
        }
      }
    },
    ctUnit() {
      if (
        String(this.questionItem_875011_unit).trim() == '' &&
        String(this.questionItem_875014_unit).trim() == ''
      ) {
        return '';
      } else {
        if (String(this.questionItem_875011_unit).trim() != '') {
          return String(this.questionItem_875011_unit).trim();
        } else {
          return String(this.questionItem_875014_unit).trim();
        }
      }
    },
  },
  methods: {
    async getSymptomData() {
      const { user_id, visit_level, sex, hosp_id } = this.$route.query;

      this.show_loading = true;
      
      let res = await api.qssInfo({
        user_id,
        visit_level,
        template_id: 30,
        sex,
        hosp_id,
      }).finally(() => {
        this.show_loading = false;
      });
      if (res.status === 0) {
        const list = res.data.question_data || [];
        this.setQuestionList(list);
      }
    },
    // 设置题目
    setQuestionList(list) {
      if (list.length > 0) {
        list.forEach((item) => {
          const answer = this.checkAnswerValue(item);
          // 设置表格
          switch (item.subquestion_id) {
            case '875001':
              this.questionItem_875001_unit = answer ? answer.unit : item.unit;
              this.setTableValue(item, 0, 'first', '葡萄糖', this.pttUnit);
              break;
            case '875004':
              this.questionItem_875004_unit = answer ? answer.unit : item.unit;
              this.setTableValue(item, 0, 'second', '葡萄糖', this.pttUnit);
              break;
            case '875006':
              this.questionItem_875006_unit = answer ? answer.unit : item.unit;
              this.setTableValue(item, 1, 'first', '胰岛素', this.ydsUnit);
              break;
            case '875009':
              this.questionItem_875009_unit = answer ? answer.unit : item.unit;
              this.setTableValue(item, 1, 'second', '胰岛素', this.ydsUnit);
              break;
            case '875011':
              this.questionItem_875011_unit = answer ? answer.unit : item.unit;
              this.setTableValue(item, 2, 'first', 'C肽', this.ctUnit);
              break;
            case '875014':
              this.questionItem_875014_unit = answer ? answer.unit : item.unit;
              this.setTableValue(item, 2, 'second', 'C肽', this.ctUnit);
              break;
            case '876':
              this.setTitleInput(item, 0, 1, 1);
              break;
            default:
              break;
          }
        });
      }
    },
    setTableValue(item, i, j, k, h) {
      this.question_list[0].children[0].data[i].title = `${k}(${h})`;
      const answer = this.checkAnswerValue(item);
      if (answer) {
        if (this.unitJudge(item.subquestion_id, h)) {
          this.question_list[0].children[0].data[i][j] =
            answer.answer_value + answer.unit;
        } else {
          this.question_list[0].children[0].data[i][j] = answer.answer_value;
        }
      }
    },
    // 设置标题填空题
    setTitleInput(item, i, j, k) {
      const answer = this.checkAnswerValue(item);
      if (answer) {
        this.question_list[i].children[j].subquestion_name_format[k].value =
          answer.answer_value;
      }
    },

    unitJudge(id, stand) {
      let unit = this['questionItem_' + id + '_unit'];
      if (stand == unit) {
        return false;
      } else {
        if (
          unit != '' &&
          String(stand).replace('/', '').trim().toLowerCase() !=
            String(unit).trim().replace('/', '').toLowerCase()
        ) {
          return true;
        } else {
          return false;
        }
      }
    },
  },
};
</script>

<style scoped lang="scss">
.outside-vascular-exam {
  padding: 0 15px;
}
</style>