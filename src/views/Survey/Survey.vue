<template>
  <div class="wrapper">
    <h1 class="header">满意度调查</h1>
    <div class="content">
      <div class="group">
        <h2 class="title">
          <i class="title-star">*</i>
          1. 您对控糖营评分
        </h2>
        <div class="ok">
          <div class="ok-txt">
            <span>很不满意</span>
            <span>很满意</span>
          </div>
          <div class="ok-star">
            <div v-for="(item, index) in oks" :key="index" class="star" @click="okScore = item.value">
              <img v-if="okScore > index" :src="starActive" alt="active">
              <img v-else :src="starGrey" alt="grey">
              <span v-if="okScore === item.value" class="tips">{{ item.name }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="group">
        <h2 class="title">
          <i class="title-star">*</i>
          2. 通过{{days}}，哪一部分您觉得对您帮助最大？
          <span class="title-tips">【多选题】</span>
        </h2>
        <checkbox-group v-model="help" class="help">
          <checkbox
            v-for="(item, index) in helpOptions"
            checked-color="#ffb44c"
            :name="item.name"
            shape="square"
            :key="index"
          >
            <span class="help-val">{{ item.value }}</span>
          </checkbox>
        </checkbox-group>
      </div>
      <div class="group">
        <h2 class="title">
          <i class="title-star">*</i>
          3. 如有类似控糖营活动，您是否愿意再次参加？
        </h2>
        <radio-group v-model="agree" class="agree">
          <radio
            v-for="(item, index) in agreeOptions"
            checked-color="#ffb44c"
            :name="item.name"
            :key="index"
          >
            <span class="agree-val">{{ item.value }}</span>
          </radio>
        </radio-group>
      </div>
      <div class="group">
        <h2 class="title">
          4. 通过{{days}}，您目前体重
        </h2>
        <field v-model="weight" type="number"/>
      </div>
      <div class="group">
        <h2 class="title">
          5. 通过{{days}}，您最近一次空腹血糖
        </h2>
        <field v-model="bg" type="number" maxlength="4"/>
      </div>
      <div class="group">
        <h2 class="title">
          6. 您对我们控糖营的建议
        </h2>
        <field v-model="advice" type="textarea" autosize rows="3"/>
      </div>
    </div>
    <div class="footer">
      <button class="submit" @click="submit" :disabled="isDisable">提交</button>
    </div>

    <div class="model" v-if="show==true">
      <img src="https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/wechat/sugarController/%E9%97%AE%E5%8D%B7%E6%8F%90%E4%BA%A4%E6%88%90%E5%8A%9F.png" alt="">
    </div>
  </div>
</template>

<script>
  import 'vant/lib/field/style'
  import 'vant/lib/checkbox/style'
  import 'vant/lib/checkbox-group/style'
  import 'vant/lib/radio/style'
  import 'vant/lib/radio-group/style'
  import Field from 'vant/lib/field'
  import Checkbox from 'vant/lib/checkbox'
  import CheckboxGroup from 'vant/lib/checkbox-group'
  import Radio from 'vant/lib/radio'
  import RadioGroup from 'vant/lib/radio-group'

  import { getReportSatisfied, getUserInfo } from '@/api/survey'

  export default {
    components: {
      'checkbox-group': CheckboxGroup,
      'checkbox': Checkbox,
      'radio-group': RadioGroup,
      'radio': Radio,
      'field': Field
    },
    data() {
      return {
        show: false,
        starGrey: require('./imgs/star-grey.png'),
        starActive: require('./imgs/star-active.png'),
        okScore: 0,
        oks: [
          {
            name: '很不满意',
            value: 1
          },
          {
            name: '不满意',
            value: 2
          },
          {
            name: '一般',
            value: 3
          },
          {
            name: '满意',
            value: 4
          },
          {
            name: '很满意',
            value: 5
          }
        ],
        help: [],
        helpOptions: [
          {
            value: '培养饮食、运动习惯',
            name: 1
          },
          {
            value: '了解更多糖尿病知识',
            name: 2
          },
          {
            value: '血糖或者体重得到更好控制',
            name: 3
          },
          {
            value: '跟其他患者交流经验',
            name: 4
          },
          {
            value: '没什么帮助',
            name: 5
          }
        ],
        agree: '',
        agreeOptions: [
          {
            value: '愿意',
            name: 1
          },
          {
            value: '不愿意',
            name: 2
          }
        ],
        weight: '',
        bg: '',
        advice: '',
        isDisable: false,
        token: '',
        days: ''
      }
    },
    created() {
      this.getUser()
    },
    methods: {
      /**
       * 提交
       */
      submit() {
        if (this.okScore === 0) {
          this.$toast('请回答第一题')
          return false
        }
        if (this.help.length === 0) {
          this.$toast('请回答第二题')
          return false
        }
        if (this.agree === '') {
          this.$toast('请回答第三题')
          return false
        }
        this.isDisable = true
        setTimeout(() => {
          this.isDisable = false
        }, 1000)
        this.token = this.$route.query.accessToken
        let that = this
        getReportSatisfied({
          'score': this.okScore,
          'help': this.help,
          'agree': this.agree,
          'weight': this.weight,
          'bg': this.bg,
          'advice': this.advice,
          'token': this.token
        }).then(function (res) {
          if (res.status === 0) {
            // this.$toast('提交成功')
            that.show = true
          } else {
            this.$toast(res.msg)
          }
        })
      },
      getUser() {
        let that = this
        getUserInfo({ 'token': this.token }).then(function (res) {
          if (res.status === 0) {
            that.days = res.data.camp_type
          } else {
            this.$toast(res.msg)
          }
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  @import "Survey.scss";
</style>
