<template>
  <div class="wrapper">
    <div>
      <!--<video class="video_info" controls :poster="thumbnail">-->
      <!--<source :src="video_url" type="video/mp4">-->
      <!--</video>-->
      <video-player class="video_info video-player vjs-custom-skin"
                    ref="videoPlayer"
                    :playsinline="true"
                    :options="playerOptions">
      </video-player>
    </div>
    <div class="footer">
      <div class="content">
        <div class="footer_info">
          <div class="left">
            <img src="./../../../assets/images/<EMAIL>" alt="">
            <div class="tips">
              <div>MMC管家</div>
              <div class="sub_tips">您的控糖健康管家</div>
            </div>
          </div>

          <a href="https://static.zz-med.com/app/download.html">
            <div class="download_btn">下载</div>
          </a>

        </div>
      </div>
    </div>
  </div>
</template>
<script>
  import {getVideoinfo} from '@/api/video.js'
  import {videoPlayer} from 'vue-video-player'
  import 'video.js/dist/video-js.css'

  export default {
    components: {
      videoPlayer
    },
    data: () => {
      return {
        title: '',
        video_url: '',
        thumbnail: '',
        playerOptions: {
          playbackRates: [0.5, 1.0, 1.5, 2.0], // 可选的播放速度
          autoplay: false, // 如果为true,浏览器准备好时开始回放。
          muted: false, // 默认情况下将会消除任何音频。
          loop: false, // 是否视频一结束就重新开始。
          preload: 'auto', // 建议浏览器在<video>加载元素后是否应该开始下载视频数据。auto浏览器选择最佳行为,立即开始加载视频（如果浏览器支持）
          language: 'zh-CN',
          fluid: true, // 当true时，Video.js player将拥有流体大小。换句话说，它将按比例缩放以适应其容器。
          sources: [{
            type: "video/mp4", // 类型
            src: '' // url地址
          }],
          poster: '', // 封面地址
          notSupportedMessage: '此视频暂无法播放，请稍后再试', // 允许覆盖Video.js无法播放媒体源时显示的默认信息。
          controlBar: {
            timeDivider: true, // 当前时间和持续时间的分隔符
            durationDisplay: true, // 显示持续时间
            remainingTimeDisplay: false, // 是否显示剩余时间功能
            fullscreenToggle: true // 是否显示全屏按钮
          }
        }
      }
    },
    created() {
      this.id = this.$route.query.id
      // 初始化
      this.init(this.id)
    },
    methods: {
      /**
       * 初始化
       */
      init(id) {
        let that = this
        getVideoinfo(id).then(res => {
          that.title = res.data.title
          that.video_url = res.data.video_url
          that.thumbnail = res.data.thumbnail
          that.playerOptions = {
            playbackRates: [0.5, 1.0, 1.5, 2.0], // 可选的播放速度
            autoplay: false, // 如果为true,浏览器准备好时开始回放。
            muted: false, // 默认情况下将会消除任何音频。
            loop: false, // 是否视频一结束就重新开始。
            preload: 'auto', // 建议浏览器在<video>加载元素后是否应该开始下载视频数据。auto浏览器选择最佳行为,立即开始加载视频（如果浏览器支持）
            language: 'zh-CN',
            fluid: false, // 当true时，Video.js player将拥有流体大小。换句话说，它将按比例缩放以适应其容器。
            sources: [{
              src: that.video_url // url地址
            }],
            width: document.body.clientWidth+ "px",
            height: document.body.clientHeight  + "px",
            poster: that.thumbnail, // 封面地址
            notSupportedMessage: '此视频暂无法播放，请稍后再试', // 允许覆盖Video.js无法播放媒体源时显示的默认信息。
            controlBar: {
              timeDivider: true, // 当前时间和持续时间的分隔符
              durationDisplay: true, // 显示持续时间
              remainingTimeDisplay: false, // 是否显示剩余时间功能
              fullscreenToggle: true // 是否显示全屏按钮
            }
          }
          that.refs.videoPlayer.height = document.body.clientHeight - 70 + "px";
        })
      }
    }
  }
</script>

<style lang="scss" scoped>

  .wrapper {
    overflow: hidden;
    background: #fff;
    .video_info {
      width: 100%;
      height: calc(100vh - 70px) !important;
    }
    .video-player {
      width: 100%;
      height: calc(100vh - 70px) !important;
    }
    .footer {
      width: 100%;
      position: absolute;
      bottom: 0px;
      .content {
        width: 100%;
        background: #fff;
        height: 70px;
        .footer_info {
          display: flex;
          justify-content: space-between;
          align-items: center;
          .left {
            margin-left: 20px;
            padding-top: 6px;
            display: flex;
            justify-content: center;
            align-items: center;
            img {
              width: 56px;
              height: 56px;
            }
            .tips {
              text-align: left;
              font-size: 18px;
              font-weight: 500;
              color: #333333;
              line-height: 25px;
              .sub_tips {
                font-size: 15px;
                font-weight: 400;
                color: #333333;
                line-height: 21px;
              }
            }
          }

          .download_btn {
            display: block;
            padding: 10px;
            width: 64px;
            height: 22px;
            margin-right: 20px;
            background: linear-gradient(90deg, #FF8100 0%, #FD9A57 100%);
            box-shadow: 0px 3px 10px 0px rgba(0, 0, 0, 0.1);
            border-radius: 22px;
            font-size: 16px;
            font-weight: 500;
            color: #FFFFFF;
            line-height: 22px;
          }
        }
      }
    }
  }
</style>
