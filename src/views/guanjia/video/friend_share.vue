<template>
  <div class="wrapper">
    <img :src="img_url" alt="">
  </div>
</template>
<script>
  import {getVideoinfo} from '@/api/video.js'

  export default {
    data: () => {
      return {
        img_url: '',
      }
    },
    created() {
      this.img_url =  decodeURIComponent(this.$route.query.img_url)
    }
  }
</script>

<style lang="scss" scoped>
  .wrapper {
    overflow: hidden;
    background: #fff;
    img{
      width: 100vw;
    }
  }
</style>
