<template>
  <div class="main" ref="height">
    <div class="content">
      <div class="title">请输入验证密码</div>
      <input type="text" class="input" v-model="code">
      <div class="tips" v-if="err==true">请输入正确的验证密码</div>
      <button class="btn" @click="btnExchangeCode">验证</button>
    </div>
  </div>
</template>

<script>
  import { getExchangeCode } from '@/api/guanjia/guoshou/index'

  export default {
    data() {
      return {
        code: '',
        err: false
      }
    },
    created() {
      this.$nextTick(() => {
        this.$refs.height.style.height = window.innerHeight + 'px'
      })
    },
    methods: {
      btnExchangeCode() {
        let that = this
        if (that.code === '') {
          that.err = true
          return false
        }
        this.noDoubleTap(() => {
          getExchangeCode({ 'exchange_code': that.code }).then(function (res) {
            console.log(res)
            if (res.status == 0) {
              if (res.data.exist == true) {
                that.err = false
                that.code = ''
                that.$router.push({
                  name: 'guanjia.guoshou.success',
                  query: {}
                })
              } else {
                that.err = true
              }
            } else {
              that.err = true
            }
          })

          setTimeout(() => {
            that.$btnDisabled = true
          }, 2000)
        }, false)
      }
    }
  }
</script>

<style lang="scss" scoped>
  @import "index";
</style>
