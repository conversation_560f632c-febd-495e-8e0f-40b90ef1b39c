<template>
  <div class="wrapper" id="wrapper">
    <div class="tab" id="tab">
      <div @click="tabId = 1" :class="{'tab-item': true, 'tab-item-use': tabId === 1}">
        <img v-if="tabId === 1" src="./imgs/<EMAIL>" class="tab-icon1">
        <img v-else src="./imgs/<EMAIL>" class="tab-icon1">
        <span class="tab-name tab-name-use">血糖</span>
      </div>
      <div @click="navUrlToBp" :class="{'tab-item': true, 'tab-item-use': tabId === 2}">
        <img v-if="tabId === 2" src="./imgs/<EMAIL>" class="tab-icon2">
        <img v-else src="./imgs/<EMAIL>" class="tab-icon2">
        <span class="tab-name tab-name-use">血压</span>
      </div>
      <div @click="navUrlToBmi" :class="{'tab-item': true, 'tab-item-use': tabId === 3}">
        <img v-if="tabId === 3" src="./imgs/<EMAIL>" class="tab-icon3">
        <img v-else src="./imgs/<EMAIL>" class="tab-icon3">
        <span class="tab-name tab-name-use">BMI</span>
      </div>
    </div>
    <!-- 血糖 -->
    <div v-if="tabId === 1" class="bg">
      <div style="background: linear-gradient(180deg, #FFFFFF 0%, #F3F3F3 100%);">
        <div class="card" id="card">
          <div class="card-title">
            <span class="title-name">{{bgInfo.name}}</span>
            <div class="title-date">
              <span>{{bgInfo.measure_at}}</span>
            </div>
          </div>
          <div class="card-info">
            <p v-if="bgInfo.bg!==undefined" class="info-status">{{bgInfo.dining_status|formatStatus}}</p>
            <p v-if="bgInfo.bg!==undefined" class="info-value">{{bgInfo.bg}}</p>
            <img v-if="bgInfo.bg===undefined" src="./imgs/bsNoData.png" @click="navUrlTo" alt=""
                class="noDataImg">
          </div>
          <div class="card-result" v-if="bgInfo.bg!==undefined">
            <van-icon name="add" @click="navUrlTo" class="result-add"></van-icon>
            <span>测量血糖</span>
          </div>
        </div>
        <div class="bind" v-if="deviceBindFlg==0">
          <div class="bind-info">
            <img src="./imgs/bangding.png" class="bind-icon">
            <span class="bind-name">绑定血糖仪，测量数据可自动同步</span>
          </div>
          <span class="bind-btn" @click="navUrlToDevice">去绑定</span>
        </div>
      </div>
      <div class="case" id="course_container">
        <p class="case-tips">说明：血糖测量数据与“MMC管家”APP保持同步</p>
        <div class="case-box">
          <div class="case-tab" id="switch_tab">
            <span
              @click="caseTabId = 1"
              :class="{'case-tab-item': true, 'case-tab-item-active': caseTabId == 1}"
            >明细记录</span>
            <span
              @click="caseTabId = 2"
              :class="{'case-tab-item': true, 'case-tab-item-active': caseTabId == 2}"
            >血糖表格</span>
            <span
              @click="caseTabId = 3"
              :class="{'case-tab-item': true, 'case-tab-item-active': caseTabId == 3}"
            >趋势统计</span>
            <span
              @click="caseTabId = 4"
              :class="{'case-tab-item': true, 'case-tab-item-active': caseTabId == 4}"
            >七日统计</span>
          </div>
          <!-- 1：明细记录； -->
          <div v-if="caseTabId==1" class="case-group">
            <detail-record/>
          </div>

          <!-- 2：8点法统计； -->
          <div v-if="caseTabId==2" class="case-group">
            <eight-dot v-if="bg_table==1"/>
            <div class="bg_sample" v-if="bg_table==0">
              <div class="level_box">
                <div class="level_title pt20">升级成“健康短工”，可解锁此功能</div>
                <div class="level_tip pt20">你现在的等级是“{{level_name}}”</div>
                <div class="submit pt20"><span @click="navUrlToHealthPower" class="btn">查看详情</span></div>
              </div>
              <img src="./imgs/xuetangjilu_tanchuang.png" alt="">
            </div>
          </div>

          <!-- 3：趋势统计； -->
          <div v-if="caseTabId==3" class="case-group">
            <trend-stat v-if="trend_statistics==1"/>
            <div class="bg_sample" v-if="trend_statistics==0">
              <div class="level_box">
                <div class="level_title pt20">升级成“健康掌柜”，可解锁此功能</div>
                <div class="level_tip pt20">你现在的等级是“{{level_name}}”</div>
                <div class="submit pt20" @click="navUrlToHealthPower"><span class="btn">查看详情</span></div>
              </div>
              <img src="./imgs/qushitongji.png" alt="">
            </div>
          </div>

          <!-- 4：七日报告； -->
          <div v-if="caseTabId==4" class="case-group">
            <seven-day v-if="seven_day_statistics==1"/>
            <div class="bg_sample" v-if="seven_day_statistics==0">
              <div class="level_box">
                <div class="level_title pt20">升级成“健康状元”，可解锁此功能</div>
                <div class="level_tip pt20">你现在的等级是“{{level_name}}”</div>
                <div class="submit pt20"><span @click="navUrlToHealthPower" class="btn">查看详情</span></div>
              </div>
              <img src="./imgs/qiritongji.png" alt="">
            </div>
          </div>

        </div>
      </div>
    </div>
    <!-- 血压 -->
    <div v-if="tabId === 2"></div>
    <!-- BMI -->
    <div v-if="tabId === 3"></div>
  </div>
</template>

<script>
  import detailRecord from './components/detailRecordS.vue'
  import eightDot from './components/eightDot.vue'
  import trendStat from './components/trendStat.vue'
  import sevenDay from './components/sevenDay.vue'
  import wx from 'weixin-js-sdk'
  import moment from 'moment'
  import {getBgRecord, getHealthUnlock} from '@/api/sugar'

  export default {
    data: () => {
      return {
        arrayList: ['明细记录', '明细记录', '明细记录', '明细记录'],
        tabId: 1, // 1：血糖；2：血压；
        bgInfo: {},
        deviceBindFlg: 0,
        offset_top: 0,
        "seven_day_statistics": 0,                //类型：Number  必有字段  备注：七日统计
        "trend_statistics": 0,                //类型：Number  必有字段  备注：趋势统计
        "bg_table": 0,                //类型：Number  必有字段  备注：血糖表格
        "level_name": "",
        caseTabId: 1 // 1：明细记录；2：8点法统计；3：趋势统计；4：七日报告；
      }
    },
    components: {
      'seven-day': sevenDay,
      'trend-stat': trendStat,
      'eight-dot': eightDot,
      'detail-record': detailRecord
    },
    mounted() {
      let that = this
      // 如果检测到页面是从“往返缓存”中读取的，刷新页面
      window.addEventListener('pageshow', (e) => {
        if (e.persisted || (window.performance)) {
          that.init()
        }
      }, false)
    },
    created() {
      let that = this
      this.deviceBindFlg = this.$route.query.deviceBindFlg
      this.xtyType = this.$route.query.xtyType   //1是好糖，2是mmc血糖仪
      this.caseTabId = this.$route.query.tabId || 1
      this.init()

      this.$nextTick(() => {

        this.screenHeight = window.screen.height
        this.topHeight = document.getElementById("tab").offsetHeight + document.getElementById("card").offsetHeight
        this.contentHeight;

        $(window).scroll(function () {
          that.debounce(that.setScoll(), 13444)
        });

      })
    },
    filters: {
      formatStatus(val) {
        switch (val) {
          case 1:
            return '空腹'
            break
          case 2:
            return '早餐后'
            break
          case 3:
            return '午餐前'
            break
          case 4:
            return '午餐后'
            break
          case 5:
            return '晚餐前'
            break
          case 6:
            return '晚餐后'
            break
          case 7:
            return '睡前'
            break
          case 8:
            return '凌晨'
            break
          default:
            return '其他'
        }
      }
    },
    methods: {
      setScoll() {
        this.noDoubleTap(() => {
          this.contentHeight = document.getElementById("wrapper").offsetHeight

          if (this.contentHeight - this.topHeight + 120 > this.screenHeight) {
            //获取 id="course_container" 元素，offsetTop是当前元素·距离网页窗口顶部的距离
            var offset_top = document.getElementById("course_container").offsetTop
            console.log("tab`距离顶部的距离：" + offset_top)
            this.offset_top = offset_top
            //获取垂直滚动的距离（scrollTop()是从顶部开始滚动产生的距离）
            var scroll_top = $(document).scrollTop();
            console.log("滚动的距离:" + scroll_top)

            if (scroll_top > offset_top) {
              console.log("fixed success")
              // 到达顶部位置，动态的添加元素属性，并给元素添加相应的元素样式
              document.getElementById("switch_tab").classList.add("switch_title_fixed");
            } else {
              // 同理，把之前添加的元素移除即可
              document.getElementById("switch_tab").classList.remove("switch_title_fixed");
            }
          } else {
            document.getElementById("switch_tab").classList.remove("switch_title_fixed");
          }
        })
      },
      debounce(fn, wait) {
        var timer = null;
        return function () {
          var context = this
          var args = arguments
          if (timer) {
            clearTimeout(timer);
            timer = null;
          }
          timer = setTimeout(function () {
            fn.apply(context, args)
          }, wait)
        }
      },
      throttle(fn, wait) {
        var pre = Date.now();
        return function () {
          var context = this;
          var args = arguments;
          var now = Date.now();
          if (now - pre >= wait) {
            fn.apply(context, args);
            pre = Date.now();
          }
        }
      },
      /**
       * 初始化
       */
      init() {
        let that = this
        getHealthUnlock().then((res) => {
          if (res.status === 0) {
            that.seven_day_statistics = res.data.seven_day_statistics
            that.trend_statistics = res.data.trend_statistics
            that.bg_table = res.data.bg_table
            that.level_name = res.data.level_name
          }
        })
        getBgRecord(undefined).then(function (res) {
          if (res.status === 0) {
            that.bgInfo = res.data.bg_distribution
            that.bgInfo.measure_at = moment(res.data.bg_distribution.measure_at).format('YYYY/MM/DD')
          }
        })
      },
      navUrlToDevice() {
        this.noDoubleTap(() => {
          wx.miniProgram.navigateTo({
            url: '/pages/pressure/page/device/manage'
          })
        })
      },
      navUrlTo() {
        this.noDoubleTap(() => {
          // wx.miniProgram.navigateTo({
          //   url: '/pages/pressure/page/measurementBS/measurementBS'
          // })
          if (this.deviceBindFlg == 1) {

            if(this.xtyType == 1){
              // 这是好糖血糖仪设备码
              wx.miniProgram.navigateTo({
                url: '/pages/pressure/page/autoMeasurementBS/autoMeasurementBS'
              })
            } else if(this.xtyType == 2) {
              // 这是MMC血糖仪设备码
              wx.miniProgram.navigateTo({
                url: '/pages/pressure/page/newMeasurementBS/newMeasurementNextBS'
              })
            }

          } else {
            wx.miniProgram.navigateTo({
              url: '/pages/pressure/page/measurementBS/measurementBS'
            })
          }
        })
      },
      navUrlToHealthPower() {
        this.noDoubleTap(() => {
          wx.miniProgram.navigateTo({
            url: '/pages/home/<USER>/healthPower/healthPower'
          })
        })
      },
      navUrlToBp() {
        this.noDoubleTap(() => {
          wx.miniProgram.redirectTo({
            url: '/pages/pressure/page/pressureIndex/pressureIndex'
          })
        })
      },
      navUrlToBmi() {
        this.noDoubleTap(() => {
          wx.miniProgram.redirectTo({
            url: '/pages/pressure/page/heightWeightRecord/heightWeightRecord'
          })
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  .wrapper {

    .tab {
      display: flex;
      align-items: center;
      justify-content: space-evenly;

      .tab-item {
        display: flex;
        padding: 20px 0;
        align-items: center;
        justify-content: center;
        border-bottom: 1px solid #fff;

        .tab-icon1 {
          width: 20px;
        }

        .tab-icon2 {
          width: 20px;
        }
        .tab-icon3 {
          width: 20px;
        }

        .tab-name {
          color: #333;
          font-size: 17px;
          font-weight: 500;
          margin-left: 7px;
        }
      }

      .tab-item-use {
        border-bottom: 1px solid #F67710;

        .tab-name-use {
          color: #F67710;
        }
      }
    }

    .bg {
      margin-top: 15px;

      .card {
        margin: 0 10px;
        border-radius: 10px;
        box-shadow: 0px 6px 24px 0px rgba(36, 36, 36, 0.1);

        .card-title {
          height: 60px;
          border-top-left-radius: 10px;
          border-top-right-radius: 10px;
          display: flex;
          padding: 0 20px;
          align-items: center;
          justify-content: space-between;
          background: linear-gradient(90deg, rgba(255, 165, 85, 1) 0%, rgba(247, 125, 26, 1) 100%);

          .title-name {
            color: #fff;
            font-size: 23px;
            font-weight: 400;
          }

          .title-date {
            color: #fff;
            font-size: 13px;
            font-weight: 300;

            span:last-of-type {
              margin-left: 10px;
            }
          }
        }

        .card-info {
          padding: 30px 0 25px;
          .noDataImg {
            width: 104px;
            height: 105px;
            display: block;
            margin: 0 auto;
          }
          .info-status {
            color: #333;
            font-size: 15px;
            font-weight: 400;
          }

          .info-value {
            font-size: 50px;
            margin-top: 16px;
            font-weight: 400;
            color: #F67710;
          }
        }

        .card-result {
          border-bottom-right-radius: 10px;
          border-bottom-left-radius: 10px;
          min-height: 26px;
          line-height: 24px;
          position: relative;
          background: #FFF8F2;
          padding: 17px 26px;

          .result-text {
            font-size: 16px;
            color: #F67710;
            font-weight: 400;
          }
          span {
            font-size: 17px;
            font-weight: 400;
            color: #F3955F;
            line-height: 24px;
            position: absolute;
            right: 18px;
            top: 15px;
          }
          .result-add {
            font-size: 52px;
            color: #F67710;
            transform: translateY(-50%);
            position: absolute;
            right: 23px;
            top: -11px;
          }
        }
      }

      .bind {
        height: 60px;
        display: flex;
        margin: 0 10px;
        padding: 0 10px;
        margin-top: 20px;
        align-items: center;
        background: #FFECDD;
        justify-content: space-between;
        border-radius: 10px;

        .bind-info {
          display: flex;
          align-items: center;

          .bind-icon {
            width: 15px;
          }

          .bind-name {
            font-size: 15px;
            font-weight: 400;
            margin-left: 6px;
            color: #474747;
          }
        }

        .bind-btn {
          width: 70px;
          height: 30px;
          color: #FFF;
          font-size: 15px;
          font-weight: 400;
          line-height: 30px;
          border-radius: 20px;
          background: #FF8E00;
        }
      }

      .case {
        padding: 15px 10px;
        background-color: #F3F3F3;

        .case-tips {
          height: 19px;
          color: #999;
          font-size: 15px;
          font-weight: 400;
          line-height: 19px;
        }

        .case-box {
          overflow: hidden;
          margin-top: 15px;
          border-radius: 5px;

          .case-tab {
            display: flex;
            background: #fff;
            align-items: center;
            justify-content: space-between;

            .case-tab-item {
              color: #333;
              padding: 0 4px;
              font-size: 16px;
              font-weight: 400;
              line-height: 44px;
              border-bottom: 2px solid #fff;
            }

            .case-tab-item-active {
              color: #FF7C35;
              border-bottom: 2px solid #FF7C35;
            }
          }

          .switch_title_fixed {
            width: 375px !important;
            animation: searchTop .5s ease-in-out;
            position: fixed;
            margin-top: 0;
            z-index: 100;
            top: 0;
            left: 0;
            box-shadow: 0px 2px 1px rgba(0, 0, 0, .2);
          }

          .case-group {
            overflow: hidden;
          }
        }
      }
    }
  }

  .bg_sample {
    .pt20 {
      padding-top: 8px;
    }
    padding-top: 10px;
    background: #fff;
    border-radius: 4px;
    .level_box {
      margin: 0 10px;
      padding: 10px;
      background: #F5F5F5;
      .level_title {
        font-size: 17px;
        font-weight: 400;
        color: #4A4A4A;
        line-height: 24px;
      }
      .level_tip {
        font-size: 15px;
        font-weight: 400;
        color: #999999;
        line-height: 21px;
      }
      .submit {
        margin-bottom: 20px;
        .btn {
          font-size: 18px;
          font-weight: 400;
          color: #FFFFFF;
          line-height: 25px;
          padding: 7px 40px;
          background: linear-gradient(270deg, #FC9954 0%, #FF8101 100%);
          border-radius: 6px;
        }
      }

    }
    img {
      width: 100%;
    }

  }
</style>
