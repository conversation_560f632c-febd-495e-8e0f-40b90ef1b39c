<template>
  <div class="wrapper">
    <div class="card">
      <div class="card-title">
        <span class="title-name">{{heightInfo.normal_text}}</span>
        <div class="title-date">
          <span>{{heightInfo.date}}</span>
        </div>
      </div>
      <div class="card-info">
        <div class="info-group">
          <p class="info-group-name">体重 kg</p>
          <p class="info-group-val">{{heightInfo.weight==undefined?'-':heightInfo.weight}}</p>
        </div>
        <div class="info-group">
          <p class="info-group-name">身高 cm</p>
          <p class="info-group-val">{{heightInfo.height==undefined?'-':heightInfo.height}}</p>
        </div>
        <div class="info-group">
          <p class="info-group-name">BMI kg/m²</p>
          <p class="info-group-val">{{heightInfo.bmi==undefined?'-':heightInfo.bmi}}</p>
        </div>
      </div>
      <div class="card-result">
        <p class="result-text">{{heightInfo.normal_advice}}</p>
        <van-icon name="add" @click="navUrlTo" class="result-add"/>
      </div>
    </div>
    <div :class="{'content': true, 'content-tab1': tabId === 1}">
      <div class="tab">
        <span
          @click="tabId = 1"
          :class="{'tab-item': true, 'tab-item-active': tabId === 1}"
        >21天报告</span>
        <span
          @click="tabId = 2"
          :class="{'tab-item': true, 'tab-item-active': tabId === 2}"
        >明细记录</span>
      </div>
      <!-- 21天报告 -->
      <div v-if="tabId === 1" class="report">
        <div class="report-box">
          <div class="report-chart" ref="reportChart"></div>
        </div>
        <div class="report-tab">
          <span
            @click="reportTabId = 1"
            :class="{'report-tab-item': true, 'report-tab-item-active': reportTabId === 1}"
          >体重</span>
          <span
            @click="reportTabId = 2"
            :class="{'report-tab-item': true, 'report-tab-item-active': reportTabId === 2}"
          >体质指数(BMI)</span>
        </div>
      </div>
      <!-- 明细记录 -->
      <div v-if="tabId === 2" class="detail">
        <div v-if="heightList.length!=0" class="detail-box" :name="item"
             :key="index" v-for="(item, index) in heightList">
          <p class="detail-date"
          >{{item.group_time}}</p>
          <div class="detail-info">

            <div class="detail-group" v-for="(subItem, subIndex) in item.data"
                 :name="subItem"
                 :key="subIndex" @click="editBmi(item.group_time,subItem.id)">
              <div class="detail-val-box">
                <div class="detail-val">
                  <p class="detail-num">{{subItem.height}}</p>
                  <p class="detail-unit">cm</p>
                </div>
              </div>
              <div class="detail-val-box">
                <div class="detail-val">
                  <p class="detail-num">{{subItem.weight}}</p>
                  <p class="detail-unit">kg</p>
                </div>
              </div>
              <div class="detail-last">
                <span class="detail-time">{{subItem.time}}</span>
                <img src="./imgs/edit.png" class="detail-icon" v-if="getToday==item.group_time">
              </div>
            </div>
          </div>
        </div>
        <div v-if="heightList.length==0" class="noDataBox">
          <img src="./imgs/kongshuju.png" alt="">
          <div>暂时没有数据呢</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import echarts from 'echarts'
  import wx from 'weixin-js-sdk'
  import moment from 'moment'
  import {getHeightInfo, getHeightChat, getHeightList} from '@/api/heightManage'
  import {getClientHeight, getScrollTop, getScrollHeight} from '@/utils/getHight.js'

  export default {
    data: () => {
      return {
        sourceType:'',
        tabId: 2, // 1: 21天报告; 2: 明细记录
        reportTabId: 1, // 1: 体重; 2: 体质指数（BMI）
        weightChartData: [],
        BMIChartData: [],
        chartTime: [],
        heightList: [],
        heightOldList: [],
        pageSize: 10,
        totalPage: 1,
        isClientBottom: false,
        heightInfo: {}
      }
    },
    mounted() {
      // 判断是否滑到底部
      this.checkScroll()
    },
    watch: {
      $route(to, from) {
        // 初始化
        this.init()
        return false
      },
      reportTabId(newVal, oldVal) {
        let chartData = []
        let unit = ''

        if (newVal === 1) {
          unit = 'kg'
          chartData = this.weightChartData
        } else if (newVal === 2) {
          unit = 'kg/m²'
          chartData = this.BMIChartData
        }

        // 折线图初始化
        this.$nextTick(() => {
          this.lineChartInit('reportChart', chartData, this.chartTime, unit)
        })
      },
      tabId(newVal, oldVal) {
        if (newVal === 1) {
          let chartData = []
          let unit = ''

          if (this.reportTabId === 1) {
            unit = 'kg'
            chartData = this.weightChartData
          } else if (this.reportTabId === 2) {
            unit = 'kg/m²'
            chartData = this.BMIChartData
          }

          // 折线图初始化
          this.$nextTick(() => {
            this.lineChartInit('reportChart', chartData, this.chartTime, unit)
          })
        }
      },
      isClientBottom(newVal, oldVal) {
        // 滑倒底部并且还有更多报告时，以及不在加载中（防止重复加载）
        if (newVal) {
          // 获取往期报告列表
          let pageNo = parseInt(this.pageNo)
          let totalPage = parseInt(this.totalPage)
          if (pageNo < totalPage) {
            this.pageNo = this.pageNo + 1
            this.getHeightList(this.pageNo)
          }
        }
      }
    },
    computed: {
      getToday() {
        console.log(moment().format('YYYY-MM-DD'), 123)
        return moment().format('YYYY-MM-DD')
      }
    },
    created() {
      this.sourceType = this.$route.query.source_type
      // 初始化
      this.init()
    },
    methods: {
      navUrlTo() {
        this.noDoubleTap(() => {
          if(this.sourceType==5){
            wx.miniProgram.navigateTo({
              url: '/pages/newM90/page/height/BMIEdit/BMIEdit'
            })
          }else{
            wx.miniProgram.navigateTo({
              url: '/pages/pressure/page/BMIEdit/BMIEdit'
            })
          }
        })
      },
      /**
       * 初始化
       */
      init() {
        this.getHeight()
      },
      formatList(measureList, key) {
        var map = {}
        var dest = []
        for (var i = 0; i < measureList.length; i++) {
          var ai = measureList[i]
          if (!map[ai[key]]) {
            dest.push({
              [key]: ai[key],
              data: [ai]
            })
            map[ai[key]] = ai
          } else {
            for (var j = 0; j < dest.length; j++) {
              var dj = dest[j]
              if (dj[key] == ai[key]) {
                dj.data.push(ai)
                break
              }
            }
          }
        }
        return dest
      },
      editBmi(day, id) {
        this.noDoubleTap(() => {
          if(this.sourceType==5){
            return
          }
          if (this.getToday == day) {
            wx.miniProgram.navigateTo({
              url: '/pages/pressure/page/BMIEdit/BMIEdit?id=' + id
            })
          }
        })
      },
      getHeight() {
        let that = this
        getHeightInfo().then(function (res) {
          if (res.status === 0) {
            that.heightInfo = res.data
          }
        })
        getHeightChat().then(function (res) {
          if (res.status === 0) {
            let chartDate = res.data.chart_data
            let dataList = res.data.data
            let filterItem
            chartDate.forEach((item) => {
              filterItem = dataList.filter((subItem) => {
                return item === subItem.date
              })

              if (filterItem.length > 0) {
                that.weightChartData.push([item, filterItem[0].weight])
                that.BMIChartData.push([item, filterItem[0].bmi])
              }
            })
            console.log(that.weightChartData, 'that.weightChartData')
            console.log(that.BMIChartData, 'that.BMIChartData')
            that.chartTime = chartDate
          }
        })
        this.getHeightList()
      },
      getHeightList(PageNo = 1) {
        let that = this
        getHeightList({page_no: PageNo}).then(function (res) {
          if (res.status === 0) {
            let heightList = that.heightOldList
            heightList = heightList.concat(res.data.data)
            that.heightOldList = heightList
            that.heightList = that.formatList(heightList, 'group_time')
            that.totalPage = res.data.total_page
            that.pageNo = res.data.current_page
          }
        })
      },
      /**
       * 折线图初始化
       * @param {String} ref DOM
       * @param {Array} yAxisData y轴数据
       * @param {Array} xAxisData x轴数据
       * @param {String} unit 单位
       */
      lineChartInit(ref, yAxisData, xAxisData, unit) {
        let myEchart = echarts.init(this.$refs[ref])

        let myOption = {
          tooltip: {
            show: true,
            trigger: 'axis'
          },
          grid: {
            top: '30px',
            bottom: '5px',
            right: '15px',
            left: '13px',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            boundaryGap: true,
            axisLabel: {
              interval: 'auto',
              color: '#666',
              rotate: 0
            },
            axisTick: {
              alignWithLabel: true
            },
            axisLine: {
              lineStyle: {
                color: '#ccc'
              }
            },
            data: xAxisData
          },
          yAxis: {
            type: 'value',
            name: unit,
            axisLine: {
              lineStyle: {
                color: '#ccc'
              }
            },
            axisLabel: {
              color: '#666'
            },
            splitLine: {
              lineStyle: {
                color: '#eee'
              }
            }
          },
          series: [
            {
              type: 'line',
              symbol: 'circle',
              symbolSize: 9,
              itemStyle: {
                normal: {
                  color: '#FF8E00',
                  label: {
                    show: true,
                    color: '#333333'
                  },
                  lineStyle: {
                    color: '#ccc',
                    width: 1
                  }
                }
              },
              data: yAxisData
            }
          ]
        }

        myEchart.setOption(myOption)
      },
      checkScroll() {
        let scrollTop = getScrollTop()
        window.onscroll = () => {
          let newSrollTop = getScrollTop() // 当前滚动高度
          let scrollHeight = getScrollHeight() // 文档完整的高度
          let clientHeight = getClientHeight() // 可视范围的高度
          let distance = 2 // 底部间距

          // 如果当前滚动高度大于之前滚动高度 -> 下滑
          if (newSrollTop > scrollTop) {
            // 如果到底部 this.isClientBottom = true；如果不在底部 this.isClientBottom = false
            if (newSrollTop + clientHeight + distance < scrollHeight) {
              console.log('没到底')
              this.isClientBottom = false
            } else {
              console.log('到底了')
              this.isClientBottom = true
            }
          } else {
            this.isClientBottom = false
          }

          // 每次滚动后，把当前滚动高度赋值给之前滚动高度
          scrollTop = newSrollTop
        }
      }
    }
  }
</script>

<style lang="scss" scoped>
  .wrapper {
    width: 100%;
    overflow: hidden;
    background-color: #fff;

    .card {
      overflow: hidden;
      margin: 15px 10px 0;
      border-radius: 10px;
      background-color: #fff;
      box-shadow: 0px 6px 24px 0px rgba(36, 36, 36, 0.1);

      .card-title {
        height: 60px;
        display: flex;
        padding: 0 20px;
        align-items: center;
        justify-content: space-between;
        background: linear-gradient(90deg, rgba(255, 165, 85, 1) 0%, rgba(247, 125, 26, 1) 100%);

        .title-name {
          color: #fff;
          font-size: 23px;
          font-weight: 400;
        }

        .title-date {
          color: #fff;
          font-size: 13px;
          font-weight: 300;

          span:last-of-type {
            margin-left: 10px;
          }
        }
      }

      .card-info {
        display: flex;
        padding: 30px;
        align-items: center;
        justify-content: center;

        .info-group {
          flex: 1;

          &:nth-of-type(2) {
            border-left: 1px solid #EEE;
            border-right: 1px solid #EEE;
          }

          .info-group-name {
            color: #333;
            font-size: 15px;
            font-weight: 400;
          }

          .info-group-val {
            font-size: 34px;
            margin-top: 15px;
            font-weight: 400;
            color: #F67710;
          }
        }
      }

      .card-result {
        position: relative;
        background: #FFF8F2;
        padding: 30px 26px 20px;

        .result-text {
          font-size: 16px;
          color: #F67710;
          font-weight: 400;
          line-height: 23px;
        }

        .result-add {
          font-size: 52px;
          color: #F67710;
          transform: translateY(-50%);
          position: absolute;
          right: 23px;
          top: 0;
        }
      }
    }

    .content {
      background: #FFF;
      padding-bottom: 30px;
      margin: 25px 10px 30px;

      .tab {
        display: flex;
        background: #fff;
        align-items: center;
        justify-content: space-evenly;

        .tab-item {
          color: #333;
          padding: 0 4px;
          font-size: 16px;
          font-weight: 400;
          line-height: 44px;
          border-bottom: 2px solid #fff;
        }

        .tab-item-active {
          color: #FF7C35;
          border-bottom: 2px solid #FF7C35;
        }
      }

      .report {
        padding: 0 10px;
        margin-top: 20px;

        .report-box {
          width: 100%;
          height: 200px;

          .report-chart {
            width: 100%;
            height: 100%;
          }
        }

        .report-tab {
          display: flex;
          margin-top: 20px;
          align-items: center;
          justify-content: space-evenly;

          .report-tab-item {
            width: 117px;
            height: 27px;
            color: #666;
            font-size: 14px;
            font-weight: 400;
            line-height: 29px;
            border-radius: 3px;
            border: 1px solid #E6E6E6;
          }

          .report-tab-item-active {
            color: #F56B12;
            border: 1px solid #F56B12;
          }
        }
      }

      .detail {
        padding: 0 10px;
        margin-top: 20px;

        .detail-box {
          margin-top: 15px;
        }

        .noDataBox{
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          img{
            width: 164px;
            height: 193px;
            margin-top: 20px;
            margin-bottom: 20px;
          }
          div{
            font-size: 17px;
            color: #333;
          }
        }

        .detail-date {
          font-size: 17px;
          text-align: left;
          font-weight: 400;
          color: #2D2D2D;
          padding-left: 15px;
        }

        .detail-info {
          margin-top: 5px;
          padding: 0 15px;
          border-radius: 5px;
          background: #F3F3F3;

          .detail-group {
            display: flex;
            padding: 20px 0;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid #E7E7E7;

            &:last-of-type {
              border-bottom: 0;
            }

            .detail-val-box {
              flex: 1;
              display: flex;

              .detail-val {

                .detail-num {
                  font-size: 20px;
                  color: #2D2D2D;
                  font-weight: 400;
                }

                .detail-unit {
                  flex: 1;
                  font-size: 11px;
                  margin-top: 6px;
                  color: #8E8E8E;
                  font-weight: 400;
                }
              }
            }

            .detail-last {
              display: flex;
              align-items: center;

              .detail-time {
                font-size: 12px;
                color: #8E8E8E;
                font-weight: 400;
              }

              .detail-icon {
                width: 20px;
                margin-left: 12px;
              }
            }
          }
        }
      }
    }

    .content-tab1 {
      border-radius: 10px;
      box-shadow: 0px 6px 24px 0px rgba(36, 36, 36, 0.1);
    }
  }
</style>
