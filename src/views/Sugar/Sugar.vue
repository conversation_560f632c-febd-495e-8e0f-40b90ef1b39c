<template>
  <div class="wrapper">
    <div class="tab">
      <div @click="tabId = 1" :class="{'tab-item': true, 'tab-item-use': tabId === 1}">
        <img v-if="tabId === 1" src="./imgs/<EMAIL>" class="tab-icon1">
        <img v-else src="./imgs/bloodSuger_grey.png" class="tab-icon1">
        <span class="tab-name tab-name-use">血糖</span>
      </div>
      <div @click="navUrlToBp" :class="{'tab-item': true, 'tab-item-use': tabId === 2}">
        <img v-if="tabId === 2" src="./imgs/<EMAIL>" class="tab-icon2">
        <img v-else src="./imgs/bloodPressure_grey.png" class="tab-icon2">
        <span class="tab-name tab-name-use">血压</span>
      </div>
    </div>
    <!-- 血糖 -->
    <div v-if="tabId === 1" class="bg">
      <div class="card">
        <div class="card-title">
          <span class="title-name">{{bgInfo.name}}</span>
          <div class="title-date">
            <span>{{bgInfo.measure_at}}</span>
          </div>
        </div>
        <div class="card-info">
          <p v-if="bgInfo.bg!==undefined" class="info-status">{{bgInfo.dining_status|formatStatus}}</p>
          <p v-if="bgInfo.bg!==undefined" class="info-value">{{bgInfo.bg}}</p>
          <img v-if="bgInfo.bg===undefined" src="./imgs/bsNoData.png" @click="navUrlTo" alt=""
               class="noDataImg">
        </div>
        <div class="card-result" v-if="bgInfo.bg!==undefined">
          <p class="result-text">{{bgInfo.result}}</p>
          <van-icon name="add" @click="navUrlTo" class="result-add"/>
        </div>
      </div>
      <div class="bind" v-if="deviceBindFlg==0">
        <div class="bind-info">
          <img src="./imgs/bangding.png" class="bind-icon">
          <span class="bind-name">绑定血糖仪，测量数据可自动同步</span>
        </div>
        <span class="bind-btn" @click="navUrlToDevice">去绑定</span>
      </div>
      <div class="case">
        <p class="case-tips">说明：血糖测量数据与“MMC管家”APP保持同步</p>
        <div class="case-box">
          <div class="case-tab">
            <span
              @click="caseTabId = 1"
              :class="{'case-tab-item': true, 'case-tab-item-active': caseTabId === 1}"
            >明细记录</span>
            <span
              @click="caseTabId = 2"
              :class="{'case-tab-item': true, 'case-tab-item-active': caseTabId === 2}"
            >8点法统计</span>
            <span
              @click="caseTabId = 3"
              :class="{'case-tab-item': true, 'case-tab-item-active': caseTabId === 3}"
            >趋势统计</span>
            <span
              @click="caseTabId = 4"
              :class="{'case-tab-item': true, 'case-tab-item-active': caseTabId === 4}"
            >七日报告</span>
          </div>
          <!-- 1：明细记录； -->
          <div v-if="caseTabId === 1" class="case-group">
            <detail-record/>
          </div>
          <!-- 2：8点法统计； -->
          <div v-if="caseTabId === 2" class="case-group">
            <eight-dot/>
          </div>
          <!-- 3：趋势统计； -->
          <div v-if="caseTabId === 3" class="case-group">
            <trend-stat/>
          </div>
          <!-- 4：七日报告； -->
          <div v-if="caseTabId === 4" class="case-group">
            <seven-day/>
          </div>
        </div>
      </div>
    </div>
    <!-- 血压 -->
    <div v-if="tabId === 2"></div>
  </div>
</template>

<script>
  import detailRecord from './components/detailRecord.vue'
  import eightDot from './components/eightDot.vue'
  import trendStat from './components/trendStat.vue'
  import sevenDay from './components/sevenDay.vue'
  import wx from 'weixin-js-sdk'
  import {getBgRecord} from '@/api/sugar'

  export default {
    data: () => {
      return {
        tabId: 1, // 1：血糖；2：血压；
        bgInfo: {},
        deviceBindFlg: 0,
        caseTabId: 1 // 1：明细记录；2：8点法统计；3：趋势统计；4：七日报告；
      }
    },
    components: {
      'seven-day': sevenDay,
      'trend-stat': trendStat,
      'eight-dot': eightDot,
      'detail-record': detailRecord
    },
    created() {
      this.deviceBindFlg = this.$route.query.deviceBindFlg
      this.init()
    },
    filters: {
      formatStatus(val) {
        switch (val) {
          case 1:
            return '空腹'
            break
          case 2:
            return '早餐后'
            break
          case 3:
            return '午餐前'
            break
          case 4:
            return '午餐后'
            break
          case 5:
            return '晚餐前'
            break
          case 6:
            return '晚餐后'
            break
          case 7:
            return '睡前'
            break
          case 8:
            return '凌晨'
            break
          default:
            return '其他'
        }
      }
    },
    methods: {
      /**
       * 初始化
       */
      init() {
        let that = this
        getBgRecord(undefined).then(function (res) {
          if (res.status === 0) {
            that.bgInfo = res.data.bg_distribution
          }
        })
      },
      navUrlToDevice() {
        this.noDoubleTap(() => {
          wx.miniProgram.navigateTo({
            url: '/pages/my/page/device/manage'
          })
        })
      },
      navUrlTo() {
        this.noDoubleTap(() => {
          if (this.deviceBindFlg == 1) {
            wx.miniProgram.navigateTo({
              url: '/pages/pressure/page/autoMeasurementBS/autoMeasurementBS'
            })
          } else {
            wx.miniProgram.navigateTo({
              url: '/pages/pressure/page/measurementBS/measurementBS'
            })
          }
        })
      },
      navUrlToBp() {
        this.noDoubleTap(() => {
          wx.miniProgram.redirectTo({
            url: '/pages/pressure/page/BpPressureRecord/BpPressureRecord'
          })
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  .wrapper {

    .tab {
      display: flex;
      align-items: center;
      justify-content: space-evenly;

      .tab-item {
        display: flex;
        padding: 20px 0;
        align-items: center;
        justify-content: center;
        border-bottom: 1px solid #fff;

        .tab-icon1 {
          width: 14px;
        }

        .tab-icon2 {
          width: 20px;
        }

        .tab-name {
          color: #333;
          font-size: 17px;
          font-weight: 500;
          margin-left: 7px;
        }
      }

      .tab-item-use {
        border-bottom: 1px solid #F67710;

        .tab-name-use {
          color: #F67710;
        }
      }
    }

    .bg {
      margin-top: 15px;

      .card {
        margin: 0 10px;
        border-radius: 10px;
        box-shadow: 0px 6px 24px 0px rgba(36, 36, 36, 0.1);

        .card-title {
          height: 60px;
          display: flex;
          padding: 0 20px;
          align-items: center;
          justify-content: space-between;
          background: linear-gradient(90deg, rgba(255, 165, 85, 1) 0%, rgba(247, 125, 26, 1) 100%);

          .title-name {
            color: #fff;
            font-size: 23px;
            font-weight: 400;
          }

          .title-date {
            color: #fff;
            font-size: 13px;
            font-weight: 300;

            span:last-of-type {
              margin-left: 10px;
            }
          }
        }

        .card-info {
          padding: 30px 0 25px;
          .noDataImg {
            width: 104px;
            height: 105px;
            display: block;
            margin: 0 auto;
          }
          .info-status {
            color: #333;
            font-size: 15px;
            font-weight: 400;
          }

          .info-value {
            font-size: 50px;
            margin-top: 16px;
            font-weight: 400;
            color: #F67710;
          }
        }

        .card-result {
          min-height: 26px;
          line-height: 24px;
          position: relative;
          background: #FFF8F2;
          padding: 17px 26px;

          .result-text {
            font-size: 16px;
            color: #F67710;
            font-weight: 400;
          }

          .result-add {
            font-size: 52px;
            color: #F67710;
            transform: translateY(-50%);
            position: absolute;
            right: 23px;
            top: -11px;
          }
        }
      }

      .bind {
        height: 60px;
        display: flex;
        padding: 0 15px;
        margin-top: 20px;
        align-items: center;
        background: #FFECDD;
        justify-content: space-between;

        .bind-info {
          display: flex;
          align-items: center;

          .bind-icon {
            width: 15px;
          }

          .bind-name {
            font-size: 15px;
            font-weight: 400;
            margin-left: 6px;
            color: #474747;
          }
        }

        .bind-btn {
          width: 80px;
          height: 40px;
          color: #FFF;
          font-size: 17px;
          font-weight: 400;
          line-height: 40px;
          border-radius: 20px;
          background: #FF8E00;
        }
      }

      .case {
        padding: 15px 10px;
        background-color: #F3F3F3;

        .case-tips {
          height: 19px;
          color: #999;
          font-size: 15px;
          font-weight: 400;
          line-height: 19px;
        }

        .case-box {
          overflow: hidden;
          margin-top: 15px;
          border-radius: 5px;

          .case-tab {
            display: flex;
            padding: 0 15px;
            background: #fff;
            align-items: center;
            justify-content: space-between;

            .case-tab-item {
              color: #333;
              padding: 0 4px;
              font-size: 16px;
              font-weight: 400;
              line-height: 44px;
              border-bottom: 2px solid #fff;
            }

            .case-tab-item-active {
              color: #FF7C35;
              border-bottom: 2px solid #FF7C35;
            }
          }

          .case-group {
            overflow: hidden;
          }
        }
      }
    }
  }
</style>
