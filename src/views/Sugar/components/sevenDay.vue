<template>
  <div class="wrapper">
    <div class="date-seven">
      <div @click="beforeStage" class="date-arrow">
        <van-icon name="arrow-left" class="arrow-icon"/>
        <span class="arrow-text">上阶段</span>
      </div>
      <div class="date-comp">
        <div class="date-comp-cont">
          <span>{{ dateFormat(startDate, '.') }}</span>
          <b class="date-comp-sign">-</b>
          <span>{{ dateFormat(endDate, '.') }}</span>
          <cc-svg-icon icon-class="date" class="date-comp-icon"/>
        </div>
        <input type="date"
               v-model="endDate"
               :max="dateFormat(new Date())"
               class="date-comp-input">
      </div>
      <div @click="afterStage" class="date-arrow">
        <span class="arrow-text">下阶段</span>
        <van-icon name="arrow" class="arrow-icon"/>
      </div>
    </div>
    <p class="date-tips">根据所选日期往前7日血糖测量报告</p>
    <div class="chart">
      <p class="chart-title">七日总结</p>
      <div class="chart-box" ref="chartBox"></div>
    </div>
    <div class="table-box">
      <table class="table">
        <tr>
          <td>血糖</td>
          <td>正常</td>
          <td>过高</td>
          <td>过低</td>
        </tr>
        <tr>
          <td>次数</td>
          <td class="table-val">{{statistic.normal==0?'-':statistic.normal}}</td>
          <td class="table-val">{{statistic.up==0?'-':statistic.up}}</td>
          <td class="table-val">{{statistic.down==0?'-':statistic.down}}</td>
        </tr>
      </table>
    </div>
    <div class="table-box">
      <table class="table">
        <tr>
          <td>单位</td>
          <td>平均值</td>
          <td>最高值</td>
          <td>最低值</td>
        </tr>
        <tr>
          <td>
            <p>血糖</p>
            <p>（mmol/L）</p>
          </td>
          <td class="table-val">{{statistic.bg_avg==0?'-':statistic.bg_avg}}</td>
          <td class="table-val">{{statistic.bg_max==0?'-':statistic.bg_max}}</td>
          <td class="table-val">{{statistic.bg_min==0?'-':statistic.bg_min}}</td>
        </tr>
      </table>
    </div>
  </div>
</template>

<script>
  import echarts from 'echarts'
  import {getRecordReport} from '@/api/sugar.js'

  export default {
    data: () => {
      return {
        endDate: '',
        statistic: {},
        isLoading: false
      }
    },
    computed: {
      // 开始时间
      startDate() {
        let endDate = this.endDate
        if (isNaN(Date.parse(endDate))) endDate = new Date()
        return this.dateCount(endDate, -6)
      }
    },
    watch: {
      endDate(newVal, oldVal) {
        // 初始化改变时 返回
        if (isNaN(Date.parse(oldVal))) return
        // 如果不是日期格式 返回
        if (isNaN(Date.parse(newVal))) {
          this.endDate = this.dateFormat(new Date())
          return
        }
        // 获取血糖数据
        this.getBgData()
      }
    },
    created() {
      // 初始化
      this.init()
    },
    methods: {
      /**
       * 初始化
       */
      init() {
        this.endDate = this.dateFormat(new Date())
        // 获取血糖数据
        this.getBgData()
      },
      /**
       * 获取血糖数据
       */
      getBgData() {
        this.isLoading = true
        let that = this
        getRecordReport(this.endDate).then(res => {
          if (res.status === 0) {
            that.isLoading = false
            that.statistic = res.data.statistic
            if (res.data.percent.normal_per == 0 && res.data.percent.up_per == 0 && res.data.percent.down_per == 0) {
              // 饼状图初始化
              that.$nextTick(() => {
                that.pieChartInit1('chartBox')
              })
            } else {
              // 饼状图初始化
              that.$nextTick(() => {
                that.pieChartInit('chartBox', res.data.percent.normal_per, res.data.percent.up_per, res.data.percent.down_per)
              })
            }
          } else {
            alert(res.msg)
          }
        })
      },
      /**
       * 日期格式化
       * @param {String | Date} date 日期
       * @param {String} sep 分隔符
       * @return {String} 格式化日期
       */
      dateFormat(date, sep = '-') {
        let oDate = new Date(date)
        let y = oDate.getFullYear()
        let m = oDate.getMonth() + 1
        let d = oDate.getDate()
        if (m < 10) m = `0${m}`
        if (d < 10) d = `0${d}`
        return `${y}${sep}${m}${sep}${d}`
      },
      /**
       * 日期计算
       * @param {String} date 日期
       * @param {Number} index 未来或过去的天数（正数未来，负数过去）
       * @param {String} sep 分隔符
       */
      dateCount(date, index, sep = '-') {
        let oDate = new Date(date)
        // 天数转换为毫秒数
        let oMin = index * 24 * 60 * 60 * 1000
        let newDate = new Date(oDate.getTime() + oMin)
        return this.dateFormat(newDate, sep)
      },
      /**
       * 上阶段
       */
      beforeStage() {
        let that = this
        this.noDoubleTap(() => {
          if (this.isLoading) return
          that.endDate = that.dateCount(that.startDate, -1)
          setTimeout(() => {
            that.$btnDisabled = true
          }, 2000)
        }, false)
      },
      /**
       * 下阶段
       */
      afterStage() {
        let that = this
        this.noDoubleTap(() => {
          if (that.isLoading) return

          // 开始时间为计算属性，因此从开始日期推未来推13天
          let oDate = that.dateCount(that.startDate, 13)
          let newDate = new Date(oDate)
          let todayDate = new Date()
          if (newDate.getTime() > todayDate.getTime()) oDate = that.dateFormat(todayDate)
          that.endDate = oDate
          setTimeout(() => {
            that.$btnDisabled = true
          }, 2000)
        }, false)
      },
      /**
       * 饼状图初始化
       * @param {String} ref DOM
       * @param {Number} normal 正常次数
       * @param {Number} high 过高次数
       * @param {Number} low 过低次数
       */
      pieChartInit(ref, normal, high, low) {
        let colorList = []
        let legendName = []
        let resultData = []
        if (normal !== 0) {
          colorList.push('#07C160')
          legendName.push('正常')
          resultData.push({value: normal, name: '正常'})

        }
        if (high !== 0) {
          colorList.push('#F5222D')
          legendName.push('过高')
          resultData.push({value: high, name: '过高'})
        }
        if (low !== 0) {
          colorList.push('#7B62FF')
          legendName.push('过低')
          resultData.push({value: low, name: '过低'})
        }
        console.log(colorList, legendName, resultData)
        let myEcharts = echarts.init(this.$refs[ref])
        myEcharts.setOption({
          color: colorList,
          legend: {
            y: 'bottom',
            textStyle: {
              fontSize: 10
            },
            data: legendName,
            itemWidth: 7,
            itemHeight: 7
          },
          series: [
            {
              type: 'pie',
              silent: true,
              radius: ['70%', '45%'],
              avoidLabelOverlap: false,
              label: {
                normal: {
                  formatter: '{b}:{c}%'
                }
              },
              data: resultData,
              itemStyle: {
                borderWidth: 3,
                borderColor: '#fff',
                emphasis: {
                  shadow: 10,
                  shadowOffsetX: 0,
                  shadowColor: '#rgba(0,0,0,0.5)'
                }
              }
            }
          ]
        })
      },
      // 空数据的饼图
      pieChartInit1(ref) {
        let myEcharts = echarts.init(this.$refs[ref])
        myEcharts.setOption({
          color: ['#e5e5e5'],
          legend: {
            y: 'bottom',
            textStyle: {
              fontSize: 10
            },
            data: ['直接访问'],
            itemWidth: 7,
            itemHeight: 7
          },
          series: [
            {
              type: 'pie',
              silent: true,
              radius: ['70%', '45%'],
              avoidLabelOverlap: false,
              label: {
                normal: {
                  formatter: '{b}:{c}%'
                }
              },
              data: [{value: 0, name: '空数据'}],
              itemStyle: {
                borderWidth: 3,
                borderColor: '#fff',
                emphasis: {
                  shadow: 10,
                  shadowOffsetX: 0,
                  shadowColor: '#rgba(0,0,0,0.5)'
                }
              }
            }
          ]
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  .wrapper {
    overflow: hidden;
    padding: 10px 15px;
    background: #fff;

    .date-seven {
      height: 28px;
      display: flex;
      align-items: center;
      justify-content: center;

      .date-arrow {
        width: 60px;
        display: flex;
        color: #333333;
        align-items: center;
        justify-content: space-between;

        .arrow-text {
          font-size: 13px;
        }

        .arrow-icon {
          font-size: 18px;
        }
      }

      .date-comp {
        width: 200px;
        margin: 0 8px;
        color: #F67710;
        position: relative;

        .date-comp-cont {
          height: 25px;
          display: flex;
          font-size: 13px;
          color: #F67710;
          border-radius: 5px;
          align-items: center;
          justify-content: center;
          border: 1px solid #ffecdd;

          .date-comp-sign {
            margin: 0 2px;
          }

          .date-comp-icon {
            margin-left: 5px;
          }
        }

        .date-comp-input {
          width: 200px;
          height: 25px;
          font-size: 13px;
          text-align: center;
          border-radius: 5px;
          border: 1px solid #ffecdd;
          position: absolute;
          top: 0;
          left: 0;
          opacity: 0;
        }
      }
    }

    .date-tips {
      font-size: 13px;
      color: #999999;
      margin-top: 10px;
    }

    .chart {
      width: 100%;
      height: 200px;
      margin-top: 20px;
      position: relative;
      .chart-title {
        color: #333;
        font-size: 16px;
        text-align: left;
        font-weight: 600;
        line-height: 20px;
      }

      .chart-box {
        width: 100%;
        height: 180px;
      }
      .noDataTiPS {
        position: absolute;
        bottom: 0;
        right: 0;
        top: 0;
        left: 0;
        margin: auto;
        font-size: 12px;
        color: #666;
        border: 14px #ddd solid;
        width: 100px;
        height: 100px;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        div {
          margin-top: 10px;
        }
      }
    }

    .table-box {
      margin-top: 10px;

      .table {
        width: 100%;

        td {
          width: 25%;
          height: 40px;
          color: #666;
          font-size: 14px;
          font-weight: 400;
          vertical-align: middle;
          border: 1px solid #E6E6E6;
        }

        .table-val {
          color: #333;
          font-size: 17px;
          font-weight: normal;
        }
      }
    }
  }
</style>
