<template>
  <div class="wrapper">
    <div class="content">
      <div class="date-seven">
        <div @click="beforeStage" class="date-arrow">
          <van-icon name="arrow-left" class="arrow-icon"/>
          <span class="arrow-text">上阶段</span>
        </div>
        <div class="date-comp">
          <div class="date-comp-cont">
            <span>{{ dateFormat(startDate, '.') }}</span>
            <b class="date-comp-sign">-</b>
            <span>{{ dateFormat(endDate, '.') }}</span>
            <cc-svg-icon icon-class="date" class="date-comp-icon"/>
          </div>
          <input
            type="date"
            v-model="endDate"
            :max="dateFormat(new Date())"
            class="date-comp-input"
          >
        </div>
        <div @click="afterStage" class="date-arrow">
          <span class="arrow-text">下阶段</span>
          <van-icon name="arrow" class="arrow-icon"/>
        </div>
      </div>
      <p class="date-tips">根据所选日期往前7日血糖测量报告</p>
      <div class="tab">
        <span v-for="tab in tabList"
              :key="tab.id"
              @click="changeTab(tab)"
              :class="{'tab-item': 1, 'tab-item-select': tab.select}"
        >{{tab.name}}</span>
      </div>
      <div class="chart">
        <div class="chart-box" ref="chartBox"></div>
      </div>
      <div v-if="allDate" class="all-date">
        <span
          v-for="(item, index) in weekDate"
          :key="index"
          @click="showSingle(item.date, item.year)"
          class="chart-date"
        >{{ item.date }}</span>
      </div>
    </div>
    <!-- 单日趋势图 -->
    <van-dialog
      v-model="singleDialog"
      :show-confirm-button="false"
      :close-on-click-overlay="true"
      class="single-dialog"
    >
      <div class="single-wrapper">
        <h1 class="single-title">{{singleTitle}}</h1>
        <div class="single-chart">
          <div class="chart-box" ref="singleChartBox"></div>
        </div>
        <div @click="closeSingle" class="single-footer">
          <span class="single-footer-btn">关闭</span>
        </div>
      </div>
    </van-dialog>
  </div>
</template>

<script>
  import echarts from 'echarts'
  import {bgQSGraphData, getBgDateRecords} from '@/api/sugar.js'
  import {Toast} from 'vant'
  import moment from 'moment'

  export default {
    data: () => {
      return {
        tabList: [
          {
            id: 0,
            name: '全部',
            select: true
          },
          {
            id: 1,
            name: '空腹',
            select: false
          },
          {
            id: 2,
            name: '早餐后',
            select: false
          },
          {
            id: 3,
            name: '午餐前',
            select: false
          },
          {
            id: 4,
            name: '午餐后',
            select: false
          },
          {
            id: 5,
            name: '晚餐前',
            select: false
          },
          {
            id: 6,
            name: '晚餐后',
            select: false
          },
          {
            id: 7,
            name: '睡前',
            select: false
          },
          {
            id: 8,
            name: '凌晨',
            select: false
          }
        ],
        endDate: '',
        dinStatus: 0,
        chartData: [],
        chartDate: [],
        weekDate: [],
        singleDialog: false,
        singleTitle: '',
        singleData: [],
        singleDate: [],
        isLoading: false
      }
    },
    computed: {
      // 全部状态下显示7日按钮开关
      allDate() {
        return this.tabList[0].select
      },
      // 开始时间
      startDate() {
        let endDate = this.endDate
        if (isNaN(Date.parse(endDate))) endDate = new Date()
        return this.dateCount(endDate, -6)
      }
    },
    watch: {
      endDate(newVal, oldVal) {
        // 全部日期
        let weekDate = []
        for (var i = 6; i >= 0; i--) {
          let oDate = moment(this.startDate).add(i, 'days').format('YYYY/MM/DD')
          let year = oDate.slice(0, 5)
          let date = oDate.slice(5)
          weekDate.push({year, date})
        }
        this.weekDate = weekDate.reverse()
        // 初始化改变时 返回
        if (isNaN(Date.parse(oldVal))) return
        // 如果不是日期格式 返回
        if (isNaN(Date.parse(newVal))) {
          this.endDate = this.dateFormat(new Date())
          return
        }
        // 获取血糖趋势
        this.getBgGraphData()
      }
    },
    created() {
      // 初始化
      this.init()
    },
    methods: {
      /**
       * 初始化
       */
      init() {
        this.endDate = this.dateFormat(new Date())
        // 获取血糖趋势
        this.getBgGraphData()
      },
      /**
       * 获取血糖趋势
       */
      getBgGraphData() {
        this.isLoading = true
        let that = this
        bgQSGraphData(this.endDate, this.dinStatus).then(res => {
          if (res.status === 0) {
            that.chartDate = res.data.date
            that.chartData = res.data.bg
            // that.chartData = []
            // that.chartDate.forEach((item, index) => {
            //   console.log(item, index)
            //   if (res.data.bg[index] != '') {
            //     that.chartData.push([item, res.data.bg[index]])
            //   }
            // })
            console.log(that.chartData, 1324)
            // that.chartData = res.data.bg

            that.$nextTick(() => {
              // 折线图初始化
              that.lineChartInit('chartBox', that.chartData, that.chartDate, that.dinStatus)
            })
            that.isLoading = false
          } else {
            alert(res.msg)
          }
        })
      },
      /**
       * 日期格式化
       * @param {String | Date} date 日期
       * @param {String} sep 分隔符
       * @return {String} 格式化日期
       */
      dateFormat(date, sep = '-') {
        let oDate = new Date(date)
        let y = oDate.getFullYear()
        let m = oDate.getMonth() + 1
        let d = oDate.getDate()
        if (m < 10) m = `0${m}`
        if (d < 10) d = `0${d}`
        return `${y}${sep}${m}${sep}${d}`
      },
      /**
       * 日期计算
       * @param {String} date 日期
       * @param {Number} index 未来或过去的天数（正数未来，负数过去）
       * @param {String} sep 分隔符
       */
      dateCount(date, index, sep = '-') {
        let oDate = new Date(date)
        // 天数转换为毫秒数
        let oMin = index * 24 * 60 * 60 * 1000
        let newDate = new Date(oDate.getTime() + oMin)
        return this.dateFormat(newDate, sep)
      },
      /**
       * 改变tab
       */
      changeTab(tab) {
        // 已经聚焦的tab 或者 正在加载中 直接返回
        if (tab.select || this.isLoading) return

        this.tabList.forEach(item => {
          item.select = false
          if (item.id === tab.id) {
            item.select = true
            this.dinStatus = item.id
          }
        })
        // 获取血糖趋势
        this.getBgGraphData()
      },
      /**
       * 折线图初始化
       * @param {String} ref DOM
       * @param {Array} yAxisData y轴数据
       * @param {Array} xAxisData x轴数据
       * @param {String} type 类型（0：全部; -1:单日趋势图）
       */
      lineChartInit(ref, yAxisData, xAxisData, type = 0) {
        let myEchart = echarts.init(this.$refs[ref])
        let minData = []
        let maxData = []
        if (type === 1) {
          maxData = [[0, 2.6], [1, 2.6], [2, 2.6], [3, 2.6], [4, 2.6], [5, 2.6], [6, 2.6]]
          minData = [[0, 4.4], [1, 4.4], [2, 4.4], [3, 4.4], [4, 4.4], [5, 4.4], [6, 4.4]]
        } else if (type > 1) {
          maxData = [[0, 5.6], [1, 5.6], [2, 5.6], [3, 5.6], [4, 5.6], [5, 5.6], [6, 5.6]]
          minData = [[0, 4.4], [1, 4.4], [2, 4.4], [3, 4.4], [4, 4.4], [5, 4.4], [6, 4.4]]
        }

        let myOption = {
          // tooltip: {
          //   show: true,
          //   trigger: 'axis'
          // },
          grid: {
            top: '30px',
            bottom: '5px',
            right: '15px',
            left: '10px',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            boundaryGap: true,
            axisLabel: {
              fontSize: 0,
              interval: 0,
              color: 'rgba(0,23,11,0)',
              rotate: 0
            },
            axisTick: {
              show: type !== 0,
              alignWithLabel: true
            },
            axisLine: {
              lineStyle: {
                color: '#ccc'
              }
            },
            data: xAxisData
          },
          yAxis: {
            type: 'value',
            name: 'mmol/L',
            axisLine: {
              lineStyle: {
                color: '#ccc'
              }
            },
            axisLabel: {
              color: '#666'
            },
            splitLine: {
              lineStyle: {
                color: '#eee'
              }
            }
          },
          series: [
            {
              type: 'line',
              symbol: 'circle',
              symbolSize: 9,
              itemStyle: {
                normal: {
                  color: '#FF8E00',
                  label: {
                    show: true,
                    color: '#333333'
                  },
                  lineStyle: {
                    color: '#F67710',
                    width: 1
                  }
                }
              },
              data: yAxisData
            },
            {
              type: 'line',
              name: 'min',
              stack: '正常',
              symbol: 'none',
              lineStyle: {opacity: 0},
              data: minData
            },
            {
              type: 'line',
              name: 'max',
              stack: '正常',
              symbol: 'none',
              lineStyle: {opacity: 0},
              areaStyle: {color: '#B8E986'},
              data: maxData
            }
          ]
        }

        myEchart.setOption(myOption)
      },
      /**
       * 显示单日趋势图
       * @param {String} date 月日('05/13')
       * @param {String} year 年('2020/')
       */
      showSingle(date, year) {
        let oDate = this.dateFormat(year + date)
        let that = this
        // 获取某一天的血糖数据
        getBgDateRecords(oDate).then(res => {
          if (res.status === 0) {
            if (res.data.items.length > 0) {
              that.singleData = []
              that.singleDate = []
              res.data.items.forEach(function (subItem) {
                that.singleData.push(subItem.bg)
                that.singleDate.push(subItem.measure_time)
              })
              that.singleDialog = true
              that.$nextTick(() => {
                that.lineChartInit('singleChartBox', that.singleData, that.singleDate, -1)
              })
            } else {
              Toast('当天无测量数据')
            }
          } else {
            alert(res.msg)
          }
        })
        this.singleTitle = `${date}血糖趋势变化`
      },
      /**
       * 关闭单日趋势图
       */
      closeSingle() {
        this.singleDialog = false
        this.singleTitle = ''
        this.singleData = []
        this.singleDate = []
      },
      /**
       * 上阶段
       */
      beforeStage() {
        if (this.isLoading) return

        this.endDate = this.dateCount(this.startDate, -1)
      },
      /**
       * 下阶段
       */
      afterStage() {
        if (this.isLoading) return

        // 开始时间为计算属性，因此从开始日期推未来推13天
        let oDate = this.dateCount(this.startDate, 13)
        let newDate = new Date(oDate)
        let todayDate = new Date()
        if (newDate.getTime() > todayDate.getTime()) oDate = this.dateFormat(todayDate)
        this.endDate = oDate
      }
    }
  }
</script>

<style lang="scss" scoped>
  .wrapper {
    padding: 10px 15px;
    background: #fff;

    .content {
      overflow: hidden;

      .date-seven {
        height: 28px;
        display: flex;
        align-items: center;
        justify-content: center;

        .date-arrow {
          width: 60px;
          display: flex;
          color: #333333;
          align-items: center;
          justify-content: space-between;

          .arrow-text {
            font-size: 13px;
          }

          .arrow-icon {
            font-size: 18px;
          }
        }

        .date-comp {
          width: 200px;
          margin: 0 8px;
          color: #F67710;
          position: relative;

          .date-comp-cont {
            height: 25px;
            display: flex;
            font-size: 13px;
            color: #F67710;
            border-radius: 5px;
            align-items: center;
            justify-content: center;
            border: 1px solid #ffecdd;

            .date-comp-sign {
              margin: 0 2px;
            }

            .date-comp-icon {
              margin-left: 5px;
            }
          }

          .date-comp-input {
            width: 200px;
            height: 25px;
            font-size: 13px;
            text-align: center;
            border-radius: 5px;
            border: 1px solid #ffecdd;
            position: absolute;
            top: 0;
            left: 0;
            opacity: 0;
          }
        }
      }

      .date-tips {
        font-size: 13px;
        color: #999999;
        margin-top: 10px;
      }

      .tab {
        display: flex;
        flex-wrap: wrap;
        margin-top: 20px;

        .tab-item {
          width: 30%;
          height: 30px;
          font-size: 16px;
          color: #666666;
          font-weight: 400;
          line-height: 30px;
          border-radius: 3px;
          box-sizing: border-box;
          border: 1px solid #E8E8E8;
        }

        .tab-item:not(:nth-of-type(3n)) {
          margin-right: 5%;
        }

        .tab-item:not(:nth-of-type(-n + 3)) {
          margin-top: 10px;
        }

        .tab-item-select {
          color: #FF9600;
          border: 1px solid #FF9600;
        }
      }

      .chart {
        width: 100%;
        height: 180px;
        margin-top: 20px;

        .chart-box {
          width: 100%;
          height: 100%;
        }
      }

      .all-date {
        display: flex;
        padding: 0 15px;
        align-items: center;
        justify-content: space-between;

        .chart-date {
          width: 38px;
          height: 35px;
          color: #333;
          font-size: 15px;
          font-weight: 400;
          line-height: 35px;
          text-align: center;
          border-radius: 4px;
          background: linear-gradient(180deg, #FFF7F1 0%, #FFECDE 100%);
        }
      }
    }

    .single-dialog {
      width: 320px;

      .single-wrapper {
        width: 100%;

        .single-title {
          width: 100%;
          height: 50px;
          color: #333;
          font-size: 20px;
          font-weight: 400;
          line-height: 50px;
        }

        .single-chart {
          width: 100%;
          height: 270px;
          padding: 0 10px;
          box-sizing: border-box;

          .chart-box {
            width: 100%;
            height: 100%;
          }
        }

        .single-footer {
          width: 100%;
          height: 40px;
          line-height: 40px;
          border-top: 1px solid #E8E8E8;

          .single-footer-btn {
            color: #333;
            font-size: 17px;
            font-weight: 400;
          }
        }
      }
    }
  }
</style>
