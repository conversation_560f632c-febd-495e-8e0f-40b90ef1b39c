<template>
  <div class="wrapper">
    <!--<div class="over">-->
    <!--<p class="over-title">血糖目标</p>-->
    <!--<div class="over-mid">-->
    <!--<p class="over-val">空腹/餐前：<span>4.4（含）-7.0（含）</span></p>-->
    <!--<van-icon name="arrow" class="over-icon"/>-->
    <!--</div>-->
    <!--<p class="over-val">餐后2小时：<span>4.4（含）-10.0（不含）</span></p>-->
    <!--</div>-->
    <div class="detail">
      <div v-if="bgList.length!==0" class="detail-group" v-for="(item, index) in bgList" :key="index">
        <p class="detail-date">{{item.measure_day}}</p>
        <div class="detail-box" v-for="(subItem, subIndex) in item.data" :key="subIndex"
             @click="editBg(subItem.measure_type,subItem.id)">
          <div class="detail-record">
            <div class="record-result-box">
              <span :class="['record-result', resultClass(subItem.deviation_status)]">{{subItem.deviation}}</span>
            </div>
            <div class="record-val-box">
              <p class="record-num">{{subItem.bg}}</p>
              <p class="record-unit">mmol/L</p>
            </div>
            <div class="record-src-box">
              <span class="record-src">{{subItem.measure_type_desc}}</span>
            </div>
            <div class="record-status-box">
              <span class="record-status">{{subItem.dinner_status_desc}}</span>
            </div>
            <div class="record-date-box">
              <span class="record-date">{{subItem.measure_time}}</span>
            </div>
            <van-icon name="arrow" class="record-icon"/>
          </div>
          <div class="detail-abnormal" v-if="subItem.deviant_bg.reason_type!==undefined">
            <div class="abnormal-mid">
              <span class="abnormal-mid-name">异常原因：</span>
              <span class="abnormal-mid-val">{{subItem.deviant_bg.reason_type_desc  }}</span>
            </div>
          </div>
        </div>
      </div>
      <div v-if="bgList.length===0" class="noDataBox">
        <img src="../imgs/kongshuju.png" alt="">
        <div>暂时没有数据呢</div>
      </div>
    </div>
  </div>
</template>

<script>
  import { getDetailRecords } from '@/api/sugar.js'
  import { getClientHeight, getScrollTop, getScrollHeight } from '@/utils/getHight.js'
  import moment from 'moment'
  import wx from 'weixin-js-sdk'

  export default {
    data: () => {
      return {
        bgList: [],
        measureList: [],
        pageNo: 1,
        pageSize: 20,
        totalPage: 1,
        isClientBottom: false
      }
    },
    watch: {
      isClientBottom(newVal, oldVal) {
        // 滑倒底部并且还有更多报告时，以及不在加载中（防止重复加载）
        if (newVal) {
          // 获取往期报告列表
          let pageNo = parseInt(this.pageNo)
          let totalPage = parseInt(this.totalPage)
          if (pageNo < totalPage) {
            this.pageNo = this.pageNo + 1
            this.init(this.pageSize, this.pageNo)
          }
        }
      }
    },
    mounted() {
      // 判断是否滑到底部
      this.checkScroll()
    },
    created() {
      // 初始化
      this.init(this.pageSize, this.pageNo)
    },
    filters: {
      formatTime(val) {
        return moment(val).format('HH:mm')
      },
      formatType(val) {
        return 'todo'
      }
    },
    methods: {
      editBg(type, id) {
        if (parseInt(type) == 0) {
          wx.miniProgram.navigateTo({
            url: '/pages/pressure/page/measurementBS/measurementBS?id=' + id
          })
        }
      },
      /**
       * 初始化
       */
      init(pageSize, pageNo) {
        let that = this
        // 获取血糖数据
        getDetailRecords({
          page_no: pageNo,
          page_size: pageSize
        }).then(function (res) {
          if (res.status === 0) {
            let data = res.data
            let measureList = that.measureList
            that.measureList = measureList.concat(data.data)
            that.bgList = that.formatList(that.measureList, 'measure_day')
            that.totalPage = data.total_page
            that.pageNo = data.current_page
          }
        })
      },
      formatList(measureList, key) {
        let map = {}
        let dest = []
        for (let i = 0; i < measureList.length; i++) {
          let ai = measureList[i]
          if (!map[ai[key]]) {
            dest.push({
              [key]: ai[key],
              data: [ai]
            })
            map[ai[key]] = ai
          } else {
            for (let j = 0; j < dest.length; j++) {
              let dj = dest[j]
              if (dj[key] == ai[key]) {
                dj.data.push(ai)
                break
              }
            }
          }
        }
        return dest
      },
      checkScroll() {
        let scrollTop = getScrollTop()
        window.onscroll = () => {
          let newSrollTop = getScrollTop() // 当前滚动高度
          let scrollHeight = getScrollHeight() // 文档完整的高度
          let clientHeight = getClientHeight() // 可视范围的高度
          let distance = 2 // 底部间距

          // 如果当前滚动高度大于之前滚动高度 -> 下滑
          if (newSrollTop > scrollTop) {
            // 如果到底部 this.isClientBottom = true；如果不在底部 this.isClientBottom = false
            if (newSrollTop + clientHeight + distance < scrollHeight) {
              // console.log('没到底')
              this.isClientBottom = false
            } else {
              // console.log('到底了')
              this.isClientBottom = true
            }
          } else {
            this.isClientBottom = false
          }

          // 每次滚动后，把当前滚动高度赋值给之前滚动高度
          scrollTop = newSrollTop
        }
      },
      /**
       * 异常结果class
       * @param {Number} val 结果（1：正常；2：偏低；3：低血糖；4：严重低血糖；5：偏高；6：很高）
       * @return {String} 异常结果class
       */
      resultClass(val) {
        let name = 'record-result-'

        switch (val) {
          case 2:
            name += 'low'
            break
          case 3:
            name += 'lower'
            break
          case 4:
            name += 'lower'
            break
          case 5:
            name += 'high'
            break
          case 6:
            name += 'higher'
            break
          case 1:
          default:
            name = ''
            break
        }

        return name
      }
    }
  }
</script>

<style lang="scss" scoped>
  .wrapper {
    overflow: hidden;

    .over {
      text-align: left;
      padding: 10px 15px;
      background: #fff;
      border-radius: 5px;

      .over-title {
        color: #666;
        font-size: 13px;
      }

      .over-mid {
        display: flex;
        margin: 14px 0;
        align-items: center;
        justify-content: space-between;

        .over-icon {
          color: #666;
          font-size: 14px;
        }
      }

      .over-val {
        color: #333;
        font-size: 14px;
      }
    }

    .detail {
      background-color: #F3F3F3;

      .detail-group {
        margin-top: 15px;

        .detail-date {
          color: #333;
          font-size: 17px;
          text-align: left;
          margin-bottom: 10px;
          padding-left: 15px;
        }

        .detail-box {
          padding: 0 15px;
          background: #fff;
          border-bottom: 1px solid #E9E9E9;

          .detail-record {
            display: flex;
            text-align: left;
            align-items: center;
            padding: 20px 0;
            justify-content: flex-start;
            .record-result-box {
              width: 90px;
              height: 24px;
              /*正常*/
              .record-result {
                font-size: 11px;
                color: #07C160;
                padding: 5px 6px;
                border-radius: 9px;
                vertical-align: super;
                border: 1px solid #07C160;
              }
              /*偏高*/
              .record-result-high {
                color: #F5222D;
                border: 1px solid #F5222D;
              }
              /*很高*/
              .record-result-higher {
                color: #E30000;
                border: 1px solid #E30000;
              }
              /*偏低*/
              .record-result-low {
                color: #7B62FF;
                border: 1px solid #7B62FF;
              }
              /*低血糖和严重低血糖*/
              .record-result-lower {
                color: #517DFF;
                border: 1px solid #517DFF;
              }
            }

            .record-val-box {
              width: 80px;

              .record-num {
                color: #333;
                font-size: 20px;
                padding-left: 4px;
              }

              .record-unit {
                color: #999;
                font-size: 11px;
              }
            }

            .record-src-box {
              width: 50px;
              height: 18px;

              .record-src {
                color: #333;
                font-size: 16px;
                vertical-align: super;
              }
            }

            .record-status-box {
              width: 65px;
              height: 18px;

              .record-status {
                color: #333;
                font-size: 16px;
                vertical-align: super;
              }
            }

            .record-date-box {
              width: 40px;
              height: 20px;

              .record-date {
                color: #999;
                font-size: 14px;
                vertical-align: super;
              }
            }

            .record-icon {
              height: 12px;
              color: #666;
              font-size: 14px;
            }
          }

          .detail-abnormal {
            padding: 0 0 10px;

            .abnormal-top {
              display: flex;
              align-items: center;
              justify-content: space-between;

              .abnormal-val {
                color: #333;
                font-size: 16px;
              }

              .abnormal-date {
                color: #999;
                font-size: 12px;
              }
            }

            .abnormal-mid {
              text-align: left;

              .abnormal-mid-name {
                color: #666;
                font-size: 14px;
              }

              .abnormal-mid-val {
                color: #333;
                font-size: 16px;
              }
            }

            .abnormal-bot {
              display: flex;
              text-align: left;
              align-items: baseline;

              .abnormal-bot-name {
                color: #666;
                font-size: 14px;
              }

              .abnormal-bot-val {
                flex: 1;
                color: #333;
                font-size: 16px;
                line-height: 28px;
              }
            }
          }
        }
      }

      .noDataBox{
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        img{
          width: 164px;
          height: 193px;
          margin-top: 20px;
          margin-bottom: 20px;
        }
        div{
          font-size: 17px;
          color: #333;
        }
      }
    }
  }
</style>
