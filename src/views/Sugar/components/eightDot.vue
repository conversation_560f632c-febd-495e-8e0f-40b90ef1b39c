<template>
  <div class="wrapper">
    <!--<div class="over">-->
    <!--<p class="over-title">血糖目标</p>-->
    <!--<div class="over-mid">-->
    <!--<p class="over-val">空腹/餐前：<span>4.4（含）-7.0（含）</span></p>-->
    <!--<van-icon name="arrow" class="over-icon"/>-->
    <!--</div>-->
    <!--<p class="over-val">餐后2小时：<span>4.4（含）-10.0（不含）</span></p>-->
    <!--</div>-->
    <div class="tips">
      <div class="left">以血糖标准范围判定</div>
      <div class="right">
        <span><cc-svg-icon icon-class="shuidi-lvse" class-name="icon-class"/> 正常</span>
        <span><cc-svg-icon icon-class="shuidi-lanse" class-name="icon-class"/>过低</span>
        <span><cc-svg-icon icon-class="shuidi-chengse" class-name="icon-class"/>过高</span>
      </div>
    </div>
    <div class="sugarTable">
      <div class="tables">
        <div class="tr">
          <div class="th td_center width1">日期</div>
          <div class="th td_center width1">凌晨</div>
          <div class="td width2">
            <div class="tdd2">早餐</div>
            <div class="tdd1 td_center">
              <div class=" thsmall width3">空腹</div>
              <div class=" thsmall  width3 border-none">后</div>
            </div>
          </div>
          <div class="td width2">
            <div class="tdd2">午餐</div>
            <div class="tdd1 td_center">
              <div class=" thsmall width3">前</div>
              <div class=" thsmall  width3 border-none">后</div>
            </div>
          </div>
          <div class="td width2">
            <div class="tdd2">晚餐</div>
            <div class="tdd1 td_center">
              <div class=" thsmall width3">前</div>
              <div class=" thsmall  width3 border-none">后</div>
            </div>
          </div>
          <div class="th td_center width1">睡前</div>
        </div>
        <div v-for="(item, index) in familyGluData" :key="index" class="table">
          <div class="tr" v-if="item.items.dining_status_1!==undefined">
            <span class="thsmall width1 date">{{item.date}}</span>
            <span class="thsmall width1 data"
                  :class="item.items['dining_status_8'].bg | aftercolor">{{item.items['dining_status_8'].bg==undefined ? '--' : item.items['dining_status_8'].bg}}</span>
            <span class="thsmall width4 data"
                  :class="item.items['dining_status_1'].bg | beforecolor">{{item.items['dining_status_1'].bg==undefined  ? '--' : item.items['dining_status_1'].bg}}</span>
            <span class="thsmall width4 data" :class="item.items['dining_status_2'].bg | aftercolor">{{item.items['dining_status_2'].bg==undefined  ? '--' : item.items['dining_status_2'].bg}}</span>
            <span class="thsmall width4 data"
                  :class="item.items['dining_status_3'].bg | aftercolor">{{item.items['dining_status_3'].bg==undefined  ? '--' : item.items['dining_status_3'].bg }}</span>
            <span class="thsmall width4 data" :class="item.items['dining_status_4'].bg | aftercolor">{{item.items['dining_status_4'].bg==undefined ? '--' :item.items['dining_status_4'].bg}}</span>
            <span class="thsmall width4 data"
                  :class="item.items['dining_status_5'].bg | aftercolor">{{item.items['dining_status_5'].bg==undefined  ? '--' : item.items['dining_status_5'].bg}}</span>
            <span class="thsmall width4 data" :class="item.items['dining_status_6'].bg | aftercolor">{{item.items['dining_status_6'].bg==undefined  ? '--' : item.items['dining_status_6'].bg }}</span>
            <span class="thsmall width1 data"
                  :class="item.items['dining_status_7'].bg | aftercolor">{{item.items['dining_status_7'].bg==undefined ? '--' : item.items['dining_status_7'].bg}}</span>
          </div>
          <div class="tr" v-if="item.items.dining_status_1===undefined">
            <span class="thsmall width1 date">{{item.date}}</span>
            <span class="thsmall width1 data">--</span>
            <span class="thsmall width4 data">--</span>
            <span class="thsmall width4 data">--</span>
            <span class="thsmall width4 data">--</span>
            <span class="thsmall width4 data">--</span>
            <span class="thsmall width4 data">--</span>
            <span class="thsmall width4 data">--</span>
            <span class="thsmall width1 data">--</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import { getTableRecords } from '@/api/sugar.js'
  import { getClientHeight, getScrollTop, getScrollHeight } from '@/utils/getHight.js'

  let startQuery = false
  export default {
    data: () => {
      return {
        familyGluData: [],
        endDate: ''
      }
    },
    filters: {
      beforecolor(val) {
        if (val === undefined) {
          return ''
        }
        if (val > 0 && val < 4.4) {
          return 'colorDown'
        } else if (val >= 4.4 && val <= 7 || val == 0) {
          return 'colorNormal'
        } else {
          return 'colorHigh'
        }
      },
      aftercolor(val) {
        if (val === undefined) {
          return ''
        }
        if (val > 0 && val < 4.4) {
          return 'colorDown'
        } else if (val >= 4.4 && val <= 10 || val == 0) {
          return 'colorNormal'
        } else {
          return 'colorHigh'
        }
      }
    },
    created() {
      this.init()
    },
    mounted() {
      // 判断是否滑到底部
      this.checkScroll()
    },
    watch: {
      isClientBottom(newVal, oldVal) {
        // 滑倒底部并且还有更多报告时，以及不在加载中（防止重复加载）
        if (newVal) {
          // 获取往期报告列表
          // let pageNo = parseInt(this.pageNo)
          // let totalPage = parseInt(this.totalPage)
          // if (pageNo < totalPage) {
          //   this.pageNo = this.pageNo + 1
          //   this.init(this.tabItem, this.pageNo)
          // }
        }
      }
    },
    methods: {
      init() {
        this.getTableRecords()
      },
      getTableRecords(endDate = undefined) {
        let that = this

        getTableRecords({ last_time: endDate }).then(function (res) {
          startQuery = false
          if (res.status === 0) {
            that.familyGluData = that.familyGluData.concat(res.data)
            if (res.data.length > 0) {
              that.endDate = res.data[res.data.length - 1].date_day
            }
          }
        })
      },
      checkScroll() {
        let scrollTop = getScrollTop()
        let that = this
        window.onscroll = () => {
          let newSrollTop = getScrollTop() // 当前滚动高度
          let scrollHeight = getScrollHeight() // 文档完整的高度
          let clientHeight = getClientHeight() // 可视范围的高度
          let distance = 2 // 底部间距

          // 如果当前滚动高度大于之前滚动高度 -> 下滑
          if (newSrollTop > scrollTop) {
            // 如果到底部 this.isClientBottom = true；如果不在底部 this.isClientBottom = false
            if (newSrollTop + clientHeight + distance < scrollHeight) {
              console.log('没到底')
              this.isClientBottom = false
            } else {
              console.log('到底了')
              if (startQuery === false) {
                startQuery = true
                this.getTableRecords(that.endDate)
              }

              this.isClientBottom = true
            }
          } else {
            this.isClientBottom = false
          }

          // 每次滚动后，把当前滚动高度赋值给之前滚动高度
          scrollTop = newSrollTop
        }
      }
    }
  }
</script>

<style lang="scss" scoped>
  .wrapper {
    padding: 10px 15px;
    background: #fff;
    .tips .left {
      width: 50%;
      height: 25px;
      font-size: 13px;
      font-weight: normal;
      text-align: left;
      color: rgba(153, 153, 153, 1);
      float: left;
    }

    .tips .right {
      width: 49%;
      height: 25px;
      font-size: 13px;
      font-weight: normal;
      text-align: right;
      float: right;
      color: rgba(153, 153, 153, 1);
    }

    .over {
      text-align: left;

      .over-title {
        color: #666;
        font-size: 13px;
      }

      .over-mid {
        display: flex;
        margin: 14px 0;
        align-items: center;
        justify-content: space-between;

        .over-icon {
          color: #666;
          font-size: 14px;
        }
      }

      .over-val {
        color: #333;
        font-size: 14px;
      }
    }

    .sugarTable {
      width: 100%;
      margin: 20px auto 0;
      .tables {
        width: 100%;
        background: #F7F7F8;
        border-radius: 6px;
        font-size: 13px;
        .tr {
          display: flex;
          width: 100%;
          border-top: 1px solid #fff;
          border-left: 1px solid #fff;
          color: #555;
          box-sizing: border-box;
          .td {
            width: 20%;
            justify-content: center;
            text-align: center;
            border-right: 1px solid #fff;
            .tdd1 {
              border-bottom: none;
              .thsmall {
                justify-content: center;
                display: flex;
                align-items: center;
                height: 31px;
                color: #555;
                border-right: 1px solid #fff;
              }
            }
            .tdd2 {
              border-bottom: 1px solid #fff;
              padding: 6px 4px;
            }
          }
          .th {
            justify-content: center;
            display: flex;
            height: 62px;
            align-items: center;
            color: #555;
            border-right: 1px solid #fff;
          }
          .td_center {
            display: flex;
            align-items: center;
          }
          .width1 {
            width: 13.34%;
          }
          .width4 {
            width: 10%;
          }
          .width2 {
            width: 20%;
          }
          .width3 {
            width: 50%;
          }
        }
        .table {
          .data {
            background: #EEF7FF;
            font-size: 15px;
          }
        }
        /*.table {*/
        /*.data {*/
        /*background: #EEF7FF;*/
        /*font-size: 15px;*/
        /*}*/
        /*}*/
        .table {
          /*.date {*/
          /*background: rgba(122, 213, 255, 0.05);*/
          /*}*/
          .thsmall {
            justify-content: center;
            display: flex;
            align-items: center;
            height: 31px;
            color: #555;
            border-right: 1px solid #fff;
          }
        }
      }
    }
  }

  .colorHigh {
    color: #F8875C !important;
  }

  .colorDown {
    color: #61A7FF !important;
  }

  .colorNormal {
    color: #07C160 !important;
  }
</style>
