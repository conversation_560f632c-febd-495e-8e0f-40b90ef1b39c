<template>
  <!--  -->
  <div class="wrapper">
    <div class="header">
      <div class="swiper" ref="my_swiper">
        <div class="swiper-wrapper">
          <div class="swiper-slide" v-for="(item, index) in head_img_list" :key="index"><img :src="item"></div>
        </div>
        <!-- 如果需要使用分页器 -->
        <div class="swiper-pagination"></div>
      </div>
      <div class="tip_count">
        {{ current }}/{{ head_img_list.length }}
      </div>
    </div>

    <div class="main">
      <div class="box">
        <div class="title">{{ name }}</div>
        <div class="price">
          <div class="old">￥{{ market_price }}</div>
          <div class="new">
            <span>优惠价</span>￥{{ price }}
          </div>
        </div>
      </div>
    </div>
    <div class="desc">
      <img loading="lazy" v-for="(item, index) in detail_img_list" :key="index" :src="item">
    </div>
    <div style="height:100px"></div>
    <div class="btn_buy cannot_buy">
      <div>仅限患者购买</div>
    </div>
  </div>
</template>

<script>
import {getProductDetail} from '@/api/servicePackage'
import Swiper from 'swiper/swiper-bundle.min.js';

//一定要引入css
import 'swiper/swiper-bundle.min.css';

export default {
  data: () => {
    return {
      current: 1,
      detail_img_list: [],
      head_img_list: [],
      name: '',
      market_price: '',
      price: '',
      doc_open_package_id: 0
    }
  },
  created() {
    this.doc_open_package_id = this.$route.query.doc_open_package_id
    this.init()
  },
  mounted() {

  },
  methods: {
    init() {
      let that = this
      getProductDetail({doc_open_package_id: this.doc_open_package_id}).then((res) => {
        if (res.status == 0) {
          that.pageStatus = true
          that.price = res.data.price
          that.name = res.data.name
          that.head_img_list = res.data.head_img_list
          that.market_price = res.data.market_price
          that.description = res.data.description
          that.detail_img_list = res.data.detail_img_list
          document.title = res.data.name
          if (res.data.head_img_list.length > 0) {
            this.$nextTick(() => {
              new Swiper('.swiper', {
                loop: true, // 循环模式选项
                // 如果需要分页器
                pagination: {
                  el: '.swiper-pagination'
                },
                on: {
                  slideChange: function () {
                    console.log(that.$refs.my_swiper.swiper.activeIndex, 123123123)

                    if (that.$refs.my_swiper.swiper.activeIndex == 0) {
                      that.current = res.data.head_img_list.length;
                    } else if (that.$refs.my_swiper.swiper.activeIndex > res.data.head_img_list.length) {
                      that.current = 1;
                    } else {
                      that.current = that.$refs.my_swiper.swiper.activeIndex;
                    }
                  },
                }
              })
            })
          }
        } else {

        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #f5f6fa;
  /*swiper*/
  .header {
    padding-top: 13px;
    position: relative;

    .tip_count {
      z-index: 99;
      position: absolute;
      background: #000000;
      opacity: 0.7;
      border-radius: 48px;
      font-weight: 400;
      font-size: 14px;
      line-height: 20px;
      /* identical to box height, or 143% */
      color: #FFFFFF;
      padding: 2px 10px;
      right: 5px;
      bottom: 10px;
    }
  }

  .swiper {
    width: 345px;
    height: 345px;

    .swiper-wrapper {
      width: 345px;
      height: 3px;

      .swiper-slide {
        width: 345px;
        height: 345px;
        border-radius: 8px;

        img {
          width: 345px;
          height: 345px;
          border-radius: 8px;
        }
      }
    }

    .swiper-pagination {
      position: absolute;
    }

  }


  /*swiper*/
  .main {
    padding-top: 10px;

    .box {
      width: 315px;
      padding: 15px;
      background: #FFFFFF;
      border-radius: 8px;
      display: flex;
      flex-direction: column;

      .title {
        font-weight: 500;
        font-size: 17px;
        line-height: 24px;
        text-align: left;
        /* identical to box height */
        color: #000000;
      }

      .price {
        padding-top: 9px;
        display: flex;
        justify-content: flex-start;

        .new {
          margin-left: 16px;
          background: #FFEDED;
          border-radius: 34px;
          font-weight: 500;
          font-size: 12px;
          line-height: 26px;
          color: #FA1E1E;
          padding: 0 10px;
        }


        .old {
          font-weight: 500;
          font-size: 12px;
          line-height: 26px;
          color: #FA1E1E;
        }
      }
    }
  }

  .desc {
    padding-top: 16px;
    display: flex;
    flex-direction: column;

    img {
      width: 345px;
    }
  }

  .btn_buy {
    z-index: 999;
    padding: 10px 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: fixed;
    bottom: 40px;
    width: 315px;
    background: #FF4228;
    border-radius: 8px;
    font-weight: 500;
    font-size: 17px;
    line-height: 24px;
    text-align: center;
    color: #FFFFFF;
  }

  .cannot_buy {
    background: #FF9182 !important;
    padding: 14px 0 !important;
  }
}
</style>
