<template>
  <div class="wrapper" ref="height">
      <div class="date-seven">
        <div @click="beforeStage" class="date-arrow">
          <van-icon name="arrow-left" /><span>上一周</span>
        </div>
        <div class="date-comp-cont">
          <span>{{ dateFormat(startDate, '.') }}</span>
          <b class="date-comp-sign">~</b>
          <span>{{ dateFormat(endDate, '.') }}</span>
        </div>
        <div @click="afterStage" :class="['date-arrow', {nextActive:nextClick== true}]">
          下一周<van-icon name="arrow" />
        </div>
      </div>
      <div class="content">
        <div class="noData" v-if="dataList.length==0">
          <img src="./../../assets/images/<EMAIL>">
          <div class="noDataTitle">本周暂无数据</div>
        </div>
        <div class="hasData" v-else>
          
          <div class="title">
            <img src="./../../assets/images/gth.png">
            <span>本周的运营报告</span>
          </div>
          <div class="details">
            <p>
              <img src="./../../assets/images/operationReport/person.png" alt="">
              <span>患者数量</span>
            </p>
            <div class="table-box mb20">
                <table class="table">
                    <thead>
                        <tr>
                            <th>医生姓名</th>
                            <th>新增（人）</th>
                            <th>异常（人）</th>
                            <th>总数</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="(item,index) in dataList" :key="index">
                            <td class="td">{{ item.doc_name }}</td>
                            <td class="td">{{ item.increased_num }}</td>
                            <td class="td">{{ item.abnormal_num }}</td>
                            <td class="td">{{ item.total_num }}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
          </div>
          <div class="details">
            <p>
              <img src="./../../assets/images/operationReport/trander.png" alt="">
              <span>家庭测量情况</span>
            </p>
            <div class="table-box mb20">
                <table class="table">
                    <thead>
                        <tr>
                            <th>医生姓名</th>
                            <th>血糖（次）</th>
                            <th>血压（次）</th>
                            <th>异常（次）</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="(item,index) in dataList" :key="index">
                            <td class="td">{{ item.doc_name }}</td>
                            <td class="td">{{ item.family_bg_measure_times }}</td>
                            <td class="td">{{ item.family_bp_measure_times }}</td>
                            <td class="td">{{ item.family_measure_abnormal_times }}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
          </div>
          <div class="details detailss">
            <p>
              <img src="./../../assets/images/operationReport/statistics.png" alt="">
              <span>工作统计</span>
            </p>
            <div class="table-box mb20">
                <table class="table">
                    <thead>
                        <tr class="tr20">
                            <th>医生姓名</th>
                            <th>干预（次）</th>
                            <th>问答（次）</th>
                            <th>动态（次）</th>
                            <th>通知（条）</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="tr20" v-for="(item,index) in dataList" :key="index">
                            <td class="td">{{ item.doc_name }}</td>
                            <td class="td">{{ item.intervene_times }}</td>
                            <td class="td">{{ item.consult_reply_times }}</td>
                            <td class="td">{{ item.posts_num }}</td>
                            <td class="td">{{ item.notice_num }}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
          </div>
          
        </div>
      </div>
  </div>
</template>

<script>
  import moment from 'moment'
  import {getWorkroomData} from '@/api/operationReport.js'
  export default {
    components: {
    },
    data: () => {
      return {
        endDate: '',
        pageDate: '', // 保存一个日期和切换后的endDate作比较
        isLoading: false,
        nextClick: false,
        workroom_id: 0,// 8227 测试数据
        dataList:[],
      }
    },
    computed: {
      // 开始时间
      startDate() {
        let endDate = this.endDate
        if (isNaN(Date.parse(endDate))) endDate = new Date()
        // console.log(this.lastDay(this.getStartDayOfWeek(this.endDate).slice(0, 10)),'-----this,')
        // console.log(this.endDate)
        return this.getStartDayOfWeek(this.getStartDayOfWeek(this.endDate).slice(0, 10)).slice(0, 10)
      }
    },
    watch: {
      endDate(newVal, oldVal) {
        let todayDate = new Date()
        console.log(newVal)
        if (newVal == this.pageDate){
          this.nextClick = true
        } else {
          this.nextClick = false
        }
        // 初始化改变时 返回
        if (isNaN(Date.parse(oldVal))) return
        // 如果不是日期格式 返回
        if (isNaN(Date.parse(newVal))) {
          this.endDate = this.dateFormat(new Date())
          return
        }
        this.getPatientData()
      }
    },
    created() {
      this.$nextTick(() => {
        this.$refs.height.style.height = window.innerHeight + 'px'
      })
      // 获取结束日期   如果有则是从历史记录里面进入时间不一定是上周末，若没有则正常点击进入时间必定为上周末
      let getEndDates = this.$route.query.endDate
      console.log(getEndDates)
      if (getEndDates != undefined && getEndDates != '') {
        this.endDate =this.lastDay(this.getEndDayOfWeek(getEndDates).slice(0, 10) ) 
        // moment获取上一周的结束日期
        this.pageDate = moment().week(moment().week() - 1).endOf('week').add(1, 'days').format('YYYY-MM-DD')
      } else {
        // moment获取上一周的日期
        let startDate =this.lastDay(this.getStartDayOfWeek(new Date().toISOString().slice(0, 10)).slice(0, 10)) //moment().week(moment().week() - 1).startOf('week').add(1, 'days').format('YYYY-MM-DD')
        let endDate = this.lastDay(this.getEndDayOfWeek(new Date().toISOString().slice(0, 10)).slice(0, 10) )//moment().week(moment().week() - 1).endOf('week').add(1, 'days').format('YYYY-MM-DD')
        console.log(startDate, endDate)
        this.endDate = endDate
        this.pageDate = endDate
      }
      this.workroom_id = this.$route.query.workroom_id
      // this.userInfoFun()
      this.getPatientData()
    },
    methods: {
      
      // 获取 患者、家庭、工作 表格数据
      getPatientData() {
        this.isLoading = true
        let datas = {workroom_id: this.workroom_id, start_date: this.startDate, end_date: this.endDate}
        getWorkroomData(datas).then(res => {
          if (res.code == 200) {
            this.dataList = res.data;
            this.isLoading = false
          } else {
            this.$toast(res.msg)
            this.isLoading = false
          }
        })
      },
      /**
       * 日期格式化
       * @param {String | Date} date 日期
       * @param {String} sep 分隔符
       * @return {String} 格式化日期
       */
      dateFormat(date, sep = '-') {
        let oDate = new Date(date)
        let y = oDate.getFullYear()
        let m = oDate.getMonth() + 1
        let d = oDate.getDate()
        if (m < 10) m = `0${m}`
        if (d < 10) d = `0${d}`
        return `${y}${sep}${m}${sep}${d}`
      },
      /**
       * 日期计算
       * @param {String} date 日期
       * @param {Number} index 未来或过去的天数（正数未来，负数过去）
       * @param {String} sep 分隔符
       */
      dateCount(date, index, sep = '-') {
        let oDate = new Date(date)
        // 天数转换为毫秒数
        let oMin = index * 24 * 60 * 60 * 1000
        let newDate = new Date(oDate.getTime() + oMin)
        return this.dateFormat(newDate, sep)
      },
      /**
       * 上阶段
       */
      beforeStage() {
        if (this.isLoading) return
        console.log(this.dateCount(this.startDate, -1))
        this.endDate = this.dateCount(this.startDate, -1)
      },
      /**
       * 下阶段
       */
      afterStage() {
        if (this.isLoading) return
        // 开始时间为计算属性，因此从开始日期推未来推13天
        let oDate = this.dateCount(this.startDate, 13)
        let newDate = new Date(oDate)
        let todayDate = new Date()
        // console.log('2', this.endDate, '3', this.pageDate)
        if (this.endDate == this.pageDate){
          return false
        } else {
          if (newDate.getTime() > todayDate.getTime()) oDate = this.dateFormat(todayDate)
          this.endDate = oDate
        }
      },
      lastDay(time) {
        let now = new Date(time); // 当前日期
        return this.formatDate(new Date(now.getFullYear(), now.getMonth(), now.getDate() - 7))
      },
      //获得本周的结束时间：
      getEndDayOfWeek(time) {
            let now = new Date(time); // 当前日期
            let nowDayOfWeek = now.getDay(); // 今天本周的第几天
            let day = nowDayOfWeek || 7;
            let nowDay = now.getDate(); // 当前日
            let nowMonth = now.getMonth(); // 当前月
            return this.formatDate(new Date(now.getFullYear(), nowMonth, nowDay + 7 - day));
      },
      //获得本周的开始时间：
      getStartDayOfWeek(time) {
            let now = new Date(time); // 当前日期
            let nowDayOfWeek = now.getDay(); // 今天本周的第几天
            let day = nowDayOfWeek || 7;
            let nowDay = now.getDate(); // 当前日
            let nowMonth = now.getMonth(); // 当前月
            return this.formatDate(new Date(now.getFullYear(), nowMonth, nowDay + 1 - day));
        },
      formatDate(date) {
        let myyear = date.getFullYear();
        let mymonth = date.getMonth() + 1;
        let myweekday = date.getDate();
        if (mymonth < 10) {
            mymonth = '0' + mymonth;
        }
        if (myweekday < 10) {
            myweekday = '0' + myweekday;
        }
        return (myyear + '-' + mymonth + '-' + myweekday);
      }
    },
  }
</script>

<style lang="scss" scoped>
  $a: '44px';
  .wrapper {
    overflow: hidden;
    background: #F9F9F9;
    box-sizing: border-box;
    
    .date-seven {
      width: 100%;
      height: 28px;
      padding: 16px 0;
      // background: #F6A142;
      background: white;
      display: flex;
      align-items: center;
      justify-content: center;
      .date-arrow {
        font-size: 14px;
        font-weight: 400;
        color: #666666;
      }
      .nextActive{
        color: #aaa;
      }
      .date-comp-cont {
        width: 215px;
        height: 36px;
        line-height: 36px;
        margin: 0 8px;
        border-radius: 26px;
        border: 1px solid rgba(238, 120, 0, 0.3);
        display: flex;
        font-size: 15px;
        // color: #FFF;
        color: #EE7800;
        background: rgba(255,255,255,0.25);
        font-weight: 500;
        align-items: center;
        justify-content: center;
        .date-comp-sign {
          margin: 0 2px;
          margin-left: 5px;
        }
      }
    }
    .content{
      height: calc(100vh - #{$a});
      overflow-y: auto;
      &::-webkit-scrollbar {
        display: none;
      }
      padding: 0 10px 30px;
      box-sizing: border-box;
    }
    .hasData{
      margin-bottom: 100px;
    }
    .noData{
      width: 225px;
      height: 131px;
      margin: 0 auto;
      margin-top: 80px;
      img{
        width: 225px;
        height: 131px;
      }
      .noDataTitle{
        width: 100%;
        height: 24px;
        text-align: center;
        font-size: 17px;
        font-weight: 400;
        color: #999999;
        line-height: 24px;
        margin-top: 21px;
      }
    }
    
    .title{
      width: 100%;
      height: 42px;
      line-height: 42px;
      font-size: 13px;
      font-weight: 400;
      color: #A7A7A7;
      display: flex;
      align-items: center;
      justify-content:flex-start;
      span {
        padding-top: 2.5px;
      }
      img{
        width: 20px;
        height: 20px;
        margin: 0 5px 0 10px;
        vertical-align: text-top;
      }
    }
    ::v-deep .details{
      width: 100%;
      height: auto;
      background: #fff;
      margin-bottom: 13px;
      border-radius: 4px;
      p{
        // width: 343px;
        height: 58px;
        line-height: 58px;
        text-align: left;
        font-size: 16px;
        font-weight: 500;
        color: #333333;
        // padding: 0 10px;
        margin: 0 10px;
        border-bottom: 1px solid #F9F9F9;
        span {
          padding-top: 2px;
        }
        img {
          width: 20px;
          height: 20px;
          vertical-align: text-top;
          margin-right: 10px;
        }
      }
    }
    .detailss{
      height: auto;
    }
    ::v-deep .table-box {
      background: #fff;
      padding-bottom: 16px;
      border-radius: 4px;
      p{
        // width: 343px;
        width: 100%;
        height: 58px;
        line-height: 58px;
        text-align: left;
        font-size: 16px;
        font-weight: 500;
        color: #333333;
        padding: 0 10px;
        box-sizing: border-box;
        margin: 0 auto;
        // border-bottom: 1px solid #F9F9F9;
        display: inline-flex;
        align-items: center;
        img {
          width: 20px;
          height: 20px;
          // vertical-align: text-top;
          margin-right: 10px;
        }
      }
      .table-outer {
        padding: 0 10px;
      }
      .table {
        width: 100%;
        min-height: 48px;
        height: auto;
        margin: 0 auto;
        thead{
          height: 40px;
          border: 1px solid #E6E6E6;
          border-right: 0;
          border-bottom: 0;
          text-align: center;
          line-height: 40px;
          tr{
            height: auto;
            min-height: 40px;
            background: #F7F7F7;
            font-size: 13px;
            font-weight: 400;
            th{
              width: 25%;
              height: auto;
              min-height: 40px;
              color: #111;
              border-right: 1px solid #E6E6E6;
            }
            
          }
        }
        tbody{
          border-right: 1px solid #E6E6E6;
          border-bottom: 1px solid #E6E6E6;
        }
        tr{
          height: auto;
          min-height: 40px;
          td {
            width: 25%;
            height: auto;
            min-height: 40px;
            color: #474747;
            font-size: 13px;
            font-weight: 400;
            border: 1px solid #E6E6E6;
            padding: 8px 5px;
            text-align: center;
          }
          .td{
            border-right: 0;
            border-bottom: 0;
          }
        }
        .table-val {
          color: #333;
          font-size: 17px;
          font-weight: normal;
        }
        .tr20{
          th{
            width: 20%;
          }
          .td{
            width: 20%;
          }
        }
      }
    }
    .mb20{
      margin-bottom: 20px;
    }
  }
</style>
