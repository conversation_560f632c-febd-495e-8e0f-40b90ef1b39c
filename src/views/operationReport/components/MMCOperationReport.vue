<!--
 * @Descripttion: <PERSON><PERSON>中心团队周报和个人周报
 * @version: 
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-12 14:26:49
 * @LastEditors: l<PERSON><PERSON><PERSON>n
 * @LastEditTime: 2021-08-10 16:43:15
-->
<template>
    <div class="wrapper">
        <div class="content">
            <div 
                v-if="!reportData.user_data"
                class="noData"
            >
                <img src="./../../../assets/images/<EMAIL>">
                <div class="noDataTitle">本周暂无数据</div>
            </div>
            <div 
                v-if="reportData.user_data"
                class="hasData"
            >
                <div 
                    v-if="reportType == 1 || reportType == 2"
                    class="userInfo">
                    <img 
                        :src="reportData.doc_info.photo == '' ? 'https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/reportToux%402x.png' : reportData.doc_info.photo"
                        class="userImg" />
                    <span class="userName">{{ reportData.doc_info.name }}</span>
                    <i>{{ reportData.doc_info.job_rank }}</i>
                    <i>&nbsp;| {{ reportData.doc_info.dept && reportData.doc_info.dept.name }}</i>
                </div>
                <div class="title">
                    <img src="./../../../assets/images/gth.png" alt="">
                    <span>更新截止时间：{{ reportData.user_data.last_update }}</span>
                </div>
                <div class="details">
                    <p>
                        <img src="./../../../assets/images/operationReport/person.png" alt="">
                        <span>中心患者数量</span>
                    </p>
                    <!--团队-->
                    <ul class="2">
                        <li class="licontent width">
                            <span>质控人数（人）</span>
                            <span>{{ reportData.user_data.quality_count }}</span>
                        </li>
                        <li class="liborder"></li>
                        <li class="licontent width">
                            <span>患者总数（人）</span>
                            <span>{{ reportData.user_data.user_count }}</span>
                        </li>
                    </ul>
                </div>
                <!--团队个人，个人-->
                <template v-if="reportType == 1 || reportType == 2">
                    <div class="table-box">
                        <p>
                            <img src="./../../../assets/images/operationReport/trander.png" alt="">
                            <span>管理患者的中心随访率</span>
                        </p>
                        <div class="table-outer">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>访视阶段</th>
                                        <th>中心随访率</th>
                                        <th>我的随访率</th>
                                        <!-- <th>预计到访</th>
                                        <th>实际到访</th> -->
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr
                                        v-for="(item, index) in reportData.visit_data"
                                        :key="index"
                                    >
                                        <td class="td">{{ item.visit_level }}</td>
                                        <td class="td">{{ item.percent }}</td>
                                        <td class="td">{{ item.my && item.my.percent }}</td>
                                        <!-- <td class="td">{{ item.actual }}</td> -->
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="table-box">
                        <p>
                            <img src="./../../../assets/images/operationReport/trander.png" alt="">
                            <span>管理患者的糖化达标率</span>
                        </p>
                        <div class="table-outer">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>访视阶段</th>
                                        <th>中心糖化达标率</th>
                                        <th>我的糖化达标率</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr
                                        v-for="(item, index) in reportData.bg"
                                        :key="index"
                                    >
                                        <td class="td">{{ item.level }}</td>
                                        <td class="td">{{ item.rate }}</td>
                                        <td class="td">{{ item.my.rate }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="table-box">
                        <p>
                            <img src="./../../../assets/images/operationReport/trander.png" alt="">
                            <span>管理患者的血压达标率</span>
                        </p>
                        <div class="table-outer">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>访视阶段</th>
                                        <th>中心血压达标率</th>
                                        <th>我的血压达标率</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr
                                        v-for="(item, index) in reportData.bp"
                                        :key="index"
                                    >
                                        <td class="td">{{ item.level }}</td>
                                        <td class="td">{{ item.rate }}</td>
                                        <td class="td">{{ item.my.rate }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="table-box">
                        <p>
                            <img src="./../../../assets/images/operationReport/trander.png" alt="">
                            <span>管理患者的血脂达标率</span>
                        </p>
                        <div class="table-outer">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>访视阶段</th>
                                        <th>中心血脂达标率</th>
                                        <th>我的血脂达标率</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr
                                        v-for="(item, index) in reportData.ldl"
                                        :key="index"
                                    >
                                        <td class="td">{{ item.level }}</td>
                                        <td class="td">{{ item.rate }}</td>
                                        <td class="td">{{ item.my.rate }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="table-box">
                        <p>
                            <img src="./../../../assets/images/operationReport/trander.png" alt="">
                            <span>管理患者的综合达标率</span>
                        </p>
                        <div class="table-outer">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>访视阶段</th>
                                        <th>中心综合达标率</th>
                                        <th>我的综合达标率</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr
                                        v-for="(item, index) in reportData.compare"
                                        :key="index"
                                    >
                                        <td class="td">{{ item.level }}</td>
                                        <td class="td">{{ item.rate }}</td>
                                        <td class="td">{{ item.my.rate }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </template>
                <!--团队-->
                <template v-if="reportType == 3">
                    <div class="table-box">
                        <p>
                            <img src="./../../../assets/images/operationReport/trander.png" alt="">
                            <span>中心随访率</span>
                        </p>
                        <div class="table-outer">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>访视阶段</th>
                                        <th>中心随访率</th>
                                        <th>预计到访</th>
                                        <th>实际到访</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr
                                        v-for="(item, index) in reportData.visit_data"
                                        :key="index"
                                    >
                                        <td class="td">{{ item.visit_level }}</td>
                                        <td class="td">{{ item.percent }}</td>
                                        <td class="td">{{ item.should }}</td>
                                        <td class="td">{{ item.actual }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="table-box">
                        <p>
                            <img src="./../../../assets/images/operationReport/trander.png" alt="">
                            <span>糖化达标率</span>
                        </p>
                        <div class="table-outer">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>访视阶段</th>
                                        <th>达标人数</th>
                                        <th>总人数</th>
                                        <th>百分比</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr
                                        v-for="(item, index) in reportData.bg"
                                        :key="index"
                                    >
                                        <td class="td">{{ item.level }}</td>
                                        <td class="td">{{ item.standard }}</td>
                                        <td class="td">{{ item.total }}</td>
                                        <td class="td">{{ item.rate }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="table-box">
                        <p>
                            <img src="./../../../assets/images/operationReport/trander.png" alt="">
                            <span>血压达标率</span>
                        </p>
                        <div class="table-outer">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>访视阶段</th>
                                        <th>达标人数</th>
                                        <th>总人数</th>
                                        <th>百分比</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr
                                        v-for="(item, index) in reportData.bp"
                                        :key="index"
                                    >
                                        <td class="td">{{ item.level }}</td>
                                        <td class="td">{{ item.standard }}</td>
                                        <td class="td">{{ item.total }}</td>
                                        <td class="td">{{ item.rate }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="table-box">
                        <p>
                            <img src="./../../../assets/images/operationReport/trander.png" alt="">
                            <span>血脂达标率</span>
                        </p>
                        <div class="table-outer">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>访视阶段</th>
                                        <th>达标人数</th>
                                        <th>总人数</th>
                                        <th>百分比</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr
                                        v-for="(item, index) in reportData.ldl"
                                        :key="index"
                                    >
                                        <td class="td">{{ item.level }}</td>
                                        <td class="td">{{ item.standard }}</td>
                                        <td class="td">{{ item.total }}</td>
                                        <td class="td">{{ item.rate }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="table-box">
                        <p>
                            <img src="./../../../assets/images/operationReport/trander.png" alt="">
                            <span>综合达标率</span>
                        </p>
                        <div class="table-outer">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>访视阶段</th>
                                        <th>达标人数</th>
                                        <th>总人数</th>
                                        <th>百分比</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr
                                        v-for="(item, index) in reportData.compare"
                                        :key="index"
                                    >
                                        <td class="td">{{ item.level }}</td>
                                        <td class="td">{{ item.standard }}</td>
                                        <td class="td">{{ item.total }}</td>
                                        <td class="td">{{ item.rate }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </template>
                
            </div>
        </div>
    </div>
</template>

<script>
import { mmcSingleReport, mmcTeamReport, mmcTeamSingleReport } from '@/api/operationReport.js'
export default {
    data() {
        return {
            reportType: 1, // 1 个人报告 2 团队个人报告 3 团队报告
            team_id: '',
            reportData: {
                user_data: {},
                doc_info: {}, 
                visit_data: []
            }
        }
    },
    created() {
        this.reportType = this.$route.query.type
        console.log(this.reportType)
        if (this.reportType == 3) {
            this.team_id = this.$route.query.team_id
        }
        this.init()
    },
    methods: {
        /**
         * 初始化
         */
        init () {
            // 个人
            if (this.reportType == 1) {
                this.getSingleData()
            }
            // 团队
            if (this.reportType == 3) {
                this.getTeamData()
            }
            // 团队个人
            if (this.reportType == 2) {
                this.getTeamSingleData()
            }
        },
        // mmc 个人
        getSingleData () {
            mmcSingleReport().then(res => {
                if (res.code === 200) {
                    this.reportData = res.data
                } else {
                    this.$toast(res.msg)
                }
            })
        },
        // mmc 团队
        getTeamData () {
            mmcTeamReport({
                team_id: this.team_id
            }).then(res => {
                if (res.code === 200) {
                    this.reportData = res.data
                } else {
                    this.$toast(res.msg)
                }
            })
        },
        // mmc 团队个人
        getTeamSingleData () {
            mmcTeamSingleReport({
                doc_id: this.$route.query.team_doc_id
            }).then(res => {
                if (res.code === 200) {
                    this.reportData = res.data
                } else {
                    this.$toast(res.msg)
                }
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.wrapper {
    .content {
        .noData {
            height: auto;
        }
        .details {
            .width {
                width: 49%;
            }
        }
        
    }
}
</style>