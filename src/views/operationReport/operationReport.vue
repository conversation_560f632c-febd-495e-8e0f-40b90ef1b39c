<template>
  <div class="wrapper" ref="height">
    <van-tabs v-model="active">
      <van-tab title="医生工作室">
        <div class="date-seven">
          <div @click="beforeStage" class="date-arrow">
            <van-icon name="arrow-left" /><span>上一周</span>
          </div>
          <div class="date-comp-cont">
            <span>{{ dateFormat(startDate, '.') }}</span>
            <b class="date-comp-sign">~</b>
            <span>{{ dateFormat(endDate, '.') }}</span>
          </div>
            <!-- <input type="date"
                  v-model="endDate"
                  :max="dateFormat(new Date())"
                  class="date-comp-input"> -->
          <div @click="afterStage" :class="['date-arrow', {nextActive:nextClick== true}]">
            下一周<van-icon name="arrow" />
          </div>
        </div>
        <div class="content" v-if="flg">
          <div class="noData" v-if="reportData.report == null">
            <img src="./../../assets/images/<EMAIL>">
            <div class="noDataTitle">本周暂无数据</div>
          </div>
          <div class="hasData" v-if="reportData.report != null">
            <div class="userInfo" v-if="reportType == 1 || reportType == 2">
              <img :src="reportData.doc_info.photo == '' ? 'https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/reportToux%402x.png' : reportData.doc_info.photo" class="userImg" />
              <span class="userName">{{reportData.doc_info.name}}</span>
              <i>{{reportData.doc_info.job_rank}}</i><i>&nbsp;| {{reportData.doc_info.dept.name}}</i>
            </div>
            <div class="title" v-if="reportType == 1">
              <img src="./../../assets/images/gth.png"><span>本周的个人报告</span>
            </div>
            <div class="title" v-if="reportType == 2 || reportType == 3">
              <img src="./../../assets/images/gth.png">
              <span>本周的运营报告</span>
            </div>
            <div class="details">
              <p>
                <img src="./../../assets/images/operationReport/person.png" alt="">
                <span>患者数量</span>
              </p>
              <ul>
                <li class="licontent"><span>新增 (人)</span><span>{{reportData.report.increased_num}}</span></li>
                <li class="liborder"></li>
                <li class="licontent"><span>异常 (人)</span><span>{{reportData.report.abnormal_num}}</span></li>
                <li class="liborder"></li>
                <li class="licontent"><span>总数 (人)</span><span>{{reportData.report.total_num}}</span></li>
              </ul>
            </div>
            <div class="details">
              <p>
                <img src="./../../assets/images/operationReport/trander.png" alt="">
                <span>家庭测量情况</span>
              </p>
              <ul>
                <li class="licontent"><span>血糖 (次)</span><span>{{reportData.report.family_bg_measure_times}}</span></li>
                <li class="liborder"></li>
                <li class="licontent"><span>血压 (次)</span><span>{{reportData.report.family_bp_measure_times}}</span></li>
                <li class="liborder"></li>
                <li class="licontent"><span>异常 (次)</span><span>{{reportData.report.family_measure_abnormal_times}}</span></li>
              </ul>
            </div>
            <div class="details detailss">
              <p>
                <img src="./../../assets/images/operationReport/statistics.png" alt="">
                <span>工作统计</span>
              </p>
              <ul>
                <li class="licontents"><span>干预 (次)</span><span>{{reportData.report.intervene_times}}</span></li>
                <li class="liborder"></li>
                <li class="licontents"><span>问答 (次)</span><span>{{reportData.report.consult_reply_times}}</span></li>
              </ul>
              <ul>
                <li class="licontents"><span>动态 (条)</span><span>{{reportData.report.posts_num}}</span></li>
                <li class="liborder"></li>
                <li class="licontents"><span>通知 (条)</span><span>{{reportData.report.notice_num}}</span></li>
              </ul>
            </div>
            <div class="table-box" v-if="reportType == 3 && reportData.docs_report.length > 0">
              <p>
                <img src="./../../assets/images/operationReport/doc.png" alt="">
                <span>医生列表</span>
              </p>
              <div class="table-outer">
                <table class="table">
                <thead>
                  <tr><th class="th">医生姓名</th><th>新增 (人)</th><th>异常 (人)</th><th>总数</th></tr>
                </thead>
                <tbody>
                  <tr v-for="(item, index) in reportData.docs_report" :key=index>
                    <td class="td tds">{{item.doc_name}}</td>
                    <td class="td">{{item.increased_num}}</td>
                    <td class="td">{{item.abnormal_num}}</td>
                    <td class="td">{{item.total_num}}</td>
                  </tr>
                </tbody>
              </table>
              </div>
            </div>
            </div>
        </div>
      </van-tab>
      <van-tab v-if="mmcRight == 1" title="MMC中心">
        <MMCOperationReport></MMCOperationReport>
      </van-tab>
    </van-tabs>
    

  </div>
</template>

<script>
  import moment from 'moment'
  import {singleReport, teamSingleReport, teamReport, userInfoFun} from '@/api/operationReport.js'
  import MMCOperationReport from './components/MMCOperationReport.vue'

  export default {
    components: {
      MMCOperationReport
    },
    data: () => {
      return {
        reportType: 1, // 1个人报告 需传入type  2 团队个人报告 需传入type, team_id， team_doc_id   3 团队报告  需传入type, team_id
        team_id: '',
        team_doc_id: '',
        endDate: '',
        pageDate: '', // 保存一个日期和切换后的endDate作比较
        reportData: {report: null, doc_info: {}, docs_report: []},
        isLoading: false,
        nextClick: false,
        flg: false,
        active: 0,     // tab页   0: 医生工作室 1: MMC中心
        mmcRight: 0   // mmc 中心tab页是否有权限显示 0: 无权限  1: 有权限
      }
    },
    computed: {
      // 开始时间
      startDate() {
        let endDate = this.endDate
        if (isNaN(Date.parse(endDate))) endDate = new Date()
        return this.getStartDayOfWeek(this.getStartDayOfWeek(this.endDate).slice(0, 10)).slice(0, 10)
      }
    },
    watch: {
      endDate(newVal, oldVal) {
        let todayDate = new Date()
        console.log(newVal)
        if (newVal == this.pageDate){
          this.nextClick = true
        } else {
          this.nextClick = false
        }
        // 初始化改变时 返回
        if (isNaN(Date.parse(oldVal))) return
        // 如果不是日期格式 返回
        if (isNaN(Date.parse(newVal))) {
          this.endDate = this.dateFormat(new Date())
          return
        }
        this.init()
      }
    },
    created() {
      this.$nextTick(() => {
        this.$refs.height.style.height = window.innerHeight + 'px'
      })
      // 获取结束日期   如果有则是从历史记录里面进入时间不一定是上周末，若没有则正常点击进入时间必定为上周末
      let getEndDates = this.$route.query.endDate
      console.log(getEndDates)
      if (getEndDates != undefined && getEndDates != '') {
        this.endDate = this.lastDay(this.getEndDayOfWeek(getEndDates).slice(0, 10) )
        // moment获取上一周的结束日期
        this.pageDate = moment().week(moment().week() - 1).endOf('week').add(1, 'days').format('YYYY-MM-DD')
      } else {
        // moment获取上一周的日期
        let startDate =this.lastDay(this.getStartDayOfWeek(new Date().toISOString().slice(0, 10)).slice(0, 10)) //moment().week(moment().week() - 1).startOf('week').add(1, 'days').format('YYYY-MM-DD')
        let endDate = this.lastDay(this.getEndDayOfWeek(new Date().toISOString().slice(0, 10)).slice(0, 10) )//moment().week(moment().week() - 1).endOf('week').add(1, 'days').format('YYYY-MM-DD')
        console.log(startDate, endDate)
        this.endDate = endDate
        this.pageDate = endDate
      }
      // 初始化
      this.reportType = this.$route.query.type
      if (this.reportType == 2) {
        this.team_id = this.$route.query.team_id
        this.team_doc_id = this.$route.query.team_doc_id
        document.title = '团队成员报告'
      } else if (this.reportType == 3) {
        this.team_id = this.$route.query.team_id
        document.title = '团队报告'
      }
      this.userInfoFun()
      this.init()
    },
    methods: {
      /**
       * 初始化
       */
      init() {
        if (this.reportType == 1) {
          // 个人报告
          this.getSingleData()
        } else if (this.reportType == 2) {
          this.getTeamSingleData()
        } else if (this.reportType == 3) {
          this.getTeamData()
        }
      },
      getSingleData() {
        let that = this
        let datas = {start_date: this.startDate, end_date: this.endDate}
        singleReport(datas).then(res => {
          console.log('个人报告', res)
          if (res.code === 200) {
            that.isLoading = false
            that.flg = true
            that.reportData = res.data
          } else {
            this.$toast(res.msg)
          }
        })
      },
      getTeamSingleData() {
        this.isLoading = true
        let that = this
        let datas = {team_id: this.team_id, team_doc_id: this.team_doc_id, start_date: this.startDate, end_date: this.endDate}
        teamSingleReport(datas).then(res => {
          console.log('团队个人报告', res)
          if (res.code === 200) {
            that.isLoading = false
            that.flg = true
            that.reportData = res.data
          } else {
            this.$toast(res.msg)
          }
        })
      },
      getTeamData() {
        this.isLoading = true
        let that = this
        let datas = {team_id: this.team_id, start_date: this.startDate, end_date: this.endDate}
        teamReport(datas).then(res => {
          console.log('团队报告', res)
          if (res.code === 200) {
            that.isLoading = false
            that.flg = true
            that.reportData = res.data
          } else {
            this.$toast(res.msg)
          }
        })
      },
      /**
       * 日期格式化
       * @param {String | Date} date 日期
       * @param {String} sep 分隔符
       * @return {String} 格式化日期
       */
      dateFormat(date, sep = '-') {
        let oDate = new Date(date)
        let y = oDate.getFullYear()
        let m = oDate.getMonth() + 1
        let d = oDate.getDate()
        if (m < 10) m = `0${m}`
        if (d < 10) d = `0${d}`
        return `${y}${sep}${m}${sep}${d}`
      },
      /**
       * 日期计算
       * @param {String} date 日期
       * @param {Number} index 未来或过去的天数（正数未来，负数过去）
       * @param {String} sep 分隔符
       */
      dateCount(date, index, sep = '-') {
        let oDate = new Date(date)
        // 天数转换为毫秒数
        let oMin = index * 24 * 60 * 60 * 1000
        let newDate = new Date(oDate.getTime() + oMin)
        return this.dateFormat(newDate, sep)
      },
      /**
       * 上阶段
       */
      beforeStage() {
        if (this.isLoading) return
        console.log(this.dateCount(this.startDate, -1))
        this.endDate = this.dateCount(this.startDate, -1)
      },
      /**
       * 下阶段
       */
      afterStage() {
        if (this.isLoading) return
        // 开始时间为计算属性，因此从开始日期推未来推13天
        let oDate = this.dateCount(this.startDate, 13)
        let newDate = new Date(oDate)
        let todayDate = new Date()
        // console.log('2', this.endDate, '3', this.pageDate)
        if (this.endDate == this.pageDate){
          return false
        } else {
          if (newDate.getTime() > todayDate.getTime()) oDate = this.dateFormat(todayDate)
          this.endDate = oDate
        }
      },
      lastDay(time) {
        let now = new Date(time); // 当前日期
        return this.formatDate(new Date(now.getFullYear(), now.getMonth(), now.getDate() - 7))
      },
      //获得本周的结束时间：
      getEndDayOfWeek(time) {
            let now = new Date(time); // 当前日期
            let nowDayOfWeek = now.getDay(); // 今天本周的第几天
            let day = nowDayOfWeek || 7;
            let nowDay = now.getDate(); // 当前日
            let nowMonth = now.getMonth(); // 当前月
            return this.formatDate(new Date(now.getFullYear(), nowMonth, nowDay + 7 - day));
      },
      //获得本周的开始时间：
      getStartDayOfWeek(time) {
            let now = new Date(time); // 当前日期
            let nowDayOfWeek = now.getDay(); // 今天本周的第几天
            let day = nowDayOfWeek || 7;
            let nowDay = now.getDate(); // 当前日
            let nowMonth = now.getMonth(); // 当前月
            return this.formatDate(new Date(now.getFullYear(), nowMonth, nowDay + 1 - day));
        },
      formatDate(date) {
        let myyear = date.getFullYear();
        let mymonth = date.getMonth() + 1;
        let myweekday = date.getDate();
        if (mymonth < 10) {
            mymonth = '0' + mymonth;
        }
        if (myweekday < 10) {
            myweekday = '0' + myweekday;
        }
        return (myyear + '-' + mymonth + '-' + myweekday);
      },
      // mmc中心权限
      userInfoFun() {
        userInfoFun().then(res => {
          if (res.code === 200) {
            console.log('res', res)
            this.mmcRight = res.data && res.data.info && res.data.info.docinfo.is_joined_mmc
          } else {
            this.$toast(res.msg)
          }
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  $a: '44px';
  .wrapper {
    overflow: hidden;
    background: #F9F9F9;
    box-sizing: border-box;
    ::v-deep .van-tab {
      color: #666666;
      font-size: 16px;
    }
    ::v-deep .van-tab--active {
      color: #000000;
      font-weight: 500;
    }
    ::v-deep .van-tabs__line {
      background-color: #EE7800;
      width: 33px !important;
      height: 4px;
      border-radius: 2px;
    }
    .date-seven {
      width: 100%;
      height: 28px;
      padding: 16px 0;
      // background: #F6A142;
      background: white;
      display: flex;
      align-items: center;
      justify-content: center;
      .date-arrow {
        font-size: 14px;
        font-weight: 400;
        color: #666666;
        .van-icon {
          vertical-align: -2px;
        }
      }
      .nextActive{
        // color: #FCE2C6;
        color: #9B9B9B;
      }
      .date-comp-cont {
        width: 215px;
        height: 36px;
        line-height: 36px;
        margin: 0 8px;
        border-radius: 26px;
        border: 1px solid rgba(238, 120, 0, 0.3);
        display: flex;
        font-size: 15px;
        // color: #FFF;
        color: #EE7800;
        background: rgba(255,255,255,0.25);
        font-weight: 500;
        align-items: center;
        justify-content: center;
        .date-comp-sign {
          margin: 0 2px;
          margin-left: 5px;
        }
      }
    }
    ::v-deep .content{
      height: calc(100vh - #{$a});
      // box-sizing: border-box;
      // height: 100vh;
      overflow-y: auto;
      &::-webkit-scrollbar {
        // background-color: transparent;
        display: none;
      }
      padding: 0 10px;
    }
    ::v-deep .hasData{
      margin-bottom: 100px;
    }
    ::v-deep .noData{
      width: 225px;
      height: 131px;
      margin: 0 auto;
      margin-top: 80px;
      img{
        width: 225px;
        height: 131px;
      }
      .noDataTitle{
        width: 100%;
        height: 24px;
        text-align: center;
        font-size: 17px;
        font-weight: 400;
        color: #999999;
        line-height: 24px;
        margin-top: 21px;
      }
    }
    ::v-deep .userInfo{
      width: 100%;
      height: 70px;
      background: #fff;
      display: flex;
      align-items: center;
      justify-content:flex-start;
      margin-top: 13px;
      border-radius: 4px;
      .userImg{
        width: 44px;
        height: 44px;
        border-radius: 100%;
        overflow: hidden;
        margin: 13px 10px;
      }
      .userName{
        font-size: 21px;
        font-weight: 400;
        color: #000000;
        margin-right: 11px;
      }
      i{
        font-size: 13px;
        font-weight: 400;
        font-style: normal;
        color: #666666;
      }
    }
    ::v-deep .title{
      width: 100%;
      height: 42px;
      line-height: 42px;
      font-size: 13px;
      font-weight: 400;
      color: #A7A7A7;
      display: flex;
      align-items: center;
      justify-content:flex-start;
      span {
        padding-top: 2.5px;
      }
      img{
        width: 20px;
        height: 20px;
        margin: 0 5px 0 10px;
        vertical-align: text-top;
      }
    }
    ::v-deep .details{
      width: 100%;
      height: 143px;
      background: #fff;
      margin-bottom: 13px;
      border-radius: 4px;
      p{
        // width: 343px;
        height: 58px;
        line-height: 58px;
        text-align: left;
        font-size: 16px;
        font-weight: 500;
        color: #333333;
        // padding: 0 10px;
        margin: 0 10px;
        border-bottom: 1px solid #F9F9F9;
        span {
          padding-top: 2px;
        }
        img {
          width: 20px;
          height: 20px;
          vertical-align: text-top;
          margin-right: 10px;
        }
      }
      ul{
        width: 100%;
        height: 84px;
        li{
          float: left;
        }
        .licontent{
          width: 33%;
          height: 84px;
          display: flex;
          justify-content:center;
          flex-direction: column;
          span:nth-child(1){
            font-size: 13px;
            font-weight: 400;
            color: #999999;
            margin-bottom: 8px;
          }
          span:nth-child(2){
            font-size: 18px;
            font-weight: 400;
            color: #000000;
          }
        }
        .licontents{
          width: 49%;
          height: 84px;
          display: flex;
          justify-content:center;
          flex-direction: column;
          span:nth-child(1){
            font-size: 13px;
            font-weight: 400;
            color: #999999;
            margin-bottom: 8px;
          }
          span:nth-child(2){
            font-size: 18px;
            font-weight: 400;
            color: #000000;
          }
        }
        .liborder{
          width: 1px;
          height: 34px;
          margin-top: 25px;
          background: #EEEEEE;
        }
      }
    }
    .detailss{
      height: 228px;
    }
    ::v-deep .table-box {
      background: #fff;
      padding-bottom: 16px;
      border-radius: 4px;
      p{
        // width: 343px;
        width: 100%;
        height: 58px;
        line-height: 58px;
        text-align: left;
        font-size: 16px;
        font-weight: 500;
        color: #333333;
        padding: 0 10px;
        box-sizing: border-box;
        margin: 0 auto;
        // border-bottom: 1px solid #F9F9F9;
        display: inline-flex;
        align-items: center;
        img {
          width: 20px;
          height: 20px;
          // vertical-align: text-top;
          margin-right: 10px;
        }
      }
      .table-outer {
        padding: 0 10px;
      }
      .table {
        width: 100%;
        min-height: 48px;
        margin: 0 auto;
        thead{
          border: 1px solid #E6E6E6;
          border-right: 0;
          border-bottom: 0;
          tr{
            display: flex;
            height: 48px !important;
            line-height: 48px !important;
            background: #F7F7F7;
            font-size: 13px;
            font-weight: 400;
            th{
              flex: 1;
              display: block;
              color: #111;
              border-right: 1px solid #E6E6E6;
            }
            .th{
              flex: 1.5;
            }
          }
        }
        tbody{
          border-right: 1px solid #E6E6E6;
          border-bottom: 1px solid #E6E6E6;
        }
        tr{
          display: flex;
          height: 36px !important;
          line-height: 36px !important;
          td {
            flex: 1;
            color: #474747;
            font-size: 13px;
            font-weight: 400;
            vertical-align: middle;
            border: 1px solid #E6E6E6;
          }
          .td{
            border-right: 0;
            border-bottom: 0;
          }
          .tds{
            flex: 1.5;
          }
        }

        .table-val {
          color: #333;
          font-size: 17px;
          font-weight: normal;
        }
      }
    }
  }
</style>
