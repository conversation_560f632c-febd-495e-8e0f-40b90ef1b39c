<template>
  <div :class="'myShowcase ' + `${pageType == 'goodsPool' ? 'goodsPoolOuter' : 'myShowcaseOuter'}`">
    <div class="topBlock">
      <van-button v-if="pageType != 'goodsPool'" icon="add-o" color="#F7830D" class="goPoolAdd" type="primary" block @click="goGoodsPool">从平台商城选品</van-button>
      <van-search v-else v-model="searchVal" shape="round" placeholder="请输入搜索关键词" class="searchLine" />
    </div>
    <div class="splitLine"></div>
    <template v-if="firstMenuList.length > 0">
      <div class="mainInner" v-if="!searchVal">
        <div class="leftMenu">
          <div class="leftMenuItem" :class="[item.id == firstMenuActive ? 'firstMenuActive' : '']"
            v-for="item in firstMenuList" :key="item.id" @click="firstMenuClick(item)">
            <div v-if="item.id == firstMenuActive" class="leftMenuActiveDot"></div>
            {{ item.name }}
          </div>
        </div>
        <div class="rightContent">
          <div class="topMenu" v-if="secondMenuList.length > 0">
            <div class="topMenuItem" ref="topMenuItem" :data-item="JSON.stringify(item)" :class="[item.id == secondMenuActive ? 'secondMenuActive' : '']"
              v-for="item in secondMenuList" :key="item.id" @click="secondMenuClick(item)">
              {{ item.name }}
            </div>
          </div>
          <div class="goodsList" :class="[secondMenuList.length > 0 ? 'hasSecondClass' : '']" @scroll="listenerScroll" ref="goodsList">
            <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="getGoodsFun">
              <div class="goodsItem" ref="goodsItem" v-for="item in goodsList" :key="item.id" :data-item="JSON.stringify(item)" @click="goDetails(item)">
                <img class="goodsImg" :src="item.head_img" alt="">
                <div class="goodsInfo">
                  <div>
                    <div class="title textCut">{{ item.package_name }}</div>
                    <div class="desc textCut2">{{ item.summary }}</div>
                  </div>
                  <div class="priceLine">
                    <div class="price">
                      <div class="nowPrice">
                        <span style="font-size: 12px;">￥</span>{{Number(item.price).toString()}}
                      </div>
                      <div class="oldPrice">
                        <span style="font-size: 12px;">￥</span>{{ Number(item.market_price).toString() }}
                      </div>
                    </div>
                    <div v-if="pageType != 'goodsPool'" class="btn" @click.stop="removeGoods(item)">移除</div>
                    <div v-else class="btn addBtn" @click.stop="addToMyShowcaseFun(item)">添加</div>
                  </div>
                </div>
              </div>
            </van-list>
          </div>
        </div>
      </div>
      <div v-else class="goodsList searchGoodsList">
        <van-list v-model="loading" :finished="finished" :finished-text="searchFinishedText" @load="handleSearch">
          <div class="goodsItem" ref="goodsItem" v-for="item in goodsList" :key="item.id" :data-item="JSON.stringify(item)" @click="goDetails(item)">
            <img class="goodsImg" :src="item.head_img" alt="">
            <div class="goodsInfo">
              <div class="title textCut">{{ item.package_name }}</div>
              <div class="desc textCut2">{{ item.summary }}</div>
              <div class="priceLine">
                <div class="price">
                  <div class="nowPrice">
                    <span style="font-size: 12px;">￥</span>{{Number(item.price).toString()}}
                  </div>
                  <div class="oldPrice">
                    <span style="font-size: 12px;">￥</span>{{Number(item.market_price).toString()}}
                  </div>
                </div>
                <div v-if="pageType != 'goodsPool'" class="btn" @click.stop="removeGoods(item)">移除</div>
                <div v-else class="btn addBtn" @click.stop="addToMyShowcaseFun(item)">添加</div>
              </div>
            </div>
          </div>
        </van-list>
      </div>
    </template>
    <div v-else class="noData">暂无数据</div>
    <van-overlay :show="isLoadingShow" duration="0.1">
      <van-loading class="loading" type="" size="40" color="#F7830D" />
    </van-overlay>
  </div>
</template>

<script>
import { getCategory,getCategoryForDoc,getShowcaseGoods,getAllGoods,addToMyShowcase,removeFromMyShowcase } from '@/api/docWorkRoom'
import { getSystemType } from '@/utils/utils'
import { Toast } from 'vant'
export default {
  data() {
    return {
      firstMenuList: [],
      firstMenuActive: 0,
      firstMenuLoadId: 0,
      secondMenuList: [],
      secondMenuLoadList: [],
      secondMenuActive: 0,
      secondMenuLoadId: 0,
      loading: false,
      finished: true,
      pageType: 'goodsPool',
      goodsList: [],
      page: 1,
      scrollTimer: null,
      searchVal: '',
      searchTimer: null,
      searchFinishedText: '',
      isLoadingShow: true
    }
  },
  watch: {
    searchVal(newVal) {
      if(!newVal){
        this.page = 1
        this.goodsList = []
        this.init()
        this.isLoadingShow = true
        return
      }
      if (this.searchTimer) {
        clearTimeout(this.searchTimer)
      }
      this.goodsList = []
      this.page = 0
      this.finished = true
      this.searchFinishedText = '搜索中...'
      this.searchTimer = setTimeout(() => {
        this.handleSearch()
      }, 1000);
    },
  },
  methods: {
    handleSearch(){
      let obj = {
        page: this.page += 1,
        page_size: 10,
        package_name: this.searchVal,
        category_id: 0,
        sub_category_id: 0
      }
      getAllGoods(obj).then(res => {
        if (res.code == 200) {
          let { list, pager } = res.data
          if (this.page === 1) {
            this.goodsList = list
          } else {
            this.goodsList = [...this.goodsList, ...list]
          }
          this.loading = false
          if (!pager.has_next) {
            this.finished = true
            this.searchFinishedText = '没有更多了'
          }else{
            this.finished = false
          }
        }
      })
    },
    addToMyShowcaseFun({ package_id }){
      addToMyShowcase({package_id}).then(res=>{
        if(res.code == 200){
          if(this.searchVal != ''){
            this.page = 0
            this.goodsList = []
            this.finished = true
            this.searchFinishedText = '搜索中...'
            this.handleSearch()
            return
          }
          Toast('添加成功')
          localStorage.setItem('firstMenuActive',this.firstMenuActive)
          // 此方法作用：url携带&invite_reload=1都会被刷新，所以当前页也会被刷新 不需要reload手动刷新了
          if (getSystemType() === 'ios') {
            window.webkit.messageHandlers.nextRefresh.postMessage('')
          } else if (getSystemType() === 'android') {
            setTimeout(()=>{
              window.android.nextRefresh()
            },1000)
          }
          // this.init()
          // window.location.reload()
        }
      })
    },
    removeGoods({ package_id }){
      removeFromMyShowcase({package_id}).then(res=>{
        if(res.code == 200){
          Toast('移除成功')
          // 此方法作用：url携带&invite_reload=1都会被刷新，所以当前页也会被刷新 不需要reload手动刷新了
          if (getSystemType() === 'ios') {
            window.webkit.messageHandlers.nextRefresh.postMessage('')
          } else if (getSystemType() === 'android') {
            setTimeout(()=>{
              window.android.nextRefresh()
            },1000)
          }
          // this.init()
          // window.location.reload()
        }
      })
    },
    goDetails({ package_id,package_name }){
      let url = `/docWorkRoom/goodsDetails?package_id=${package_id}&package_name=${package_name}&from=${this.pageType}`
      if (getSystemType() == 'ios') {
        window.webkit.messageHandlers.breathMessage.postMessage(encodeURI(url))
      } else {
        window.android.breathMessage(url)
      }
      // this.$router.push({
      //   path: '/docWorkRoom/goodsDetails',
      //   query: {
      //     package_id,
      //     package_name
      //   }
      // })
    },
    // resetFun(){
    //   this.goodsList = []
    //   this.page = 1
    //   this.getGoodsFun()
    // },
    firstMenuClick(item){
      console.log(item)
      // 切换菜单并获取数据
      // this.firstMenuActive = item.id
      // if(item.children.length > 0){
      //   this.secondMenuList = item.children
      //   this.secondMenuActive = this.secondMenuLoadId = item.children[0].id
      // }else{
      //   this.secondMenuList = []
      //   this.secondMenuActive = this.secondMenuLoadId = 0
      // }
      // this.resetFun()

      // 滑动到菜单对应的内容区域
      // this.firstMenuActive = this.firstMenuLoadId = item.id
      this.firstMenuLoadId = item.id
      const items = this.$refs.goodsItem
      for(let inner of items){
        let curObj = JSON.parse(inner.dataset.item)
        if(curObj.cate_pid == 0){
          if(curObj.cate_id == item.id){
            if(getSystemType() == 'ios'){
              inner.scrollIntoView({ behavior: 'smooth', block: 'start' })
            }else{
              inner.scrollIntoView({ block: 'start' })
            }
            break
          }
        }else{
          if(curObj.cate_pid == item.id){
            if(getSystemType() == 'ios'){
              inner.scrollIntoView({ behavior: 'smooth', block: 'start' })
            }else{
              inner.scrollIntoView({ block: 'start' })
            }
            break
          }
        }
      }
    },
    secondMenuClick(item){
      console.log(item.id)
      // this.secondMenuActive = this.secondMenuLoadId = item.id
      // this.resetFun()
      this.secondMenuLoadId = item.id
      const items = this.$refs.goodsItem
      for(let inner of items){
        let curObj = JSON.parse(inner.dataset.item)
        if(curObj.cate_id == item.id){
          if(getSystemType() == 'ios'){
            inner.scrollIntoView({ behavior: 'smooth', block: 'start' })
          }else{
            inner.scrollIntoView({ block: 'start' })
          }
          break
        }
      }
      this.secondMenuScrollToView()
    },
    secondMenuScrollToView(){
      const items = this.$refs.topMenuItem
      if(!items) return 
      for(let item of items){
        let curObj = JSON.parse(item.dataset.item)
        if(curObj.id == this.secondMenuActive){
          if(getSystemType() == 'ios'){
            item.scrollIntoView({ behavior: 'smooth', block: 'start' })
          }else{
            item.scrollIntoView({ block: 'start' })
          }
          break
        }
      }
    },
    getGoodsFun(firstMenuId, secondMenuId){
      let obj = {
        page: this.page,
        page_size: 10,
        sub_category_id: secondMenuId ? (Number(secondMenuId) || 0) : this.secondMenuActive,
        category_id: firstMenuId || this.firstMenuActive
      }
      let commonApi = this.pageType == 'goodsPool' ? getAllGoods : getShowcaseGoods
      let that = this
      commonApi(obj).then(res => {
        if (res.code == 200) {
          let { list, pager } = res.data
          // if (this.page === 1) {
          //   this.goodsList = list
          // } else {
          //   this.goodsList = [...this.goodsList, ...list]
          // }
          this.goodsList = [...this.goodsList, ...list]
          if(this.goodsList.length > 0){
            this.firstMenuActive = this.goodsList[0].cate_pid ? this.goodsList[0].cate_pid : this.goodsList[0].cate_id
            this.secondMenuList = this.firstMenuList.find(item=>item.id == this.firstMenuActive).children
            this.secondMenuActive = this.secondMenuList.length > 0 ? this.secondMenuList[0].id : ''
          }
          
          this.loading = false
          if (!pager.has_next) {
            this.finished = true
            this.page = 1
            this.loadMoreSecond()
          } else {
            this.finished = false
            this.page += 1
            this.getGoodsFun(firstMenuId, secondMenuId)
          }
          if(that.isLoadingShow){
            let {id,children} = this.firstMenuList[this.firstMenuList.length - 1]
            // 最后一个大类 如果有二级分类
            if(children && children.length > 0){
              // 且二级分类的最后一个id 是 当前加载的二级分类id 则取消loading
              if(children[children.length - 1].id == that.secondMenuLoadId){
                // that.isLoadingShow = false
                // this.handleGoodsListH()
                console.log('1111111111111111111')
                this.handleLoadEnd()
              }
            }else{ 
              // 如果没有二级分类，但是当前加载的一级分类id 是 最后一个大类id 则取消loading
              if(id && id == that.firstMenuLoadId){
                // that.isLoadingShow = false
                // this.handleGoodsListH()
                console.log('22222222222222222222')
                this.handleLoadEnd()
              }
            }
          }
        }
      })
    },
    getShowcaseCateFun(){
      let commonApi = this.pageType == 'goodsPool' ? getCategory : getCategoryForDoc
      commonApi().then(res => {
        if(res.code == 200){
          this.goodsList = []
          this.page = 1
          if(res.data.length > 0){
            this.firstMenuList = res.data
            this.firstMenuActive = this.firstMenuLoadId = this.firstMenuList[0].id
            this.secondMenuList = this.secondMenuLoadList = this.firstMenuList[0].children
            this.secondMenuActive = this.secondMenuLoadId = this.secondMenuList.length > 0 ? this.secondMenuList[0].id : 0
          }else{
            this.isLoadingShow = false
            this.handleGoodsListH()
          }
          
          // 初始化默认选中的一级二级分类
          // this.firstMenuActive = this.firstMenuLoadId = this.firstMenuList.length > 0 ? this.firstMenuList[0].id : 0
          // this.secondMenuList = this.secondMenuLoadList = this.firstMenuList.length > 0 ? this.firstMenuList[0].children : []
          // this.secondMenuActive = this.secondMenuLoadId = this.secondMenuList.length > 0 ? this.secondMenuList[0].id : 0
          this.finished = false
          // this.getGoodsFun()
        }
      })
    },
    loadMoreSecond(){
      let curSecondIndex = this.secondMenuLoadList.findIndex(item=> item.id == this.secondMenuLoadId)
      // 当前二级分类是否是最后一个
      // 不是最后一个
      if(curSecondIndex != this.secondMenuLoadList.length - 1){
        this.secondMenuLoadId = this.secondMenuLoadList[curSecondIndex + 1].id
        this.getGoodsFun(this.firstMenuLoadId,this.secondMenuLoadId)
      }else{
        // 最后一个
        // this.finished = true
        this.loadMoreFirst()
      }
      console.log(curSecondIndex,this.secondMenuLoadList.length - 1)
    },
    loadMoreFirst(){
      let curFirstIndex = this.firstMenuList.findIndex(item=> item.id == this.firstMenuLoadId)
      // 当前一级分类是否是最后一个
      // 不是最后一个
      if(curFirstIndex != this.firstMenuList.length - 1){
        let nextMenu = this.firstMenuList[curFirstIndex + 1]
        this.firstMenuLoadId = nextMenu.id
        this.secondMenuLoadList = nextMenu.children
        this.secondMenuLoadId = nextMenu.children.length > 0 ? this.secondMenuLoadList[0].id : '0'
        this.getGoodsFun(this.firstMenuLoadId,this.secondMenuLoadId)
      }else{
        // 最后一个
        this.finished = true
      }
    },
    listenerScroll(event){
      // if (this.scrollTimer) {
      //   clearTimeout(this.scrollTimer)
      // }
      // this.scrollTimer = setTimeout(() => {
        this.handleScroll(event)
      // }, 100)
    },
    handleScroll(event){
      const items = event.target.querySelectorAll('.goodsItem')
      // let finalItem = items[items.length - 1]
      // const finalItemRect = finalItem.getBoundingClientRect()
      // 如果最后一个条目出现在视口中，则直接将二级菜单的最后一个置为高亮 选中
      // if(finalItemRect.top <= window.innerHeight){
      //   let curObj = JSON.parse(finalItem.dataset.item)
      //   this.secondMenuActive = Number(curObj.cate_id)
      //   this.secondMenuScrollToView()
      //   return
      // }
      items.forEach(item => {
        const rect = item.getBoundingClientRect()
        // console.log(rect.top,rect.bottom)
        // top：表示元素顶部到视口顶部的距离  bottom：表示元素底部到视口顶部的距离
        if (rect.top <= 120 && rect.bottom >= 100) {
          console.log(JSON.parse(item.dataset.item).cate_id,'------')
          let curObj = JSON.parse(item.dataset.item)
          let catePid = Number(curObj.cate_pid)
          let cateId = Number(curObj.cate_id)
          // 此元素的一级菜单
          if(catePid != 0){
            this.firstMenuActive = catePid
            this.secondMenuList = this.firstMenuList.find(item => item.id == catePid).children
          }else{
            this.firstMenuActive = cateId
            this.secondMenuList = []
          }
          this.secondMenuActive = cateId
          // console.log(item.dataset.item)
        }
      })
      this.secondMenuScrollToView()
    },
    goGoodsPool(){
      localStorage.removeItem('firstMenuActive')
      let url = `/docWorkRoom/myShowcase/goodsList?pageType=goodsPool&invite_reload=1`
      if (getSystemType() == 'ios') {
        window.webkit.messageHandlers.breathMessage.postMessage(url)
      } else {
        window.android.breathMessage(url)
      }
      // this.$router.push({
      //   path: '/docWorkRoom/myShowcase/goodsList',
      //   query: {
      //     pageType: 'goodsPool'
      //   }
      // })
    },
    // 处理数据加载完之后
    handleLoadEnd(){
      let firstMenuActive = localStorage.getItem('firstMenuActive')  // 读local看是否有记录的一级分类
      let isExist = this.firstMenuList.some(item => item.id == firstMenuActive)  // 这个记录的一级分类在最新的分类列表中存在
      // 记录了一级分类 && 当前页面是商品池 && 这个记录的一级分类在分类列表中存在 自动滑动到记录的分类
      if(firstMenuActive && this.pageType == 'goodsPool' && isExist){
        let obj = {id: firstMenuActive}
        setTimeout(()=>{
          this.firstMenuClick(obj)  //自动滑动到记录的分类
          this.handleGoodsListH()  // 将商品列表高度加高 使商品列表的最后一条可以滑动到最顶部
          this.isLoadingShow = false  // 去掉loading
        },1000)
      }else{
        this.handleGoodsListH()
        this.isLoadingShow = false
      }
    },
    handleGoodsListH(){
      const el = this.$refs.goodsList
      if (el) {
        let height = el.offsetHeight
        console.log('元素高度:', height)
        let paddingBottomH = height - 120 - 40  // 减去一个条目的高度 和 二级分类的高度
        el.style.paddingBottom = `${paddingBottomH}px`
      }
    },
    init(){
      this.pageType = this.$route.query.pageType
      document.title = this.pageType == 'goodsPool' ? '平台商城' : '我的橱窗'
      this.getShowcaseCateFun()
    }
  },
  created() {

  },
  mounted() {
    this.init()
  }
}
</script>

<style lang='scss'>
.goodsPoolOuter{

}
.myShowcaseOuter{
  .topBlock {
    padding: 10px 12px 0;
  }
  .splitLine{
    margin-top: 10px;
  }
  .mainInner{
    height: calc(100% - 70px) !important;
  }
}
.myShowcase {
  // font-family: '';
  width: 100vw;
  height: 100vh;
  .topBlock {
    // padding: 0 12px;
  }

  .splitLine {
    width: 100%;
    height: 10px;
    background: #F6F6F6;
    // margin-top: 10px;
  }
  .searchLine{
    height: 50px;
  }
  .goPoolAdd{
    border-radius: 4px;
    height: 40px;
  }
  .mainInner {
    display: flex;
    width: 100%;
    height: calc(100% - 60px);
    .leftMenu::-webkit-scrollbar {
      display: none; /* 隐藏滚动条 */
    }
    .leftMenu {
      width: 90px;
      min-width: 90px;
      height: 100%;
      overflow: auto;
      background: #F6F6F6;
      box-sizing: border-box;

      .leftMenuItem {
        min-height: 50px;
        color: #666;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        padding: 0 12px;
        line-height: 16px;
        position: relative;
        .leftMenuActiveDot{
          width: 4px;
          height: 20px;
          background: #F7830D;
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
        }
      }
      .firstMenuActive{
        // font-weight: bold;
        font-weight: 500;
        color: #333;
        background: white;
      }
    }

    .rightContent {
      flex: 1;
      // width: calc(100% - 96px);
      position: relative;
      box-sizing: border-box;
      .topMenu {
        height: 38px;
        box-shadow: 0 4px 6px -4px rgba(0, 0, 0, 0.2);
        position: absolute;
        width: 100%;
        top: 0;
        z-index: 999999;
        display: flex;
        align-items: center;
        overflow: auto;
        padding-right: 8px;
        box-sizing: border-box;
        &::-webkit-scrollbar{
          display: none;
        }
        .topMenuItem{
          display: flex;
          align-items: center;
          justify-content: center;
          flex: 0 0 auto;
          // width: 72px;
          padding: 0 10px;
          height: 28px;
          border-radius: 30px;
          background: #F6F6F6;
          color: #5E5E5E;
          font-size: 12px;
          margin-left: 8px;
        }
        .secondMenuActive{
          background: #FFF6ED;
          color: #F7830D;
          // font-weight: bold;
          font-weight: 500;
        }
      }
      .hasSecondClass {
        margin-top: 38px;
        height: calc(100% - 38px) !important;
      }
    }
  }
  .searchGoodsList{
    height: calc(100% - 60px) !important;
  }
  .goodsList{
    margin-left: 8px;
    height: 100%;
    box-sizing: border-box;
    // margin-top: 38px;
    // height: calc(100% - 38px);
    overflow: auto;
    .goodsItem{
      display: flex;
      // align-items: center;
      padding: 10px 0 2px;
      // border: .5px solid;
      .goodsImg{
        width: 90px;
        min-width: 90px;
        height: 90px;
        border-radius: 10px;
        // border: 1px solid;
      }
      .goodsInfo{
        display: flex;
        flex-direction: column;
        text-align: left;
        padding: 0 16px 0 8px;
        flex: 1;
        height: 90px;
        justify-content: space-between;
        .title{
          font-size: 16px;
          color: #333;
          // font-weight: bold;
          font-weight: 500;
          line-height: normal;
        }
        .desc{
          font-size: 12px;
          color: #999;
          // margin-top: 6px;
          line-height: normal;
        }
        .priceLine{
          display: flex;
          justify-content: space-between;
          align-items: flex-end;
          // margin-top: 12px;
        }
        .price{
          display: flex;
          align-items: flex-end;
        }
        .nowPrice{
          color: #FE3C26;
          font-size: 14px;
          font-weight: bold;
        }
        .oldPrice{
          color: #BEBEBE;
          font-size: 10px;
          margin-left: 2px;
          text-decoration: line-through;
        }
        .btn{
          font-family: '';
          font-size: 12px;
          width: 48px;
          height: 25px;
          border-radius: 4px;
          color: #F7830D;
          border: 1px solid #F7830D;
          border-color: #F7830D;
          display: flex;
          justify-content: center;
          align-items: center;
          font-weight: bold;
        }
        .addBtn{
          background: #F7830D;
          color: white;
          font-weight: bold;
        }
      }
    }
    
  }
  .textCut2{
    display: -webkit-box;
    -webkit-line-clamp: 2; /* 限制为两行 */
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all;
  }
  .textCut{
    display: -webkit-box;
    -webkit-line-clamp: 1; /* 限制为两行 */
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all;
  }
  .noData{
    font-size: 14px;
    color: #999999;
    margin-top: 20px;
  }
  .loading{
    position: fixed;
    z-index: 999999999;
    top: 30%;
    left: 50%;
    transform: translateX(-50%);
  }
  .van-overlay{
    background-color: rgba(0,0,0,.4);
    z-index: 99999999;
  }
  .van-icon-search{
    color: #BABABA;
  }
  .van-icon-add-o{
    font-size: 16px;
  }
  input::placeholder {
    color: #999999;
  }
}
</style>