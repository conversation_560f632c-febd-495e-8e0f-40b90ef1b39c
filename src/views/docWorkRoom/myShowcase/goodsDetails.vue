<template>
  <div class="goodsDetails">
    <img class="img" :src="imgUrl" alt="">
    <div class="btnBox">
      <van-button v-if="added" class="btn removeBtn" block @click.stop="removeGoods(item)">移除</van-button>
      <van-button v-else class="btn addBtn" icon="add-o" block @click.stop="addToMyShowcaseFun(item)">添加到我的橱窗</van-button>
    </div>
    <van-dialog v-model="isQrcodePopShow" title="" 
      show-cancel-button 
      confirm-button-color="#F7830D" 
      confirm-button-text="保存图片" 
      cancel-button-color="#333333" 
      close-on-click-overlay
      @confirm="handleDownload">
      <div class="qrcodeInner" id="qrcodeInner">
        <div class="innerTitle">{{ dialogTitle }}</div>
        <img class="qrcodeImg" crossorigin="anonymous" :src="`${qrcodeUrl}?t=${ts}`" alt="">
      </div>
    </van-dialog>
  </div>
</template>

<script>
import { goodsDetail,addToMyShowcase,removeFromMyShowcase,packageIntroListItemCode } from '@/api/docWorkRoom'
import { getSystemType } from '@/utils/utils'
import html2canvas from "html2canvas"
const UA = navigator.userAgent;
const isAndroid = UA.indexOf("Android") > -1 || UA.indexOf("Adr") > -1; // android终端
const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); // ios终端
export default {
  data() {
    return {
      packageId: "",
      imgUrl: "",
      added: false,
      isQrcodePopShow: false,
      dialogTitle: '',
      qrcodeUrl: '',
      ts: new Date().getTime(),
      package_doc_id: '',
    }
  },
  methods: {
    getDetails(val) {
      goodsDetail({ package_id: this.packageId }).then(res => {
        if(res.code == 200){
          this.imgUrl = res.data.doc_detail_img
          this.added = res.data.added
          this.package_doc_id = res.data.package_doc_id
          if(val && val == 'refresh'){
            if (getSystemType() === 'ios') {
              window.webkit.messageHandlers.nextRefresh.postMessage('')
            } else if (getSystemType() === 'android') {
              window.android.nextRefresh()
            }
          }
        }
      })
    },
    addToMyShowcaseFun(){
      addToMyShowcase({ package_id: this.packageId }).then(res=>{
        if(res.code == 200){
          this.getDetails('refresh')
        }
      })
    },
    removeGoods(){
      removeFromMyShowcase({ package_id: this.packageId }).then(res=>{
        if(res.code == 200){
          this.getDetails('refresh')
        }
      })
    },
    showShareQRcode(){
      let obj = {
        package_doc_id: this.package_doc_id,
        package_id: this.packageId
      }
      packageIntroListItemCode(obj).then(res=>{
        if(res.code == 200){
          let {package_name,qrcode_url} = res.data
          this.dialogTitle = package_name
          this.qrcodeUrl = qrcode_url
          this.isQrcodePopShow = true
        }else{
          this.$toast(res.msg || '请求错误')
        }
      })
    },
    handleDownload() {
      const that = this
      const element = document.getElementById('qrcodeInner')
      this.ts = +new Date()
    
      // 使用 html2canvas 截取指定区域内容并生成图片
      html2canvas(element, {
        scale: 3,
        allowTaint: true,
        useCORS: true,
        backgroundColor: '#FFFFFF',
        // ignoreElements: [document.querySelector('.footer')],
        onclone: () => {
          that.ts = +new Date()
        }
      }).then((canvas) => {
          // 将 Canvas 转换为图片并保存到手机本地
          // let image = canvas.toDataURL('image/png').replace('image/png', 'image/octet-stream')

          const image = canvas.toDataURL('image/png')


          const base64 = image.slice(22)
          console.log(base64)
          if (isIOS) {
            window.webkit.messageHandlers.saveImageToAlbum.postMessage(base64)
          }
          if (isAndroid) {
            window.android.saveImageToAlbum(base64)
          }
      })
    },
  },
  created() {
    
  },
  mounted() {
    let {package_id,package_name} = this.$route.query
    document.title = package_name
    this.packageId = package_id
    this.getDetails()
    window.showShareQRcode = this.showShareQRcode
    // setTimeout(() => {
    //   this.showShareQRcode()
    // }, 3000);
  }
}
</script>

<style lang='scss'>
.goodsDetails{
  padding-bottom: 70px;
  .img{
    width: 100%;
  }
  .btnBox{
    background: white;
    padding: 0 30px;
    position: fixed;
    bottom: 0;
    width: 100%;
    height: 70px;
    box-sizing: border-box;
    // display: flex;
    // align-items: center;
    .btn{
      border-radius: 8px;
      font-size: 16px;
      margin-top: 16px;
    }
    .removeBtn{
      color: #F7830D;
      border: 1px solid #F7830D;
    }
    .addBtn{
      color: white;
      background: #F7830D;
    }
  }
  .van-icon-add-o{
    font-size: 16px;
  }
  .van-button__text{
    font-weight: bold;
  }
  .qrcodeInner{
    padding: 30px 16px 25px;
    .innerTitle{
      font-size: 18px;
      font-weight: bold;
      line-height: 22px;
    }
    .qrcodeImg{
      width: 192px;
      height: 192px;
      margin-top: 20px;
    }
  }
}
</style>