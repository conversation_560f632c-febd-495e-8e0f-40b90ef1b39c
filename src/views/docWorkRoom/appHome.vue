<template>
  <div class="appHome">
    <!-- <div class="titleine">
      <div class="dot/template>
      <div class="titleText">{{ detailForm.intro_promotion && detailForm.intro_promotion.title || '智众医疗慢病管理云平台介绍' }}</div>
    </div> -->
    <div class="swipeOuter" v-if="detailForm.home_banner.length > 0">
      <van-swipe class="swipe" v-model="activeIndex" @change="onChange" :autoplay="autoPlay" :stop-propagation="false">
        <van-swipe-item class="swipeItem" v-for="(item, index) in detailForm.home_banner" :key="index">
          <template v-if="item.jump_type == 0">
            <img class="imgItem" @click="redirectTo(item)" :src="item.banner" alt="">
          </template>
          <template v-else>
            <!-- <video class="videoItem" 
              ref="video"
              webkit-playsinline='true'
              playsinline='true'
              :poster="item.cover_img"
              @play="playVideo"
              @pause="pauseVideo"
              :src="item.resource"
              controls
              controlslist="nodownload noplaybackrate  noremoteplayback"
              :disablePictureInPicture="true"
              v-on:fullscreenchange="handleFullscreenChange"
              v-on:webkitfullscreenchange="handleFullscreenChange"
            ></video> -->
            <!-- <img class="videoCoverImg" v-if="isvideoCoverImgShow" @click="playVideo(item)" :src="item.cover_img" alt=""> -->
            <img class="videoCoverImg" @click="playVideo(item)" :src="item.banner" alt="">
          </template>
        </van-swipe-item>
      </van-swipe>
    </div>
    <!-- <div v-else class="swipeOuter">
      <div class="videoItem blockBg"></div>
    </div> -->
    <div class="hotProsLine">
      <div class="hotPros">
        <span class="hotProText">{{ detailForm.intro_project && detailForm.intro_project.title || '热门项目' }}</span>
        <span class="sub-title">&nbsp;打造数字科室新模式</span>
        <!-- <div class="bottomLine"></div> -->
      </div>
      <div class="loadMore" @click="goDetail(detailForm.intro_project.btn_link)">{{ detailForm.intro_project &&
        detailForm.intro_project.btn_name || '了解更多' }}</div>
    </div>
    <div class="prosOuter" v-if="detailForm.intro_project">
      <img class="proImg hotProImg" v-for="(item, index) in detailForm.intro_project.data" :key="index"
        :src="item.resource" alt="" @click="goDetail(item.link)">
    </div>
    <div class="prosOuter" v-else>
      <div class="proImg hotProImg blockBg" v-for="(item, index) in [0, 1, 2, 3, 4, 5]" :key="index"></div>
    </div>
    <div class="splitLine"></div>

    <div class="hotProsLine" v-if="cases.length > 0">
      <div class="hotPros">
        <span class="hotProText">案例动态</span>
      </div>
      <div class="loadMore" @click="goMoreCase()">更多动态</div>
    </div>
    <div class="prosOuter" v-if="cases.length > 0">
      <ul class="case-dynamic">
        <li class="item" v-for="(item, index) in cases" :key="index" @click="goCaseDetail(item)">
          <div class="border"></div>
          <div class="rt">
            <img class="cover" :src="item.share_custom_icon" :alt="item.type">
          </div>
          <div class="lf">
            <div class="tp">
              <span class="title" v-if="item.suggest_word">【{{ item.suggest_word }}】</span>
              <!-- <span class="content" v-if="item.suggest_word">【{{ item.suggest_word }}】</span> -->
              <span class="content" v-text="item.share_custom_title"></span>
            </div>
            <div class="bt">
              <span class="type" v-text="item.project_name || ''"></span>
              <!-- <span class="date" v-text="item.suggest_date_at ? item.suggest_date_at.split(' ')[0] : ''"></span> -->
            </div>
          </div>
        </li>
      </ul>
    </div>
    <div class="splitLine" v-show="cases.length > 0"></div>
    <!-- <div class="prosOuter" v-else>
      <div class="proImg specialProImg blockBg" v-for="(item,index) in [0,1,2]" :key="index"></div>
    </div> -->

    <div class="hotProsLine">
      <div class="hotPros">
        <span class="hotProText">{{ detailForm.intro_community && detailForm.intro_community.title || '网上社区' }}</span>
        <span class="sub-title">&nbsp;打造医生品牌新平台</span>
        <!-- <div class="bottomLine"></div> -->
      </div>
    </div>
    <div class="prosOuter" v-if="detailForm.intro_community">
      <img class="proImg specialProImg" v-for=" (item, index) in detailForm.intro_community.data" :key="index"
        :src="item.resource" alt="" @click="goDetail(item.link)">
    </div>
    <div class="prosOuter" v-else>
      <div class="proImg specialProImg blockBg" v-for="(item, index) in [0, 1]" :key="index"></div>
    </div>
    <div v-if="!isMobile()" class="bottomBlock">
      <div class="qrCodeOuter">
        <img class="qrcode" src="https://ares.zz-med.com/doctorapp/docAppDownloadQrCode.png" alt="">
        <div class="tips">扫码下载医生工作室App 了解更多医疗信息</div>
      </div>
      <div class="feedBackBtn" @click="feedBack">
        <van-icon class="icon" name="chat-o" size="16" />
        在线反馈
      </div>
      <div class="telText">售后电话：************</div>
    </div>
    <PromoteDownloadPop v-if="isPromotePopShow"></PromoteDownloadPop>
  </div>
</template>

<script>
import { getAppHomeDataNew, getPatientHomePage } from '@/api/docWorkRoom'
import { getSystemType, isMobile } from '@/utils/utils'
import PromoteDownloadPop from './projectMain/components/promoteDownloadPop.vue';
import { getTrainingPlatformUrl } from "@/api/common/projectMain.js";
export default {
  components: { PromoteDownloadPop },
  data() {
    return {
      autoPlay: '3000',
      activeIndex: 0,
      detailForm: {},
      isvideoCoverImgShow: true,
      bannerList: [],
      isPromotePopShow: false,
      cases: [],
      isSaasWInApp: null
    }
  },
  methods: {
    isMobile,
    feedBack() {
      if (!isMobile()) {
        window.parent.postMessage({ type: 'feedBack', data: '' }, `${process.env.VUE_APP_SAAS_PC_URL}`)
        return
      }
    },
    getConfigData() {
      getAppHomeDataNew().then(res => {
        if (res.code == 200) {
          this.detailForm = res.data
          // let userManual = {
          //   'type': 'img',
          //   'imgPath': 'https://zz-med-pub.oss-cn-hangzhou.aliyuncs.com/applet/health-assistant/banner_userManual.png',
          //   'url': 'https://mp.weixin.qq.com/s/l0kflGvxwwpnKZrQNeHd0A',
          // }
          // let live = {
          //   'type': 'img',
          //   'imgPath': 'https://zz-med-pub.oss-cn-hangzhou.aliyuncs.com/applet/health-assistant/banner_mmcLive.png',
          //   'url': 'https://wx.vzan.com/live/page/961971559?v=1716795680312',
          // }
          // this.bannerList = [live,userManual,...this.detailForm.intro_promotion.data]
          // console.log(this.bannerList)
          // this.detailForm.intro_promotion.data.push(obj)
        }
      })
    },
    async getPatientHome() {
      const res = await getPatientHomePage()

      if (res.code === 200) {
        this.cases = res.data || []
      }
    },
    onChange(index) {
      this.activeIndex = index
      // this.pauseVideo()
    },
    playVideo(item) {
      if (!isMobile()) {
        if (!this.isSaasWInApp) {
          this.isPromotePopShow = true
          return
        }
        let obj = {
          src: item.jump_url,
          title: item.title,
          cover_img: item.banner
        }
        window.parent.postMessage({ type: 'playVideo', data: obj }, `${process.env.VUE_APP_SAAS_PC_URL}`)
        return
      }
      // const video = this.$refs.video[0];
      // if(video) {
      //   video.play();
      // }

      if (getSystemType() === 'ios') {
        window.webkit.messageHandlers.videoFullScreenPlayback.postMessage(JSON.stringify({ 'url': item.jump_url }))
      } else if (getSystemType() === 'android') {
        window.android.videoFullScreenPlayback(item.jump_url)
      }

      // if(this.isvideoCoverImgShow) this.isvideoCoverImgShow = false
      // this.autoPlay = '0'
    },
    pauseVideo() {
      const video = this.$refs.video[0]
      if (video) video.pause();
      this.autoPlay = '3000'
    },
    // 案例动态跳转
    goCaseDetail(item) {
      // 分享页面 点击按钮 跳转下载工作室
      // if(this.isShare){
      //   this.goDownloadDocApp()
      //   return
      // }
      if (!item.is_click) return

      // if(!item.link_text){
      //   Toast('暂无配置功能链接，请联系管理员')
      //   return
      // }
      if (item.children && item.children.length) {
        if (
          item.extra_info.project_apply_mmc_type != -1 &&
          item.extra_info.project_apply_mmc_type != 0
        ) {
          let params = "";
          item.children.forEach((item) => {
            if (item.extra_info.project_apply_mmc_type_name == item.name) {
              params = item.id;
            }
          });
          this.openUrl(2, `/docWorkRoom/projectMain/dataList?signId=${params}&titleNmae=${encodeURIComponent(item.name)}`, item);
        } else {
          this.show = true;
          this.actions = item.children;
        }
      } else if (item.column_type == 5) {
        this.openUrl(2, `/docWorkRoom/projectMain/dataList?signId=${item.id}&titleNmae=${encodeURIComponent(item.name)}`, item);
      } else if (item.column_type == 8) {
        if (!isMobile()) {
          this.isPromotePopShow = true
          return
        }
        this.getTrainingPlatformUrlFun(item)
      } else {
        this.downloadIsOpen(item);
      }
    },
    // 医生工作室下载页面
    goDownloadDocApp() {
      return
      window.location.href = "https://patient-h5.zz-med.com/static/doc/View/download/Index.html"
    },
    openUrl(type, url, item = {}) {
      const UA = navigator.userAgent;
      const isAndroid = UA.indexOf("Android") > -1 || UA.indexOf("Adr") > -1; // android终端
      const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); // ios终端
      if (!url.startsWith('http')) {
        url = process.env.VUE_APP_BASE_URL + url.slice(1, url.length)
      }

      let param = JSON.stringify({
        type,
        url,
        shareTitle: item.share_custom_title,
        shareIcon: item.share_custom_icon,
        shareContent: ''
      });
      console.log(url);
      console.log(param);

      if (isMobile()) {
        if (isAndroid) {
          try {
            window.android.openOtherPage(type, url);
          } catch (error) {
          }
          try {
            window.android.openOtherPage(type, url, item.share_custom_title, item.share_custom_icon, '')
          } catch (error) {
          }
        } else if (isIOS) {
          window.webkit.messageHandlers.openOtherPage.postMessage(param);
        }
      } else {
        if (url.slice(0, 1) == '/') {
          // let tempUrl = url.slice(0,1) == '/' ? url.slice(1,url.length) : url
          url = process.env.VUE_APP_BASE_URL + url.slice(1, url.length)
        }
        if (url.indexOf('previewData') != -1 && url.indexOf('.mp4') != -1) {
          window.location.href = url
          return
        }
        if (url.indexOf('weixin') != -1 || (url.indexOf('previewData') != -1 && (url.indexOf('.pdf') != -1 || url.indexOf('.jpg') != -1 || url.indexOf('.png') != -1 || url.indexOf('.webp') != -1))) {
          window.openNewWindowFromSaas ? window.openNewWindowFromSaas(url) : window.open(url)
          return
        }
        if ([1, 6].indexOf(item.column_type) != -1 && item.resource_type == 1) {
          // this.isPromotePopShow = true
          // window.parent.postMessage({type: 'showNavBar',data: true},`${process.env.VUE_APP_SAAS_PC_URL}`)
          // window.location.href = url
          window.openNewWindowFromSaas ? window.openNewWindowFromSaas(url) : window.open(url)
          return
        }
        window.location.href = url
        // if(url.indexOf('weixin') != -1 || (url.indexOf('previewData') != -1 && (url.indexOf('pdf') != -1 || url.indexOf('jpg') != -1 || url.indexOf('png') != -1 || url.indexOf('webp') != -1))){
        //   window.openNewWindowFromSaas ? window.openNewWindowFromSaas(url) : window.open(url)
        // }else{
        //   window.location.href = url
        // }
      }

      // if (process.env.NODE_ENV === "production") {
      //   if (isAndroid) {
      //     window.android.openOtherPage(type, url);
      //   } else if (isIOS) {
      //     window.webkit.messageHandlers.openOtherPage.postMessage(param);
      //   }
      // } else {
      //   this.$router.push(url);
      // }
    },
    getTrainingPlatformUrlFun(item) {
      getTrainingPlatformUrl(item.extra_info.project_id).then(res => {
        if (res.code == 200) {
          if (isMobile()) {
            if (getSystemType() == 'ios') {
              let params = JSON.stringify({ type: 1, url: res.data.link })
              window.webkit.messageHandlers.openOtherPage.postMessage(params)
            } else {
              window.android.openOtherPage(1, res.data.link)
            }
          } else {
            if (res.data.link.indexOf('weixin') != -1) {
              window.openNewWindowFromSaas ? window.openNewWindowFromSaas(res.data.link) : window.open(res.data.link)
            } else {
              window.location.href = res.data.link
            }
            // window.location.href = res.data.link
          }
        }
      })
    },
    downloadIsOpen(item) {
      if (
        item.column_type == 5 ||
        (item.column_type == 6 && item.resource_type == 3)
      ) {
        this.openUrl(2, `/docWorkRoom/projectMain/dataList?signId=${item.id}&titleNmae=${encodeURIComponent(item.name)}`, item);
      } else {
        if (item.resource_type == 1) {
          this.openUrl(1, item.link_text, item);
        } else {
          if (item.link_text) {
            this.openUrl(1, item.link_text, item);
          } else {
            if (item.extra_info.project_attr_info.media[0].chunk_urls && item.extra_info.project_attr_info.media[0].chunk_urls.length) {
              this.openUrl(
                2,
                `/docWorkRoom/projectMain/previewData?format=${this.formatJudge(item.extra_info.project_attr_info.media[0].media_url)}&isPreview=1&urlPath=${encodeURIComponent(item.extra_info.project_attr_info.media[0].media_url)}&media_img=${encodeURIComponent(item.extra_info.project_attr_info.media[0].cover_image)}&titleNmae=${encodeURIComponent(item.name)}&flag=true`,
                item
              );
              localStorage.setItem("chunk_urls", JSON.stringify(item.extra_info.project_attr_info.media[0].chunk_urls));
            } else {
              this.openUrl(
                2,
                `/docWorkRoom/projectMain/previewData?format=${this.formatJudge(item.extra_info.project_attr_info.media[0].media_url)}&isPreview=1&urlPath=${encodeURIComponent(item.extra_info.project_attr_info.media[0].media_url)}&media_img=${encodeURIComponent(item.extra_info.project_attr_info.media[0].cover_image)}&titleNmae=${encodeURIComponent(item.name)}`,
                item
              );
            }
          }
        }
      }
    },
    formatJudge(url) {
      let dcode = decodeURIComponent(url);
      let parsedURL = new URL(dcode);
      let pathname = parsedURL.pathname;
      let fileExtension = pathname.split(".").pop().toLowerCase();
      return fileExtension;
    },
    // 跳转其他页面
    goDetail(item) {
      let { type = '', path = '' } = item
      if (!path) return
      if (type == 'h5') {
        let isProjectMain = path.indexOf('projectMain/projectMain') > -1 || path.indexOf('projectMain/newProjectMain') > -1
        if (isMobile()) {
          if (getSystemType() == 'ios') {

            isProjectMain ? window.webkit.messageHandlers.openProjectMain.postMessage(JSON.stringify({ 'parameter': path })) : window.webkit.messageHandlers.breathMessage.postMessage(JSON.stringify({ 'parameter': path }))

          } else {
            isProjectMain ? window.android.openProjectMain(path) : window.android.breathMessage(path)
          }
        } else {
          window.location.href = path
          // console.log(path)
        }
      } else if (type == 'origin') {
        if (!isMobile()) {
          // this.$toast('请下载医生工作室体验完整功能')
          this.isPromotePopShow = true
          return
        }
        if (getSystemType() == 'ios') {
          window.webkit.messageHandlers.jumpNativeMethod.postMessage(JSON.stringify({ 'parameter': path }))
        } else {
          window.android.jumpNativeMethod(path)
        }
      }
    },
    goMoreCase() {
      if (!isMobile()) {
        // this.$toast('请下载医生工作室体验完整功能')
        this.isPromotePopShow = true
        return
      }
      //更多案例动态
      if (process.env.NODE_ENV === 'production') {

        let param = `/docWorkRoom/news-list?origin=appHome`
        param = process.env.VUE_APP_BASE_URL + param.slice(1, param.length)
        if (getSystemType() == 'ios') {
          window.webkit.messageHandlers.breathMessage.postMessage(JSON.stringify({ 'parameter': param }))
        } else {
          try {
            window.android.openOtherPage(2, param);
          } catch (error) {
          }
          try {
            window.android.openOtherPage(2, param, '', '', '')
          } catch (error) {
          }
          //window.android.breathMessage(param)
        }
      } else {
        this.$router.push({
          path: '/docWorkRoom/news-list?origin=appHome',
          query: {
            origin: 'appHome',
          }
        })
      }
    },
    // 针对安卓做全屏处理
    handleFullscreenChange() {
      const video = this.$refs.video[this.activeIndex]
      console.log(video.src)
      if (document.fullscreenElement || document.webkitFullscreenElement) {
        if (getSystemType() == 'android') {
          window.android.fullScreenPlay(video.src)
        }
      }
    },
    redirectTo(item) {
      if (!item.jump_url) return
      if (isMobile()) {
        let param = {
          "parameter": item.jump_url,
          "shareTitle": item.title,
          "shareIcon": "https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/guoshou/logo.png",
          "shareContent": ''
        }
        if (getSystemType() == 'ios') {
          if (item.share == 1) {

            window.webkit.messageHandlers.breathMessage.postMessage(JSON.stringify(param));
          } else {
            window.webkit.messageHandlers.breathMessage.postMessage(JSON.stringify({ "parameter": item.jump_url }));
          }
        } else {
          if (item.share == 1) {
            try {
              window.android.breathMessage(item.jump_url);
            } catch (error) {
            }
            try {
              window.android.breathMessage(item.jump_url, param.shareTitle, param.shareIcon, param.shareContent)
            } catch (error) {
            }
          } else {
            window.android.breathMessage(item.jump_url,'','','');
          }

        }
      } else {
        if (item.jump_url.indexOf('polyv') != -1 || item.jump_url.indexOf('live') != -1) {
          // window.parent.postMessage({type: 'showNavBar',data: true},`${process.env.VUE_APP_SAAS_PC_URL}`)
          // window.location.href = item.link.path
          this.isPromotePopShow = true
          return
        }

        // if(item.jump_url.indexOf('weixin') != -1){
        //   window.openNewWindowFromSaas ? window.openNewWindowFromSaas(item.jump_url) : window.open(item.jump_url)
        //   return;
        // }
        // else{
        //   window.location.href = item.jump_url;
        //   return;
        // }
        window.openNewWindowFromSaas ? window.openNewWindowFromSaas(item.jump_url) : window.open(item.jump_url)
        // if(item.link.type && item.link.type == 'h5'){
        //   window.openNewWindowFromSaas ? window.openNewWindowFromSaas(item.jump_url) : window.open(item.jump_url)
        //   return
        // }
        // window.location.href = item.link.path
        // window.open(item.link.path)
      }
    },
    init() {
      let authorization = this.$route.query.authorization;
      localStorage.setItem('authorization', authorization)
      this.getConfigData()
      this.getPatientHome()

    },
    handleMessage(e) {
      let { type, data } = e.data
      console.log('H5接收到saas父页面的消息', type, data)
      // 播放视频
      if (type == 'isSaasWInApp') {
        this.isSaasWInApp = data
        console.log('接收到了isSaasWInApp', data)
      }
    }
  },
  created() {
    this.init()
  },
  mounted() {
    if (!isMobile()) {
      this.$nextTick(() => {
        window.parent.postMessage({ type: 'showNavBar', data: false }, `${process.env.VUE_APP_SAAS_PC_URL}`)
        window.parent.postMessage({ type: 'isLoaded', data: '' }, `${process.env.VUE_APP_SAAS_PC_URL}`)
      })
    }
    window.addEventListener('message', this.handleMessage, false);
  }
}
</script>

<style lang="scss" scoped>
.appHome {
  font-family: "PingFang SC";
  width: 100%;
  min-height: 100vh;
  padding: 10px 15px 5px;
  box-sizing: border-box;

  .titleine {
    display: flex;
    align-items: center;

    .dotText {
      color: #FFF;
      font-size: 12px;
      font-weight: 500;
      width: 32px;
      height: 20px;
      text-align: center;
      line-height: 20px;
      border-radius: 4px;
      background: #EE4700;
    }

    .titleText {
      color: #262626;
      font-size: 16px;
      font-weight: 500;
      margin-left: 8px;
    }
  }

  .swipeOuter {
    width: 100%;
    height: 100px;
    // margin-top: 16px;
    position: relative;

    .videoCoverImg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 99;
      border-radius: 8px;
    }

    .swipe,
    .swipeItem {
      width: 100%;
      height: 100px;
      border-radius: 8px;
    }

    .videoItem,
    .imgItem {
      width: 100%;
      height: 100px;
      border-radius: 8px;
    }
  }

  .hotProsLine {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 16px;

    .hotPros {
      display: flex;
      // flex-direction: column;
      align-items: center;

      .hotProText {
        color: #000;
        font-size: 17px;
        font-weight: 500;
      }

      .sub-title {
        font-size: 14px;
        color: #666;
      }

      .bottomLine {
        width: 26px;
        height: 3px;
        border-radius: 24px;
        background: #FF6F00;
        margin-top: 4px;
      }
    }

    .loadMore {
      width: 68px;
      height: 24px;
      text-align: center;
      line-height: 24px;
      border-radius: 63px;
      border: 0.5px solid #FFDDC3;
      color: #FF6F00;
      font-size: 12px;
      font-weight: 500;
      cursor: pointer;
    }
  }

  .splitLine {
    width: calc(100% + 30px);
    height: 10px;
    margin: 12px -15px 0;
    background-color: #F7F7F7;
  }

  .prosOuter {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    cursor: pointer;

    .case-dynamic {
      width: 100%;
      margin-top: 5px;

      .item {
        position: relative;
        display: flex;
        justify-content: space-between;
        align-items: center;
        // margin-top: 12px;
        padding: 7px 0;

        &:last-child {
          padding-bottom: 0;

          .border {
            display: none;
          }
        }

        .border {
          position: absolute;
          bottom: 0;
          left: 0;
          width: 100%;
          height: 1px;
          background-color: #EEEDED;
          transform: scaleY(0.5);
          transform-origin: bottom;
        }

        .lf {
          width: calc(100% - 80px);
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          height: 65px;

          .tp {
            display: -webkit-box;
            line-clamp: 2;
            -webkit-line-clamp: 2;
            box-orient: vertical;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-align: left;
            line-height: 1.5;
            font-size: 14px;

            .title {
              color: #FF6F00;
              font-weight: 400;
            }

            .content {
              color: #333;
              font-weight: 400;
            }
          }

          .bt {
            // margin-top: 15px;
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #999;

            .type {
              max-width: 150px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
        }

        .rt {
          width: 65px;

          .cover {
            width: 65px;
            height: 65px;
            border-radius: 8px;
            vertical-align: top;
          }
        }
      }
    }

    .proImg {
      width: 49%;
      height: 52px;
      margin-top: 8px;
      border-radius: 8px;
    }

    .hotProImg {
      &.blockBg {
        height: 66px;
      }
    }

    .specialProImg {
      height: 77px;
    }
  }

  .blockBg {
    background: #DFDFDF;
  }

  .bottomBlock {
    margin-top: 16px;

    .qrCodeOuter {
      padding: 16px 0;
      border-top: 1px solid #EFEFEF;
      border-bottom: 1px solid #EFEFEF;
    }

    .feedBackBtn {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #377CFB;
      height: 42px;
      border: 1px solid #377CFB;
      margin-top: 16px;
      border-radius: 6px;
      cursor: pointer;

      .icon {
        margin-right: 2px;
      }
    }

    .qrcode {
      width: 120px;
      height: 120px;
    }

    .tips {
      font-size: 16px;
      font-weight: 500;
      color: #000000;
      margin-top: 10px;
    }

    .telText {
      color: #999999;
      font-size: 18px;
      line-height: normal;
      margin-top: 16px;
      padding-bottom: 10px;
    }
  }
}
</style>