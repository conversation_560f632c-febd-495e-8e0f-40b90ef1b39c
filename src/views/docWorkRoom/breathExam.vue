<!--
 * @Descripttion: 眼底检查
 * @version: 
 * @Author: guxiang
 * @Date: 2021-12-16 14:13:26
 * @LastEditors: guxiang
 * @LastEditTime: 2021-12-16 14:13:26
-->
<template>
  <div class="content">
    <van-list 
            v-model="loading"
            :finished="finished"
            :immediate-check="false"
            finished-text="没有更多了"
            @load="getBreatheListsFun"
            offset="300"
       >
       <div 
        v-for="(item, index) in list"
        :key="item.id"
      >
        <div class="title" @click="getBreathDetailFun(index, item)">
          <div class="left">肺功能监测</div>
          <div class="right">
            {{ item.measure_at }}
            <van-icon v-if="item.extend" name="arrow-up" />
            <van-icon v-if="!item.extend" name="arrow-down" />
          </div>
        </div>
        <div v-if="item.extend" class="result">
          <h3>添加人：{{ item.data.operator_name }}</h3>
          <table class="table">
            <tr>
              <td>释义</td>
              <td>项目</td>
              <td>结果</td>
              <td>单位</td>
            </tr>
            <tr>
              <td>一秒率</td>
              <td>FEV1.0/FVC</td>
              <td>{{ item.data.fev_one_dot_zero_fvc }}</td>
              <td>%</td>
            </tr>
            <tr>
              <td>用力肺活量</td>
              <td>FVC<br />(%Pred)</td>
              <td>{{ item.data.fvc_perc_pred }}</td>
              <td>%</td>
            </tr>
            <tr>
              <td>肺活量</td>
              <td>SVC<br />(%Pred)</td>
              <td>{{ item.data.svc_perc_pred }}</td>
              <td>%</td>
            </tr>
            <tr>
              <td>每分钟最大通气量</td>
              <td>MVV<br />(%Pred)</td>
              <td>{{ item.data.mvv }}</td>
              <td>L/min</td>
            </tr>
            <tr>
              <td>报告质控评级</td>
              <td>QC等级</td>
              <td>{{ item.data.qc_grade }}</td>
              <td></td>
            </tr>
          </table>
        </div>
      </div>
    </van-list>
    
    
  </div>
</template>

<script>
import { getBreatheLists, getBreathDetailApi } from "@/api/saas.js";
export default {
  data() {
    return {
      list: [],
      total: 0,
      page: 1,
      limit: 20,
      loading: false,
      finished: false,
    };
  },
  created() {
    this.getBreatheListsFun()
  },
  methods: {
    // 获取肺功能检测列表
    async getBreatheListsFun () {
      if (this.list.length < this.total) {
          this.page += 1
      }
      let res = await getBreatheLists({
        patient_id: this.$route.query.patient_id,
        limit: 20,
        page: 1
      })
      if (res.status == 200) {
          this.total = res.data.total
          let temp = res.data.data.map(item => {
              item.extend = false
              item.data = {}
              return item
          })
          for (let i = 0; i < temp.length; i++) {
              this.list.push(temp[i])
          }
          this.loading = false
          if (this.list.length >= this.total) {
              this.finished = true
          }
      } else {
          this.$toast(res.msg)
      }
    },
    // 获取肺功能检测详情
    async getBreathDetailFun(index, item) {
      this.list[index].extend = !this.list[index].extend
      if (!this.list[index].extend || this.list[index].data && Object.keys(this.list[index].data).length > 0) {
          return
      }
      let res = await getBreathDetailApi({
        patient_id: this.$route.query.patient_id,
        date: item.measure_at,
        id: item.id
      });
      if (res.status == 200) {
        this.list[index].data = res.data;
      } else {
        this.$toast(res.msg);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.content {
  background: #ffffff;
  font-size: 14px;
  .title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    // margin-top: 15px;
    // margin-bottom: 18px;
    border-bottom: 1px solid #f1f1f1;
    padding: 15px 19px;
    .left {
      font-size: 16px;
      color: #333333;
    }
    .right {
      font-size: 12px;
      color: #666666;
      font-weight: 400;
      &:hover {
        cursor: pointer;
      }
    }
  }
  .result {
    // border-top: 1px solid #f1f1f1;
    h3 {
      padding: 0 25px;
      text-align: left;
      padding-top: 8px;
      font-weight: 500;
      color: #333333;
    }
    .table {
      width: 100%;
      margin-top: 10px;
      // margin-bottom: 20px;
      border: 1px solid #f1f1f1;
      tr {
        border: 1px solid #f1f1f1;
        height: 46px;
        &:nth-child(1) {
          background: #f7f7f7;
          td {
            border: none;
          }
        }
        td {
          border: 1px solid #f1f1f1;
          vertical-align: middle;
          text-align: center;
        }
      }
    }
  }
}
</style>