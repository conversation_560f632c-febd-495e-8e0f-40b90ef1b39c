<!--
 * @Descripttion: 血管内皮
 * @version: 
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-27 14:50:26
 * @LastEditors: liu<PERSON>annan
 * @LastEditTime: 2021-08-07 16:32:05
-->
<template>
    <van-list 
        v-model="loading"
        :finished="finished"
        :immediate-check="false"
        finished-text="没有更多了"
        @load="getFmdListsFun"
        offset="300"
    >
        <div class="content">
            <div 
                v-for="(item, index) in list"
                :key="item.id"
            >
                <div class="title" @click="getFmdDetailFun(index, item.id)">
                    <div class="left">血管内皮</div>
                    <div class="right">
                        {{ item.measure_at }}
                        <van-icon v-if="item.extend" name="arrow-up"></van-icon>
                        <van-icon v-if="!item.extend" name="arrow-down"></van-icon>
                    </div>
                </div>
                <div v-if="item.extend">
                    <table class="table">
                        <tr>
                            <td>项目</td>
                            <td>结果</td>
                            <td>项目</td>
                            <td>结果</td>
                        </tr>
                        <tr>
                            <td>安静时直径（mm）</td>
                            <td>
                                <abnormalIndicator
                                    code="diameter_rest"
                                    :record="item.data"
                                    :codeValue="item.data.diameter_rest"
                                />
                            </td>
                            <td>最大直径（mm）</td>
                            <td>
                                <abnormalIndicator
                                    code="diameter_max"
                                    :record="item.data"
                                    :codeValue="item.data.diameter_max"
                                />
                            </td>
                        </tr>
                        <tr>
                            <td>血管扩展率（%）</td>
                            <td>
                                <abnormalIndicator
                                    code="diameter_change"
                                    :record="item.data"
                                    :codeValue="item.data.diameter_change"
                                />
                            </td>
                            <td>血流增大率（倍）</td>
                            <td>
                                <abnormalIndicator
                                    code="blood_change"
                                    :record="item.data"
                                    :codeValue="item.data.blood_change"
                                />
                            </td>
                        </tr>
                        <tr>
                            <td>血管壁厚度（mm）</td>
                            <td>
                                <abnormalIndicator
                                    code="wall_thickness"
                                    :record="item.data"
                                    :codeValue="item.data.wall_thickness"
                                />
                            </td>
                        </tr>
                    </table>
                    <!--报告-->
                    <report-item 
                        v-if="item.data.file_paths && item.data.file_paths.length > 0"
                        :fileList="item.data.file_paths" 
                        pageName="血管内皮"
                        pageType="bloodsceen">
                    </report-item>
                </div>
            </div>
        </div>
    </van-list>
    
</template>

<script>
import reportItem from './sickerMedicalRecord/components/reportItem.vue'
import { getFmdLists, getFmdDetailApi } from '@/api/saas.js'
import abnormalIndicator from './sickerMedicalRecord/components/abnormalIndicator.vue'
export default {
    components: {
        reportItem,
        abnormalIndicator
    },
    data () {
        return {
            list: [],
            total: 0,
            page: 1,
            limit: 20,
            loading: false,
            finished: false,
        }
    },
    created () {
        this.getFmdListsFun()
    },
    computed: {
        searchInfo() {
            return this.$store.getters['docWorkRoom/searchInfo']
        },
    },
    methods: {
        // 获取血管内皮列表
        async getFmdListsFun () {
            if (this.list.length < this.total) {
                this.page += 1
            }
            let res = await getFmdLists({
                patient_id: this.$route.query.patient_id,
                limit: this.limit,
                page: this.page,
                // ...this.searchInfo
            })
            if (res.status == 200) {
                this.total = res.data.total
                let temp = res.data.data.map(item => {
                    item.extend = false
                    item.data = {}
                    return item
                })
                for (let i = 0; i < temp.length; i++) {
                    this.list.push(temp[i])
                }
                this.loading = false
                if (this.list.length >= this.total) {
                    this.finished = true
                }
            } else {
                this.$toast(res.msg)
            }
        },
        // 获取血管内皮详情
        async getFmdDetailFun (index, id) {
            this.list[index].extend = !this.list[index].extend
            if (!this.list[index].extend || this.list[index].data && Object.keys(this.list[index].data).length > 0) {
                return
            }
            let res = await getFmdDetailApi({
                id: id,
                patient_id: this.$route.query.patient_id
            })
            if (res.status == 200) {
                this.list[index].data = res.data
            } else {
                this.$toast(res.msg)
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.content {
    background: #ffffff;
    font-size: 14px;
    text-align: center;
    .title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #f1f1f1;
        padding: 15px 19px;
        .left {
            font-size: 16px;
            color: #333333;
        }
        .right {
            font-size: 12px;
            color: #666666;
            font-weight: 400;
            &:hover {
                cursor: pointer;
            }
        }
    }
    .table {
        width: 100%;
        // margin-bottom: 20px;
        // margin-top: 20px;
        border: 1px solid #F1F1F1;
        tr {
            border: 1px solid #F1F1F1;
            height: 46px;
            &:nth-child(1) {
                background: #F7F7F7;
                td {
                    border: none;
                }
            }
            td {
                border: 1px solid #F1F1F1;
                vertical-align: middle;
                text-align: center;
            }
        }
    }
}
</style>