<template>
  <div class="appHome">
    <!-- <div class="titleine">
      <div class="dot/template>
      <div class="titleText">{{ detailForm.intro_promotion && detailForm.intro_promotion.title || '智众医疗慢病管理云平台介绍' }}</div>
    </div> -->
    <div class="swipeOuter" v-if="detailForm.intro_promotion">
      <van-swipe class="swipe" v-model="activeIndex" @change="onChange" :autoplay="autoPlay" :stop-propagation="false">
        <van-swipe-item class="swipeItem" v-for="(item, index) in detailForm.intro_promotion.data" :key="index">
          <template v-if="item.resource_type == 'image'">
            <img class="imgItem" @click="redirectTo(item)" :src="item.resource" alt="">
          </template>
          <template v-else>
            <video class="videoItem" 
              ref="video"
              webkit-playsinline='true'
              playsinline='true'
              :poster="item.cover_img"
              @play="playVideo"
              @pause="pauseVideo"
              :src="item.resource"
              controls
              controlslist="nodownload noplaybackrate  noremoteplayback"
              :disablePictureInPicture="true"
              v-on:fullscreenchange="handleFullscreenChange"
              v-on:webkitfullscreenchange="handleFullscreenChange"
            ></video>
            <img class="videoCoverImg" v-if="isvideoCoverImgShow" @click="playVideo(item)" :src="item.cover_img" alt="">
          </template>
        </van-swipe-item>
      </van-swipe>
    </div>
    <div v-else class="swipeOuter">
      <div class="videoItem blockBg"></div>
    </div>
    <div class="hotProsLine">
      <div class="hotPros">
        <span class="hotProText">{{ detailForm.intro_project && detailForm.intro_project.title || '热门项目' }}</span>
        <!-- <div class="bottomLine"></div> -->
      </div>
      <div class="loadMore" @click="goDetail(detailForm.intro_project.btn_link)">{{detailForm.intro_project && detailForm.intro_project.btn_name || '了解更多'}}</div>
    </div>
    <div class="prosOuter" v-if="detailForm.intro_project">
      <img class="proImg hotProImg" v-for="(item,index) in detailForm.intro_project.data" :key="index" :src="item.resource" alt="" @click="goDetail(item.link)">
    </div>
    <div class="prosOuter" v-else>
      <div class="proImg hotProImg blockBg" v-for="(item,index) in [0,1,2,3,4,5]" :key="index"></div>
    </div>
    <div class="hotProsLine">
      <div class="hotPros">
        <span class="hotProText">{{ detailForm.intro_community && detailForm.intro_community.title || '网上社区' }}</span>
        <!-- <div class="bottomLine"></div> -->
      </div>
    </div>
    <div class="prosOuter" v-if="detailForm.intro_community">
      <img class="proImg specialProImg" v-for=" (item,index) in detailForm.intro_community.data" :key="index" :src="item.resource" alt=""  @click="goDetail(item.link)">
    </div>
    <div class="prosOuter" v-else>
      <div class="proImg specialProImg blockBg" v-for="(item,index) in [0,1]" :key="index"></div>
    </div>
    <div v-if="!isMobile()" class="bottomBlock">
      <div class="qrCodeOuter">
        <img class="qrcode" src="https://ares.zz-med.com/doctorapp/docAppDownloadQrCode.png" alt="">
        <div class="tips">扫码下载医生工作室App 了解更多医疗信息</div>
      </div>
      <div class="feedBackBtn" @click="feedBack">
        <van-icon class="icon" name="chat-o" size="16" />
        在线反馈
      </div>
      <div class="telText">售后电话：************</div>
    </div>
    <PromoteDownloadPop v-if="isPromotePopShow"></PromoteDownloadPop>
  </div>
</template>

<script>
import { getAppHomeData } from '@/api/docWorkRoom'
import { getSystemType,isMobile } from '@/utils/utils'
import PromoteDownloadPop from './projectMain/components/promoteDownloadPop.vue';
export default {
  components: { PromoteDownloadPop },
  data(){
    return {
      autoPlay: '3000',
      activeIndex: 0,
      detailForm: {},
      isvideoCoverImgShow: true,
      bannerList: [],
      isPromotePopShow: false,
      isSaasWInApp: null
    }
  },
  methods: {
    isMobile,
    feedBack(){
      if(!isMobile()){
        window.parent.postMessage({type: 'feedBack',data: ''},`${process.env.VUE_APP_SAAS_PC_URL}`)
        return
      }
    },
    getConfigData(){
      getAppHomeData().then(res=>{
        if(res.code == 200){
          this.detailForm = res.data
          // let userManual = {
          //   'type': 'img',
          //   'imgPath': 'https://zz-med-pub.oss-cn-hangzhou.aliyuncs.com/applet/health-assistant/banner_userManual.png',
          //   'url': 'https://mp.weixin.qq.com/s/l0kflGvxwwpnKZrQNeHd0A',
          // }
          // let live = {
          //   'type': 'img',
          //   'imgPath': 'https://zz-med-pub.oss-cn-hangzhou.aliyuncs.com/applet/health-assistant/banner_mmcLive.png',
          //   'url': 'https://wx.vzan.com/live/page/961971559?v=1716795680312',
          // }
          // this.bannerList = [live,userManual,...this.detailForm.intro_promotion.data]
          // console.log(this.bannerList)
          // this.detailForm.intro_promotion.data.push(obj)
        }
      })
    },
    onChange(index) {
      this.activeIndex = index
      this.pauseVideo()
    },
    playVideo(item) {
      if(!isMobile()){
        if(!this.isSaasWInApp){
          this.isPromotePopShow = true
          return
        }
        let obj = {
          src: item.resource,
          title: item.title,
          cover_img: item.cover_img
        }
        window.parent.postMessage({type: 'playVideo',data: obj},`${process.env.VUE_APP_SAAS_PC_URL}`)
        return
      }
      const video = this.$refs.video[0];
      if(video) video.play()
      if(this.isvideoCoverImgShow) this.isvideoCoverImgShow = false
      this.autoPlay = '0'
    },
    pauseVideo() {
      const video = this.$refs.video[0]
      if(video) video.pause();
      this.autoPlay = '3000'
    },
    // 跳转其他页面
    goDetail(item){
      let { type='',path='' } = item
      if(!path) return
      if(type == 'h5'){
        let isProjectMain = path.indexOf('projectMain/projectMain') > -1 || path.indexOf('projectMain/newProjectMain') > -1
        if(isMobile()){
          if(getSystemType() == 'ios'){
            isProjectMain ? window.webkit.messageHandlers.openProjectMain.postMessage(JSON.stringify({'parameter': path})) : window.webkit.messageHandlers.breathMessage.postMessage(JSON.stringify({'parameter': path}))
          }else{
            isProjectMain ? window.android.openProjectMain(path) : window.android.breathMessage(path)
          }
        }else{
          window.location.href = path
          // console.log(path)
        }
      }else if(type == 'origin'){
        if(!isMobile()){
          // this.$toast('请下载医生工作室体验完整功能')
          this.isPromotePopShow = true
          return
        }
        if(getSystemType() == 'ios'){
          window.webkit.messageHandlers.jumpNativeMethod.postMessage(JSON.stringify({'parameter': path}))
        }else{
          window.android.jumpNativeMethod(path)
        }
      }
    },
    // 针对安卓做全屏处理
    handleFullscreenChange(){
      const video = this.$refs.video[this.activeIndex]
      console.log(video.src)
      if (document.fullscreenElement || document.webkitFullscreenElement) {
        if(getSystemType() == 'android'){
          window.android.fullScreenPlay(video.src)
        }
      }
    },
    redirectTo(item){
      if(!item.link.path) return
      if(isMobile()){
        if(getSystemType() == 'ios'){
          window.webkit.messageHandlers.breathMessage.postMessage(JSON.stringify({ "parameter": item.link.path}));
        }else{
          window.android.breathMessage(item.link.path);
        }
      }else{
        if(item.link.path.indexOf('polyv') != -1 || item.link.path.indexOf('live') != -1){
          // window.parent.postMessage({type: 'showNavBar',data: true},`${process.env.VUE_APP_SAAS_PC_URL}`)
          // window.location.href = item.link.path
          this.isPromotePopShow = true
          return
        }
        if(item.link.type && item.link.type == 'h5'){
          window.openNewWindowFromSaas ? window.openNewWindowFromSaas(item.link.path) : window.open(item.link.path)
          return
        }
        if(item.link.path.indexOf('weixin') != -1){
          window.openNewWindowFromSaas ? window.openNewWindowFromSaas(item.link.path) : window.open(item.link.path)
        }else{
          window.location.href = item.link.path
        }
        // window.location.href = item.link.path
        // window.open(item.link.path)
      }
    },
    init(){
      // let authorization = this.$route.query.authorization;
      // localStorage.setItem('authorization',authorization)
      this.getConfigData()
    },
    handleMessage(e){
      let { type, data } = e.data
      console.log('H5接收到saas父页面的消息',type,data)
      // 播放视频
      if(type == 'isSaasWInApp'){
        this.isSaasWInApp = data
        console.log('接收到了isSaasWInApp', data)
      }
    }
  },
  created(){
    this.init()
  },
  mounted(){
    if(!isMobile()){
      this.$nextTick(()=>{
        window.parent.postMessage({type: 'showNavBar',data: false},`${process.env.VUE_APP_SAAS_PC_URL}`)
        window.parent.postMessage({type: 'isLoaded',data: ''},`${process.env.VUE_APP_SAAS_PC_URL}`)
      })
    }
    window.addEventListener('message', this.handleMessage, false);
  }
}
</script>

<style lang="scss" scoped>
.appHome{
  font-family: "PingFang SC";
  width: 100%;
  min-height: 100vh;
  padding: 10px 15px 5px;
  box-sizing: border-box;
  .titleine{
    display: flex;
    align-items: center;
    .dotText{
      color: #FFF;
      font-size: 12px;
      font-weight: 500;
      width: 32px;
      height: 20px;
      text-align: center;
      line-height: 20px;
      border-radius: 4px;
      background: #EE4700;
    }
    .titleText{
      color: #262626;
      font-size: 16px;
      font-weight: 500;
      margin-left: 8px;
    }
  }
  .swipeOuter {
    width: 100%;
    height: 194px;
    // margin-top: 16px;
    position: relative;
    .videoCoverImg{
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 99;
      border-radius: 16px;
    }
    .swipe,
    .swipeItem {
      width: 100%;
      height: 194px;
      border-radius: 16px;
    }
    .videoItem,.imgItem {
      width: 100%;
      height: 194px;
      border-radius: 16px;
    }
  }
  .hotProsLine{
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 16px;
    .hotPros{
      display: flex;
      flex-direction: column;
      align-items: center;
      .hotProText{
        color: #000;
        font-size: 16px;
        font-weight: 500;
      }
      .bottomLine{
        width: 26px;
        height: 3px;
        border-radius: 24px;
        background: #FF6F00;
        margin-top: 4px;
      }
    }
    .loadMore{
      width: 68px;
      height: 24px;
      text-align: center;
      line-height: 24px;
      border-radius: 63px;
      border: 0.5px solid #FFDDC3;
      color: #FF6F00;
      font-size: 12px;
      font-weight: 500;
      cursor: pointer;
    }
  }
  .prosOuter{
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    cursor: pointer;
    .proImg{
      width: 48%;
      margin-top: 12px;
      border-radius: 12px;
    }
    .hotProImg{
      height: 66px;
    }
    .specialProImg{
      height: 108px;
    }
  }
  .blockBg{
    background: #DFDFDF;
  }
  .bottomBlock{
    margin-top: 16px;
    .qrCodeOuter{
      padding: 16px 0;
      border-top: 1px solid #EFEFEF;
      border-bottom: 1px solid #EFEFEF;
    }
    .feedBackBtn{
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #377CFB;
      height: 42px;
      border: 1px solid #377CFB;
      margin-top: 16px;
      border-radius: 6px;
      cursor: pointer;
      .icon{
        margin-right: 2px;
      }
    }
    .qrcode{
      width: 120px;
      height: 120px;
    }
    .tips{
      font-size: 16px;
      font-weight: 500;
      color: #000000;
      margin-top: 10px;
    }
    .telText{
      color: #999999;
      font-size: 18px;
      line-height: normal;
      margin-top: 16px;
      padding-bottom: 10px;
    }
  }
}
</style>