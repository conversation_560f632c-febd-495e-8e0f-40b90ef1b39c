<template>
  <div class="sicker-medical-record">
    <!-- <searchRecord @getCoreData="getCoreData" @getAbnormalData="getAbnormalData"/> -->
    <div :class="`main main-${platform}`" v-if="!isEmpty">
      <manageInfo v-if="isApp && isProjectInfo" :mananeInfo="currentLevelInfo" />
      <!--待办-->
      <recordTask :levelTask="levelTask" :levelName="levelName" v-if="isApp && isLevelTask" />
      <!--异常情况-->
      <abnormalData 
        v-if="isAbnormalData"
        :abnormalData="abnormalData" 
        :abnormalRoleData="abnormalRoleData"
        @getAbnormalData="getAbnormalData"
      />
      <!--核心数据-->
      <recordCore 
        @getCoreData="getCoreData"
        :coreData="coreData" 
        :coreRoleData="coreRoleData" 
        v-if="isCoreData" />
      <recordCheck :menuList="menuList" />
    </div>
    <empty v-else />
  </div>
</template>

<script>
const UA = navigator.userAgent;
const isAndroid = UA.indexOf("Android") > -1 || UA.indexOf("Adr") > -1; // android终端
const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); // ios终端

import searchRecord from './components/searchRecord.vue'
import manageInfo from './components/manageInfo.vue'
import recordTask from './components/recordTask.vue'
import recordCore from './components/recordCore.vue'
import recordCheck from './components/recordCheck.vue'
import empty from './components/empty.vue'
import abnormalData from './components/abnormalData.vue';

import {Toast} from "vant";
import {getCurrentLevelInfo, getCoreDataList, getEnter, getWorkshopFuncListApi, getAbnormalIndicatorListApi} from "@/api/saas";

import diagnosis from '../imgs/diagnosis.png'
import wx_diagnosis from '../imgs/wx_diagnosis.png'
import medical_care from '../imgs/medical_care.png'
import wx_medical_care from '../imgs/wx_medical_care.png'
import inspect from '../imgs/inspect.png'
import wx_inspect from '../imgs/wx_inspect.png'
import dashboard from '../imgs/dashboard.png'
import wx_dashboard from '../imgs/wx_dashboard.png'
import biomarkers from '../imgs/biomarkers.png'
import wx_biomarkers from '../imgs/wx_biomarkers.png'

export default {
  name: "sickerMedicalRecord",
  components: {
    searchRecord,
    manageInfo,
    recordTask,
    recordCore,
    recordCheck,
    empty,
    abnormalData
  },
  computed: {
    platform() {
      return this.$store.getters['docWorkRoom/platform']
    },
    isApp() {
      return this.$store.getters['docWorkRoom/isApp']
    },
    searchInfo() {
      return this.$store.getters['docWorkRoom/searchInfo']
    },
  },
  data() {
    return {
      isEmpty: false,
      isLevelTask: false,
      levelName: '待办事项',
      isProjectInfo: false,
      isAbnormalData: false,
      abnormalData: [],
      abnormalRoleData: {},
      abnormal_source: '0',
      isCoreData: false,
      coreRoleData: {},
      currentLevelInfo: {},
      workshopRole: {},
      levelTask: [],
      coreData: [],
      toastLoading: null,
      menu: {
        life_sign: { sortvalue: 1, name: '个人体征数据', appimg: diagnosis, img: wx_diagnosis, indicator_group_id: 1, isShowTrend: true },
        public_template_experiment: { sortvalue: 2, name: '实验室检查', appimg: inspect, img: wx_inspect, indicator_group_id: 71, isShowTrend: false },
        body_check: { sortvalue: 3, name: '辅助检查', appimg: medical_care, img: wx_medical_care, indicator_group_id: 1129, isShowTrend: false },
        public_template_shengwuxue: { sortvalue: 4, name: '生物学标志物', appimg: biomarkers, img: wx_biomarkers, indicator_group_id: 2851, isShowTrend: false },
        assessment: { sortvalue: 5, name: '评估', appimg: dashboard, img: wx_dashboard, indicator_group_id: 0, isShowTrend: false },
      },
      menuList: [],
    }
  },
  created() {
    // wechat  小程序
    // saas 医生工作室
    const { auth_platform = 'saas', authorization = '', room_id = '', project_id = '',isFrom = '' } = this.$route.query
    this.$store.dispatch('docWorkRoom/setPlatform', auth_platform)
    this.$store.dispatch('docWorkRoom/setNavigator', { isAndroid, isIOS, isWeixin: auth_platform === 'wechat' })
    this.getRoleList()
    if(isFrom && isFrom == 'zzApp'){
      localStorage.setItem('isFrom','zzApp')
    }else{
      localStorage.removeItem('isFrom')
    }
  },
  mounted() {
    // document.documentElement.style.overflow = 'hidden';
    if (this.isApp) this.getCurrentLevelInfo()
  },
  methods: {
    setRole() {
      // project_id patient_id room_id user_id hosp_id authorization


      if (this.workshopRole && this.workshopRole.length && this.workshopRole[0].child.length) {
        const roleList = this.workshopRole[0].child.filter(v => v.code === 'sicker_medical_record')
        if (roleList && roleList.length) {
          document.title = roleList[0].name
          if (!roleList[0].child.length) {
            this.isEmpty = true
          } else {
            roleList[0].child.forEach(v => {
              if (v.code === 'project_info' && this.isApp) {
                this.isProjectInfo = true
              }
              if (v.code === 'managestage_data' && this.isApp) {
                this.isLevelTask = true
                this.levelName = v.name
              }
              if (v.code === 'abnormal_data') {
                this.isAbnormalData = true
                this.abnormalRoleData = {...v}
                // 查询异常数据
                this.getAbnormalData()
              }
              if (v.code === 'core_data') {
                this.isCoreData = true
                this.coreRoleData = { indicator_group_id: 0, ...v}
              }
              if (this.menu[v.code]) {
                this.menuList.push({ ...this.menu[v.code], ...v, })
              }
            })
            this.menuList = this.menuList.sort((a, b) => a.sortvalue - b.sortvalue)
          }
        }
      }

    },
    async getRoleList() {
      this.menuList = []
      if (this.isApp) {
        let { status, data } = await getEnter({
          project_id: this.$route.query.project_id,
        });
        if (status == 200) {
          this.workshopRole = data
          this.setRole()
        }

      } else {
        let { status, data = [] } = await getWorkshopFuncListApi({});
        if (status == 200) {
          console.log('datadatadata')
          console.log(data)
          this.workshopRole = [
            {
              "name": "医院数据(医生工作室)",
              "path": null,
              "pid": 0,
              "deep": 0,
              "code": "hospital_data",
              child: [
                {
                  "id": 221,
                  "name": "患者病案",
                  "path": null,
                  "pid": 92,
                  "deep": 1,
                  "code": "sicker_medical_record",
                  "child": data
                }
              ]
            }
          ]
          // 评估/生物学标志物 小程序单独查询
          this.setRole()
        }
      }
    },
    async getCurrentLevelInfo () {
      this.levelTask = []
      this.toastLoading = Toast.loading({
        message: '加载中...',
        forbidClick: true,
      });
      try {
        const { status, data } = await getCurrentLevelInfo({
          patient_id: this.$route.query.patient_id,
        })
        if (status == 200) {
          this.currentLevelInfo = data
          this.setLevelTask()
        }
        this.toastLoading.clear();
      } catch (e) {
        this.toastLoading.clear();
      }
    },
    setLevelTask() {
      if (this.currentLevelInfo.current_level_task) {
        if (this.currentLevelInfo.current_level_task.survey && this.currentLevelInfo.current_level_task.survey.length) {
          this.currentLevelInfo.current_level_task.survey.forEach(v => {
            this.levelTask.push({ id: v.id, name: v.name })
          })
        }

        if (this.currentLevelInfo.current_level_task.examination.some(v => v.children && (v.children.length !== 0))) {
          this.currentLevelInfo.current_level_task.examination.forEach(v => {
            v.children.forEach(m => {
              this.levelTask.push({ id: m.id, name: `${v.name}-${m.name}` })
            })
          })
        }
      }
    },
    async getCoreData () {
      this.toastLoading = Toast.loading({
        message: '加载中...',
        forbidClick: true,
      });
      // this.coreData = []
      try {
        const { status, data } = await getCoreDataList({
          patient_id: this.$route.query.patient_id,
          start_date: this.searchInfo.start_date,
          end_date: this.searchInfo.end_date,
        })
        if (status == 200) {
          this.coreData = data
        }
        this.toastLoading.clear();
      } catch (e) {
        this.toastLoading.clear();
      }
      this.getRoleList()
    },
    // 查询异常数据
    async getAbnormalData (abnormal_source) {
      this.toastLoading = Toast.loading({
        message: '加载中...',
        forbidClick: true,
      });
      // this.abnormalData = []
      try {
        if (abnormal_source) {
          this.abnormal_source = abnormal_source
        }
        const { status, data } = await getAbnormalIndicatorListApi({
          patient_id: this.$route.query.patient_id,
          abnormal_source: this.abnormal_source
        })
        if (status == 200) {
          this.abnormalData = data
        }
        this.toastLoading.clear();
      } catch (e) {
        this.toastLoading.clear();
      }
    }
  }
}
</script>

<style lang="scss">
@import "./common.scss";
body {
  // padding-top: env(safe-area-inset-top); /* 顶部刘海区域的安全距离 */
  // padding-bottom: env(safe-area-inset-bottom); /* 底部刘海区域的安全距离 */
  padding-left: constant(safe-area-inset-left); /* 左侧刘海区域的安全距离 */
  padding-left: env(safe-area-inset-left); /* 左侧刘海区域的安全距离 */

}
.sicker-medical-record{
  width: 100%;
  height: 100%;
  background: $app-bg-color;
  .main{
    height: calc(100vh - 40px);
    overflow-y: auto;
    background: $app-bg-color;
  }
  .main-wechat{
    background: $wx-bg-color;
  }
}


</style>
