<template>
  <div class="container">
    <div class="chart-wrap">
      <div class="title">
        <div class="title-label">
          <img src="../../imgs/time.png" class="img" alt="">
          <span class="title-label-text">{{ dateRange }}</span>
        </div>
        <div class="title-content">
          <div>
            <img src="../../imgs/page.png" class="img20" alt="">
            <span @click="handleReport($route.query.landscape ? report_url : chartData.report_url)" class="title-content-text">分析报告</span>
          </div>
          <!-- <img
            v-if="!$route.query.landscape"
            @click="handleFullScreen"
            src="../../imgs/full_screen.png"
            class="img20 right"
            alt=""> -->
        </div>
      </div>
      <div v-show="have_data_in_period" class="chart">
        <div class="chart" ref="chartRef"></div>
      </div>
      <empty v-show="list.length == 0 && emptyFlag && !have_data_in_period" :chartEmpty="true" class="chart"></empty>
    </div>
  </div>
</template>

<script>
import { jsToClient } from '@/utils/bfBridge'
import { Toast } from 'vant'
import empty from '../components/empty.vue';
import { getCgmDeviceDataApi } from "@/api/saas.js"
import { getCgmDeviceDataApiV2 } from '@/api/docWorkRoom'
const UA = navigator.userAgent;
const isAndroid = UA.indexOf("Android") > -1 || UA.indexOf("Adr") > -1; // android终端
const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); // ios终端
export default {
  props: {
    chartData: {
      type: Object,
      default: () => {}
    },
    entry: {
      type: String,
      default: '',
    },
  },
  components: {
    empty
  },
  data () {
    return {
      source: '',
      list: [],
      normal_range: {},
      enable_time: '',
      last_time: '',
      report_url: [],
      emptyFlag: false,
      have_data_in_period: true
    }
  },
  computed: {
    isApp() {
      return this.$store.getters['docWorkRoom/isApp']
    },
    dateRange () {
      let param = ''
      if (this.$route.query.landscape) {
        param = this.enable_time + '~' + this.last_time
      } else {
        param = this.$props.chartData.enable_time + '~' + this.$props.chartData.last_time
      }

      return param
    }
  },
  created () {
    this.getCgmDeviceData()
  },
  // watch: {
  //   chartData: {
  //     handler (newVal, oldVal) {
  //       if (newVal) {
  //         this.getCgmDeviceData()
  //       }
  //     },
  //     deep: true,
  //     immediate: true
  //   }
  // },
  methods: {
    async getCgmDeviceData () {
      const toast = Toast.loading({
        message: '加载中...',
        forbidClick: true,
        duration: 0
      })
      try {
        const { patient_id, device_id, current_data_range, entry, source } = this.$route.query
        const _entry = this.entry || entry
        this.entry = _entry || ''
        this.source = source

        const params = {
          device_id: this.$props.chartData && this.$props.chartData.device_id ? this.$props.chartData.device_id : device_id,
          current_data_range: this.$props.chartData && this.$props.chartData.current_data_range ? this.$props.chartData.current_data_range : current_data_range
        }
        if (this.entry === 'record') {
          params.user_id = patient_id
          params.is_patient_platform = this.source === 'docApp' ? '0' : '1'
        } else {
          params.patient_id = patient_id
        }

        const { data } = this.entry === 'record' ? await getCgmDeviceDataApiV2(params) : await getCgmDeviceDataApi(params)

        // const { data } = await getCgmDeviceDataApi({
        //   patient_id,
        //   device_id: this.$props.chartData && this.$props.chartData.device_id ? this.$props.chartData.device_id : device_id,
        //   current_data_range: this.$props.chartData && this.$props.chartData.current_data_range ? this.$props.chartData.current_data_range : current_data_range
        // })
        this.list = data.bg_list
        if (this.list.length == 0) {
          this.emptyFlag = true
        }
        this.normal_range = data.normal_range
        this.enable_time = data.enable_time
        this.last_time = data.last_time
        this.report_url = data.report_url
        this.have_data_in_period = data.have_data_in_period
        this.handleRenderChart()
        toast.clear()
      } catch {
        toast.clear()
      }
    },
    handleFullScreen () {
      const { device_id, current_data_range, enable_time, last_time, report_url } = this.$props.chartData
      let params = '/docWorkRoom/sickerMedicalRecord/cgmBs/chart' + location.search + `&landscape=true&device_id=${device_id}&current_data_range=${current_data_range}&enable_time=${enable_time}&last_time=${last_time}&report_url=${report_url}&entry=${this.entry}`
      if (process.env.NODE_ENV === "production") {
        // 暴风 智众健康助手App
        // let isFrom = localStorage.getItem('isFrom')
        // Toast('source: ' + this.source)
        // if(this.source === 'patientApp'){
        //     this.$router.push(params)
        //     return
        // }
        if (isAndroid) {
            window.android.showLandScape(params)
        }
        if (isIOS) {
            window.webkit.messageHandlers.showLandScape.postMessage(params)
        }
      }
    },
    handleReport (report_url) {
      if (process.env.NODE_ENV === 'production' && report_url && report_url.length) {
        let link = report_url[0]

        if (this.source === 'patientApp') {
          // link = encodeURIComponent(decodeURIComponent(link))
          link = encodeURIComponent(link)
          const params = {
            action: 0,
            data: {
              title: '动态血糖报告',
              url: `${process.env.VUE_APP_BF_BASE_URL}web/measure/trendXT/report?url=${link}`,
              fullScreen: false,
              scale: true
            }
          }
          jsToClient('jumpTo', JSON.stringify(params))
        } else {
          if (isAndroid) {
            window.android.showFilePdf(link);
          }
          if (isIOS) {
            window.webkit.messageHandlers.showFilePdf.postMessage(link);
          }
        }

        // if (isAndroid) {
        //   window.android.showFilePdf(link);
        // }
        // if (isIOS) {
        //   window.webkit.messageHandlers.showFilePdf.postMessage(link);
        // }
      } else {
        Toast('暂无报告')
      }
    },
    processCreatedAt (arr) {
      const firstYear = arr[0].split('-')[0];
      const isSameYear = arr.every(item => item.startsWith(firstYear));
      const result = arr.map(item => {
        if (isSameYear) {
          return item.replace(/^[0-9]{4}-/, '');
        } else {
          return item;
        }
      });
      return result
    },
    clearEchartsData(arr) {
      var Xdata = [];
      var Sdata = [];
      for (var i = 0; i < arr.length; i++) {
        var item = arr[i];
        Xdata.push(item.measured_at);
        Sdata.push({
          ...item,
          value: item.bg
        })
      }
      return [Xdata, Sdata]
    },
    handleRenderChart () {
      let myEchart = echarts.init(this.$refs.chartRef, 'light')
      let startValue = 0, endValue = 100
      if ((this.$props.chartData && this.$props.chartData.current_data_range && this.$props.chartData.current_data_range == 7) || this.$route.query.current_data_range == 7) {
        endValue = 50
      } else if ((this.$props.chartData && this.$props.chartData.current_data_range && this.$props.chartData.current_data_range == 14) || this.$route.query.current_data_range == 14) {
        endValue = 30
      }
      let yMax = 25
      let tempList = this.clearEchartsData(this.list)[1].map(item => item.bg && parseFloat(item.bg))
      let maxBg = Math.max.apply(null, tempList)
      if (maxBg > 25) {
        yMax = maxBg + 5
      }
      let option = {
        xAxis: {
          type: 'category',
          boundaryGap: false,
          nameTextStyle: {
            fontSize: 12,
            padding: [0, 0, 0, -300],
          },
          axisLabel: {
            color: '#999',
            align: 'center',
            interval: 'auto',
            formatter: function (value) {
              return value.split(' ')[0] + '\n' + value.split(' ')[1]
            },
          },
          axisLine: {
            lineStyle: {
              color: 'rgba(0, 0, 0, 0.25)'
            }
          },
          axisTick: {
            show: true,
            lineStyle: {
              color: 'rgba(0, 0, 0, 0.25)'
            },
            alignWithLabel: false
          },
          data: this.processCreatedAt(this.clearEchartsData(this.list)[0])
        },
        grid: {
          // left: 38,
          // right: 25,
          bottom: 70,
          top: 26
        },
        visualMap: {
          show: false,
          type: 'piecewise',
          pieces: [
            {
              gte: this.normal_range.min,
              lte: this.normal_range.max,
              color: '#FC9547'
            },
            { gt: this.normal_range.max, color: '#F5242D' },
            { lt: this.normal_range.min, color: '#F5242D' },
          ],
          precision: 2
        },
        yAxis: [
          {
            name: 'mmol/L',
            type: 'value',
            max: yMax,
            min: 0,
            axisLabel: {
              color: '#999'
            },
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            splitLine: {
              lineStyle: {
                color: 'rgba(0, 0, 0, 0.15)'
              }
            }
          },
        ],
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            snap: false,
            label: {
              show:false
            },
          },
          borderWidth: 0,
          // backgroundColor: "#377CFB",
          backgroundColor: '#FC9547',
          borderColor: "rgba(1,172,253,0)",
          position: ['20%', '10%'],
          formatter: function (params) {
            if (params[0].data && params[0].data.measured_at) {
              return `
              <div style="padding:6px;display:flex;flex-direction: column;border-radius: 4px;">
                <div style="line-height:20px">
                  <span style="font-size: 14px;color: #FFF">血糖值：</span>
                  <span style="color:#FFF;font-size: 24px">${params[0].data.value}</span>
                  <span style="color:#fff;opacity: 0.5;font-size: 14px">${params[0].data.unit}</span>
                </div>
                <div style="color:#fff">
                  <span>测量时间：</span>
                  <span>${params[0].data.measured_at}</span>
                </div>
              </div>
            `
            } else {
              return
            }
          },
        },
        dataZoom: [
          {
            type: 'slider',
            realtime: true,
            start: startValue,
            end: endValue,
            textStyle: {
              color: 'rgba(0, 0,0, 0)'
            }
          }
        ],
        series: [
          {
            name: '血糖正常',
            symbolSize: '5',
            symbol: 'none',
            data: this.clearEchartsData(this.list)[1],
            type: 'line',
            // type: 'scatter',
            smooth: true,
            itemStyle: {
              color: '#377CFB'
            },
            // lineStyle: {
            //   width: 10
            // },
            markArea: {
              itemStyle: {
                color: 'rgba(7, 192, 96, .1)'
              },
              data: [
                [
                  {
                    yAxis: this.normal_range.min
                  },
                  {
                    yAxis: this.normal_range.max
                  }
                ],
              ]
            },
          },
          // {
          //   name: '血糖异常',
          //   data: [],
          //   type: 'scatter',
          //   itemStyle: {
          //     color: '#F5242D'
          //   },
          // },
          // {
          //   name: '血糖正常范围值',
          //   data: [],
          //   type: 'scatter',
          //   itemStyle: {
          //     color: 'rgba(55, 124, 251, .5)'
          //   },
          // }
        ]
      }
      console.log(option)
      console.log(this.$refs.chartRef)
      // myEchart.clear()
      myEchart.setOption(option)
      this.$nextTick(() => {
        myEchart.resize()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 5vh;
  font-size: 15px;
  // margin-bottom: 24px;
  .title-label-text {
    color: #333;
    font-weight: 500;
    margin-left: 5px;
  }

  .title-content {
    display: flex;
    align-items: flex-end;
    justify-content: flex-end;
    flex: 1;
  }
  .title-content-text {
    color: #666;
    margin-left: 2px;
  }
  .right {
    margin-left: 8px;
  }
  .img {
    width: 15px;
    height: 15px;
    vertical-align: -0.13em;
  }
  .img20 {
    width: 15px;
    height: 15px;
    vertical-align: -0.25em;
  }
}
.chart {
  width: 95vh;
  height: 90vw;
  margin: 0 auto;
  touch-action: none;
}
::v-deep .empty {
  background-color: white;
}
.container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100vw;
  height: 100vh;
}
.chart-wrap {
  min-width: 100vh;
  min-height: 100vw;
  transform: rotate(90deg);
  .title {
    height: 10vw;
    font-size: 12px;
    // margin-bottom: 12px;
    // .title-label-text {
    //   margin-left: 2px;
    // }
    // .title-content-text {
    //   margin-left: 1px;
    //   margin-right: 10px;
    // }
    // .img {
    //   width: 8px;
    //   height: 8px;
    //   vertical-align: -0.13em;
    // }
    // .img20 {
    //   width: 10px;
    //   height: 10px;
    //   vertical-align: -0.25em;
    // }
  }
  .chart {
    // width: 100%;
    // height: calc(100vh - 32px);
    touch-action: none;
  }
}
</style>
