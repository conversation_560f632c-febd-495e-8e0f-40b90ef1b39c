<!--动态血糖详情-->
<template>
  <div class="bgm">
    <template v-if="deviceList.length">
      <div
        v-for="(i, index) in deviceList"
        :key="i.id"
        class="row">
        <div class="time" @click="handleClick($event, index)">
          <div
            v-for="item in timeList"
            :key="item.value"
            class="item"
            :class="{active: i.current_data_range == item.value ? true : false}"
            :data-value="item.value">
            {{ item.name }}
          </div>
        </div>
        <chart :chartData="i" :entry="entry"></chart>
        <div class="foot">
          图表中“ <span class="foot-block"></span> ”表示血糖正常范围区间({{ i.normal_range.min }}~{{ i.normal_range.max }});
          <br/>
          数据更新时间: {{ i.last_meas_date }}
        </div>
      </div>
    </template>
    <empty v-if="deviceList.length == 0 && emptyFlag"></empty>
  </div>
</template>

<script>
import { Toast } from 'vant'
import empty from '../components/empty.vue'
import chart from './chart.vue'
import { getCgmDeviceListApi } from "@/api/saas.js"
import { getCgmDeviceListApiV2 } from '@/api/docWorkRoom'
export default {
  components: {
    empty,
    chart
  },
  data () {
    return {
      timeList: [{
        name: '1日',
        value: 1
      }, {
        name: '7日',
        value: 7
      }, {
        name: '14日',
        value: 14
      }],
      deviceList: [],
      emptyFlag: false
    }
  },
  props: {
    entry: {
      type: String,
      default: '',
    },
  },
  computed: {
    searchInfo() {
      return this.$store.getters['docWorkRoom/searchInfo']
    },
  },
  mounted () {
    this.getCgmDeviceList()
  },
  methods: {
    handleClick (e, index) {
      if (e.target.dataset.value == this.deviceList[index].current_data_range || !e.target.dataset.value) {
        return
      }
      this.deviceList[index].current_data_range = e.target.dataset.value
    },
    async getCgmDeviceList () {
      const { patient_id, start_date, end_date, from_common_menu } = this.$route.query
      const toast = Toast.loading({
        message: '加载中...',
        forbidClick: true,
        duration: 0
      })
      try {
        let tempParams = {}
        if (from_common_menu) {
          tempParams = {
            ...this.searchInfo
          }
        } else {
          tempParams = {
            start_date,
            end_date
          }
        }
        const params = {
          ...tempParams,
        }
        if (this.entry === 'record') {
          params.user_id = patient_id
          params.is_patient_platform = this.$route.query.source == 'docApp' ? '0' : '1'
        } else {
          params.patient_id = patient_id
        }

        const { data } = this.entry === 'record' ? await getCgmDeviceListApiV2(params) : await getCgmDeviceListApi(params)

        // const { data } = await getCgmDeviceListApi({
        //   ...tempParams,
        //   patient_id
        // })
        // this.deviceList = data.data
        this.deviceList = this.entry === 'record' ? (data || []) : (data.data || [])
        if (this.deviceList.length == 0) {
          this.emptyFlag = true
        }
        toast.clear()
      } catch {
        toast.clear()
      }

    }
  }
}
</script>

<style lang="scss" scoped>
.bgm {
  background: #F8F8F8;
  height: calc(100vh - 24px);
  padding: 12px 14px 12px 16px;
  .row {
    background: #fff;
    padding: 12px 15px;
    display: flex;
    flex-direction: column;
    .time {
      background: rgba(252, 149, 71, 0.12);
      border-radius: 100px;
      padding: 3px;
      margin-bottom: 24px;
      height: 40px;
      display: flex;
      justify-content: space-around;
      align-items: center;
      color: #999;
      font-size: 15px;
      .item {
        width: 33.33%;
        height: 100%;
        border-radius: 100px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .active {
        background: #fff;
        color: #FC9547;
      }
    }
    .foot {
      font-size: 12px;
      color: #999;
      margin-top: 16px;
      text-align: left;
      line-height: 18px;
      .foot-block {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 2px;
        background: #CDF2DF;
        vertical-align: middle;
      }
    }
  }
}
</style>
