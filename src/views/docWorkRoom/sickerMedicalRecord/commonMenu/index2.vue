<template>
    <div>
      <div @click="breathMessage" style="margin-top: 20px">breathMessage跳转下一页</div>
      <div @click="showLandScape" style="margin-top: 20px">showLandScape</div>
      <div @click="showFilePdf" style="margin-top: 20px">showFilePdf pdf</div>
    </div>
</template>

<script>
import empty from '../components/empty.vue'
import { getFuncDetailApi } from "@/api/saas.js"
import { getChildRight } from "@/utils/utils.js"
// 老模式页面code
const old = ['eyeCheck', 'bchumancomponent', 'pwv', 'bcnerveconduction', 'hoursBp', 'fat', 'bloodsceen','ecg', 'liver_hardness']
// 老模式code 映射路由
const oldToUrl = {
    eyeCheck: '/docWorkRoom/fundusExam',                // 眼底
    bchumancomponent: '/docWorkRoom/humancomponent',    // 人体成分分析
    pwv: '/docWorkRoom/pwvAbiTbi',                      // pwv
    bcnerveconduction: '/docWorkRoom/nerveconduction',  // 神经传导
    fat: '/docWorkRoom/visceralFat',                    // 内脏脂肪
    bloodsceen: '/docWorkRoom/vec',                     // 血管内皮
    ecg: '/docWorkRoom/ecgExam',                        // 心电图
    liver_hardness: '/docWorkRoom/liverHardness',       // 肝胆弹性
    hoursBp: '/docWorkRoom/hoursBp',                    // 24小时动态血压
}
// 老模式code 映射安卓和iOS方法
// const oldToMethod = {
//     eyeCheck: 'fundusMessage',
//     bchumancomponent: 'breathMessage',
//     pwv: 'pwvAbiTbiMessage',
//     bcnerveconduction: 'breathMessage',
//     fat: 'visceralFatMessage',
//     bloodsceen: 'vecMessage',
//     ecg: 'breathMessage',
//     liver_hardness: 'breathMessage',
//     hoursBp: 'breathMessage'
// }
// 生命体征
const life_sign = ['public_template_tab_hw', 'public_template_tab_sides', 'public_template_tab_bp', 'public_template_tab_lifesigns_peak_flow_rate',
'public_template_tab_lifesigns_blood_oxygen', 'public_template_tab_general_signs','public_template_tab_bs']
const UA = navigator.userAgent;
const isAndroid = UA.indexOf("Android") > -1 || UA.indexOf("Adr") > -1; // android终端
const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); // ios终端
const isWeixin = UA.indexOf("micromessenger") !== -1 || UA.indexOf("MicroMessenger") !== -1;    // 微信

export default {
    components: {
        empty,
    },
    data () {
        return {
            list: [],

        }
    },
    created () {
        document.title = this.$route.query.title
        this.getFuncDetail()
        console.log(this.$store)
    },
    computed: {
        platform() {
            return this.$store.getters['docWorkRoom/platform']
        },
    },
    methods: {
        async getFuncDetail () {
            const { data } = await getFuncDetailApi()
            this.list = getChildRight(data, ['patient_list', 'detail', 'public_template_experiment'])
            // 过滤掉哮鸣音
            this.list = this.list && this.list.filter(item => item.code != 'public_template_tab_wheeze')
        },
        handleClick ({ name, indicator_group_id, code }) {
            const { patient_id, project_id } = this.$route.query
            let params = ''
            if (old.includes(code)) { // 辅助检查老模式
                params = `${oldToUrl[code]}?patient_id=${patient_id}&project_id=${project_id}`
            } else if (life_sign.includes(code)) { // 生命体征
                params = `/docWorkRoom/personal?patient_id=${patient_id}&project_id=${project_id}&title=${name}&indicator_group_id=${indicator_group_id}&code=${code}`
            } else { // 新模式（辅助检查，生物学标志物，实验室检查）
                params = `/docWorkRoom/sickerMedicalRecord/commonCheck?patient_id=${patient_id}&project_id=${project_id}&title=${name}&indicator_group_id=${indicator_group_id}`
            }
            if (process.env.NODE_ENV === "production") {
                if (isAndroid) {
                    window.android.showLandScape(params)
                }
                if (isIOS) {
                    window.webkit.messageHandlers.showLandScape.postMessage(params)
                }
                let isFrom = localStorage.getItem('isFrom')
                if (isWeixin || (isFrom && isFrom === 'zzApp')) {
                    this.$router.push(params)
                }
            } else {
                this.$router.push(params)
            }
        },
      breathMessage() {
        const { patient_id, project_id } = this.$route.query
        let params = `/docWorkRoom/hoursBp?patient_id=${patient_id}&project_id=${project_id}&title=${name}`

        if (process.env.NODE_ENV === 'production') {
          if (isAndroid) {
            window.android.breathMessage(params)
          }
          if (isIOS) {
            window.webkit.messageHandlers.breathMessage.postMessage(params)
          }
        } else {
          this.$router.push(params)
        }
      },
      showLandScape() {
        const { patient_id, project_id } = this.$route.query
        let param = `/docWorkRoom/ecgChartScreen?id=${1}&patient_id=${patient_id}`;
        if (process.env.NODE_ENV === 'production') {
            if (isAndroid) {
              window.android.showLandScape(param);
            }
            if (isIOS) {
              window.webkit.messageHandlers.showLandScape.postMessage(param);
            }
        } else {
          this.$router.push(param)
        }
      },
      showFilePdf() {
        const { patient_id, project_id } = this.$route.query
        if (process.env.NODE_ENV === 'production') {
            if (isAndroid) {
                window.android.showFilePdf('https://zmsc.zz-med-test.com/api/web/v1/inspect/file/%E8%82%BF%E7%98%A4%E6%A0%87%E5%BF%97%E7%89%A9%E6%8A%A5%E5%91%8A.pdf?token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***********************************************************************************************************************************************************************************************************************************************************************.tj1k2ZVbNtnPd1aYe0BKaMwkJyol03CzZyRL1fcc0KU&ext=pdf&file_path=58999_180776/material/20230711/1545248639960481830_64ad051e2e081_20230711153038.pdf|150008|detection');
              }
              if (isIOS) {
                window.webkit.messageHandlers.showFilePdf.postMessage('https://zmsc.zz-med-test.com/api/web/v1/inspect/file/%E8%82%BF%E7%98%A4%E6%A0%87%E5%BF%97%E7%89%A9%E6%8A%A5%E5%91%8A.pdf?token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***********************************************************************************************************************************************************************************************************************************************************************.tj1k2ZVbNtnPd1aYe0BKaMwkJyol03CzZyRL1fcc0KU&ext=pdf&file_path=58999_180776/material/20230711/1545248639960481830_64ad051e2e081_20230711153038.pdf|150008|detection');
              }
        } else {

        }
      },
        handleClickTest () {
            alert(1)
            if (process.env.NODE_ENV === "production") {

                if (isAndroid) {
                    window.android.breathMessage(params)
                }
                if (isIOS) {
                    window.webkit.messageHandlers.breathMessage.postMessage(params)
                }
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.common {
    background: #F8F8F8;
    height: 100vh;
    .row {
        display: flex;
        justify-content: space-between;
        border-bottom: 1px solid #F1F1F1;
        background: white;
        padding: 14px;
        &:hover {
            cursor: pointer;
        }
        .left {
            font-size: 17px;
            color: #0A0A0A;
        }
        .right {
            font-size: 14px;
            color: #878F99;
            .van-icon {
                margin-left: 5px;
            }
        }
    }
}
.common-wechat {
    background-color: #F5F7FB;
}
</style>
