<template>
    <div>
        <div
            v-if="list && list.length > 0"
            class="common"
            :class="{ 'common-wechat': !$store.getters['docWorkRoom/isApp'] }"
            >
            <div
                v-for="item in list"
                :key="item.code"
                class="row"
                @click="handleClick(item)"
                >
                <div class="left">{{ item.name }}</div>
                <div class="right">
                    <span>{{ item.measdate }}</span>
                    <van-icon name="arrow" />
                </div>
            </div>
        </div>
        <empty v-else></empty>
    </div>
</template>

<script>
import { Toast } from 'vant'
import empty from '../components/empty.vue'
import { getFuncDetailApi } from "@/api/saas.js"
import { getChildRight } from "@/utils/utils.js"
// 老模式页面code
const old = ['eyeCheck', 'bchumancomponent', 'pwv', 'bcnerveconduction', 'hoursBp', 'fat', 'bloodsceen','ecg', 'liver_hardness']
// 老模式code 映射路由
const oldToUrl = {
    eyeCheck: '/docWorkRoom/fundusExam',                // 眼底
    bchumancomponent: '/docWorkRoom/humancomponent',    // 人体成分分析
    pwv: '/docWorkRoom/pwvAbiTbi',                      // pwv
    bcnerveconduction: '/docWorkRoom/nerveconduction',  // 神经传导
    fat: '/docWorkRoom/visceralFat',                    // 内脏脂肪
    bloodsceen: '/docWorkRoom/vec',                     // 血管内皮
    ecg: '/docWorkRoom/ecgExam',                        // 心电图
    liver_hardness: '/docWorkRoom/liverHardness',       // 肝胆弹性
    hoursBp: '/docWorkRoom/hoursBp',                    // 24小时动态血压
}
// 老模式code 映射安卓和iOS方法
// const oldToMethod = {
//     eyeCheck: 'fundusMessage',
//     bchumancomponent: 'breathMessage',
//     pwv: 'pwvAbiTbiMessage',
//     bcnerveconduction: 'breathMessage',
//     fat: 'visceralFatMessage',
//     bloodsceen: 'vecMessage',
//     ecg: 'breathMessage',
//     liver_hardness: 'breathMessage',
//     hoursBp: 'breathMessage'
// }
// 生命体征
const life_sign = ['public_template_tab_hw', 'public_template_tab_sides', 'public_template_tab_bp', 'public_template_tab_lifesigns_peak_flow_rate',
'public_template_tab_lifesigns_blood_oxygen', 'public_template_tab_general_signs','public_template_tab_bs']
const UA = navigator.userAgent;
const isAndroid = UA.indexOf("Android") > -1 || UA.indexOf("Adr") > -1; // android终端
const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); // ios终端
const isWeixin = UA.indexOf("micromessenger") !== -1 || UA.indexOf("MicroMessenger") !== -1;    // 微信
import wx from "weixin-js-sdk"
export default {
    components: {
        empty,
    },
    data () {
        return {
            list: [],
        }
    },
    computed: {
        searchInfo() {
            return this.$store.getters['docWorkRoom/searchInfo']
        },
        platform() {
            return this.$store.getters['docWorkRoom/platform']
        },
    },
    created () {
        document.title = this.$route.query.title ? decodeURI(this.$route.query.title) : ''
        this.getFuncDetail()
    },
    methods: {
        async getFuncDetail () {
            const toast = Toast.loading({
                message: '加载中...',
                forbidClick: true,
                duration: 0
            })
            console.log(1, this.toast)
            try {
                const { patient_id, indicator_group_id} = this.$route.query
                const { data } = await getFuncDetailApi({
                    patient_id,
                    indicator_group_id,
                    // ...this.searchInfo
                })
                // this.list = getChildRight(data, ['patient_list', 'detail', this.$route.query.code])
                this.list = data.list
                // 过滤掉哮鸣音
                this.list = this.list && this.list.filter(item => item.code != 'public_template_tab_wheeze')
                toast.clear()
            } catch {
                toast.clear()
            }

        },
        // handleLimitsAuthority(list) {
        //     let data = list.reduce((pre, cur) => {
        //         if (cur.code == 6000002 && cur.state) {
        //             pre.push({
        //                 name: cur.name,
        //                 code: 'public_template_tab_' + cur.alias
        //             })
        //             return pre
        //         } else {
        //             return []
        //         }
        //     }, [])
        //     return data
        // },
        // async addTrendBs () {
        //     const { data } = await getAuthorityFieldsCodesApi()
        //     const cgmBs = data.find(item => item.alias == 'cgm_bg_records') && data.find(item => item.alias == 'cgm_bg_records').child
        //     if (cgmBs) {
        //         const tempCgmBs = this.handleLimitsAuthority(cgmBs)
        //         if (process.env.NODE_ENV === "production") { 
        //             if (this.$store.getters['docWorkRoom/isApp']) {
        //                 this.list.push(...tempCgmBs)
        //             }
        //         } else {
        //             this.list.push(...tempCgmBs)
        //         }
        //     }
            
        // },
        handleClick ({ name, indicator_group_id, code }) {
            const { patient_id, project_id } = this.$route.query
            let params = ''
            if (old.includes(code)) { // 辅助检查老模式
                params = `${oldToUrl[code]}?patient_id=${patient_id}&project_id=${project_id}`
            } else if (life_sign.includes(code)) { // 生命体征
                params = `/docWorkRoom/sickerMedicalRecord/personal?patient_id=${patient_id}&project_id=${project_id}&title=${encodeURI(name)}&indicator_group_id=${indicator_group_id}&code=${code}`
            } else if (code == 'cgm_bg' || name == 'CGM血糖') {
                params = `/docWorkRoom/sickerMedicalRecord/cgmBs?patient_id=${patient_id}&project_id=${project_id}&from_common_menu=true`
            } else { // 新模式（辅助检查，生物学标志物，实验室检查）
                params = `/docWorkRoom/sickerMedicalRecord/commonCheck?patient_id=${patient_id}&project_id=${project_id}&title=${encodeURI(name)}&indicator_group_id=${indicator_group_id}`
            }

            if (process.env.NODE_ENV === "production") {
                // 暴风 智众健康助手App
                let isFrom = localStorage.getItem('isFrom')
                if(isFrom && isFrom === 'zzApp'){
                    this.$router.push(params)
                    return
                }
                if (isWeixin) {
                    // this.$router.push(params)
                  wx.miniProgram.navigateTo({
                    // url: `/pages/my/pages/webView2/webView2?url=${location.origin}${params.replace('?', '&')}`,
                    url: `/pages/my/pages/webView/webView?url=${encodeURIComponent(location.origin + params)}`,
                  })
                    return
                }
                if (isAndroid) {
                    window.android.breathMessage(params)
                    return
                }
                if (isIOS) {
                    window.webkit.messageHandlers.breathMessage.postMessage(params)
                    return
                }

            } else {
                this.$router.push(params)
            }
        },
    }
}
</script>

<style lang="scss" scoped>
.common {
    background: #F8F8F8;
    height: 100vh;
    .row {
        display: flex;
        justify-content: space-between;
        border-bottom: 1px solid #F1F1F1;
        background: white;
        padding: 17px 14px;
        &:hover {
            cursor: pointer;
        }
        .left {
            font-size: 17px;
            color: #0A0A0A;
        }
        .right {
            font-size: 14px;
            color: #878F99;
            .van-icon {
                margin-left: 5px;
            }
        }
    }
}
.common-wechat {
    background-color: #F5F7FB;
}
</style>
