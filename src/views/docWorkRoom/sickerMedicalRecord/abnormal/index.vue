<template>
  <div :class="`check check-${platform}`">
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <van-list
        v-model="loading"
        :finished="finished"
        finished-text="已经全部加载完毕"
        @load="onLoad"
      >
      <div 
          v-for="item in list"
          :key="item.id"
          class="content">
          <div class="information">
            <div class="name">
              {{ item.name }}
              <span v-if="item.measure_type_name" class="sub_name">({{ item.measure_type_name }})</span>
            </div>
            <div class="value-unit">
              <div class="value">
                <div v-if="item.code == 'sbp' || item.code == 'dbp'" class="special-flex">
                  <div class="flex">
                    <abnormalIndicator
                      indicatorsource="patientList"
                      :code="item.abnormal_info[0].code"
                      :record="item.abnormal_info[0]"
                      :codeValue="item.abnormal_info[0].value"
                      :isShowDoc="false"
                    />
                    <span v-if="item.abnormal_info.length > 1"> &nbsp;/ &nbsp;</span>
                    <abnormalIndicator
                      v-if="item.abnormal_info.length > 1"
                      indicatorsource="patientList"
                      :code="item.abnormal_info[1].code"
                      :record="item.abnormal_info[1]"
                      :codeValue="item.abnormal_info[1].value"
                      :isShowDoc="false"
                    />
                  </div>
                  <bp-status :record="item"></bp-status>
                </div>
                <abnormalIndicator
                  v-else
                  indicatorsource="patientList"
                  :code="item.abnormal_info[0].code"
                  :record="item.abnormal_info[0]"
                  :codeValue="item.abnormal_info[0].value"
                />
              </div>
              <div class="unit">{{ item.unit }}</div>
            </div>
          </div>
          <div class="measure-at">{{ item.measure_at }}</div>
        </div>
      </van-list>
    </van-pull-refresh>
  </div>
</template>

<script>
import { Toast } from 'vant'
import abnormalIndicator from '../components/abnormalIndicator.vue'
import bpStatus from '../components/bpStatus.vue'
import { getAbnormalIndicatorDetailListApi } from '@/api/saas.js'
export default {
  components: {
    abnormalIndicator,
    bpStatus
  },
  computed: {
    platform() {
      return this.$store.getters['docWorkRoom/platform']
    },
    isApp() {
      return this.$store.getters['docWorkRoom/isApp']
    },
    navigatorInfo() {
      return this.$store.getters['docWorkRoom/navigatorInfo']
    },
  },
  data() {
    return {
      refreshing: false,
      loading: false,
      finished: false,
      list: [],
      page: 1
    }
  },
  created() {
    document.title = this.$route.query.title ? decodeURI(this.$route.query.title) : ''
  },
  methods: {
    async getAbnormalIndicatorDetailList () {
      // const toast = Toast.loading({
      //   message: '加载中...',
      //   forbidClick: true,
      //   duration: 0
      // })
      const { patient_id, project_id, indicator_group_id, measure_type, indicator_id } = this.$route.query
      try {
        const { data } = await getAbnormalIndicatorDetailListApi({
          patient_id,
          measure_type,
          indicator_group_id,
          indicator_id,
          page: this.page,
          page_size: 10
        })
        for (let i = 0; i< data.data.length; i++) {
          this.list.push(data.data[i])
        }
        this.page += 1
        this.loading = false
        this.refreshing = false
        if (data.current_page == data.total_page) {
          this.finished = true
        }
        // toast.clear()
      } catch {
        // toast.clear()
      }
    },
    onRefresh () {
      this.list = []
      this.page = 1
      this.finished = false
      this.loading = false
      this.refreshing = true
      this.onLoad()
    },
    onLoad () {
      this.getAbnormalIndicatorDetailList()
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../common.scss";
.check {
  background-color: $app-bg-color;
  height: 100vh;
  .content {
    padding: 17px 20px 15px;
    background-color: white;
    border-bottom: 1px solid #F1F1F1;
    text-align: left;
    font-size: 17px;
    display: flex;
    justify-content: space-between;

    .information{
      .name {
        font-size: 17px;
        font-weight: 400;
        line-height: 24px;
        color: #0A0A0A;
        .sub_name {
          color: #878F99;
        }
      }
      .value-unit{
        display: flex;
        align-items: baseline;
        .value{
          display: inline;
          font-size: 20px;
          font-style: normal;
          font-weight: 400;
          line-height: normal;
          color: #0A0A0A;
        }
        .value ::v-deep .abnormal-indicator-box-value {
          display: inline;
          font-size: 20px;
          font-style: normal;
          font-weight: 400;
          line-height: normal;
          //color: #0A0A0A;
        }
        
        .unit {
          display: inline;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          line-height: normal;
          color: #878F99;
          margin-left: 11px;
        }
      }
    }

    .measure-at{
      min-width: 105px;
      text-align: right;
      margin-top: 4px;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
      color: #5A6266;
    }
    .flex {
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
    }
    .special-flex {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }
  }
}
.check-wechat{
  background-color: $wx-bg-color;
  .content {
    background-color: #fff
    
  }
}
</style>