<!--个人体征和核心数据折线图-->
<template>
    <div :class="`box-chart box-chart-${platform}`">
        <div class="chart-title">
            <div @click="handleTitle">
                <span class="span">{{ chartTitle }}</span>
                <van-icon class="icon" name="arrow-down" />
            </div>
            <!-- <van-radio-group
                v-if="groupType && haveData"
                v-model="groupType"
                direction="horizontal"
                @change="groupTypeFunc"
                icon-size="15px"
                >
                <van-radio :name="1">瞬时</van-radio>
                <van-radio :name="2">平均</van-radio>
            </van-radio-group> -->
            <div v-if="groupType && haveData" class="btn-group">
                <div
                    @click="groupTypeFunc(1)"
                    class="item"
                    :class="{ active: groupType == 1}">
                    瞬时
                </div>
                <div
                    @click="groupTypeFunc(2)"
                    class="item-2"
                    :class="{ active: groupType == 2}">
                    平均
                </div>
            </div>
            <div v-if="!$route.query.landscape && isApp" @click="handleAllScreen" class="result-allscreen">
                <img class="img" src="../../imgs/allscreen.png" alt="">
            </div>
        </div>

        <div class="chart-unit">{{ unit }}</div>
        <div
            v-show="haveData"
            class="chart"
            ref="chartRef"></div>
        <empty v-show="!haveData" :chartEmpty="true"></empty>
        <van-popup
            v-model="show"
            position="bottom"
            round
        >
            <van-picker
                show-toolbar
                :columns="list"
                confirm-button-text="确定"
                value-key="name"
                :default-index="index"
                @confirm="onConfirm"
                @cancel="show = false"
            ></van-picker>
        </van-popup>
    </div>
</template>

<script>
import { Toast } from 'vant'
const UA = navigator.userAgent;
const isAndroid = UA.indexOf("Android") > -1 || UA.indexOf("Adr") > -1; // android终端
const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); // ios终端
const isWeixin = UA.indexOf("micromessenger") !== -1 || UA.indexOf("MicroMessenger") !== -1;    // 微信
import echarts from 'echarts'
import empty from '../components/empty.vue'
import { getCoreDataChartListApi, getFuncDetailApi, getCoreDataList } from '@/api/saas.js'
import { getChildRight } from "@/utils/utils.js"

import moment from 'moment'
export default {
    components: {
        empty
    },
    data () {
        return {
            chartData: {},
            list: [],
            haveData: true,
            groupType: null,
            chartTitle: null,
            unit: null,
            maxYval: null,
            dataValueList: null,
            show: false,
            index: 0
        }
    },
    computed: {
        searchInfo() {
            return this.$store.getters['docWorkRoom/searchInfo']
        },
        platform() {
            return this.$store.getters['docWorkRoom/platform']
        },
        isApp() {
            return this.$store.getters['docWorkRoom/isApp']
        }
    },
    created () {
        document.title = this.$route.query.title ? decodeURI(this.$route.query.title) + '趋势' : '数据趋势'
        // if (this.$route.query.landscape) {
        //     document.documentElement.style.fontSize = '32px'
        // }
    },
    mounted() {
        if (isWeixin) {
            window.addEventListener('orientationchange', this.handleOrientationChange)
        }
        if (this.$route.query.code == 'core_data') {
            this.getCoreData()
        } else {
            this.getFuncDetail()
        }
    },
    beforeDestroy() {
        window.removeEventListener('orientationchange', this.handleOrientationChange)
    },
    methods: {
        handleOrientationChange () {
            // if (window.orientation === 90 || window.orientation === -90) {
                if (this.$route.query.code == 'core_data') {
                    this.getCoreData()
                } else {
                    this.getFuncDetail()
                }
            // }
        },
        dealData () {
            if (this.list && this.list.length > 0) {
                if (this.$route.query.landscape) {
                    this.getCoreDataChartList(this.list[parseInt(this.$route.query.index)])
                    this.index = this.$route.query.index
                } else {
                    this.getCoreDataChartList(this.list[0])
                    this.index = 0
                }
            }
        },
        // 核型数据列表
        async getCoreData () {
            const toast = Toast.loading({
                message: '加载中...',
                forbidClick: true,
                duration: 0
            })
            try {
                const { status, data } = await getCoreDataList({
                    patient_id: this.$route.query.patient_id,
                    ...this.searchInfo
                })
                if (status == 200) {
                    this.list = data
                    this.dealData()
                }
                toast.clear()
            } catch (e) {
                toast.clear()
            }
        },
        async getFuncDetail () {
            const toast = Toast.loading({
                message: '加载中...',
                forbidClick: true,
                duration: 0
            })
            try {
                const { patient_id, indicator_group_id } = this.$route.query
                const { data } = await getFuncDetailApi({
                    patient_id,
                    indicator_group_id,
                    // ...this.searchInfo
                })
                // this.list = getChildRight(data, ['patient_list', 'detail', this.$route.query.code])
                this.list = data.list
                // 过滤掉哮鸣音
                this.list = this.list && this.list.filter(item => item.code != 'public_template_tab_wheeze')
                this.dealData()
                toast.clear()
            } catch {
                toast.clear()
            }

        },
        async getCoreDataChartList ({indicator_group_id, code, indicator_ids, field_type }) {
            const toast = Toast.loading({
                message: '加载中...',
                forbidClick: true,
                duration: 0
            })
            try {
                const { patient_id } = this.$route.query
                let tempObj = {

                }
                if (this.$route.query.code == 'core_data') {
                    tempObj = {
                        ...this.searchInfo
                    }
                } else {
                    tempObj = {}
                }
                const { data } = await getCoreDataChartListApi({
                    patient_id,
                    indicator_group_id,
                    measure_type: 0,
                    measure_method: '2',
                    show_detail: code == 'public_template_tab_bs' ? 1 : 0,// 如果是血糖就传1
                    ...tempObj,
                    indicator_ids,
                    field_type
                })
                this.chartData = data
                this.getEchartsFunc()
                toast.clear()
            } catch {
                toast.clear()
            }
        },
        getMarkLine () {
            let lineStyle = {
                color: '#ff0000'
            }
            let markLine = { symbolSize: 0, data: [] }
            let maxVal = Math.ceil(Math.max(...this.dataValueList))
            switch (this.chartData.function_code) {
                case 'public_template_tab_bp':
                    if (this.chartData.code === 'pulse') { // 心率
                        markLine = { symbolSize: 0, data: [
                            { lineStyle, yAxis: 100 },
                            { lineStyle, yAxis: 60 },
                        ] }
                        maxVal = maxVal > 110 ? maxVal : 110
                    }
                    if (this.chartData.code.indexOf('sbp,dbp') !== -1) { // 血压
                        markLine = { symbolSize: 0, data: [
                            { lineStyle, yAxis: 140 },
                            { lineStyle, yAxis: 90 },
                        ] }
                        maxVal = maxVal > 150 ? maxVal : 150
                    }
                break;
                case 'public_template_tab_hw': // 身高体重
                    if (this.chartData.code === 'bmi') { // bmi
                        markLine = { symbolSize: 0, data: [
                            { lineStyle, yAxis: 24 },
                            { lineStyle, yAxis: 18.5 },
                        ] }
                        maxVal = maxVal > 30 ? maxVal : 30
                    }
                break;
                case 'public_template_tab_lifesigns_blood_oxygen': // 血氧饱和
                    markLine = { symbolSize: 0, data: [
                        { lineStyle, yAxis: 95 },
                        ] }
                    maxVal = 100
                break;
            }
            this.maxYval = maxVal
            return { markLine }
        },
        getSeriesItem (v) {
            this.dataValueList = [...this.dataValueList, ...v.values]
            const { markLine } = this.getMarkLine()
            return {
                markLine,
                connectNulls: true,
                name: `${v.name}|${v.unit}`,
                type: 'line',
                data: v.values,
                symbol: 'circle',
                symbolSize: 12,
            }
        },
        getEchartsFunc () {
            const data = this.chartData
            this.haveData = data.charts && (data.charts.length !== 0)
            this.chartTitle = data.detection_name
            if (data.function_code == 'cgm_bg' || data.detection_name == '动态血糖') {
                this.jumpFun()
                return
            }
            this.unit = data.unit || ''
            const unit = this.unit
            this.maxYval = 0
            this.dataValueList = []
            let myEchart = echarts.init(this.$refs.chartRef, 'light')
            if (!this.haveData) {
                return
            }
            const actData = data.charts[0] || {}

            let xAxisData = actData.dates
            let series = []
            let seriesObj = {}

            // group_typ 1 平均
            if (actData.group_type) {
                (!this.groupType) && (this.groupType = 1);
                data.charts.forEach(v => {
                    !seriesObj[v.group_type] && (seriesObj[v.group_type] = [])
                    seriesObj[v.group_type].push(v)
                })

                seriesObj[this.groupType].forEach((v) => {
                    xAxisData = v.dates
                    series.push(this.getSeriesItem(v))
                })

            } else {
                this.groupType = 0
                data.charts.forEach((v) => {
                    series.push(this.getSeriesItem(v))
                })
            }

            const options = {
                tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                },
                formatter: function (params) {
                    let htmlStr = '';
                    for (let i = 0; i < params.length; i++) {
                    let param = params[i];
                    let xName = param.name;
                    let [seriesName, paramunit] = param.seriesName.split('|');
                    let value = param.value;
                    let color = param.color;
                    if (i === 0) {
                        htmlStr += xName + '<br/>'; //x轴的名称
                    }
                    htmlStr += `<div style='width: 100%;display: flex; justify-content: space-between; align-items:center;margin-top: 4px'>
                                <div>
                                    <span style="margin-right:5px;display:inline-block;width:10px;height:10px;border-radius:5px;
                                    background-color:${color};"></span>
                                    ${seriesName}
                                </div>
                                <div>
                                    <span>${value}</span>
                                    ${unit ? '' : `<span style="margin-left:2px;">${paramunit}</span>`}
                                </div>
                                </div>`
                    }
                    htmlStr += '</div>';
                    return htmlStr
                }
                },
                legend: {
                    type: 'scroll',
                    itemWidth: 15,
                    itemGap: 20,
                    icon: 'circle',
                    top: 10,
                    right: 24,
                    width: '70%',
                    formatter: function (params) {
                        return params.split('|')[0]
                    }
                },
                grid: {
                    left: 20,
                    right: 40,
                    bottom: 11,
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    axisLine: {
                        show: true
                    },
                    axisTick: {
                        show: true
                    },
                    axisLabel: {
                        margin: 16,
                        formatter: function (value) {
                        return moment(value).format('M/D')
                        }
                    },
                    data: xAxisData
                },
                yAxis: [
                {
                    max: this.maxYval,
                    type: 'value',
                    axisLabel: {
                        formatter: '{value}',
                        margin: 20,
                        },
                        splitLine: {
                        show: true,
                        lineStyle: {
                            type: [2, 16],
                            color: 'rgba(0, 0, 0, 0.25)',
                            dashOffset: 16
                        }
                    }
                }
                ],
                series
            };
            myEchart.clear();
            myEchart.setOption(options)
            // window.addEventListener('resize', () => {
                this.$nextTick(() => {
                    myEchart.resize()
                })
            // })
        },
        groupTypeFunc (value) {
            this.groupType = value
            this.getEchartsFunc()
        },
        handleTitle () {
            this.show = true
        },
        onConfirm (value, index) {
            console.log(value, index)
            this.show = false
            this.index = index
            if (value.code != 'cgm_bg') {
                this.getCoreDataChartList(value)
            } else {
                this.jumpFun()
            }
        },
        jumpFun () {
            const { patient_id, project_id } = this.$route.query
            let params = `/docWorkRoom/sickerMedicalRecord/cgmBs?patient_id=${patient_id}&project_id=${project_id}&from_common_menu=true`
            if (process.env.NODE_ENV === "production") {
                // 暴风 智众健康助手App
                let isFrom = localStorage.getItem('isFrom')
                if(isFrom && isFrom === 'zzApp'){
                    this.$router.push(params)
                    return
                }
                if (isWeixin) {
                    // this.$router.push(params)
                wx.miniProgram.navigateTo({
                    // url: `/pages/my/pages/webView2/webView2?url=${location.origin}${params.replace('?', '&')}`,
                    url: `/pages/my/pages/webView/webView?url=${encodeURIComponent(location.origin + params)}`,
                })
                    return
                }
                if (isAndroid) {
                    window.android.breathMessage(params)
                    return
                }
                if (isIOS) {
                    window.webkit.messageHandlers.breathMessage.postMessage(params)
                    return
                }

            } else {
                this.$router.push(params)
            }
        },
        onCancel () {
            this.show = false
        },
        handleAllScreen(){
            let params = location.pathname + location.search + `&landscape=true&index=${this.index}`
            if (process.env.NODE_ENV === "production") {
                // 暴风 智众健康助手App
                let isFrom = localStorage.getItem('isFrom')
                if(isFrom && isFrom === 'zzApp'){
                    this.$router.push(params)
                    return
                }
                if (isAndroid) {
                    window.android.showLandScape(params)
                }
                if (isIOS) {
                    window.webkit.messageHandlers.showLandScape.postMessage(params)
                }
            }
        },
    }
}
</script>

<style lang="scss" scoped>
@import "../common.scss";
.box-chart {
    width: 100%;
    position: relative;
    .chart-unit {
        position: absolute;
        top: 72px;
        left: 24px;
        font-size: 12px;
        color: #74787C;
    }
    .chart {
        width: 100%;
        height: calc(100vh - 60px);
    }
    .chart-title {
        padding: 16px 14px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #0A0A0A;
        font-size: 17px;

        .icon {
            color: #C0C6CC;
            margin-left: 8px;
        }
        .result-allscreen {
            height: 26px;
            .img {
                height: 26px;
            }
        }
    }
    ::v-deep .van-radio {
        font-size: 14px;
    }
    ::v-deep .van-picker__confirm {
        color: $app-color;
        // font-size: 15px;
    }
    .btn-group {
        display: inline-flex;
        font-size: 15px;
        .item {
            padding: 0 14px;
            height: 27px;
            line-height: 30px;
            background: #EEEEEE;
            border-top-left-radius: 4px;
            border-bottom-left-radius: 4px;
            color: #878F99;
        }
        .item-2 {
            padding: 0 14px;
            height: 27px;
            line-height: 30px;
            background: #EEEEEE;
            border-top-right-radius: 4px;
            border-bottom-right-radius: 4px;
            color: #878F99;
        }
        .active {
            background: $app-color;
            color: white;

        }

    }
}
.box-chart-wechat {
    ::v-deep .van-picker__confirm {
        color: $wx-color;
        // font-size: 15px;
    }
}

@media screen and (orientation: landscape) {
  .box-chart {
    width: calc(100% - env(safe-area-inset-right));
    position: relative;
    .chart-unit {
        position: absolute;
        top: 36px;
        left: 12px;
        font-size: 6px;
        color: #74787C;
    }
    .chart {
        width: 100%;
        height: calc(100vh - 20px);
    }
    .chart-title {
        padding: 8px 7px 0 7px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #0A0A0A;
        font-size: 8.5px;

        .icon {
            color: #C0C6CC;
            margin-left: 4px;
        }
        .result-allscreen {
            height: 26px;
            .img {
                height: 26px;
            }
        }
    }
    ::v-deep .van-radio {
        font-size: 14px;
    }
    ::v-deep .van-picker__confirm {
        color: $app-color;
        font-size: 7.5px;
    }
    .btn-group {
        display: inline-flex;
        font-size: 7.5px;
        .item {
            padding: 0 7px;
            height: 13.5px;
            line-height: 15px;
            background: #EEEEEE;
            border-top-left-radius: 2px;
            border-bottom-left-radius: 2px;
            color: #878F99;
        }
        .item-2 {
            padding: 0 7px;
            height: 13.5px;
            line-height: 15px;
            background: #EEEEEE;
            border-top-right-radius: 2px;
            border-bottom-right-radius: 2px;
            color: #878F99;
        }
        .active {
            background: $app-color;
            color: white;
        }

    }
    }

}


</style>
