<!--实验室检查，辅助检查，生物学标志物-->
<template>
    <div>
        <div
            v-if="list && list.length > 0"
            class="check"
            :class="{ 'check-wechat': !$store.getters['docWorkRoom/isApp'] }"
            >
            <div
                v-for="(item, index) in list"
                :key="index"
                >
                <div @click="handleClick(item, index)"  class="row">
                    <div class="left">{{ item.measure_date }}</div>
                    <div class="right">
                        <van-icon v-if="item.extend" name="arrow-up" />
                        <van-icon v-else name="arrow-down" />
                    </div>
                </div>
                <div v-if="item.children && item.children.length > 0 && item.extend">
                    <div
                        v-for="(childItem, childIndex) in item.children"
                        :key="childIndex"
                        class="content">
                        <div class="left">
                            <div class="name">{{ childItem.name }}</div>
                            <div v-if="childItem.unit" class="unit">{{ childItem.unit }}</div>
                        </div>
                        <div class="right">
                            <div v-if="childItem.showValue" class="value">
                            <!-- {{ childItem.showValue }} -->
                                <abnormalIndicator
                                    :code="childItem.code"
                                    :record="item"
                                    :codeValue="childItem.showValue"
                                />
                            </div>
                            <div v-else class="value no-value">无结果</div>
                            <div v-if="childItem.type === 'content-options' && childItem.inputValue" class="range">{{ childItem.inputValue }}</div>
                            <div v-if="childItem.reference_range !== childItem.type === 'content-options'" class="range">{{ childItem.reference_range }}</div>
                        </div>
                    </div>
                    <report-item
                        v-if="item.file_paths && item.file_paths.length > 0"
                        :fileList="item.file_paths"
                        :pageName="$route.query.title">
                    </report-item>
                </div>
            </div>
        </div>
        <empty v-else></empty>
    </div>
</template>

<script>
import { Toast } from 'vant'
import empty from '../components/empty.vue'
import reportItem from '../components/reportItem.vue'

import { getExaminationItemListApi, getDetectionDetailApi } from "@/api/saas.js"
import { getDataProcessing } from "@/utils/utils.js"
import abnormalIndicator from '../components/abnormalIndicator.vue'

export default {
    components: {
        empty,
        reportItem,
        abnormalIndicator
    },
    data () {
        return {
            list: [],
        }
    },
    created () {
        document.title = this.$route.query.title ? decodeURI(this.$route.query.title) : ''
        this.getExaminationItemList()
    },
    computed: {
        searchInfo() {
            return this.$store.getters['docWorkRoom/searchInfo']
        },
    },
    methods: {
        async getExaminationItemList () {
            const { patient_id, project_id, indicator_group_id } = this.$route.query
            const { data } = await getExaminationItemListApi({
                patient_id,
                indicator_group_id,
                page: 1,
                page_size: 100,
                // ...this.searchInfo
            })
            if (data && data.list && data.list.length > 0) {
                this.list = data.list
                this.list[0].extend = true
                this.getDetectionDetail(this.list[0].id, 0)
            }
        },
        async getDetectionDetail (id, index) {
            const toast = Toast.loading({
                message: '加载中...',
                forbidClick: true,
                duration: 0
            })
            try {
                const { data } = await getDetectionDetailApi({
                    id
                })
                this.$set(this.list[index], 'children', getDataProcessing(data).fields)
                this.$set(this.list[index], 'abnormal_info', getDataProcessing(data).abnormal_info)
                this.$set(this.list[index], 'file_paths', data.file_paths)
                toast.clear()
            } catch {
                toast.clear()
            }

        },
        handleClick (item, index) {
            this.list = this.list.map((v, i) => {
                if (i == index) {
                    v.extend = !v.extend
                }
                // else {
                //     v.extend = false
                // }
                return v
            })
            if (this.list[index].extend) {
                this.getDetectionDetail(item.id, index)
            }
        },
    }
}
</script>

<style lang="scss" scoped>
.check {
    background-color: #F8F8F8;
    height: 100vh;
    .row {
        display: flex;
        justify-content: space-between;
        padding: 17px 14px;
        background-color: #F8F8F8;
        &:hover {
            cursor: pointer;
        }
        .left {
            font-size: 15px;
            color: #0A0A0A;
        }
        .right {
            font-size: 14px;
            color: #878F99;
        }
    }
    .content {
        padding: 13.5px 14px;
        background-color: white;
        border-bottom: 1px solid #F1F1F1;
        display: flex;
        justify-content: space-between;
        font-size: 17px;
        line-height: 24px;
        .left, .right {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            justify-content: space-between;
            width: 50%;
            text-align: left;
            line-height: 24px;
        }
        .right {
            align-items: flex-end;
        }
        .name {
            font-size: 17px;
            color: #0A0A0A;
        }
        .value {
            font-size: 17px;
            color: #5A6266;
        }
        .no-value {
            color: #C0C6CC;
        }

        .unit, .range {
            padding-top: 12px;
            font-size: 12px;
            color: #878F99;
        }
    }
}
.check-wechat {
    background-color: #F5F7FB;
    .row {
        background-color: #F5F7FB;
    }
}
</style>
