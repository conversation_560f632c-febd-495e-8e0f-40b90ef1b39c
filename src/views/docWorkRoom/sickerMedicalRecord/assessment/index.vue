<template>
  <div>
    <div
      v-if="list && list.length > 0"
      class="check"
      :class="{ 'check-wechat': !$store.getters['docWorkRoom/isApp'] }"
    >
      <div
        v-for="(item, index) in list"
        :key="index"
      >
        <div @click="handleClick(item, index)" class="row">
          <div class="left">{{ setTime(item.created_at) }}</div>
          <div class="right">
            <van-icon v-if="item.extend" name="arrow-up"/>
            <van-icon v-else name="arrow-down"/>
          </div>
        </div>
        <div v-if="item.extend">
          <div class="content">
            <div class="left">
              <div class="name">{{ item.disease_name }}</div>
            </div>
            <div class="right">
              <div v-if="item.hierarchy_management" class="value">{{ item.hierarchy_management }}</div>
              <div v-else class="value no-value">无结果</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <empty v-else></empty>
  </div>
</template>

<script>
import moment from 'moment'
import empty from '../components/empty.vue'

import {getAssessmentApi} from "@/api/saas.js"

export default {
  components: {
    empty,
  },
  data() {
    return {
      list: [],
    }
  },
  created() {
    document.title = decodeURI(this.$route.query.title)
    this.getExaminationItemList()
  },
  computed: {
    searchInfo() {
      return this.$store.getters['docWorkRoom/searchInfo']
    },
  },
  methods: {
    async getExaminationItemList() {
      const {patient_id, project_id, indicator_group_id} = this.$route.query
      const { data } = await getAssessmentApi({
        patient_id,
        indicator_group_id,
        page: 1,
        // ...this.searchInfo
      })
      if (data && data.length > 0) {
        this.list = data
        this.list.map((v, k) => {
          v.extend = k === 0
        })
      }
    },
    handleClick(item, index) {
      this.list = this.list.map((v, i) => {
        if (i == index) {
          v.extend = !v.extend
        }
        // else {
        //     v.extend = false
        // }
        return v
      })
    },
    setTime(value) {
      return moment(value).format('YYYY-MM-DD')
    }
  }
}
</script>

<style lang="scss" scoped>
.check {
  background-color: #F8F8F8;
  height: 100vh;

  .row {
    display: flex;
    justify-content: space-between;
    padding: 17px 14px;
    background-color: #F8F8F8;

    &:hover {
      cursor: pointer;
    }

    .left {
      font-size: 15px;
      color: #0A0A0A;
    }

    .right {
      font-size: 14px;
      color: #878F99;
    }
  }

  .content {
    padding: 13.5px 14px;
    background-color: white;
    border-bottom: 1px solid #F1F1F1;
    display: flex;
    justify-content: space-between;
    font-size: 17px;
    line-height: 24px;
    .left, .right {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      justify-content: space-between;
      width: 50%;
      text-align: left;
    }

    .right {
      align-items: flex-end;
    }

    .name {
      font-size: 17px;
      color: #0A0A0A;
    }

    .value {
      font-size: 17px;
      color: #5A6266;
    }

    .no-value {
      color: #C0C6CC;
    }

    .unit, .range {
      padding-top: 12px;
      font-size: 12px;
      color: #878F99;
    }
  }
}

.check-wechat {
  background-color: #F5F7FB;

  .row {
    background-color: #F5F7FB;
  }
}
</style>
