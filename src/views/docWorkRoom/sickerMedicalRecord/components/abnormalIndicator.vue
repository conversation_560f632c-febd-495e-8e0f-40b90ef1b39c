<template>
  <div class="abnormal-indicator">
    <div class="abnormal-indicator-box">
      <!-- <span class="iconfont icona-26_warning" :class="`warningFilledStatus${abnormalInfo.deflection}`" v-if="(abnormalInfo.deflection === 4 || abnormalInfo.deflection === 3) && isNoNull" ></span> -->
      <div 
        v-if="(abnormalInfo.deflection === 4 || abnormalInfo.deflection === 3) && isNoNull"
        :class="`warningFilledStatus${abnormalInfo.deflection}`"
        >
        <img v-if="abnormalInfo.deflection === 4" class="common-icon" src="../../imgs/red_warn.png" alt="">
        <img v-if="abnormalInfo.deflection === 3" class="common-icon" src="../../imgs/blue_warn.png" alt="">
      </div>
      <div 
        :class="`abnormal-indicator-box-value value-status${isNoNull ? abnormalInfo.deflection : ''}`"
        :style="{color: abnormalInfo.is_text_type && abnormalInfo.abnormal_status == 1 ? '#F5242D' : ''}"
        >
        {{ isNoNull || ['pwvReport'].includes(indicatorsource) ? codeValue : '--' }}
        </div>
      <!-- <span class="iconfont iconpiandi" v-if="[1, 3].includes(abnormalInfo.deflection) && isNoNull" ></span>
      <span class="iconfont iconpiangao" v-if="[2, 4].includes(abnormalInfo.deflection) && isNoNull" ></span> -->
      <img v-if="[1, 3].includes(abnormalInfo.deflection) && isNoNull" class="common-icon arrow" src="../../imgs/piandi.png" alt="">
      <img v-if="[2, 4].includes(abnormalInfo.deflection) && isNoNull" class="common-icon arrow" src="../../imgs/piangao.png" alt="">
    </div>
    <div class="newindicator-inputValue" v-if="['vititManage', 'newindicator'].includes(indicatorsource) && isInputValue && isNoNull && record.type === 'content-options'">{{ record.inputValue }}</div>
    <div :class="`newindicator-tip`" v-if="isNoNull && abnormalInfo.status_display && isShowDoc">
      <div v-if="abnormalInfo.is_text_type && abnormalInfo.abnormal_status == 1" :class="`newindicator-tip-4`">{{ abnormalInfo.status_display }}</div>
      <div v-else :class="`newindicator-tip-${abnormalInfo.deflection}`">{{ abnormalInfo.status_display }}</div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    indicatorsource: { // 来源：newindicator新模式
      type: String,
      default: ''
    },
    isInputValue: { // 新模式是否展示选择题扩展输入的内容
      type: Boolean,
      default: false
    },
    isShowDoc: { // 是否展示异常文案
      type: Boolean,
      default: true
    },
    code: { // 当前指标code
      type: String,
      default: ''
    },
    record: { // 当前表格行数据集合
      type: Object,
      default: () => {
        return {}
      }
    },
    codeValue: { // 当前指标code的展示值
      type: [String, Number],
      default: ''
    },
    flag: {
      type: String,
      default: 'abnormal_info'
    }
  },
  data () {
    return {

    }
  },
  computed: {
   abnormalInfo() {         
    if (['patientList', 'vititManage', 'bsTableTooltip'].includes(this.indicatorsource)) {
        return this.record
    } else {
      const abnormalList = this.record[this.flag] && this.record[this.flag].filter(v => v.code === this.code)
      return abnormalList && abnormalList.length ? abnormalList[0] : {}
    }
   },
   isNoNull() {
    return (this.codeValue) !== '' && this.codeValue !== '--'
   }
  },
  created () {

  },
}
</script>

<style lang="scss" scoped>
.abnormal-indicator{
  display: flex;
  justify-content: center;
  flex-direction: column;

  .abnormal-indicator-box{
    display: flex;
    justify-content: center;
    align-items: center;
    color: #303133;

    .icona-26_warning{
      font-size: 12px;
      margin-top: 1px;
    }

    .iconpiangao,
    .value-status2,
    .value-status4
    {
      color: #F5242D;
    }
    .iconpiandi,
    .value-status1,
    .value-status3
    {
      color: #377CFB;
    }
    .iconpiandi{
      margin-top: 1px;
    }
    .warningFilledStatus3{
      color: #377CFB;
      margin-right: 4px;
      display: flex;
      align-items: center;
    }
    .warningFilledStatus4{
      color: #F5242D;
      margin-right: 4px;
      display: flex;
      align-items: center;
    }
  }
  .newindicator-inputValue{
    color: #999;
  }
  .abnormal-indicator-box-value {
    // font-size: 14px;
  }
  .newindicator-tip{
    width: 100%;
    color: #303133;
    font-size: 12px;
    line-height: 18px;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 2px;
    // padding-bottom: 6px;
    div{
      font-size: 12px;
      border-radius: 20px;
      padding: 2px 6px 0;
      transform: scale(0.95);
    }
  }

  .newindicator-tip-1, .newindicator-tip-3 {
    border: 1px solid #A0C8FF;
    background: #ECF4FF;
    color: #377CFB;
  }
  .newindicator-tip-2, .newindicator-tip-4 {
    border: 1px solid #FFB1B3;
    background: #FFEFF0;
    color: #F5242D;
  }
  .common-icon {
    height: 20px;
  }
  .arrow {
    margin-left: 4px;
  }
}
</style>