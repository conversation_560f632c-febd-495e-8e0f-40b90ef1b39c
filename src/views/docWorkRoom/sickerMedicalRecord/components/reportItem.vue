<template>
    <div>
        <div
            v-for="(item, index) in fileList"
            :key="index"
            class="report"
            @click="handleClick(item)"
            >
            <div class="report-title">
                <img :src="reportImg" class="img" alt="">
                <div>查看报告</div>
            </div>
            <div class="report-detail">
                查看详情
                <van-icon name="arrow" />
            </div>
        </div>
    </div>
</template>

<script>
const UA = navigator.userAgent;
const isAndroid = UA.indexOf("Android") > -1 || UA.indexOf("Adr") > -1; // android终端
const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); // ios终端
const isWeixin = UA.indexOf("micromessenger") !== -1 || UA.indexOf("MicroMessenger") !== -1;    // 微信
import reportImg from '@/assets/report.png'
import { getFileUrl, getPwvFileUrl } from '@/utils/utils.js'

export default {
    props: {
        fileList: {
            type: Array,
            default: () => []
        },
        pageType: {
            type: String,
            default: ''
        },
        pageName: {
            type: String,
            default: ''
        },
    },
    data () {
        return {
            reportImg: reportImg,
            fileName: {
                'pwv': 'PWV+ABI',
                'bloodsceen': '血管内皮',
                'fat': '内脏脂肪',
                'lung': '肺功能检测',
                'ecg': '心电图',
                'liver_hardness': '肝脏瞬时弹性硬度检测',
                'humancomponent': '人体成分分析',
                'nerveconduction': '神经传导',
            },
            imgList: []
        }
    },
    computed: {
        getFileFun () {
            return this.pageType === 'pwv' ? getPwvFileUrl : getFileUrl
        },
        platform() {
            return this.$store.getters['docWorkRoom/platform']
        },
    },
    methods: {
        handleClick ({ ext, file_path, path }) {
            if (path) { // 24小时动态血压的报告
                file_path = path
            }
            // weixin,h5
            const params = `/docWorkRoom/sickerMedicalRecord/commonReport?ext=${ext}&file_path=${file_path}&pageType=${this.pageType}&pageName=${encodeURI(this.pageName)}`
            console.log(this.getFileFun(ext, file_path, this.pageName || this.fileName[this.pageType] || ''))
            const isPdf = ext.toLocaleLowerCase() === 'pdf'
            // app pdf
            // const pathUrl = this.getFileFun(ext, file_path, this.pageName || this.fileName[this.pageType] || '')
            // app img
            // const imgPath = JSON.stringify({
            //     index: 0,
            //     imgs: [encodeURI(pathUrl)]
            // })
            if (process.env.NODE_ENV === "production") {
                // this.$router.push(params)
                // return
                // 微信 || 暴风 智众健康助手App
                let isFrom = localStorage.getItem('isFrom')
                if (isWeixin || (isFrom && isFrom === 'zzApp')) {
                    this.$router.push(params)
                    return
                }
                if (isAndroid) {
                    window.android.breathMessage(encodeURI(params))
                    // if (isPdf) {
                    //     window.android.showFilePdf(encodeURI(pathUrl))
                    // } else {
                    //     window.android.showLargeImage(imgPath)
                    // }
                }
                if (isIOS) {
                    window.webkit.messageHandlers.breathMessage.postMessage(encodeURI(params))
                    // if (isPdf) {
                    //     window.webkit.messageHandlers.breathMessage.postMessage(encodeURI(pathUrl))
                    // } else {
                    //     window.webkit.messageHandlers.showLargeImage.postMessage(imgPath)
                    // }
                }

            } else {
                this.$router.push(params)
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.report {
    display: flex;
    justify-content: space-between;
    padding: 14px;
    border-bottom: 1px solid #F1F1F1;
    line-height: 20px;
    .report-title {
        color: #0A0A0A;
        font-size: 17px;
        display: inline-flex;
        align-items: center;
        div{
            height: 20px;
            line-height: 22px;
        }
        .img {
          vertical-align: middle;
            width: 20px;
            height: 20px;
            margin-right: 7px;
        }
    }
    .report-detail {
        color: #878F99;
        font-size: 15px;
    }
    .van-icon {
        margin-left: 5px;
    }
}
</style>
