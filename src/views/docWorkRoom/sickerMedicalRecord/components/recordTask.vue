<template>
  <div class="check">
    <div class="row" @click="handleClick">
      <div class="left">
        <img :src="profileOutlined" alt="">
        {{levelName}}
      </div>
      <div class="right">
        <van-icon v-if="extend" name="arrow-up"/>
        <van-icon v-else name="arrow-down"/>
      </div>
    </div>
    <div v-if="extend">
      <div
        v-for="childItem in levelTask"
        :key="childItem.id"
        class="content">
        <div class="name">{{ childItem.name }}</div>
      </div>
    </div>
    <empty v-if="!levelTask.length && extend" :isRecord="true" />
  </div>
</template>

<script>
import empty from './empty.vue'
import profileOutlined from '../../imgs/profileOutlined.png'

export default {
  components: {
    empty,
  },
  props: {
    levelTask: {
      type: Array,
      default() {
        return []
      }
    },
    levelName: {
      type: String,
      default: ''
    }
  },
  computed: {
    platform() {
      return this.$store.getters['docWorkRoom/platform']
    },
  },
  data() {
    return {
      profileOutlined,
      extend: true
    }
  },
  created() {
  },
  methods: {
    handleClick() {
      this.extend = !this.extend
    }
  }
}
</script>

<style lang="scss" scoped>
.check {
  background-color: #fff;

  .row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #F8F8F8;
    padding: 16px 20px;

    .left {
      display: flex;
      justify-content: flex-start;
      color: #0A0A0A;
      font-size: 17px;
      font-style: normal;
      font-weight: 400;
      line-height: 24px;
      img{
        display: block;
        width: 19px;
        height: 19px;
        margin-right: 8px;
        margin-top: 1px;
      }
    }

    .right {
      width: 14px;
      height: 24px;
      line-height: 24px;
      font-size: 14px;
      color: #C0C6CC;
    }
  }

  .content {
    padding: 17px 20px 15px;
    background-color: white;
    border-bottom: 1px solid #F1F1F1;
    text-align: left;
    font-size: 17px;

    .name {
      font-size: 17px;
      font-weight: 400;
      line-height: 24px;
      color: #0A0A0A;
    }
  }
}

.check-wechat {
  background-color: #F5F7FB;

  .row {
    background-color: #F5F7FB;
  }
}
</style>
