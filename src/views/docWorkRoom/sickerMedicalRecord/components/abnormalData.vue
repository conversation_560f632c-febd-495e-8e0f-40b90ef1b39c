<template>
  <div :class="`check check-${platform}`">
    <div class="row" @click.stop="handleClick">
      <div class="left">
        <img :src="isApp ? warn : wx_warn" alt="">
        <div>{{ abnormalRoleData.name ? abnormalRoleData.name : '异常情况' }}</div>
      </div>

      <div class="right alignRight">
        <van-icon v-if="extend" name="arrow-up"></van-icon>
        <van-icon v-else name="arrow-down"></van-icon>
      </div>
    </div>
    <div v-if="extend">
      <div @click="handleBtnClick" class="condition">
        <div class="condition-item" :class="{ active: abnormal_source == '0'}" data-value="0">全部</div>
        <div class="condition-item" :class="{ active: abnormal_source == '1'}" data-value="1">院内</div>
        <div class="condition-item" :class="{ active: abnormal_source == '2'}" data-value="2">家庭</div>
      </div>
      <div 
        v-for="item in abnormalData"
        :key="item.id"
        class="content"
        @click="handleJump(item)"
      >
        <div class="information">
          <div class="name">
            {{ item.name }}
            <span v-if="item.measure_type_name" class="sub_name">({{ item.measure_type_name }})</span>
          </div>
          <div class="value-unit">
            <div class="value">
              <div v-if="item.code == 'sbp' || item.code == 'dbp'" class="special-flex">
                <div class="flex">
                  <abnormalIndicator
                    indicatorsource="patientList"
                    :code="item.abnormal_info[0].code"
                    :record="item.abnormal_info[0]"
                    :codeValue="item.abnormal_info[0].value"
                    :isShowDoc="false"
                  />
                  <span v-if="item.abnormal_info.length > 1"> &nbsp;/ &nbsp;</span>
                  <abnormalIndicator
                    v-if="item.abnormal_info.length > 1"
                    indicatorsource="patientList"
                    :code="item.abnormal_info[1].code"
                    :record="item.abnormal_info[1]"
                    :codeValue="item.abnormal_info[1].value"
                    :isShowDoc="false"
                  />
                </div>
                <bp-status :record="item"></bp-status>
              </div>
              <abnormalIndicator
                v-else
                indicatorsource="patientList"
                :code="item.abnormal_info[0].code"
                :record="item.abnormal_info[0]"
                :codeValue="item.abnormal_info[0].value"
              />
              
            </div>
            <div class="unit">{{ item.unit }}</div>
          </div>
          <div class="measure-at">{{ item.measure_at }}</div>
        </div>
        <div class="abn">
          <span class="abn_cnt">{{ item.abn_cnt }}</span>
          <span class="abn_cnt_unit">次</span>
          <van-icon class="van-icon-right" name="arrow" />
        </div>
      </div>
    </div>
    <empty v-if="!abnormalData.length && extend" :isRecord="true"></empty>
  </div>
</template>

<script>
import moment from 'moment'
import empty from './empty.vue'
import warn from '../../imgs/warn1.png'
import wx_warn from '../../imgs/wx_warn.png'
import abnormalIndicator from './abnormalIndicator.vue'
import bpStatus from './bpStatus.vue'
import wx from "weixin-js-sdk"
export default {
  components: {
    empty,
    abnormalIndicator,
    bpStatus
  },
  props: {
    abnormalData: {
      type: Array,
      default: () => ([])
    },
    abnormalRoleData: {
      type: Object,
      default: () => ({name: '异常情况'})
    }
  },
  computed: {
    platform() {
      return this.$store.getters['docWorkRoom/platform']
    },
    isApp() {
      return this.$store.getters['docWorkRoom/isApp']
    },
    navigatorInfo() {
      return this.$store.getters['docWorkRoom/navigatorInfo']
    },
  },
  data () {
    return {
      warn,
      wx_warn,
      extend: true,
      abnormal_source: '0'
    }
  },
  methods: {
    handleClick () {
      this.extend = !this.extend
    },
    momentFormat(time) {
      return moment(time).format('YYYY-MM-DD')
    },
    handleJump ({ measure_type, indicator_id, indicator_group_id, measure_type_name, name}) {
      const { patient_id, project_id } = this.$route.query
      let tempTitle = measure_type_name ? '(' + measure_type_name + ')' : ''
      let title = name + tempTitle + '异常情况'
      const params = `/docWorkRoom/sickerMedicalRecord/abnormal?project_id=${project_id}&patient_id=${patient_id}&measure_type=${measure_type}&indicator_id=${indicator_id}&indicator_group_id=${indicator_group_id}&title=${encodeURI(title)}`
      if (process.env.NODE_ENV === 'production') {
        // 暴风 智众健康助手App
        let isFrom = localStorage.getItem('isFrom')
        if(isFrom && isFrom === 'zzApp'){
          this.$router.push(params)
          return
        }
        if (!this.isApp) {
          wx.miniProgram.navigateTo({
            url: `/pages/my/pages/webView/webView?url=${encodeURIComponent(location.origin + params)}`,
          })
        } else {
          if (this.navigatorInfo.isAndroid) {
            window.android.breathMessage(params)
          }
          if (this.navigatorInfo.isIOS) {
            window.webkit.messageHandlers.breathMessage.postMessage(params)
          }
        }
      } else {
        this.$router.push(params)
      }
    },
    handleBtnClick (e) {
      if (e.target.dataset.value && e.target.dataset.value != this.abnormal_source) {
        this.abnormal_source = e.target.dataset.value
        this.$emit('getAbnormalData', e.target.dataset.value)
      } 
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../common.scss";
.check {
  background-color: #fff;

  .row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: $app-bg-color;
    padding: 16px 20px;

    .left {
      display: flex;
      justify-content: flex-start;
      color: #0A0A0A;
      font-size: 17px;
      font-style: normal;
      font-weight: 400;
      line-height: 24px;
      img{
        display: block;
        width: 19px;
        height: 19px;
        margin-right: 8px;
        margin-top: 1px;
      }
    }

    .right {
      white-space: nowrap;
      width: 97px;
      font-size: 14px;
      color: #C0C6CC;
      display: flex;
      justify-content: space-between;
      align-items: center;

    }

    .alignRight{
      justify-content: flex-end;
    }
  }

  .condition {
    display: flex;
    padding: 12px 15px;
    .condition-item {
      margin-right: 12px;
      padding: 8px 16px;
      color: #5A6266;
      background-color: #EEEEEE;
      font-size: 15px;
      border-radius: 28px;
    }
    .active {
      color: white;
      background-color: #FD9856;
    }
  }

  .content {
    padding: 17px 20px 15px;
    background-color: white;
    border-bottom: 1px solid #F1F1F1;
    text-align: left;
    font-size: 17px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .information{
      .name {
        font-size: 17px;
        font-weight: 400;
        line-height: 24px;
        color: #0A0A0A;
        .sub_name {
          color: #878F99;
        }
      }
      .value-unit{
        display: flex;
        align-items: baseline;
        margin-top: 6px;
        .value{
          display: inline;
          font-size: 20px;
          font-style: normal;
          font-weight: 400;
          line-height: normal;
          color: #0A0A0A;
        }
        .value ::v-deep .abnormal-indicator-box-value {
          display: inline;
          font-size: 20px;
          font-style: normal;
          font-weight: 400;
          line-height: normal;
          //color: #0A0A0A;
        }
        
        .unit {
          display: inline;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          line-height: normal;
          color: #878F99;
          margin-left: 11px;
        }
      }
    }
    .abn {
      .abn_cnt {
        font-size: 20px;
        color: #5A6266;
        margin-right: 4px;
      }
      .abn_cnt_unit {
        font-size: 12px;
        color: #878F99;
        margin-right: 4px;
      }
      .van-icon-right {
        font-size: 14px;
        color: #C0C6CC;
      }
    }
    

    .measure-at{
      min-width: 105px;
      margin-top: 4px;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
      color: #5A6266;
    }
    .flex {
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
    }
    .special-flex {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }
  }
}

.check-wechat{
  .row {
    background-color: $wx-bg-color;
    .trend{
      color: $wx-color;
      border: 1px solid $wx-color;
    }
  }
  .condition {
    .active {
      color: white;
      background-color: #008AFF;
    }
  }
}
</style>