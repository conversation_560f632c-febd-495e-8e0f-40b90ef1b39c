<template>
  <div v-if="statusValue && statusValue.status_display_group" class="newindicator-tip">
    <div 
      :class="`newindicator-tip-${statusValue && statusValue.deflection_group}`">
      {{ statusValue && statusValue.status_display_group }}
    </div> 
  </div>
</template>

<script>
export default {
  props: {
    record: {
      type: Object,
      default: () => ({})
    },
    flag: {
      type: String,
      default: 'abnormal_info'
    }
  },
  computed: {
    statusValue() {
      return this.record && this.record[this.flag] && this.record[this.flag].find(item => item.code == 'sbp' || item.code == 'dbp')
    }
  }, 
  data () {
    return {}
  }
}
</script>

<style lang="scss" scoped>
.newindicator-tip{
  width: 100%;
  color: #303133;
  font-size: 12px;
  line-height: 18px;
  text-align: center;
  display: flex;
  justify-content: center;
  margin-top: 2px;
  cursor: pointer;
  div{
    font-size: 12px;
    border-radius: 20px;
    padding: 0 6px;
    transform: scale(0.95);
  }
}
.newindicator-tip-1, .newindicator-tip-3 {
  border: 1px solid #A0C8FF;
  background: #ECF4FF;
  color: #377CFB;
}
.newindicator-tip-2, .newindicator-tip-4 {
  border: 1px solid #FFB1B3;
  background: #FFEFF0;
  color: #F5242D;
}
</style>