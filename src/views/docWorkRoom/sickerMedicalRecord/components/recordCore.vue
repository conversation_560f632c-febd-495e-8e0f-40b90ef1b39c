<template>
  <div :class="`check check-${platform}`">
    <div class="row" @click.stop="handleClick">
      <div class="left">
        <img :src="isApp? activity : wx_activity" alt="">
        <div>{{coreRoleData.name}}</div>
      </div>

      <div class="right" :class="{alignRight: !coreData.length}">
        <div class="trend" @click.stop="trendClick(coreRoleData)" v-show="coreData.length">数据趋势</div>
        <van-icon v-if="extend" name="arrow-up"/>
        <van-icon v-else name="arrow-down"/>
      </div>
    </div>
    <div v-if="extend">
      <!-- 时间筛选 -->
      <dropdown-menu
        :active-color="platformColor"
        :close-on-click-outside="false"
      >
        <dropdown-item :title="timeTitle" @open="dropdownOpen" ref="dropdown-time">
          <div class="time-content">
            <div v-for="item in cycleList" :key="item.value"
                class="time-name"
                :class="{'time-act': time == item.value}"
                @click="timeClick(item)"
            >
              <span>{{item.name}}</span>
            </div>
            <div class="time-custom" :class="{ 'time-custom-color': start_date != '' }" @click="showcCalendar = true">
              <div class="time-custom-input">{{start_date || '请选择开始时间'}}</div>
              <div class="time-custom-line"></div>
              <div class="time-custom-input">{{end_date || '请选择结束时间'}}</div>
            </div>
          </div>
          <div class="project-btn">
            <div class="reset" @click="resetClick()">重置</div>
            <div class="submit" @click="submitClick()">提交</div>
          </div>
        </dropdown-item>
      </dropdown-menu>
      <van-calendar
        class="search-record-calendar"
        :color="platformColor"
        v-model="showcCalendar"
        type="range"
        :max-range="365"
        :min-date="minDate"
        :max-date="maxDate"
        :allow-same-day="true"
        @confirm="calendarConfirm">
      </van-calendar>
      <div
        v-for="childItem in coreData"
        :key="childItem.id"
        class="content">
        <div class="information">
          <div class="name">{{ childItem.name }}</div>
          <div class="value-unit">
            <div class="value">
              <div 
                v-if="childItem.key == 'sbp' || childItem.key == 'dbp'" 
                class="special-flex">
                <div class="flex">
                  <div 
                    v-for="(item, index) in childItem.value_abnormal_info" 
                    :key="index" 
                    class="flex"
                    >
                    <abnormal-indicator
                      indicatorsource="patientList"
                      :isShowDoc="false"
                      :code="item.code"
                      :record="item"
                      :codeValue="item.value"
                      >
                    </abnormal-indicator> 
                    <span v-if="index != childItem.value_abnormal_info.length - 1"> &nbsp;/ &nbsp;</span>
                  </div>
                </div>
                <bp-status :record="childItem" flag="value_abnormal_info"></bp-status>
              </div>
              <div v-else class="flex">
                <abnormal-indicator
                    :code="childItem.key"
                    :record="childItem"
                    :codeValue="childItem.value"
                    flag="value_abnormal_info"
                    >
                  </abnormal-indicator> 
              </div>
            </div>
            <div class="unit">{{ childItem.unit }}</div>
          </div>
        </div>
        <div class="measure-at">{{ momentFormat(childItem.measure_at) }}</div>
      </div>
    </div>
    <empty v-if="!coreData.length && extend" :isRecord="true" />
  </div>
</template>

<script>
import moment from 'moment'
import empty from './empty.vue'
import activity from '../../imgs/activity.png'
import wx_activity from '../../imgs/wx_activity.png'
import wx from "weixin-js-sdk"
import abnormalIndicator from './abnormalIndicator.vue'
import bpStatus from './bpStatus.vue'
import { DropdownMenu, DropdownItem } from 'vant';
export default {
  components: {
    empty,
    abnormalIndicator,
    bpStatus,
    'dropdown-menu': DropdownMenu,
    'dropdown-item': DropdownItem
  },
  props: {
    coreData: {
      type: Array,
      default() {
        return []
      }
    },
    coreRoleData: {
      type: Object,
      default() {
        return {
          name: ''
        }
      }
    }
  },
  computed: {
    platform() {
      return this.$store.getters['docWorkRoom/platform']
    },
    isApp() {
      return this.$store.getters['docWorkRoom/isApp']
    },
    navigatorInfo() {
      return this.$store.getters['docWorkRoom/navigatorInfo']
    },
    platformColor() {
      return this.$store.getters['docWorkRoom/platformColor']
    },
    searchInfo() {
      return this.$store.getters['docWorkRoom/searchInfo']
    },
  },
  data() {
    return {
      activity,
      wx_activity,
      extend: true,
      timeTitle: '时间筛选',
      isSubmit: false,
      time: '3m',
      start_date: '',
      end_date: '',
      showcCalendar: false,
      minDate: new Date(),
      maxDate: new Date(),
      cycleList: [
        { value: '1', name: '一周' },
        { value: '2', name: '两周' },
        { value: '3', name: '一个月'},
        { value: '3m', name: '三个月'},
        { value: '6m', name: '半年'},
        { value: '1y', name: '一年'}
      ],
    }
  },
  created() {
    const nextTime = moment(new Date()).add('year',1).format("YYYY-MM-DD").split('-')
    this.maxDate = new Date(nextTime[0]*1, 12, 31)
    this.minDate = new Date(1970, 1, 1)
  },
  mounted() {
    this.setTime(this.isApp)
  },
  methods: {
    handleClick() {
      this.extend = !this.extend
    },
    momentFormat(time) {
      return moment(time).format('YYYY-MM-DD')
    },
    trendClick({ name, indicator_group_id = 1, code }) {
      if (!this.coreData.length) return
      const { patient_id, project_id } = this.$route.query
      let params = `/docWorkRoom/sickerMedicalRecord/chart?patient_id=${patient_id}&project_id=${project_id}&code=${code}&title=${encodeURI(name)}&indicator_group_id=${indicator_group_id}`
      if (process.env.NODE_ENV === 'production') {
        // 暴风 智众健康助手App
        let isFrom = localStorage.getItem('isFrom')
        if(isFrom && isFrom === 'zzApp'){
          this.$router.push(params)
          return
        }
        if (!this.isApp) {
          wx.miniProgram.navigateTo({
            url: `/pages/my/pages/webView2/webView2?url=${encodeURIComponent(location.origin + params)}`,
          })
        } else {
          if (this.navigatorInfo.isAndroid) {
            window.android.breathMessage(params)
          }
          if (this.navigatorInfo.isIOS) {
            window.webkit.messageHandlers.breathMessage.postMessage(params)
          }
        }
      } else {
        this.$router.push(params)
      }
    },
    dropdownOpen() {
      this.isSubmit = true
      this.time = this.searchInfo.time
      if (!this.time) {
        this.start_date = this.searchInfo.start_date
        this.end_date = this.searchInfo.end_date
      } else {
        this.start_date = ''
        this.end_date = ''
      }
    },
    resetClick() {
      this.timeClick({ value: '3m' })
      this.setTime()
    },
    timeClick({ value }) {
      this.time = value
      this.start_date = ''
      this.end_date = ''
    },
    calendarConfirm(date) {
      const [start, end] = date;
      this.time = ''
      this.start_date = moment(start).format('YYYY-MM-DD')
      this.end_date = moment(end).format('YYYY-MM-DD')
      this.showcCalendar = false
    },
    submitClick() {
      this.setTime()
    },
    setTime(isSearch = true) {
      let start_date = ''
      let end_date = ''
      if (this.time) {
        const { startTime, endTime } = this.getFollowupTime(this.time)
        start_date = startTime
        end_date = endTime
      } else {
        start_date = `${this.start_date.slice(0, 4)}-${this.start_date.slice(5, 7)}-${this.start_date.slice(8, 10)}`
        end_date = `${this.end_date.slice(0, 4)}-${this.end_date.slice(5, 7)}-${this.end_date.slice(8, 10)}`
      }
      this.$store.dispatch('docWorkRoom/setSearchInfo', {
        time: this.time,
        start_date,
        end_date
      })
      this.isSubmit = false
      this.$refs['dropdown-time'].toggle(false);
      let title = '时间筛选'
      if (this.time) {
        title = this.cycleList.filter(v => v.value == this.time)[0].name
      } else {
        title = `${this.start_date}-${this.end_date}`
      }
      this.timeTitle = title
      if (isSearch) this.$emit('getCoreData')
    },
    getFollowupTime(type) {
      return {
        '1': { // 一周
          startTime: moment().subtract(7, 'days').format('YYYY-MM-DD'),
          endTime: moment().format('YYYY-MM-DD'),
        },
        '2': { // 两周
          startTime: moment().subtract(14, 'days').format('YYYY-MM-DD'),
          endTime: moment().format('YYYY-MM-DD'),
        },
        '3': { // 一个月
          startTime: moment().subtract(30, 'days').format('YYYY-MM-DD'),
          endTime: moment().format('YYYY-MM-DD'),
        },
        '3m': { // 近90天
          startTime: moment().subtract(90, 'days').format('YYYY-MM-DD'),
          endTime: moment().format('YYYY-MM-DD'),
        },
        '6m':{ // 近半年
          startTime: moment().subtract(180, 'days').format('YYYY-MM-DD'),
          endTime: moment().format('YYYY-MM-DD'),
        },
        '1y': { // 近1年
          startTime: moment().subtract(365, 'days').format('YYYY-MM-DD'),
          endTime: moment().format('YYYY-MM-DD'),
        },
        '2y':{ // 近2年
          startTime: moment().subtract(730, 'days').format('YYYY-MM-DD'),
          endTime: moment().format('YYYY-MM-DD'),
        },
        '3y':{ // 近3年
          startTime: moment().subtract(1095, 'days').format('YYYY-MM-DD'),
          endTime: moment().format('YYYY-MM-DD'),
        },
      }[type]
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../common.scss";
.check {
  background-color: #fff;

  .row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: $app-bg-color;
    padding: 16px 20px;

    .left {
      display: flex;
      justify-content: flex-start;
      color: #0A0A0A;
      font-size: 17px;
      font-style: normal;
      font-weight: 400;
      line-height: 24px;
      img{
        display: block;
        width: 19px;
        height: 19px;
        margin-right: 8px;
        margin-top: 1px;
      }
    }

    .trend{
      font-size: 12px;
      font-weight: 400;
      padding: 0 8px 0;
      color: $app-color;
      border-radius: 6px;
      border: 1px solid $app-color;
      margin-right: 17px;
      height: 24px;
      line-height: 25.5px;
    }

    .right {
      white-space: nowrap;
      width: 97px;
      font-size: 14px;
      color: #C0C6CC;
      display: flex;
      justify-content: space-between;
      align-items: center;

    }

    .alignRight{
      justify-content: flex-end;
    }
  }

  .content {
    padding: 17px 20px 15px;
    background-color: white;
    border-bottom: 1px solid #F1F1F1;
    text-align: left;
    font-size: 17px;
    display: flex;
    justify-content: space-between;

    .information{
      .name {
        font-size: 17px;
        font-weight: 400;
        line-height: 24px;
        color: #0A0A0A;
      }
      .value-unit{
        display: flex;
        align-items: baseline;
        .value{
          display: inline;
          font-size: 20px;
          font-style: normal;
          font-weight: 400;
          line-height: normal;
          color: #0A0A0A;
        }
        .value ::v-deep .abnormal-indicator-box-value {
          display: inline;
          font-size: 20px;
          font-style: normal;
          font-weight: 400;
          line-height: normal;
          //color: #0A0A0A;
        }
        
        .unit {
          display: inline;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          line-height: normal;
          color: #878F99;
          margin-left: 11px;
        }
      }
    }

    .measure-at{
      min-width: 105px;
      text-align: right;
      margin-top: 4px;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
      color: #5A6266;
    }
    .flex {
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
    }
    .special-flex {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }
  }

  ::v-deep .van-dropdown-menu__bar{
    padding: 0 10px;
    height: 40px;
    box-shadow: initial;
    .van-dropdown-menu__item{
      padding: 0 10px;
    }
    .van-dropdown-menu__title--active{
      color: #0A0A0A !important;
    }
    .van-dropdown-menu__title{
      color: $app-color;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
      &::after {
        border-color: transparent transparent #5a6266 #5a6266;
      }
    }
  }
  ::v-deep .van-dropdown-item__content{
    background: #FFF;
    overflow: hidden;
  }
  .time-content {
    height: 144px;
    margin: 0 20px 0 10px;

    .time-name {
      display: inline-block;
      width: 105px;
      height: 32px;
      line-height: 32px;
      border-radius: 6px;
      background: #F5F6FB;
      margin: 13px 0 0 10px;
      text-align: center;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      color: #5A6266;
    }

    .time-act {
      background: #F5F6FB;
      color: $app-color;
    }

    .time-custom{
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 12px 0 0 10px;
      .time-custom-input{
        width: 150px;
        height: 32px;
        line-height: 34px;
        flex-shrink: 0;
        border-radius: 6px;
        background: #F5F6FB;
        text-align: center;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        color: #C0C6CC;
      }
      .time-custom-line{
        width: 11px;
        height: 2px;
        background: #E5E5E5;
      }
    }
    .time-custom-color{
      .time-custom-input{
        color: $app-color;
      }
    }
  }
  .project-btn{
    padding: 20px;
    display: flex;
    justify-content: space-between;
    .reset{
      width: 162px;
      height: 44px;
      flex-shrink: 0;
      border-radius: 8px;
      background: $app-bg-color;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 44px;
      color: #0A0A0A;
    }
    .submit{
      width: 162px;
      height: 44px;
      flex-shrink: 0;
      border-radius: 8px;
      background: $app-color;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 44px;
      color: #fff;
    }
  }
  .search-record-calendar{
    ::v-deep .van-button--round{
      border-radius: 8px;
    }
    ::v-deep .van-icon{
      position: absolute;
      font-size: 22px;
    }
  }
}

.check-wechat{
  .row {
    background-color: $wx-bg-color;
    .trend{
      color: $wx-color;
      border: 1px solid $wx-color;
    }
  }
  ::v-deep .van-dropdown-menu__bar{
    .van-dropdown-menu__title{
      color: $wx-color;
    }
  }
  .project-btn{
    .reset{
      background: $wx-bg-color;
    }
    .submit{
      background: $wx-color;
    }
  }
  .time-content {
    .time-act {
      color: $wx-color;
    }
    .time-custom-color{
      .time-custom-input{
        color:$wx-color;
      }
    }
  }
}
</style>
