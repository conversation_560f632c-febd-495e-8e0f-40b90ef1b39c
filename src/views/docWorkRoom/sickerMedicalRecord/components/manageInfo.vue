<template>
  <div :class="`manage-info manage-info-${platform}`">
    <div class="manage-info-main">
      <div class="project-name">管理方案：<span>{{mananeInfo.manage_plan_name|| '--'}}</span></div>
      <div class="level-name" v-if="mananeInfo.this_time_visit_level_name">
        {{mananeInfo.this_time_visit_level_name}}<div class="current">当前访视</div>
      </div>
      <div class="time" :class="{mt12: !mananeInfo.this_time_visit_level_name}">加入项目：{{mananeInfo.join_time || '--'}}</div>
      <div class="time time-visit">上次就诊：{{mananeInfo.latest_visit_record_at || '--'}}</div>
      <div class="time">应随访时间：{{mananeInfo.this_time_visit_level_start_date || '--'}}</div>
    </div>
  </div>
</template>

<script>
import project_act from '../../imgs/project_act.png'

export default {
  name: "manageInfo",
  props: {
    mananeInfo: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      project_act,
    }
  },
  computed: {
    platform() {
      return this.$store.getters['docWorkRoom/platform']
    },
    platformColor() {
      return this.$store.getters['docWorkRoom/platformColor']
    },
  },
  created() {
  },
  mounted() {
  },
  methods: {
  }
}
</script>

<style lang="scss" scoped>
@import "../common.scss";

.manage-info{
  background: #EDF0F2;
  padding: 15px;

  .manage-info-main{
    width: 315px;
    padding: 18px 15px 21px;
    border-radius: 8px;
    background: #FFF;
    text-align: left;

    .mt12{
      margin-top: 12px;
    }

    .project-name{
      line-height: 24px;
      font-size: 17px;
      font-style: normal;
      font-weight: 400;
      color: #0A0A0A;
    }
    .level-name{
      width: 100%;
      min-height: 24px;
      margin: 8px 0 16px;
      font-size: 16px;
      font-style: normal;
      font-weight: 500;
      line-height: 24px;
      color: $app-color;
      display: flex;
      justify-content: flex-start;
      align-items: center;

      .current{
        width: 64px;
        text-align: center;
        display: inline-block;
        vertical-align: middle;
        color: #FFF;
        font-size: 12px;
        font-style: normal;
        font-weight: 500;
        height: 20px;
        line-height: 22px;
        border-radius: 23px;
        background: $app-color;
        margin: 0 0 2px 8px;
      }
    }
    .time{
      font-size: 15px;
      font-style: normal;
      font-weight: 400;
      color: #3F4447;
      height: 21px;
      line-height: 21px;
      margin-left: 10px;
      position: relative;
      &::after{
        content: '';
        position: absolute;
        top: 5px;
        left: -10px;
        width: 2px;
        height: 8px;
        flex-shrink: 0;
        border-radius: 34px;
        background: $app-color;
      }
    }
    .time-visit{
      margin: 12px 0 12px 10px;
    }
  }



  ::v-deep .van-dropdown-menu__bar{

  }
}

.manage-info-wechat{
  .project-content{
    .project-name{
      float: initial;
      height: 50px;
      line-height: 50px;
      padding: 0 12px;
      border-radius: 0;
      background: initial;
      margin: 13px 0 0 10px;
      font-size: 15px;
      font-style: normal;
      font-weight: 400;
      color: #0A0A0A;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #F4F4F4;
    }
    .project-act{
      color: $wx-color;
    }
  }
  .project-btn{
    .submit{
      background: $wx-color;
    }
  }
  .time-content {
    .time-act {
      color: $wx-color;
    }
  }
}

</style>
