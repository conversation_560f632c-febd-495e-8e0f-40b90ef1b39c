<template>
    <div class="empty" :class="{ 
            'common-wechat': $store.state.docWorkRoom.platform ? true : false, 
            'empty-record': isRecord ,
            'empty-chart': chartEmpty
            }">
        <img
            :src="emptyImg"
            class="img"
            alt="empty">
        <span>暂无数据</span>
    </div>
</template>

<script>
import emptyImg from '@/assets/empty.png'
export default {
    props: {
        isRecord: {
            type: Boolean,
            default: false
        },
        chartEmpty: {
            type: Boolean,
            default: false
        }
    },
    data () {
        return {
            emptyImg: emptyImg
        }
    },
}
</script>

<style lang="scss" scoped>
.empty {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: #F8F8F8;
    height: 100vh;
    font-size: 15px;
    color: #5A6266;
    .img {
        width: 232px;
        height: 125px;
        margin-bottom: 29px;
    }
}
.empty-chart {
    height: calc(100vh - 57px);
}
.empty-record{
   height: 225px;
}
.empty-wechat {
    background-color: #F5F7FB;
}
@media screen and (orientation: landscape) {
    .empty {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        background: #F8F8F8;
        height: 100vh;
        font-size: 7.5px;
        color: #5A6266;
        .img {
            width: 116px;
            height: 62.5px;
            margin-bottom: 14.5px;
        }
    }
    .empty-chart {
        height: calc(100vh - 25px);
        margin-top: 8px;
    }
    .empty-record{
        height: 225px;
    }
    .empty-wechat {
        background-color: #F5F7FB;
    }
}

</style>

