<template>
  <div>
    <div :class="`check check-${platform}`" v-for="item in menuList" :key="item.code">
      <div class="row" @click.stop="handleClick(item)">
        <div class="left">
          <img :src="isApp ? item.appimg : item.img" alt="">
          {{ item.name }}
        </div>
        <div class="right" :class="{ 'right-icon': !item.isShowTrend }">
          <div class="trend" v-if="item.isShowTrend" @click.stop="trendClick(item)">数据趋势</div>
          <van-icon name="arrow" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import wx from "weixin-js-sdk"
export default {
  name: 'recordCheck',
  props: {
    menuList: {
      type: Array,
      default() {
        return []
      }
    }
  },
  computed: {
    platform() {
      return this.$store.getters['docWorkRoom/platform']
    },
    isApp() {
      return this.$store.getters['docWorkRoom/isApp']
    },
    navigatorInfo() {
      return this.$store.getters['docWorkRoom/navigatorInfo']
    },
  },
  data() {
    return {
    }
  },
  created() {
  },
  methods: {
    trendClick({ name, indicator_group_id = 1, code }) {
      console.log('1')
      const { patient_id, project_id } = this.$route.query
      let params = `/docWorkRoom/sickerMedicalRecord/chart?patient_id=${patient_id}&project_id=${project_id}&code=${code}&title=${encodeURI(name)}&indicator_group_id=${indicator_group_id}`
      if (process.env.NODE_ENV === 'production') {
        // 暴风 智众健康助手App
        let isFrom = localStorage.getItem('isFrom')
        if(isFrom && isFrom === 'zzApp'){
          this.$router.push(params)
          return
        }
        if (!this.isApp) {
          wx.miniProgram.navigateTo({
            // url: `/pages/my/pages/webView2/webView2?url=${location.origin}${params.replace('?', '&')}`,
            url: `/pages/my/pages/webView2/webView2?url=${encodeURIComponent(location.origin + params)}`,
          })
        } else {
          if (this.navigatorInfo.isAndroid) {
            window.android.breathMessage(params)
          }
          if (this.navigatorInfo.isIOS) {
            window.webkit.messageHandlers.breathMessage.postMessage(params)
          }
        }
      } else {
        this.$router.push(params)
      }
    },
    handleClick({ name, indicator_group_id = 1, code }) {



      const { patient_id, project_id } = this.$route.query
      let params = `/docWorkRoom/sickerMedicalRecord/commonMenu?patient_id=${patient_id}&project_id=${project_id}&code=${code}&title=${encodeURI(name)}&indicator_group_id=${indicator_group_id}`

      if (code === 'assessment') {
        params = `/docWorkRoom/sickerMedicalRecord/assessment?patient_id=${patient_id}&project_id=${project_id}&code=${code}&title=${encodeURI(name)}`
      }


      if (process.env.NODE_ENV === 'production') {
        // 暴风 智众健康助手App
        let isFrom = localStorage.getItem('isFrom')
        if(isFrom && isFrom === 'zzApp'){
          this.$router.push(params)
          return
        }
        if (!this.isApp) {
          // this.$router.push(params)
          wx.miniProgram.navigateTo({
            // url: `/pages/my/pages/webView2/webView2?url=${location.origin}${params.replace('?', '&')}`,
            url: `/pages/my/pages/webView/webView?url=${encodeURIComponent(location.origin + params)}`,
          })
        } else {
          if (this.navigatorInfo.isAndroid) {
            window.android.breathMessage(params)
          }
          if (this.navigatorInfo.isIOS) {
            window.webkit.messageHandlers.breathMessage.postMessage(params)
          }
        }
      } else {
        this.$router.push(params)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../common.scss";
.check {
  background-color: #fff;

  .row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: $app-bg-color;
    padding: 16px 20px;

    .left {
      display: flex;
      justify-content: flex-start;
      color: #0A0A0A;
      font-size: 17px;
      font-style: normal;
      font-weight: 400;
      line-height: 24px;
      img{
        display: block;
        width: 19px;
        height: 19px;
        margin-right: 8px;
        margin-top: 1px;
      }
    }

    .trend{
      font-size: 12px;
      font-weight: 400;
      padding: 0 8px;
      color: $app-color;
      border-radius: 6px;
      border: 1px solid  $app-color;
      margin-right: 17px;
      height: 24px;
      line-height: 25.5px;
    }

    .right {
      white-space: nowrap;
      width: 97px;
      font-size: 14px;
      color: #C0C6CC;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .right-icon{
      width: 14px;
    }
  }

}
.check-wechat{

  .row {
    background-color: $wx-bg-color;
    .trend{
      color: $wx-color;
      border: 1px solid $wx-color;
    }
  }
}
</style>
