<template>
  <div :class="`platform platform-${platform}`">
    <dropdown-menu
      :active-color="platformColor"
      :close-on-click-outside="false"
    >
      <dropdown-item :title="projectTitle" :options="[]" disabled v-if="isApp" :title-class="isApp ? 'hideIcon' : ''">
      </dropdown-item>

      <dropdown-item :title="projectTitle" @open="dropdownOpen('1')" ref="dropdown-project" v-if="!isApp">
        <div class="project-content">
          <div v-for="item in projects" :key="item.id"
               class="project-name"
               :class="{'project-act': sel_project_id == item.value}"
               @click="projectClick(item)"
          >
            <div class="text">{{item.text}}</div>
            <div class="img">
              <img :src="project_act" alt="" v-show="sel_project_id == item.value && !isApp ">
            </div>
          </div>
        </div>
        <div class="project-btn">
          <div class="reset" @click="resetClick()">重置</div>
          <div class="submit" @click="submitClick()">提交</div>
        </div>
      </dropdown-item>

      <!-- <dropdown-item :title="timeTitle" @open="dropdownOpen('2')" ref="dropdown-time">
        <div class="time-content">
          <div v-for="item in cycleList" :key="item.value"
               class="time-name"
               :class="{'time-act': time == item.value}"
               @click="timeClick(item)"
          >
            <span>{{item.name}}</span>
          </div>
          <div class="time-custom" :class="{ 'time-custom-color': start_date != '' }" @click="showcCalendar = true">
            <div class="time-custom-input">{{start_date || '请选择开始时间'}}</div>
            <div class="time-custom-line"></div>
            <div class="time-custom-input">{{end_date || '请选择结束时间'}}</div>
          </div>
        </div>
        <div class="project-btn">
          <div class="reset" @click="resetClick()">重置</div>
          <div class="submit" @click="submitClick()">提交</div>
        </div>
      </dropdown-item> -->
    </dropdown-menu>
    <van-calendar
      class="search-record-calendar"
      :color="platformColor"
      v-model="showcCalendar"
      type="range"
      :max-range="365"
      :min-date="minDate"
      :max-date="maxDate"
      :allow-same-day="true"
      @confirm="calendarConfirm" ></van-calendar>
  </div>
</template>

<script>
import moment from 'moment'
import { DropdownMenu, DropdownItem, Toast, Calendar } from 'vant';
import {getAppDoctorDepartment, getDoctorDepartment} from '@/api/saas.js'
import project_act from '../../imgs/project_act.png'

export default {
  name: "searchRecord",
  components: {
    'dropdown-menu': DropdownMenu,
    'dropdown-item': DropdownItem
  },
  data() {
    return {
      project_act,
      loading: true,
      isSubmit: false,
      showcCalendar: false,
      dropdownTab: '',
      minDate: new Date(),
      maxDate: new Date(),
      start_date: '',
      end_date: '',
      project_id: '',
      sel_project_id: '',
      time: '3m',
      projectTitle: '',
      timeTitle: '时间筛选',
      projects: [],
      cycleList: [
        { value: '1', name: '一周' },
        { value: '2', name: '两周' },
        { value: '3', name: '一个月'},
        { value: '3m', name: '三个月'},
        { value: '6m', name: '半年'},
        { value: '1y', name: '一年'}
        // 7天、15天、一个月（30天）、三个月（90天）、半年（180天）、一年（365天）
      ],
    }
  },
  computed: {
    platform() {
      return this.$store.getters['docWorkRoom/platform']
    },
    isApp() {
      return this.$store.getters['docWorkRoom/isApp']
    },
    platformColor() {
      return this.$store.getters['docWorkRoom/platformColor']
    },
    searchInfo() {
      return this.$store.getters['docWorkRoom/searchInfo']
    },
  },
  created() {
    const nextTime = moment(new Date()).add('year',1).format("YYYY-MM-DD").split('-')
    // const preTime = moment(new Date()).add('year',-1).format("YYYY-MM-DD").split('-')
    this.maxDate = new Date(nextTime[0]*1, 12, 31)
    this.minDate = new Date(1970, 1, 1)
  },
  mounted() {
    // this.setTime(this.isApp)
    this.getDoctorDepartment()
  },
  methods: {
    async getDoctorDepartment () {
      const toast1 = Toast.loading({
        message: '加载中...',
        forbidClick: true,
      });

      try {
        const { status, data } = await (this.isApp ? getAppDoctorDepartment : getDoctorDepartment)({
          patient_id: this.$route.query.patient_id,
        })
        if (status == 200) {
          this.sel_project_id = localStorage.getItem('saas_room_id')*1
          if (data && data.length) {
            data.map(v => {
              if (this.$route.query.project_id == v.project_id && this.isApp) {
                this.projectTitle = v.project_name
              }
              v.text = v.project_name || `${v.hosp_name}-${v.dept_name}`
              v.value = v.room_id
            })
            this.projects = data
          }
          if (!this.isApp) this.setProject()
          // this.$refs.dropdown.toggle(true);

        }
        toast1.clear();
      } catch (e) {
        toast1.clear();
      }
    },
    dropdownOpen(e) {
      this.isSubmit = true
      this.dropdownTab = e
      if (e == 1) {
        this.projectClick(this.projects.filter(v => v.value == this.project_id)[0])
      } else {
        this.time = this.searchInfo.time
        if (!this.time) {
          this.start_date = this.searchInfo.start_date
          this.end_date = this.searchInfo.end_date
        } else {
          this.start_date = ''
          this.end_date = ''
        }
      }
    },
    resetClick() {
      if (this.dropdownTab == '1') {
        this.projectClick(this.projects.filter(v => v.value == this.$route.query.room_id*1)[0])
        this.setProject()
      } else {
        this.timeClick({ value: '3m' })
        this.setTime()
      }
    },
    projectClick(item) {
      this.sel_project_id = item.room_id
    },
    timeClick({ value }) {
      this.time = value
      this.start_date = ''
      this.end_date = ''
    },
    calendarConfirm(date) {
      const [start, end] = date;
      this.time = ''
      this.start_date = moment(start).format('YYYY-MM-DD')
      this.end_date = moment(end).format('YYYY-MM-DD')
      this.showcCalendar = false
    },
    submitClick() {
      if (this.dropdownTab == '1') {
        this.setProject()
      } else if (this.dropdownTab == '2')  {
        this.setTime()
      }
    },
    setProject() {
      let name = '选择科室'
      this.project_id = this.sel_project_id
      if (this.projects.length) {
        let project = this.projects.filter(v => v.value == this.project_id)
        if (project && project.length) {
          name = project[0].project_name || `${project[0].hosp_name}-${project[0].dept_name}`
        } else {
          this.project_id = this.projects[0].value
          name = this.projects[0].project_name || `${this.projects[0].hosp_name}-${this.projects[0].dept_name}`
        }
      }
      this.projectTitle = name
      localStorage.setItem('saas_room_id', this.project_id)
      this.$emit('getCoreData')
      this.$emit('getAbnormalData')
      this.$refs['dropdown-project'].toggle(false);
    },
    setTime(isSearch = true) {
      let start_date = ''
      let end_date = ''
      if (this.time) {
        const { startTime, endTime } = this.getFollowupTime(this.time)
        start_date = startTime
        end_date = endTime
      } else {
        start_date = `${this.start_date.slice(0, 4)}-${this.start_date.slice(5, 7)}-${this.start_date.slice(8, 10)}`
        end_date = `${this.end_date.slice(0, 4)}-${this.end_date.slice(5, 7)}-${this.end_date.slice(8, 10)}`
      }
      this.$store.dispatch('docWorkRoom/setSearchInfo', {
        time: this.time,
        start_date,
        end_date
      })
      this.isSubmit = false
      this.$refs['dropdown-time'].toggle(false);
      let title = '时间筛选'
      if (this.time) {
        title = this.cycleList.filter(v => v.value == this.time)[0].name
      } else {
        title = `${this.start_date}-${this.end_date}`
      }
      this.timeTitle = title
      if (isSearch) this.$emit('getCoreData')
    },
    getFollowupTime(type) {
      return {
        '1': { // 一周
          startTime: moment().subtract(7, 'days').format('YYYY-MM-DD'),
          endTime: moment().format('YYYY-MM-DD'),
        },
        '2': { // 两周
          startTime: moment().subtract(14, 'days').format('YYYY-MM-DD'),
          endTime: moment().format('YYYY-MM-DD'),
        },
        '3': { // 一个月
          startTime: moment().subtract(30, 'days').format('YYYY-MM-DD'),
          endTime: moment().format('YYYY-MM-DD'),
        },
        '3m': { // 近90天
          startTime: moment().subtract(90, 'days').format('YYYY-MM-DD'),
          endTime: moment().format('YYYY-MM-DD'),
        },
        '6m':{ // 近半年
          startTime: moment().subtract(180, 'days').format('YYYY-MM-DD'),
          endTime: moment().format('YYYY-MM-DD'),
        },
        '1y': { // 近1年
          startTime: moment().subtract(365, 'days').format('YYYY-MM-DD'),
          endTime: moment().format('YYYY-MM-DD'),
        },
        '2y':{ // 近2年
          startTime: moment().subtract(730, 'days').format('YYYY-MM-DD'),
          endTime: moment().format('YYYY-MM-DD'),
        },
        '3y':{ // 近3年
          startTime: moment().subtract(1095, 'days').format('YYYY-MM-DD'),
          endTime: moment().format('YYYY-MM-DD'),
        },
      }[type]
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../common.scss";
.platform{
  ::v-deep .hideIcon{
    color: #5A6266 !important;
    &::after {
      opacity: 0;
      z-index: -1 !important;
    }
  }
  ::v-deep .van-dropdown-menu__bar{
    padding: 0 10px;
    height: 40px;
    box-shadow: initial;
    .van-dropdown-menu__item{
      padding: 0 10px;
    }
    .van-dropdown-menu__title--active{
      color: #0A0A0A !important;
    }
    .van-dropdown-menu__title{
      color: $app-color;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
      &::after {
        border-color: transparent transparent #5a6266 #5a6266;
      }
    }
    .van-dropdown-item__content{
      background: #FFF;
    }
  }
  .project-content{
    height: 212px;
    margin: 0 20px 0 10px;
    overflow-y: auto;
    clear: both;

    .project-name{
      float: left;
      min-height: 32px;
      line-height: 16px;
      padding: 0 12px;
      border-radius: 6px;
      background: $app-bg-color;
      margin: 13px 0 0 10px;
      text-align: center;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      color: #5A6266;

    }
    .project-act{
      background: #F5F6FB;
      color: $app-color;
    }
  }
  .time-content {
    height: 144px;
    margin: 0 20px 0 10px;

    .time-name {
      display: inline-block;
      width: 105px;
      height: 32px;
      line-height: 32px;
      border-radius: 6px;
      background: #F5F6FB;
      margin: 13px 0 0 10px;
      text-align: center;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      color: #5A6266;
    }

    .time-act {
      background: #F5F6FB;
      color: $app-color;
    }

    .time-custom{
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 12px 0 0 10px;
      .time-custom-input{
        width: 150px;
        height: 32px;
        line-height: 34px;
        flex-shrink: 0;
        border-radius: 6px;
        background: #F5F6FB;
        text-align: center;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        color: #C0C6CC;
      }
      .time-custom-line{
        width: 11px;
        height: 2px;
        background: #E5E5E5;
      }
    }
    .time-custom-color{
      .time-custom-input{
        color: $app-color;
      }
    }
  }
  .project-btn{
    padding: 20px;
    display: flex;
    justify-content: space-between;
    .reset{
      width: 162px;
      height: 44px;
      flex-shrink: 0;
      border-radius: 8px;
      background: $app-bg-color;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 44px;
      color: #0A0A0A;
    }
    .submit{
      width: 162px;
      height: 44px;
      flex-shrink: 0;
      border-radius: 8px;
      background: $app-color;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 44px;
      color: #fff;
    }
  }
  .search-record-calendar{
    ::v-deep .van-button--round{
      border-radius: 8px;
    }
    ::v-deep .van-icon{
      position: absolute;
      font-size: 22px;
    }
  }
}

.platform-wechat{
  ::v-deep .van-dropdown-menu__bar{
    .van-dropdown-menu__title{
      color: $wx-color;
    }
  }
  .project-content{
    .project-name{
      float: initial;
      padding: 13px 12px;
      border-radius: 0;
      background: inherit;
      margin: 0 0 0 10px;
      font-size: 15px;
      font-style: normal;
      font-weight: 400;
      color: #0A0A0A;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #F4F4F4;
      .text{
        flex: 1;
        line-height: 24px;
        text-align: left;
      }
      .img{
        width: 20px;
        height: 20px;
      }
    }
    .project-act{
      color: $wx-color;
    }
  }
  .project-btn{
    .reset{
      background: $wx-bg-color;
    }
    .submit{
      background: $wx-color;
    }
  }
  .time-content {
    .time-act {
      color: $wx-color;
    }
    .time-custom-color{
      .time-custom-input{
        color:$wx-color;
      }
    }
  }
}

</style>
