<!--查看报告页面-->
<template>
    <div class="report-page" :class="{ 'report-page-wechat': !$store.getters['docWorkRoom/isApp'] }">
        <!-- <iframe
            v-if="isPdf"
            class="report-image-iframe"
            width="100%"
            height="100%"
            wmode="Opaque"
            quality="high"
            :src="getSrc">
        </iframe> -->
        <div v-if="isPdf">
            <div class="pdf-title">
                <div class="left">{{ decodeURI($route.query.pageName) }}</div>
                <div class="right">
                    <span class="active">第{{pageNum}}页</span>/
                    <span class="unactive">共{{pageTotalNum}}页</span>
                </div>
            </div>
            <pdf
              ref="pdf"
                :src="getSrc"
                :page="pageNum"
                @num-pages="pageTotalNum=$event"
                @error="pdfError($event)"
                class="pdf-content"
                >
            </pdf>
            <div v-if="pageTotalNum > 1" class="btn-group">
                <div @click="prePage" class="btn">上一页</div>
                <div @click="nextPage" class="btn">下一页</div>
            </div>
        </div>

        <div v-else class="content">
            <img
                :src="getSrc"
                alt="报告图片"
                class="img"
                >
        </div>
    </div>
</template>

<script>
import pdf from 'vue-pdf'
import CMapReaderFactory from 'vue-pdf/src/CMapReaderFactory.js';
import { getFileUrl, getPwvFileUrl } from '@/utils/utils.js'
export default {
    components: {
        pdf
    },
    data () {
        return {
            fileName: {
                'pwv': 'PWV+ABI',
                'bloodsceen': '血管内皮',
                'fat': '内脏脂肪',
                'lung': '肺功能检测',
                'ecg': '心电图',
                'liver_hardness': '肝脏瞬时弹性硬度检测',
                'humancomponent': '人体成分分析',
                'nerveconduction': '神经传导',
            },
            pageNum: 1,
            pageTotalNum: 1,
        }
    },
    computed: {
        getFileFun () {
            return this.$route.query.pageType === 'pwv' ? getPwvFileUrl : getFileUrl
        },
        isPdf () {
            return this.$route.query.ext.toLocaleLowerCase() === 'pdf'
        },
        isApp() {
            return this.$store.getters['docWorkRoom/isApp']
        },
        getSrc () {
            const { ext, file_path, pageName, pageType } = this.$route.query
            console.log(this.getFileFun(ext, file_path, decodeURI(pageName) || this.fileName[pageType] || ''))
            let data = this.getFileFun(ext, file_path, decodeURI(pageName) || this.fileName[pageType] || '')
            if (this.isPdf) {
              data = pdf.createLoadingTask({
                url: data,
                CMapReaderFactory
              })
            }
            return data
        },
    },
    methods: {
        prePage() {
            let page = this.pageNum
            page = page > 1 ? page - 1 : this.pageTotalNum
            this.pageNum = page
        },
        nextPage() {
            let page = this.pageNum
            page = page < this.pageTotalNum ? page + 1 : 1
            this.pageNum = page
        },
        pdfError(error) {
            console.error(error)
        },
    },

}
</script>

<style lang="scss">
@import "../common.scss";
.report-page {
    height: 100vh;
    background: $app-bg-color;
    .content {
        width: 100%;
        height: 100%;
        overflow-y: auto;
        .img {
            width: 100%;
        }
    }
    .pdf-title {
        display: flex;
        justify-content: space-between;
        padding: 14px 20px;
        .left {
            width: 70%;
            text-align: left;
            font-size: 17px;
            color: #0A0A0A;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .right {
            font-size: 12px;
            line-height: 17px;
            .active {
                color: $app-color;
            }
            .unactive {
                color: #878F99;
            }
        }
    }
    .pdf-content {
        height: calc(100vh - 115px);
        overflow: auto;
    }
    .btn-group {
        padding: 16px 20px 10px;
        display: flex;
        justify-content: space-between;
        background-color: white;
        .btn {
            width: 162px;
            height: 44px;
            line-height: 44px;
            border-radius: 8px;
            font-size: 16px;
            background: $app-color;
            color: white;
        }
    }
}
.report-page-wechat {
    background-color: $wx-bg-color;
    .pdf-title {
        .right {
            .active {
                color: $wx-color;
            }
        }
    }
    .btn-group {
        .btn {
            background-color: $wx-color;
        }
    }
}
</style>
