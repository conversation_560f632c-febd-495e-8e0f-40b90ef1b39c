<!--个人体征-->
<template>
    <div>
        <div
            v-if="list && list.length > 0"
            class="personal"
            :class="{ 'personal-wechat': !$store.getters['docWorkRoom/isApp'] }"
            >
            <div v-for="(item, index) in list" :key="index">
                <div @click="handleClick(index)" class="row">
                    <div v-if="item.measure_at" class="left">{{ item.measure_at.slice(0, 10) }}</div>
                    <div v-else-if="item.measured_at" class="left">{{ item.measured_at.slice(0, 10) }}</div>
                    <div v-else class="left">{{ item.measuretimestamp.slice(0, 10) }}</div>
                    <div class="right">
                        <van-icon v-if="item.extend" name="arrow-up"></van-icon>
                        <van-icon v-else name="arrow-down"></van-icon>
                    </div>
                </div>
                <div v-if="item.children && item.children.length > 0 && item.extend">
                    <div
                        v-for="(childItem, childIndex) in item.children"
                        :key="childIndex"
                        class="content"
                    >
                        <div class="left">
                            <div class="name">{{ childItem.name }}</div>
                            <div v-if="childItem.unit" class="unit">{{ childItem.unit }}</div>
                        </div>
                        <div class="right">
                            <div v-if="childItem.showValue && (childItem.showValue != '-1.0' && childItem.showValue != '-1.00')" class="value">
                                <abnormalIndicator
                                    v-if="$route.query.code != 'public_template_tab_bs'"
                                    :code="childItem.code"
                                    :record="item"
                                    :codeValue="childItem.showValue"
                                />
                                <abnormalIndicator
                                    v-else
                                    :code="`dining_status${item.dining_status}`"
                                    :record="item"
                                    :codeValue="childItem.showValue"
                                />
                            </div>
                            <div v-else class="value no-value">无结果</div>
                            <div v-if="childItem.reference_range" class="range">{{ childItem.reference_range }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <empty v-else></empty>
    </div>
</template>

<script>
import { Toast } from 'vant'
import empty from '../components/empty'
import abnormalIndicator from '../components/abnormalIndicator.vue'
import { 
    getDetectionConfigApi,
    getHeightWeightListApi,     // 身高体重
    getBloodSugarListApi,       // 血糖
    getSpoListApi,              // 血氧饱和
    getPefListApi,              // 流速峰值
    getBpListApi,
    getSidesListApi,
    getGeneralSignListApi,
} from '@/api/saas.js'

// 血糖映射 
const bs = {
    1: '空腹',
    2: '早餐后',
    3: '午餐前',
    4: '午餐后',
    5: '晚餐前',
    6: '晚餐后',
    7: '睡前',
    8: '凌晨',
    9: '随机测量'
}

export default {
    components: {
        empty,
        abnormalIndicator
    },
    data () {
        return {
            list: [],
            displayFields: []
        }
    },
    computed: {
        searchInfo() {
            return this.$store.getters['docWorkRoom/searchInfo']
        },
    },
    created () {
        document.title = this.$route.query.title
        this.getDetectionConfig()
    },
    methods: {
        handleClick (index) {
            this.list = this.list.map((item, i) => {
                if (i == index) {
                    item.extend = !item.extend
                } 
                // else {
                //     item.extend = false
                // }
                return item
            })
        },
        // 获取配置的的字段
        async getDetectionConfig () {
            const toast = Toast.loading({
                message: '加载中...',
                forbidClick: true,
                duration: 0
            })
            try {
                const { patient_id, project_id, indicator_group_id } = this.$route.query
                const { data: { config } } = await getDetectionConfigApi({
                    patient_id,
                    indicator_group_id,
                    scene: 'sicker_medical_record',
                })
                this.displayFields = config.display_fields
                this.getData()
                toast.clear()
            } catch {
                toast.clear()
            }
        },
        // 获取参数
        getBaseParams () {
            const { patient_id, project_id, indicator_group_id, code } = this.$route.query
            return {
                patient_id,
                indicator_group_id,
                measure_type: 0,    // 0: 全部
                // ...this.searchInfo,
                show_detail: code == 'public_template_tab_bs' ? 1 : 0,  // 血糖：1 其他 ：0
                measure_method: '2',
                page: 1,
                page_size: 100,   // 血糖和其他都改成 100 条
            }
        },
        // 处理数据
        dealData (data) {
            this.list = data.map((item) => {
                item.children = this.displayFields.reduce((pre, cur) => {
                    pre.push({
                        ...cur,
                        showValue: item[cur.code]
                    })
                    return pre
                }, [])
                return item
            })
            if (this.list.length > 0) {
                this.list[0].extend = true
            }
        },
        // 获取具体数据
        async getData () {
            let result = null 
            const code = this.$route.query.code
            switch (code) {
                // 血糖
                case 'public_template_tab_bs':
                    result = await getBloodSugarListApi(this.getBaseParams())
                    // 取出血糖分类（例如：dining_status1 取出1）
                    const displayFields = this.displayFields.map(item => +item.code.slice(-1))
                    console.log(displayFields)
                    // this.list = result && result.data && result.data.list.filter(item => {
                    //     if (displayFields.includes(+item.dining_status)) {
                    //         item.measure_date = item.measure_at
                    //         item.children = [{ name: bs[item.dining_status], showValue: item.bg}]
                    //         return item
                    //     }
                    // })
                    this.list = result && result.data && result.data.list.filter(item => {
                        if (displayFields.includes(+item.dining_status)) {
                            return item
                        }
                    })
                    this.list = this.list.reduce((pre, cur) => {
                        if (!pre.find(item => item.measure_at == cur.measure_at)) {
                            cur.children = [{ name: bs[cur.dining_status], showValue: cur.bg}]
                            pre.push(cur)
                        } else {
                            const index = pre.length - 1
                            pre[index].children.push({
                                name: bs[cur.dining_status], 
                                showValue: cur.bg
                            })
                        }
                        return pre
                    }, [])
                   
                    console.info('血糖')
                    console.log(this.list)
                    if (this.list.length > 0) {
                        this.list[0].extend = true
                    }
                break;
                // 血氧饱和
                case 'public_template_tab_lifesigns_blood_oxygen':
                    result = await getSpoListApi(this.getBaseParams())
                    this.dealData(result && result.data && result.data.data)
                break;
                // 身高体重
                case 'public_template_tab_hw': 
                    result = await getHeightWeightListApi(this.getBaseParams())
                    this.dealData(result && result.data && result.data.list)
                break;
                // 流速峰值
                case 'public_template_tab_lifesigns_peak_flow_rate':
                    result = await getPefListApi(this.getBaseParams())
                    this.dealData(result && result.data && result.data.data)
                break;
                // 血压
                case 'public_template_tab_bp':
                    result = await getBpListApi(this.getBaseParams())
                    this.dealData(result && result.data && result.data.list)
                break;
                // 纬度信息
                case 'public_template_tab_sides':
                    result = await getSidesListApi(this.getBaseParams())
                    this.dealData(result && result.data && result.data.list)
                break;
                // 一般体征
                case 'public_template_tab_general_signs':
                    result = await getGeneralSignListApi(this.getBaseParams())
                    this.dealData(result && result.data && result.data.data)
                break;
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.personal {
    background-color: #F8F8F8;
    height: 100vh;
    .row {
        display: flex;
        justify-content: space-between;
        padding: 17px 14px;
        background-color: #F8F8F8;
        &:hover {
            cursor: pointer;
        }
        .left {
            font-size: 15px;
            color: #0A0A0A;
        }
        .right {
            font-size: 14px;
            color: #878F99;
        }
    }
    .content {
        padding: 13.5px 14px;
        background-color: white;
        border-bottom: 1px solid #F1F1F1;
        display: flex;
        justify-content: space-between;
        font-size: 17px;
        line-height: 24px;
        .left, .right {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            justify-content: space-between;
            width: 50%;
            text-align: left;
        }
        .right {
            align-items: flex-end;
        }
        .name {
            font-size: 17px;
            color: #0A0A0A;
        }
        .value {
            font-size: 17px;
            color: #5A6266;
        }
        .no-value {
            color: #C0C6CC;
        }

        .unit, .range {
            padding-top: 12px;
            font-size: 12px;
            color: #878F99;
        }
    }
}
.personal-wechat {
    background-color: #F5F7FB;
    .row {
        background-color: #F5F7FB;
    }
}
</style>