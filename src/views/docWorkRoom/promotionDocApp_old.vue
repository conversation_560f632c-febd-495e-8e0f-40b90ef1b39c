<template>
    <div>
        <div class="promotionDocApp" v-if="!isErr">
            <div class="logoOuter">
                <img class="logo" src="./imgs/zzLogo.png" alt="">
            </div>
            <div class="mainOuter">
                <img class="topBg" src="./imgs/topBg.png" alt="">
                <div class="inputOuter">
                    <div class="invitationText">
                        <div class="inviter textCut" v-if="referer_info.is_show_referrer == '1'">{{referer_info.referer_name || ''}}</div>
                        <div>邀请你加入医生工作室</div>
                    </div>
                    <div class="joinBtn" @click="register">立即注册加入医生工作室</div>
                    <div class="inputInner">
                        <input class="inputLine1 input" placeholder="请输入手机号" type="number" v-model="telNum">
                        <div class="inputLine2">
                            <input class="input2" placeholder="请输入验证码" type="number" v-model="code" maxlength="4">
                            <span class="time" :class="[canSend?'canSend':'noSend']" @click="getCode">{{timeText}}</span>
                        </div>
                        <img v-show="telNum != ''" @click="clearText" class="closeIcon" src="./imgs/closeIcon.png" alt="">
                    </div>
                    <div class="agreement">
                        <van-checkbox color="#181818" icon-size="16px" v-model="checked" @change="checkedChange"></van-checkbox>
                        <div class="agreementText">
                            已阅读并同意 
                            <a href="https://zz-med-test-pub.oss-cn-hangzhou.aliyuncs.com/agreement/contents/No-20220415-HVIT/v1-2-7.html"> 隐私保护政策 </a> 
                            和 
                            <a href="https://zz-med-test-pub.oss-cn-hangzhou.aliyuncs.com/agreement/contents/no-20220509-clg0/2-0-1.html"> 用户使用协议 </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div v-else>
            <span class="errMsg">{{errMsg}}</span>
        </div>
    </div>
</template>

<script>
import { getSmsCode,getPromotionPersonInfo,docLogin,getWxConfig } from '@/api/docWorkRoom.js'
import { getUrlParams } from '@/utils/utils.js'
import wx from "weixin-js-sdk"
import axios from 'axios'
export default {
    data(){
        return {
            timeText: '获取验证码',
            canSend: true,  //是否可发送验证码
            telNum: '',
            code: '',
            noDoubleTap: true,
            time: 60,
            referer_info: {},  //推荐人配置（是否显示和推荐人姓名）
            channel_id: null,  //渠道ID
            event_source: null,  //注册场景、来源  scan/card_share
            checked: false,
            isErr: false,  //推广码错误
            errMsg: '请联系发码人员',
        }
    },
    methods: {
        checkedChange(e){
            console.log(e)
        },
        clearText(){
            this.telNum = ''
        },
        checkTelNum(){
            let telNum = this.telNum
            if (telNum == '') {
                this.$toast('请输入您的手机号')
                return false
            }
            let myreg = /^[1][0-9]{10}$/;
            if (!myreg.test(telNum)) {
                this.$toast('请输入正确的手机号')
                return false
            }
            return true
        },
        getCode(){
            if(!this.checkTelNum()){
                return
            }
            if(!this.canSend){
                return
            }
            this.noDoubleTapFun(() => {
                let obj = {
                    mobile: this.telNum,
                    type: 5
                }
                getSmsCode(obj).then(res=>{
                    console.log(res)
                    if(res.code == 200){
                        let timer = setInterval(() => {
                            if (this.time > 1 && this.time <= 60) {
                                this.time = this.time - 1
                                this.canSend = false
                                this.timeText = `${this.time} s后获取`
                            } else {
                                this.time = 60
                                this.canSend = true
                                this.timeText = `获取验证码`
                                clearInterval(timer)
                            }
                        }, 1000)
                    }else{
                        this.$toast(res.msg)
                    }
                })
            });
        },
        getPromotionPersonInfo(channelId){
            getPromotionPersonInfo(channelId).then(res=>{
                if(res.code == 200){
                    this.referer_info = res.data.referer_info
                }else{
                    this.isErr = true
                    this.errMsg = res.msg
                }
            })
        },
        register(){
            if(!this.checkTelNum()){
                return
            }
            if(this.code == ''){
                this.$toast('请输入验证码')
                return
            }
            if(!this.checked){
                this.$toast('请同意隐私保护政策和用户使用协议')
                return
            }
            let channel_info = {
                channel_id: this.channel_id,
                event_source: this.event_source ? this.event_source : 'scan'
            }
            let obj = {
                user_account: this.telNum,
                valid_code: this.code,
                password: '',
                from_flag: 5,
                channel_info: JSON.stringify(channel_info)
            }
            docLogin(obj).then(res=>{
                if(res.code == 200){
                    this.$toast('开通成功，下载app立即使用吧')
                    setTimeout(()=>{
                        window.location.href = "https://patient-h5.zz-med.com/static/doc/View/download/Index.html"
                    },1500)
                }else{
                    this.$toast(res.msg)
                }
            })
        },
        getWxConfig(){
            // let url = `${window.location.href}&event_source=card_share`
            let url = window.location.href.split('#')[0]
            let obj = {
                url,
                account: 'ihec'
            }
            axios.post('https://patient-api.zz-med.com/api/v1/wechat/js_api/sign',obj).then(res=>{
            // getWxConfig(obj).then(res=>{
                if(res.data.status == 0){
                    let {appId,timestamp,nonceStr,signature} = res.data.data
                    wx.config({
                        debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
                        appId: appId, // 必填，公众号的唯一标识
                        timestamp: timestamp, // 必填，生成签名的时间戳
                        nonceStr: nonceStr, // 必填，生成签名的随机串
                        signature: signature, // 必填，签名
                        jsApiList: [
                            "onMenuShareAppMessage"
                        ],
                    })
                    wx.ready(function () {   //需在用户可能点击分享按钮前就先调用
                        wx.onMenuShareAppMessage({
                            title: '下载医生工作室App',
                            desc: '医生工作室，为医生搭建专业的患者管理、科室管理、个人工作管理平台', // 分享描述
                            link: url, // 分享链接，该链接域名或路径必须与当前页面对应的公众号 JS 安全域名一致
                            imgUrl: 'https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/guoshou/logo.png', // 分享图标
                            success: function () {
                                console.log('成功')
                            }
                        })
                    })
                }
            })
        },
        init(){
            let url = window.location.href
            let channel_id = this.channel_id = getUrlParams(url,'channel_id')
            this.event_source = getUrlParams(url,'event_source')
            this.getPromotionPersonInfo(channel_id)
            this.getWxConfig()
        },
        /**
         * 防止多次点击
         * @param {Function} fn 执行函数
         */
        noDoubleTapFun(fn) {
            if (this.noDoubleTap) {
                this.noDoubleTap = false
                setTimeout(() => {
                    this.noDoubleTap = true
                }, 5000);
                fn();
            }
        }
    },
    created() {
        this.init()        
    }
}
</script>

<style lang="scss">
.promotionDocApp{
    background: url('./imgs/mainBg.png') 100% 100%;
    background-size: cover;
    width: 100%;
    height: 100vh;
    position: relative;
    .logoOuter{
        position: absolute;
        top: 0;
        left: 4px;
        width: 117px;
        height: 35px;
        background: rgba(255,255,255,0.8);
        border-radius: 0 0 6px 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        .logo{
            width: 100px;
            height: 20px;
        }
    }
    .mainOuter{
        position: absolute;
        top: 56px;
        left: 50%;
        transform: translate(-50%,0);
        .topBg{
            width: 335px;
            height: 210px;
        }
        .inputOuter{
            width: 345px;
            height: 380px;
            background: url('./imgs/innerBg.png') 100% 100%;
            background-size: cover;
            position: relative;
            top: -60px;
            .joinBtn{
                color: #9D531D;
                font-size: 18px;
                font-weight: 500;
                width: 292px;
                height: 52px;
                border-radius: 100px;
                border: 1px solid #F45A29;
                background: linear-gradient(211deg, #FFE39A 18.31%, #FFCD6B 54.93%, #FFCB65 80.66%);
                box-shadow: 0px 4px 4px 0px rgba(255, 209, 46, 0.20), 0px -6px 0px 0px #FF961C inset, 0px -2px 4px 0px rgba(255, 255, 255, 0.22) inset;
                display: flex;
                align-items: center;
                justify-content: center;
                position: absolute;
                bottom: 24px;
                z-index: 11;
                left: 50%;
                transform: translate(-50%, 0);
            }
            .invitationText{
                position: absolute;
                top: 60px;
                right: 15px;
                color: white;
                font-size: 16px;
                font-weight: 500;
                display: flex;
                align-items: center;
                height: 27px;
                line-height: 27px;
                .inviter{
                    max-width: 70px;
                    width: max-content;
                    height: 27px;
                    text-align: center;
                    line-height: 27px;
                    border-radius: 6px;
                    background: #FFA927;
                    padding: 0 5px;
                    margin-right: 5px;
                }
            }
            .inputInner{
                position: absolute;
                top: 130px;
                left: 50%;
                transform: translate(-50%, 0);
                width: 293px;
                .inputLine1{
                    width: 100%;
                    height: 48px;
                    line-height: 48px;
                    border-radius: 100px;
                    border: 1.5px solid rgba(255, 140, 34, 0.13);
                    background: #FFE4CB;
                    box-shadow: 0px 0px 8px 0px rgba(255, 158, 69, 0.19);
                }
                .inputLine2{
                    display: flex;
                    margin-top: 24px;
                    justify-content: space-between;
                }
                .input2{
                    width: 152px;
                    height: 48px;
                    border-radius: 100px;
                    border: 1.5px solid rgba(255, 140, 34, 0.13);
                    background: #FFE4CB;
                    box-shadow: 0px 0px 8px 0px rgba(255, 158, 69, 0.19);
                }
                input{
                    padding: 0 16px;
                    box-sizing: border-box;
                }
                .input{
                    font-size: 20px;
                    font-weight: 500;
                    color: #AE521E;
                }
                .input2{
                    color: #FF8B20;
                    font-size: 17px;
                    font-weight: 400;
                }
                .inputLine1::-webkit-input-placeholder,.input2::-webkit-input-placeholder {
                    color: #FF8B20;
                    font-weight: 400;
                }
                .time{
                    width: 125px;
                    height: 48px;
                    line-height: 48px;
                    text-align: center;
                    border-radius: 100px;
                }
                .canSend{
                    color: #FFF;
                    font-size: 17px;
                    font-weight: 500;
                    border-radius: 100px;
                    background: linear-gradient(180deg, #FF9F2E 0%, #FF7918 100%);
                    box-shadow: 0px -2px 8px 0px #FFF280 inset, 0px -2px 4px 0px rgba(255, 209, 140, 0.77) inset;
                }

                .noSend{
                    color: #9D531D;
                    font-size: 17px;
                    font-weight: 400;
                    background: rgba(250, 225, 203);
                    box-shadow: 0px -2px 8px 0px #FFF280 inset, 0px -2px 4px 0px rgba(255, 209, 140, 0.77) inset;
                }
                .closeIcon{
                    position: absolute;
                    top: 12px;
                    right: 16px;
                    width: 24px;
                    height: 24px;
                }
            }
            .agreement{
                position: absolute;
                bottom: -30px;
                color: #181818;
                font-size: 12px;
                font-weight: 400;
                left: 50%;
                transform: translate(-50%, 0);
                width: max-content;
                display: flex;
                align-items: center;
                .agreementText{
                    margin-left: 5px;
                    margin-top: 2px;
                }
                a{
                    font-size: 12px;
                    font-weight: 400;
                    color: #181818;
                    text-decoration: underline;
                    text-underline-offset: 2px;
                }
                .van-icon {  
                    border: 1px solid #181818;  
                }
            }
        }
    }
    .textCut {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        line-height: normal;
    }
}
.errMsg{
    margin-top: 30%;
    display: block;
    color: #666666;
    font-size: 18px;
    padding: 0 10px;
}
</style>