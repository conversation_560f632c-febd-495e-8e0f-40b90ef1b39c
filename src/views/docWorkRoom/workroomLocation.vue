<template>
  <div class="workroom-location">
    <div class="header">
      <!-- <van-search v-model="value" shape="round" id="tipinput" placeholder="请输入搜索关键词" /> -->
      <van-popover
        v-model="showPopover"
        trigger="click"
        :offset="[0, 0]"
      >
        <!-- <div class="list">
          <div class="item" v-for="(item, index) in actions" :key="index" @click="onSelect(item)">
            <p class="name ellipsis">{{ item.title }}</p>
            <p class="address ellipsis">{{ item.address }}</p>
          </div>
        </div> -->
        <template #reference>
          <van-search v-model="value" shape="round" id="tipinput" placeholder="请输入科室的所在地区" />
          <!-- <van-button round size="small" type="info" :disabled="!location">确定</van-button> -->
        </template>
      </van-popover>
    </div>
    <div id="container"></div>
    <div class="panel" id="panel" ref="panel">
      <div class="resAddressItem" v-for="item in resAddressList" :key="item.id" @click="selectAddress(item)">
        <div>
          <div class="title">{{ item.title }}</div>
          <div class="address">{{ item.address }}</div>
        </div>
        <div style="width: 20px;">
          <van-icon v-if="item.checked" name="success" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// import AMapLoader from '@amap/amap-jsapi-loader'
// window._AMapSecurityConfig = {
//   securityJsCode: process.env.VUE_APP_AMAP_SECURITYJSCODE,
// }
const TMAPKEY = 'QBOBZ-L6KKB-33KUM-JVS7R-TK4G2-LEFXJ'
export default {
  data() {
    return {
      key: '',
      status: 0,
      value: '',
      timer: null,
      showPopover: false,
      actions: [],
      map: null,
      autoComplete: null,
      placeSearch: null,
      addr: '北京市',
      latitude: 116.397755, // 纬度
      longitude: 39.903179, //经度
      location: null,
      resAddressList: [],
      mapMarker: null
    }
  },
  watch: {
    value(newVal, oldVal) {
      if(newVal){
        const f = this.throttle(() => {
          this.handleSearch(newVal)
        }, 500)
        f()
      }
      // if (newVal !== oldVal && this.status !== 1) {
      //   const f = this.throttle(() => {
      //     this.autoComplete.search(newVal, (status, result) => {
      //       if (status !== 'error' || result.info === 'OK') {
      //         const { tips } = result
      //         if (tips.length > 0) {
      //           tips.forEach(item => {
      //             item.text = item.name
      //           })
      //           this.actions = tips
      //           this.showPopover = true
      //         }
      //       }
      //     })
      //   }, 500)
      //   f()
      // }
    },
  },
  mounted() {
    const { addr, lat, lng } = this.$route.query
    if (addr) {
      const city = addr.split(',')[0]
      this.addr = city
    }
    if (lat && lng) {
      this.latitude = parseFloat(lat)
      this.longitude = parseFloat(lng)
    }
  },
  created(){
    this.initMap()
  },
  methods: {
    throttle(fn, delay) {    
      return () => {
        if (this.timer) {
          return
        }
        this.timer = setTimeout(() => {
          fn()
          this.timer = null
        }, delay)
      }
    },
    async handleSearch(val){
      const params = {
        keyword: val,
        center: new TMap.LatLng(this.latitude, this.longitude),
        radius: 1000,
        pageIndex: 1,
        pageSize: 10
      }
      let addressList = await this.placeSearch.searchNearby(params)
      this.resAddressList = addressList.data.map(item=>{
        item.checked = false
        return item
      })
      console.log(this.resAddressList)
    },
    selectAddress(item){
      this.resAddressList = this.resAddressList.map(item=>{
        item.checked = false
        return item
      })
      item.checked = true
      let {lat,lng} = item.location
      this.mapMarker.setGeometries([])
      this.mapMarker.add([
        {
          id: `marker-${Date.now()}`, // 唯一标识
          position: new TMap.LatLng(lat, lng), // 标记坐标
          styleId: 'label',
          properties: {
            title: item.title, // 标记标题
          },
          content: item.title, // 标记标题
        },
      ]);
      this.map.setCenter(new TMap.LatLng(lat, lng))
      this.sendLocationInfo(item)
    },
    async loadSDK() {
      return new Promise((resolve, reject) => {
        if (typeof TMap !== 'undefined') {
            resolve()
            return
        }
        const script = document.createElement('script')
        script.src = `https://map.qq.com/api/gljs?v=1.exp&key=${TMAPKEY}&libraries=service`
        script.onerror = reject;
        script.onload = resolve;
        document.head.appendChild(script)
      })
    },
    // 初始化地图
    async initMap() {
      await this.loadSDK()
      this.placeSearch = new TMap.service.Search({
        key: TMAPKEY
      })
      this.handleSearch(this.addr)
      // 初始化地图
      const map = new TMap.Map('container', {
        zoom: 12,
        center: [this.latitude, this.longitude],
      });
      this.map = map
      this.mapMarker = new TMap.MultiMarker({
        map: map,
        styles: {
          'label': new TMap.MarkerStyle({
            color: '#000000', // 文字颜色（黑色）
            size: 14, // 文字大小
            backgroundColor: '#FFFFFF', // 背景颜色（白色）
            borderRadius: 8, // 圆角半径
            offset: { x: 0, y: -40 }, // 标签偏移，使其位于标记图标上方
            angle: 0, // 标签旋转角度
            alignment: 'center', // 水平对齐方式
            verticalAlignment: 'middle', // 垂直对齐方式
          })
        },
        geometries: [],
      })
      // AMapLoader.load({
      //   key: process.env.VUE_APP_AMAP_KEY,
      //   version: '2.0',
      // }).then((AMap) => {
      //   const map = new AMap.Map("container", {
      //     zoom: 12,
      //     center: [this.latitude, this.longitude],
      //   })
      //   this.map = map

      //   AMap.plugin(['AMap.AutoComplete', 'AMap.PlaceSearch'], () => {
      //     // const geoLocation = new AMap.Geolocation()
      //     // map.addControl(geoLocation)
      //     // geoLocation.getCurrentPosition((status, result) => {
      //     //   console.log('status: ', status)
      //     //   console.log('result: ', result)
      //     // })

      //     const autoComplete = new AMap.AutoComplete({
      //       city: this.addr,
      //     })
      //     this.autoComplete = autoComplete;

      //     const placeSearch = new AMap.PlaceSearch({
      //       map: map,
      //       // type: '全部',
      //       pageSize: 10,
      //       panel: 'panel',
      //     })
      //     this.placeSearch = placeSearch;
      //     AMap.Event.addListener(placeSearch, 'selectChanged', (SelectChangeEvent) => {
      //       this.location = SelectChangeEvent.selected.data;
      //       this.sendLocationInfo()
      //     })

      //     this.placeSearch.search(this.addr, this.setDefaultSelected)
      //   })
      // })
    },
    // 选择模糊地址 选择联想词的方法
    // onSelect(e) {
    //   const { name, adcode } = e
    //   this.placeSearch.setCity(adcode)
    //   this.placeSearch.search(name, this.setDefaultSelected)
    //   this.showPopover = false
    //   this.status = 1
    //   this.value = name

    //   setTimeout(() => {
    //     this.status = 0
    //   }, 300)
    // },
    // 设置默认选中的地址
    // setDefaultSelected(status, result) {
    //   if (status === 'complete' && result.info === 'OK') {
    //     const { count, pois } = result.poiList
    //     if (count > 0) {
    //       this.$nextTick(() => {
    //         const node_list = this.$refs.panel.querySelectorAll('.poibox')
    //         if (node_list) {
    //           node_list[0].click()
    //           this.location = pois[0]
    //           this.sendLocationInfo()
    //         }
    //       })
    //     }
    //   }
    // },
    // 传值给原生
    sendLocationInfo(item) {
      const UA = navigator.userAgent
      const isAndroid = UA.indexOf('Android') > -1 || UA.indexOf('Adr') > -1 // android终端
      const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端

      // const {
      //   name,
      //   address,
      //   pname,
      //   cityname,
      //   adname,
      // } = this.location
      // const { lat, lng } = this.location.entr_location || { lat: '', lng: '' }
      let {title:name,address,ad_info,location} = item
      let {province:pname,city:cityname,district:adname} = ad_info
      let {lat,lng} = location

      const obj = { name, address, pname, cityname, adname, lat, lng }
      const params = JSON.stringify(obj)
      // if (process.env.NODE_ENV === 'production') {
        if (isAndroid) {
          window.android.getLocationInfo(params)
        }
        if (isIOS) {
          window.webkit.messageHandlers.getLocationInfo.postMessage(params)
        }
      // }
    },
  },
}
</script>

<style lang="scss" scoped>
.workroom-location,
#a-map {
  width: 100vw;
  min-height: 100vh;
}
#container {
  width: 100%;
  height: 45vh;
}
.header {
  .van-popover__wrapper {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .van-search {
      width: 100%;
    }
    .van-button {
      width: 60px;
      margin-right: 12px;
    }
  }
}
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.van-popover {
  .van-popover__content {
    border-radius: 0 !important;
    .list {
      width: 100vw;
      max-width: 100vw;
      max-height: 38vh;
      overflow-y: scroll;
      .item {
        border-bottom: 1px solid #e8e8e8;
        padding: 9px 12px;
        &:last-of-type {
          border: none;
        }
        .name {
          font-size: 14px;
          color: #666;
        }
        .address {
          margin-top: 5px;
          font-size: 12px;
          color: #999;
        }
      }
    }
  }
}
.panel {
  height: calc(55vh - 54px);
  overflow-y: scroll;
  .resAddressItem{
    text-align: left;
    padding: 10px;
    line-height: normal;
    border-bottom: 1px solid #EEEEEE;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .title{
      color: #000000;
    }
    .address{
      color: #999999;
    }
  }
  // background-color: #eeeeee;
  // ::v-deep .amap_lib_placeSearch .poibox {
  //   &.active {
  //     background-color: #eeeeee;
  //   }
  //   .poi-info {
  //     text-align: left;
  //   }
  //   .poi-title {
  //     text-align: left;
  //     .poi-name {
  //       max-width: initial;
  //       display: block;
  //     }
  //   }
  // }

}
</style>
