<template>
    <div class="promotionDocAppOuter">
        <div class="promotionDocApp" v-if="!isErr">
            <video
                webkit-playsinline='true'
                playsinline='true'
                class="video" 
                controlslist="nodownload noplaybackrate  noremoteplayback" 
                :disablePictureInPicture="true"
                ref="video" 
                controls
                :src="referer_info.promotion_url"
                :poster="referer_info.promotion_video_img">
            </video>
            <div class="mainOuter">
                <div class="dotTitle">
                    <div class="dot"></div>
                    <div class="titleText">
                        <div class="textCut" v-if="referer_info.is_show_referrer != 0">{{ referer_info.referer_name || '' }}</div>
                        <div>{{['doc_intro','doc_intro_distribution'].indexOf(channelType) != -1 ? '邀请您开通医生工作室' : '邀请您加入公司工作室'}}</div>
                    </div>
                </div>
                <div class="subTitle" v-if="['doc_intro','doc_intro_distribution'].indexOf(channelType) != -1">感谢您为慢病患者做出的贡献</div>
                <div class="inputInner">
                    <div class="companyBlock" v-if="['invite_channel_agent','invite_member'].indexOf(this.channelType) != -1">
                        <div class="companyLine">
                            <input :style="{'color': referer_info.hosp_name ? '#666' : '#000'}" :readonly="referer_info.hosp_name || isSelected ? true : false" class="companyInput" placeholder="查找您的公司（至少输入4个字）" type="text" v-model="companyText" @input="searchCompany">
                            <template v-if="!referer_info.hosp_name">
                                <van-icon v-if="companyText.length == 0" class="rightIcon" name="play" color="#CCCCCC" />
                                <van-icon v-else name="close" class="rightIcon" color="#CCCCCC"  @click="clearFun"/>
                            </template>
                            
                        </div>
                        <div class="companyList" v-if="isCompanyListPopShow">
                            <template v-if="companyList.length > 0">
                                <div class="companyItem" @click="selectCompany(item)" v-for="(item,index) in companyList" :key="index">{{item.company_name}}</div>
                            </template>
                            <template v-if="companyList.length == 0 && !loading">
                                <div class="companyItem">未搜索到相关公司</div>
                            </template>
                        </div>
                        
                    </div>
                    <input class="input1" placeholder="请输入手机号" type="number" v-model="telNum">
                    <div class="inputLine2">
                        <input class="input2" placeholder="请输入验证码" type="number" v-model="code" maxlength="4">
                        <span class="time" :class="[canSend?'canSend':'noSend']" @click="getCode">{{timeText}}</span>
                    </div>
                </div>
                <!-- 如果需要填写公司 并且没有从搜索出来的公司选择 按钮置灰 -->
                <div class="joinBtn" :style="{'background': ['invite_channel_agent'].indexOf(this.channelType) != -1 ? isSelected ? '#F7830D': '#CCCCCC' : '#F7830D'}" @click="register">{{['doc_intro','doc_intro_distribution'].indexOf(channelType) != -1 ? '立即开通医生工作室' : '立即注册加入公司工作室'}}</div>
                <div class="agreement">
                    <van-checkbox color="#181818" icon-size="16px" v-model="checked" @change="checkedChange"></van-checkbox>
                    <div class="agreementText">
                        已阅读并同意 
                        <a href="https://zz-med-test-pub.oss-cn-hangzhou.aliyuncs.com/agreement/contents/No-20220415-HVIT/v1-2-7.html"> 隐私保护政策 </a> 
                        和 
                        <a href="https://zz-med-test-pub.oss-cn-hangzhou.aliyuncs.com/agreement/contents/no-20220509-clg0/2-0-1.html"> 用户使用协议 </a>
                    </div>
                </div>
            </div>
            
        </div>
        <div v-else>
            <span class="errMsg">{{errMsg}}</span>
        </div>
        <van-dialog v-model="isChangePersonPopShow" :title="changePersonPopTitle" show-cancel-button confirmButtonText="确认更换" confirmButtonColor="#FF6F00" @confirm="saveFun">
            <div class="changePersonPop">
                <div class="person">
                    <div class="name">{{ oldPerson.name }}</div>
                    <div class="tel">{{ oldPerson.cell }}</div>
                </div>
                <img src="./imgs/changePersonIcon.png" alt="">
                <div class="person">
                    <div class="name" style="font-weight: bold;">{{ newPerson.name }}</div>
                    <div class="tel">{{ newPerson.cell }}</div>
                </div>
            </div>
        </van-dialog>
        <van-dialog v-model="isSuccessPopShow" title="您已成功绑定您的客服经理" confirmButtonText="确认" confirmButtonColor="#FF6F00" @confirm="goDownloadApp">
            <div class="changePersonPop changeSuccessPop">
                <img src="./imgs/managerDefaultHeadPic.png" alt="">
                <div class="name" style="font-weight: bold;">{{ bindManagerPerson.name }}</div>
                <div class="tel">{{ bindManagerPerson.cell }}</div>
            </div>
        </van-dialog>
        <!-- <van-overlay :z-index="zIndex" show="true" class="openAppPop" :style="{'opacity': zIndex > -1 ? 1 : 0}"> -->
        <van-overlay :show="isOpenAppShow" class="openAppPop">
            <div class="inner">
                <wx-open-launch-app 
                    id="openAppBtn" 
                    appid="wxd4d251ee3ba85b70" 
                    :extinfo="extInfo"
                    >
                    <!-- style="position: fixed;left: 0;bottom: 100px;width: 100%;background-color: transparent;z-index: 9999999999;" -->
                    <script type="text/wxtag-template">
                        <style>
                            .openAppBtnOuter{
                                width: 280px;
                                background-color: #fff;
                                border-radius: 8px;
                                padding-bottom: 20px;
                            }
                            .title{
                                font-size: 17px;
                                font-weight: 500;
                                padding: 20px 0;
                                text-align: center;
                                width: 100%;
                            }
                            .openAppBtn {
                                width: 220px;
                                border-radius: 8px;
                                text-align: center;
                                height: 44px;
                                line-height: 44px;
                                background: #F7830D;
                                font-size: 16px;
                                font-weight: bold;
                                color: white;
                                margin-top: 20px;
                                margin-left: 50%;
                                position: relative;
                                left: -110px;
                            }
                            .logo{
                                margin-left: 50%;
                                position: relative;
                                left: -50px;
                                width: 100px;
                                height: 100px;
                            }
                        </style>
                        <div class="openAppBtnOuter">
                            <div class="title">渠道账号注册成功</div>
                            <img class="logo" src="https://ares.zz-med.com/doctorapp/docAppLogo.png" alt="">
                            <div class="openAppBtn">立即打开/下载App</div>
                        </div>
                    </script>
                </wx-open-launch-app>
            </div>
        </van-overlay>
    </div>
</template>

<script>
const UA = navigator.userAgent.toLowerCase()
const isWeixin = UA.indexOf("micromessenger") !== -1     // 微信
import { getSmsCode,getPromotionPersonInfo,docLogin,getWxConfig,searchCompanyList,promotionAfterScan,promotionGetConfig,promotionSaveCheck,promotionSave } from '@/api/docWorkRoom.js'
import { getUrlParams } from '@/utils/utils.js'
import wx from "weixin-js-sdk"
import axios from 'axios'
export default {
    data(){
        return {
            timeText: '获取验证码',
            canSend: true,  //是否可发送验证码
            telNum: '',
            code: '',
            noDoubleTap: true,
            time: 60,
            referer_info: {},  //推荐人配置（是否显示和推荐人姓名）
            channel_id: null,  //渠道ID
            event_source: null,  //注册场景、来源  scan/card_share
            checked: false,
            isErr: false,  //推广码错误
            errMsg: '请联系发码人员',
            companyText: '',
            timer: null,
            companyList: [],
            loading: true,
            scanId: '',
            channelType: '',  //doc_intro: app推广码 doc_intro_distribution: 渠道绑定码 invite_channel_agent： 邀请代理商码 invite_channel_personal：邀请外部个人码 invite_member：邀请员工码
            isChangePersonPopShow: false,
            isSuccessPopShow: false,
            isCompanyListPopShow: false,
            newPerson: {},
            oldPerson: {},
            bindManagerPerson: {},
            changePersonPopTitle: '',
            isSelected: false,  //从搜索出来的公司中选择 标示
            isOpenAppShow: false,  //是否打开app弹窗
            extInfo: JSON.stringify({'key': 'appHome'}),
        }
    },
    watch: {
        companyText:{
            handler(newVal,oldVal){
                if(newVal.length < 4){
                    this.companyList = []
                    this.isCompanyListPopShow = false
                    if(this.timer) clearTimeout(this.timer)
                }
            }
        }
    },
    methods: {
        selectCompany(item){
            this.companyText = item.company_name
            this.companyList = []
            this.isCompanyListPopShow = false
            this.isSelected = true
        },
        searchCompany(){
            console.log(this.companyText,this.companyText.length)
            if(this.companyText.length >= 4){
                if(this.timer) clearTimeout(this.timer)
                this.loading = true
                this.timer = setTimeout(()=>{
                    this.searchCompanyFun()
                    this.timer = null
                },200)
            }
        },
        searchCompanyFun(){
            searchCompanyList({keywords: this.companyText}).then(res=>{
                if(res.code == 200){
                    this.loading = false
                    this.companyList = res.data.data
                    this.isCompanyListPopShow = true
                }else{
                    this.loading = false
                    this.$toast(res.msg)
                }
            }).catch(err=>{
                console.log(err)
                console.log(err.code)
                if(err.code == 'ECONNABORTED'){
                    this.$toast('请求超时,请稍后重试')
                }
            })
        },
        clearFun(){
            this.companyList = []
            this.companyText = ''
            this.isSelected = false
        },
        checkedChange(e){
            console.log(e)
        },
        clearText(){
            this.telNum = ''
        },
        checkTelNum(){
            let telNum = this.telNum
            if (telNum == '') {
                this.$toast('请输入您的手机号')
                return false
            }
            let myreg = /^[1][0-9]{10}$/;
            if (!myreg.test(telNum)) {
                this.$toast('请输入正确的手机号')
                return false
            }
            return true
        },
        getCode(){
            if(!this.checkTelNum()){
                return
            }
            if(!this.checked){
                this.$toast('请同意隐私保护政策和用户使用协议')
                return
            }
            if(!this.canSend){
                return
            }
            this.noDoubleTapFun(() => {
                let obj = {
                    mobile: this.telNum,
                    type: 5
                }
                getSmsCode(obj).then(res=>{
                    console.log(res)
                    if(res.code == 200){
                        let timer = setInterval(() => {
                            if (this.time > 1 && this.time <= 60) {
                                this.time = this.time - 1
                                this.canSend = false
                                this.timeText = `${this.time} s后获取`
                            } else {
                                this.time = 60
                                this.canSend = true
                                this.timeText = `获取验证码`
                                clearInterval(timer)
                            }
                        }, 1000)
                    }else{
                        this.$toast(res.msg)
                    }
                })
            });
        },
        async register(){
            if(['invite_channel_agent'].indexOf(this.channelType) != -1){
                if(!this.isSelected){
                    return
                }
                if(this.companyText == ''){
                    this.$toast('请选择您的公司')
                    return
                }
            }
            if(!this.checkTelNum()){
                return
            }
            if(this.code == ''){
                this.$toast('请输入验证码')
                return
            }
            if(!this.checked){
                this.$toast('请同意隐私保护政策和用户使用协议')
                return
            }
            let checkQuery = {
                channel_id: this.channel_id,
                cell: this.telNum,
                hosp_name: this.companyText
            }
            let checkRes = await promotionSaveCheck(checkQuery)
            if(checkRes.code == 200){
                let {code,msg,data} = checkRes.data
                if(code == 80000){
                    this.saveFun(code)
                    return
                }
                if(code == 80001){
                    this.changePersonPopTitle = msg
                    this.isChangePersonPopShow = true
                    this.newPerson = data.after
                    this.oldPerson = data.current
                    return
                }
                if(code == 80002){
                    this.$toast(msg)
                    return
                }
                if(code == 0){
                    this.saveFun(code)
                }
            }else{
                this.$toast(checkRes.msg)
                return
            }
            
        },
        saveFun(paramsCode){
            let obj = {
                cell: this.telNum,
                valid_code: this.code,
                scan_id: this.scanId,
                channel_id: this.channel_id,
                hosp_name: this.companyText,
                source: 'scan'
            }
            promotionSave(obj).then(res=>{
                if(res.code == 200){
                    if(paramsCode == 0 && this.channelType == 'doc_intro_distribution'){
                        this.bindManagerPerson = res.data
                        this.isSuccessPopShow = true
                        return
                    }
                    this.$toast('开通成功，下载app立即使用吧')
                    setTimeout(()=>{
                        if(isWeixin){
                            this.isOpenAppShow = true
                        }else{
                            window.location.href = "https://patient-h5.zz-med.com/static/doc/View/download/Index.html"
                        }
                    },1500)
                }else{
                    this.$toast(res.msg)
                }
            })
        },
        getWxConfig(){
            // let url = `${window.location.href}&event_source=card_share`
            let url = window.location.href.split('#')[0]
            let obj = {
                url,
                account: 'ihec'
            }
            axios.post(`${process.env.VUE_APP_BASE_URL}api/v1/wechat/js_api/sign`,obj).then(res=>{
            // getWxConfig(obj).then(res=>{
                if(res.data.status == 0){
                    let {appId,timestamp,nonceStr,signature} = res.data.data
                    wx.config({
                        debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
                        appId: appId, // 必填，公众号的唯一标识
                        timestamp: timestamp, // 必填，生成签名的时间戳
                        nonceStr: nonceStr, // 必填，生成签名的随机串
                        signature: signature, // 必填，签名
                        jsApiList: [
                            "onMenuShareAppMessage"
                        ],
                        openTagList: ["wx-open-launch-app"]
                    })
                    wx.ready(function () {   //需在用户可能点击分享按钮前就先调用
                        wx.onMenuShareAppMessage({
                            title: '下载医生工作室App',
                            desc: '医生工作室，为医生搭建专业的患者管理、科室管理、个人工作管理平台', // 分享描述
                            link: url, // 分享链接，该链接域名或路径必须与当前页面对应的公众号 JS 安全域名一致
                            imgUrl: 'https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/guoshou/logo.png', // 分享图标
                            success: function () {
                                console.log('成功')
                            }
                        })
                    })
                    wx.error(function (res) {
                        // alert('wx.err')
                        // alert(JSON.stringify(res))
                    })
                    this.openAppListener()
                }
            })
        },
        openAppListener(){
            const btn = document.getElementById('openAppBtn');
            btn.addEventListener('launch', function(e){
                console.log('launch')
                console.log(JSON.stringify(e))
                console.log(JSON.stringify(e.detail))
                // alert('launch')
                // alert(JSON.stringify(e.detail))
            });
            btn.addEventListener('error', function(e){
                console.log('error')
                console.log(e.detail)
                setTimeout(() => {
                    window.location.href = "https://patient-h5.zz-med.com/static/doc/View/download/Index.html"
                },2000)
                console.log(e.detail.errMsg)
                // alert('error')
                // alert(e.detail.errMsg)
            });
        },
        afterScanFun(channel_id){
            promotionAfterScan({channel_id}).then(res=>{
                if(res.code == 200){
                    this.scanId = res.data.scan_id
                }
            })
        },
        getPromotionConfig(channel_id){
            promotionGetConfig({channel_id}).then(res=>{
                if(res.code == 200){
                    let {hosp_name,channel_type} = res.data.referer_info
                    this.referer_info = res.data.referer_info
                    this.companyText = hosp_name || ''
                    this.channelType = channel_type
                }else{
                    this.isErr = true
                    this.errMsg = res.msg
                }
            })
        },
        goDownloadApp(){
            if(isWeixin){
                this.isOpenAppShow = true
            }else{
                window.location.href = "https://patient-h5.zz-med.com/static/doc/View/download/Index.html"
            }
        },
        init(){
            let url = window.location.href
            let channel_id = this.channel_id = getUrlParams(url,'channel_id')
            this.event_source = getUrlParams(url,'event_source')
            this.afterScanFun(channel_id)
            this.getPromotionConfig(channel_id)
            // this.getPromotionPersonInfo(channel_id)
            this.getWxConfig()
        },
        /**
         * 防止多次点击
         * @param {Function} fn 执行函数
         */
        noDoubleTapFun(fn) {
            if (this.noDoubleTap) {
                this.noDoubleTap = false
                setTimeout(() => {
                    this.noDoubleTap = true
                }, 5000);
                fn();
            }
        }
    },
    created() {
        this.init()        
    }
}
</script>

<style lang="scss">
.promotionDocAppOuter{
    .promotionDocApp{
        width: 100%;
        height: 100vh;
        position: relative;
        .video{
            width: 100%;
            // height: 194px;
            box-sizing: border-box;
        }
        .mainOuter{
            margin-top: 36px;
            padding: 0 22px;
            .dotTitle{
                display: flex;
                align-items: center;
                justify-content: flex-start;
                .dot{
                    width: 3px;
                    height: 21px;
                    border-radius: 40px;
                    background: linear-gradient(180deg, #F7830D 0%, rgba(247, 131, 13, 0.00) 100%);
                }
                .titleText{
                    color: #000;
                    font-size: 20px;
                    font-weight: 600;
                    margin-left: 4px;
                    display: flex;
                    align-items: center;
                    font-family: "PingFang SC";
                }
                .textCut{
                    max-width: 120px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }
            }
            .subTitle{
                color: #494949;
                font-size: 15px;
                font-weight: 400;
                margin-top: 6px;
                text-align: left;
            }
            .inputInner{
                input{
                    color: #000;
                    font-size: 17px;
                    font-weight: 400;
                    padding: 0 16px;
                    box-sizing: border-box;
                }
                .input1,.input2,.companyInput{
                    width: 100%;
                    height: 48px;
                    line-height: 48px;
                    border-radius: 8px;
                    background: #F1F1F1;
                }
                .companyBlock{
                    position: relative;
                    .companyLine{
                        position: relative;
                        margin-top: 32px;
                        .rightIcon{
                            position: absolute;
                            right: 16px;
                            top: 16px;
                            transform: rotate(90deg);
                        }
                    }
                    .companyList{
                        position: absolute;
                        z-index: 99;
                        width: 100%;
                        height: 260px;
                        border-radius: 8px;
                        background: white;
                        top: 56px;
                        overflow-y: auto;
                        .companyItem{
                            padding: 12px 15px;
                            font-size: 15px;
                            color: #666666;
                            text-align: left;
                        }
                    }
                }
                .input1{
                    // margin-top: 32px;
                }
                .inputLine2,.input1{
                    position: relative;
                    margin-top: 24px;
                }
                .input1::-webkit-input-placeholder,.input2::-webkit-input-placeholder,.companyInput::-webkit-input-placeholder {
                    color: #989898;
                    font-size: 17px;
                    font-weight: 400;
                }
                .time{
                    position: absolute;
                    height: 48px;
                    line-height: 48px;
                    right: 16px;
                    color: #F7830D;
                    font-size: 17px;
                    font-weight: 500;
                }
                .canSend{
                    color: #F7830D;
                }
                .noSend{
                    color: #989898;
                }
                .closeIcon{
                    position: absolute;
                    top: 12px;
                    right: 16px;
                    width: 24px;
                    height: 24px;
                }
            }
            .joinBtn{
                border-radius: 8px;
                background: #F7830D;
                font-size: 18px;
                font-weight: 500;
                width: 100%;
                height: 52px;
                line-height: 52px;
                margin-top: 24px;
                color: white;
            }
            .agreement{
                color: #181818;
                font-size: 12px;
                font-weight: 400;
                width: max-content;
                display: flex;
                align-items: center;
                margin-top: 24px;
                .agreementText{
                    margin-left: 5px;
                    margin-top: 2px;
                }
                a{
                    font-size: 12px;
                    font-weight: 400;
                    color: #181818;
                    text-decoration: underline;
                    text-underline-offset: 2px;
                }
                .van-icon {  
                    border: 1px solid #181818;  
                }
            }
        }
        .van-checkbox__icon--checked .van-icon{
            background: #F7830D;
            border-color: #F7830D !important;
        }
    }
    .changePersonPop{
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 14px 24px;
        .name{
            font-size: 15px;
            color: #0A0A0A;
            line-height: normal;
        }
        .tel{
            color: #666666;
            font-size: 13px;
            line-height: normal;
        }
        img{
            width: 40px;
            height: 40px;
        }
    }
    .changeSuccessPop{
        display: flex;
        flex-direction: column;
        align-items: center;
        img{
            width: 60px;
            height: 60px;
        }
    }
    .openAppPop{
        .inner{
            position: fixed;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
        }
    }
}
.errMsg{
    margin-top: 30%;
    display: block;
    color: #666666;
    font-size: 18px;
    padding: 0 10px;
}
</style>