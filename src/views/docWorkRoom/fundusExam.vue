<!--
 * @Descripttion: 眼底检查
 * @version:
 * @Author: guxiang
 * @Date: 2021-12-16 14:13:26
 * @LastEditors: guxiang
 * @LastEditTime: 2021-12-16 14:13:26
-->
<template>
  <div class="content">
    <van-list
            v-model="loading"
            :finished="finished"
            :immediate-check="false"
            finished-text="没有更多了"
            @load="getEyebaseListsFun"
            offset="300"
      >
      <div
        v-for="(item, index) in list"
        :key="item.id"
      >
        <div class="title" @click="getEyebaseDetailFun(index, item)">
          <div class="left">眼底检查</div>
          <div class="right">
            {{ item.measure_at }}
            <van-icon v-if="item.extend" name="arrow-up" />
            <van-icon v-if="!item.extend" name="arrow-down" />
          </div>
        </div>
        <div v-if="item.extend" class="result">
          <h3>第一组</h3>
          <div class="funduspic">
            <div>
              <div class="fundusPicTitle">左眼照片(L)</div>
              <div class="fundusImg">
                <img
                  @click="showBigImgFun(0, item.imgs)"
                  class="picture"
                  v-if="item.imgs[0]"
                  :src="item.imgs[0]"
                  alt=""
                />
                <img v-else src="@/assets/images/noPic.png" alt="" />
              </div>
            </div>
            <div>
              <div class="fundusPicTitle">右眼照片(R)</div>
              <div class="fundusImg">
                <img
                  @click="showBigImgFun(1, item.imgs)"
                  class="picture"
                  v-if="item.imgs[1]"
                  :src="item.imgs[1]"
                  alt=""
                />
                <img v-else src="@/assets/images/noPic.png" alt="" />
              </div>
            </div>
          </div>
          <h3>第二组</h3>
          <div class="funduspic">
            <div>
              <div class="fundusPicTitle">左眼照片(L)</div>
              <div class="fundusImg">
                <img
                  @click="showBigImgFun(2, item.imgs)"
                  class="picture"
                  v-if="item.imgs[2]"
                  :src="item.imgs[2]"
                  alt=""
                />
                <img v-else src="@/assets/images/noPic.png" alt="" />
              </div>
            </div>
            <div>
              <div class="fundusPicTitle">右眼照片(R)</div>
              <div class="fundusImg">
                <img
                  @click="showBigImgFun(3, item.imgs)"
                  class="picture"
                  v-if="item.imgs[3]"
                  :src="item.imgs[3]"
                  alt=""
                />
                <img v-else src="@/assets/images/noPic.png" alt="" />
              </div>
            </div>
          </div>
          <h3>AI分析结果</h3>
          <div
            class="textarea"
            readonly
            rows="3"
            v-html="AIresult(index)"
          >
          </div>
          <h3>人工复核结果</h3>
          <div v-html="recheckResult(index)" class="textarea" readonly rows="3">
          </div
          >
        </div>

      </div>
    </van-list>




  </div>
</template>

<script>
const UA = navigator.userAgent;
const isWeixin = UA.indexOf("micromessenger") !== -1 || UA.indexOf("MicroMessenger") !== -1;    // 微信

import { getEyebaseLists, getEyebaseDetailApi } from "@/api/saas.js";
import { ImagePreview } from 'vant';
export default {
  data() {
    return {
      list: [],
      total: 0,
      page: 1,
      limit: 20,
      loading: false,
      finished: false,
    };
  },
  computed: {
    searchInfo() {
        return this.$store.getters['docWorkRoom/searchInfo']
    },
  },
  created() {
    this.getEyebaseListsFun()
  },
  methods: {
    // AI分析结果
    AIresult(index) {
      if (!(this.list[index].data && Object.keys(this.list[index].data).length > 0)) {
        return
      }
      if (
        this.list[index].data &&
        this.list[index].data.left_eye_dignosis == null &&
        this.list[index].data.right_eye_dignosis == null
      ) {
        return "暂无结果";
      } else {
        return (
          "眼底检查（左眼）：" + (this.list[index].data.left_eye_dignosis || '') + "<br/>" +
          "眼底检查（右眼）：" + (this.list[index].data.right_eye_dignosis || '') + "<br/>" +
          "转诊建议：" + (this.list[index].data.recom_transfer || '') + "<br/>" +
          "复查建议：" + (this.list[index].data.recom_reexam || '')
        );
      }
    },
    // 人工复核结果
    recheckResult(index) {
       if (!(this.list[index].data && Object.keys(this.list[index].data).length > 0)) {
        return
      }
      let eyesCheck = [
        "无明显视网膜病变",
        "轻度非增值期视网膜病变",
        "中度非增值期视网膜病变",
        "增值期视网膜病变",
        "重度非增值期视网膜病变",
        "无法判断",
      ];
      if (
        this.list[index].data && this.list[index].data.recheck_result &&
        this.list[index].data.recheck_result.left_eye == null &&
        this.list[index].data.recheck_result.right_eye == null &&
        this.list[index].data.recheck_result.describe == null
      ) {
        return "暂无数据";
      } else {
        return (
          "眼底检查（左眼）：" +
          (eyesCheck[this.list[index].data.recheck_result.left_eye - 1] || '') +
          "<br/>" +
          "眼底检查（右眼）：" +
          (eyesCheck[this.list[index].data.recheck_result.right_eye - 1] || '') +
          "<br/>" +
          "眼底检测描述：" +
          (this.list[index].data.recheck_result.describe
            ? this.list[index].data.recheck_result.describe
            : "")
        );
      }
    },
    // 获取眼底检查列表
    async getEyebaseListsFun () {
      if (this.list.length < this.total) {
          this.page += 1
      }
      let res = await getEyebaseLists({
        patient_id: this.$route.query.patient_id,
        limit: this.limit,
        page: this.page,
        // ...this.searchInfo
      })
      if (res.status == 200) {
          this.total = res.data.total
          let temp = res.data.data.map(item => {
              item.extend = false
              item.data = {}
              item.imgs = []
              return item
          })
          for (let i = 0; i < temp.length; i++) {
              this.list.push(temp[i])
          }
          this.loading = false
          if (this.list.length >= this.total) {
              this.finished = true
          }
      } else {
          this.$toast(res.msg)
      }
    },
    // 获取眼底检查详情
    async getEyebaseDetailFun(index, item) {
      this.list[index].extend = !this.list[index].extend
      if (!this.list[index].extend || this.list[index].data && Object.keys(this.list[index].data).length > 0) {
          return
      }
      let res = await getEyebaseDetailApi({
        patient_id: this.$route.query.patient_id,
        project_id: this.$route.query.project_id,
        date: item.measure_at,
        id: item.id
      });
      if (res.status == 200) {
        if (res.data == null) {
          return
        }
        this.list[index].data = res.data;
        this.list[index].imgs = [
          res.data.eye_pictures_one.left_eye,
          res.data.eye_pictures_one.right_eye,
          res.data.eye_pictures_two.left_eye,
          res.data.eye_pictures_two.right_eye,
        ];
      } else {
        this.$toast(res.msg);
      }
    },
    // 显示大图
    showBigImgFun(index, imgs) {
      const UA = navigator.userAgent;
      const isAndroid = UA.indexOf("Android") > -1 || UA.indexOf("Adr") > -1; // android终端
      const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); // ios终端
      let param = JSON.stringify({
        index,
        imgs
      });
      if (isWeixin) {
        ImagePreview({
          images: imgs,
          startPosition: index,
        });
        return
      }
      // 原生app方法名称：showLargeImage
      if (isAndroid) {
        window.android.showLargeImage(param);
      } else if (isIOS) {
        window.webkit.messageHandlers.showLargeImage.postMessage(param);
      } else {
        // Toast({
        //   message: "无法放大图片",
        //   type: "html",
        //   forbidClick: true,
        //   duration: 1000,
        // });

      }
    },
  },
};
</script>

<style lang="scss" scoped>
.content {
  background: #ffffff;
  font-size: 14px;
  .title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    // margin-top: 15px;
    // margin-bottom: 18px;
    border-bottom: 1px solid #f1f1f1;
    padding: 15px 19px;
    .left {
      font-size: 16px;
      color: #333333;
    }
    .right {
      font-size: 12px;
      color: #666666;
      font-weight: 400;
      &:hover {
        cursor: pointer;
      }
    }
  }
  .result {
    border-bottom: 1px solid #f1f1f1;
    padding: 0 25px;
    h3 {
      text-align: left;
      padding-top: 8px;
      font-weight: 500;
      color: #333333;
    }
    .funduspic {
      margin-top: 12px;
      margin-bottom: 14px;
      text-align: center;
      display: flex;
      justify-content: space-between;
      .fundusPicTitle {
        color: #f67710;
      }
      .fundusImg {
        margin-top: 7px;
        border-radius: 4px;
        width: 155px;
        height: 155px;
        background: #e2e2e2;
        display: flex;
        align-items: center;
        justify-content: center;
        .picture {
          border-radius: 4px;
          width: 155px;
          height: 155px;
        }
      }
    }
    .textarea {
      resize: none;
      border: 0px;
      margin-top: 6px;
      margin-bottom: 12px;
      padding: 9px 15px;
      background: #f9f9f9;
      color: #999999;
      width: calc(100% - 30px);
      border-radius: 4px;
      text-align: left;
      line-height: 20px;
    }
  }
}
</style>
