<!--
 * @Descripttion: 
 * @version: 
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-15 15:15:43
 * @LastEditors: liuqiannan
 * @LastEditTime: 2021-07-27 15:28:15
-->
<template>
    <div class="wrapper">
        <van-collapse 
            v-model="activeNames"
            @change="changeFun"
            :accordion="true"
            >
            <van-collapse-item 
                v-for="(item, index) in list"
                :key="index"
                :name="item.id"
            >
                <template #title>
                    <div class="title">
                        <span class="left">{{ item.name }}</span>
                        <span class="right">{{ item.visit_at }}</span>
                    </div>
                </template>
                <div class="content">
                    <h3>参数</h3>
                    <table class="table">
                        <tr>
                            <td>项目</td>
                            <td>Rb<br/>(右上臂)</td>
                            <td>Lb<br/>(左上臂)</td>
                            <td>Ra<br/>(右脚踝)</td>
                            <td>La<br/>(左脚踝)</td>
                        </tr>
                        <tr>
                            <td>SYS<br/>(收缩压，四肢)</td>
                            <td>{{ data.RbSYS}}</td>
                            <td>{{ data.LbSYS }}</td>
                            <td>{{ data.RaSYS}}</td>
                            <td>{{ data.LaSYS }}</td>
                        </tr>
                        <tr>
                            <td>MAP<br/>(平均动脉压力，四肢)</td>
                            <td>{{ data.RbMAP }}</td>
                            <td>{{ data.LbMAP }}</td>
                            <td>{{ data.RaMAP }}</td>
                            <td>{{ data.LaMAP }}</td>
                        </tr>
                        <tr>
                            <td>DIA<br/>(舒张压，四肢)</td>
                            <td>{{ data.RbDIA }}</td>
                            <td>{{ data.LbDIA }}</td>
                            <td>{{ data.RaDIA }}</td>
                            <td>{{ data.LaDIA }}</td>
                        </tr>
                        <!--
                            929027  PP（脉压差，四肢）Rb（右上臂
                            929028  PP（脉压差，四肢）Lb（左上臂）
                            929029  PP（脉压差，四肢）Ra（右脚踝）
                            929030 PP（脉压差，四肢）La（左脚踝）
                        -->
                        <tr>
                            <td>PP<br/>(脉压差，四肢)</td>
                            <td>{{ (data.RbSYS - data.RbDIA).toFixed(1) }}</td>
                            <td>{{ (data.LbSYS - data.LbDIA).toFixed(1) }}</td>
                            <td>{{ (data.RaSYS - data.RbDIA).toFixed(1) }}</td>
                            <td>{{ (data.LaSYS - data.LaDIA).toFixed(1) }}</td>
                        </tr>
                    </table>
                    <table class="table">
                        <tr>
                            <td>项目</td>
                            <td>结果</td>
                            <td>项目</td>
                            <td>结果</td>
                        </tr>
                        <tr>
                            <td>踝肱指数<br/>(ABI)</td>
                            <td>{{ data.Labi }}</td>
                            <td>踝肱指数<br/>(ABI)</td>
                            <td>{{ data.Rabi }}</td>
                        </tr>
                        <tr>
                            <td>臂踝脉搏波传导速度<br/>(BAPWV)</td>
                            <td>{{ data.LbaPWV }}</td>
                            <td>臂踝脉搏波传导速度<br/>(BAPWV)</td>
                            <td>{{ data.RbaPWV }}</td>
                        </tr>
                        <tr>
                            <td>趾肱指数<br/>(TBI)</td>
                            <td>{{ data.Ltbi}}</td>
                            <td>趾肱指数<br/>(TBI)</td>
                            <td>{{ data.Rtbi }}</td>
                        </tr>
                        <tr>
                            <td>心率<br/>(HR)</td>
                            <td colspan="3">{{ data.HR }}</td>
                            <!-- <td></td> -->
                            <!-- <td>心率<br/>(HR)</td>
                            <td>{{ data.HR }}</td> -->
                        </tr>
                    </table>
                    <div class="btn-wrap">
                        <van-button @click="lookCompleteReportFun(item.id)">
                            查看完整报告
                        </van-button>
                    </div>
                </div>
            </van-collapse-item>
        </van-collapse>
    </div>
</template>

<script>
import { auxiliaryExaminationFun, reportDetailFun } from '@/api/operationReport.js'
export default {
    data () {
        return {
            activeNames: [],
            list: [],
            data: {}
        }
    },
    created () {
        this.auxiliaryExaminationFun()

    },
    methods: {
        // 辅助检测 --- 四肢血压/动脉硬化检测
        auxiliaryExaminationFun () {
            auxiliaryExaminationFun({
                user_id: this.$route.query.user_id
            }).then(res => {
                if (res.code === 200) {
                    let arr = res.data.list.filter(item => item.type === 100)
                    this.list = arr && arr[0] && arr[0].children
                } else {
                    this.$toast(res.msg)
                }
            })
        },
        // 完整档案 --- 动脉硬化检测详情（患者医院数据）
        reportDetailFun (id) {
            reportDetailFun({
                user_id: this.$route.query.user_id,
                id: id
            }).then(res => {
                if (res.code === 200) {
                    this.data = res.data
                } else {
                    this.$toast(res.msg)
                }
            })
        },
        // 面板展开折叠事件
        changeFun (id) {
            if (id) {
                this.reportDetailFun(id)
            }
        },
        // 查看完整报告
        lookCompleteReportFun (id) {
            const UA = navigator.userAgent
            const isAndroid = UA.indexOf('Android') > -1 || UA.indexOf('Adr') > -1 // android终端
            const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)// ios终端
            let user_id = this.$route.query.user_id
            let param = `/docWorkRoom/completeReport?user_id=${user_id}&id=${id}`
            if (isAndroid) {
                window.android.completeReportMessage(param)
            }
            if (isIOS) {
                window.webkit.messageHandlers.completeReportMessage.postMessage(param)
            }
            // this.$router.push({
            //     path: '/docWorkRoom/completeReport',
            //     query: {
            //         user_id: this.$route.query.user_id,
            //         id: id
            //     }
            // })
        }
    }
}
</script>

<style lang="scss" scoped>
.wrapper {
    background: #ffffff;
    font-size: 14px;
    text-align: left;
    .title {
        display: flex;
        justify-content: space-between;
        .left {
            font-size: 16px;
            color: #333;
        }
        .right {
            font-size: 12px;
            color: #666;
        }
    }
    ::v-deep .van-collapse-item__content {
        padding: 16px 0;
    }
    .content {
        font-size: 14px;
        color: #333333;
        font-weight: 500;
        h3 {
            margin-bottom: 16px;
            padding-left: 19px;
        }
        .btn-wrap {
            text-align: center;
        }
        .van-button {
            border-color: #EE7800;
            border-radius: 18px;
            color: #EE7800;
        }
        .table {
            width: 100%;
            margin-bottom: 20px;
            border: 1px solid #F1F1F1;
            tr {
                border: 1px solid #F1F1F1;
                height: 46px;
                &:nth-child(1) {
                    background: #F7F7F7;
                }
                td {
                    border: 1px solid #F1F1F1;
                    vertical-align: middle;
                    text-align: center;
                }
            }
        }
    }
}
</style>