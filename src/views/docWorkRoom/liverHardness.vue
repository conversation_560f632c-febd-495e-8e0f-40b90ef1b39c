<template>
  <div class="content">
    <van-tabs v-model="active">
      <van-tab name="1" title="检查记录">
        <van-list
          v-model="loading"
          :finished="finished"
          :immediate-check="false"
          finished-text="没有更多了"
          @load="getListFunc"
          offset="300"
        >
          <div v-for="(item, index) in list" :key="item.id">
            <div class="title cursor" @click="getDetailFun(index, item)">
              <div :class="[{'active-bold':item.extend==true}]" class="left" v-if=" item.measured_at"> {{measureAtChangeFunc(item.measured_at)[0]}}</div>
              <div v-if=" item.measured_at" class="right unit">
                {{measureAtChangeFunc(item.measured_at)[1]}}
                <van-icon class="unit" v-if="item.extend" name="arrow-up" />
                <van-icon class="unit" v-if="!item.extend" name="arrow-down" />
              </div>
            </div>
            <div v-if="item.extend" class="result">
              <div class="flex">
                <div class="word">脂肪衰减</div>
                <div class="word-sub">
                  <abnormalIndicator
                    code="cap"
                    :record="item"
                    :codeValue="item.cap"
                  />
                  <span class="unit" style="margin-left: 4px;">dB/m</span>
                </div>
              </div>
              <div class="flex">
                <div  class="word">肝脏硬度</div>
                <div  class="word-sub">
                  <abnormalIndicator
                    code="stiffness"
                    :record="item"
                    :codeValue="item.stiffness"
                  />
                  <span class="unit" style="margin-left: 4px;">kPa</span>
                </div>
              </div>
              <div class="flex-comments">
                <div  class="word" style="flex: 1">结论</div>
                <div class="word-sub" v-html="item.comments"></div>
              </div>
              <!--报告-->
              <report-item 
                  v-if="item.file_paths && item.file_paths.length > 0"
                  :fileList="item.file_paths" 
                  pageName="肝脏瞬时弹性硬度检测"
                  pageType="liver_hardness">
              </report-item>
            </div>
          </div>
        </van-list>
      </van-tab>
      <van-tab name="2" title="趋势图">
        <!-- <div class="setflex">
         <div class="date-class" @click="handleDateChange(item.id)" :class="[{active: activeDate == item.id} , {ml: item.class=='mrleft'} ,  {mr: item.class=='mrright'}]" v-for="(item) in dateList" :key="item.id">{{ item.name }}</div>
        </div> -->

        <div class="result-chart">
          <div class="result-title">结果统计</div>
<!--          <div @click="handleAllScreen" class="result-allscreen">-->
<!--            <img class="mobile-png" src="./imgs/mobile.png" alt="">-->
<!--            全屏-->
<!--          </div>-->
        </div>
        <liverHardnessChart :range_date="activeDate" ></liverHardnessChart>
      </van-tab>
    </van-tabs>
  </div>
</template>

<script>
import liverHardnessChart from './liverHardnessChart.vue'
import reportItem from './sickerMedicalRecord/components/reportItem.vue'
import { getLiverLists } from '@/api/saas.js'
const UA = navigator.userAgent;
const isAndroid = UA.indexOf("Android") > -1 || UA.indexOf("Adr") > -1; // android终端
const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); // ios终端
import abnormalIndicator from './sickerMedicalRecord/components/abnormalIndicator.vue';

export default {
  components: {
    liverHardnessChart,
    reportItem,
    abnormalIndicator
  },
  data() {
    return {
      active: "1", // 1 检查记录 还是 2 心率分布图
      dateList: [{
        id: '6m' ,
        name:'近半年',
        class: 'mrleft'
      },{
        id: '1y' ,
        name:'近一年',
        class: ''
      },{
        id: '2y' ,
        name:'近两年',
        class: ''
      },{
        id: '3y' ,
        name:'近三年',
        class: 'mrright'
      }],
      activeDate: 0, //默认0 为全部
      list: [],
      loading: false,
      finished: true,
    };
  },
  computed: {
    searchInfo() {
        return this.$store.getters['docWorkRoom/searchInfo']
    },
  },
  methods: {
    handleDateChange(id){
      if(id == this.activeDate){
        this.activeDate = 0
      }else{
        this.activeDate = id
      }
    },
    async getListFunc() {
      let {patient_id} = this.$route.query;

      let {data} =  await getLiverLists({
          patient_id, 
        //...this.searchInfo
        })
      this.list = data;
      this.list.forEach(i=>{
        this.$set(i , 'extend' , false)
        this.$set(i , 'comments' , i.comments ? i.comments.replace(/\n|\r|\↵/g, '<br />') : '')
      })

    },

    measureAtChangeFunc(time){
      return time.split(' ')

    },
    getDetailFun(index) {
      this.list[index].extend = !this.list[index].extend;
    },
    handleAllScreen(){
      // this.$toast('全屏方法')
      let patient_id = this.$route.query.patient_id;
      let param = `/docWorkRoom/ecgChartScreen?id=${1}&patient_id=${patient_id}`;
      if (process.env.NODE_ENV === "production") {
        if (isAndroid) {
         window.android.showLandScape(param);
        }
        if (isIOS) {
          window.webkit.messageHandlers.showLandScape.postMessage(param);
        }
      }
      if (process.env.NODE_ENV === "development") {
        this.$router.push({
          path: "/docWorkRoom/ecgChartScreen",
          query: {
            id: 1,
            patient_id: this.$route.query.patient_id,
          },
        });
      }
    }
  },
  mounted() {
    this.getListFunc();
  },
};
</script>

<style lang="scss" scoped>
.content {
  font-size: 14px;

  .title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 16px;
    padding: 16px 16px;
    border-bottom: 1px solid #F1F1F1;


  }

  .right{
    font-size: 14px;
  }

  .result{
    font-size: 12px;

  }
  .flex{
    display: flex;
    justify-content: space-between;
        padding: 14px 32px;
    border-bottom: 1px solid #F1F1F1 ;
    background: #FBFBFB;

    .word{
      font-size: 16px;
    }

    .word-sub{
      font-size: 14px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }

  .flex-comments{
    padding: 14px 32px;
    border-bottom: 1px solid #F1F1F1 ;
    background: #FBFBFB;
    text-align: left;

    .word{
      font-size: 16px;
      margin-bottom: 14px;
    }

    .word-sub{
      font-size: 14px;
      line-height: 18px;
    }
  }

  .unit{
    color: #909399;
  }
}
.cursor{
  cursor: pointer;
}


.setflex{
  display: flex;
  justify-content: space-between;
  margin: 20px 0;

}

::v-deep  .van-tab__text{
  font-size: 16px;
}

.result{
  // display: flex;
  // justify-content: space-between;


}
.date-class{
  color: #999;
  padding: 4px 12px;
  border: 1px solid #999;
  border-radius: 4px;
}

.ml{
  margin-left: 16px;
}
.mr{
  margin-right: 16px;
}

.result-chart{
  display: flex;
  justify-content: space-between;
  margin-top: 20px;

  .result-title{
    margin-left: 16px;
  }
  .result-allscreen{
    margin-right: 16px;

  }
}
//::v-deep .van-tab--active {
//  color: #000000;
//  font-weight: 500;
//}
//::v-deep .van-tabs__line {
//  background-color: #EE7800;
//  width: 33px !important;
//  height: 4px;
//  border-radius: 2px;
//}
.active{
  border: 1px solid #FAAD14;
  color: #FAAD14;
  background: #FCF6EC;
}

.active-bold{
  font-weight: bold;
}
.mobile-png{
  width: 14px;
  height: 14px;
}
</style>
