<template>
  <div class="servicePackageExchange" v-if="isShowPage">
    <img class="bgImg" src="./imgs/topLeftBg.png" alt="" />
    <div class="main flexCenter">
      <div class="inner flexCenter">
        <div class="title flexCenter">
          <img class="wechat" src="./imgs/white-wechat.png" alt="" />
          <span class="bigFont">长按 </span>
          <span class="smallFont"> 二维码，关注公众号</span>
        </div>
        <div class="qrcodeDiv flexColCenter">
          <div v-if="!qrcode_url" class="qrcode"></div>
          <img v-else class="qrcode" :src="qrcode_url" alt="" />
          <span>关注“智众健康”公众号</span>
          <span>享受健康服务</span>
        </div>
      </div>
    </div>
    <div class="bottom">
      <div class="bottomTitleLine flexCenter">
        <img class="blueLine rotate" src="./imgs/blueLine.png" alt="" />
        <span class="bottomTitleText">兑换步骤</span>
        <img class="blueLine" src="./imgs/blueLine.png" alt="" />
      </div>
      <div class="stepDiv">
        <div class="item flexColCenter">
          <img class="img" src="./imgs/step1.png" alt="" />
          <span>扫描兑换码</span>
        </div>
        <div class="item flexColCenter">
          <img class="img" src="./imgs/step2.png" alt="" />
          <span>关注公众号</span>
        </div>
        <div class="item flexColCenter">
          <img class="img" src="./imgs/step3.png" alt="" />
          <span>点击卡片</span>
        </div>
      </div>
    </div>
  </div>
  <van-loading v-else style="margin-top: 30%;"/>
</template>

<script>
import { getTempExchangeCode,getTargetUrl } from "@/api/docWorkRoom.js";
export default {
  data() {
    return {
      qrcode_url: "",
      isShowPage: false
    };
  },
  methods: {
    async init() {
      let self = this
      let { qrcode_id, token } = self.queryURLParams();
      let res = await getTargetUrl({qrcode_id, token})
      if (res.status == 0) {
        const targetPath = res.data.target_path
        if(targetPath.indexOf('docWorkRoom/servicePackageExchange') != -1){
          self.isShowPage = true
        }else{
          window.location.href = targetPath
          return
        }
      }
      getTempExchangeCode(qrcode_id, token).then((res) => {
        if (res.status == 0) {
          self.qrcode_url = res.data.qrcode_url;
        }
      });
    },
    queryURLParams() {
      let path = this.$route.fullPath
      // let path = 'https://patient-h5.zz-med-test.com/docWorkRoom/servicePackageExchange?qrcode_id=32061&token=**********'
      let url = path.split("?")[1];
      let obj = {}; // 声明参数对象
      let arr = url.split("&"); // 以&符号分割为数组
      arr.forEach(item => {
        let tempArr = item.split('=')
        obj[tempArr[0]] = tempArr[1]
      });
      return obj;
    },
  },
  mounted() {
    this.init();
  },
};
</script>

<style lang="scss">
.servicePackageExchange {
  width: 100%;
  height: 100vh;
  background-color: rgb(234, 242, 254);
  box-sizing: border-box;
  padding-top: 80px;
  .bgImg {
    width: 196px;
    height: 284px;
    position: fixed;
    top: 0;
    left: 0;
  }
  .main {
    width: 340px;
    height: 380px;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 20px;
    margin: 0 auto;
    .inner {
      width: 320px;
      height: 360px;
      background-color: white;
      border-radius: 20px;
      position: relative;
      .title {
        width: 320px;
        height: 52px;
        border-radius: 20px 20px 0 0;
        color: white;
        background-color: #00c70a;
        position: absolute;
        top: -30px;
        .wechat {
          width: 26px;
          height: 26px;
        }
        .bigFont {
          font-weight: 500;
          font-size: 24px;
          margin-left: 5px;
        }
        .smallFont {
          font-size: 20px;
          margin-left: 2px;
        }
      }
      .qrcodeDiv {
        margin-top: 15px;
        .qrcode {
          width: 177px;
          height: 177px;
          margin-bottom: 16px;
        }
        span {
          font-size: 18px;
          padding: 4px 0;
        }
      }
    }
  }
  .bottom {
    margin-top: 30px;
    .bottomTitleLine {
      font-weight: 500;
      font-size: 20px;
      color: black;
      .blueLine {
        width: 105px;
        height: 2px;
      }
      .bottomTitleText {
        padding: 0 10px;
      }
    }
    .stepDiv {
      padding: 0 32px;
      margin-top: 15px;
      display: flex;
      justify-content: space-between;
      .item {
        font-size: 17px;
      }
      .img {
        width: 82px;
        height: 82px;
        margin-bottom: 10px;
      }
    }
  }
  .rotate {
    transform: rotate(180deg);
  }
  .flexCenter {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .flexColCenter {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
}
</style>