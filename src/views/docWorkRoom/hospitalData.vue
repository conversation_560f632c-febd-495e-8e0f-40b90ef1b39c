<!--
 * @Descripttion: 医院数据
 * @version:
 * @Author: l<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-27 10:00:42
 * @LastEditors: liu<PERSON><PERSON>n
 * @LastEditTime: 2021-08-18 16:45:58
-->
<template>
    <div class="hospital">
        <div
            v-if="showData.diagnosis"
            @click="lookDiagnoseDetailFun"
            class="content"
            >
            <div class="left">{{showData.diagnosis.name}}</div>
            <div class="right">
                <span class="span">{{ diagnosis }}</span>
                <van-icon name="arrow" />
            </div>
        </div>
        <van-divider v-if="showData.diagnosis" />
        <div @click="medicalRecordFun" class="content" v-if="showData.sicker_medical_record">
          <div class="left">
            {{showData.sicker_medical_record.name}}
          </div>
          <div class="right">
            <van-icon name="arrow" />
          </div>
        </div>
        <van-divider v-if="showData.sicker_medical_record" />
        <div @click="lookCompletePatientExampleFun" class="content" v-if="showData.complete_patient_example">
            <div class="left">
                {{showData.complete_patient_example.name}}
            </div>
            <div class="right">
                <van-icon name="arrow" />
            </div>
        </div>
        <van-divider v-if="showData.complete_patient_example" />
        <div @click="lookPatientReportFun" class="content" v-if="showData.patient_report">
            <div class="left">
                {{showData.patient_report.name}}
            </div>
            <div class="right">
                <van-icon name="arrow"/>
            </div>
        </div>
        <van-divider v-if="showData.patient_report"/>
        <div @click="lookQuestionnaireFun" class="content" v-if="showData.questionnaire">
            <div class="left">
                {{showData.questionnaire.name}}
            </div>
            <div class="right">
                <van-icon name="arrow"/>
            </div>
        </div>
        <van-divider v-if="showData.questionnaire"/>
        <!-- 用药 -->
        <div @click="medicationFun" class="content" v-if="showData.drug_management">
            <div class="left">
                {{showData.drug_management.name}}
            </div>
            <div class="right">
                <van-icon name="arrow"/>
            </div>
        </div>
        <van-divider v-if="showData.drug_management"/>
        <div class="content" v-if="showData.key_index && columns.length">
            <div class="left height">
                {{showData.key_index.name}}
            </div>
            <div class="right">
                <div @click="selectFun('key')" class="select">
                    {{ columns.length ? columns[value].name : '暂无数据' }}<van-icon name="arrow" />
                </div>
            </div>
        </div>
        <div class="btn-group" v-if="showData.key_index && columns.length">
            <div
                @click="btnSelectFun(30)"
                class="item"
                :class="{'btn-selected': + selected == 30}"
                >
                近30天
            </div>
            <div
                @click="btnSelectFun(60)"
                class="item"
                :class="{'btn-selected': + selected == 60}"
                >
                近60天
            </div>
            <div
                @click="btnSelectFun(90)"
                class="item"
                :class="{'btn-selected': + selected == 90}"
                >
                近90天
            </div>
        </div>
        <div v-if="showData.key_index && columns.length" class="content chart" ref="chartRef"></div>
        <van-divider v-if="showData.key_index && columns.length"/>

        <div class="content" style="position: relative;" v-if="isCoreChartShow">
            <div class="left height">
                核心指标趋势
            </div>
            <div class="right" @click="toggleTime">
                <div class="calendarSelect">
                    <img class="calendar" src="./imgs/calendar.png" alt="">
                    <span>{{timeTitle}}</span>
                </div>
                
            </div>
            <div class="calendarOverlay" :style="{'height': calendarPopHeight + 'px'}" v-if="isOverlayShow">
                <div class="time-content">
                    <div v-for="item in cycleList" :key="item.value"
                        class="time-name"
                        :class="{'time-act': time == item.value}"
                        @click="timeClick(item)">
                        <span>{{item.name}}</span>
                    </div>
                    <div class="time-custom" :class="{ 'time-custom-color': start_date != '' }" @click="showcCalendar = true">
                        <div class="time-custom-input">{{start_date || '请选择开始时间'}}</div>
                        <div class="time-custom-line"></div>
                        <div class="time-custom-input">{{end_date || '请选择结束时间'}}</div>
                    </div>
                    <div class="project-btn">
                        <div class="reset" @click="resetClick()">重置</div>
                        <div class="submit" @click="submitClick()">提交</div>
                    </div>
                </div>
            </div>
        </div>
        <template v-if="isCoreChartShow">
            <div class="content" style="margin-top: 20px;">
                <div class="left height">
                    <div class="leftDiv" v-if="coreLabellist.length > 0">
                        <div class="leftDot"></div>
                        {{chartTitle || ''}}
                    </div>
                    <div v-else> </div>
                </div>
                <div class="right">
                    <div @click="selectFun('core')" class="select">
                        {{ coreLabellist.length ? coreLabellist[coreLabelIndex].name : '暂无数据' }}<van-icon name="arrow" />
                    </div>
                </div>
            </div>
            <div v-if="groupType && haveData" class="coreBtn-group">
                    <div
                        @click="groupTypeFunc(1)"
                        class="item"
                        :class="{ active: groupType == 1}">
                        瞬时
                    </div>
                    <div
                        @click="groupTypeFunc(2)"
                        class="item"
                        :class="{ active: groupType == 2}">
                        平均
                    </div>
            </div>
            <div class="content chart" ref="coreChartRef"></div>
        </template>
        
        <!-- <van-overlay :show="isOverlayShow" @click="toggleTime" z-index="99" /> -->
        <van-calendar
        class="search-record-calendar"
        :color="platformColor"
        v-model="showcCalendar"
        type="range"
        :max-range="365"
        :min-date="minDate"
        :max-date="maxDate"
        :allow-same-day="true"
        @confirm="calendarConfirm" ></van-calendar>
    </div>
</template>

<script>
import moment from 'moment'
import { getBpChartApi, getBgChartApi, getTcChartApi, getEnter, getQuotaChart, getDagnosis, getCoreDataList, getCoreDataChartListApi } from '@/api/saas.js'
import echarts from 'echarts'
import {Toast} from "vant";
export default {
    data () {
        return {
            chartTitle: '',
            calendarPopHeight: 0,
            selectType: 'key',  // 因为关键指标初始化会用到selectType，所以默认为key，key：关键指标指标选择项   core：核心指标指标选择项
            unit: null,
            maxYval: null,
            dataValueList: null,
            coreChartData: {},
            haveData: true,
            groupType: null,
            coreChartData: [],  //核心指标-数据
            coreLabellist: [],  //核心指标-指标数组
            coreLabelIndex: 0,  //核心指标当前索引
            searchInfo: {},  //核心指标 搜索条件
            showcCalendar: false,
            start_date: '',
            end_date: '',
            time: '3m',
            cycleList: [
                { value: '1', name: '一周' },
                { value: '2', name: '两周' },
                { value: '3', name: '一个月'},
                { value: '3m', name: '三个月'},
                { value: '6m', name: '半年'},
                { value: '1y', name: '一年'}
                // 7天、15天、一个月（30天）、三个月（90天）、半年（180天）、一年（365天）
            ],
            timeTitle: '时间筛选',
            isOverlayShow: false,
            selected: 30,
            show: false,
            value: 0,
            columns: [],
            showData:{
                sicker_medical_record:false,
                complete_patient_example:false,
                patient_report:false,
                questionnaire:false,
                key_index:false
            },
            diagnosis: '',
            isCoreChartShow: false,  //核心指标数据趋势图 是否显示
        }
    },
    computed:{
        platformColor() {
            return this.$store.getters['docWorkRoom/platformColor']
        }
    },
    created () {
        this.getEnter()
        this.getDiagnosisFun()
    },
    mounted () {
        window['confirmFun'] = (value) => {
            this.confirmFun(value)
        }
        this.setTime()
    },
    methods: {
        // 瞬时/平均 切换
        groupTypeFunc (value) {
            this.groupType = value
            this.getEchartsFunc()
        },
        // 核型数据列表
        async getCoreData () {
            // const toast = Toast.loading({
            //     message: '加载中...',
            //     forbidClick: true,
            //     duration: 0
            // })
            try {
                const { status, data } = await getCoreDataList({
                    patient_id: this.$route.query.patient_id,
                    ...this.searchInfo
                })
                if (status == 200) {
                    this.coreLabellist = data.map((item,index)=>{
                        item.value = index
                        return item
                    })
                    console.log('this.coreLabellist--------------------',this.coreLabellist)
                    this.dealData()
                }
                // toast.clear()
            } catch (e) {
                // toast.clear()
            }
        },
        dealData () {
            if (this.coreLabellist && this.coreLabellist.length > 0) {
                this.getCoreDataChartList(this.coreLabellist[0])
                this.coreLabelIndex = 0
            }else{
                const options = {
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'shadow',
                            lineStyle: {
                                width: 0.5
                            }
                        },
                    },
                    legend: {
                        type: 'scroll',
                        itemWidth: 10,
                        itemHeight: 2,
                        itemGap: 20,
                        icon: 'circle',
                        right: 0,
                        top: 0,
                        formatter: function (params) {
                            return params.split('|')[0]
                        }
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        axisLine: {
                            // show: true
                            lineStyle: {
                                color: 'rgba(0, 0, 0, 0.15)'
                            }
                        },
                        axisTick: {
                            show: true,
                            lineStyle: {
                                color: 'rgba(0, 0, 0, 0.25)'
                            },
                            alignWithLabel: true
                        },
                        axisLabel: {
                            // margin: 16,
                            color: 'rgba(0, 0, 0, 0.45)',
                            formatter: function (value) {
                            return moment(value).format('M/D')
                            }
                        },
                        data: ['06-01','06-02','06-03','06-04','06-05','06-06']
                    },
                    yAxis: [{
                        max: 50,
                        min: 0,
                        type: 'value',
                        axisLabel: {
                            // formatter: '{value}',
                            // margin: 20,
                            show: true,
                            color: 'rgba(0, 0, 0, 0.45)'
                        },
                        axisTick: {
                            show: true,
                            lineStyle: {
                                color: 'rgba(0, 0, 0, 0.15)'
                            }
                        },
                        axisLine: {
                            show: false
                        },
                        splitLine: {
                            show: true,
                            lineStyle: {
                                color: 'rgba(0, 0, 0, 0.15)',
                            }
                        }
                    }],
                    series: [
                        {
                            name: '未知',
                            data: [null,null,null,null,null],
                            type: 'line'
                        }
                    ]
                };
                let myEchart = echarts.init(this.$refs.coreChartRef, 'light')
                myEchart.clear()
                myEchart.setOption(options)
            }
        },
        async getCoreDataChartList ({indicator_group_id, code, indicator_ids, field_type }) {
            // const toast = Toast.loading({
            //     message: '加载中...',
            //     forbidClick: true,
            //     duration: 0
            // })
            try {
                const { patient_id } = this.$route.query
                const { data } = await getCoreDataChartListApi({
                    patient_id,
                    indicator_group_id,
                    measure_type: 0,
                    measure_method: '2',
                    show_detail: code == 'public_template_tab_bs' ? 1 : 0,// 如果是血糖就传1
                    ...this.searchInfo,
                    indicator_ids,
                    field_type
                })
                this.coreChartData = data
                this.getEchartsFunc()
                // toast.clear()
            } catch {
                // toast.clear()
            }
        },
        getEchartsFunc () {
            const data = this.coreChartData
            this.haveData = data.charts && (data.charts.length !== 0)
            this.chartTitle = data.detection_name
            this.unit = data.unit || ''
            const unit = this.unit
            this.maxYval = 0
            this.dataValueList = []
            let myEchart = echarts.init(this.$refs.coreChartRef, 'light')
            if (!this.haveData) {
                return
            }
            const actData = data.charts[0] || {}

            let xAxisData = actData.dates
            let series = []
            let seriesObj = {}

            // group_typ 1 平均
            if (actData.group_type) {
                (!this.groupType) && (this.groupType = 1);
                data.charts.forEach(v => {
                    !seriesObj[v.group_type] && (seriesObj[v.group_type] = [])
                    seriesObj[v.group_type].push(v)
                })

                seriesObj[this.groupType].forEach((v) => {
                    xAxisData = v.dates
                    series.push(this.getSeriesItem(v))
                })

            } else {
                this.groupType = 0
                data.charts.forEach((v) => {
                    series.push(this.getSeriesItem(v))
                })
            }
            if(series[0].data.length == 0){
                series[0].data = new Array(xAxisData.length).fill(null)
            }
            const options = {
                tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow',
                    lineStyle: {
                        width: 0.5
                    }
                },
                formatter: function (params) {
                    let htmlStr = '';
                    for (let i = 0; i < params.length; i++) {
                    let param = params[i];
                    let xName = param.name;
                    let [seriesName, paramunit] = param.seriesName.split('|');
                    let value = param.value;
                    let color = param.color;
                    if (i === 0) {
                        htmlStr += xName + '<br/>'; //x轴的名称
                    }
                    htmlStr += `<div style='width: 100%;display: flex; justify-content: space-between; align-items:center;margin-top: 4px'>
                                <div>
                                    <span style="margin-right:5px;display:inline-block;width:10px;height:10px;border-radius:5px;
                                    background-color:${color};"></span>
                                    ${seriesName}
                                </div>
                                <div>
                                    <span>${value}</span>
                                    ${unit ? '' : `<span style="margin-left:2px;">${paramunit}</span>`}
                                </div>
                                </div>`
                    }
                    htmlStr += '</div>';
                    return htmlStr
                }
                },
                legend: {
                    type: 'scroll',
                    // itemWidth: 15,
                    itemWidth: 10,
                    itemHeight: 10,
                    itemGap: 20,
                    icon: 'circle',
                    // top: 10,
                    // right: 24,
                    // width: '70%',
                    right: 0,
                    top: 0,
                    formatter: function (params) {
                        return params.split('|')[0]
                    }
                },
                grid: {
                    // left: 20,
                    right: 40,
                    // bottom: 11,
                    left: '3%',
                    // right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    axisLine: {
                        // show: true
                        lineStyle: {
                            color: 'rgba(0, 0, 0, 0.15)'
                        }
                    },
                    axisTick: {
                        show: true,
                        lineStyle: {
                            color: 'rgba(0, 0, 0, 0.25)'
                        },
                        alignWithLabel: true
                    },
                    axisLabel: {
                        // margin: 16,
                        color: 'rgba(0, 0, 0, 0.45)',
                        formatter: function (value) {
                        return moment(value).format('M/D')
                        }
                    },
                    data: xAxisData
                },
                yAxis: [{
                    max: this.maxYval,
                    type: 'value',
                    axisLabel: {
                        // formatter: '{value}',
                        // margin: 20,
                        show: true,
                        color: 'rgba(0, 0, 0, 0.45)'
                    },
                    axisTick: {
                        show: true,
                        lineStyle: {
                            color: 'rgba(0, 0, 0, 0.15)'
                        }
                    },
                    axisLine: {
                        show: false
                    },
                    splitLine: {
                        show: true,
                        lineStyle: {
                            color: 'rgba(0, 0, 0, 0.15)',
                        }
                    }
                }],
                series
            };
            console.log('核心指标的图表数据series----------',series)
            myEchart.clear();
            myEchart.setOption(options)
            // window.addEventListener('resize', () => {
                this.$nextTick(() => {
                    myEchart.resize()
                })
            // })
        },
        getSeriesItem (v) {
            this.dataValueList = [...this.dataValueList, ...v.values]
            const { markLine } = this.getMarkLine()
            return {
                markLine,
                connectNulls: true,
                name: `${v.name}|${v.unit}`,
                type: 'line',
                data: v.values,
                symbol: 'circle',
                symbolSize: 12,
                itemStyle: {  
                    color: '#F6BD16'  // 这将改变折线图的颜色  
                },
                lineStyle: {
                    width: 1.5
                },
            }
        },
        getMarkLine () {
            let lineStyle = {
                color: '#ff0000'
            }
            let markLine = { symbolSize: 0, data: [] }
            let maxVal = Math.ceil(Math.max(...this.dataValueList))
            switch (this.coreChartData.function_code) {
                case 'public_template_tab_bp':
                    if (this.coreChartData.code === 'pulse') { // 心率
                        markLine = { symbolSize: 0, data: [
                            { lineStyle, yAxis: 100 },
                            { lineStyle, yAxis: 60 },
                        ] }
                        maxVal = maxVal > 110 ? maxVal : 110
                    }
                    if (this.coreChartData.code.indexOf('sbp,dbp') !== -1) { // 血压
                        markLine = { symbolSize: 0, data: [
                            { lineStyle, yAxis: 140 },
                            { lineStyle, yAxis: 90 },
                        ] }
                        maxVal = maxVal > 150 ? maxVal : 150
                    }
                break;
                case 'public_template_tab_hw': // 身高体重
                    if (this.coreChartData.code === 'bmi') { // bmi
                        markLine = { symbolSize: 0, data: [
                            { lineStyle, yAxis: 24 },
                            { lineStyle, yAxis: 18.5 },
                        ] }
                        maxVal = maxVal > 30 ? maxVal : 30
                    }
                break;
                case 'public_template_tab_lifesigns_blood_oxygen': // 血氧饱和
                    markLine = { symbolSize: 0, data: [
                        { lineStyle, yAxis: 95 },
                        ] }
                    maxVal = 100
                break;
            }
            this.maxYval = maxVal
            return { markLine }
        },
        // 核心指标 时间筛选提交
        submitClick() {
            this.setTime()
            this.isOverlayShow = !this.isOverlayShow
        },
        // 核心指标 时间筛选重置
        resetClick() {
            this.timeClick({ value: '3m' })
            this.setTime()
            this.isOverlayShow = !this.isOverlayShow
        },
        // 核心指标 打开/关闭筛选弹窗
        toggleTime(e){
            console.log(e.pageY,document.documentElement.offsetHeight)
            this.calendarPopHeight = document.documentElement.offsetHeight - e.pageY
            console.log(this.calendarPopHeight)
            this.isOverlayShow = !this.isOverlayShow
        },
        // 核心指标 点击时间节点
        timeClick({ value }) {
            this.time = value
            this.start_date = ''
            this.end_date = ''
        },
        // 核心指标 日历 确认
        calendarConfirm(date) {
            const [start, end] = date;
            this.time = ''
            this.start_date = moment(start).format('YYYY-MM-DD')
            this.end_date = moment(end).format('YYYY-MM-DD')
            this.showcCalendar = false
        },
        // 核心指标 
        setTime(isSearch = true) {
            let start_date = ''
            let end_date = ''
            if (this.time) {
                const { startTime, endTime } = this.getFollowupTime(this.time)
                start_date = startTime
                end_date = endTime
            } else {
                start_date = `${this.start_date.slice(0, 4)}-${this.start_date.slice(5, 7)}-${this.start_date.slice(8, 10)}`
                end_date = `${this.end_date.slice(0, 4)}-${this.end_date.slice(5, 7)}-${this.end_date.slice(8, 10)}`
            }
            // this.$store.dispatch('docWorkRoom/setSearchInfo', {
            //     time: this.time,
            //     start_date,
            //     end_date
            // })
            this.searchInfo = {
                time: this.time,
                start_date,
                end_date
            }
            this.getCoreData()
            this.isSubmit = false
            let title = '时间筛选'
            if (this.time) {
                title = this.cycleList.filter(v => v.value == this.time)[0].name
            } else {
                title = `${this.start_date}-${this.end_date}`
            }
            this.timeTitle = title
        },
        getFollowupTime(type) {
            return {
                '1': { // 一周
                startTime: moment().subtract(7, 'days').format('YYYY-MM-DD'),
                endTime: moment().format('YYYY-MM-DD'),
                },
                '2': { // 两周
                startTime: moment().subtract(14, 'days').format('YYYY-MM-DD'),
                endTime: moment().format('YYYY-MM-DD'),
                },
                '3': { // 一个月
                startTime: moment().subtract(30, 'days').format('YYYY-MM-DD'),
                endTime: moment().format('YYYY-MM-DD'),
                },
                '3m': { // 近90天
                startTime: moment().subtract(90, 'days').format('YYYY-MM-DD'),
                endTime: moment().format('YYYY-MM-DD'),
                },
                '6m':{ // 近半年
                startTime: moment().subtract(180, 'days').format('YYYY-MM-DD'),
                endTime: moment().format('YYYY-MM-DD'),
                },
                '1y': { // 近1年
                startTime: moment().subtract(365, 'days').format('YYYY-MM-DD'),
                endTime: moment().format('YYYY-MM-DD'),
                },
                '2y':{ // 近2年
                startTime: moment().subtract(730, 'days').format('YYYY-MM-DD'),
                endTime: moment().format('YYYY-MM-DD'),
                },
                '3y':{ // 近3年
                startTime: moment().subtract(1095, 'days').format('YYYY-MM-DD'),
                endTime: moment().format('YYYY-MM-DD'),
                },
            }[type]
        },
        // 获取入口
        async getEnter(){
            this.columns = []
            let res = await getEnter({
                project_id: this.$route.query.project_id,
            });
            if (res.status == 200) {
                // 调整数据结构，showData控制展示
                for (let i = 0; i < res.data.length; i++) {
                    if(res.data[i].code == 'hospital_data'){
                        for (let j = 0; j < res.data[i].child.length; j++) {
                            this.showData[res.data[i].child[j].code] = res.data[i].child[j];
                            if (res.data[i].child[j].child.length && res.data[i].child[j].code == 'key_index') {
                             this.columns = res.data[i].child[j].child.map((item, index) => (
                                  {
                                     value: index,
                                     name: item.name,
                                     code: item.code
                                 }
                             ))
                            }
                            if(res.data[i].child[j].code == 'drug_management'){
                                this.medicationTabs = res.data[i].child[j].child
                            }
                        }
                        this.isCoreChartShow = res.data[i].is_checked_core_data  //核心指标趋势图 是否显示
                    }
                }
                // this.getBpChartFun()
               this.columns.length && this.confirmFun(this.value)
            }
        },
        // 获取诊断数据
        async getDiagnosisFun () {
            const { data } = await getDagnosis({
                project_id: this.$route.query.project_id,
                patient_id: this.$route.query.patient_id,
            })
            this.diagnosis = data.diagnosis
        },
        // 诊断
        lookDiagnoseDetailFun () {
            const UA = navigator.userAgent
            const isAndroid = UA.indexOf('Android') > -1 || UA.indexOf('Adr') > -1 // android终端
            const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
            let project_id = this.$route.query.project_id
            let patient_id = this.$route.query.patient_id
            let authorization = this.$route.query.authorization
            let room_id = this.$route.query.room_id
            let param = `/docWorkRoom/diagnoseDetail?project_id=${project_id}&patient_id=${patient_id}&authorization=${authorization}&room_id=${room_id}`
            if (process.env.NODE_ENV === 'production') {
                if (isAndroid) {
                    window.android.patientDiagnosticInfo(param)
                }
                if (isIOS) {
                    window.webkit.messageHandlers.patientDiagnosticInfo.postMessage(param)
                }
            }
            if (process.env.NODE_ENV === 'development') {
                this.$router.push({
                    path: '/docWorkRoom/diagnoseDetail',
                    query: {
                        project_id: project_id,
                        patient_id: patient_id
                    }
                })
            }
        },
        // 完整病例
        lookCompletePatientExampleFun () {
            const UA = navigator.userAgent
            const isAndroid = UA.indexOf('Android') > -1 || UA.indexOf('Adr') > -1 // android终端
            const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
            let project_id = this.$route.query.project_id
            let patient_id = this.$route.query.patient_id
            let authorization = this.$route.query.authorization
            let room_id = this.$route.query.room_id
            let param = `/docWorkRoom/completePatientExample?project_id=${project_id}&patient_id=${patient_id}&authorization=${authorization}&room_id=${room_id}`
          //let param = `/docWorkRoom/sickerMedicalRecord?project_id=${project_id}&patient_id=${patient_id}`
            if (process.env.NODE_ENV === 'production') {
                if (isAndroid) {
                    window.android.completePatientExampleMessage(param)
                }
                if (isIOS) {
                    window.webkit.messageHandlers.completePatientExampleMessage.postMessage(param)
                }
            }
            if (process.env.NODE_ENV === 'development') {
                this.$router.push({
                    path: '/docWorkRoom/completePatientExample',
                    query: {
                        project_id: project_id,
                        patient_id: patient_id
                    }
                })
            }
        },
        // 患者报告
        lookPatientReportFun () {
            const UA = navigator.userAgent
            const isAndroid = UA.indexOf('Android') > -1 || UA.indexOf('Adr') > -1 // android终端
            const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
            let project_id = this.$route.query.project_id
            let patient_id = this.$route.query.patient_id
            let authorization = this.$route.query.authorization
            let room_id = this.$route.query.room_id
            let param = `/docWorkRoom/patientReport?project_id=${project_id}&patient_id=${patient_id}&authorization=${authorization}&room_id=${room_id}`
            if (process.env.NODE_ENV === 'production') {
                if (isAndroid) {
                    window.android.patientReportMessage(param)
                }
                if (isIOS) {
                    window.webkit.messageHandlers.patientReportMessage.postMessage(param)
                }
            }
            if (process.env.NODE_ENV === 'development') {
                this.$router.push({
                    path: '/docWorkRoom/patientReport',
                    query: {
                        project_id: project_id,
                        patient_id: patient_id
                    }
                })
            }
        },
        // 问卷
        lookQuestionnaireFun(){
            const UA = navigator.userAgent
            const isAndroid = UA.indexOf('Android') > -1 || UA.indexOf('Adr') > -1 // android终端
            const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
            let project_id = this.$route.query.project_id
            let patient_id = this.$route.query.patient_id
            let authorization = this.$route.query.authorization
            let room_id = this.$route.query.room_id
            let param = `/docWorkRoom/questionnaire?project_id=${project_id}&patient_id=${patient_id}&authorization=${authorization}&room_id=${room_id}`
            if (process.env.NODE_ENV === 'production') {
                if (isAndroid) {
                    window.android.questionnaireMessage(param)
                }
                if (isIOS) {
                    window.webkit.messageHandlers.questionnaireMessage.postMessage(param)
                }
            }
            if (process.env.NODE_ENV === 'development') {
                this.$router.push({
                    path: '/docWorkRoom/questionnaire',
                    query: {
                        project_id: project_id,
                        patient_id: patient_id
                    }
                })
            }
        },
        // 用药跳转的方法
        medicationFun(){
            const UA = navigator.userAgent
            const isAndroid = UA.indexOf('Android') > -1 || UA.indexOf('Adr') > -1 // android终端
            const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
            let project_id = this.$route.query.project_id
            let patient_id = this.$route.query.patient_id
            let authorization = this.$route.query.authorization
            let param = `/docWorkRoom/medication/index?project_id=${project_id}&patient_id=${patient_id}&authorization=${authorization}&medicationTabs=${JSON.stringify(this.medicationTabs)}&channel=doctor&scene=hosp`
            if (process.env.NODE_ENV === 'production') {
                if (isAndroid) {
                    window.android.questionnaireMessage(encodeURI(param))
                }
                if (isIOS) {
                    window.webkit.messageHandlers.questionnaireMessage.postMessage(encodeURI(param))
                }
            }
            if (process.env.NODE_ENV === 'development') {
                this.$router.push({
                    path: param
                })
            }
        },
        btnSelectFun (param) {
            this.selected = param
            this.getChartFun(this.value)
        },
        selectFun (type) {
            this.selectType = type
            console.log(this.selectType)
            // this.show = true
            if ((type == 'key' && !this.columns.length) || (type == 'core' && !this.coreLabellist.length)) {
            //  this.$toast('请联系管理员！')
             return
            }
            const UA = navigator.userAgent
            const isAndroid = UA.indexOf('Android') > -1 || UA.indexOf('Adr') > -1 // android终端
            const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
            let params = {
                value: type == 'key'?this.value:this.coreLabelIndex,
                columns: type == 'key'?this.columns:this.coreLabellist
            }
            if (process.env.NODE_ENV === 'production') {
                if (isAndroid) {
                    window.android.modalMessage(JSON.stringify(params))
                }
                if (isIOS) {
                    window.webkit.messageHandlers.modalMessageNew.postMessage(params)
                }
            }
        },
        confirmFun (value) {
            console.log('点击的条目value*******selectType',value,this.selectType)
            if(this.selectType == 'key'){
                this.value = parseInt(value)
                // this.show = false
                this.getChartFun(value)
            }else{
                this.coreLabelIndex = parseInt(value)
                console.log('触发加载数据的参数',this.coreLabellist[value])
                this.getCoreDataChartList(this.coreLabellist[value])
            }
        },
        // 关键指标趋势-各数据
        async getChartFun (val) {
            let currentType = this.columns.length && this.columns.find(item => item.value == val).code
            let obj = this.columns.length && this.columns.find(item => item.value == val)
            let legendName = obj.code == 'sbp_dbp' ? ['收缩压', '舒张压'] : obj.code == 'pwv' ? ['LBAPWV', 'RBAPWV'] : obj.code == 'abi' ? ['LABI', 'RABI'] : obj.name
            let res = await getQuotaChart({
                period: this.selected,
                patient_id: this.$route.query.patient_id,
                project_id: this.$route.query.project_id,
                type: currentType
            })
            if (res.status == 200) {
                let data = res.data.y && Object.prototype.toString.call(res.data.y) == '[object Object]' ? {
                    seriesData: [{
                        symbol: 'circle',
                        symbolSize: 12,
                        name: legendName[0],
                        type: 'line',
                        smooth: false,
                        connectNulls: true,
                        itemStyle: {
                            color: '#F6BD16',
                        },
                        lineStyle: {
                            width: 1.5
                        },
                        showAllSymbol: true,
                        data: currentType == 'sbp_dbp' ? res.data.y.sbp : res.data.y.left
                        // data: [11, 98, 11, 67, 11, 43, 11, 43, 89, null, 89, 76, null, 34,89]
                    },
                    {
                        symbol: 'circle',
                        symbolSize: 12,
                        name: legendName[1],
                        type: 'line',
                        smooth: false,
                        connectNulls: true,
                        itemStyle: {
                            color: '#E8684A'
                        },
                        lineStyle: {
                            width: 1.5
                        },
                        showAllSymbol: true,
                        data: currentType == 'sbp_dbp' ? res.data.y.dbp : res.data.y.right
                        // data: [22, 45, 22,78, 22, 45, 22, 45]
                    }],
                    legend:  {
                        data: legendName,
                        right: 0,
                        top: 0,
                        data: [{
                            name: legendName[0],
                            icon: 'rect'
                        }, {
                            name: legendName[1],
                            icon: 'rect'
                        }],
                        itemWidth: 10,
                        itemHeight: 2
                    },
                    xAxisData: res.data.x
                } : {
                    seriesData: [{
                        symbol: 'circle',
                        symbolSize: 12,
                        name: legendName,
                        type: 'line',
                        smooth: false,
                        connectNulls: true,
                        itemStyle: {
                            color: '#F6BD16'
                        },
                        lineStyle: {
                            width: 1.5
                        },
                        showAllSymbol: true,
                        data: res.data.y
                    }],
                    legend: {
                        data: [legendName],
                        right: 0,
                        top: 0,
                        data: [{
                            name: legendName,
                            icon: 'rect'
                        }],
                        itemWidth: 10,
                        itemHeight: 2
                    },
                    xAxisData: res.data.x
                }
                this.initChartFun(data)
            } else {
                this.$toast(res.msg)
            }
        },

        initChartFun (data) {
            echarts.init(this.$refs.chartRef).dispose()
            let max
            if (typeof data.seriesData[0].data.find(value => typeof value != 'object') == 'string' ||
            typeof data.seriesData[0].data.find(value => typeof value != 'object') == 'number') {
                // max = 'dataMax' + 2
            } else {
                max = 50
            }
            let option = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow',
                        lineStyle: {
                            width: 0.5
                        }
                    },
                },
                legend: data.legend,
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                // dataZoom: [{
                //     type: 'inside',
                //     start: 0,
                //     end: 20,
                //     minSpan: 0,
                //     maxSpan: 20
                // }],
                xAxis: {
                    type: 'category',
                    data: data.xAxisData,
                    axisTick: {
                        show: true,
                        lineStyle: {
                            color: 'rgba(0, 0, 0, 0.25)'
                        },
                        alignWithLabel: true
                    },
                    axisLabel: {
                        color: 'rgba(0, 0, 0, 0.45)',
                        // rotate: 90
                        formatter: function(value, index) {
                            return value.replace(/\-/g, '/').slice(5)
                        }
                    },
                    axisLine: {
                        lineStyle: {
                            color: 'rgba(0, 0, 0, 0.15)'
                        }
                    },
                },
                yAxis: {
                    type: 'value',
                    splitLine: {
                        lineStyle: {
                            color: 'rgba(0, 0, 0, 0.15)'
                        }
                    },
                    min: 0,
                    max: max,
                    axisLine: {
                        show: false
                    },
                    axisTick: {
                        show: true,
                        lineStyle: {
                            color: 'rgba(0, 0, 0, 0.15)'
                        }
                    },
                    axisLabel: {
                        show: true,
                        color: 'rgba(0, 0, 0, 0.45)'
                    }
                },
                series: data.seriesData
            }
            let myEchart = echarts.init(this.$refs.chartRef)
            myEchart.setOption(option)
        },
      medicalRecordFun() { // 我的病案
        const UA = navigator.userAgent
        const isAndroid = UA.indexOf('Android') > -1 || UA.indexOf('Adr') > -1 // android终端
        const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
        let project_id = this.$route.query.project_id
        let patient_id = this.$route.query.patient_id
        let authorization = this.$route.query.authorization
        let room_id = this.$route.query.room_id
        let param = `/docWorkRoom/sickerMedicalRecord?project_id=${project_id}&patient_id=${patient_id}&authorization=${authorization}&room_id=${room_id}`
        if (process.env.NODE_ENV === 'production') {
          if (isAndroid) {
            window.android.completePatientExampleMessage(param)
          }
          if (isIOS) {
            window.webkit.messageHandlers.completePatientExampleMessage.postMessage(param)
          }
        }
        if (process.env.NODE_ENV === 'development') {
          this.$router.push({
            path: '/docWorkRoom/sickerMedicalRecord',
            query: {
              project_id: project_id,
              patient_id: patient_id
            }
          })
        }
      }
    }
}
</script>

<style lang="scss" scoped>
$app-color: #F7830D;
$wx-color: #3388FF;
$app-bg-color: #F8F8F8;
$wx-bg-color: #F5F7FB;
.hospital {
    padding: 20px 16px 16px;
    // height: 100vh;
    // background: red;
    .content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #333333;
        font-weight: 600;
        font-size: 16px;
        &:hover {
            cursor: pointer;
        }

        .right {
            color: #ccc;
            display: flex;
            align-items: center;
            .span {
                max-width: 200px;
                display: inline-block;
                text-overflow: ellipsis;
                white-space: nowrap;
                overflow: hidden;
                color: #5A6266;
                font-size: 16px;
            }
            .calendarSelect{
                display: flex;
                align-items: center;
                font-size: 14px;
                color: #F7830D;
                .calendar{
                    width: 16px;
                    height: 16px;
                    margin: -3px 3px 0 0;
                }
            }
        }
        .select {
            width: 145px;
            height: 32px;
            display: inline-flex;
            justify-content: center;
            align-items: center;
            border-radius: 16px;
            border: 1px solid #EE7800;
            font-size: 14px;
            color: #666;
            .van-icon {
                color: #EE7800;
                vertical-align: middle;
            }
        }
        .height {
            display: inline-flex;
            align-items: center;
        }
    }
    .btn-group {
        height: 30px;
        width: 180px;
        font-size: 13px;
        z-index: 1000;
        margin-top: 10px;
        position: absolute;
        display: flex;
        .item {
            width: 60px;
            background: rgba(0, 0, 0, 0.04);
            color: #999;
            display: inline-flex;
            justify-content: center;
            align-items: center;
            &:nth-child(1) {
                border-radius: 4px 0 0 4px;
            }
            &:nth-child(3) {
                border-radius: 0 4px 4px 0;

            }
        }
        .btn-selected {
            background: #EE7800;
            color: #ffffff;
        }
    }
    .chart {
        width: 100%;
        height: 300px;
        margin-top: 20px;
    }
    .leftDiv{
        display: flex;
        align-items:center;
    }
    .leftDot{
        width: 3px;
        height: 15px;
        background: #F7830D;
        border-radius: 10px;
        margin-right: 2px;
        margin-top: -3px;
    }
}
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 15px;
    height: 30px;
    line-height: 30px;
    padding: 0 25px;
    .left {
        color: #999999;
    }
    .right {
        color: #108EE9;
    }

}
.van-popup {
    ::v-deep .van-picker {
        width: 100%;
        .van-picker__cancel {
            color: #999999;
        }
        .van-picker__confirm {
            color: #108EE9;
        }
    }
    .van-divider {
        margin: 0;
    }

}
.calendarOverlay{
    position: absolute;
    top: 0.9375rem;
    width: 100vw;
    left: -16px;
    background: rgba(0, 0, 0, 0.6);
    z-index: 1001;
}
.time-content {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    // border-radius: 8px;
    background: white;
    padding: 0 20px 20px;
    // box-shadow:#ccc 0px 0px 10px;
    .time-name {
        margin-top: 10px;
      display: inline-block;
      width: 105px;
      height: 32px;
      line-height: 32px;
      border-radius: 6px;
      background: #F5F6FB;
      text-align: center;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      color: #5A6266;
    }

    .time-act {
      background: #F5F6FB;
      color: $app-color;
    }

    .time-custom{
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      margin-top: 10px;
      .time-custom-input{
        width: 40%;
        height: 32px;
        line-height: 34px;
        flex-shrink: 0;
        border-radius: 6px;
        background: #F5F6FB;
        text-align: center;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        color: #C0C6CC;
      }
      .time-custom-line{
        width: 11px;
        height: 2px;
        background: #E5E5E5;
      }
    }
    .time-custom-color{
      .time-custom-input{
        color: $app-color;
      }
    }
}
.project-btn{
    display: flex;
    justify-content: space-between;
    width: 100%;
    margin-top: 70px;
    .reset{
      width: 45%;
      height: 44px;
      flex-shrink: 0;
      border-radius: 8px;
      background: $app-bg-color;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 44px;
      color: #0A0A0A;
    }
    .submit{
      width: 45%;
      height: 44px;
      flex-shrink: 0;
      border-radius: 8px;
      background: $app-color;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 44px;
      color: #fff;
    }
}
.coreBtn-group {
        height: 30px;
        width: 180px;
        font-size: 13px;
        z-index: 1000;
        margin-top: 10px;
        position: absolute;
        display: flex;
        .item {
            width: 60px;
            background: rgba(0, 0, 0, 0.04);
            color: #999;
            display: inline-flex;
            justify-content: center;
            align-items: center;
            &:nth-child(1) {
                border-radius: 4px 0 0 4px;
            }
            &:nth-child(2) {
                border-radius: 0 4px 4px 0;

            }
        }
        .active {
            background: #EE7800;
            color: #ffffff;
        }
    }
</style>
