<template>
  <div class="abnormalRecord">
    <van-dropdown-menu class="dropdownMenu" active-color="#F7830D">
      <van-dropdown-item @change="bigTypeChange" v-model="bigTypeChecked" :disabled="bigTypeDisabled" :options="bigTypeList" />
      <van-dropdown-item :title="indicatorText" @close="searchFun('indicator')">
        <template #default>
          <div class="indexList">
            <div class="indexItem" v-for="(item,index) in indicatorOptions" :key="index" @click="selectIndicator(item,index)" >
              <span :style="{color: item.selected ? '#F7830D' : '#323233'}">{{ item.text }}</span>
              <van-icon v-if="item.selected" color="#F7830D" name="success" />
            </div>
          </div>
        </template>
      </van-dropdown-item>
      <van-dropdown-item @close="searchFun('time')" v-model="timeChecked" :options="timeOPtions" />
    </van-dropdown-menu>
    <van-list
      class="list"
      v-model="loading"
      :finished="finished"
      finished-text="没有更多了"
      @load="getDataFun"
    >
      <div class="item" v-for="item in dataList" :key="item.id">
        <div class="left">
          <div class="indexLine">
            <span class="indicatorText">{{item.name}}</span>
            <span class="source" v-if="item.measure_type">（{{item.measure_type}}）</span>
            <img v-if="item.is_read == 0" class="redDot" src="./imgs/redDot_new.png" alt="">
          </div>
          <div class="indexNumLine">
            <div v-for="(innerItem,index) in item.list" :key="index">
              <img v-if="innerItem.deflection == 4" class="warnIcon" src="./imgs/warning.png" alt="">
              <img v-if="innerItem.deflection == 3" class="warnIcon" src="./imgs/blueWarning.png" alt="">
              <span v-if="item.is_text_type == 1" :style="indexText" :class="[ innerItem.abnormal_status == 0 ? '' : 'redColor']">
                {{ innerItem.result }}
              </span>
              <span v-else class="indexNum" :class="[ innerItem.deflection ? [1,3].includes(innerItem.deflection) ? 'blueColor' : 'redColor' : '']">
                {{ innerItem.result }}
                <van-icon v-if="innerItem.deflection" name="down" :class="[ [1,3].includes(innerItem.deflection) ? '' : 'arrowRotate']" :color="[1,3].includes(innerItem.deflection) ? '#008AFF' : '#FF1F33' " />
              </span>
              <span class="indexNum" v-if="index < item.list.length - 1">&nbsp;/&nbsp;</span>
            </div>
            <span class="unit">{{item.unit_name}}</span>
          </div>
        </div>
        <div class="date">{{item.measure_at}}</div>
      </div>
    </van-list>
  </div>
</template>

<script>
import { getAbnormalList,getIndicatorList,abnormalRead,getIndexGroup } from '@/api/docWorkRoom'
export default {
  data(){
    return {
      // 纯文本的指标结果样式
      indexText: {
        'font-size': '16px',
        'display': 'block',
        'text-align': 'left',
        'color': '#0A0A0A'
      },
      dataList: [],
      loading: false,
      finished: false,
      indicatorText: '所有指标',
      timeChecked: 1,
      timeOPtions: [
        { text: '所有时间', value: 1 },
        { text: '24小时内', value: 2 },
        { text: '7天内', value: 3 },
      ],
      indicatorOptions: [],
      indicatorsChecked: [],
      pageNo: 0,
      workroomId: '',
      docId: '',
      patientId: '',
      isFirstLoad: true,
      bigTypeList: [
        { text: '所有数据', value: 0 },
        { text: '院内数据', value: 1 },
        { text: '家庭数据', value: 2 },
      ],
      bigTypeChecked: 0,
      finalSelectIndicators: [],
      finalSelectedTime: null,
      bigTypeDisabled: false,
    }
  },
  methods: {
    bigTypeChange(e){
      this.bigTypeChecked = e
      this.getIndicatorData()
      this.searchFun()
    },
    isNumeric(str) {
      return /^-?\d+(\.\d+)?$/.test(str);
    },
    searchFun(type){
      if(type == 'indicator' && JSON.stringify(this.finalSelectIndicators) == JSON.stringify(this.indicatorsChecked)){
        return
      }
      if(type == 'time' && this.finalSelectedTime == this.timeChecked){
        return
      }
      console.log(this.indicatorsChecked,this.timeChecked)
      this.dataList = []
      this.pageNo = 0
      this.getList()
    },
    selectIndicator(itemObj){
      this.indicatorsChecked = []  //不想做删除和新增  所以直接置空 后面遍历数据源将选中的push进去
      if(itemObj.value === 0){
        this.indicatorText = "所有指标"
        this.indicatorOptions.forEach(item=>{
          if(item.value === 0){
            item.selected = true
          }else{
            item.selected = false
          }
        })
      }else{
        // 选中的条目数组
        let selectedArr = this.indicatorOptions.filter(item=>item.selected)
        if(itemObj.selected && selectedArr.length == 1){
          this.$toast('至少保留一项')
          this.indicatorsChecked = selectedArr.map(item=>item.id)
          return
        }
        itemObj.selected = !itemObj.selected
        this.indicatorText = ''
        this.indicatorOptions.forEach(item=>{
          if(item.value === 0) item.selected = false  //将所有指标选项 取消选中
          if(item.selected){
            this.indicatorsChecked.push(item.id + '')
            this.indicatorText = this.indicatorText == '' ? item.text : `${this.indicatorText}/${item.text}`
          }
        })
      }
      console.log(this.indicatorText)
      console.log(this.indicatorsChecked)
      // this.dataList = []
      // this.pageNo = 0
      // this.getList()
    },
    getIndicatorData(){
      this.indicatorsChecked = []
      this.indicatorText = '所有指标'
      let obj = {
        workroom_id: this.workroomId,
        type: this.bigTypeChecked
      }
      getIndexGroup(obj).then(res=>{
        if(res.code == 200){
          this.indicatorOptions = res.data.list.map(item=>{
            item.text = item.name
            item.value = item.id
            item.selected = false
            if(item.name == '所有指标'){
              item.selected = true
            }
            return item
          })
        }
      })
    },
    getList(isFirstLoadFlag) {
      let obj = {
        "doc_id": this.docId,
        "user_id": this.patientId,
        "workroom_id": this.workroomId,
        "page_size": 20,
        "indicators": this.indicatorsChecked,
        "page_num": this.pageNo += 1,
        "select_date_time" : this.timeChecked,
        "indicator_type": this.bigTypeChecked,
        "is_intervene": 1
      }
      this.finalSelectIndicators = this.indicatorsChecked
      this.finalSelectedTime = this.timeChecked
      getAbnormalList(obj).then(res=>{
        if(res.code == 200){
          let {list,total} = res.data
          this.dataList = this.dataList.concat(list)
          // 加载状态结束
          this.loading = false
          if (this.dataList.length >= total) {
            this.finished = true
          }else{
            this.finished = false
          }
          if(isFirstLoadFlag){
            this.abnormalReadFun()
          }
        }
      })
    },
    getDataFun(){
      if(this.isFirstLoad){
        this.getList('isFirstLoad')
        this.isFirstLoad = false
        return
      }
      this.getList()
    },
    abnormalReadFun(){
      let obj = {
        "user_id": this.patientId,
        "workroom_id": this.workroomId,
        "doc_id": this.docId
      }
      abnormalRead(obj).then(res=>{
        if(res.code == 200){
          console.log('全部已读')
        }
      })
    },
    init(){
      let query = this.$route.query
      this.docId = query.doc_id  // 24839
      this.workroomId = query.workroom_id  // 43233
      this.patientId = query.patient_id  // 10001193
      if(query.onlyFamilyData){
        this.bigTypeChecked = 2
        this.bigTypeDisabled = true
      }
      this.getIndicatorData()
      // this.abnormalReadFun()
    }
  },
  mounted(){
    this.init()
  }
}
</script>

<style lang="scss">
.abnormalRecord{
  .dropdownMenu{
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 999;
  }
  .indexList{
    .indexItem{
      box-sizing: border-box;
      width: 100%;
      height: 45px;
      padding: 0 16px;
      color: #323233;
      font-size: 14px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 0.5px solid #ebedf080;
    }
  }
  .list{
    padding: 0 15px;
    margin-top: 48px;
    .item{
      display: flex;
      justify-content: space-between;
      align-items: baseline;
      padding: 16px 0;
      border-bottom: 1px solid #F1F1F1;
      .left{
        display: flex;
        flex-direction: column;
        align-items: baseline;
        line-height: 20px;
        // width: 60%;
        flex: 1;
        .indexLine{
          font-size: 17px;
          text-align: left;
          .indicatorText{
            color: #0A0A0A;
            
            line-height: 20px;
          }
          .source{
            color: #878F99;
            display: inline-flex;
          }
          .redDot{
            width: 16px;
            height: 14px;
          }
        }
        .indexNumLine{
          display: flex;
          align-items: baseline;
          margin-top: 10px;
          .indexNum{
            font-size: 18px;
            font-weight: 500;
          }
          .unit{
            font-size: 12px;
            color: #878F99;
            margin-left: 6px;
          }
        }
        .warnIcon{
          width: 20px;
          height: 20px;
          position: relative;
          top: 3px;
        }
      }
      .date{
        color: #878F99;
        font-size: 13px;
      }
    }
  }
  .redColor {
    color: #ff1f33 !important;
  }
  .blueColor{
    color: #008AFF !important;
  }
  .arrowRotate{
    transform: rotate(180deg);
  }
}
</style>