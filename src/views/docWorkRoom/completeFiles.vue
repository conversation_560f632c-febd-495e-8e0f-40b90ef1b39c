<!--
 * @Descripttion: 医生工作室健康便利店医院数据完整档案
 * @version: 
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-12 10:10:14
 * @LastEditors: liuqiannan
 * @LastEditTime: 2021-07-23 16:02:41
-->
<template>
    
    <div class="wrapper">
        <div class="title">
            <div class="left">
                生命体征
            </div>
            <div 
                class="right"
                @click="rightClick(0)"
            >
                {{lifeStatus.update_at}}更新
                <van-icon v-if="extend" name="arrow-up" />
                <van-icon v-if="!extend" name="arrow-down" />
            </div>
        </div>
        <div 
            v-if="extend"
            class="content">
            <div class="item">
                <div class="label">身高</div>
                <div>{{ lifeStatus.height }} <span class="unit">cm</span></div>
            </div>
            <div class="item">
                <div class="label">体重</div>
                <div>{{ lifeStatus.weight}} <span class="unit">kg</span></div>
            </div>
            <div class="item">
                <div class="label">BMI</div>
                <div>{{ lifeStatus.bmi }} <span class="unit">kg/m²</span></div>
            </div>
            <div class="item">
                <div class="label">收缩压</div>
                <div>{{ lifeStatus.sbp }} <span class="unit">mmHg</span></div>
            </div>
            <div class="item">
                <div class="label">舒张压</div>
                <div>{{ lifeStatus.dbp }}  <span class="unit">mmHg</span></div>
            </div>
            <div class="item">
                <div class="label">脉搏</div>
                <div>{{ lifeStatus.pulse }} <span class="unit">bpm</span></div>
            </div>
        </div>
        <div class="title">
            <div class="left">
                辅助检查
            </div>
            <div 
                class="right"
                @click="rightClick(1)"
                >
                {{ visit_at }}更新
                <van-icon v-if="extend2" name="arrow-up" />
                <van-icon v-if="!extend2" name="arrow-down" />
            </div>
        </div>
        <div 
            v-if="extend2"
            class="content2">
            <div 
                v-for="(item, index) in list"
                :key="index"
                class="row"
                @click="checkClick(item.type)"
                >
                <div class="left">
                    {{ item.report_name }}
                </div>
                <div class="right">
                    <van-icon name="arrow" />
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { medicalSignFun, auxiliaryExaminationFun } from '@/api/operationReport.js'
export default {
    data () {
        return {
           extend: true,
           extend2: true,
           lifeStatus: {},
           list: [],
           visit_at: ''
        }
    },
    created () {
        this.medicalSignFun()
        this.auxiliaryExaminationFun()
    },
    methods: {
        rightClick (param) {
            if (param === 0) {
                this.extend = !this.extend
            }
            if (param === 1) {
                this.extend2 = !this.extend2
            }
        },
        // 生命体征
        medicalSignFun () {
            medicalSignFun({
                user_id: this.$route.query.user_id ? this.$route.query.user_id : '1515837'
            }).then(res => {
                if (res.code === 200) {
                    this.lifeStatus = res.data
                } else {
                    this.$toast(res.msg)
                }
            })
        },
        // 辅助检测
        auxiliaryExaminationFun () {
            auxiliaryExaminationFun({
                user_id: this.$route.query.user_id ? this.$route.query.user_id : '1515837'
            }).then(res => {
                if (res.code === 200) {
                    this.list = res.data.list
                    this.visit_at = res.data.visit_at
                } else {
                    this.$toast(res.msg)
                }
            })
        },
        /**
         * 辅助检查click 
         * @param type
         * 7: 眼底检查
         * 100: 四肢血压/动脉硬化检测
         */
        checkClick (type) {
            const UA = navigator.userAgent
            const isAndroid = UA.indexOf('Android') > -1 || UA.indexOf('Adr') > -1 // android终端
            const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
            // 患者id
            let user_id = this.$route.query.user_id ? this.$route.query.user_id : '1515837'
            let hosp_id = this.$route.query.hosp_id
            let token = localStorage.getItem('authorization')
            let param2 = `/api/medical/auxInfo?user_id=${user_id}&type=7&hosp_id=${hosp_id}&token=${token}`
            let param = `/docWorkRoom/pressureCheck?user_id=${user_id}` // 四肢血压
            

            // 眼底检查
            if (type == 7) {
                if (isAndroid) {
                    window.android.medicalAuxInfoMessage(param2)
                }
                if (isIOS) {
                    window.webkit.messageHandlers.medicalAuxInfoMessage.postMessage(param2)
                }
            }
            // 四肢血压动脉检测
            if (type == 100) {
                if (isAndroid) {
                    window.android.pressureCheckMessage(param)
                }
                if (isIOS) {
                    window.webkit.messageHandlers.pressureCheckMessage.postMessage(param)
                }
                // // 患者id
                // let user_id = this.$route.query.user_id ? this.$route.query.user_id : '1515837'
                // this.$router.push({
                //     path: '/docWorkRoom/pressureCheck',
                //     query: {
                //         user_id: user_id
                //     }
                // })
            }
        }
    }

}
</script>

<style lang="scss" scoped>
.wrapper {
    background: #ffffff;
    font-size: 14px;
    .title {
        display: flex;
        justify-content: space-between;
        margin-top: 15px;
        padding: 0 19px;
        .left {
            font-size: 16px;
            color: #333333;
        }
        .right {
            font-size: 12px;
            color: #666666;
            &:hover {
                cursor: pointer;
            }
        }
    }
    .content {
        margin-top: 20px;
        display: flex;
        justify-content: space-around;
        flex-wrap: wrap;
        border-bottom: 1px solid #ccc;
        .item {
            width: 33%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            margin-bottom: 16px;
            font-size: 12px;
            color: #333333;
            font-weight: 600;
            &:nth-child(1), &:nth-child(2), &:nth-child(4), &:nth-child(5) {
                border-right: 1px solid #F1F1F1;
            }
            .label {
                margin-bottom: 5px;
            }
            .unit {
                color: #999;
                font-weight: 400;
            }
        }
        
    }
    .content2 {
        margin-top: 20px;
        border-bottom: 1px solid #ccc;
        padding: 0 19px;
        .row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 16px;
            font-size: 12px;
            color: #333333;
            font-weight: 600;
            &:hover {
                cursor: pointer;
            }
        }
        .right {
            .van-icon {
                font-size: 14px;
            }
        }
    }
    .van-icon {
        color:#CCCCCC;
    }
    .van-icon-arrow-up, .van-icon-arrow-down {
        font-size: 18px;
        vertical-align: text-top;
        // display: inline-flex;
        // justify-content: center;
        // align-items: center;
    }
}
</style>