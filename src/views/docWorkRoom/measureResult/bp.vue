<template>
  <div class="measure-result-bp">
    <div class="content">
      <div class="result">
        本次测量平均值
        <img :class="avg.sbp.warn" v-if="avg.sbp.warn" :src="avg.sbp.url" alt="warn"><span :class="`${avg.sbp.st}`">{{ avg.sbp.value }}</span><van-icon v-if="avg.sbp.st" :class="avg.sbp.st" name="down" />/
        <img :class="avg.dbp.warn" v-if="avg.dbp.warn" :src="avg.dbp.url" alt="warn"><span :class="`${avg.dbp.st}`">{{ avg.dbp.value }}</span><van-icon v-if="avg.dbp.st" :class="avg.dbp.st" name="down" />
        <img :class="avg.pulse.warn" v-if="avg.pulse.warn" :src="avg.pulse.url" alt="warn"><span :class="`${avg.pulse.st}`">&nbsp;{{ avg.pulse.value }}</span><van-icon v-if="avg.pulse.st" :class="avg.pulse.st" name="down" />
      </div>
      <div :class="`badge ${group.st}`">
        <span :class="`bp ${group.bp.st}`">血压{{ group.bp.status_display }}</span>
        &nbsp;
        <span :class="`pulse ${group.pulse.st}`">{{ group.pulse.status_display }}</span>
      </div>
      <!-- <div class="tips">{{ group.tips }}</div> -->
    </div>
    <div class="list">
      <ul class="header">
        <li v-for="(item, i) in list.header" :key="i" v-text="item"></li>
      </ul>
      <ul v-for="(item, i) in list.data" :key="i">
        <li v-for="(el, j) in item" :key="j" :class="el.af_flg_st">
          <img :class="el.warn" v-if="el.warn" :src="el.url" alt="warn">
          <span :class="el.st">{{ el.v }}</span>
          <van-icon v-if="el.st" :class="el.st" name="down" />
        </li>
      </ul>
    </div>
    <div class="footer">
      <p class="title">
        <span class="lf">正常范围</span>
        <!-- <span class="rt">出自《中国家庭血压检测指南》</span> -->
      </p>
      <div class="range">
        <p>
          <span>血压：{{ bp_normal_info }}</span>
        </p>
        <p>脉搏：{{ pulse_normal_info }}</p>
      </div>
    </div>
    <div class="btns">
      <div class="btn" @click="handleOk">我知道了</div>
      <!-- <div class="btn rt">去咨询医生</div> -->
    </div>
  </div>
</template>

<script>
import { getBpMeasureResult } from '@/api/docWorkRoom'
import { getSystemType } from '@/utils/utils'
import warn_high_url from '../houseHoldData/imgs/warning.png'
import warn_low_url from '../houseHoldData/imgs/blueWarning.png'
export default {
  data() {
    return {
      avg: {
        sbp: {},
        dbp: {},
        pulse: {},
      },
      group: {
        bp: {},
        pulse: {},
      },
      list: {
        header: ['次数', '收缩压', '舒张压', '脉搏', '心律'],
        data: []
      },
      bp_normal_info: '',
      pulse_normal_info: '',
    }
  },
  created() {
    this.initData()
  },
  methods: {
    async initData() {
      const { id, source, patient_id } = this.$route.query
      const res = await getBpMeasureResult({
        user_id: patient_id,
        id: decodeURIComponent(id),
        is_patient_platform: source === 'docApp' ? '0' : '1'
      })
      if (res.status === 0) {
        this.formateData(res.data || {})
      }
    },
    formateData(data) {
      const { bp_normal_info, pulse_normal_info, indicator, group, list } = data

      this.bp_normal_info = bp_normal_info
      this.pulse_normal_info = pulse_normal_info

      if (group.length > 0) {
        let sbp = group.find(el => el.code === 'sbp')
        let dbp = group.find(el => el.code === 'dbp')
        let pulse = group.find(el => el.code === 'pulse')

        const _group = {
          bp: sbp || dbp,
          pulse: pulse || {},
          tips: sbp.status_remind || ''
        }
        const getStatus = (d) => {
          if ([1, 3].includes(d)) {
            return 'low'
          } else if ([2, 4].includes(d)) {
            return 'high'
          }
          return 'normal'
        }
        _group.bp.st = getStatus(_group.bp.deflection)
        _group.pulse.st = getStatus(_group.pulse.deflection)
        _group.pulse.status_display = _group.pulse.status_display === '正常' ? '脉搏正常' : _group.pulse.status_display
        const getgroupStatus = () => {
          if (_group.bp.st === 'high' || _group.pulse.st === 'high') {
            return 'high'
          }
          if (_group.bp.st === 'low' || _group.pulse.st === 'low') {
            return 'low'
          }
          return 'normal'
        }
        _group.st = getgroupStatus()
        this.group = _group
      }

      if (indicator.length > 0) {
        indicator.forEach(item => {
          let st = '', warn = '', url = '', deflection = item.deflection
          if ([1, 3].includes(deflection)) {
            st = 'low'
            if (deflection === 3) {
              warn = 'low'
              url = warn_low_url
            }
          } else if ([2, 4].includes(deflection)) {
            st = 'high'
            if (deflection === 4) {
              warn = 'high'
              url = warn_high_url
            }
          }
          item.st = st
          item.warn = warn
          item.url = url

          if (item.code !== 'heart_rate') {
            this.avg[item.code] = item
          }
        })
      }

      if (list.length > 0) {
        this.list.data = []
        list.forEach((item , index) => {
          const arr = []
          arr.push({ v: index + 1 })
          arr.push(this.formateListBp(item, 'sbp'))
          arr.push(this.formateListBp(item, 'dbp'))
          arr.push(this.formateListBp(item, 'pulse'))
          arr.push(this.formateListBp(item, 'af_flg'))
          // arr.push({ v: item.af_flg_name })
          this.list.data.push(arr)
        })
      }
      // console.log(this.list.data)
    },
    formateListBp(item, type) {
      const val = item[type]
      const o = { v: val }

      let st = '', warn = '', url = ''
      item.abnormal_info.forEach(el => {
        if (el.code === type) {
          if ([1, 3].includes(el.deflection)) {
            st = 'low'
            if (el.deflection === 3) {
              warn = 'low'
              url = warn_low_url
            }
          } else if ([2, 4].includes(el.deflection)) {
            st = 'high'
            if (el.deflection === 4) {
              warn = 'high'
              url = warn_high_url
            }
          }
          if (type === 'af_flg') {
            o.af_flg_st = `af_flg_abnormal_${el.abnormal_status}`
          }
        }
      })
      if (type === 'af_flg') {
        o.v = item.af_flg_name
      }
      o.st = st
      o.warn = warn
      o.url = url

      return o
    },
    handleOk() {
      if (getSystemType() === 'ios') {
        window.webkit.messageHandlers.backGoHomePage.postMessage('')
      } else {
        window.android.backGoHomePage()
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.measure-result-bp {
  padding: 15px;
  height: 100vh;
  min-height: 660px;
  box-sizing: border-box;
  background-color: #F5F5F5;
  .content {
    padding: 20px 15px;
    border-radius: 8px;
    background-color: #fff;
    .result {
      font-size: 17px;
      font-weight: 500;
      color: #666;
      .high {
        color: #F23616;
        &.van-icon {
          transform: rotate(180deg);
        }
      }
      .low {
        color: #3388FF;
      }
      img {
        display: inline-block;
        vertical-align: top;
        width: 18px;
        height: 18px;
      }
    }
    .badge {
      width: 315px;
      height: 80px;
      line-height: 80px;
      border-radius: 40px;
      margin: 21px auto;
      background-color: rgba(15, 234, 50, 0.1);
      &.high {
        background-color: #FFF0EE;
      }
      &.normal {
        background-color: rgba(15, 234, 50, 0.1);
      }
      &.low {
        background-color: #F3F8FF;
      }
      span {
        font-size: 24px;
        font-weight: 600;
        color: #333;
        &.high {
          color: #F23616;
        }
        &.normal {
          color: #333;
        }
        &.low {
          color: #3388FF;
        }
      }
    }
    .tips {
      line-height: 1.5;
      font-size: 15px;
      color: #666;
    }
  }
  .list {
    margin: 12px 0;
    padding: 18px 15px;
    border-radius: 8px;
    background-color: #fff;
    > ul {
      display: flex;
      justify-content: space-between;
      align-items: center;
      &:not(.header) {
        margin-top: 16px;
        li {
          display: flex;
          align-items: center;
          &.af_flg_abnormal_1 > span {
            color: #F23616;
          }
          .high {
            color: #F23616;
            &.van-icon {
              transform: rotate(180deg);
            }
          }
          .low {
            color: #3388FF;
          }
          img {
            display: inline-block;
            vertical-align: top;
            width: 16px;
            height: 16px;
          }
        }
      }
      &.header {
        padding-bottom: 18px;
        border-bottom: 1px solid #F5F5F5;
        li {
          font-size: 16px;
          color: #333;
        }
      }
      li {
        font-size: 14px;
        font-weight: 500;
        text-align: left;
        color: #666;
        &:nth-of-type(1), &:nth-of-type(4) {
          width: 16%;
        }
        &:nth-of-type(2), &:nth-of-type(3) {
          width: 22%;
        }
        &:nth-of-type(5) {
          width: 24%;
        }
      }
    }
  }
  .footer {
    margin: 12px 0;
    padding: 18px 15px;
    border-radius: 8px;
    text-align: left;
    background-color: #fff;
    .title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 3px 0 22px;
      .lf {
        font-size: 17px;
        font-weight: 500;
        color: #0A0A0A;
      }
      .rt {
        font-size: 15px;
        color: #999;
      }
    }
    .range {
      font-size: 15px;
      color: #666;
      > p {
        display: flex;
        justify-content: space-between;
        align-items: center;
        line-height: 1.5;
      }
    }
  }
  .btns {
    display: flex;
    justify-content: center;
    height: 52px;
    margin: 30px auto;
    .btn {
      width: 128px;
      height: 50px;
      line-height: 50px;
      border-radius: 25px;
      font-size: 17px;
      color: #EE9A57;
      border: 1px solid #EE9A57;
      background-color: #f5ece5;
      &.rt {
        width: 200px;
        font-weight: 500;
        color: #EE9A57;
        border: 1px solid #EE9A57;
        // background-color: rgba(238, 154, 87, 0.1);
        background-color: #f5ece5;
      }
    }
  }
}
</style>