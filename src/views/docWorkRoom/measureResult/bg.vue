<template>
  <div class="measure-result-bg">
    <div class="content">
      <div class="result">
        本次测量结果 
        <img :class="bg.warn" v-if="bg.warn" :src="bg.url" alt="warn">
        <span :class="`value ${bg.st}`">{{ bg.bg }}</span>
        <van-icon v-if="!!arrow" :class="arrow" name="down" />
        mmol/L
      </div>
      <div :class="`badge ${bg.st}`">{{ bg.status_display }}</div>
      <!-- <div class="tips">{{ bg.result }}</div> -->
    </div>
    <div class="footer">
      <p class="title">正常范围</p>
      <div class="range">
        <p>{{ tips }}</p>
      </div>
    </div>
    <div class="btns">
      <div class="btn" @click="handleOk">我知道了</div>
      <!-- <div class="btn rt" @click="handleDoctor">去咨询医生</div> -->
    </div>
  </div>
</template>

<script>
import { getBgMeasureResult } from '@/api/docWorkRoom'
import { getSystemType } from '@/utils/utils'
import warn_high_url from '../houseHoldData/imgs/warning.png'
import warn_low_url from '../houseHoldData/imgs/blueWarning.png'
export default {
  data() {
    return {
      bg: {},
      tips: '',
    }
  },
  computed: {
    arrow() {
      if (this.bg.st === 'high') {
        return 'up'
      } else if (this.bg.st === 'low') {
        return 'down'
      }
      return ''
    },
  },
  created() {
    // 严重低血糖(type:5, ab:1, abi: [...]) deflection: 3
    // 低血糖(type:6, ab:1, abi: [...]) deflection: 3
    // 正常(type:1, ab:0, abi: []) deflection: 3  太棒啦！请您继续保持。
    // 偏高(type:2, ab:1, abi: [...]) deflection: 3
    // 很高(type:4, ab:1, abi: [...]) deflection: 3
    this.getData()
  },
  methods: {
    async getData() {
      const { record_id, source, patient_id } = this.$route.query
      const res = await getBgMeasureResult({
        user_id: patient_id,
        record_id: decodeURIComponent(record_id),
        is_patient_platform: source === 'docApp' ? '0' : '1'
      })
      if (res.status === 0) {
        const { bg_distribution } = res.data
        this.bg = bg_distribution
        this.setBgValue(bg_distribution)
      }
    },
    setBgValue(bg_distribution) {
      const { abnormal, abnormal_info, normal_info } = bg_distribution

      if (abnormal === 1) {
        const o = abnormal_info[0] || {}
        const { deflection, status_display } = o

        let st = '', warn = '', url = ''
        if ([1, 3].includes(deflection)) {
          st = 'low'
          if (deflection === 3) {
            warn = 'low'
            url = warn_low_url
          }
        } else if ([2, 4].includes(deflection)) {
          st = 'high'
          if (deflection === 4) {
            warn = 'high'
            url = warn_high_url
          }
        }
        this.bg.st = st
        this.bg.warn = warn
        this.bg.url = url
        this.bg.status_display = status_display
      } else {
        // this.bg.result = '太棒啦！请您继续保持。'
        this.bg.status_display = this.bg.name
      }

      const normal = normal_info[0] || {}
      this.tips = `${normal.name}：${normal.normal_info}`
    },
    handleOk() {
      if (getSystemType() === 'ios') {
        window.webkit.messageHandlers.backGoHomePage.postMessage('')
      } else {
        window.android.backGoHomePage()
      }
    },
    // handleDoctor() {},
  }
}
</script>

<style lang="scss" scoped>
.measure-result-bg {
  padding: 15px;
  height: 100vh;
  min-height: 660px;
  box-sizing: border-box;
  background-color: #F5F5F5;
  .content {
    padding: 20px 15px;
    border-radius: 8px;
    background-color: #fff;
    .result {
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 17px;
      font-weight: 500;
      color: #666;
      .value {
        color: #666;
        &.high {
          color: #ff0200;
        }
        &.low {
          color: #008AFF;
        }
      }
      .van-icon {
        &.up {
          transform: rotate(180deg);
          color: #ff0200;
        }
        &.down {
          color: #008AFF;
        }
      }
      img {
        display: inline-block;
        vertical-align: top;
        width: 18px;
        height: 18px;
      }
    }
    .badge {
      width: 315px;
      height: 80px;
      line-height: 80px;
      border-radius: 40px;
      margin: 21px auto;
      font-size: 24px;
      font-weight: 600;
      border: 2px solid rgba(255, 255, 255, 1);
      box-shadow: 0px 8px 20px 0px rgba(0, 0, 0, 0.04);
      color: #333;
      background-color: rgba(15, 234, 50, 0.1);
      &.high {
        color: #F23616;
        background-color: #FFF0EE;
      }
      &.normal {
        color: #333;
        background-color: rgba(15, 234, 50, 0.1);
      }
      &.low {
        color: #3388FF;
        background-color: #F3F8FF;
      }
    }
    .tips {
      font-size: 15px;
      color: #666;
    }
  }
  .footer {
    margin: 12px 0;
    padding: 18px 15px;
    border-radius: 8px;
    text-align: left;
    background-color: #fff;
    .title {
      margin: 3px 0 22px;
      font-size: 17px;
      font-weight: 500;
      color: #0A0A0A;
    }
    .range {
      font-size: 15px;
      color: #666;
      > p {
        line-height: 1.5;
      }
    }
  }
  .btns {
    display: flex;
    justify-content: center;
    height: 52px;
    margin: 30px auto;
    .btn {
      width: 128px;
      height: 50px;
      line-height: 50px;
      border-radius: 25px;
      font-size: 17px;
      color: #EE9A57;
      border: 1px solid #EE9A57;
      background-color: #f5ece5;
      &.rt {
        width: 200px;
        font-weight: 500;
        color: #EE9A57;
        border: 1px solid #EE9A57;
        // background-color: rgba(238, 154, 87, 0.1);
        background-color: #f5ece5;
      }
    }
  }
}
</style>