<!--
 * @Descripttion: 问卷列表
 * @version:
 * @Author: guxiang
 * @Date: 2022-01-07 14:13:51
 * @LastEditors: guxiang
 * @LastEditTime: 2022-01-21 15:20:55
-->
<template>
  <div class="content" :class="{ 'sunmi': $route.query.source}">
    <van-tabs v-model="active" title-active-color="#333" line-width="33px" sticky @change="tabchange">
      <van-tab title="问卷列表" name="1">
        <van-pull-refresh v-model="surveyrefreshing" @refresh="onRefresh('1')">
          <van-list
            v-model="surveyloading"
            :finished="surveyfinished"
            finished-text="已经全部加载完毕"
            @load="onLoadList"
          >
            <div class="questionlist" v-for="(item, index) in surveyList" :key="index">
              <div class="questionlist-title">{{item.survey_name}}</div>
              <div class="questionlist-info">
                <span class="name" v-show="item.visit_level_name">随访阶段</span>
                <span class="level">{{item.visit_level_name}}</span>
              </div>
              <div class="questionlist-btn" @click.stop="surveyClick(item)">去填写</div>
            </div>
          </van-list>
        </van-pull-refresh>
      </van-tab>
      <van-tab title="填写问卷记录" name="2">
        <div class="filter" @click="showPopup">
          <span class="current">{{ date }}</span>
          <van-icon name="filter-o"/>
          <span>筛选</span>
        </div>
        <van-popup v-model="show" position="bottom">
          <van-datetime-picker
            v-model="currentDate"
            type="date"
            cancel-button-text="清除"
            title="选择年月日"
            :min-date="minDate"
            :max-date="maxDate"
            @confirm="confirmTime"
            @cancel="clearTime"
          />
        </van-popup>
        <van-pull-refresh v-model="refreshing" @refresh="onRefresh('2')">
          <van-list
            v-model="loading"
            :finished="finished"
            finished-text="已经全部加载完毕"
            @load="onLoad"
          >
            <div v-for="(item, index) in showList" :key="index">
              <div class="date">{{ item.trigger_time }}</div>
              <van-cell
                is-link
                v-for="e in item.data"
                :key="e.id"
                @click="todetail(e,true)"
              >
                <template #title>
                  <span class="custom-title">{{ e.survey_name }}</span>
                  <van-tag size="medium" type="primary" v-if="e.audit_state == 0"
                  >待审核
                  </van-tag
                  >
                  <van-tag size="medium" type="success" v-if="e.audit_state == 1"
                  >审核通过
                  </van-tag
                  >
                  <van-tag size="medium" type="danger" v-if="e.audit_state == 2"
                  >未通过
                  </van-tag
                  >
                  <van-tag size="medium" color="#BFBFBF" v-if="e.audit_state == 3"
                  >未提审
                  </van-tag
                  >
                </template>
              </van-cell>
            </div>
          </van-list>
        </van-pull-refresh>
      </van-tab>
    </van-tabs>





  </div>
</template>

<script>
import {getQuestionnaire, getQuestionnaireSurvey, getQuestionnaireSurveyCopy} from "@/api/saas.js";
import { Toast } from 'vant';

export default {
  data() {
    return {
      refreshing: false,
      surveyrefreshing: false,
      loading: false,
      finished: false,
      surveyloading: false,
      surveyfinished: false,
      show: false,
      list: [],
      showList: [],
      surveyList:  [],
      page_no: 1,
      minDate: new Date(2020, 0, 1),
      maxDate: new Date(2025, 10, 1),
      currentDate: new Date(),
      date: "",
      active: "1",
    };
  },
  created() {
    // this.onLoadList()
  },
  methods: {
    onRefresh(type) {
      if (type === '1') {
        this.surveyList = []
        this.surveyfinished = false;
        this.surveyloading = true;
        this.onLoadList();
      } else {
        this.list = []
        this.page_no = 1
        this.finished = false;
        this.loading = true;
        this.onLoad();
      }
    },
    onLoad() {
      this.getQuestionnaire();
    },
    async getQuestionnaire() {
      let res = await getQuestionnaire({
        patient_id: this.$route.query.patient_id,
        project_id: this.$route.query.project_id,
        page: this.page_no,
        date: this.date,
      });
      if (res.status == 200) {
        for (let i = 0; i < res.data.data.length; i++) {
          this.list.push(res.data.data[i]);
        }
        // 获得的数据转为二维数组
        var map = {};
        this.showList = [];
        for (let a = 0; a < this.list.length; a++) {
          var ai = this.list[a];
          if (!map[ai.trigger_time]) {
            this.showList.push({
              trigger_time: ai.trigger_time,
              data: [ai],
            });
            map[ai.trigger_time] = ai;
          } else {
            for (let j = 0; j < this.showList.length; j++) {
              var dj = this.showList[j];
              if (dj.trigger_time == ai.trigger_time) {
                dj.data.push(ai);
                break;
              }
            }
          }
        }
        // 页数加一
        this.page_no += 1;
        // 加载状态结束
        this.loading = false;
        this.refreshing = false;
        // 数据全部加载完成
        if (res.data.current_page == res.data.last_page) {
          this.finished = true;
        }
      }
    },
    todetail(e, is_look = false) {
      const UA = navigator.userAgent;
      const isAndroid = UA.indexOf("Android") > -1 || UA.indexOf("Adr") > -1; // android终端
      const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); // ios终端
      let patient_id = this.$route.query.patient_id;
      let project_id = this.$route.query.project_id;
      let token = this.$route.query.authorization;
      let source = this.$route.query.source     // 从商米进的问卷
      let param = `/redirect/h5/writeQuestion?project_id=${project_id}&patient_id=${patient_id}&survey_id=${e.survey_id}&doc_id=${e.doctor_id}&token=${token}&is_look=${is_look}&source=${source}`;
      if (process.env.NODE_ENV === "production") {
        if (isAndroid) {
          window.android.todetailMessage(param);
        }
        if (isIOS) {
          window.webkit.messageHandlers.todetailMessage.postMessage(param);
        }
      }

      // window.open(`http://localhost:3100${param}`)

      // this.$router.push({
      //   path: 'https://saas-pc.zz-med-test.com' + param,
      //   query: {
      //     project_id: project_id,
      //     patient_id: patient_id,
      //   },
      // });

    },
    showPopup() {
      this.show = true;
    },
    confirmTime() {
      // 隐藏弹框
      this.show = false;
      // 日期格式化
      this.date = this.dateFormat("YYYY-mm-dd", this.currentDate);
      console.log(this.date);
      // 初始化查询条件
      this.page_no = 1;
      this.list = [];
      this.loading = true;
      this.finished = false;
      // 加载
      this.onLoad();
    },
    clearTime() {
      // 隐藏弹框
      this.show = false;
      // 初始化日期
      this.date = "";
      // 初始化查询条件
      this.page_no = 1;
      this.list = [];
      this.loading = true;
      this.finished = false;
      // 加载
      this.onLoad();
    },
    dateFormat(fmt, date) {
      let ret;
      const opt = {
        "Y+": date.getFullYear().toString(), // 年
        "m+": (date.getMonth() + 1).toString(), // 月
        "d+": date.getDate().toString(), // 日
        "H+": date.getHours().toString(), // 时
        "M+": date.getMinutes().toString(), // 分
        "S+": date.getSeconds().toString(), // 秒
        // 有其他格式化字符需求可以继续添加，必须转化成字符串
      };
      for (let k in opt) {
        ret = new RegExp("(" + k + ")").exec(fmt);
        if (ret) {
          fmt = fmt.replace(
            ret[1],
            ret[1].length == 1 ? opt[k] : opt[k].padStart(ret[1].length, "0")
          );
        }
      }
      return fmt;
    },
    async onLoadList () {
      this.surveyloading = true
      try {
        let res = await getQuestionnaireSurvey({
          patient_id: this.$route.query.patient_id,
          project_id: this.$route.query.project_id
        });
        if (res.status == 200) {
          this.surveyList = res.data || []
          this.surveyfinished = true
        }
      } catch (e) {
        this.surveyfinished = true
      }
      this.surveyloading = false
      this.surveyrefreshing = false
    },
    async surveyClick({ level_task_id }) {
      const toast1 = Toast.loading({
        message: '加载中...',
        forbidClick: true,
      });
      try {
        let res = await getQuestionnaireSurveyCopy({
          level_task_id,
          patient_id: this.$route.query.patient_id,
          project_id: this.$route.query.project_id
        });
        if (res.status == 200) {
          toast1.clear();
          this.todetail(res.data)
        } else {
          this.$toast(res.msg)
        }
      } catch (e) {
        toast1.clear();
      }
    },
    tabchange() {
      if (this.active === '1') { // 问卷列表
        // this.onLoadList()
        this.onRefresh('1')
      } else {
        // this.onLoad()
        this.onRefresh('2')
      }
      console.log(this.active)
    }
  },
};
</script>

<style lang="scss" scoped>
.content {
  background: #f5f5f5;
  text-align: left;
  width: 100vw;
  min-height: 100vh;

  .filter {
    background: #ffffff;
    font-size: 14px;
    font-weight: 500;
    color: #377cfb;
    padding: 16px 22px;
    text-align: right;
    .current {
      margin-right: 6px;
    }
  }

  .date {
    font-size: 14px;
    font-weight: 400;
    color: #454545;
    padding: 12px 17px;
  }

  ::v-deep .custom-title {
    margin-right: 10px;
  }

  .questionlist{
    position: relative;
    width: calc(100% - 67px);
    height: 100%;
    margin: 12px 15px 0;
    background: #FFFFFF;
    border-radius: 6px;
    padding: 17px 15px 20px;

    &-title{
      font-weight: 400;
      font-size: 16px;
      line-height: 24px;
      color: #0A0A0A;
      width: 100%;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      margin-bottom: 6px;
    }

    &-info{
      height: 21px;
      .name{
        font-weight: 400;
        font-size: 15px;
        line-height: 21px;
        color: #878F99;
        margin-right: 26px;
      }
      .level{
        font-weight: 400;
        font-size: 15px;
        line-height: 19px;
        color: #5A6266;
      }
    }

    &-btn{
      position: absolute;
      right: 15px;
      bottom: 15px;
      width: 70px;
      height: 30px;
      line-height: 30px;
      background: #fef3e7;
      border-radius: 18px;
      font-weight: 900;
      font-size: 14px;
      text-align: center;
      color: #F7830D;
    }
  }

  .tip{
    width: 100vw;
    text-align: center;
    margin-top: 20px;
    font-weight: 400;
    font-size: 14px;
    line-height: 18px;
    color: #999999;
  }
}
::v-deep .van-tabs__line{
  background-color: #EE7800 !important;
}
::v-deep .van-tab__text{
  font-weight: 400;
  font-size: 16px;
  line-height: 26px;
  text-align: center;
  color: #3F4447;
}
::v-deep .van-tab--active .van-tab__text{
  color: #0A0A0A;
  font-weight: 900;
}
::v-deep .van-list{
  min-height: 100vh;
}

.sunmi {
  background: #f5f5f5;
  text-align: left;
  width: 100vw;
  min-height: 100vh;

  .filter {
    background: #ffffff;
    font-size: 14px;
    font-weight: 500;
    color: #377cfb;
    padding: 16px 22px;
    text-align: right;
  }

  .date {
    font-size: 14px;
    font-weight: 400;
    color: #454545;
    padding: 12px 17px;
  }

  ::v-deep .custom-title {
    margin-right: 10px;
  }

  .questionlist{
    position: relative;
    width: calc(100% - 67px);
    height: 100%;
    margin: 12px 15px 0;
    background: #FFFFFF;
    border-radius: 6px;
    padding: 17px 15px 20px;

    &-title{
      font-weight: 400;
      font-size: 16px;
      line-height: 24px;
      color: #0A0A0A;
      width: 100%;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      margin-bottom: 6px;
    }

    &-info{
      height: 21px;
      .name{
        font-weight: 400;
        font-size: 15px;
        line-height: 21px;
        color: #878F99;
        margin-right: 26px;
      }
      .level{
        font-weight: 400;
        font-size: 15px;
        line-height: 19px;
        color: #5A6266;
      }
    }

    &-btn{
      position: absolute;
      right: 15px;
      bottom: 15px;
      width: 70px;
      height: 30px;
      line-height: 30px;
      background: #DEECFF;
      border-radius: 18px;
      font-weight: 900;
      font-size: 14px;
      text-align: center;
      color: #3388FF;
    }
  }

  .tip{
    width: 100vw;
    text-align: center;
    margin-top: 20px;
    font-weight: 400;
    font-size: 14px;
    line-height: 18px;
    color: #999999;
  }
  ::v-deep .van-tabs__line{
    background-color: #3388FF !important;
  }
  ::v-deep .van-tab__text{
    font-weight: 400;
    font-size: 16px;
    line-height: 26px;
    text-align: center;
    color: #3F4447;
  }
  ::v-deep .van-tab--active .van-tab__text{
    color: #0A0A0A;
    font-weight: 900;
  }
  ::v-deep .van-list{
    min-height: 100vh;
  }

}
</style>
