<!--
 * @Descripttion: 医生工作室对接-转交客服经理
 * @version:
 * @Author: mahy
 * @Date: 2024-10-25 10:02:10
 * @LastEditors: mahy
 * @LastEditTime: 2024-10-28 13:55:36
-->

<template>
    <div class="wrapper">
        <div class="content">
            <div class="div-search">
                <form action="/">
                    <van-search maxlength="11" class="search" ref="searchTextBox" v-model="cellValue" shape="round"
                        placeholder="请输入手机号" @search="onSearch" />
                </form>
            </div>
            <div class="div-cell" v-show="showCell" @click="onSearch">
                <div class="icon-search">
                    <van-icon name="search" color="#FFFFFF" size="26" />
                </div>
                <van-cell :title="`搜索${this.cellValue}`" title-class="label" value-class="value" size="large"
                    :center="true" is-link />
            </div>
        </div>
        <van-dialog v-model="showDialog" show-cancel-button @confirm="debounceConfirm" @cancel="debounceCancel"
            :message="tips" confirmButtonColor="#FF6F00">
        </van-dialog>

    </div>
</template>


<script>
import { docTransferSaleSearch, docTransfer } from "@/api/docWorkRoom.js";
import { getSystemType } from '@/utils/utils'
export default {
    data() {
        return {
            tips: "",
            data: [],
            showDialog: false,
            cellValue: "",
        };
    },
    computed: {
        showCell() {
            const phonePattern = /^1\d{10}$/;
            return phonePattern.test(this.cellValue);
        }
    },

    created() {
        //把docid这个医生传给了搜索到的这个医生的id下面
    },
    methods: {
        onSearch() {
            if (!this.showCell) {
                this.$toast('手机号格式不正确')
                this.$refs.searchTextBox.focus();
                return;
            }
            this.docSaleSearch();
        },
        onCancel() {
            this.showDialog = false;
            this.$refs.searchTextBox.focus();
        },
        onConfirm() {
            this.showDialog = false;
            this.confirmTransSale();
            this.$refs.searchTextBox.focus();
        },
        // 根据手机号搜索渠道人员
        docSaleSearch() {
            let obj = {
                doc_id: this.$route.query.docId,
                to_sale_cell: this.cellValue,
            }
            docTransferSaleSearch(obj).then(res => {
                if (res.code == 200) {
                    this.data = res.data;
                    this.tips = this.data.tips;
                    this.showDialog = true;
                } else {
                    this.$toast(res.msg || '请求错误')
                }
            })
        },
        async confirmTransSale() {
            let obj = {
                doc_id: this.data.doc_id,
                to_sale_id: this.data.to_sale_id,
            }
            await docTransfer(obj).then(res => {
                if (res.code == 200) {
                    this.$toast('转交成功')
                    setTimeout(() => {
                        if (process.env.NODE_ENV === 'production') {
                            if (getSystemType() == 'ios') {
                                window.webkit.messageHandlers.goBlackTwoPageAndRefresh.postMessage('')
                            } else {
                                window.android.goBlackTwoPageAndRefresh()
                            }
                        } else {
                            this.$router.go(-2);
                        }
                    }, 1200);
                } else {
                    this.$toast(res.msg || '请求错误')
                }
            });
        },
        // 防抖
        debounce(func, delay) {
            // 返回值必须得是函数   keyup 事件处理函数
            return function (args) {
                clearTimeout(func.id)
                // 函数是对象   id挂在func上  func是闭包中的自由
                // console.log(func.id);
                func.id = setTimeout(function () {
                    func(args)
                }, delay);
            }
        },
        debounceConfirm() {
            this.debounce(this.onConfirm, 300)();
        },
        debounceCancel() {
            this.debounce(this.onCancel, 300)();
        }
    },
};
</script>

<style lang="scss" scoped>
.wrapper {
    background: white;
    font-size: 18px;
    padding: 0px 0;
    box-sizing: border-box;

    .content {
        margin-top: 5px;
        display: flex;
        flex-direction: column;

        .div-search {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: left;

            .search {
                display: flex;
                width: 100vw;
                font-size: 16px;
            }

            .search .van-search__content {
                border: solid 2px #8dbcff;
            }
        }

        .div-cell {
            display: flex;
            flex-direction: row;
            align-items: center;
            margin: 10px 15px;
            justify-content: left;

            .icon-search {
                width: 45px;
                height: 40px;
                background-color: #FF6F00;
                display: flex;
                justify-content: center;
                align-items: center;

            }

            .label {
                color: #8dbcff;
            }
        }
    }
}
</style>