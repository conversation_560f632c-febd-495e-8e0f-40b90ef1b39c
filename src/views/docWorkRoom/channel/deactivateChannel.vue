<template>
  <div class="deactivate-channel">
    <van-cell title="姓名" :value="name" size="large" />
    <van-cell title="手机号" :value="phone" size="large"/>
    <van-cell title="渠道码状态" value="启用中" size="large" value-class="is_enable"/>

    <div class="footer">
      <van-button @click="handleDeactivate" class="btn">停用此渠道</van-button>
    </div>
  </div>
</template>

<script>
import { Dialog, Toast } from 'vant'
import { loadNativechannelRefreshPop } from '@/utils/utils'
import { setChannelStopApi } from '@/api/docWorkRoom.js'
export default {
  data () {
    return {
      name: '',
      phone: '',
      is_enable: 1,
      workroom_id: '',
      sale_id: ''
    }
  },
  created () {
    const { sale_name, sale_cell, is_enable, workroom_id, sale_id } = this.$route.query
    this.name = sale_name 
    this.phone = sale_cell 
    this.is_enable = is_enable
    this.workroom_id = workroom_id
    this.sale_id = sale_id
  },
  methods: {
    handleDeactivate () {
      Dialog.confirm({
        confirmButtonColor: '#F7830D',
        theme: 'round',
        title: '提示',
        message: '确认停用此人的渠道码，停用后，此渠道码扫码后提示已失效，不支持客户绑定关系。对应历史服务的客户将不展示此客服经理。'
      }).then(async () => {
          await setChannelStopApi({
            workroom_id: this.workroom_id,
            sale_id: this.sale_id
          })
          this.loading = false
          Toast('操作成功')
          loadNativechannelRefreshPop()
      }).catch(() => {
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.deactivate-channel {
  background-color: #F5F5F5;
  box-sizing: border-box;
  position: relative;
  height: 100vh;
  padding-top: 8px;
  .van-cell--large {
    padding: 16px 15px;
    .van-cell__title {
      font-size: 17px;
      color: #0A0A0A;
    }
    .van-cell__value {
      font-size: 15px;
      color: #666;
    }
    .is_enable {
      color: #FF6F00;
    }
  }
  .footer {
    position: absolute;
    bottom: 58px;
    width: 100%;
    .btn {
      display: inline-block;
      border-radius: 8px;
      background: #F7830D;
      color: white;
      font-size: 16px;
      font-weight: 500;
      padding: 11px;
      width: 80%;
      margin: 0 auto;
    }
  }
  
}
</style>