<template>
  <div class="channel-order">
    <van-dropdown-menu
      ref="dropdownMenu"
      active-color="#F7830D"
      :close-on-click-outside="false"
      class="dropdown-menu-search"
    >
      <van-dropdown-item
        v-for="item in fixedSearchArr"
        @close="handleDropMenuClose(item)"
        @closed="handleDropMenuClosed(item)"
        :key="item.name"
        :title="item.title"
        :ref="`dropdownItem_${item.name}`"
        :title-class="item.title !== item.fake_title ? 'active' : ''"
      >
        <template #title v-if="item.name === 'keywords'">
          <div class="alignCenter">
            <span>筛选</span>
            <van-icon name="filter-o" />
          </div>
        </template>
        <div :class="`customDropMenu ${item.name}`">
          <!-- 筛选内 -->
          <template v-if="item.name === 'keywords'">
            <div class="dropMenuInner" style="height: 340px; overflow: auto">
              <div v-for="otherItem in otherSearchArr" :key="otherItem.name">
                <!-- 多选 -->
                <template v-if="['channel_doc'].indexOf(otherItem.name) !== -1">
                  <div class="labelLine">{{ otherItem.label }}</div>
                  <div class="checkList">
                    <div
                      :class="[inner.checked ? 'checked' : '']"
                      v-for="inner in otherItem.options"
                      :key="inner.name + inner.id"
                      class="checkItem"
                      @click="checkFun(inner, otherItem, otherItem.name)"
                    >
                      {{ inner.name }}
                    </div>
                  </div>
                </template>
                <!-- 单选 -->
                <template
                  v-if="
                    ['service_doc_id', 'channel_type', 'channel_hosp','order_service_status'].indexOf(
                      otherItem.name
                    ) !== -1
                  "
                >
                  <div class="labelLine">{{ otherItem.label }}</div>
                  <div class="checkList">
                    <div
                      :class="[inner.checked ? 'checked' : '']"
                      v-for="inner in otherItem.options"
                      :key="inner.name + inner.id"
                      class="checkItem"
                      @click="radioFun(inner, otherItem, 'other')"
                    >
                      {{ inner.name }}
                    </div>
                  </div>
                </template>
                <!-- 机构 科室 -->
                <template v-if="['hosp_id'].indexOf(otherItem.name) !== -1">
                  <div class="labelLine">{{ otherItem.label }}</div>
                  <div class="hospDeptBlock">
                    <div class="hospLine" @click="openHospPop('hosp')">
                      <span class="label">{{
                        selectHospObj.name || "选择机构"
                      }}</span>
                      <van-icon
                        class="arrowRotate"
                        name="play"
                        color="#878F99"
                        size="12px"
                      />
                    </div>
                    <div
                      class="hospLine deptLine"
                      @click="openHospPop('dept')"
                      v-if="JSON.stringify(selectHospObj) !== '{}'"
                    >
                      <span class="label">{{
                        selectDeptObj.name || "选择科室"
                      }}</span>
                      <van-icon
                        class="arrowRotate"
                        name="play"
                        color="#878F99"
                        size="12px"
                      />
                    </div>
                  </div>
                </template>
              </div>
            </div>
            <!-- <div class="btnOuter">
              <div class="cancelBtn" @click="resetFun">重置</div>
              <div class="confirmBtn" @click="submitFun">确定</div>
            </div> -->
            <div class="footer inner">
              <span class="btn" @click="onSearchResetInner(item)">重置</span>
              <span class="btn confirm" @click="onSearchConfirmInner(item)"
                >提交</span
              >
            </div>
          </template>
          <!-- 筛选外 -->
          <template v-else>
            <!-- 单选 -->
            <template v-if="['order_service_status','order_type'].indexOf(item.name) !== -1">
              <div class="radioList" style="padding: 0;">
                <div
                  class="radioItem"
                  v-for="inner in item.options"
                  :key="inner.name + inner.id"
                  @click="radioFun(inner, item, 'fixed')"
                >
                  <span :class="[inner.checked ? 'checked' : '']">{{
                    inner.name
                  }}</span>
                  <van-icon
                    color="#F7830D"
                    v-show="inner.checked"
                    name="success"
                  />
                </div>
              </div>
            </template>
            <!-- 日期 -->
            <template v-if="item.name === 'order_created_at'">
              <van-datetime-picker
                v-model="search_create_date"
                type="year-month"
                title="选择年月"
                :min-date="minDate"
                :max-date="maxDate"
                :show-toolbar="false"
                :formatter="formatter"
                :swipe-duration="300"
                @change="handleDatetimeChange($event, item)"
              />
              <div class="footer">
                <span
                  class="btn"
                  @click="onSearchDateReset('search_create', item.name)"
                  >重置</span
                >
                <span
                  class="btn confirm"
                  @click="onSearchDateConfirm('search_create', item.name)"
                  >提交</span
                >
              </div>
            </template>
            <!-- 日期 -->
            <template v-if="item.name === 'order_fee_withdrawal'">
              <van-datetime-picker
                v-model="search_remittance_date"
                type="year-month"
                title="选择年月"
                :min-date="minDate"
                :max-date="maxDate"
                :show-toolbar="false"
                :formatter="formatter"
                :swipe-duration="300"
                @change="handleDatetimeChange($event, item)"
              />
              <div class="footer">
                <span
                  class="btn"
                  @click="onSearchDateReset('search_remittance', item.name)"
                  >重置</span
                >
                <span
                  class="btn confirm"
                  @click="onSearchDateConfirm('search_remittance', item.name)"
                  >提交</span
                >
              </div>
            </template>
            <!-- 机构 科室 -->
            <template v-if="['hosp_id'].indexOf(item.name) !== -1">
              <div class="hospDeptBlock">
                <div class="hospLine" @click="openHospPop('hosp')">
                  <span class="label">{{
                    selectHospObj.name || "选择机构"
                  }}</span>
                  <van-icon
                    class="arrowRotate"
                    name="play"
                    color="#878F99"
                    size="12px"
                  />
                </div>
                <div
                  class="hospLine deptLine"
                  @click="openHospPop('dept')"
                  v-if="JSON.stringify(selectHospObj) !== '{}'"
                >
                  <span class="label">{{
                    selectDeptObj.name || "选择科室"
                  }}</span>
                  <van-icon
                    class="arrowRotate"
                    name="play"
                    color="#878F99"
                    size="12px"
                  />
                </div>
              </div>
            </template>
          </template>
        </div>
      </van-dropdown-item>
    </van-dropdown-menu>

    <van-popup
      class="hospPopup"
      v-model="isHospPopShow"
      round
      position="bottom"
    >
      <div class="topBlock">
        <div class="titleLine">
          <span class="cancel" @click="hospCancel">取消</span>
          <span class="title">{{
            selectHospOrDept === "hosp" ? "选择机构" : "选择科室"
          }}</span>
          <span class="confirm" @click="hospConfirm">确认</span>
        </div>
        <van-search
          :clearable="false"
          v-if="selectHospOrDept === 'hosp'"
          class="searchLine"
          v-model="hospKeywords"
          placeholder="搜索医院"
          @blur="getHospList"
        />
      </div>
      <div
        class="radioList"
        style="height: calc(100% - 100px)"
        v-if="selectHospOrDept === 'hosp'"
      >
        <div
          class="radioItem"
          v-for="item in hospList"
          :key="item.hosp_id"
          @click="selectHospObj = item"
        >
          <span
            :class="[item.hosp_id == selectHospObj.hosp_id ? 'checked' : '']"
            >{{ item.name }}</span
          >
          <van-icon
            color="#F7830D"
            v-show="item.hosp_id == selectHospObj.hosp_id"
            name="success"
          />
        </div>
      </div>
      <div class="radioList" style="height: calc(100% - 50px)" v-else>
        <div
          class="radioItem"
          v-for="item in deptList"
          :key="item.id"
          @click="selectDeptObj = item"
        >
          <span :class="[item.id == selectDeptObj.id ? 'checked' : '']">{{
            item.name
          }}</span>
          <van-icon
            color="#F7830D"
            v-show="item.id == selectDeptObj.id"
            name="success"
          />
        </div>
      </div>
    </van-popup>

    <van-list
      class="order-list"
      v-model="loading"
      :finished="finished"
      :immediate-check="false"
      finished-text="没有更多了"
      @load="loadData"
    >
      <div class="container">
        <div v-for="(item, index) in order" :key="index">
          <div class="header">
            <div class="lf">
              <span class="logo">
                <img :src="item.head_img" />
                <span :class="`st service_status_${item.service_status}`">{{
                  item.service_status_str
                }}</span>
              </span>
              <span class="title">{{ item.order_title }}</span>
            </div>
            <div class="rt" v-if="item.doc_type !== 2">{{ formateSalefeText(item) }}</div>
          </div>
          <div :class="`service doc_type_${item.doc_type}` " v-if="item.doc_type !== 2">
            <div class="lf" v-if="item.doc_type !== 3">
              <div class="top">{{ item.sale_name || "未完善信息" }}</div>
              <div class="bot">客服经理</div>
            </div>
            <div class="md">
              <div class="top">
                <span v-if="item.sale_fee">&yen;</span
                >{{ item.sale_fee || "--" }}
              </div>
              <div class="bot">
                服务支持费
                <van-popover
                  v-if="item.sale_fee_detail"
                  v-model="item.popover_sale_fee"
                  get-container="#channel-order-popover"
                  trigger="click"
                  placement="top-end"
                  class="channel-order-popover"
                >
                  <span>{{ item.sale_fee_detail }}</span>
                  <template #reference>
                    <van-icon name="question-o" color="#666" size="14" />
                  </template>
                </van-popover>
              </div>
            </div>
            <div class="rt">
              <div class="top">{{ item.withdrawal_date || "--" }}</div>
              <div class="bot">
                <!-- 可提现时间 -->
                入账时间
                <van-popover
                  v-model="item.popover_withdrawal_date"
                  get-container="#channel-order-popover"
                  trigger="click"
                  placement="top-end"
                  class="channel-order-popover"
                >
                  <span v-if="item.order_type == 2">服务包的服务结束后</span>
                  <span v-if="[1,4,5].indexOf(item.order_type) != -1">问诊的服务结束后</span>
                  <span v-if="item.order_type == 9">会员的服务结束后</span>
                  <span v-if="item.order_type == 3">在线诊室的服务结束后</span>
                  <template #reference>
                    <van-icon name="question-o" color="#666" size="14" />
                  </template>
                </van-popover>
              </div>
            </div>
          </div>
          <div class="more" v-if="!item.show_detail && item.doc_type !== 2">
            <span @click="handleShowOrderDetail(item, index)"
              ><span class="label">更多订单信息</span
              ><van-icon name="arrow-down" size="12" color="#999"
            /></span>
          </div>
          <div
            :class="`content ${item.doc_type === 2 ? 'off' : 'on'}`"
            v-if="item.show_detail || item.doc_type === 2"
          >
          <span v-if="item.check_recipient_status" class="checkConsigneeInfoBtn" @click="goCheck(item.order_no)">查看收货人信息</span>
            <div>
              <span class="label">订单号：</span
              ><span class="value">{{ item.order_no }}</span>
            </div>
            <div>
              <span class="label">订单金额：</span
              ><span class="value">&yen;{{ item.pay_amount }}</span>
            </div>
            <div>
              <span class="label">订单创建时间：</span
              ><span class="value">{{ item.created_at }}</span>
            </div>
            <div>
              <span class="label">服务人：</span
              ><span class="value">{{ item.service_name }}</span>
            </div>
            <div>
              <span class="label">服务有效期：</span
              ><span class="value"
                >{{ item.start_date }} ~ {{ item.end_date }}</span
              >
            </div>
          </div>
        </div>
      </div>
    </van-list>
  </div>
</template>

<script>
import {
  getChannelOrderData,
  getChannelSearchConfig,
  getChannelHospList,
  getChannelDeptList,
  getChannelSearchConfigApi,
} from "@/api/docWorkRoom";
import { getSystemType } from '@/utils/utils'
export default {
  data() {
    return {
      drop_menu_status: 0,
      query_order_created_at: "",
      query_sale_id: "",
      query_service_doc_id: "",
      search_create_value: "创建时间",
      search_create_date: new Date(),
      search_remittance_value: "费用到账",
      order_fee_withdrawal: "",
      search_remittance_date: new Date(),

      page: 1,
      loading: false,
      finished: false,
      order: [],

      fixedSearchArr: [],
      otherSearchArr: [],
      hospList: [],
      deptList: [],
      selectHospObj: {},
      selectDeptObj: {},
      isHospPopShow: false,
      selectHospOrDept: "",
      hospKeywords: "",
      query_order_type: "",  //工作台传过来的订单类型
    };
  },
  computed: {
    minDate() {
      return new Date(2022, 0)
    },
    maxDate() {
      const date = new Date()
      const y = date.getFullYear() + 1
      let m = date.getMonth()
      return new Date(y, m)
    },
  },
  created() {
    this.workroom_id = this.$route.query.workroom_id;
    this.initParams();
  },
  methods: {
    initParams() {
      const o = {};
      const { order_created_at, sale_id, doc_id, order_ids, order_fee_withdrawal,order_type } = this.$route.query;

      if (order_created_at) {
        o.order_created_at = order_created_at;
        this.query_order_created_at = order_created_at;
        this.search_create_date = new Date(order_created_at);
      }

      if (sale_id) {
        o.channel_doc = sale_id;
        this.query_sale_id = sale_id;
      }

      if (doc_id) {
        o.service_doc_id = doc_id;
        this.query_service_doc_id = doc_id;
      }

      if (order_ids) {
        o.order_ids = order_ids;
      }

      if (order_fee_withdrawal) {
        o.order_fee_withdrawal = order_fee_withdrawal;
        this.order_fee_withdrawal = order_fee_withdrawal;
        this.search_remittance_date = new Date(order_fee_withdrawal);
      }
      if(order_type){
        o.order_type = order_type;
        this.query_order_type = order_type;
      }
      this.getOrderList(o);
      this.getSearchConfig();
    },
    async getOrderList(o = {}) {
      this.loading = true;

      const params = {
        workroom_id: this.workroom_id,
        page: this.page,
        page_size: 10,
        ...o,
      };

      const res = await getChannelOrderData(params);

      if (res.code === 200) {
        const { items, pager } = res.data;
        if (items.length > 0) {
          items.forEach((el) => {
            el.popover_sale_fee = false;
            el.popover_withdrawal_date = false;
          });
        }
        this.order = this.order.concat(items);

        if (pager.current_page == pager.last_page) {
          this.finished = true;
        }

        this.page += 1;

        if(getSystemType() == 'ios'){
          window.webkit.messageHandlers.transferDataTotal.postMessage(pager.total)
        }else{
          window.android.transferDataTotal(pager.total)
        }
      } else {
        this.$toast(res.msg);
      }

      this.loading = false;
    },
    loadData() {
      const o = {};
      const params = this.getSearchParams();
      for (let k in params) {
        o[k] = params[k].toString();
      }

      this.getOrderList(o);
    },
    handleShowOrderDetail(item, index) {
      this.$set(this.order[index], "show_detail", !item.show_detail);
    },
    formatter(type, val) {
      if (type === "year") {
        return `${val}年`;
      } else if (type === "month") {
        return `${val}月`;
      }
      return val;
    },
    getSearchParams() {
      const params = {};
      this.fixedSearchArr.forEach((item) => {
        if (item.name === "order_created_at") {
          // 创建时间
          params[item.name] = item.value || "";
        } else if (item.name === "order_fee_withdrawal") {
          // 提现时间
          params[item.name] = item.value || "";
        } else if (item.name === "order_service_status" || item.name === "order_type") {
          // 服务包状态
          const o = [];
          item.options.forEach((el) => {
            if (el.checked) {
              o.push(el.id);
            }
          });
          params[item.name] = o;
        }
      });

      let keywords_status = 0;
      this.otherSearchArr.forEach((item) => {
        if (item.name === "service_doc_id") {
          // 服务人
          const o = [];
          item.options.forEach((el) => {
            if (el.checked) {
              o.push(el.doc_id);
            }
          });
          if (keywords_status === 1 || o.length > 0) keywords_status = 1;
          params[item.name] = o;
        } else if (item.name === "channel_doc") {
          // 客服经理
          const o = [];
          item.options.forEach((el) => {
            if (el.checked) {
              o.push(el.id);
            }
          });
          if (keywords_status === 1 || o.length > 0) keywords_status = 1;
          params[item.name] = o;
        } else if (item.name === "channel_type" || item.name === "order_service_status") {
          // 管理关系 || 服务状态
          const o = [];
          item.options.forEach((el) => {
            if (el.checked) {
              o.push(el.id);
            }
          });
          if (keywords_status === 1 || o.length > 0) keywords_status = 1;
          params[item.name] = o;
        } else if (item.name === "channel_hosp") {
          // 客服公司
          const o = [];
          item.options.forEach((el) => {
            if (el.checked) {
              o.push(el.id);
            }
          });
          if (keywords_status === 1 || o.length > 0) keywords_status = 1;
          params[item.name] = o;
        } else if (item.name === "hosp_id") {
          // 机构
          params[item.name] = this.selectHospObj.hosp_id
            ? [this.selectHospObj.hosp_id]
            : [];
          if (keywords_status === 1 || params[item.name].length > 0)
            keywords_status = 1;
        } else if (item.name === "dept_id") {
          // 科室
          params[item.name] = this.selectDeptObj.id
            ? [this.selectDeptObj.id]
            : [];
          if (keywords_status === 1 || params[item.name].length > 0)
            keywords_status = 1;
        }
      });

      this.fixedSearchArr.forEach((item) => {
        if (item.name === "keywords") {
          item.title = keywords_status === 1 ? keywords_status : item.label;
        }
      });

      return params;
    },
    radioFun(inner, item, type) {
      const st = inner.checked;

      item.options.forEach((v) => {
        v.checked = false;
      });

      if (st) {
        inner.checked = st;
        return;
      } else {
        inner.checked = !st;
      }

      if (type === "fixed") {
        // 提交
        this.drop_menu_status = 1;
        if (st) {
          item.title = item.label;
        } else {
          item.title = inner.name;
        }

        this.$refs[`dropdownItem_${item.name}`][0].toggle(false);

        this.submitSearch();
      }
    },
    checkFun(inner, item, type) {
      inner.checked = !inner.checked;
    },
    handleDropMenuClosed(item) {
      this.drop_menu_status = 0;
    },
    handleDropMenuClose(item) {
      if (this.drop_menu_status === 1) return;
      const { name, value_type } = item;

      if (value_type === "date") {
        const { date, t } = this.getSelectedDate(item);

        if (this[`${t}_value`] === date) {
          this.submitSearch();
        }
      }

      if (value_type !== "date") {
        this.submitSearch();
      }
    },
    handleDatetimeChange(evt, o) {
      const { date, t } = this.getSelectedDate(o);

      this[`${t}_value`] = date;
      this.fixedSearchArr.forEach((item) => {
        if (item.name === o.name) {
          item.title = date;
          item.value = date;
        }
      });
    },
    getSelectedDate(item) {
      const { name, value_type } = item;
      let e = new Date();
      let t = "";
      if (name === "order_created_at") {
        t = "search_create";
        e = new Date(this.search_create_date);
      } else if (name === "order_fee_withdrawal") {
        t = "search_remittance";
        e = new Date(this.search_remittance_date);
      }

      const y = e.getFullYear();
      let m = e.getMonth() + 1;
      m = m < 10 ? "0" + m : m;
      const date = y + "-" + m;
      return { date, t }
    },
    onSearchDateReset(t, name) {
      this.drop_menu_status = 1;
      if (t === "search_create") {
        this.search_create_value = "创建时间";
      } else if (t === "search_remittance") {
        this.search_remittance_value = "费用到账";
      }

      this.fixedSearchArr.forEach((item) => {
        if (item.name === name) {
          item.title = item.fake_title;
          item.value = "";
        }
      });

      this.$refs[`dropdownItem_${name}`][0].toggle(false);

      this.submitSearch();
    },
    onSearchDateConfirm(t, name) {
      this.drop_menu_status = 1;
      let e = new Date();
      if (t === "search_create") {
        e = new Date(this.search_create_date);
      } else if (t === "search_remittance") {
        e = new Date(this.search_remittance_date);
      }

      const y = e.getFullYear();
      let m = e.getMonth() + 1;
      m = m < 10 ? "0" + m : m;
      const date = y + "-" + m;

      this[`${t}_value`] = date;
      this.fixedSearchArr.forEach((item) => {
        if (item.name === name) {
          item.title = date;
          item.value = date;
        }
      });

      this.$refs[`dropdownItem_${name}`][0].toggle(false);

      this.submitSearch();
    },
    onSearchResetInner(item) {
      this.drop_menu_status = 1;

      this.otherSearchArr.forEach((el) => {
        if (el.options.length > 0) {
          el.options.forEach((el) => {
            el.checked = false;
          });
        }
        if (el.name === "hosp_id") {
          // 机构
          this.selectHospObj.hosp_id = "";
          this.selectHospObj.name = "";
          this.selectHospObj = {};
        } else if (el.name === "dept_id") {
          // 科室
          this.selectDeptObj.id = "";
          this.selectDeptObj.name = "";
          this.selectDeptObj = {};
        }
      });

      this.$refs[`dropdownItem_${item.name}`][0].toggle(false);

      this.submitSearch();
    },
    onSearchConfirmInner(item) {
      this.drop_menu_status = 1;

      this.$refs[`dropdownItem_${item.name}`][0].toggle(false);

      this.submitSearch();
    },
    submitSearch() {
      const o = {};
      const params = this.getSearchParams();
      for (let k in params) {
        o[k] = params[k].toString();
      }

      this.page = 1;
      this.loading = false;
      this.finished = false;
      this.order = [];

      this.getOrderList(o);
    },
    // 筛选下发
    async getSearchConfig() {
      const res = await getChannelSearchConfig({
        workroom_id: this.workroom_id,
      });
      if (res.code === 200) {
        let { fixed_search_configs, other_search_configs } = res.data;
        if (fixed_search_configs && fixed_search_configs.length > 0) {
          fixed_search_configs.forEach((item) => {
            if (item.options && item.options.length > 0) {
              item.title = item.label;
              item.options.forEach((option) => {
                option.checked = false;
              });
            }

            if (
              item.name === "order_created_at" ||
              item.name === "order_fee_withdrawal"
            ) {
              item.title = item.label;
            }

            item.fake_title = item.title;
            if (item.name === "order_service_status" || item.name === "keywords") {
              item.fake_title = item.label;
            }
            if (item.name === "keywords") {
              item.title = item.label;
              item.fake_title = item.label;

              if (this.query_sale_id) {
                item.title = 1;
              }

              if (this.query_service_doc_id) {
                item.title = 1;
              }
            }

            // 从URL传参的筛选项 - 创建时间
            if (
              item.name === "order_created_at" &&
              this.query_order_created_at
            ) {
              item.title = this.query_order_created_at;
              item.value = this.query_order_created_at;
            }

            if (
              item.name === "order_fee_withdrawal" &&
              this.order_fee_withdrawal
            ) {
              item.title = this.order_fee_withdrawal;
              item.value = this.order_fee_withdrawal;
            }
            if(item.name == "order_type" && this.query_order_type){
              item.value = this.query_order_type;
              item.options.forEach(inner=>{
                if(inner.id == this.query_order_type){
                  inner.checked = true
                  item.title = inner.name
                }
              })
            }
          });
        }
        if (other_search_configs && other_search_configs.length > 0) {
          other_search_configs.forEach((item) => {
            if (item.options && item.options.length > 0) {
              item.title = item.label;
              item.options.forEach((option) => {
                option.checked = false;
              });
            }
          });
        }
        this.fixedSearchArr = fixed_search_configs;
        this.otherSearchArr = other_search_configs;
        if (other_search_configs && other_search_configs.length > 0) {
          other_search_configs.forEach((item) => {
            item.is_api && this.getSearchConfigApi(item)
          })
        }
      } else {
        this.$toast(res.msg);
      }
    },
    async getSearchConfigApi(o) {
      const { url, name } = o
      const res = await getChannelSearchConfigApi(url, {
        workroom_id: this.workroom_id,
      })

      if (res.code === 200) {
        this.otherSearchArr.forEach(item => {
          // 客服公司
          if (item.name === name && name === "channel_hosp") {
            item.title = "客服公司";
            item.selected = "";
            item.options = res.data.map((inner) => {
              inner.checked = false;
              return inner;
            });
          } else if (item.name === name && name === "channel_doc") {
            item.title = "客服经理";
            item.selected = "";
            item.options = res.data.map((inner) => {
              inner.checked = false;
              if (inner.name == "") inner.name = inner.cell;
              // 从URL传参的筛选项 - 客服经理 channel_doc
              if (this.query_sale_id && this.query_sale_id == inner.id) {
                inner.checked = true;
              }
              return inner;
            });
          } else if (item.name === name && name === "service_doc_id") {
            item.title = "服务人";
            item.selected = "";
            item.options = res.data.map((inner) => {
              inner.checked = false;
              inner.name = inner.doc_name;
              // 从URL传参的筛选项 - 客服经理 channel_doc
              if (this.query_service_doc_id && this.query_service_doc_id == inner.doc_id) {
                inner.checked = true;
              }
              return inner;
            });
          }
        })
      } else {
        this.$toast(res.msg);
      }
    },
    openHospPop(type) {
      this.selectHospOrDept = type;
      this.isHospPopShow = true;
      this.hospKeywords = "";
      this.hospList = [];
    },
    hospCancel() {
      // this.selectHospOrDept == 'hosp' ? this.selectHospObj = {} : this.selectDeptObj = {}
      // this.closeHospPop()
    },
    hospConfirm() {
      if (this.selectHospOrDept === "hosp") {
        getChannelDeptList({ hosp_id: this.selectHospObj.hosp_id }).then(
          (res) => {
            if (res.code === 200) {
              this.selectDeptObj.id = "";
              this.selectDeptObj.name = "";
              this.selectDeptObj = {};
              this.deptList = res.data;
            }
          }
        );
      }
      this.closeHospPop();
    },
    closeHospPop() {
      this.isHospPopShow = false;
    },
    getHospList() {
      getChannelHospList({ keyword: this.hospKeywords }).then((res) => {
        if (res.code === 200) {
          this.hospList = res.data;
        }
      });
    },
    formateSalefeText(item) {
      const { sale_fee, service_status,order_type } = item;
      if(order_type == 2){
        if (service_status == 0) {
          return "等待中";
        } else if (service_status == 1) {
          return `+¥${sale_fee}`;
        } else if (service_status == 2) {
          return "+¥0";
        }
      }else if(order_type == 9){
        if (service_status == 1) {
          return "等待中";
        } else if (service_status == 2) {
          return `+¥${sale_fee}`;
        } else {
          return "+¥0";
        }
      }else{
        if (service_status == 1 || service_status == 2) {
          return "等待中";
        } else if (service_status == 4) {
          return `+¥${sale_fee}`;
        } else {
          return "+¥0";
        }
      }
    },

    resetFun() {
      this.otherSearchArr.forEach((item) => {
        item.selected = "";
        if (item.name === "hosp_id") {
          this.selectHospObj = {};
          this.selectDeptObj = {};
        }
        item.options.forEach((inner) => {
          inner.checked = false;
        });
      });
    },
    submitFun() {
      this.$refs.dropdownItem[this.$refs.dropdownItem.length - 1].toggle(false);
    },
    goCheck(orderNo){
      let url = `/docWorkRoom/consigneeInfo?orderNo=${orderNo}`
      if(getSystemType() == 'ios'){
        window.webkit.messageHandlers.breathMessage.postMessage(url)
      }else{
        window.android.breathMessage(url)
      }
    }
  },
};
</script>

<style scoped lang="scss">
.channel-order {
  min-height: 100vh;
  overflow: hidden;
  background-color: #f5f5f5;
  ::v-deep .van-popup {
    padding-bottom: 0;
  }
  ::v-deep .hospPopup {
    height: 90%;
    padding: 16px;
    box-sizing: border-box;
    overflow: hidden;
    // .topBlock {
    //   height: 100px;
    // }
    .radioList {
      margin-top: 16px;
      padding: 0 18px;
      // height: calc(100% - 100px);
      overflow: auto;
      .radioItem {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 0;
        font-size: 16px;
        color: #0a0a0a;
        border-bottom: 1px solid #f5f5f5;
        line-height: 24px;
        > span {
          text-align: left;
        }
      }
      .checked {
        color: #f7830d;
      }
    }

    .titleLine {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 16px;
      height: 30px;
      line-height: normal;
      .cancel {
        color: transparent;
      }
      .confirm {
        color: #f7830d;
        font-weight: 500;
      }
      .title {
        color: #000;
      }
    }
    .searchLine {
      margin-top: 35px;
    }
    .van-search {
      padding: 0 !important;
    }
  }
  ::v-deep .dropdown-menu-search {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    z-index: 10;
    .van-dropdown-menu__bar {
      .active {
        color: #f7830d;
      }
    }
    .dropdown-item-screening {
      .van-dropdown-item__content {
        max-height: initial;
        height: calc(100vh - 48px);
      }
      .screening {
        background-color: #f5f6fb;
        li {
          margin: 9px 0 0;
          padding: 0 20px 15px;
          background: #fff;
          .label {
            padding: 9px 15px;
            font-size: 15px;
            text-align: left;
            font-weight: 500;
            color: #000;
          }
          .content {
            display: flex;
            span {
              display: inline-block;
              margin-right: 9px;
              border-radius: 6px;
              padding: 8px 29px;
              font-size: 15px;
              color: #666;
              background-color: #f5f6fb;
            }
          }
        }
      }
    }
    .customDropMenu {
      padding: 15px;
      &.package_status {
        padding: 0;
      }
      .labelLine {
        font-size: 15px;
        font-weight: 500;
        color: #0a0a0a;
        text-align: left;
        line-height: normal;
      }
      .hospDeptBlock {
        padding: 14px 0 30px 0;
        .hospLine {
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 10px 18px;
          border-radius: 6px;
          background: #f5f5f5;
        }
        .deptLine {
          margin-top: 16px;
        }
        .label {
          font-size: 15px;
          color: #666666;
        }
        .arrowRotate {
          transform: rotate(90deg);
          margin-left: 12px;
        }
      }

      .checkList {
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 30px;
        .checkItem {
          line-height: normal;
          font-size: 15px;
          padding: 6px 24px;
          border-radius: 6px;
          background: #f5f5f5;
          margin: 14px 12px 0 0;
          box-sizing: border-box;
          color: #666666;
        }
        .checked {
          color: #f7830d;
        }
      }

      .btnOuter {
        display: flex;
        justify-content: space-between;
        padding-top: 20px;

        .cancelBtn,
        .confirmBtn {
          width: 162px;
          height: 44px;
          border-radius: 8px;
          font-size: 16px;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .cancelBtn {
          color: #0a0a0a;
          background: #f5f5f5;
        }

        .confirmBtn {
          color: white;
          background: #f7830d;
        }
      }
    }

    .radioList {
      padding: 0 15px;
      .radioItem {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 0;
        font-size: 16px;
        color: #0a0a0a;
        border-bottom: 1px solid #f5f5f5;
        line-height: 24px;
      }
      .checked {
        color: #f7830d;
      }
    }

    .footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 0 10px;
      &.inner {
        padding: 5px 0 10px;
      }
      .btn {
        display: inline-block;
        width: 162px;
        height: 44px;
        line-height: 44px;
        text-align: center;
        border-radius: 6px;
        font-weight: 500;
        font-size: 16px;
        color: #0a0a0a;
        background-color: #f5f6fb;
        &.confirm {
          color: #fff;
          background-color: #f7830d;
        }
      }
    }
  }

  ::v-deep .van-list {
    margin-top: 57px;
    .container {
      > div {
        margin: 9px 0;
        background-color: #fff;
        .header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 13px 15px;
          border-bottom: 1px solid #f5f5f5;
          .lf {
            display: flex;
            align-items: center;
            .logo {
              position: relative;
              width: 46px;
              min-width: 46px;
              height: 46px;
              border-radius: 5px;
              img {
                display: inline-block;
                width: 100%;
                height: 100%;
                border-radius: 5px;
                vertical-align: top;
              }
              .st {
                position: absolute;
                left: 0;
                bottom: 0;
                width: 100%;
                height: 18px;
                line-height: 18px;
                font-size: 12px;
                border-radius: 0 0 5px 5px;
                text-align: center;
                color: #fff;
                background-color: rgba(255, 111, 0, 0.7);
              }
            }
            .title {
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 2;
              margin-left: 9px;
              font-size: 17px;
              font-weight: 500;
              line-height: 1.3;
              text-align: left;
              color: #0a0a0a;
            }
          }
          .rt {
            width: 66px;
            min-width: 66px;
            font-size: 14px;
            font-weight: 500;
            text-align: right;
            color: #ff6f00;
          }
        }
        .service {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 16px 15px;
          box-sizing: border-box;
          &.doc_type_3 {
            justify-content: space-around;
            .md, .rt {
              text-align: center;
            }
          }
          > div {
            width: 33.33%;
            &.lf {
              text-align: left;
            }
            &.md {
              text-align: center;
            }
            &.rt {
              text-align: right;
            }
            .top {
              font-size: 15px;
              font-weight: 500;
              color: #333;
            }
            .bot {
              margin-top: 12px;
              font-size: 13px;
              color: #666;
              .channel-order-popover {
                .van-popover__content {
                  max-width: 150px;
                  padding: 9px 12px;
                  line-height: 1.3;
                  font-size: 12px;
                  color: #666;
                  text-align: left;
                }
              }
            }
          }
        }
        .more {
          display: flex;
          justify-content: center;
          align-items: center;
          height: 30px;
          > span {
            display: flex;
            align-items: center;
          }
          .label {
            font-size: 12px;
            color: #999;
          }
        }
        .content {
          padding: 3px 0;
          text-align: left;
          font-size: 13px;
          margin: 0 15px;
          position: relative;
          &.on {
            border-top: 1px solid #f5f5f5;
          }
          > div {
            margin: 12px 0;
          }
          .label {
            color: #333;
          }
          .value {
            color: #666;
          }
          .checkConsigneeInfoBtn{
            position: absolute;
            right: 0;
            top: 16px;
            color: #377cfb;
          }
        }
      }
    }
  }
}
</style>
