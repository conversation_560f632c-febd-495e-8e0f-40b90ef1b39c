<!--
 * @Descripttion: 医生工作室对接-科室管理患者情况
 * @version:
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-27 10:02:10
 * @LastEditors: guxiang
 * @LastEditTime: 2022-01-12 13:55:36
-->

<template>
    <div class="wrapper">
        <van-cell title-class="label" value-class="value" size="large" v-for="item in this.items" class="list-item"
            :key="item.id" :title="item.business_name" :value="`${item.user_cnt}人`" :center="true" />
    </div>
</template>


<script>
import { getDeptPatients } from "@/api/docWorkRoom.js";
export default {
    data() {
        return {
            items: [],
        };
    },
    created() {
        let obj = {
            doc_id: this.$route.query.doc_id,
        }
        getDeptPatients(obj).then(res => {
            if (res.code == 200) {
                this.items = res.data;
            } else {
                this.$toast(res.msg || '请求错误')
            }
        })
    },
    methods: {
    },
};
</script>

<style lang="scss" scoped>
.wrapper {
    background: white;
    font-size: 18px;
    padding: 0px 0;
    box-sizing: border-box;

    .list-item {
        display: flex;
        justify-content: space-between;
        padding: 10px;
        // border: 1.5px solid #000;
    }

    .label {
        color: #0A0A0A;
        font-size: 16px;
    }

    .value {
        color: #999999;
        font-size: 15px;
    }

    // .list-item:first-child {
    //    margin: 10px 0px 0px 0px;
    // }
}
</style>