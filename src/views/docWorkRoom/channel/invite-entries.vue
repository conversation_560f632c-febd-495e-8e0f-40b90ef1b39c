<template>
  <div class="invite-entries">
    <div class="item" @click="handleGetInvite('invite_channel_agent')">
      <div class="lf">
        <img class="icon" src="../../../assets/images/channel/icon-company.png" alt="icon">
      </div>
      <div class="md">
        <p class="title">邀请代理商</p>
        <p class="content">合作说明：限以公司为主体进行合作，通过机构对公方式按月结算费用</p>
      </div>
      <div class="rt">
        <van-icon name="arrow" color="#B3B3B3" />
      </div>
    </div>
    <div class="item" @click="handleGetInvite('invite_channel_personal')">
      <div class="lf">
        <img class="icon" src="../../../assets/images/channel/icon-personal.png" alt="icon">
      </div>
      <div class="md">
        <p class="title">邀请个人</p>
        <p class="content">合作说明：限以个人为主体进行合作，通过灵活用工方式按月结算费用</p>
      </div>
      <div class="rt">
        <van-icon name="arrow" color="#B3B3B3" />
      </div>
    </div>
  </div>
</template>

<script>
import { loadNativePage } from '@/utils/utils'
export default {
  data() {
    return {
      
    }
  },
  created() {

  },
  methods: {
    handleGetInvite(t) {
      loadNativePage(`/docWorkRoom/invite?channel_type=${t}&invite_reload=1`)
    }
  },
}
</script>

<style scoped lang="scss">
.invite-entries {
  min-height: 100vh;
  padding-top: 15px;
  background-color: #F5F5F5;
  .item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 345px;
    height: 120px;
    margin: 0 auto 15px;
    padding: 0 15px;
    border-radius: 8px;
    box-sizing: border-box;
    background-color: #FFFFFF;
    .icon {
      width: 46px;
      height: 46px;
    }
    .md {
      margin: 0 15px;
      text-align: left;
      .title {
        margin-bottom: 8px;
        font-size: 17px;
        font-weight: 500;
        color: #0A0A0A;
      }
      .content {
        line-height: 1.4;
        font-size: 15px;
        color: #666666;
      }
    }
  }
}
</style>