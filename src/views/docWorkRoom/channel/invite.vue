<template>
  <div class="invite">
    <div class="header">
      <span class="lf">扫码页面显示推荐人名称：</span>
      <span class="rt" @click="handleSetName">
        <span :class="`name ${referrer_name_st}`">{{ referrer_name_desc }}</span>
        <van-icon name="arrow" color="#B3B3B3" />
      </span>
    </div>
    <div class="code" id="code" :style="`background-image: url(${bg});`">
      <span class="content">
        <img class="qrcode" crossorigin="anonymous" :src="`${qrcode_url}?t=${ts}`" />
        <span :class="`tips ${channel_type}`">扫描二维码 快速加入{{ desc }}</span>
      </span>
    </div>
    <div class="footer">
      <span class="btn" @click="handleDownload">下载二维码</span>
    </div>
  </div>
</template>

<script>
import html2canvas from "html2canvas"
import { loadNativePage, getSystemType } from '@/utils/utils'
import { getinvitationQrcode } from '@/api/docWorkRoom'

const invite_channel_agent = require('../../../assets/images/channel/invite-code-bg-1.png') // 代理商邀请码
const invite_channel_personal = require('../../../assets/images/channel/invite-code-bg-2.png') // 个人邀请码
const invite_member = require('../../../assets/images/channel/invite-code-bg-3.png') // 邀请员工
const doc_intro_distribution = require('../../../assets/images/channel/invite-code-bg-4.png') // 邀请码
export default {
  data() {
    return {
      ts: new Date().getTime(),
      channel_type: '',
      bg: '',
      desc: '',
      qrcode_url: '',
      is_show_referrer: '',
      referrer_name: '',
      channel_id: '',
    };
  },
  computed: {
    referrer_name_st() {
      if (this.is_show_referrer && this.referrer_name) {
        return 'on'
      }
      return 'off'
    },
    referrer_name_desc() {
      if (this.is_show_referrer) {
        if (this.referrer_name) {
          return this.referrer_name
        }
        return '空'
      } else {
        return '不显示'
      }
    },
  },
  created() {
    this.channel_type = this.$route.query.channel_type
    this.setType(this.channel_type)
    this.init()
  },
  methods: {
    setType(t) {
      let title = ''
      switch (t) {
        case 'invite_channel_agent':
          this.bg = invite_channel_agent
          this.desc = '公司工作室'
          title = '我的代理商邀请码'
          break

        case 'invite_channel_personal':
          this.bg = invite_channel_personal
          this.desc = ''
          title = '我的个人邀请码'
          break

        case 'invite_member':
          this.bg = invite_member
          this.desc = '公司'
          title = '邀请员工'
          break

        case 'doc_intro_distribution':
          this.bg = doc_intro_distribution
          this.desc = '医生工作室'
          title = '我的邀请码'
          break
    
        default:
          break;
      }
      document.title = title
    },
    async init() {
      const res = await getinvitationQrcode({ channel_type: this.channel_type })

      if (res.code === 200) {
        this.qrcode_url = res.data.qrcode_url
        this.is_show_referrer = res.data.is_show_referrer
        this.referrer_name = res.data.referrer_name
        this.channel_id = res.data.channel_id
      } else {
        this.$toast(res.msg)
      }
    },
    handleDownload() {
      const that = this
      const element = document.getElementById('code')
      this.ts = +new Date()
    
      html2canvas(element, {
        scale: 3,
        allowTaint: true,
        useCORS: true,
        backgroundColor: 'transparent',
        // ignoreElements: [document.querySelector('.footer')],
        onclone: () => {
          that.ts = +new Date()
        }
      }).then((canvas) => {
          const image = canvas.toDataURL('image/png')

          // let link = document.createElement('a')
          // link.download = 'my-image.png'
          // link.href = image
          // link.click()

          const base64 = image.slice(22)
          if (getSystemType() === 'ios') {
            window.webkit.messageHandlers.saveImageToAlbum.postMessage(base64)
          } else if (getSystemType() === 'android') {
            window.android.saveImageToAlbum(base64)
          }
      })
    },
    handleSetName() {
      // this.$router.push({
      //   path: '/docWorkRoom/invite-referral',
      //   query: {
      //     channel_type: this.channel_type,
      //     channel_id: this.channel_id,
      //   }
      // })

      loadNativePage(`/docWorkRoom/invite-referral?channel_type=${this.channel_type}&channel_id=${this.channel_id}`)
    },
  },
}
</script>

<style scoped lang="scss">
.invite {
  width: 100%;
  padding-bottom: 120px;
  position: relative;
  min-height: 585px;
  background-color: #fff;
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 45px;
    padding: 0 15px;
    background-color: #F5F5F5;
    .lf {
      font-size: 15px;
      color: #666666;
    }
    .rt {
      padding: 9px 0 9px 12px;
      .name {
        margin-right: 5px;
        font-size: 15px;
        &.on {
          color: #666666;
        }
        &.off {
          color: #0A0A0A;
        }
      }
    }
  }
  .code {
    // width: 343px;
    // height: 570px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    top: 60px;
    // left: calc(50% - 343px);
    left: calc(50% - 172px);
    border-radius: 32px;
    width: 1372px;
    height: 2280px;
    transform: scale(0.25);
    transform-origin: 0% 0%;
    background-color: transparent;
    background-position: center center;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    // background: transparent url('../../../assets/images/channel/invite-code-bg-1.png') center/100% no-repeat;
    .content {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      margin-top: 120px;
      padding: 50px 80px 0;
      border-radius: 48px;
      background-color: #fff;
      .qrcode {
        // width: 235px;
        // height: 235px;
        width: 940px;
        height: 940px;
      }
      .tips {
        padding: 50px 0 70px;
        font-size: 60px;
        &.invite_channel_agent {
          color: #688ACD;
        }
        &.invite_channel_personal {
          color: #2B995E;
        }
        &.invite_member {
          color: #688ACD;
        }
        &.doc_intro_distribution {
          color: #CD9668;
        }
      }
    }
  }
  .footer {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 20px 0;
    background-color: #fff;
    .btn {
      display: inline-block;
      width: 135px;
      height: 33px;
      line-height: 35px;
      text-align: center;
      border-radius: 8px;
      border: 1px solid #F7830D;
      color: #F7830D;
    }
  }
}
</style>