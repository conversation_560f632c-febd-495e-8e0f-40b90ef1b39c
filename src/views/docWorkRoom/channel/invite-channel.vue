<template>
  <div class="invite-channel">
    <div class="header">
      <span class="lf">扫码页面显示推荐人名称：</span>
      <span class="rt" @click="handleSetName">
        <span class="name">不显示</span>
        <van-icon name="arrow" color="#B3B3B3" />
      </span>
    </div>
    <div class="code" id="code" :style="`background-image: url(${bg});`">
      <span class="content">
        <!-- <img class="qrcode" crossorigin="anonymous" :src="`https://ares.zz-med-test.com/pro/qrcode/channel/206da1d82c31fae24497302eb7934f6c.png?t=${ts}`" /> -->
        <img class="qrcode" src="https://zz-med-test-pub.oss-cn-hangzhou.aliyuncs.com/qrcode.png" />
        <!-- <img class="qrcode" crossorigin="anonymous" :src="`https://zz-med-test-pub.oss-cn-hangzhou.aliyuncs.com/pro/qrcode/channel/3c07e4621d41aaa57cd22ce957713d6d.png?t=${ts}`" /> -->
        <span :class="`tips type_${type}`">扫描二维码 快速加入公司工作室</span>
      </span>
    </div>
    <div class="footer">
      <span class="btn" @click="handleDownload">下载二维码</span>
    </div>
  </div>
</template>

<script>
import html2canvas from "html2canvas"
import { getSystemType } from '@/utils/utils'

// const bg1 = require('../../../assets/images/channel/invite-code-bg-1.png')
const bg1 = require('../../../assets/images/channel/invite-code-bg-2.png')
const bg2 = require('../../../assets/images/channel/invite-code-bg-3.png')
export default {
  data() {
    return {
      ts: new Date().getTime(),
      type: '',
      bg: '',
    };
  },
  created() {
    const type = this.$route.query.type
    this.type = type
    if (type === '1') {
      this.bg = bg1
    } else if (type === '2') {
      this.bg = bg2
    }
  },
  methods: {
    handleDownload() {
      const that = this
      const element = document.getElementById('code')
      this.ts = +new Date()
    
      // 使用 html2canvas 截取指定区域内容并生成图片
      html2canvas(element, {
        scale: 3,
        allowTaint: true,
        useCORS: true,
        backgroundColor: 'transparent',
        // ignoreElements: [document.querySelector('.footer')],
        onclone: () => {
          that.ts = +new Date()
        }
      }).then((canvas) => {
          // 将 Canvas 转换为图片并保存到手机本地
          // let image = canvas.toDataURL('image/png').replace('image/png', 'image/octet-stream')

          const image = canvas.toDataURL('image/png')

          // let link = document.createElement('a')
          // link.download = 'my-image.png'
          // link.href = image
          // link.click()


          const base64 = image.slice(22)
          console.log(base64)
          if (getSystemType() === 'ios') {
            window.webkit.messageHandlers.saveImageToAlbum.postMessage(base64)
          } else if (getSystemType() === 'android') {
            window.android.saveImageToAlbum(base64)
          }


          // const imgUrl = canvas.toDataURL('image/png')
          // that.downloadIamge(imgUrl, "my-image.png")
      })
    },
    handleSetName() {
      this.$router.push({
        name: 'docWorkRoom.inviteReferral',
        query: {
          name: 'yushare'
        }
      })
    },
    downloadIamge(imgsrc, name) {  //下载图片地址和图片名
      const image = new Image();
      // 解决跨域 Canvas 污染问题
      image.setAttribute("crossOrigin", "anonymous");
      image.onload = function () {
        let canvas = document.createElement("canvas");
        canvas.width = image.width;
        canvas.height = image.height;
        const context = canvas.getContext("2d");
        context.drawImage(image, 0, 0, image.width, image.height);
        const url = canvas.toDataURL("image/png"); //得到图片的base64编码数据

        // const base64 = url.slice(22)
        // if (getSystemType() === 'ios') {
        //   window.webkit.messageHandlers.saveImageToAlbum.postMessage(base64)
        // } else if (getSystemType() === 'android') {
        //   window.android.saveImageToAlbum(base64)
        // }

        const a = document.createElement("a"); // 生成一个a元素
        const event = new MouseEvent("click"); // 创建一个单击事件
        a.download = name || "photo"; // 设置图片名称
        a.href = url; // 将生成的URL设置为a.href属性
        a.dispatchEvent(event); // 触发a的单击事件
      };
      image.src = imgsrc;
    }
  },
}
</script>

<style scoped lang="scss">
.invite-channel {
  width: 100%;
  padding-bottom: 120px;
  position: relative;
  min-height: 585px;
  background-color: #fff;
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 45px;
    padding: 0 15px;
    background-color: #F5F5F5;
    .lf {
      font-size: 15px;
      color: #666666;
    }
    .rt {
      .name {
        margin-right: 5px;
        font-size: 15px;
        color: #0A0A0A;
      }
    }
  }
  .code {
    // width: 343px;
    // height: 570px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    top: 60px;
    // left: calc(50% - 343px);
    left: calc(50% - 172px);
    border-radius: 32px;
    width: 1372px;
    height: 2280px;
    transform: scale(0.25);
    transform-origin: 0% 0%;
    background-color: transparent;
    background-position: center center;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    // background: transparent url('../../../assets/images/channel/invite-code-bg-1.png') center/100% no-repeat;
    .content {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      margin-top: 120px;
      padding: 50px 80px 0;
      border-radius: 48px;
      background-color: #fff;
      .qrcode {
        // width: 235px;
        // height: 235px;
        width: 940px;
        height: 940px;
      }
      .tips {
        padding: 50px 0 70px;
        font-size: 60px;
        &.type_1 {
          color: #688ACD;
        }
        &.type_2 {
          color: #CD9668;
        }
      }
    }
  }
  .footer {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 20px 0;
    background-color: #fff;
    .btn {
      display: inline-block;
      width: 135px;
      height: 33px;
      line-height: 35px;
      text-align: center;
      border-radius: 8px;
      border: 1px solid #F7830D;
      color: #F7830D;
    }
  }
}
</style>