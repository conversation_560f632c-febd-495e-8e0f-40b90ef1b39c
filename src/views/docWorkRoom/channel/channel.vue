<template>
  <div class="channel">
    <van-tabs v-model="active" sticky @change="handleChange">
      <van-tab title="代理商渠道" name="1" class="channel-company">
        <div class="add" @click="handleInvite">
          <van-icon name="add-o" color="#CE8953" size="18px" />
          <span class="tips">邀请渠道</span>
        </div>
        <ul class="container" v-if="list_company.length > 0">
          <li v-for="(item, index) in list_company" :key="index" @click="handleGetCompany(item.hosp_id)">
            <div class="lf">
              <img src="../../../assets/images/channel/logo-company.png" alt="logo">
            </div>
            <div class="md">
              <div class="name">{{ item.hosp_name }}</div>
              <div class="intro">
                <div>
                  <span class="label">管理员：</span>
                  <span class="value">{{ item.admin_name }}</span>
                </div>
                <div>
                  <span class="label">员工数：</span>
                  <span class="value">{{ item.agent_cnt }}人</span>
                </div>
              </div>
            </div>
            <div class="rt">
              <van-icon name="arrow" color="#B3B3B3" />
            </div>
          </li>
        </ul>
        <div class="empty" v-if="status && list_company.length === 0">
          <img src="../../../assets/images/channel/empty.png" alt="empty">
          <span>暂无数据</span>
        </div>
      </van-tab>
      <van-tab title="个人渠道" name="2" class="channel-personal">
        <div class="add" @click="handleInvite">
          <van-icon name="add-o" color="#CE8953" size="18px" />
          <span class="tips">邀请渠道</span>
        </div>
        <ul class="container" v-if="list_personal.length > 0">
          <li v-for="(item, index) in list_personal" :key="index" :class="{gray: item.is_enable == 0 ? true : false}">
            <div class="person">
              <div class="lf">
                <span :class="`logo ${item.sale_name ? 'on' : 'off'}`">{{ item.sale_name ? item.sale_name[0] : '未' }}</span>
              </div>
              <div class="rt">
                <div class="name">{{ item.sale_name || '未完善信息' }}</div>
                <div class="tel">{{ item.sale_cell }}</div>
              </div>
              <div 
                v-if="item.is_enable == 1" 
                @click="handleSet(item)"
                class="set">
                <span class="set-item">设置</span>
              </div>
              <div v-else class="set">
                <span class="set-item">已停用</span>
              </div>
            </div>
            <div class="sub">
              <div @click="handleGetDoctor(item)">
                <span class="label">服务客户：</span>
                <span class="value">{{ item.service_doc_cnt }}人</span>
                <van-icon name="arrow" color="#B3B3B3" />
              </div>
              <div @click="handleGetOrder(item.sale_id)">
                <span class="label">产生订单：</span>
                <span class="value">{{ item.service_order_cnt }}单</span>
                <van-icon name="arrow" color="#B3B3B3" />
              </div>
            </div>
          </li>
        </ul>
        <div class="empty" v-if="status && list_personal.length === 0">
          <img src="../../../assets/images/channel/empty.png" alt="empty">
          <span>暂无数据</span>
        </div>
      </van-tab>
    </van-tabs>
  </div>
</template>

<script>
import { loadNativePage } from '@/utils/utils'
import { getChannelListData } from '@/api/docWorkRoom'

export default {
  data() {
    return {
      workroom_id: '',
      active: '1',
      status: 0,
      list_company: [],
      list_personal: [],
    }
  },
  created() {
    this.workroom_id = this.$route.query.workroom_id
    this.init()
    this.active = this.$route.query.person_channel_from ? '2' : '1'
  },
  methods: {
    async init() {
      const res = await getChannelListData({ workroom_id: this.workroom_id })
      if (res.code === 200) {
        const { agent, personal } = res.data
        this.list_company = agent || []
        this.list_personal = personal || []
        this.status = 1
      } else {
        this.$toast(res.msg)
      }
    },
    setPersonLogo(name) {
      if (name) {
        return name[0]
      }
      return '未'
    },
    // 跳转代理商渠道
    handleGetCompany(hosp_id) {
      // this.$router.push({
      //   path: '/docWorkRoom/channel-company',
      //   query: {
      //     workroom_id: this.workroom_id,
      //     hosp_id,
      //     entry: 'inner'
      //   }
      // })

      loadNativePage(`/docWorkRoom/channel-company?workroom_id=${this.workroom_id}&hosp_id=${hosp_id}&entry=inner&invite_reload=1`)
    },
    // 邀请渠道
    handleInvite() {
      // this.$router.push({
      //   path: '/docWorkRoom/invite-entries',
      //   query: {
      //     workroom_id: this.workroom_id,
      //   }
      // })

      loadNativePage(`/docWorkRoom/invite-entries?workroom_id=${this.workroom_id}`)
    },
    // 跳转服务客户
    handleGetDoctor(item) {
      // this.$router.push({
      //   path: '/docWorkRoom/myServeDocList',
      //   query: {
      //     workroom_id: this.workroom_id,
      //     channel_doc: item.sale_id
      //   }
      // })

      loadNativePage(`/docWorkRoom/myServeDocList?workroom_id=${this.workroom_id}&channel_doc=${item.sale_id}`)
    },
    // 跳转订单列表
    handleGetOrder(sale_id) {
      // this.$router.push({
      //   path: '/docWorkRoom/channel-order',
      //   query: {
      //     sale_id,
      //     workroom_id: this.workroom_id,
      //   }
      // })

      loadNativePage(`/docWorkRoom/channel-order?workroom_id=${this.workroom_id}&sale_id=${sale_id}`)
    },
    // 调整停用渠道页面
    handleSet ({sale_name, sale_cell, is_enable, sale_id}) {
      let param = `/docWorkRoom/deactivateChannel?sale_name=${sale_name}&sale_cell=${sale_cell}&is_enable=${is_enable}&sale_id=${sale_id}&workroom_id=${this.workroom_id}`
      if (process.env.NODE_ENV == 'development') {
        this.$router.push({
          path: encodeURI(param)
        })
      } else {
        loadNativePage(encodeURI(param))
      }
    },
    handleChange (name) {
      this.active = name
      if (name == 2 && !location.search.includes('person_channel_from')) {
        let search = location.search + '&person_channel_from=1'
        this.$router.replace(location.pathname + search)
      } else if (name == 1 && location.search.includes('person_channel_from')){
        let search = location.search.replace('&person_channel_from=1', '')
        this.$router.replace(location.pathname + search)
      }
    }
  },
}
</script>

<style scoped lang="scss">
.channel {
  min-height: 100vh;
  overflow: hidden;
  background-color: #f5f5f5;
  ::v-deep .van-tabs {
    >div{
      .van-sticky {
        padding: 2px 0 6px;
        background-color: #fff;
        > div {
          width: 60vw;
          .van-tabs__line {
            background-color: #FF6F00 !important;
          }
          .van-tab {
            .van-tab__text {
              font-size: 17px;
              color: #878F99;
            }
            &.van-tab--active {
              .van-tab__text {
                font-weight: 500;
                color: #0A0A0A;
              }
            }
          }
        }
      }
    }
  }
  .add {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 45px;
    background-color: #FFE4CF;
    .tips {
      margin-left: 6px;
      font-size: 17px;
      color: #CE8953;
    }
  }
  .channel-company {
    ul {
      li {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 345px;
        padding: 15px;
        margin: 12px auto;
        border-radius: 8px;
        box-sizing: border-box;
        background-color: #fff;
        .lf {
          img {
            display: inline-block;
            width: 46px;
            height: 46px;
            border-radius: 50%;
            vertical-align: top;
          }
        }
        .md {
          width: 100%;
          margin: 0 15px;
          text-align: left;
          .name {
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient:vertical;
            -webkit-line-clamp:2;
            margin-bottom: 9px;
            font-size: 17px;
            font-weight: 500;
            line-height: 1.3;
            color: #0A0A0A;
          }
          .intro {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 15px;
            .label {
              color: #0A0A0A;
            }
            .value {
              color: #666666;
            }
          }
        }
      }
    }
  }
  .channel-personal {
    .container {
      li {
        width: 345px;
        // padding: 0 15px;
        margin: 12px auto;
        border-radius: 8px;
        box-sizing: border-box;
        background-color: #fff;
        .person {
          display: flex;
          // justify-content: space-between;
          align-items: center;
          padding: 17px 15px;
          .logo {
            display: inline-block;
            width: 46px;
            height: 46px;
            line-height: 46px;
            border-radius: 50%;
            font-size: 17px;
            font-weight: 500;
            color: #fff;
            &.on {
              background-color: #FF6F00;
            }
            &.off {
              background-color: #3388FF;
            }
          }
          .rt {
            margin-left: 15px;
            text-align: left;
            .name {
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-box-orient:vertical;
              -webkit-line-clamp:2;
              margin-bottom: 6px;
              font-size: 17px;
              font-weight: 500;
              line-height: 1.3;
              color: #0A0A0A;
            }
            .tel {
              font-size: 15px;
              color: #666666;
            }
          }
          .set {
            flex: 1;
            align-self: center;
            text-align: right;
            font-size: 13px;
            color: #FF6F00;
            
            .set-item {
              border: 1px solid #F7E1D0;
              padding: 2px 10px;
              border-radius: 33px;
            }
          }
        }
        .sub {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 17px 0px;
          border-top: 1px solid #F8F8F8;
          > div {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 50%;
            font-size: 15px;
            .label {
              color: #0A0A0A;
            }
            .value {
              margin-right: 3px;
              color: #FF6F00;
            }
          }
        }
      }
    }
    .gray {
      filter: grayscale(100%);
      opacity: 0.6;
    }
  }
  .empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 120px;
    img {
      width: 300px;
      margin-bottom: 15px;
    }
    span {
      font-size: 17px;
      color: #666;
    }
  }
}
</style>