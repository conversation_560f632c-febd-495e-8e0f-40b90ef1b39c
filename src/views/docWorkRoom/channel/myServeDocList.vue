<template>
  <div class='myServeDocList'>
    <div class="searchBar">
      <van-search
        v-model="searchVal"
        shape="round"
        :clearable="false"
        placeholder="支持姓名/手机号/机构查询"
        @blur="searchFun('input')"
      />
      <van-dropdown-menu active-color="#F7830D" :close-on-click-outside="false">
        <van-dropdown-item :title-class="item.title != item.label ? 'titleActive' : ''" v-for="item in fixedSearchArr" @close="searchFun('dropdown')" :key="item.name" :title="item.title || item.label" :ref="'dropMenuItem' + item.name">
          <template #title v-if="item.name == 'keywords'">
            <div class="alignCenter">
              <span :class="[ screenTitleActive ? 'titleActive' : '']">筛选</span>
              <van-icon :color="screenTitleActive ? '#F7830D' : ''" name="filter-o" />
            </div>
          </template>
          <div class="customDropMenu">
            <!-- 筛选内 -->
            <template v-if="item.name == 'keywords'" >
              <div class="dropMenuInner">
                <div v-for="otherItem in otherSearchArr" :key="otherItem.name">
                  <!-- 多选 -->
                  <template v-if="['channel_doc'].indexOf(otherItem.name) !== -1">
                    <div class="labelLine">{{ otherItem.label }}</div>
                    <div class="checkList">
                      <div :class="[inner.checked ? 'checked' : '']" v-for="inner in otherItem.options" :key="inner.id" class="checkItem" @click="checkFun(inner,otherItem,otherItem.name,'inside')">
                        {{inner.name}}
                      </div>
                    </div>
                  </template>
                  <!-- 单选 -->
                  <template v-if="['sale_order','channel_type','is_ehosp_package','auth_status','ehosp_status','is_open','channel_hosp'].indexOf(otherItem.name) !== -1">
                    <div class="labelLine">{{ otherItem.label }}</div>
                    <div class="checkList">
                      <div :class="[inner.id === otherItem.selected ? 'checked' : '']" v-for="inner in otherItem.options" :key="inner.id" class="checkItem" @click="radioFun(inner,otherItem,otherItem.name,'inside')">{{inner.name}}</div>
                    </div>
                  </template>
                  <!-- 机构 科室 -->
                  <template v-if="['hosp_id'].indexOf(otherItem.name) !== -1">
                    <div class="labelLine">{{ otherItem.label }}</div>
                    <div class="hospDeptBlock">
                      <div class="hospLine" @click="openHospPop('hosp','inside')">
                        <span class="label">{{selectHospObj.name ||'选择机构'}}</span>
                        <van-icon class="arrowRotate" name="play" color="#878F99" size="12px" />
                      </div>
                      <div class="hospLine deptLine" @click="openHospPop('dept')" v-if="JSON.stringify(selectHospObj) !== '{}'">
                        <span class="label">{{selectDeptObj.name || '选择科室'}}</span>
                        <van-icon class="arrowRotate" name="play" color="#878F99" size="12px" />
                      </div>
                    </div>
                  </template>
                </div>
              </div>
              <div class="btnOuter">
                <div class="cancelBtn" @click="insideResetFun">重置</div>
                <div class="confirmBtn" @click="insideSubmitFun">确定</div>
              </div>
            </template>
            <!-- 筛选外 外层 -->
            <template v-else>
              <!-- 多选 -->
              <template v-if="['channel_doc'].indexOf(item.name) !== -1">
                <div class="checkList" style="margin: 6px 0 0;">
                  <div :class="[inner.checked ? 'checked' : '']" v-for="inner in item.options" :key="inner.id" class="checkItem" @click="checkFun(inner,item,item.name)">{{inner.name}}</div>
                </div>
                <div class="btnOuter">
                  <div class="cancelBtn" @click="outsideResetFun(item.name,item.label)">重置</div>
                  <div class="confirmBtn" @click="outsideSubmitFun(item.name)">确定</div>
                </div>
              </template>
              <!-- 单选 -->
              <template v-if="['sale_order','channel_type','is_ehosp_package','auth_status','ehosp_status','is_open','channel_hosp'].indexOf(item.name) !== -1">
                <div class="radioList">
                  <div class="radioItem" v-for="inner in item.options" :key="inner.id" @click="radioFun(inner,item,item.name)">
                    <span :class="[inner.id === item.selected ? 'checked' : '']">{{inner.name}}</span>
                    <van-icon color="#F7830D" v-show="inner.id === item.selected" name="success" />
                  </div>
                </div>
              </template>
              <!-- 机构 科室 -->
              <template v-if="['hosp_id'].indexOf(item.name) !== -1">
                <div class="hospDeptBlock" style="padding: 20px 0 0;">
                  <div class="hospLine" @click="openHospPop('hosp')">
                    <span class="label">{{selectHospObj.name ||'选择机构'}}</span>
                    <van-icon class="arrowRotate" name="play" color="#878F99" size="12px" />
                  </div>
                  <div class="hospLine deptLine" @click="openHospPop('dept')" v-if="JSON.stringify(selectHospObj) !== '{}'">
                    <span class="label">{{selectDeptObj.name || '选择科室'}}</span>
                    <van-icon class="arrowRotate" name="play" color="#878F99" size="12px" />
                  </div>
                </div>
                <div class="btnOuter">
                  <div class="cancelBtn" @click="outsideResetFun(item.name,item.label)">重置</div>
                  <div class="confirmBtn" @click="outsideSubmitFun(item.name)">确定</div>
                </div>
              </template>
              <template v-if="['bind_created_at'].indexOf(item.name) !== -1">
                <van-datetime-picker
                  :show-toolbar="false"
                  v-model="bindDate"
                  type="year-month"
                  :min-date="minDate"
                  :max-date="maxDate"
                  @change="bindDateChange"
                  swipe-duration="300"
                />
                <div class="btnOuter">
                  <div class="cancelBtn" @click="outsideResetFun(item.name,item.label)">重置</div>
                  <div class="confirmBtn" @click="outsideSubmitFun(item.name)">确定</div>
                </div>
              </template>
            </template>
          </div>
        </van-dropdown-item>
      </van-dropdown-menu>
    </div>
    <div class="list">
      <van-list
        v-model="loading"
        :finished="finished"
        :finished-text="finishedText"
        @load="getDataList('load')"
      >
        <div class="item" v-for="item in docList" :key="item.doc_id" @click="goDetails(item)">
          <!-- <template> -->
            <div class="leftPic" :style="{ 'background': item.doc_name == '未完善' ? '#3388FF' : '#FF6F00'}">{{item.headStr}}</div>
            <div class="rightContent">
              <div class="line between">
                <div class="leftName">
                  <span class="name">{{ item.doc_name }}</span>
                  <span class="tel">{{ item.cell }}</span>
                </div>
                <span class="rightStatus">{{item.channel_doc_type}}</span>
              </div>
              <div class="line">
                <span class="hospital">
                  {{ `${item.hosp_name} ${item.dept_name}`}}
                </span>
              </div>
              <div class="line" v-if="item.channel_doc_name">
                <div class="label" style="min-width: 70px;">客服经理：</div>
                <div class="val">
                  <span>{{ item.channel_doc_name }}</span>
                  <span v-if="item.channel_doc_channel_hosp">({{ item.channel_doc_channel_hosp }})</span>
                </div>
              </div>
              <div class="tagLine">
                <div class="tag sold">
                  <div class="leftIcon"><img class="icon" src="../imgs/servicePackageIcon.png" alt=""></div>
                  <span class="text">{{ `已售${item.ehosp_package_sale_num}单` }}</span>
                </div>
                <div class="tag sale">
                  <div class="leftIcon"><img class="icon" src="../imgs/servicePackageIcon.png" alt=""></div>
                  <span class="text">{{ `在售${item.ehosp_package_num}款` }}</span>
                </div>
              </div>
            </div>
          <!-- </template> -->
          <!-- <template>
            <div class="unKnownLine">
              <div class="leftPic">未</div>
              <div class="unKnownContent">
                <div class="line">
                  <div class="leftName">
                    <span class="name">未完善信息</span>
                    <span class="tel">166****7225</span>
                  </div>
                </div>
              </div>
            </div>
          </template> -->
        </div>
      </van-list>
    </div>
    <van-popup class="hospPopup" v-model="isHospPopShow" round position="bottom">
      <div class="topBlock">
        <div class="titleLine">
          <span class="cancel" @click="hospCancel" >取消</span>
          <span class="title">{{selectHospOrDept == 'hosp' ? '选择机构' : '选择科室'}}</span>
          <span class="confirm" @click="hospConfirm">确认</span>
        </div>
        <van-search :clearable="false" v-if="selectHospOrDept == 'hosp'" class="searchLine" v-model="hospKeywords" placeholder="搜索医院" @blur="getHospList" />
      </div>
      <div class="radioList" style="height: calc(100% - 100px)" v-if="selectHospOrDept == 'hosp'">
        <div class="radioItem" v-for="item in hospList" :key="item.hosp_id" @click="selectHospObj = item">
          <span class="hospName" :class="[item.hosp_id == selectHospObj.hosp_id ? 'checked' : '']">{{item.name}}</span>
          <van-icon color="#F7830D" v-show="item.hosp_id == selectHospObj.hosp_id" name="success" />
        </div>
      </div>
      <div class="radioList" style="height: calc(100% - 50px)" v-else>
        <div class="radioItem" v-for="item in deptList" :key="item.id" @click="selectDeptObj = item">
          <span :class="[item.id == selectDeptObj.id ? 'checked' : '']">{{item.name}}</span>
          <van-icon color="#F7830D" v-show="item.id == selectDeptObj.id" name="success" />
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import moment from 'moment'
import { getSystemType } from '@/utils/utils'
import { getSearchConfig,getStaffServiceManager,getAgentServiceManager,getAgentCompany,getHospList,getDeptList,getMyServiceDocList } from '@/api/docWorkRoom'
export default {
  data() {
    return {
      docList: [],
      searchVal: '',
      hasordersVal: 'all',
      dropMenuArr: [],
      fixedSearchArr: [],
      otherSearchArr: [],
      isHospPopShow: false,
      selectHospOrDept: '',
      hospKeywords: '',
      hospList: [],
      deptList: [],
      selectHospObj: {},
      selectDeptObj: {},
      pageNum: 0,
      loading: false,
      finished: true,  //先设置为true阻止van-list直接加载列表数据，等带筛选条件加载完成后再置为false，请求列表
      finishedText: '',
      dropQuery: {},
      workroomId: '',
      fromChannelListDocId: '',  //从渠道列表跳转过来带的客服经理
      fromHomeBindDate: '',
      minDate: new Date('2023','0'),
      maxDate: new Date(),
      bindDate: new Date(),
      screenTitleActive: false
    }
  },
  methods: {
    bindDateChange(e){
      let year = e.getValues()[0]
      let month = e.getValues()[1]
      this.fixedSearchArr.forEach(item=>{
        if(item.name == 'bind_created_at'){
          item.selected = `${year}-${month}-01`
          item.title = year + '-' + month
        }
      })
    },
    // 筛选内重置
    insideResetFun(){
      this.otherSearchArr.forEach(item=>{
        item.selected = ''
        if(item.name == 'hosp_id'){
          this.selectHospObj = {}
          this.selectDeptObj = {}
        }
        item.options.forEach(inner=>{
          inner.checked = false
        })
      })
    },
    // 筛选内提交
    insideSubmitFun(){
      this.$refs.dropMenuItemkeywords[0].toggle(false)
    },
    // 筛选外重置 客服经理/客服公司/医院-科室/绑定时间
    outsideResetFun(type,label){
      if(type == 'bind_created_at'){
        this.$refs['dropMenuItem'+type][0].toggle(false)
        this.fixedSearchArr.forEach(item=>{
          if(item.name == type){
            this.bindDate = new Date()
            item.selected = ''
            item.title = label
          }
        })
        return
      }
      if(type == 'hosp_id'){
        this.selectHospObj = {}
        this.selectDeptObj = {}
      }else{
        this.fixedSearchArr.forEach(item=>{
          if(item.name == type){
            item.selected = ''
            item.title = label
            if(item.options && item.options.length > 0){
              item.options.forEach(inner=>{
                inner.checked = false
              })
            }
          }
        })
      }
      this.$refs['dropMenuItem'+type][0].toggle(false)
    },
    // 筛选外提交
    outsideSubmitFun(type){
      if(type == 'bind_created_at'){
        this.fixedSearchArr.forEach(item=>{
          if(item.name == type){
            item.title = moment(this.bindDate).format('YYYY-MM')
            item.selected = moment(this.bindDate).format('YYYY-MM-DD')
          }
        })
      }
      this.$refs['dropMenuItem'+type][0].toggle(false)
    },
    openHospPop(type){
      this.selectHospOrDept = type
      this.isHospPopShow = true
      this.hospKeywords = ''
      this.hospList = []
    },
    hospCancel(){
      // this.selectHospOrDept == 'hosp' ? this.selectHospObj = {} : this.selectDeptObj = {}
      // this.closeHospPop()
    },
    hospConfirm(){
      if(this.selectHospOrDept == 'hosp'){
        getDeptList({hosp_id: this.selectHospObj.hosp_id}).then(res=>{
          if(res.code == 200){
            this.deptList = res.data
          }else{
            this.$toast(res.msg)
          }
        })
      }
      this.closeHospPop()
    },
    closeHospPop(){
      this.isHospPopShow = false
    },
    getHospList(){
      getHospList({keyword: this.hospKeywords}).then(res=>{
        if(res.code == 200){
          this.hospList = res.data
          if(res.data.length == 0) this.$toast('未搜索到相关数据')
        }else{
          this.$toast(res.msg)
        }
      })
    },
    radioFun(inner,item,menuId,type){
      item.title = inner.name
      item.selected = inner.id
      if(type == 'inside'){
        // this.handleScreenTitle()
        return
      }
      this.$refs['dropMenuItem'+menuId][0].toggle(false)
    },
    checkFun(inner,item,menuId,type){
      let defaultName = menuId == 'channel_doc' ? '客服经理' : '客服公司'
      inner.checked = !inner.checked
      let selectedArr = item.options.filter(i=>i.checked)
      item.title = selectedArr.map(i=> i.name ).join(',') || defaultName
      item.selected = selectedArr.map(i=> i.id ).join(',')
      // if(type == 'inside'){
      //   this.handleScreenTitle()
      // }
    },
    handleScreenTitle(){
      for(let item of this.otherSearchArr){
        if(item.selected != ''){
          this.screenTitleActive = true 
          return
        }else{
          this.screenTitleActive = false
        }
      }
      this.screenTitleActive = JSON.stringify(this.selectHospObj) == '{}' ? false : true
    },
    // 处理下拉菜单的搜索参数
    dropMenuChange(){
      let tempDropMenu = this.fixedSearchArr.concat(this.otherSearchArr)
      let query = {}
      tempDropMenu.forEach(item=>{
        query[item.name] = item.selected
        if(item.name == 'hosp_id'){
          query['hosp_id'] = this.selectHospObj.hosp_id || ''
        }
        if(item.name == 'dept_id'){
          query['dept_id'] = this.selectDeptObj.id || ''
        }
        if(item.name == 'bind_created_at'){
          query['bind_created_at'] = item.selected ? moment(item.selected).format('YYYY-MM-DD') : ''
        }
      })
      console.log(query)
      return query
    },
    // type: input顶部搜索栏搜索；dropdown下拉菜单搜索
    searchFun(type){
      this.handleScreenTitle()
      let query = this.dropMenuChange()
      if(type == 'dropdown' && JSON.stringify(query) === JSON.stringify(this.dropQuery)){
        return
      }
      this.dropQuery = query
      this.pageNum = 0
      this.docList = []
      this.getDataList('search')
    },
    goDetails(item){
      if(process.env.NODE_ENV === 'production'){
        let param = `/docWorkRoom/myServeDocDetails?docObj=${encodeURIComponent(JSON.stringify(item))}&workroomId=${this.workroomId}&showChangeBtn=${item.is_can_transfer}&changeUrl=/docWorkRoom/myTransferSale?docId=${item.doc_id}`
        if (getSystemType() == 'ios') {
          window.webkit.messageHandlers.breathMessage.postMessage(param)
        }else{
          window.android.breathMessage(param)
        }
      }else{
        this.$router.push({
          path: '/docWorkRoom/myServeDocDetails',
          query: {
            docObj: JSON.stringify(item),
            workroomId: this.workroomId,
            showChangeBtn: item.is_can_transfer,
            changeUrl: `/docWorkRoom/myTransferSale?docId=${item.doc_id}`
          }
        })
      }
    },
    async getSearchConfig(){
      let workroom_id = this.workroomId = this.$route.query.workroom_id
      this.fromChannelListDocId = this.$route.query.channel_doc || ''
      this.fromHomeBindDate = this.$route.query.bind_created_at || ''
      let configRes = await getSearchConfig({workroom_id})
      if(configRes.code == 200){
        let {fixed_search_configs,other_search_configs} = configRes.data
        let tempData = fixed_search_configs.concat(other_search_configs)
        this.fixedSearchArr = this.handleSearchConfig(fixed_search_configs)
        this.otherSearchArr = this.handleSearchConfig(other_search_configs)
        for(let item of tempData){
          if(item.name == 'channel_hosp'){
            await this.getAgentCompany()
          }
          if(item.name == 'channel_doc'){
            await this.getserviceManager(item.url)
          }
        }
        this.dropQuery = this.dropMenuChange()
        this.finished = false
        this.finishedText = '没有更多了'
        this.handleScreenTitle()
      }else{
        this.$toast(configRes.msg)
      }
    },
    handleSearchConfig(arr){
      if(!Array.isArray(arr)){
        this.$toast('筛选条件处理错误')
        return
      }
      arr.forEach(item=>{
        if(item.name == 'bind_created_at'){
          item.options = []
          item.selected = this.fromHomeBindDate ? `${this.fromHomeBindDate}-01` : ''
          item.title = this.fromHomeBindDate || item.label
          return
        }
        item.selected = ''
        item.title = item.label
        // if(item.options.length > 0){
        //   item.selected = item.options[0].id
        //   item.title = item.options[0].name
        // }
      })
      return arr
    },
    async getAgentCompany(){
      let res = await getAgentCompany()
      if(res.code == 200){
        let fixedArr = this.fixedSearchArr.filter(item=>item.name == 'channel_hosp')
        if(fixedArr.length > 0){
          this.handleChannelHosp(this.fixedSearchArr,res.data)
        }else{
          this.handleChannelHosp(this.otherSearchArr,res.data)
        }
      }
    },
    handleChannelHosp(arr,resData){
      arr.forEach(item=>{
        if(item.name == 'channel_hosp'){
          item.title = item.label
          item.selected = ''
          item.options = resData.map(inner=>{
            inner.checked = false
            return inner
          })
        }
      })
    },
    async getserviceManager(api){
      let temp = api == '/api/studio/v2/channel_code/channel_doc/list' ? getStaffServiceManager() : getAgentServiceManager()
      let res = await temp
      if(res.code == 200){
        let fixedArr = this.fixedSearchArr.filter(item=>item.name == 'channel_doc')
        if(fixedArr.length > 0){
          this.handleChannelDoc(this.fixedSearchArr,res.data)
        }else{
          this.handleChannelDoc(this.otherSearchArr,res.data)
        }
      }
    },
    handleChannelDoc(arr,resData){
      arr.forEach(item=>{
        if(item.name == 'channel_doc'){
          item.title = item.label
          item.selected = ''
          item.options = resData.map(inner=>{
            inner.checked = false
            if(inner.name == '') inner.name = inner.cell
            //从渠道列表跳转过来 回填客服经理
            if(this.fromChannelListDocId == inner.id){
              inner.checked = true
              item.title = inner.name
              item.selected = inner.id
            }
            return inner
          })
        }
      })
    },
    // type == 'search', 顶部搜索栏触发
    getDataList(type){
      let obj = {
        workroom_id: this.workroomId,
        page: this.pageNum += 1,
        page_size: 15,
        keyword: this.searchVal,
        ...this.dropQuery
      }
      getMyServiceDocList(obj).then(res=>{
        if(res.code == 200){
          let {items,pager} = res.data
          let tempData = items.map(item=>{
            item.headStr = item.doc_name.slice(0,1)
            return item
          })
          type == 'search' ? this.docList = tempData : this.docList = this.docList.concat(tempData)
          this.loading = false
          if(this.docList.length >= pager.total){
            this.finished = true
          }else{
            this.finished = false
          }
        }else{
          this.$toast(res.msg)
        }
      })
    },
    init(){
      this.getSearchConfig()
    }
  },
  mounted() {
    this.init()
  },
  created() {
    
  }
}
</script>

<style lang='scss'>
.myServeDocList{
  background: white;
  font-family: "PingFang SC";
  .alignCenter{
    display: flex;
    align-items: center;
  }
  .center{
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .searchBar{
    width: 100%;
    position: fixed;
    left: 0;
    top: 0;
  }
  .radioList{
    .radioItem{
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 0;
      font-size: 16px;
      color: #0A0A0A;
      border-bottom: 1px solid #F5F5F5;
      line-height: 24px;
    }
    .checked{
      color: #F7830D;
    }
    .hospName{
      text-align: left;
    }
  }
  .dropMenuInner{
    // height: 340px;
    height: calc(100vh - 300px);
    overflow: auto;
    padding-top: 16px;
  }
  .customDropMenu{
    padding: 0 15px;
    .labelLine{
      font-size: 15px;
      font-weight: 500;
      color: #0A0A0A;
      text-align: left;
      line-height: normal;
    }
    .hospDeptBlock{
      padding: 14px 0 30px 0;
      .hospLine{
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 6px 18px;
        border-radius: 6px;
        background: #F5F5F5;
      }
      .deptLine{
        margin-top: 16px;
      }
      .label{
        font-size: 15px;
        color: #666666;
        line-height: 20px;
      }
      .arrowRotate{
        transform: rotate(90deg);
        margin-left: 12px;
      }
    }
    
    .checkList{
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 30px;
      .checkItem{
        line-height: normal;
        font-size: 15px;
        padding: 6px 24px;
        border-radius: 6px;
        background: #F5F5F5;
        margin: 14px 12px 0 0;
        box-sizing: border-box;
        color: #666666;
        // border: 1px solid transparent;
      }
      .checked{
        color: #F7830D;
        // font-weight: 500;
        // border: 1px solid #FF8E2D;
      }
    }
  }
  .list{
    margin-top: 105px;  //search 54 + 下拉菜单 48
    padding: 0 15px;
    .item{
      // padding: 16px 0 0;
      display: flex;
      .leftPic{
        width: 46px;
        height: 46px;
        background: #FF6F00;
        border-radius: 50%;
        color: white;
        font-size: 17px;
        font-weight: 500;
        margin-top: 16px;
        @extend .center
      }
      .rightContent{
        flex: 1;
        padding: 16px 0 16px 12px;
        border-bottom: 1px solid #F5F5F5;
        .line:first-of-type{
          margin-top: 0;
        }
        .between{
          display: flex;
          justify-content: space-between;
        }
        .line{
          display: flex;
          // align-items: center;
          margin-top: 4px;
          line-height: 20px;
          .rightStatus{
            color: #F7830D;
          }
          .tel,.hospital,.label,.val,.rightStatus{
            font-size: 13px;
          }
          .hospital{
            color: #666666;
            text-align: left;
          }
          .label{
            color: #333333;
            line-height: 20px;
            text-align: left;
          }
          .val{
            color: #666666;
            line-height: 20px;
            text-align: left;
            flex: 1;
          }
        }
        .tagLine{
          display: flex;
          margin-top: 8px;
          .sold{
            border: 1px solid #A7E47A;
            .leftIcon{
              background: #A7E47A;
            }
            .text{
              color: #A7E47A;
            }
          }
          .sale{
            border: 1px solid #ADD2FC;
            .leftIcon{
              background: #ADD2FC;
            }
            .text{
              color: #ADD2FC;
            }
          }
          .tag{
            // width: 68px;
            height: 20px;
            display: flex;
            align-items: center;
            border-radius: 4px;
            font-size: 12px;
            margin-right: 12px;
            .leftIcon{
              width: 20px;
              height: 20px;
              @extend .center;
              border-radius: 0 2px 2px 0;
              .icon{
                width: 12px;
                height: 12px;
              }
            }
            .text{
              padding: 0 2px;
            }
          }
        }
      }
    }
    .name{
      color: #0A0A0A;
      font-size: 17px;
      font-weight: 500;
    }
    .tel{
      color: #999999;
      margin-left: 6px;
      font-size: 13px;
    }
    // .unKnownLine{
    //   width: 100%;
    //   display: flex;
    //   align-items: center;
    //   .leftPic{
    //     margin: 0;
    //     background: #3388FF;
    //   }
    //   .unKnownContent{
    //     flex: 1;
    //     text-align: left;
    //     padding: 26px 0 26px 12px;
    //     border-bottom: 1px solid #F5F5F5;
    //   }
    // }
  }
  .hospPopup{
    height: 90%;
    padding: 16px;
    box-sizing: border-box;
    overflow: hidden;
    .topBlock{
      // height: 100px;
    }
    .radioList{
      margin-top: 16px;
      padding: 0 18px;
      // height: calc(100% - 100px);
      overflow: auto;
    }
    .titleLine{
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 16px;
      height: 30px;
      line-height: normal;
      .cancel{
        // color: #4C4C4C;
        color: transparent;
      }
      .confirm{
        color: #F7830D;
        font-weight: 500;
      }
      .title{
        color: #000;
      }
    }
    .searchLine{
      margin-top: 35px;
    }
    .van-search{
      padding: 0 !important;
    }
  }
  .van-dropdown-menu__item:last-of-type .van-dropdown-menu__title::after{
    display: none;
  }
  .van-dropdown-menu__bar{
    box-shadow: 0px 4px 4px 2px #f9f9f9 !important;
  }
  .van-popup{
    padding-bottom: 0 !important;
  }
  .van-dropdown-item__content{
    overflow: hidden !important;
  }
  .van-dropdown-item__content{
    max-height: 100% !important;
  }
  .titleActive{
    color: #F7830D;
  }
  .btnOuter{
      display: flex;
      justify-content: space-between;
      padding: 20px 0;
      .cancelBtn,.confirmBtn{
        width: 162px;
        height: 44px;
        border-radius: 8px;
        font-size: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        
      }
      .cancelBtn{
        color: #0A0A0A;
        background: #F5F5F5;
      }
      .confirmBtn{
        color: white;
        background: #F7830D;
      }
    }
}
</style>