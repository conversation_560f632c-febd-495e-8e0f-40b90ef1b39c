<template>
  <div class='myServeDocDetails'>
    <div class="topMain">
      <div class="leftPic">{{ dataForm.headStr }}</div>
      <div class="rightContent">
        <span class="name">{{ dataForm.doc_name }}</span>
        <span class="tel">{{ dataForm.cell }}</span>
        <span class="deptInfo">{{ dataForm.hosp_name }} {{ dataForm.dept_name }}</span>
      </div>
    </div>
    <van-cell v-if="dataForm.doc_type == 1" title-class="label" value-class="value" size="large" title="管理关系"
      :center="true" :value="dataForm.channel_doc_type" />
    <van-cell v-if="dataForm.doc_type == 1" title-class="label" value-class="value" size="large" title="客服公司"
      :center="true" :value="dataForm.channel_doc_type_val == 2 ? dataForm.channel_doc_channel_hosp : '--'" />
    <van-cell v-if="[1, 21].indexOf(dataForm.doc_type) !== -1" title-class="label" value-class="value" size="large"
      title="客服经理" :center="true" :value="dataForm.channel_doc_name" />
    <van-cell title-class="label" value-class="value canClick" size="large" title="会员订单数" :center="true"
      :value="`${dataForm.membership_sale_num}单`" is-link @click="goOrderList(9)" />
    <van-cell title-class="label" value-class="value canClick" size="large" title="售卖服务包订单数" :center="true"
      :value="`${dataForm.ehosp_package_sale_num}单`" is-link @click="goOrderList(2)" />
    <van-cell title-class="label" value-class="value canClick" size="large" title="在线诊间订单数" :center="true"
      :value="`${dataForm.onlinehome_sale_num}单`" is-link @click="goOrderList(3)" />
    <van-cell title-class="label" value-class="value canClick" size="large" title="健康生活必备订单数" :center="true"
      :value="`${dataForm.ehosp_product_sale_num}单`" is-link @click="goOrderList(-2)" />
    <van-cell title-class="label" value-class="value canClick" size="large" title="问诊订单数" :center="true"
      :value="`${dataForm.wz_sale_num}单`" is-link @click="goOrderList(1)" />
    <van-cell title-class="label" value-class="value" size="large" title="认证状态" :center="true"
      :value="dataForm.apply_status_name" />
    <van-cell title-class="label" value-class="value" size="large" title="备案状态" :center="true"
      :value="dataForm.ehosp_status_name" />
    <van-cell title-class="label" value-class="value" size="large" title="问诊状态" :center="true"
      :value="dataForm.tw_open == 1 ? '已开启' : '未开启'" />
    <van-cell title-class="label" value-class="value" size="large" title="问诊价格" :center="true"
      :value="dataForm.tw_open == 1 ? dataForm.tw_open_price : '--'" />
    <van-cell title-class="label" value-class="value" size="large" title="在线诊间状态" :center="true"
      :value="dataForm.onlinehome_open_name" />
    <van-cell title-class="label" value-class="value" size="large" title="在线诊间价格" :center="true"
      :value="dataForm.onlinehome_open_price" />
    <van-cell title-class="label" value-class="value" size="large" title="会员状态" :center="true"
      :value="dataForm.membership_open_name" />
    <van-cell title-class="label" value-class="value" size="large" title="会员价格" :center="true"
      :value="dataForm.membership_open_price" />
    <van-cell title-class="label" value-class="value canClick" size="large" title="添加的服务包" :center="true"
      :value="`${dataForm.ehosp_package_num}个`" is-link @click="goServicePackageList" />
    <van-cell title-class="label" value-class="value canClick" size="large" title="科室管理患者情况" :center="true" value="查看"
      is-link @click="goDocWorkRoomPatientsList" />
    <van-cell title-class="label" value-class="value" size="large" title="网上诊所患者数" :center="true"
      :value="`${dataForm.workroom_mate_cnt}人`" />
    <van-cell title-class="label" value-class="value" size="large" title="绑定时间" :center="true"
      :value="dataForm.bind_update_at" />
  </div>
</template>

<script>
import { getSystemType } from '@/utils/utils'
export default {
  data() {
    return {
      dataForm: {},
      workroomId: ''
    }
  },
  methods: {
    init() {
      let docObj = this.$route.query.docObj
      this.dataForm = { ...JSON.parse(decodeURIComponent(docObj)) }
      this.workroomId = this.$route.query.workroomId
      console.log(this.dataForm)
    },
    goServicePackageList() {
      if (this.dataForm.ehosp_package_num == 0) {
        this.$toast('暂无添加的服务包')
        return
      }
      if (process.env.NODE_ENV === 'production') {
        let param = `/docWorkRoom/servicePackageIntroList?doc_id=${this.dataForm.doc_id}&workroom_id=${this.workroomId}`
        if (getSystemType() == 'ios') {
          window.webkit.messageHandlers.breathMessage.postMessage(param)
        } else {
          window.android.breathMessage(param)
        }
      } else {
        this.$router.push({
          path: '/docWorkRoom/servicePackageIntroList',
          query: {
            doc_id: this.dataForm.doc_id,
            workroom_id: this.workroomId
          }
        })
      }
    },
    goDocWorkRoomPatientsList() {
      // if (this.dataForm.workroom_mate_cnt == 0) {
      //   this.$toast('暂无管理患者')
      //   return
      // }
      if (process.env.NODE_ENV === 'production') {
        let param = `/docWorkRoom/myPatients?doc_id=${this.dataForm.doc_id}`
        if (getSystemType() == 'ios') {
          window.webkit.messageHandlers.breathMessage.postMessage(param)
        } else {
          window.android.breathMessage(param)
        }
      } else {
        this.$router.push({
          path: '/docWorkRoom/myPatients',
          query: {
            doc_id: this.dataForm.doc_id
          }
        })
      }
    },
    goOrderList(order_type) {
     
      if (order_type == 2 && this.dataForm.ehosp_package_sale_num == 0) {
        this.$toast('暂无售卖服务包订单')
        return
      }
      if (order_type == 9 && this.dataForm.membership_sale_num == 0) {
        this.$toast('暂无会员订单')
        return
      }
      if (order_type == 3 && this.dataForm.onlinehome_sale_num == 0) {
        this.$toast('暂无在线诊间订单')
        return
      }
      if (order_type == -2 && this.dataForm.ehosp_product_sale_num == 0) {
        this.$toast('暂无健康生活必备订单')
        return
      }
      if (order_type == 1 && this.dataForm.wz_sale_num == 0) {
        this.$toast('暂无问诊订单')
        return
      }
      if (process.env.NODE_ENV === 'production') {
        let param = `/docWorkRoom/channel-order?doc_id=${this.dataForm.doc_id}&workroom_id=${this.workroomId}&order_type=${order_type}`
        if (getSystemType() == 'ios') {
          window.webkit.messageHandlers.breathMessage.postMessage(param)
        } else {
          window.android.breathMessage(param)
        }
      } else {
        this.$router.push({
          path: '/docWorkRoom/channel-order',
          query: {
            doc_id: this.dataForm.doc_id,
            workroom_id: this.workroomId,
            order_type: order_type
          }
        })
      }
    }
  },
  created() {

  },
  mounted() {
    this.init()
  }
}
</script>

<style lang='scss'>
.myServeDocDetails {
  font-family: "PingFang SC";
  background: #F5F5F5;
  width: 100%;
  min-height: 100vh;

  .topMain {
    padding: 15px;
    display: flex;
    border-bottom: 8px solid #F5F5F5;
    background: white;

    .leftPic {
      width: 58px;
      height: 58px;
      border-radius: 8px;
      background: linear-gradient(135deg, #FFB982 0%, #FFDABD 100%);
      color: white;
      font-size: 20px;
      font-weight: 500;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .rightContent {
      margin-left: 16px;
      display: flex;
      flex: 1;
      flex-direction: column;
      align-items: baseline;

      .name {
        color: #0A0A0A;
        font-size: 24px;
        letter-spacing: 0.665px;
        line-height: normal;
      }

      .tel {
        color: #666666;
        font-size: 15px;
        margin-top: 4px;
        line-height: normal;
      }

      .deptInfo {
        text-align: left;
        color: #666666;
        font-size: 14px;
        margin-top: 4px;
        line-height: 20px;
      }
    }
  }

  .van-cell {
    background: white;
  }

  .label {
    color: #0A0A0A;
    font-size: 16px;
  }

  .value {
    color: #999999;
    font-size: 15px;
  }

  .canClick {
    color: #FF6F00;
  }
}
</style>