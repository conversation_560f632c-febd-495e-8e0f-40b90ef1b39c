<template>
  <div class='servicePackageIntroList'>
    <div class="topQrcodeBlock" v-if="isAllCodeShow" @click="getAllCode">
      查看此客户的服务包集合页二维码
      <van-icon name="arrow" color="#DA9F72" />
    </div>
    <div class="list" v-if="dataList == null || dataList.length > 0">
      <div class="item" v-for="item in dataList" :key="item.package_id" @click="goDetails(item)">
        <div class="titleLine">
          <img class="titleImg" :src="item.head_img" alt="">
          <span class="title">{{item.package_name}}</span>
        </div>
        <!-- <template v-if="item.doc_channel_fee.type"> -->
          <div class="textLine" v-if="item.doc_channel_fee.type == 1">
            <div class="text">您直接服务的客户完成履约服务后，您可获得服务支持费 <span class="money">{{item.doc_channel_fee.value}}</span> 元。</div>
            <div class="text">您通过代理商服务的客户完成履约服务后，您可获得服务支持费 <span class="money">{{item.doc_channel_fee.value1}}</span> 元。</div>
          </div>
          <div class="textLine" v-if="item.doc_channel_fee.type == 3">
            <div class="text">您服务的客户完成履约服务后，您可获得服务支持费 <span class="money">{{item.doc_channel_fee.value}}</span> 元。</div>
          </div>
          <div class="textLine" v-if="item.doc_channel_fee.type == 21">
            <div class="text">您公司的每位员工服务的客户完成履约服务后，您公司可获得服务支持费 <span class="money">{{item.doc_channel_fee.value}}</span> 元。</div>
          </div>
        <!-- </template> -->
        <div class="introLine">
          <div class="leftContent">
            <div class="intro" v-for="(inner,index) in item.product_info" :key="index">
              <van-icon class="dot" name="stop" color="#FF813A" />
              <div class="introText">{{inner.rights_desc}}</div>
            </div>
          </div>
          <van-icon name="arrow" color="#999999" />
        </div>
        <div class="qrcodeBtn" v-if="item.is_show_qrcode" @click.stop="getItemCode(item)">查看此服务包二维码</div>
      </div>
    </div>
    <img v-else class="emptyPic" src="../imgs/empty-content.png" alt="">
    <van-dialog v-model="isQrcodePopShow" title="" 
      show-cancel-button 
      confirm-button-color="#F7830D" 
      confirm-button-text="保存图片" 
      cancel-button-color="#333333" 
      close-on-click-overlay
      @confirm="handleDownload">
      <div class="qrcodeInner" id="qrcodeInner">
        <div class="innerTitle">{{ dialogTitle }}</div>
        <img class="qrcodeImg" crossorigin="anonymous" :src="`${qrcodeUrl}?t=${ts}`" alt="">
      </div>
    </van-dialog>
  </div>
</template>

<script>
import { getPackageIntroList,packageIntroListAllQrcodeShow,packageIntroListAllCode,packageIntroListItemCode } from '@/api/docWorkRoom'
const UA = navigator.userAgent;
const isAndroid = UA.indexOf("Android") > -1 || UA.indexOf("Adr") > -1; // android终端
const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); // ios终端
import html2canvas from "html2canvas"
export default {
  data() {
    return {
      dataList: null,
      isAllCodeShow: false,
      docId: '',
      isQrcodePopShow: false,
      dialogTitle: '',
      qrcodeUrl: '',
      ts: new Date().getTime(),
    }
  },
  methods: {
    init(){
      let docId = this.docId = this.$route.query.doc_id
      let workroomId = this.$route.query.workroom_id || ''
      this.getPackageList(docId,workroomId)
      this.allCodeShowFun(docId)
    },
    allCodeShowFun(docId){
      packageIntroListAllQrcodeShow({package_doc_id: docId}).then(res=>{
        if(res.code == 200){
          this.isAllCodeShow = res.data.is_show
        }
      })
    },
    getAllCode(){
      packageIntroListAllCode({package_doc_id: this.docId}).then(res=>{
        if(res.code == 200){
          let {doc_name,qrcode_url} = res.data
          this.dialogTitle = doc_name
          this.qrcodeUrl = qrcode_url
          this.isQrcodePopShow = true
        }else{
          this.$toast(res.msg || '请求错误')
        }
      })
    },
    getItemCode(item){
      let obj = {
        package_doc_id: this.docId,
        package_id: item.package_id
      }
      packageIntroListItemCode(obj).then(res=>{
        if(res.code == 200){
          let {package_name,qrcode_url} = res.data
          this.dialogTitle = package_name
          this.qrcodeUrl = qrcode_url
          this.isQrcodePopShow = true
        }else{
          this.$toast(res.msg || '请求错误')
        }
      })
    },
    handleDownload() {
      const that = this
      const element = document.getElementById('qrcodeInner')
      this.ts = +new Date()
    
      // 使用 html2canvas 截取指定区域内容并生成图片
      html2canvas(element, {
        scale: 3,
        allowTaint: true,
        useCORS: true,
        backgroundColor: '#FFFFFF',
        // ignoreElements: [document.querySelector('.footer')],
        onclone: () => {
          that.ts = +new Date()
        }
      }).then((canvas) => {
          // 将 Canvas 转换为图片并保存到手机本地
          // let image = canvas.toDataURL('image/png').replace('image/png', 'image/octet-stream')

          const image = canvas.toDataURL('image/png')


          const base64 = image.slice(22)
          console.log(base64)
          if (isIOS) {
            window.webkit.messageHandlers.saveImageToAlbum.postMessage(base64)
          }
          if (isAndroid) {
            window.android.saveImageToAlbum(base64)
          }
      })
    },
    getPackageList(docId,workroomId){
      let obj = {
        service_doc_id: docId || 0,
        workroom_id: workroomId
      }
      getPackageIntroList(obj).then(res=>{
        if(res.code == 200){
          res.data.forEach(item=>{
            let priceObj = {
              rights_desc: `市场价¥${item.market_price}，现价¥${item.price}`
            }
            item.product_info.push(priceObj)
          })
          this.dataList = res.data
        }else{
          this.$toast(res.msg)
          this.dataList = []
        }
      })
    },
    goDetails(item){
      if (isAndroid) {
        window.android.goServicePackageDetails(item.package_id,item.package_name)
      }
      if (isIOS) {
        window.webkit.messageHandlers.goServicePackageDetails.postMessage(JSON.stringify({id: item.package_id,name: item.package_name}))
      }
    }
  },
  mounted() {
    this.init()
  },
  created() {

  },
}
</script>

<style lang='scss'>
.servicePackageIntroList{
  width: 100%;
  min-height: 100vh;
  background: #F5F5F5;
  // padding: 20px 15px 20px;
  box-sizing: border-box;
  .topQrcodeBlock{
    height: 44px;
    @extend .center;
    color: #CE8853;
    background: #FFE4CF;
  }
  .center{
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .list{
    padding: 20px 15px 20px;
    .item{
      border-radius: 8px;
      padding: 15px;
      background: white;
      margin-bottom: 15px;
      .titleLine{
        height: 76px;
        display: flex;
        border-bottom: 1px solid #F5F5F5;
        @extend .center;
        justify-content: flex-start;
        .title{
          font-size: 17px;
          font-weight: 500;
          color: #0A0A0A;
          margin-left: 15px;
          text-align: left;
          line-height: 20px;
        }
        .titleImg{
          width: 46px;
          height: 46px;
          border-radius: 4px;
        }
      }
      .textLine{
        margin-top: 8px;
        border-bottom: 1px solid #F5F5F5;
        .text{
          margin-bottom: 14px;
          line-height: 20px;
          font-size: 15px;
          color: #666666;
          text-align: left;
        }
        .money{
          font-weight: 500;
          color: #F7830D;
        }
      }
      .introLine{
        display: flex;
        padding: 14px 0;
        border-bottom: 1px solid #F5F5F5;
        align-items: center;
        justify-content: space-between;
        .leftContent{
          display: flex;
          flex-direction: column;
          color: #666666;
          font-size: 15px;
          text-align: left;
          .intro:first-of-type{
             margin-top: 0;
          }
          .intro{
            margin-top: 12px;
            display: flex;
            .dot{
              border-radius: 1px;
            }
            .introText{
              line-height: 20px;
            }
          }
        }
      }
      .qrcodeBtn{
        color: #F7830D;
        font-size: 17px;
        font-weight: 500;
        padding-top: 15px;
      }
    }
  }
  .emptyPic{
    width: 60%;
    margin-top: 70%;
  }
  .qrcodeInner{
    padding: 30px 16px 25px;
    .innerTitle{
      font-size: 18px;
      font-weight: bold;
      line-height: 22px;
    }
    .qrcodeImg{
      width: 192px;
      height: 192px;
      margin-top: 20px;
    }
  }
}
</style>
