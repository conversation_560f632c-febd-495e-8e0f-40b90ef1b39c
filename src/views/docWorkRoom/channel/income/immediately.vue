<template>
  <div class="immediately-box">
    <revenueTip :tipcontent="tipcontent" v-if="tipcontent" />

    <div class="immediately-invoice">
      <div class="invoice-box" id="invoice">
        <div class="name">{{invoice_title}}</div>
        <div>{{invoice_tin}}</div>
      </div>
      <div class="invoice-copy" data-clipboard-target="#invoice" @click="copyInvoice">复制</div>
    </div>

    <div class="immediately-main">
      <van-field
        label="公司名称"
        :border="false"
        class="input"
        readonly
        rows="1"
        autosize
        v-model.trim="corporateName"
        type="textarea"
        input-align="right"
        @change="inputChange"
      />
      <div @click="setBankCard">
        <van-field
          label="银行"
          :border="false"
          class="input"
          v-model.trim="bankName"
          type="textarea"
          readonly
          rows="1"
          autosize
          input-align="right"
          placeholder="请选择"
        />
      </div>
      <van-field
        label="开户行"
        :border="false"
        class="input"
        v-model.trim="openingBank"
        type="textarea"
        rows="1"
        autosize
        input-align="right"
        placeholder="请填写"
        @change="inputChange"
      />
      <van-field
        label="银行卡号"
        :border="false"
        class="input"
        v-model.trim="cardNumber"
        type="number"
        input-align="right"
        placeholder="请填写"
        @change="inputChange"
      />
      <div class="immediately-main-file-box">
        <div class="immediately-main-file">
          <div class="main-file-label">发票（开票金额：¥ {{footerPrice}}）</div>
          <div class="main-file-btn" v-if="fileListParams.length < 10">上传</div>
        </div>
        <div class="main-file-tip">仅支持pdf电子发票</div>

        <div class="main-file-list">
          <div v-for="(item, index) in fileListParams" :key="index" class="main-file-list-item" @click.stop="handleReport(item.url)">
            <div class="main-file-list-name">{{item.name}}</div>
            <img :src="companyrevenueClose" alt="" class="companyrevenueClose" @click.stop="deleteFile(item, index)">
          </div>
        </div>

        <van-uploader
          class="main-file-uploader"
          v-model="fileList"
          upload-text=""
          accept=".pdf"
          :max-count="100"
          :preview-image="false"
          :after-read="afterRead"
          :before-read="beforeRead"
        />

      </div>

    </div>

    <van-button
      class="immediately-box-btn"
      type="primary"
      :disabled="!corporateName || !bankName || !openingBank || !cardNumber || !fileListParams.length"
      @click.stop="buttonSubmit"
    >提交</van-button>

  </div>
</template>

<script>
import Clipboard from 'clipboard'
import revenueTip from './components/revenueTip.vue'
import {Toast} from "vant";
import {getChannelFileData,getChannelAgentWithdrawSave,getChannelAgenConfig} from "@/api/docWorkRoom";
import companyrevenueClose from '@/assets/images/channel/companyrevenue-close.png'
import {getSystemType, loadNativechannelRefreshPop, loadNativePage} from "@/utils/utils";
export default {
  name: "immediately",
  components: {
    revenueTip
  },
  data() {
    return {
      companyrevenueClose,
      tipcontent: '',
      invoice_title: '',
      invoice_tin: '',
      corporateName: '', // 公司名称
      bankName: '', // 银行
      openingBank: '', // 开户行
      cardNumber: '', // 银行卡号
      workroom_id: '',
      footerPrice: '',
      fileList: [],
      fileListParams: [],
      dataInfo: {}
    }
  },
  mounted() {
    this.workroom_id = this.$route.query.workroom_id
    this.footerPrice = this.$route.query.footerPrice
    this.getChannelAgenConfig()
    window.getImmediatelyBankName = this.getImmediatelyBankName
  },
  methods: {
    getImmediatelyBankName(bankName) {
      if (bankName) {
        this.bankName = bankName
        this.storageFun()
      }
    },
    async getChannelAgenConfig() {
      this.backfilleStorageDatForm()
      const res = await getChannelAgenConfig({})
      // const res ={
      //   "code": 200,
      //   "msg": "",
      //   "data": {
      //     "tips": "请上传增值税专用发票...",
      //     "invoice_title": "上海智众医疗科技有限公司",
      //     "invoice_tin": "纳税人识别号",
      //     "bind_card": {
      //         "name": "公司名称",
      //         "bank_name": "银行id",
      //         "bank_card_no": "122",
      //         "bank_sub_name": "支行名称"
      //     }
      //   }
      // }
      if (res.code === 200) {
        this.tipcontent = res.data.tips
        this.invoice_title = res.data.invoice_title
        this.invoice_tin = res.data.invoice_tin
        this.corporateName = res.data.bind_card.name
        if (!localStorage.getItem('immediatelyFormData')) {
          this.bankName = res.data.bind_card.bank_name
          this.cardNumber = res.data.bind_card.bank_card_no
          this.openingBank = res.data.bind_card.bank_sub_name
          this.storageFun()
        }
      } else {
        this.$toast(res.msg)
      }
    },
    backfilleStorageDatForm() {
      let storageData = null
      if(localStorage.getItem('immediatelyFormData')){
        storageData = JSON.parse(localStorage.getItem('immediatelyFormData'))
        // this.corporateName = storageData.corporateName
        this.bankName = storageData.bankName
        this.openingBank = storageData.openingBank
        this.cardNumber = storageData.cardNumber
        this.fileListParams = storageData.fileListParams
      }
    },
    inputChange() {
      this.storageFun()
    },
    copyInvoice() {
      let name = `${this.invoice_title}\n${this.invoice_tin}`
      let clipboard = new Clipboard('.invoice-copy', {
        text: function (trigger) {
          return name;
        }
      })
      clipboard.on('success', function (e) {
        if (e) {
          Toast('复制成功');
          e.clearSelection()
        } else {
          Toast('复制失败,请重试');
        }
      })
    },
    handleReport (report_url) {
      if (getSystemType() === 'android') {
        window.android.showFilePdf(report_url);
      }
      if (getSystemType() === 'ios') {
        window.webkit.messageHandlers.showFilePdf.postMessage(report_url);
      }
    },
    beforeRead(file) {
      const isPdf = ['application/pdf'].indexOf(file.type) !== -1;
      if (!isPdf) {
        Toast('仅支持pdf格式!');
        return false
      }
      return true
    },
    deleteFile(file, index) {
      this.fileListParams.splice(index, 1);
      this.storageFun();
      return true
    },
    afterRead(file, detail) {
      file.status = 'uploading';
      file.message = '上传中...';
      const formData = new FormData()
      formData.append('file', file.file)
      // formData.append('name', this.fileList.length)
      getChannelFileData(formData).then((res) => {
        file.status = 'done';
        file.message = '';
        this.fileListParams.push({
          path: res.data.path,
          url: res.data.url,
          name: file.file.name,
        });
        this.storageFun();
      }).catch(v => {
        console.log(v)
        file.status = 'failed';
        file.message = '上传失败';
      })
    },
    storageFun(){
      let {
        corporateName,
        bankName,
        openingBank,
        cardNumber,
        fileListParams,
      } = this
      localStorage.setItem('immediatelyFormData', JSON.stringify({
        corporateName,
        bankName,
        openingBank,
        cardNumber,
        fileListParams,
      }))
    },
    setBankCard() {
      // this.storageFun()
      let url = `/docWorkRoom/bankCard?workroom_id=${this.$route.query.workroom_id}`
      // if (window.location.hostname === 'localhost') {
      //   this.$router.push({
      //     path: '/docWorkRoom/bankCard',
      //     query: {}
      //   })
      // }
      loadNativePage(url)
    },
    async buttonSubmit() {
      const res = await getChannelAgentWithdrawSave({
        name: this.corporateName,
        bank_card_no: this.cardNumber,
        bank_name: this.bankName,
        bank_sub_name: this.openingBank,
        invoice_info: this.fileListParams,
      })
      if (res.code === 200) {
        this.$toast('提现申请已提交，请耐心等待')
        localStorage.removeItem('immediatelyFormData')
        // window.history.go(-1)
        setTimeout(() => {
          loadNativechannelRefreshPop()
        }, 1500)
      } else {
        this.$toast(res.msg)
      }
    }
  }
}
</script>

<style scoped lang="scss">
.immediately-box{
  height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
  background-color: #f5f5f5;
  .immediately-invoice{
    width: 345px;
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #fff;
    margin-bottom: 8px;
    .invoice-box{
      color: #333;
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
      .name{
        margin-bottom: 10px;
      }
      div{
        text-align: left;
        word-break: break-all;
      }
    }
    .invoice-copy{
      white-space: nowrap;
      margin-left: 10px;
      text-align: right;
      color: #F7830D;
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
      text-decoration-line: underline;
    }
  }
  .immediately-main{
    ::v-deep .input{
      padding: 17px 15px;
      border-bottom: 1px solid #F3F3F3;
      .van-field__label{
        color: #333 !important;
        font-family: "PingFang SC";
        font-size: 16px;
        font-style: normal;
        font-weight: 400 !important;
        line-height: normal;
        min-height: initial;
        display: flex;
        align-items: center;
      }
      .van-field__control{
        color: #333;
        text-align: right;
        font-family: "PingFang SC";
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        // line-height: normal !important;
        // height: initial !important;
        min-height: initial;
        display: flex;
        align-items: center;
      }
      input::placeholder{
        // color: #999 !important;
      }
      .van-field__control::-webkit-scrollbar {
        display: none;
      }
      .van-field__control {
        -ms-overflow-style: none;  /* IE和Edge */
        scrollbar-width: none;  /* Firefox */
      }
    }
    .immediately-main-file-box{
      width: 345px;
      padding: 20px 15px;
      background: #fff;
      position: relative;
      .immediately-main-file{
        width: 345px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .main-file-label{
          color: #333;
          font-family: "PingFang SC";
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          line-height: normal;
        }
        .main-file-btn{
          width: 62px;
          padding: 4px 0;
          line-height: normal;
          border-radius: 6px;
          background: #F7830D;
          color: #FFF;
          text-align: center;
          font-family: "PingFang SC";
          font-size: 14px;
          font-style: normal;
          font-weight: 500;
        }
      }
      .main-file-tip{
        text-align: left;
        color: #999;
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
      }
      .main-file-list{
        width: 345px;

        .main-file-list-item{
          width: 317px;
          height: 20px;
          overflow: hidden;
          padding: 12px 16px 12px 12px;
          border-radius: 4px;
          margin-top: 12px;
          background: #F8F8F8;
          display: flex;
          justify-content: space-between;
          align-items: center;
          .main-file-list-name{
            text-align: left;
            flex: 1;
            color: #333;
            font-family: "PingFang SC";
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          .companyrevenueClose{
            width: 12px;
            height: 12px;
            margin-left: 10px;
            flex-shrink: 0;
          }
        }
      }
      .main-file-uploader{
        position: absolute;
        top: 18px;
        right: 5px;
        z-index: 1;
        width: 74px;
        opacity: 0;
        ::v-deep .van-uploader__upload{
          height: 30px !important;
        }
      }
    }

  }

  .immediately-box-btn{
    width: 315px;
    height: 44px;
    line-height: 44px;
    border-radius: 8px;
    background: #F7830D;
    border: 1px solid #F7830D;
    color: #FFF;
    font-family: "PingFang SC";
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    margin: 53px auto 30px;
    //position: fixed;
    //left: 30px;
    //bottom: 50px;
    //z-index: 11;
  }
}
</style>
