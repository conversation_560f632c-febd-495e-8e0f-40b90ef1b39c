<template>
  <div class="companyrevenue">
    <van-tabs v-model="peroid_type" sticky>

      <revenueItem
        v-for="item in incomeSearchData"
        :key="item.peroid_type"
        :title="item.period_label"
        :name="item.peroid_type"
        :is_show_join_button="item.is_show_join_button"
        :is_can_withdraw="item.is_can_withdraw"
        :peroid_type="peroid_type"
        :revenueType="revenueType"
        :tipcontent="item.tip_message"
        :workroom_id="workroom_id"
      />

    </van-tabs>
  </div>
</template>

<script>
import revenueItem from './components/revenueItem.vue'
import {
  getChannelIncomeSearch,
} from '@/api/docWorkRoom'

export default {
  components: {
    revenueItem
  },
  data() {
    return {
      workroom_id: '',
      peroid_type: '',
      revenueType: '1', // 1公司收入 2我的收入-内部个人 3我的收入-外部个人
      tipcontent: '',
      toastLoading: null,
      incomeSearchData: [],
    }
  },
  created() {
    this.revenueType = {
      '/docWorkRoom/companyrevenue': '1', // 公司收入
      '/docWorkRoom/innerrevenue': '2', // 我的收入-内部个人
      '/docWorkRoom/personalrevenue': '3', // 我的收入-外部个人
    }[this.$route.path]

    this.workroom_id = this.$route.query.workroom_id
  },
  mounted() {
    this.getChannelIncomeSearch()
  },
  methods: {
    async getChannelIncomeSearch() {
      // const res = {
      //   "code": 200,
      //   "msg": "",
      //   "time": 1722333649,
      //   "runtime": "838ms",
      //   "req_id": "89f73ee2d5e04557978f195cef82b5a3",
      //   "data": [
      //     {
      //       "peroid_type": 1,
      //       "period_label": "可提现",
      //       "tip_message": "提示：每月服务费将随次月工资发放",
      //       "is_show_join_button": false,
      //       "is_can_withdraw": false, // 是否品示加入零工平台按钮，只在外部个人可提现列表中有此字段，bool值
      //     },
      //     {
      //       "peroid_type": 2,
      //       "period_label": "等待中",
      //       "tip_message": "提示：以下显示金额根据已产生的服务包订单预估，若对应订单退款，则对应服务费将扣减。具体以实际结算金额为准。"
      //     },
      //     {
      //       "peroid_type": 3,
      //       "period_label": "已提现",
      //       "tip_message": ""
      //     }
      //   ]
      // }
      const res = await getChannelIncomeSearch({ workroom_id: this.workroom_id })
      if (res.code === 200) {
        this.incomeSearchData = res.data
        this.peroid_type = this.incomeSearchData[0].peroid_type
      } else {
        this.$toast(res.msg)
      }
    },
  },
}
</script>

<style scoped lang="scss">
.companyrevenue {
  min-height: 100vh;
  background-color: #f5f5f5;
  ::v-deep .van-tabs {
    >div{
      .van-sticky {
        // padding: 2px 0 6px;
        background-color: #fff;
        > div {
          // width: 60vw;
          .van-tabs__line {
            width: 26px;
            background-color: #FF6F00 !important;
          }
          .van-tab {
            line-height: 24px;
            .van-tab__text {
              margin-top: 3px;
              font-size: 17px;
              color: #999;
              font-weight: 400;
            }
            &.van-tab--active {
              .van-tab__text {
                font-weight: 500;
                color: #333;
              }
            }
          }
        }
      }
    }
  }
  .companyrevenue-withdrawable{

  }


}
</style>
