<template>
  <div class="companyrevenue-tip">
    <img :src="companyrevenueTip" alt="" class="companyrevenue-tip-img">
    <div class="companyrevenue-tip-content" v-html="tipcontent.replace(/\n/g, '<br>')"></div>
  </div>
</template>

<script>
import companyrevenueTip from '@/assets/images/channel/companyrevenue-tip.png'

export default {
  props: {
    tipcontent: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      companyrevenueTip,
    }
  },
}
</script>

<style scoped lang="scss">
.companyrevenue-tip {
  width: 345px;
  padding: 12px 15px;
  background: #FDF8F1;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  .companyrevenue-tip-img{
    width: 16px;
    height: 16px;
    margin-right: 6px;
  }
  .companyrevenue-tip-content{
    color: #666;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    text-align: left;
    word-break: break-all;
  }
}
</style>
