<template>
  <div class="companyrevenue-tap">
    <van-tab :title="title" :name="name">
      <revenueTip :tipcontent="tipcontent" v-if="tipcontent" />

<!--      <van-pull-refresh-->
<!--        v-if="peroid_type == name"-->
<!--        v-model="refreshing"-->
<!--        @refresh="handleRefresh"-->
<!--        :class="{-->
<!--          'van-pull-refresh-pb': isShowFooter && footerPrice*1 > 0-->
<!--        }"-->
<!--      >-->
        <van-list
          v-model="loading"
          :finished="finished"
          :finished-text="dataList.length ? '没有更多了' : ''"
          @load="getListData"
          offset="300"
        >
          <div class="companyrevenue-main" v-if="dataList.length">
            <div class="companyrevenue-main-item" v-for="item in dataList" :key="item.id">
              <div class="companyrevenue-item-date">{{item.label}}</div>
              <div class="companyrevenue-item-conent">
                <div class="companyrevenue-item-conent-price-box">
                  <span class="companyrevenue-item-conent-price"><span class="unit">{{item.unit}}</span>{{item.fee}}</span>
                  <span class="companyrevenue-item-conent-price-tip" v-if="item.fee_tip">{{item.fee_tip}}</span>
                </div>
                <div class="companyrevenue-item-conent-btn-box">
                  <div class="companyrevenue-item-conent-btn-staff" @click="viewByEmployeeClick(item)" v-if="['1'].includes(revenueType.toString())">
                    <span>按员工查看</span>
                    <van-icon name="arrow" />
                  </div>
                  <div class="companyrevenue-item-conent-btn-detail" @click="viewDetailsClick(item)">
                    <span>查看明细</span>
                    <van-icon name="arrow" />
                  </div>
                </div>
                <div class="companyrevenue-item-conent-subscript-status" v-if="item.subscript_status">{{item.subscript_status}}</div>
              </div>
            </div>
          </div>

          <div class="empty" v-if="!refreshing && !dataList.length && isEmpty !== null">
            <img :src="emptyImg" alt="empty">
            <span>暂无数据</span>
          </div>
        </van-list>
<!--      </van-pull-refresh>-->
    </van-tab>

    <div class="companyrevenue-tap-footer" v-if="isShowFooter && footerPrice*1 > 0">
      <div class="allprice-box">
        <span class="label">{{footerLabel}}</span>
        <span class="unit">¥</span>
        <span class="allprice">{{footerPrice}}</span>
      </div>
      <div class="revenue-submit" @click="revenueSubmit" v-if="isRevenueSubmit">{{revenueSubmitTitle}}</div>
    </div>
  </div>
</template>

<script>
import revenueTip from './revenueTip.vue'
import emptyImg from '@/assets/images/channel/empty.png'
import companyrevenueJtimg from '@/assets/images/channel/companyrevenue-jt.png'
import {getSystemType, loadNativePage} from "@/utils/utils";
import Moment from 'moment'
import {
  getChannelIncomeListData,
  getChannelIncomeSearch,
  getChannelInnerIncomeListData,
  getChannelPersonalIncomeListData
} from '@/api/docWorkRoom'

export default {
  components: {
    revenueTip
  },
  props: {
    workroom_id: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    name: {
      type: String,
      default: ''
    },
    peroid_type: {
      type: String,
      default: ''
    },
    tipcontent: {
      type: String,
      default: ''
    },
    revenueType: { // 1公司收入 2我的收入-内部个人 3我的收入-外部个人
      type: String,
      default: ''
    },
    is_show_join_button: {
      type: Boolean,
      default: true
    },
    is_can_withdraw: {
      type: Boolean,
      default: true
    },
  },
  data() {
    return {
      emptyImg,
      companyrevenueJtimg,
      page: 1,
      page_size: 10,
      total: 0,
      refreshing: false,
      loading: false,
      finished: false,
      dataList: [],
      wallet_total: '', // 已提现
      wait_total: '', // 待提现
      isHttp: {},
      isEmpty: null
    }
  },
  watch: {
    peroid_type(val, oldval) {
      console.log(val)
      console.log(this.isHttp[val])
      if (val === this.name && oldval !== val && this.isHttp[val]) {
        this.page = 1
        this.page_size = 10
        this.dataList = []
        this.getListData()
      }
    }
  },
  computed: {
    isShowFooter: function () {
      if (['1', '3'].includes(this.revenueType.toString())) {
        return ['1', '3'].includes(this.peroid_type.toString())
      }
      if (['2'].includes(this.revenueType.toString())) {
        return ['3'].includes(this.peroid_type.toString())
      }
      return false
    },
    footerLabel: function () {
      if (['1', '3'].includes(this.revenueType.toString())) {
        return {
          '1': '总计：',
          '3': '累计提现：',
        }[this.peroid_type.toString()]
      }
      if (['2'].includes(this.revenueType.toString())) {
        return {
          '3': '累计提现：',
        }[this.peroid_type.toString()]
      }
      return ''
    },
    footerPrice: function () {
      if (['1', '2', '3'].includes(this.revenueType.toString())) {
        return {
          '1': this.wait_total,
          '3': this.wallet_total,
        }[this.peroid_type.toString()]
      }
      return 0
    },
    isRevenueSubmit: function () {
      if (['1'].includes(this.revenueType.toString())) {
        return ['1'].includes(this.peroid_type.toString())
      }
      if (['3'].includes(this.revenueType.toString())) {
        return ['1'].includes(this.peroid_type.toString()) && this.is_show_join_button
      }
      return 0
    },
    revenueSubmitTitle: function () {
      return {
        '1': '立即提现',
        '3': '立即提现',
      }[this.revenueType.toString()]
    },
  },
  mounted() {

  },
  methods: {
    handleRefresh () {
      if (!this.peroid_type || this.peroid_type != this.name) return
      this.finished = false
      this.loading = false
      this.page = 1
      this.page_size = 10
      this.dataList = []
      // this.getListData()
    },
    async getListData() {
      if (!this.peroid_type || this.peroid_type != this.name) return
      this.isEmpty = null
      const api = {
        '1': getChannelIncomeListData, // 公司收入
        '2': getChannelInnerIncomeListData, // 我的收入-内部个人
        '3': getChannelPersonalIncomeListData, // 我的收入-外部个人
      }[this.revenueType]
      if (!this.isHttp[this.peroid_type]) {
        this.isHttp[this.peroid_type] = true
        this.page = 1
        this.page_size = 10
        this.dataList = []
      }
      const res = await api({
        workroom_id: this.workroom_id,
        page: this.page,
        peroid_type: this.peroid_type
      })
      // const res = {
      //   "code": 200,
      //   "msg": "",
      //   "time": 1722336057,
      //   "runtime": "1467ms",
      //   "req_id": "980e150d4af14450b0416d68fef1cfe0",
      //   "data": {
      //     wait_total: 10,
      //     wallet_total: 20,
      //     "items": [
      //       {
      //         "fee": `${804.00*this.peroid_type}`,
      //         "label": "2024年 07月",
      //         "order_ids": "2344,2498,2502",
      //         "order_fee_withdrawal": "2024-07",
      //         "unit": "￥",
      //         "fee_tip": "（预估）",
      //         "subscript_status": "提现中"
      //       },
      //       {
      //         "fee": "80.00",
      //         "label": "2024年 08月",
      //         "order_ids": "2518,2517",
      //         "order_fee_withdrawal": "2024-08",
      //         "unit": "￥",
      //         "fee_tip": "（预估）",
      //         "subscript_status": "提现中"
      //       },
      //       {
      //         "fee": "600.00",
      //         "label": "2024年 10月",
      //         "order_ids": "2497,2503,2505",
      //         "order_fee_withdrawal": "2024-10",
      //         "unit": "￥",
      //         "fee_tip": "（预估）",
      //         "subscript_status": ""
      //       }
      //     ],
      //     "pager": {
      //       "current_page": 1,
      //       "last_page": 1,
      //       "total": 3
      //     }
      //   }
      // }
      if (res.code === 200) {
        this.dataList = [...this.dataList, ...res.data.items]
        this.page += 1;
        this.finished = false;
        if (res.data.pager.current_page == res.data.pager.last_page) {
          this.finished = true;
        }
        this.wallet_total = res.data.wallet_total // 已提现
        this.wait_total = res.data.wait_total // 待提现
      } else {
        this.finished = true;
        this.$toast(res.msg)
      }
      this.isEmpty = true
      this.refreshing = false;
      this.loading = false;
    },
    viewDetailsClick(item) {
      // if (window.location.hostname === 'localhost') {
      //   this.$router.push({
      //     path: '/docWorkRoom/channel-order',
      //     query: {
      //       workroom_id: this.workroom_id,
      //       order_ids: item.order_ids,
      //       order_fee_withdrawal: item.order_fee_withdrawal,
      //     }
      //   })
      //   return
      // }
      loadNativePage(`/docWorkRoom/channel-order?workroom_id=${this.workroom_id}&order_ids=${item.order_ids}&order_fee_withdrawal=${item.order_fee_withdrawal}`)
    },
    viewByEmployeeClick(item) { // 按员工查看
      // if (window.location.hostname === 'localhost') {
      //   this.$router.push({
      //     path: '/docWorkRoom/employee-view',
      //     query: {
      //       workroom_id: this.workroom_id,
      //       order_ids: item.order_ids,
      //       order_fee_withdrawal: item.order_fee_withdrawal,
      //     }
      //   })
      //   return
      // }
      loadNativePage(`/docWorkRoom/employee-view?workroom_id=${this.workroom_id}&order_ids=${item.order_ids}&order_fee_withdrawal=${item.order_fee_withdrawal}`)
    },
    revenueSubmit() {
      let url = ''
      if (this.revenueType == '1') { // 公司收入-申请提现
        url = `/docWorkRoom/immediately?workroom_id=${this.workroom_id}&footerPrice=${this.footerPrice}&invite_reload=3`
        // if (window.location.hostname === 'localhost') {
        //   url = {
        //     path: '/docWorkRoom/immediately',
        //     query: {
        //       workroom_id: this.workroom_id,
        //       footerPrice: this.footerPrice,
        //     }
        //   }
        //   this.$router.push(url)
        //   return
        // }
        loadNativePage(url)
      }
      if (this.revenueType == '3') { // 我的收入-外部个人-申请提现
        // url = `/docWorkRoom/oddjobplatform?workroom_id=${this.workroom_id}`
        // url = {
        //   path: '/docWorkRoom/oddjobplatform',
        //   query: {
        //     workroom_id: this.workroom_id,
        //   }
        // }

        if (getSystemType() === 'ios') {
          window.webkit.messageHandlers.companyrevenueBreathMessage.postMessage('')
        } else if (getSystemType() === 'android') {
          window.android.companyrevenueBreathMessage()
        }
      }

    },
  }
}
</script>

<style scoped lang="scss">
.companyrevenue-tap {
  width: 100vw;
  .van-list{
    width: 100vw;
  }
  // min-height: calc(100vh - 44px);
  // background: #f0f0f0;

  .van-pull-refresh-pb{
    padding-bottom: 60px;
  }

  .companyrevenue-main{
    height: 100%;
    padding: 0 15px 10px;
    .companyrevenue-main-item{
      width: 345px;
      margin-top: 12px;
      .companyrevenue-item-date{
        color: #333;
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
        line-height: normal;
        text-align: left;
        margin-bottom: 8px;
      }
      .companyrevenue-item-conent{
        width: 345px;
        border-radius: 8px;
        background: #FFF;
        padding: 18px 0 14px;
        position: relative;
        .companyrevenue-item-conent-price-box{
          width: 345px;
          padding-bottom: 17px;
          .companyrevenue-item-conent-price{
            color: #333;
            font-family: "PingFang SC";
            font-size: 22px;
            font-style: normal;
            font-weight: 600;
            line-height: normal;
            .unit{
              margin-right: 6px;
            }
          }
          .companyrevenue-item-conent-price-tip{
            color: #333;
            font-family: "PingFang SC";
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
            margin-left: 8px;
          }
        }
        .companyrevenue-item-conent-btn-box{
          border-top: 1px solid #F8F8F8;
          width: 345px;
          padding: 13px 0 0 0;
          display: flex;
          justify-content: space-around;
          align-items: center;
          color: #FF6F00;

          font-family: "PingFang SC";
          font-size: 15px;
          font-style: normal;
          font-weight: 400;
          line-height: normal;
          .van-icon{
            margin-left: 4px;
            font-size: 12px;
            color: #B3B3B3;
          }
        }
        .companyrevenue-item-conent-subscript-status{
          height: 20px;
          background: #3388FF;
          color: #fff;
          font-family: "PingFang SC";
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: normal;

          padding: 3px 8px;
          position: absolute;
          top: 0;
          right: 0;
          z-index: 1;
          border-top-right-radius: 8px;
          border-bottom-left-radius: 8px;
        }
      }
    }
  }
  .companyrevenue-tap-footer{
    width: 375px;
    height: 60px;
    background: #fff;
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 999;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .allprice-box{
      margin-left: 15px;
      color: #333;
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
      .unit{
        color: #F23522;
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        margin-right: 4px;
      }
      .allprice{
        color: #F23522;
        font-family: "PingFang SC";
        font-size: 24px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
      }
    }
    .revenue-submit{
      width: 164px;
      background: #F7830D;
      color: #FFF;
      font-family: "PingFang SC";
      font-size: 18px;
      font-style: normal;
      font-weight: 500;
      height: 60px;
      line-height: 60px;
      text-align: center;
    }
  }
  .empty {
    width: 100vw;
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    padding-top: 120px;
    img {
      width: 300px;
      margin-bottom: 15px;
    }
    span {
      width: 100vw;
      text-align: center;
      font-size: 17px;
      color: #666;
    }
  }
}
</style>
