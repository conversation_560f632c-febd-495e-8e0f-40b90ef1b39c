<template>
  <div class="bank-card-box">

    <div class="bank-card-box-search-box">
      <div class="bank-card-box-search">
        <img :src="companyrevenueSearch" alt="" class="companyrevenue-search-img">
        <input ref="searchInput" class="input" v-model="keyword" autofocus type="text" placeholder="请输入银行名称">
      </div>
    </div>

    <div class="bank-card-list" v-if="searchAllList.length">
      <div
        class="bank-card-list-item"
        v-for="(item, index) in searchAllList"
        :key="'a' + index"
        @click="bankCardClick(item)"
      >
        <img :src="item.logo_img" alt="" class="bank-card-list-item-logo_img" v-if="item.logo_img">
        <div class="bank-card-list-item-name">{{item.name}}</div>
      </div>
    </div>

    <van-empty description="暂无数据" v-if="!searchAllList.length && keyword !== ''" />

  </div>
</template>

<script>
import { getWithdrawBankList } from '@/api/docWorkRoom'
import companyrevenueSearch from '@/assets/images/channel/companyrevenue-search.png'
import {loadNativechannelRefreshPop, loadNativePage} from "@/utils/utils";
import Vue from "vue";

export default {
  name: "bankCard",
  components: {
  },
  data() {
    return {
      companyrevenueSearch,
      timer: null,  //搜索节流
      keyword: '',
      searchAllList: [],
      localFormData: {}
    }
  },
  watch: {
    keyword(newVal, oldVal){
      this.searchAllList = []
      if(newVal != ''){
        if (this.timer) clearTimeout(this.timer)
        this.timer = setTimeout(() => {
          this.getWithdrawBankList()
        }, 50)
      } else {
        this.getWithdrawBankList()
      }
    }
  },
  mounted() {
    this.getWithdrawBankList()
    this.$refs.searchInput.focus()
  },
  methods: {
    async getWithdrawBankList() {
      const res = await getWithdrawBankList({
        keyword: this.keyword
      })
      this.searchAllList = res.data || []
    },
    bankCardClick(items) {
      setTimeout(() => {
        loadNativechannelRefreshPop(items.name)
      })
      // let url = `/docWorkRoom/bankCard?workroom_id=${this.workroom_id}&footerPrice=${this.footerPrice}`
      // if (window.location.hostname === 'localhost') {
      //   url = {
      //     path: '/docWorkRoom/bankCard',
      //     query: {
      //       workroom_id: this.workroom_id,
      //       footerPrice: this.footerPrice,
      //     }
      //   }
      //   this.$router.push(url)
      //   return
      // }
      // loadNativePage(url)
    }
  }
}
</script>

<style scoped lang="scss">
.bank-card-box{
  width: 100vw;
  height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
  background-color: #f5f5f5;
  font-family: 'PingFangSC-Regular';

  .bank-card-box-search-box{
    width: 100vw;
    height: 51px;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1;
    background: #fff;
  }

  .bank-card-box-search{
    width: 345px;
    height: 33px;
    margin: 9px 15px;
    background: #F7F7F7;
    border-radius: 50px;
    display: flex;
    align-items: center;


    .companyrevenue-search-img{
      width: 17px;
      height: 17px;
      margin-left: 15px;
    }
    .input{
      flex: 1;
      font-size: 14px;
      font-weight: 400;
      background: transparent;
      margin-left: 8px;
      padding-right: 15px;
      width: 100%;
      height: 30px;
      line-height: 30px;
    }
    input::placeholder{
      // font-size: 12px;
      color: #ccc;
    }

  }

  .bank-card-list{
    padding: 51px 20px 0;
    width: 335px;
    background: #fff;
    .bank-card-list-item{
      padding: 16px 0;
      width: 335px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      border-bottom: 1px solid #EEEEEE;
      .bank-card-list-item-logo_img{
        width: 24px;
        height: 24px;
        flex-shrink: 0;
        margin-right: 7px;
      }
      .bank-card-list-item-name{
        color: #333;
        font-family: "PingFang HK";
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
      }
    }
    & .bank-card-list-item:last-child{
      border-bottom: 1px solid #fff;
    }
  }

  .van-empty{
    margin: 51px auto 0;
  }

  .bank-card-box-tip{
    color: #999;
    padding: 10px 0;
    font-family: "PingFang HK";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    margin: 0 auto;
  }

}

</style>
