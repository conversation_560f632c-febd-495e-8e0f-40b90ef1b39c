<template>
  <div class="employee">
    <header class="header">
      <van-dropdown-menu
        ref="dropdownMenu"
        active-color="#F7830D"
        :close-on-click-outside="false"
      >
        <van-dropdown-item
          @close="getIncomeByAgent(true)"
          :title="isShowDate"
          :value="currentDate"
          ref="dropdownItem"
          title-class="active"
        >
          <div class="custom-drop-menu">
            <van-datetime-picker
              v-model="currentDate"
              type="year-month"
              title="选择年月"
              :min-date="minDate"
              :max-date="maxDate"
              :show-toolbar="false"
              :formatter="formatter"
              :swipe-duration="300"
            />
            <div class="footer">
              <span @click="handleReset" class="btn">重置</span>
              <span @click="handleConfirm" class="btn confirm">提交</span>
            </div>
          </div>
        </van-dropdown-item>
        
      </van-dropdown-menu>
    </header>
    <main v-if="list.length" class="main">
      <table class="table">
        <thead>
          <tr>
            <th>姓名</th>
            <th>金额</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="(item, index) in list"
            :key="index"
          >
            <td>{{ item.sale_name }}</td>
            <td>{{ item.fee }}</td>
            <td @click="handleLook(item)" class="operate">查看明细</td>
          </tr>
        </tbody>
      </table>
    </main>
    <empty v-if="list.length == 0 && emptyFlag" :chartEmpty="true"></empty>
  </div>
</template>

<script>
import moment from 'moment'
import { Toast } from 'vant'
import { getIncomeByAgentApi } from '@/api/docWorkRoom'
import empty from '@/views/docWorkRoom/sickerMedicalRecord/components/empty.vue'
import { loadNativePage } from "@/utils/utils"
export default {
  
  components: {
    empty
  },
  data() {
    return {
      currentDate: new Date(),
      list: [],
      emptyFlag: false
    }
  },
  computed: {
    minDate() {
      return new Date(2022, 0)
    },
    maxDate() {
      const date = new Date()
      const y = date.getFullYear() + 1
      let m = date.getMonth()
      return new Date(y, m)
    },
    isShowDate () {
      return moment(this.currentDate).format('YYYY-MM')
    }
  },
  created () {
    const { order_fee_withdrawal  } = this.$route.query
    this.currentDate = new Date(order_fee_withdrawal)
    this.getIncomeByAgent()
    console.log(process.env.NODE_ENV )
  },
  methods: {
    formatter(type, val) {
      if (type === 'year') {
        return `${val}年`
      } else if (type === 'month') {
        return `${val}月`
      }
      return val
    },
    handleConfirm () {
      this.$refs.dropdownItem.toggle(false)
    },
    handleReset () {
      this.$refs.dropdownItem.toggle(false)
      const { order_fee_withdrawal  } = this.$route.query
      this.currentDate = new Date(order_fee_withdrawal)
    },
    async getIncomeByAgent (search) {
      const toast = Toast.loading({
        message: '加载中...',
        forbidClick: true,
        duration: 0
      })
      try {
        const { workroom_id, order_ids, order_fee_withdrawal } = this.$route.query
        const { data } = await getIncomeByAgentApi({
          workroom_id,
          order_ids: search ? undefined : order_ids,
          order_fee_withdrawal: search ? moment(this.currentDate).format('YYYY-MM') : order_fee_withdrawal
        })
        this.list = data
        if (this.list.length == 0) {
          this.emptyFlag = true
        }
        toast.clear()
      } catch {
        toast.clear()
      }
    },
    handleLook (item) {
      let param =  `/docWorkRoom/channel-order?workroom_id=${this.$route.query.workroom_id}&order_ids=${item.order_ids}&order_fee_withdrawal=${item.order_fee_withdrawal}&sale_id=${item.channel_doc}`
      if (process.env.NODE_ENV == 'development') {
        this.$router.push({
          path: param,
        })
      } else {
        loadNativePage(param)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.employee {
  background-color: #F5F5F5;
  height: 100vh;
  .header {
    background-color: white;
    font-size: 14px;
    color: #5A6266;
  }
  ::v-deep .van-popup {
    padding-bottom: 0;
  }
  .custom-drop-menu {
    padding: 15px;
  }
  ::v-deep .active {
    color: #f7830d;
  }
  .footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0 10px;
    &.inner {
      padding: 5px 0 10px;
    }
    .btn {
      display: inline-block;
      width: 162px;
      height: 44px;
      line-height: 44px;
      text-align: center;
      border-radius: 6px;
      font-weight: 500;
      font-size: 16px;
      color: #0a0a0a;
      background-color: #f5f6fb;
      &.confirm {
        color: #fff;
        background-color: #f7830d;
      }
    }
  }
  .main {
    padding: 20px;
    margin-top: 10px;
    background-color: white;
    .table {
      width: 100%;
      margin-bottom: 16px;
      border-radius: 2px 2px 0 0;
      border-collapse: collapse;
      border-spacing: 0;
      td, th {
        text-align: center;
        padding: 9px;
        border: 1px solid #EEE;
        vertical-align: middle;
        color: #474747;
        font-size: 13px;
      }
      th {
        background-color: #F7F7F7;
        color: #111;
        padding: 15px;
      }
      .operate {
        color: #F7830D
      }
    }
  }
  
}
</style>