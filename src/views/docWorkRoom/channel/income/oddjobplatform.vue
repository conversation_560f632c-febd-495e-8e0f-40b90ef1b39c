<template>
  <div class="oddjobplatform-box">

    <div class="companyrevenue-tip">
      <img :src="companyrevenueTip" alt="" class="companyrevenue-tip-img">
      <div class="companyrevenue-tip-content">
        <div>{{tipcontent}}</div>
        <div>{{tipcontent}}</div>
      </div>
    </div>

    <div class="immediately-main">
      <van-field label="姓名" :border="false" class="input" v-model="digit" type="text" input-align="right" placeholder="请填写" />
      <van-field label="身份证号" :border="false" class="input" v-model="digit" type="text" input-align="right" placeholder="请填写" />
      <van-field label="银行" :border="false" class="input" v-model="digit" type="text" input-align="right" placeholder="请填写" />
      <van-field label="开户行" :border="false" class="input" v-model="digit" type="text" input-align="right" placeholder="请填写" />
      <van-field label="银行卡号" :border="false" class="input" v-model="digit" type="number" input-align="right" placeholder="请填写" />
      <div class="agreement">
        <van-checkbox color="#181818" icon-size="16px" v-model="checked" @change="checkedChange"></van-checkbox>
        <div class="agreementText">
          已阅读并同意
          <a href="https://zz-med-test-pub.oss-cn-hangzhou.aliyuncs.com/agreement/contents/No-20220415-HVIT/v1-2-7.html"> 隐私保护政策 </a>
        </div>
      </div>

    </div>


    <div class="oddjobplatform-box-btn">提交</div>

  </div>
</template>

<script>
import companyrevenueTip from '@/assets/images/channel/companyrevenue-tip.png'

import {Toast} from "vant";

export default {
  name: "oddjobplatform",
  components: {
  },
  data() {
    return {
      companyrevenueTip,
      tipcontent: '请上传增值税专用发票，若有疑问可联系40009090900',
      digit: '',
      checked: false,
    }
  },
  methods: {
    copyInvoice() {
      let name = '发票抬头：上海智众医疗器械有限公司' + '\n纳税人识别号:XXXXXXXXXXX'
      let clipboard = new Clipboard('.invoice-copy', {
        text: function (trigger) {
          return name;
        }
      })
      clipboard.on('success', function (e) {
        if (e) {
          Toast('复制成功');
          e.clearSelection()
        } else {
          Toast('复制失败,请重试');
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.oddjobplatform-box{
  height: 100vh;
  overflow: auto;
  background-color: #f5f5f5;

  .companyrevenue-tip {
    width: 345px;
    padding: 12px 15px;
    background: #FDF8F1;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    .companyrevenue-tip-img{
      width: 16px;
      height: 16px;
      margin-right: 6px;
    }
    .companyrevenue-tip-content{
      color: #666;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
      text-align: left;
      word-break: break-all;
    }
  }

  .immediately-main{
    .input{
      padding: 17px 15px;
      border-bottom: 1px solid #F3F3F3;
      .van-cell__title{
        color: #333;
        font-family: "PingFang SC";
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
      }
      .van-field__control{
        color: #333;
        text-align: right;
        font-family: "PingFang SC";
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
      }
      input::placeholder{
        color: #999 !important;
      }
    }
  }

  .agreement{
    color: #181818;
    font-size: 12px;
    font-weight: 400;
    width: max-content;
    display: flex;
    align-items: center;
    margin: 12px 0 0 12px;
    .agreementText{
      margin-left: 5px;
      margin-top: 2px;
    }
    a{
      font-size: 12px;
      font-weight: 400;
      color: #181818;
      text-decoration: underline;
      text-underline-offset: 2px;
    }
    .van-icon {
      border: 1px solid #181818;
    }
  }

  .oddjobplatform-box-btn{
    width: 315px;
    height: 44px;
    line-height: 44px;
    border-radius: 8px;
    background: #F7830D;
    color: #FFF;
    font-family: "PingFang SC";
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    margin: 0 auto;
    position: fixed;
    left: 30px;
    bottom: 50px;
    z-index: 11;

  }

}

</style>
