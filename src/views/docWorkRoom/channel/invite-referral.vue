<template>
  <div class="invite-referral">
    <div class="content">
      <div class="item">
        <div class="lf">是否显示推荐人名称</div>
        <van-switch v-model="checked" @change="handleSubmit('checked')" size="24px" active-color="#FF6F00" />
      </div>
      <div class="item name" v-if="checked" @click="show_action_sheet = true">
        <div class="lf">推荐人名称<span class="tips">(4字以内)</span></div>
        <div class="rt active" v-if="name">{{ name }}</div>
        <div class="rt" v-else>填写您的名称</div>
      </div>
    </div>
    <van-action-sheet
      v-model="show_action_sheet"
      @open="handleActionSheetOpen"
      @closed="handleActionSheetClosed"
      :closeable="false"
    >
      <div class="invite-referral-action-sheet">
        <div class="header">
          <span class="lf" @click="handleCancel">取消</span>
          <span class="md">填写姓名</span>
          <span class="rt" @click="handleConfirm">完成</span>
        </div>
        <div class="content">
          <van-form @submit="handleSubmit('name')" action="">
            <van-field
              class="ipt-name"
              type="text"
              v-model="ipt_name"
              :maxlength="maxlength"
              placeholder="填写姓名"
              @input="handleInput($event)"
            />
          </van-form>
        </div>
      </div>
    </van-action-sheet>
  </div>
</template>

<script>
import { getSystemType } from '@/utils/utils'
import { updateChannelInfo, getinvitationQrcode } from '@/api/docWorkRoom'

export default {
  data() {
    return {
      channel_id: '', 
      channel_type: '',
      checked: false,
      name: '',
      ipt_name: '',
      handleSubmit: null,
      maxlength: 4,
      show_action_sheet: false,
    };
  },
  created() {
    this.channel_id = this.$route.query.channel_id
    this.channel_type = this.$route.query.channel_type
    this.init()
    this.handleSubmit = this.$debounce(this.handleChange, 300)
  },
  methods: {
    async init() {
      const res = await getinvitationQrcode({ channel_type: this.channel_type })

      if (res.code === 200) {
        this.checked = res.data.is_show_referrer === 1 ? true : false
        this.name = res.data.referrer_name
        this.ipt_name = this.name
      } else {
        this.$toast(res.msg)
      }
    },
    handleCancel() {
      this.show_action_sheet = false
    },
    handleConfirm() {
      this.handleChange('name')
    },
    handleActionSheetOpen() {
      this.ipt_name = this.name
    },
    handleActionSheetClosed() {
      this.ipt_name = ''
    },
    async handleChange(t) {
      const params = {
        channel_id: this.channel_id,
      }
      if (t === 'name') {
        params.is_show_referrer = this.checked ? 1 : 0
        params.referrer_name = this.ipt_name.trim()
        if (!params.referrer_name) {
          this.$toast('请填写姓名')
          return
        }
      } else if (t === 'checked') {
        params.is_show_referrer = this.checked ? 1 : 0
      }

      const res = await updateChannelInfo(params)

      if (res.code === 200) {
        if (t === 'name') {
          this.name = this.ipt_name.trim()
          this.show_action_sheet = false
        }

        if (getSystemType() === 'ios') {
          window.webkit.messageHandlers.nextRefresh.postMessage('')
        } else if (getSystemType() === 'android') {
          window.android.nextRefresh()
        }
      } else {
        this.$toast(res.msg)
      }
    },
    handleInput(e){
      // if (this.hasChinese(e)) {
      //   this.maxlength = 4
      // } else {
      //   this.maxlength = 6
      // }
    },
    hasChinese(str) {
      return /[\u4E00-\u9FA5\uF900-\uFA2D]/.test(str)
    },
  },
}
</script>

<style scoped lang="scss">
.invite-referral {
  min-height: 100vh;
  padding-top: 10px;
  background-color: #F5F5F5;
  .content {
    padding: 0 15px;
    background-color: #fff;
    .item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 56px;
      box-sizing: border-box;
      &.name {
        font-size: 17px;
        border-top: 1px solid #F5F5F5;
      }
      .lf {
        color: #0A0A0A;
        .tips {
          color: #999;
        }
      }
      .rt {
        color: #999;
        &.active {
          color: #666;
        }
      }
    }
  }
  ::v-deep .van-action-sheet {
    height: 80vh;
    max-height: 80vh;
    .invite-referral-action-sheet {
      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px;
        span {
          font-size: 16px;
          &.lf {
            color: #4C4C4C;
          }
          &.md {
            color: #000;
          }
          &.rt {
            color: #F7830D;
          }
        }
      }
      .content {
        margin-top: 9px;
        height: 56px;
        padding: 0 24px;
        .ipt-name {
          padding: 9px;
          border-bottom: 1px solid #F2F2F2;
          ::v-deep input {
            font-size: 17px;
          }
        }
      }
    }
  }
}
</style>