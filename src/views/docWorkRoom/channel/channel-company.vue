<template>
  <div class="channel-company">
    <div class="add" @click="handleInvite" v-if="entry !== 'inner'">
      <van-icon name="add-o" color="#CE8953" size="18px" />
      <span class="tips">邀请新员工</span>
    </div>
    <ul class="container" v-if="list.length > 0">
      <li v-for="(item, index) in list" :key="index" :class="{gray: item.is_enable == 0 ? true : false}">
        <div class="person">
          <div class="lf">
            <span :class="`logo ${item.sale_name ? 'on' : 'off'}`">{{ item.sale_name ? item.sale_name[0] : '未' }}</span>
          </div>
          <div class="md">
            <div class="name">
              <span class="text">{{ item.sale_name || '未完善信息' }}</span> 
              <span class="tel">{{ item.sale_cell }}</span>
              <span class="badge" v-if="item.is_admin">管理员</span>
            </div>
            <div class="top">
              <div>
                <span class="label">上级渠道：</span>
                <span class="value">{{ item.relation_name }}</span>
              </div>
              <div v-if="!item.is_admin">
                <span 
                  v-if="item.is_enable == 1" 
                  @click="handleSet(item)"
                  class="set">设置</span>
                <span v-else class="set">已停用</span>
              </div>
            </div>
          </div>
          <!-- <div class="rt">
            <span class="badge" v-if="item.is_admin">管理员</span> 
          </div> -->
        </div>
        <div class="sub" v-if="item.is_show">
          <div @click="handleGetDoctor(item)">
            <span class="label">服务客户：</span>
            <span class="value">{{ item.service_doc_cnt }}人</span>
            <van-icon name="arrow" color="#B3B3B3" />
          </div>
          <div @click="handleGetOrder(item.sale_id)">
            <span class="label">产生订单：</span>
            <span class="value">{{ item.service_order_cnt }}单</span>
            <van-icon name="arrow" color="#B3B3B3" />
          </div>
        </div>
      </li>
    </ul>
    <div class="empty" v-if="status && list.length === 0">
      <img src="../../../assets/images/channel/empty.png" alt="empty">
      <span>暂无员工</span>
    </div>
  </div>
</template>

<script>
import { loadNativePage } from '@/utils/utils'
import { getChannelAgentListData, getChannelAdminListData } from '@/api/docWorkRoom'

export default {
  data() {
    return {
      workroom_id: '',
      status: 0,
      list: [],
      entry: '',
    }
  },
  created() {
    this.workroom_id = this.$route.query.workroom_id
    this.init()
  },
  methods: {
    async init() {
      const { entry, workroom_id, hosp_id } = this.$route.query
      this.entry = entry
      let res = {}
      if (entry === 'inner') {
        res = await getChannelAgentListData({ hosp_id })
      } else {
        res = await getChannelAdminListData({ workroom_id })
      }
      
      if (res.code === 200) {
        const { name, agent } = res.data
        document.title = name
        this.list = agent || []
        this.status = 1
      } else {
        this.$toast(res.msg)
      }
    },
    handleInvite() {
      loadNativePage(`/docWorkRoom/invite?channel_type=invite_member&invite_reload=1`)
    },
    handleGetDoctor(item) {
      // this.$router.push({
      //   path: '/docWorkRoom/myServeDocList',
      //   query: {
      //     workroom_id: this.workroom_id,
      //     channel_doc: item.sale_id
      //   }
      // })

      loadNativePage(`/docWorkRoom/myServeDocList?workroom_id=${this.workroom_id}&channel_doc=${item.sale_id}`)
    },
    handleGetOrder(sale_id) {
      // this.$router.push({
      //   path: '/docWorkRoom/channel-order',
      //   query: {
      //     sale_id,
      //     workroom_id: this.workroom_id,
      //   }
      // })

      loadNativePage(`/docWorkRoom/channel-order?workroom_id=${this.workroom_id}&sale_id=${sale_id}`)
    },
    // 调整停用渠道页面
    handleSet ({sale_name, sale_cell, is_enable, sale_id}) {
      let param = `/docWorkRoom/deactivateChannel?sale_name=${sale_name}&sale_cell=${sale_cell}&is_enable=${is_enable}&sale_id=${sale_id}&workroom_id=${this.workroom_id}`
      if (process.env.NODE_ENV == 'development') {
        this.$router.push({
          path: encodeURI(param)
        })
      } else {
        loadNativePage(encodeURI(param))
      }
    }
  },
}
</script>

<style scoped lang="scss">
.channel-company {
  min-height: 100vh;
  overflow: hidden;
  background-color: #f5f5f5;
  .add {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 45px;
    background-color: #FFE4CF;
    .tips {
      margin-left: 6px;
      font-size: 17px;
      color: #CE8953;
    }
  }
  .container {
    li {
      width: 345px;
      // padding: 0 15px;
      margin: 12px auto;
      border-radius: 8px;
      box-sizing: border-box;
      background-color: #fff;
      .person {
        display: flex;
        // justify-content: space-between;
        align-items: center;
        position: relative;
        padding: 17px 15px;
        .lf {
          .logo {
            display: inline-block;
            width: 46px;
            height: 46px;
            line-height: 46px;
            border-radius: 50%;
            font-size: 17px;
            font-weight: 500;
            color: #fff;
            &.on {
              background-color: #FF6F00;
            }
            &.off {
              background-color: #3388FF;
            }
          }
        }
        .md {
          margin-left: 15px;
          text-align: left;
          flex: 1;
          .badge {
            display: inline-block;
            margin-left: 4px;
            background: #E4F1FF;
            color: #38F;
            font-size: 12px;
            border-radius: 4px;
            padding: 2px 2px 0;
          }
          .name {
            display: flex;
            align-items: center;
            margin-bottom: 6px;
            line-height: 1.3;
            color: #0A0A0A;
            .text {
              display: inline-block;
              max-width: 110px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              font-weight: 500;
              font-size: 17px;
              // display: -webkit-box;
              // -webkit-box-orient: vertical;
              // -webkit-line-clamp: 2;
            }
            .tel {
              margin-left: 6px;
              font-size: 15px;
              color: #666;
            }
          }
          .top {
            font-size: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            .label {
              color: #333;
            }
            .value {
              color: #666;
            }
          }
        }
        // .rt {
        //   flex: 1;
          
        // }
        .set {
          display: flex;
          justify-content: center;
          align-items: center;
          border: 1px solid #F7E1D0;
          padding: 4px 10px;
          // width: 42px;
          // height: 22px;
          border-radius: 33px;
          align-self: flex-end;
          text-align: right;
          font-size: 13px;
          color: #FF6F00;
        }
      }
      .sub {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 17px 0px;
        border-top: 1px solid #F8F8F8;
        > div {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 50%;
          font-size: 15px;
          .label {
            color: #0A0A0A;
          }
          .value {
            margin-right: 3px;
            color: #FF6F00;
          }
        }
      }
    }
    .gray {
      filter: grayscale(100%);
      opacity: 0.6;
    }
  }
  .empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 120px;
    img {
      width: 300px;
      margin-bottom: 15px;
    }
    span {
      font-size: 17px;
      color: #666;
    }
  }
}
</style>