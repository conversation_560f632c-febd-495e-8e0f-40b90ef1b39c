<template>
  <div class="consigneeInfo">
    <van-cell title-class="label" value-class="value" size="large" title="订单号" :center="true" :value="dataForm.order_no" />
    <van-cell title-class="label" value-class="value" size="large" title="商品/服务包名称" :center="true" :value="dataForm.package_name" />
    <van-cell title-class="label" value-class="value" size="large" title="收件人" :center="true" :value="dataForm.receiver_name" />
    <van-cell title-class="label" value-class="value" size="large" title="手机号" :center="true" :value="dataForm.receiver_phone" />
    <van-cell title-class="label" value-class="value" size="large" title="地址" :center="true" :value="dataForm.address" />
  </div>
</template>

<script>
import { getConsigneeInfo } from '@/api/docWorkRoom'
export default {
  data() {
    return {
      dataForm: {}
    }
  },
  methods: {
    getDetails(){
      getConsigneeInfo({order_no:this.$route.query.orderNo}).then(res=>{
        if(res.code == 200){
          this.dataForm = res.data
        }
      })
    }
  },
  created() {
    
  },
  mounted() {
    this.getDetails()
  }
}
</script>

<style lang='scss'>
.consigneeInfo{
  font-family: "PingFang SC";
  background: #F5F5F5;
  width: 100%;
  min-height: 100vh;
  .van-cell {
    background: white;
  }

  .label {
    color: #0A0A0A;
    font-size: 16px;
    max-width: 130px;
  }

  .value {
    color: #999999;
    font-size: 16px;
  }
}
</style>