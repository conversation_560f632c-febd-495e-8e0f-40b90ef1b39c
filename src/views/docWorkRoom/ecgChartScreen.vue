<template>
  <div>
        <ecgChart :addClass="['box100']"></ecgChart>
  </div>
</template>

<script>
import ecgChart from './ecgChart.vue'
export default {
    components: {
        ecgChart,
    },
  mounted() {
    // const UA = navigator.userAgent;
    // const isAndroid = UA.indexOf("Android") > -1 || UA.indexOf("Adr") > -1; // android终端
    // const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); // ios终端
    // if (isAndroid) {
    //   window.android.showLandScape(param);
    // } else if (isIOS) {
    //   window.webkit.messageHandlers.showLandScape.postMessage(param);
    // } else {
    //   this.$toast('无法横屏');
    // }
  },
};
</script>

<style lang="scss" scoped>
</style>