<template>
  <div class="report-conten">
    <van-notice-bar left-icon="volume-o" wrapable :scrollable="false" :text="noticeMessage" />
    <div class="list">
      <div class="list-box" v-for="(item, index) in infoData.indicator_groups" :key="index">
        <div class="list-title">
          <span>{{ item.indicator_group_name }}</span>
          <span v-if="infoData.indicator_groups.length > 1" @click="del(index)"><van-icon name="close" />删除</span>
        </div>
        <van-cell-group v-if="!mmc_record_id" class="list-input">
          <van-field clearable input-align="right" ref="vaninput" @click="getFocus(index)" v-model="item.detection_name"
            label="检查名称" placeholder="未识别,请重新输入" />
          <van-field input-align="right" @click="onOpenPicker(item.report_date, index)" :readonly="true"
            v-model="item.report_date" label="报告时间" :right-icon="!item.report_date ? 'arrow' : ''"
            placeholder="未识别,请重新输入" />
        </van-cell-group>
        <div class="list-config" v-for="(childItem, childIndex) in item.fields" :key="childIndex"
          @click="goToDetails(index, childIndex, childItem)">
          <div class="list-config-box">
            <div class="list-config-name">
              <span>{{ childItem.name || '--' }}</span>
              <span :class="!childItem.value ? 'noData' : ''">
                {{ childItem.value || '--' }}
              </span>
            </div>
            <div class="list-config-unit">
              <span>{{ childItem.reference_range || '--' }}</span>
              <span>{{ childItem.unit || '--' }}</span>
            </div>
            <!-- <div 
              v-if="mmc_record_id && childItem.mmc_plug" 
              class="tip">
              此数据问卷中已有, 将填充为OCR识别的结果
            </div> -->
            <div 
              v-if="mmc_record_id && childItem.mmc_plug && childItem.mmc_plug.err_tips" 
              class="tip">
              {{  childItem.mmc_plug.err_tips }}
            </div>
          </div>
          <van-icon class="list-config-icon" name="arrow" />
        </div>
        <van-cell-group v-if="!mmc_record_id" class="list-config-dep">
          <van-field ref="vaninputRemark" @click="getRemarkFocus(index)" input-align="right" v-model="item.remark"
            label="备注" placeholder="未识别，请输入" />
        </van-cell-group>
      </div>
    </div>
    <newTimePlug :values="currentDate" @changeValue="onCancelTime" ref="popup" :showPicker="show"
      @confirm="onConfirmTime" />
    <!-- <van-popup v-model="show" closeable round close-icon="close" position="bottom" :style="{ height: '40%' }">
      <van-datetime-picker v-model="currentDate" @cancel="onCancelTime" @confirm="onConfirmTime" type="datetime"
        title="选择年月日" :min-date="minDate" :max-date="maxDate" />
    </van-popup> -->
    <div class="foot-btn">
      <van-button class="btn" @click="submit">提交</van-button>
    </div>
    <van-overlay :show="loading" class-name="loading-overlay">
      <van-loading type="spinner" />
    </van-overlay>
  </div>
</template>

<script>
import { Toast } from 'vant'
import { recordDetail, recordSave } from '@/api/docWorkRoom.js'
import newTimePlug from '@/components/newTimePlug.vue'
export default {
  components: {
    newTimePlug
  },
  data() {
    return {
      loading: false,
      workroom_id: null,
      record_id: null,
      noticeMessage: '',
      username: '',
      show: false,
      reportTime: '',
      infoData: [],
      getOcrDataIndex: '',
      getOcrData: [],
      localIndex:0,
      mmc_record_id: undefined
    }

  },
  created() {
    // this.workroom_id = this.$route.query.workroom_id
    // this.record_id = this.$route.query.record_id
    // this.user_id = this.$route.query.user_id
    // this.getRecordDetail()
    const { mmc_record_id } = this.$route.query
    this.mmc_record_id = mmc_record_id  // mmc ocr 识别标记
  },
  mounted() {
    //为了兼容stg，对stg环境做单独处理。
    if (window.location.hostname == 'patient-h5.zz-med-stg.com') {
      if (location.href.indexOf("#reloaded") == -1) {
        location.href = location.href + "#reloaded";
        location.reload();
      }
    }
    this.workroom_id = this.$route.query.workroom_id
    this.record_id = this.$route.query.record_id
    this.user_id = this.$route.query.user_id
    if (localStorage.getItem('ocrData') ) {
      if (this.urlComparison()) {
        this.reloadData()
        this.notice()
      } else {
        this.getRecordDetail()
      }
      this.loading = false
    } else {
      //去请求接口
      this.getRecordDetail()
    }
    // window.deleteData = this.deleteData
  },
  beforeDestroy() {
    // this.deleteData()
  },
  methods: {
    urlComparison() {
      let isLoacl = false
      const localData = JSON.parse(localStorage.getItem('ocrData')) || []
      this.localIndex = localData.findIndex((item) => {
        return item.record_id == this.record_id
      })
      if (localData[this.localIndex]) {
        isLoacl = true
      } else {
        isLoacl = false
      }
      return isLoacl
    },
    getRecordDetail() {
      this.loading = true
      recordDetail({ workroom_id: this.workroom_id, record_id: this.record_id, user_id: this.user_id, mmc_record_id: this.mmc_record_id }).then((res) => {
        this.infoData = res.data
        this.notice()
        this.loading = false
      }).catch((err) => {
        this.$toast(err)
        this.loading = false
      })
    },
    notice() {
      if (!this.infoData.indicator_groups.length) {
        this.noticeMessage = `未识别出有效数据，请重新上传图片`
      } else if (this.infoData.report_user_name && this.infoData.report_user_name != this.infoData.user_name) {
        this.noticeMessage = `图片中患者姓名为“${this.infoData.report_user_name}”，与当前患者“${this.infoData.user_name}”不一致，请确认是否继续录入？`
      } else if (this.infoData.indicator_groups.length) {
        if (this.mmc_record_id) {  // mmc ocr
          this.noticeMessage = `AI识别已智能录入${this.infoData.indicator_group_num}个项目，${this.infoData.ocr_indicator_num}项指标：`
          if (this.infoData.cover_mmc_num) {
            this.noticeMessage += `(${this.infoData.cover_mmc_num}指标原先已有数据,将更新为ocr识别的新数据)`
          }
        } else {
          this.noticeMessage = `AI识别已智能录入${this.infoData.indicator_group_num}个项目，${this.infoData.ocr_indicator_num}项指标：`
        }
      }
    },
    /**
     * 日期格式化
     * @param {String | Date} date 日期
     * @param {String} sep 分隔符
     * @return {String} 格式化日期
     */
    dateFormat(date, sep = '-') {
      let oDate = new Date(date)
      console.log(oDate)
      let y = oDate.getFullYear()
      let m = oDate.getMonth() + 1
      let d = oDate.getDate()
      let h = oDate.getHours()
      let minutes = oDate.getMinutes()
      let s = oDate.getSeconds()
      if (m < 10) m = `0${m}`
      if (d < 10) d = `0${d}`
      if (h < 10) h = `0${h}`
      if (minutes < 10) minutes = `0${minutes}`
      if (s < 10) s = `0${s}`
      return `${y}${sep}${m}${sep}${d} ${h}:${minutes}:${s}`
    },
    onOpenPicker(val) {
      this.currentDate = val ? val : ''
      this.show = true
    },
    onConfirmTime(val) {
      this.infoData.indicator_groups.forEach(item => {
        item.report_date = this.dateFormat(val.replace(/-/g, "/"))
      });
      this.show = false
    },
    onCancelTime() {
      this.show = false
    },
    goToDetails(index, childIndex, item) {
      // 如果mmc_plug.is_disable_val == 1 不可以编辑
      if (this.mmc_record_id && item.mmc_plug && item.mmc_plug.is_disable_val == 1) {
        return
      }
      this.updateData()
      this.eachData()
      //更新数据,跳转
      this.$router.push({
        path: "/docWorkRoom/reportDetails",
        //第一index记录的是项目，第二个childIndex记录的是项目里的每一项
        query: {
          index: index,
          childIndex: childIndex,
          workroom_id: this.workroom_id,
          record_id: this.record_id,
          user_id: this.user_id,
          is_disable_unit: item.mmc_plug && item.mmc_plug.is_disable_unit
        },
      });
    },
    callApp(params) {
      const UA = navigator.userAgent;
      const isAndroid = UA.indexOf("Android") > -1 || UA.indexOf("Adr") > -1; // android终端
      const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); // ios终端
      if (isAndroid) {
        window.android.succeedSendData(params);
      } else if (isIOS) {
        window.webkit.messageHandlers.succeedSendData.postMessage(params);
      }
    },
    submit() {
      //deep数据
      const params = JSON.parse(JSON.stringify(this.infoData.indicator_groups))
      //处理成后端要的
      const submitData = params.reduce((pre, cur) => {
        const itemData = {
          indicator_group_id: cur.indicator_group_id,
          report_date: cur.report_date,
          detection_name: cur.detection_name,
          remark: cur.remark || '',
          indicator: cur.fields.map(field => ({
            code: field.code,
            value: field.value,
            unit: field.unit,
            unit_id: field.unit_id
          }))
        }
        pre.detections.push(itemData);
        return pre
      }, {
        workroom_id: this.workroom_id,
        user_id: this.user_id,
        record_id: this.record_id,
        detections: [],
        mmc_record_id: this.mmc_record_id
      })
      
      let checkData = submitData.detections.every((item) => {
        return item.report_date && item.detection_name
      })
      if (!checkData && !this.mmc_record_id) {
        return this.$toast('请检查字段是否填写完整')
      }
      recordSave(submitData).then((res) => {
        if (res.code !== 200) {
          this.$toast(res.msg)
        } else {
          //调用原生跳转router
          Toast({
            message: `<div style="width:116px;height:100px;flex-direction:column;display:flex;justify-content: center;align-items: center;">
      <i class="van-icon van-icon-passed" style="font-size:40px;margin-bottom:12px"></i><span>提交成功</span></div>`,
            type: 'html',
            forbidClick: true,
            duration: 1000,
          })
          // this.deleteData()
          this.callApp(res.code)
        }
      })
    },
    getFocus(index) {
      this.$refs.vaninput[index].focus();
    },
    getRemarkFocus(index) {
      this.$refs.vaninputRemark[index].focus();

    },
    eachData() {
      const ocrData = JSON.parse(localStorage.getItem('ocrData')) || []
      const localIndex = ocrData.findIndex((item) => {
        return item.record_id == this.record_id
      })
      if(ocrData[localIndex]){
        ocrData[localIndex].infoData = this.infoData
        localStorage.setItem('ocrData', JSON.stringify(ocrData))
      }
    },
    //更新设置localStorage数据
    updateData() {
      let ocrData = JSON.parse(localStorage.getItem('ocrData')) || []
      const localIndex = ocrData.findIndex((item) => {
        return item.record_id == this.record_id
      })
      if (ocrData.length && ocrData.length >= 500) {
        ocrData.shift()
        
      }
      if (!ocrData.length) {
        ocrData.push({
          workroom_id: this.workroom_id,
          record_id: this.record_id,
          user_id: this.user_id,
          infoData: this.infoData
        })
        localStorage.setItem('ocrData', JSON.stringify(ocrData))
      }else if (localIndex == -1 && ocrData.length) {
        ocrData.push({
          workroom_id: this.workroom_id,
          record_id: this.record_id,
          user_id: this.user_id,
          infoData: this.infoData
        })
        localStorage.setItem('ocrData', JSON.stringify(ocrData))
      }
    },

    //重新加载数据
    reloadData() {
      this.infoData = JSON.parse(localStorage.getItem('ocrData'))[this.localIndex].infoData
    },
    //删除数据
    deleteData() {
      localStorage.removeItem('ocrData')
    },
    del(index) {
      this.infoData.indicator_groups.splice(index, 1)
      //更新localStorage数据
      this.eachData()
    }
  },
}
</script>

<style lang="scss" scoped>
.report-conten {
  background-color: #F7F8FA;
  text-align: left;
  padding-bottom: 100px;
  overflow: hidden;
}

::v-deep .loading-overlay {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.8);
}

.list {
  .list-box {
    background-color: #fff;
    margin-bottom: 20px;

    .list-config-dep {
      height: 54px;
      display: flex;
      align-items: center;

      ::v-deep.van-cell__title {
        font-size: 16px;
        color: #0A0A0A;
      }
    }

    .list-title {
      display: flex;
      align-items: center;
      padding-left: 30px;
      padding-right: 20px;
      justify-content: space-between;
      height: 52px;

      span:nth-child(1) {
        font-size: 18px;
        font-weight: 500;
        color: #0A0A0A;
      }

      span:nth-child(2) {
        font-size: 14px;
        color: #F7830D;

        ::v-deep.van-icon {
          margin-right: 3px;
          display: inline-block;
        }
      }

      span:nth-child(1):before {
        content: ' ';
        width: 3px;
        height: 14px;
        background-color: rgba(238, 120, 0, 1);
        display: inline-block;
        left: 20px;
        padding-top: 1px;
        position: absolute;
        border-radius: 30px;

      }

    }

    .list-input {
      padding: 0px 0px 0px 10px;
      color: #0A0A0A;

      ::v-deep.van-field {
        height: 54px;
        display: flex;
        align-items: center;
        padding-left: 20px;
      }

      ::v-deep.van-cell__title {
        color: #0A0A0A;
        font-size: 16px;
      }


      ::v-deep.van-cell__title ::before {
        position: absolute;
        left: 8px;
        color: #ee0a24;
        font-size: 0.4375rem;
        content: '*';
      }

      ::v-deep.van-cell--clickable {
        padding: 10px 16px 10px 20px;
      }

      ::v-deep.van-icon-arrow:before {
        color: #ccc
      }
    }

    .list-config {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0px 18px;

      ::v-deep.van-cell {
        padding: 0;
        height: 52px;
        line-height: 52px;
      }

      .list-config-box {
        flex-direction: column;
        align-items: center;
        display: flex;
        justify-content: space-between;
        border-bottom: 1px solid #F2F3F5;
        padding: 12px 0;
        .tip {
          font-size: 12px;
          font-weight: 400;
          color: #FF6010;
          margin-top: 2px;
          text-align: left;
          align-self: flex-start;
        }

        .list-config-name,
        .list-config-unit {
          display: flex;
          justify-content: space-between;
          min-width: 260px;
          line-height: 24px
        }

        .list-config-name {
          margin-bottom: 4px;

          span:nth-child(1) {
            margin-right: 25px;
            width: 175px;
            color: #0A0A0A;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            font-size: 17px;
          }

          span:nth-child(2) {
            min-width: 50px;
            color: #0A0A0A;
            font-size: 18px;
          }
        }

        .list-config-unit {
          span:nth-child(1) {
            color: #5A6266;
            font-size: 15px;
          }

          span:nth-child(2) {
            min-width: 50px;
            color: #5A6266;
            font-size: 15px;
          }
        }

        .list-config-data {
          color: #000000;
          font-size: 16px;
          width: 50px;
          display: flex;
          align-items: center;
          justify-content: center;

          .downrotate {
            transform: rotate(-180deg);
          }
        }
      }


      .list-config-icon {
        font-size: 16px;
        color: #ccc;
      }
    }

    .list-config:nth-last-child(2) {
      .list-config-box {
        border-bottom: none;
      }
    }
  }
}

//单独的没数据样式class
.noData {
  font-size: 15px !important;
  color: #5A6266 !important
}

.foot-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  position: fixed;
  bottom: 0;
  left: 0;
  height: 100px;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.25);
  width: 100%;
  background-color: #fff;
  margin-top: 100px;

  .btn {
    width: 335px;
    height: 44px;
    background-color: #F7830D;
    border-radius: 7px;

    ::v-deep.van-button__text {
      color: #fff;
      font-size: 16px;
      font-weight: 600;
    }
  }
}

</style>
