<!--24小时动态血压-->
<template>
    <van-list
        v-model="loading"
        :finished="finished"
        :immediate-check="false"
        finished-text="没有更多了"
        @load="getHoursBpList"
        offset="300"
    >
        <div class="content">
            <div
                v-for="(item, index) in list"
                :key="index"
            >
                <div @click="handleClick(item, index)" class="title">
                    <div class="left">24小时动态血压</div>
                    <div class="right">
                        {{ item.measure_at && item.measure_at.slice(0, 10) }}
                        <van-icon v-if="item.extend" name="arrow-up"></van-icon>
                        <van-icon v-else name="arrow-down"></van-icon>
                    </div>
                </div>
                <div v-if="item.extend">
                    <table class="table">
                        <tr>
                            <td colspan="4">全天</td>
                        </tr>
                        <tr>
                            <td></td>
                            <td>收缩压(mmHg)</td>
                            <td>舒张压(mmHg)</td>
                            <td>脉搏(次/分)</td>
                        </tr>
                        <tr>
                            <td>均值</td>
                            <td>{{ emptyFunc(item.sbp_24) }}</td>
                            <td>{{ emptyFunc(item.dbp_24) }}</td>
                            <td>{{ item.pulse_24 == -1 ? '--' : item.pulse_24 }}</td>
                        </tr>
                        <tr>
                            <td>变异系数</td>
                            <td>{{ emptyFunc(item.covs_24) }}</td>
                            <td>{{ emptyFunc(item.covd_24) }}</td>
                            <td></td>
                        </tr>
                        <tr>
                            <td colspan="4">白天清醒时段</td>
                        </tr>
                        <tr>
                            <td></td>
                            <td>收缩压(mmHg)</td>
                            <td>舒张压(mmHg)</td>
                            <td>脉搏(次/分)</td>
                        </tr>
                        <tr>
                            <td>均值</td>
                            <td>{{ emptyFunc(item.day_sbp) }}</td>
                            <td>{{ emptyFunc(item.day_dbp) }}</td>
                            <td>{{ item.day_pulse == -1 ? '--' : item.day_pulse }}</td>
                        </tr>
                        <tr>
                            <td>变异系数</td>
                            <td>{{ emptyFunc(item.day_covs) }}</td>
                            <td>{{ emptyFunc(item.day_covd) }}</td>
                            <td></td>
                        </tr>
                        <tr>
                            <td colspan="4">夜间睡眠时段</td>
                        </tr>
                        <tr>
                            <td>均值</td>
                            <td>{{ emptyFunc(item.night_sbp) }}</td>
                            <td>{{ emptyFunc(item.night_dbp) }}</td>
                            <td>{{ item.night_pulse ? '--' : item.night_pulse }}</td>
                        </tr>
                        <tr>
                            <td>变异系数</td>
                            <td>{{ emptyFunc(item.night_covs) }}</td>
                            <td>{{ emptyFunc(item.night_covd) }}</td>
                            <td></td>
                        </tr>
                        <tr>
                            <td>昼夜血压节律</td>
                            <td colspan="4">
                                <div v-if="!item.change_rs && !item.change_rd">
                                    --
                                </div>
                                <div v-else>
                                    <span> 收缩压: {{ changeFunc(item.change_rs) }}</span>
                                    <span> 舒张压: {{ changeFunc(item.change_rd) }}</span>
                                </div>
                            </td>
                            
                        </tr>
                    </table>
                    <!--报告-->
                    <report-item
                        v-if="item.file_path && item.file_path.length > 0"
                        :fileList="item.file_path"
                        pageName="24小时动态血压"
                    ></report-item>
                </div>
            </div>
        </div>
    </van-list>
</template>

<script>
import reportItem from './sickerMedicalRecord/components/reportItem.vue'
import { getBloodHoursbpListApi } from '@/api/saas.js'
export default {
    components: {
        reportItem
    },
    data () {
        return {
            list: [],
            total: 0,
            page: 1,
            loading: false,
            finished: false
        }
    },
    computed: {
        searchInfo () {
            return this.$store.getters['docWorkRoom/searchInfo']
        }
    },
    created () {
        this.getBloodHoursbpList()
    },
    methods: {
        // 获取 24 小时动态血压列表
        async getBloodHoursbpList () {
            if (this.list.length < this.total) {
                this.page += 1
            }
            const { data } = await getBloodHoursbpListApi({
                patient_id: this.$route.query.patient_id,
                page: this.page,
                show_detail: 0,
                measure_type: 0,
                measure_method: 2,
                abnormal: 0,
                // ...this.searchInfo
            })
            this.total = data.total
            let temp = data.list.map(item => {
                item.extend = false
                return item
            })
            for (let i = 0; i < temp.length; i++) {
                this.list.push(temp[i])
            }
            this.loading = false
            if (this.list.length >= this.total) {
                this.finished = true
            }
        },
        handleClick (item, index) {
            this.list[index].extend = !item.extend
        },
        emptyFunc (content) {
            if (!content || content == 0) {
                return '--'
            }
            return content
        },
        changeFunc (num) {
            if (!num) {
                return '--'
            } else if (num < 0) {
                return '反勺型'
            } else if (num < 10) {
                return '非勺型'
            } else if (num < 20) {
                return '正常'
            } else if (num >= 20) {
                return '超勺型'
            } else {
                return '--'
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.content {
    background: #ffffff;
    font-size: 14px;
    text-align: center;
    .title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #f1f1f1;
        padding: 15px 19px;
        .left {
            font-size: 16px;
            color: #333333;
        }
        .right {
            font-size: 12px;
            color: #666666;
            font-weight: 400;
            &:hover {
                cursor: pointer;
            }
        }
    }
    .table {
        width: 100%;
        border: 1px solid #f1f1f1;
        tr {
            border: 1px solid #F1F1F1;
            height: 46px;
            &:nth-child(1) {
                // background: #f7f7f7;
                border: none;
                td {
                    border: none;
                }
            }
            td {
                border: 1px solid #f1f1f1;
                vertical-align: middle;
                text-align: center;
            }
        }
    }
}
</style>
