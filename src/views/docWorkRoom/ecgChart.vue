<template>
  <div>
    <div class="chart-box" :class="[...addClass]" ref="singleChartBox"></div>
  </div>
</template>

<script>
import echarts from "echarts";

import { getEcg<PERSON><PERSON> } from "@/api/saas.js";

export default {
  props: {
    addClass: {
      type: Array,
      default: () => [],
    },
    range_date: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      key: "",
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.ecgChart("singleChartBox");
    });
  },
  watch: {
    range_date(newValue) {
      // if(newValue){
      this.$nextTick(() => {
        this.ecgChart("singleChartBox");
      });
      // }
    },
  },
  computed: {
      searchInfo() {
          return this.$store.getters['docWorkRoom/searchInfo']
      },
  },
  methods: {
    async ecgChart(ref) {
      let { patient_id } = this.$route.query;
      let dataSource = (
        await getEcgChart({
          range_date: this.range_date,
          patient_id: patient_id,
          // ...this.searchInfo
        })
      ).data;

      console.log(
        "%c [ dataSource ]-33",
        "font-size:13px; background:pink; color:#bf2c9f;",
        dataSource
      );

      let date = [...new Set(dataSource.map((i) => i.date))];
      console.log(
        "%c [ date ]-150",
        "font-size:13px; background:pink; color:#bf2c9f;",
        date
      );

      let value = dataSource.map((i) => [i.sort, i.hr_chart]);
      console.log(
        "%c [ value ]-109",
        "font-size:13px; background:pink; color:#bf2c9f;",
        value
      );

      let myEcharts = echarts.init(this.$refs[ref]);
      console.log(
        "%c [ myEcharts ]-54",
        "font-size:13px; background:pink; color:#bf2c9f;",
        myEcharts
      );
      myEcharts.setOption({
        // title: {
        //   text: "心电图",
        //   //   subtext: '人数',
        //   left: -2,
        //   textStyle: {
        //     fontSize: 16,
        //     color: "#333333",
        //     fontWeight: 400,
        //   },
        // },
        dataZoom: [
          //1.横向使用滚动条
          {
            type: "slider", //有单独的滑动条，用户在滑动条上进行缩放或漫游。inside是直接可以是在内部拖动显示
            show: date.length > 4 ? true : false, //是否显示 组件。如果设置为 false，不会显示，但是数据过滤的功能还存在。
            start: 70, //数据窗口范围的起始百分比0-100
            end: 100, //数据窗口范围的结束百分比0-100
            zoomLock: true, //锁定选择的区域，不可以缩放，只能平移。不设置的话直接有默认值就行。
            filterMode: "empty",
            showDetail: true,
            minValueSpan: 12,
            xAxisIndex: [0], // 此处表示控制第一个xAxis，设置 dataZoom-slider 组件控制的 x轴 可是已数组[0,2]表示控制第一，三个；xAxisIndex: 2 ，表示控制第二个。yAxisIndex属性同理
            brushSelect: false,
            // bottom: -10, //距离底部的距离
            height: 20,
            bottom: 0,
          },
          //   //2.在内部可以横向拖动
          {
            type: "inside", // 内置于坐标系中
            start: 0,
            end: 18,
            zoomLock: true, //锁定选择的区域，不可以缩放，只能平移。不设置的话直接有默认值就行。
            xAxisIndex: [0],
          },
        ],

        grid: {
          containLabel: true,
          left: 12,
          // top:0,
          right: 30,
          bottom: 40,
        },
        tooltip: {
          trigger: "axis",
        },

        toolbox: {
          show: false,
          feature: {
            dataZoom: {
              yAxisIndex: "none",
            },
            dataView: { readOnly: false },
            magicType: { type: ["line", "bar"] },
            restore: {},
            saveAsImage: {},
          },
        },

        xAxis: {
          type: "category",
          boundaryGap: true,
          data: date,
        },

        yAxis: {
          // offset: -12,
          axisLabel: {
            formatter: "{value}",
            color: function (v) {
              if (v == 60 || v == 100) {
                return "#52C41A";
              } else {
                return "#333";
              }
            },
          },

          type: "value",
          minInterval: 1, //只显示整数

          splitNumber: 10,
          min: 0,
          max: 200,
          // axisLabel: {
          //   formatter: '{value}',
          // },
          splitLine: {
            //横坐标变虚线
            show: true,
            lineStyle: {
              type: [2, 16],
              color: "rgba(0, 0, 0, 0.25)",
              dashOffset: 16,
            },
          },
        },
        visualMap: {
          left: 12,
          top: 12,
          // itemWidth: 10,
          itemSymbol: "circle",
          orient: "horizontal",
          itemWidth: 10,
          textGrap: 6,
          itemGap: 20,
          pieces: [
            {
              label: "过缓心率",
              gt: 0,
              lte: 59,
              color: "#CDD0D6",
            },
            {
              label: "正常心率",
              gt: 59,
              lte: 100,
              color: "#52C41A",
            },
            {
              label: "过速心率",
              gt: 100,
              color: "#F5242D",
            },
          ],
        },

        series: [
          {
            connectNulls: true,
            symbol: "circle",
            symbolSize: 16,
            type: "scatter",

            markLine: {
              silent: true,
              symbol: "none",
              // label:{show:true,position:'start' , color: '#52C41A'},
              label: { show: false, position: "start" },
              emphasis: {
                disabled: true,
              },
              data: [
                {
                  type: "average",
                  yAxis: "100",
                  lineStyle: {
                    color: "#52C41A",
                  },
                  label: {
                    color: "#52C41A",
                  },
                },
                {
                  type: "average",
                  yAxis: "60",
                  lineStyle: {
                    color: "#52C41A",
                  },
                  label: {
                    color: "#52C41A",
                  },
                },
              ],
            },
            data: value,

            // itemStyle: {
            //   color: '#E33C64',
            //   normal: {
            //     color: '#E33C64',
            //     borderColor: '#fff',
            //     lineStyle: {
            //       color: '#E33C64',
            //     },
            //   },
            // },
          },
        ],
        tooltip: {
          // triggerOn:"click",
          enterable: true,
   
          formatter: (data) => {
            if (data.componentType == "markLine") {
              return;
            }
            let content = dataSource[data.dataIndex];
            return `
              <div>${content.measure_at}</div>
              <hr style="margin:8px 0;" />
              <div >
                <div style="display: flex;justify-content: space-between;"><div>心率(HR)：</div> ${content.hr}bpm</div>
                <div style="display: flex;justify-content: space-between;"><div>P-R：</div> ${content.pr}ms</div>
                <div style="display: flex;justify-content: space-between;"><div>QT/QTc：</div> ${content.qt_qtc}ms</div>
                <div style="display: flex;justify-content: space-between;"><div>QRS：</div> ${content.qrs}ms</div>
              </div>

            `;
          },
          // borderColor: 'rgba(109, 45, 45, 1)',
          // borderWidth: 5,
        },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.chart-box {
  width: 100%;
  height: calc(100vh - 80px);
}

.box100 {
  width: 100%;
  height: 100vh;
}
</style>