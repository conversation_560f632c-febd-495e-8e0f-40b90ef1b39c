<!-- 患者权益 -->
<template>
  <div class="patientRights">
    <div class="equity nocontent" v-if="equityData.coupon_list.length == 0">
      <div class="noContentBox">
        <div class="img"></div>
        <div>暂无数据</div>
      </div>
    </div>
    <div class="equity" v-for="(item, index) in equityData.coupon_list" :key="index" v-else>
      <div class="box" :style="{ 'background-image': 'url(' + item.coupon_bg_img + ')' }">
        <div class="time">{{ item.expire_start ? `${item.expire_start} ~ ${item.expire_end}` : null }}</div>
        <div class="commonStyle useBtn" v-if="item.status == '10'" @click="handleToWriteOff(item)">{{
          equityStatusList[item.status] }}</div>
        <div class="commonStyle unUsedBtn" v-else-if="item.status == '20'" @click="handleToWriteOff(item, true)">{{
          equityStatusList[item.status] }}</div>
        <div class="commonStyle unUsedBtn" v-else>{{ equityStatusList[item.status] }}</div>
      </div>
    </div>
  </div>
</template>
<script>
import { couponUse, getBenefitActivityCouponList } from "@/api/docWorkRoom.js";
export default {
  data() {
    return {
      equityData: {
        coupon_list: []
      },
      equityStatusList: {
        '10': '去核销',
        '20': '已使用',
        '30': '已过期',
        '40': '已取消',
        '50': '待解锁'
      }
    };
  },
  created() {
    this.init()
  },
  mounted() {

  },
  methods: {
    init() {
      if (this.$route.query.token) {
        this.token = this.$route.query.token
        localStorage.setItem('authorizationandroid', this.token)
      }
      if (this.$route.query.user_id && this.$route.query.workroom_id) {
        getBenefitActivityCouponList({ user_id: this.$route.query.user_id, workroom_id: this.$route.query.workroom_id }).then(res => {
          if (res.code == 200) {
            this.equityData = res.data
          }
        })
      }
      window.handleClick = (id) => { this.handleClick(id) }
    },

    // 去核销
    handleToWriteOff(item, type) {
      const UA = navigator.userAgent;
      const isAndroid = UA.indexOf("Android") > -1 || UA.indexOf("Adr") > -1; // android终端
      const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); // ios终端
      let param = `/docWorkRoom/equityVerificationCode?code=${item.coupon_code}`;

      if (type) {
        //已使用
        // 跳转到核销码
        // if (isAndroid) {
        //   window.android.jumpToequityVerification(param);
        // } else if (isIOS) {
        //   window.webkit.messageHandlers.jumpToequityVerification.postMessage(param);
        // }
      } else {
        // 二次确认
        if (isAndroid) {
          window.android.openModal(item.id);
        } else if (isIOS) {
          window.webkit.messageHandlers.openModal.postMessage(JSON.stringify(item.id));
        }
      }
    },
    handleClick(id) {
      let obj = this.equityData.coupon_list.find(item => item.id == id)
      const UA = navigator.userAgent;
      const isAndroid = UA.indexOf("Android") > -1 || UA.indexOf("Adr") > -1; // android终端
      const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); // ios终端
      let param = `/docWorkRoom/equityVerificationCode?code=${obj.coupon_code}`;

      couponUse({ user_id: obj.user_id, user_coupon_id: obj.id, workroom_id: this.equityData.workroom_id }).then(res => {
        if (res.code == 200) {
          // 跳转到核销码  //业务修改 核销之后不跳转核销码页面20240530
          // if (isAndroid) {
          //   window.android.jumpToequityVerification(param);
          // } else if (isIOS) {
          //   window.webkit.messageHandlers.jumpToequityVerification.postMessage(param);
          // }
          this.init()
        }else{
          this.$toast(res.msg)
        }
      }).catch(e => {
        this.$toast(e)
      })
    }
  },
  watch: {}
};
</script>
<style lang="scss">
.patientRights {
  width: 100%;
  height: 416px;

  .nocontent {
    display: flex;
    justify-content: center;
    align-items: center;

    .noContentBox {
      padding-top: 107px;
      padding-left: 14px;

      .img {
        width: 300px;
        height: 159.82px;
        background-image: url('../imgPage/nocontent.png');
        background-repeat: no-repeat;
        background-size: contain;
      }
    }
  }

  .box {
    width: 345px;
    height: 77px;
    margin: 15px;
    position: relative;
    background-repeat: no-repeat;
    background-size: contain;

    .time {
      color: #CEB99A;
      font-family: "PingFang SC";
      font-size: 13px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
      position: absolute;
      left: 16px;
      top: 40px;
    }

    .commonStyle {
      width: 91px;
      height: 32px;
      flex-shrink: 0;
      text-align: center;
      font-family: "PingFang SC";
      text-align: center;
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 500;
      line-height: 32px;
      position: absolute;
      top: 20px;
      right: 15px;
      cursor: pointer;
    }

    .useBtn {
      border-radius: 44px;
      background: #FF770F;
      color: #FFF;
    }

    .unUsedBtn {
      border-radius: 40px;
      border: 1px solid #FFE0C7;
      background: #FFFAF6;
      color: #D2B39C;
    }
  }
}

.van-dialog__confirm,
.van-dialog__confirm:active {
  color: #000
}
</style>