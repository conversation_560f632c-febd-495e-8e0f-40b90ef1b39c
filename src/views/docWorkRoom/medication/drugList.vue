<template>
    <div class="record-box">
      <div v-for="(item, index) in infoList" :key="index" class="info-list">
        <van-collapse v-model="activeName" :border="false" accordion>
          <van-collapse-item :name="`${index}`">
            <template #title>
              <div class="cart-box">
                <p :class="distinguish() ? 'title':'detalTitle'">{{ drugNameData(item) }}</p>
                <span :class="distinguish() ? 'spec' : 'detailSpec'" v-show="item.strength">{{ item.strength }}</span>
                <div class="status" v-if="item.medication_status == 1">
                  使用中
                </div>
                <div class="stop" v-else>已停用</div>
              </div>
            </template>
            <div class="content-box" @click="onDetails(item)">
              <div class="info">
                <p>{{item.start_time ? item.start_time.split(' ')[0] : '' }}至{{ item.end_time ? item.end_time.split(' ')[0] : '今' }}</p>
                <p v-if="item.handle_type == 2">变更日期:{{ item.change_time ? item.change_time.split(' ')[0] : '' }}</p>
                <p v-if="item.route_of_administration.length">
                  {{item.route_of_administration.length? item.route_of_administration.join(): ""}}
                </p>
                <p>{{ item.dose_frequency_name }}</p>
                <div v-if="item.drug_dosage.length" class="dose">
                  <span v-for="(child, index) in item.drug_dosage" :key="index">
                    <span v-if="child.type == 1">
                      <span  v-if="!child.dosage && !child.unit"></span>
                      <span v-if="child.dosage">{{ child.dosage }}</span>
                      <span v-if="child.unit">{{ child.unit }}/次</span>
                    </span>
                    <span v-if="child.type == 2">
                      <span v-if="child.time_name != '自定义时间' &&child.time_name != '其他'">
                        <template v-if="child.time_name">
                          <span>{{ child.time_name }}:{{ child.dosage}}{{ child.unit }};</span>
                        </template>
                      </span>
                      <span v-if="child.time_name == '自定义时间' ||child.time_name == '其他'">
                        <template v-if="child.time">
                          <span>{{ child.time }}:{{ child.dosage}}{{ child.unit }};</span>
                        </template>
                      </span>
                    </span>
                  </span>
                </div>
                <p>{{ useDrugDependent[item.compliance_type] }}</p>
                <p :class="distinguish() ? 'remark' : ''">
                  <span v-if="item.advice.length">
                    {{
                      item.advice
                        .split(",")
                        .map((index) => ["餐前", "随餐", "餐后"][index - 1])
                        .join(",")
                    }}
                  </span>
                  <span>{{ item.note }}</span>
                </p>
              </div>
            </div>
            <template v-if="channel == 'doctor'">
              <div class="foot-box" v-if="distinguish() && scene == 'hosp'">
                <div class="btn">
                  <span @click="onDetails(item)" class="details">详情</span>
                  <div>
                    <van-button v-if="item.handle_type != 3" @click="operateBtn(2, item)" type="default">变更用药</van-button>
                    <van-button v-if="item.handle_type != 3" @click="operateBtn(3, item)" type="default">停止用药</van-button>
                    <van-button v-if="item.handle_type == 3" @click="operateBtn(4, item)" type="default">重新用药</van-button>
                  </div>
                </div>
              </div>
              <div class="foot-box" v-show="distinguish()" v-if="scene == 'family'">
                  <div class="btn">
                    <span @click="onDetails(item)" class="details">查看详情</span>
                  </div>
              </div>
            </template>
             <template v-if="channel == 'patient'">
                <div class="foot-box" v-if="distinguish() && wx_changeActive == 1">
                  <div class="btn">
                    <span @click="onDetails(item)" class="details">详情</span>
                    <div>
                      <van-button v-if="item.handle_type != 3" @click="operateBtn(2, item)" type="default">变更用药</van-button>
                      <van-button v-if="item.handle_type != 3" @click="operateBtn(3, item)" type="default">停止用药</van-button>
                      <van-button v-if="item.handle_type == 3" @click="operateBtn(4, item)" type="default">重新用药</van-button>
                    </div>
                  </div>
                </div>
                <!-- <div class="foot-box" v-if="patientAactive == 0">
                    <div class="btn">
                      <span @click="onDetails(item)" class="details">查看详情</span>
                    </div>
                </div> -->
              </template>
          </van-collapse-item>
        </van-collapse>
      </div>
      <div v-show="channel == 'doctor'"  v-if="!this.infoList.length">
         <img class="nodrugresult" v-if="scene=='hosp'" src="../imgs/noHealx2.png" alt="" />
          <img class="nodrugresult" v-if="scene == 'family'" src="../imgs/nohislist.png" alt="" />
      </div>
      <div  v-show="channel == 'patient'" v-if="!this.infoList.length">
         <img class="nodrugresult" v-if="wx_changeActive==1" src="../imgs/noHealx2.png" alt="" />
          <img class="nodrugresult" v-if="wx_changeActive ==0" src="../imgs/nohislist.png" alt="" />
      </div>
      <div v-show="this.historicalData.length && channel == 'doctor'" v-if="distinguish()" @click="onGoTohistory" class="historicalData">查看历史数据</div>
        <div v-show="this.historicalData.length && wx_changeActive ==1 && channel == 'patient'" v-if="distinguish()" @click="onGoTohistory" class="historicalData">查看历史数据</div>
      <van-button v-if="channel == 'patient' && wx_changeActive == 1" class="drugRemind" type="info" round size="small" @click="goDrugRemind">用药提醒</van-button>
  </div>
</template>

<script>
import wx from 'weixin-js-sdk'
const UA = navigator.userAgent
const isAndroid = UA.indexOf('Android') > -1 || UA.indexOf('Adr') > -1 // android终端
const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
import { jsToClient } from '@/utils/bfBridge'
import { Toast } from 'vant'
export default {
  props: {
    infoList: {
      type: Array,
      default: () => []
    },
    historicalData: {
      type: Array,
      default:()=>[]
    },
    wx_changeActive: {
      type: Number,
      default:null
    }
  },
  data() {
    return {
      activeName: '0',
      scene: '',
      channel: '',
      patient_id: '',
      medicationTabs: [],
      useDrugDependent: {
        1: "规律服药",
        2: "间断服药",
        3: "不服药",
      },
    };
  },
  created() {
    this.scene = localStorage.getItem('scene') || ''
    this.channel = localStorage.getItem('channel') || ''
    this.patient_id = localStorage.getItem('patient_id') || ''
    this.user_id = localStorage.getItem('user_id') || ''
    this.medicationTabs = localStorage.getItem('medicationTabs') || []
  },
  computed: {
    isApp() {
      return this.$store.getters['docWorkRoom/isApp']
    }
  },
  methods: {
    goDrugRemind() {
      let ua = navigator.userAgent.toLowerCase()
      if(ua.indexOf('miniprogram') !== -1){
        wx.miniProgram.navigateTo({
          url: `/pages/my/pages/infoChoose/drugRemind?pageType=2`
        })
      }else{
        const params = {
          action: 0,
          data: {
            title: "用药提醒",
            url: `${process.env.VUE_APP_BF_BASE_URL}web/measure/reminder`,
            fullScreen: false
          }
        }
        jsToClient('jumpTo', JSON.stringify(params)).catch((err) => {
          Toast(JSON.stringify(err))
        })
      }
    },
    drugNameData(item) {
      const { generic_name, trade_brand_name, company_name } = item;
      const drug_name = trade_brand_name || company_name;
      return `${generic_name}${drug_name ? `(${drug_name})` : ""}`;
    },
    // onWxGoTohistory() {
    //   wx.miniProgram.navigateTo({
    //     url: `/pages/my/pages/infoChoose/medication?drugType=1`
    //   })
    // },
    onGoTohistory() {
      let param = `/docWorkRoom/medication/historyData`
      if (process.env.NODE_ENV === "production" && this.isApp) {
        if (isAndroid) {
          window.android.breathMessage(encodeURI(param));
        }
        if (isIOS) {
          window.webkit.messageHandlers.breathMessage.postMessage(encodeURI(param));
        }
      } else {
        this.$router.push({
          path: param
        })
      }
    },
    operateBtn(val, item) {
      let source = 1
      if (this.channel == 'doctor') {
        if (this.scene == 'hosp') {
          source = 1
        }
        if (this.scene == 'family') {
          source = 3
        }
      }
      if (this.channel == 'patient') {
        source = 3
      }
      
      // if(val == 2){
      let param = `/docWorkRoom/medication/addMedication?drugListId=${item.id}&editType=change&editStatus=${val}&recordId=${item.new_record_id}&source=${source}`
      // this.$router.push({
      //   path: param
      // })
      location.assign(param)
      // if (process.env.NODE_ENV === "production" && this.isApp) {
      //   if (isAndroid) {
      //     window.android.breathMessage(encodeURI(param));
      //   }
      //   if (isIOS) {
      //     window.webkit.messageHandlers.breathMessage.postMessage(encodeURI(param));
      //   }
      // } else {
      //   this.$router.replace({
      //     path: param
      //   })
      // }
      localStorage.removeItem('formData')
      localStorage.removeItem('editPageConfig')
    },
    distinguish() {
      if (this.$route.name == 'docWorkRoom.medication') {
        return true
      }
    },
    openCollapse() {
      console.log(this.$refs['collapse-item'])
      this.$refs['collapse-item'].toggle(true);
    },
    onDetails(item) {
      if (this.distinguish()) {
        let param = `/docWorkRoom/medication/drugDetails?id=${item.id}`
        this.$router.push({
          path: param
        })
        // if (process.env.NODE_ENV === "production" && this.isApp) {
        //   if (isAndroid) {
        //     window.android.breathMessage(encodeURI(param));
        //   }
        //   if (isIOS) {
        //     window.webkit.messageHandlers.breathMessage.postMessage(encodeURI(param));
        //   }
        // } else {
        //   this.$router.replace({
        //     path: param
        //   })
        // }
      }
    },
  },
  watch: {
    wx_changeActive(newValue, oldValue) {
        this.activeName = '0'
    },
    deep: true,
  }
};
</script>

<style lang="scss">
.record-box {
  height: 100%;
  padding: 15px 16px 0px 16px;
  // margin-bottom: 12px;
  // background-color: #f2f5f9;
  .info-list {
    margin-bottom: 12px;
  }
  ::v-deep.van-cell {
    border-radius: 8px;
    padding: 13px 15px;
    // min-height: 79px;
    min-height: 79px;
    // max-height: 105px;
  }
  .cart-box {
    .status {
      width: 49px;
      height: 21px;
      position: absolute;
      text-align: center;
      top: 0;
      right: 0;
      padding: 2px 6px;
      align-items: flex-start;
      border-radius: 0px 8px;
      background-color: #07c160;
      color: #fff;
    }
    .stop {
      @extend .status;
      color: #ffffff;
      background-color: #ccc;
    }

    .detalTitle {
      color: #0a0a0a;
      font-size: 18px;
      width: 270px;
    }
    .title {
      color: #0a0a0a;
      font-size: 18px;
      width: 270px;
      word-break: break-all;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1; /* 超出几行省略 */
      overflow: hidden;
    }
    .detailSpec {
      color: #878f99;
      font-size: 14px;
      width: 270px;
    }
    .spec {
      color: #878f99;
      font-size: 14px;
      width: 270px;
      word-break: break-all;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1; /* 超出几行省略 */
      overflow: hidden;
    }
  }

  .content-box {
    background: linear-gradient(180deg, #deecff 0%, #fcfeff 100%);
    border-radius: 6px;

    .info {
      text-align: left;
      padding: 11px 15px;
      // margin-top:13px;

      p {
        color: #5a6266;
        margin-bottom: 8px;
      }
      .dose {
        word-break: break-all;
        margin-bottom: 8px;
      }
    }
  }

  .foot-box {
    margin-top: 11px;
    margin-bottom: 15px;

    .btn {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .details {
        color: #38f;
        line-height: 22px;
      }

      .van-button {
        height: 32px;
        border-radius: 4px;
        color: #38f;
        border: 1px solid #d4e6ff;

        .van-button__text {
          font-weight: 500;
        }
      }

      .van-button:nth-child(2) {
        margin-left: 12px;
      }
    }
  }
}

.van-cell__title {
  text-align: left;
}

.van-collapse {
  border-radius: 8px;
  background: #fff;

.van-cell {
  border-radius: 8px;
  padding: 13px 15px;
  // min-height: 79px;
  min-height: 79px;
  // max-height: 105px;
}

  .van-collapse-item__content {
    padding: 0px 15px;
    border-radius: 8px;
    color: #5a6266;
  }

  .van-icon {
    line-height: 60px;
  }
}
.nodrugresult{
    width: 263px;
    height: 236px;  
    margin-top:147px;
}
.mb{
  margin-bottom: 8px;
}
.remark{
    word-break: break-all;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3; /* 超出几行省略 */
    overflow: hidden;
}
.historicalData{
  color:#38F;
  text-align: center;
  font-size: 14px;
  font-weight: 400;
}
.drugRemind{
  font-size: 15px;
  padding: 0 60px;
  margin-top: 20px;
}
</style>
