<template>
  <div class="search">
    <div class="searchInputOuter">
        <div class="searchInner">
            <img class="scanIcon" @click="scanBarCode" src="../imgs/scanIcon.png" alt="">
            <div class="colLine"></div>
            <input ref="searchInput" class="input" v-model="searchStr" autofocus @focus="handlePopup" type="text" placeholder="通用名/商品名/厂商名">
            <transition name="searchPop">
                <div class="scanPopup" v-if="isScanPopupShow">
                    <div class="scanArrow"></div>
                    <div class="textBox">
                        <span>扫描条形码，快捷录入</span>
                        <van-icon @click="isScanPopupShow = false" class="closeIcon" name="cross" color="rgba(255,255,255,0.6)" size="18" />
                    </div>
                </div>
            </transition>
            <transition name="searchPop">
                <div class="inputPopup" v-if="isInputPopupShow">
                    <div class="scanArrow"></div>
                    <div class="textBox">
                        <span>使用“空格“连接多个关键词轻松实现精准搜索</span>
                        <van-icon @click="isInputPopupShow = false" class="closeIcon" name="cross" color="rgba(255,255,255,0.6)" size="18" />
                    </div>
                </div>
            </transition>
        </div>
    </div>
    <div class="innerOuter">
        <div class="useListMain" v-if="isUseListShow">
            <div class="useTitle title">常用药品</div>
            <div class="useListOuter">
                <div class="item" v-for="item in commonUseDrugShowList" :key="item.drug_id" @click="selectDrug(item)">
                    <div class="leftName">
                        <span>{{item.drug_name}}</span>
                        <span class="noUse">{{item.strength}}</span>
                    </div>
                    <div class="rightStatus" :class="[item.status == 1?'useing':'noUse']" v-if="item.status && item.status != 0">{{item.status == 1?'使用中':'已停用'}}</div>
                </div>
                <div class="showMoreBtn" :class="[commonUseDrugAllList.length == commonUseDrugShowList.length?'transformShowMOreBtn':'' ]" v-if="commonUseDrugAllList.length > 10" @click="showMoreDrug"><van-icon size="16" color="#B3B3B3" name="arrow-down" /></div>
            </div>
        </div>
        <div class="resListMain" v-else>
            <div class="resList" v-if="searchAllDrugList.length > 0">
                <template v-if="!isShowMoreList">
                    <div v-for="(item,index) in searchAllDrugList"  :key="item.drug_id">
                        <div  v-if="item.values.length > 0">
                            <div class="splitLine" v-if="index != 0"></div>
                            <div class="labelTitle commonTitle">{{item.label}}</div>
                            <div class="item" v-for="drugItem in item.showPreValues" :key="drugItem.drug_id"  @click="selectDrug(drugItem)">
                                <span class="name" v-html="drugItem.showDrugName"></span>
                                <span v-if="drugItem.status && drugItem.status != 0" class="rightStatus" :class="[drugItem.status == 1?'useing':'noUse']">{{drugItem.status == 1?'使用中':'已停用'}}</span>
                            </div>
                            <div class="loadMore commonTitle" v-if="item.values.length > 3" @click="loadMore(item)">更多</div>
                            <!-- <div class="loadMore commonTitle" v-if="item.values.length > 3" @click="loadMore(item)">{{item.more?'收起':`显示全部（${item.values.length}）`}}</div> -->
                        </div>
                    </div>
                </template>
                <!-- <template v-else>
                    <div v-for="item in moreDrugList" :key="item.drug_id">
                        <div class="item" @click="selectDrug(item)">
                            <span class="name" v-html="item.showDrugName"></span>
                            <span v-if="item.status && item.status != 0" class="rightStatus" :class="[item.status == 1?'useing':'noUse']">{{item.status == 1?'使用中':'已停用'}}</span>
                        </div>
                    </div>
                </template> -->
            </div>
            
            <div v-if="!isLoadingShow && searchAllDrugList.length == 0 && this.searchStr.length > 1" class="noData">暂无数据</div>
        </div>
    </div>
    <van-loading class="loading" v-if="!isUseListShow && isLoadingShow" size="50" />
  </div>
</template>

<script>
const UA = navigator.userAgent;
const isAndroid = UA.indexOf("Android") > -1 || UA.indexOf("Adr") > -1; // android终端
const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); // ios终端
import {Dialog} from 'vant'
import { getSearchDrugList,getDrugListByBarCode } from '@/api/docWorkRoom.js'
import wx from "weixin-js-sdk";
import axios from 'axios'
export default {
    data(){
        return {
            timer: null,  //搜索节流
            isLoadingShow: true,  //loading是否显示
            isUseListShow: true,  //true：显示常用药品  false：显示搜索结果
            showMoreLabelTitle: false,  //猜你想搜下面 药品名称列表 是否显示全部
            searchStr: '',  //搜索内容
            isScanPopupShow: false,
            isInputPopupShow: false,
            searchAllDrugList: [],  //搜索到的所有药品

            commonUseDrugShowList: [],  //页面显示 常用药品
            commonUseDrugAllList: [],  //所有常用药品
            
            isShowMoreList: false,  // 查看更多 和 搜索列表 切换falg
            moreDrugList: [],  // 查看更多的列表数据
            source: '',
            channel: '',
            scene: '',
            medicationTabs: [],
            patientId: '',
            localFormData: {}
        }
    },
    watch: {
        searchStr(newVal,oldVal){
            if(newVal != ''){
                this.searchAllDrugList = []
                this.isUseListShow = false  //隐藏常用列表
                this.isLoadingShow = true
                this.isShowMoreList = false  //隐藏查看更多列表
                if (this.timer) clearTimeout(this.timer)
                this.timer = setTimeout(() => {
                    this.getSearchList()
                }, 1000)
            }else{
                this.isUseListShow = true
            }
        }
    },
    computed: {
        isApp() {
            return this.$store.getters['docWorkRoom/isApp']
        }
    },
    methods: {
        initScanPopup(){
            let isCanShowFlag = localStorage.getItem('isCanShowPopup')
            if(isCanShowFlag === null){
                this.isScanPopupShow = true
                setTimeout(()=>{
                    this.isScanPopupShow = false
                },3000)
            }
        },
        // 输入框聚焦 对popup的处理
        handlePopup(){
            let isCanShowFlag = localStorage.getItem('isCanShowPopup')
            if(isCanShowFlag === null){
                this.isScanPopupShow = false
                this.isInputPopupShow = true
                setTimeout(()=>{
                    this.isInputPopupShow = false
                },3000)
                localStorage.setItem('isCanShowPopup',false)
            }
        },
        // 处理药品数据 收缩展开
        handleInCommonUseDrug(){
            if(this.commonUseDrugAllList.length > 10){
                this.commonUseDrugShowList = this.commonUseDrugAllList.slice(0,10)
            }else{
                this.commonUseDrugShowList = this.commonUseDrugAllList
            }
        },
        showMoreDrug(){
            if(this.commonUseDrugShowList.length == this.commonUseDrugAllList.length){
                this.handleInCommonUseDrug()
            }else{
                this.commonUseDrugShowList = this.commonUseDrugAllList
            }
        },
        getSearchList(){
            let obj = {
                searchStr: this.searchStr,
                patientId: this.patientId,
                scene: this.source  //治疗方案：1  家庭数据 3
            }
            getSearchDrugList(obj).then(res=>{
                if(res.status == 200){
                    this.searchAllDrugList = res.data.map(item=>{
                        if(this.searchStr == ''){
                            if(item.key == 'common_use'){
                                this.commonUseDrugAllList = item.values
                                this.handleInCommonUseDrug()
                            }
                        }
                        item.values.map(innerItem=>{
                            innerItem.showDrugName = innerItem.drug_name
                            if(innerItem.matches.length > 0){
                                innerItem.matches.map(mItem=>{
                                    let colorStr = `<span style="color:#F53F3F;">${mItem}</span>`
                                    innerItem.showDrugName = innerItem.showDrugName.replace(mItem,colorStr)
                                })
                            }
                            innerItem.showDrugName = innerItem.showDrugName + `<span class="spec">${innerItem.strength}</span>`
                            
                            // let colorStr = `<span style="color:#F53F3F;">${innerItem.matches[0]}</span>`
                            // //拼接名称（匹配文字加颜色） + 规格
                            // innerItem.showDrugName = innerItem.drug_name.replace(innerItem.matches[0],colorStr) + `<span class="spec">${innerItem.strength}</span>`
                            innerItem.more = false
                            return innerItem
                        })
                        item.showPreValues = item.values.slice(0,3)
                        item.more = false
                        return item
                    })
                    this.isLoadingShow = false
                }else{
                    this.isLoadingShow = false
                    this.$toast(res.msg)
                }
            })
        },
        loadMore(item){
            let obj = {
                label: item.label,
                values: item.values
            }
            localStorage.setItem('moreDrugObj',JSON.stringify(obj))
            localStorage.setItem('searchStr',this.searchStr)
            this.$router.push({
                path: '/docWorkRoom/medication/searchMoreList',
                query: {
                    source: this.source
                }
            })
            // this.isShowMoreList = true
            // this.moreDrugList = []
            // this.moreDrugList = item.values
            // console.log(item.values)
            // window.scrollTo(0,0)
            // item.more?item.showPreValues = item.values.slice(0,3):item.showPreValues = item.values
            // item.more = !item.more
        },
        scanBarCode(){
            console.log(123,process.env.NODE_ENV )
            if(this.isApp){
                // if (process.env.NODE_ENV === 'production') {
                if (isAndroid) {
                    window.android.scanDrugJSClick()
                }
                if (isIOS) {
                    window.webkit.messageHandlers.scanDrugJSClick.postMessage('')
                }
            // }
            }else{
                let self = this
                wx.ready(function () {   //需在用户可能点击分享按钮前就先调用
                     wx.scanQRCode({
                        needResult: 1, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果，
                        scanType: ["barCode"], // 可以指定扫二维码还是一维码，默认二者都有
                        success: function (res) {
                            let result = res.resultStr.split(',')[1]
                            self.getDrugListByBarCode(result)
                        }
                    })   
                })
            }
        },
        // 端上 扫码成功的回调
        getDrugListByBarCode(barCode){
            let obj = {
                barCode,
                patientId: this.patientId,
                scene: this.source
            }
            getDrugListByBarCode(obj).then(res=>{
                if(res.status == 200){
                    if(res.data.length > 0){
                        let resDrug = res.data[0]
                        this.selectDrug(resDrug,'scan')
                        // let param = `/docWorkRoom/medication/addMedication?drugObj=${JSON.stringify(resDrug)}&returnFrom=search&source=${this.source}&medicationTabs=${JSON.stringify(this.medicationTabs)}&channel=${this.channel}&scene=${this.scene}`;
                        // if (process.env.NODE_ENV === "production" && this.isApp) {
                        //     if (isAndroid) {
                        //         window.android.breathMessage(param);
                        //     }
                        //     if (isIOS) {
                        //         window.webkit.messageHandlers.breathMessage.postMessage(param);
                        //     }
                        // }else{
                        //     this.$router.replace({
                        //         path: param
                        //         // path: '/docWorkRoom/medication/addMedication',
                        //         // query: {
                        //         //     drugObj: JSON.stringify(resDrug),
                        //         //     returnFrom: 'search',
                        //         //     source: this.source
                        //         // }
                        //     })
                        // }
                    }else{
                        Dialog.confirm({
                            message: '未找到该药品，请手动录入',
                            showCancelButton: true,
                            confirmButtonText: '去录入',
                            confirmButtonColor: '#3388FF',
                        }).then(() => {
                            // this.$router.push('/docWorkRoom/medication/search')
                        }).catch(() => {
                            // on cancel
                        })
                    }
                }
            })
        },
        toDrugDetails(item){
            let param = `/docWorkRoom/medication/drugDetails?id=${item.patient_drug_list_id}&drug_id=${item.drug_id}&expand=0`
            // if (process.env.NODE_ENV === "production" && this.isApp) {
            //     if (isAndroid) {
            //         window.android.breathMessage(encodeURI(param));
            //     }
            //     if (isIOS) {
            //         window.webkit.messageHandlers.breathMessage.postMessage(encodeURI(param));
            //     }
            // }else{
                this.$router.replace({
                    path: param
                    // path: '/docWorkRoom/medication/drugDetails',
                    // query: {
                    //     drug_id: item.drug_id,
                    //     expand: '0'
                    // }
                })
            // }
        },
        selectDrug(item,type){
            delete item.showDrugName
            localStorage.removeItem('searchStr')
            if(!this.localFormData){
                localStorage.setItem('drugObj',JSON.stringify(item))
            }else{
                localStorage.removeItem('drugObj')
                
                // this.localFormData.start_time = ''
                // this.localFormData.dataForm.start_time = ''

                this.localFormData.drugObj = item
                this.localFormData.dataForm.drug_id = item.drug_id
                this.localFormData.dataForm.drug_sku_id = item.drug_sku_id
                this.localFormData.dataForm.source = this.source
                // 如果药品携带途径等参数 即回填
                // 途径
                if(Array.isArray(item.route_of_administration) && item.route_of_administration.length > 0){
                    this.localFormData.dataForm.route_of_administration = item.route_of_administration
                    this.localFormData.tempRouteCheckedArr = item.route_of_administration
                }else{
                    this.localFormData.dataForm.route_of_administration = []
                    this.localFormData.tempRouteCheckedArr = []
                }
                // 频次
                if(item.dose_frequency){
                    this.localFormData.dataForm.dose_frequency = item.dose_frequency
                    this.localFormData.frequenceObj.id = item.dose_frequency
                }else{
                    this.localFormData.dataForm.dose_frequency = ''
                    this.localFormData.frequenceObj = {}
                }
                // 剂量
                if(item.dose || item.unit){
                    let obj = {
                        "type": 1,
                        "time_name": "",
                        "time": "",
                        "unit": item.unit,
                        "dosage": item.dose
                    }
                    this.localFormData.dataForm.drug_dosage.push(obj)
                    this.localFormData.dosageSelectedId = 1
                }else{
                    this.localFormData.dataForm.drug_dosage = []
                    this.localFormData.dosageSelectedId = null
                }
                localStorage.setItem('formData', JSON.stringify(this.localFormData))
            }
            
            if(item.drug_id && item.status != 0){
                if(type && type == 'scan'){
                    Dialog.confirm({
                        message: '该患者已使用此类药品，是否需要更新信息',
                        showCancelButton: true,
                        confirmButtonText: '去变更',
                        confirmButtonColor: '#3388FF',
                        closeOnClickOverlay: true,
                    }).then(() => {
                        this.toDrugDetails(item)
                    })
                }else{
                    this.toDrugDetails(item)
                }
            }else{
                let param = `/docWorkRoom/medication/addMedication?source=${this.source}`
                let localFormData = localStorage.getItem('formData')
                if(localFormData){
                    this.$router.go(-1)
                }else{
                    location.replace(param)
                }
                // if (process.env.NODE_ENV === "production" && this.isApp) {
                //     if (isAndroid) {
                //         window.android.breathMessage(encodeURI(param));
                //     }
                //     if (isIOS) {
                //         window.webkit.messageHandlers.breathMessage.postMessage(encodeURI(param));
                //     }
                // }else{
                    // this.$router.replace({
                    //     path: param
                        // path: '/docWorkRoom/medication/addMedication',
                        // query: {
                        //     drugObj: JSON.stringify(item),
                        //     returnFrom: 'search',
                        //     source: this.source
                        // }
                    // })
                // }
            }
            
        },
        getWxConfig(){
            let url = window.location.href.split('#')[0]
            let obj = {
                url,
                account: 'ihec'
            }
            axios.post('https://patient-api.zz-med.com/api/v1/wechat/js_api/sign',obj).then(res=>{
            // getWxConfig(obj).then(res=>{
                if(res.data.status == 0){
                    let {appId,timestamp,nonceStr,signature} = res.data.data
                    wx.config({
                        debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
                        appId: appId, // 必填，公众号的唯一标识
                        timestamp: timestamp, // 必填，生成签名的时间戳
                        nonceStr: nonceStr, // 必填，生成签名的随机串
                        signature: signature, // 必填，签名
                        jsApiList: [
                            "scanQRCode"
                        ],
                    })
                }
            })
        },
        init(){
            let searchMoreListSelected = localStorage.getItem('searchMoreListSelected')
            if(searchMoreListSelected){
                localStorage.removeItem('searchMoreListSelected')
                // this.$router.replace({
                //   path: '/docWorkRoom/medication/addMedication',
                // })
                let localFormData = localStorage.getItem('formData')
                if(localFormData){
                    this.$router.go(-1)
                }else{
                    location.replace('/docWorkRoom/medication/addMedication')
                }
                return
            }
            this.localFormData = JSON.parse(localStorage.getItem('formData'))
            this.$refs.searchInput.focus()
            this.channel = localStorage.getItem('channel')
            this.scene = localStorage.getItem('scene')
            this.medicationTabs = JSON.parse(localStorage.getItem('medicationTabs'))
            this.patientId = localStorage.getItem('patient_id')
            this.source = this.$route.query.source
            window.getDrugListByBarCode = this.getDrugListByBarCode
            this.searchStr = localStorage.getItem('searchStr') || ''
            this.getWxConfig()
            this.initScanPopup()
            this.getSearchList()
            
            this.patientAactive = this.$route.query.patientAactive
        },
    },
    mounted() {
        this.init();
    }
}
</script>

<style lang="scss">
.search{
    font-family: 'PingFangSC-Regular';
    .searchInputOuter{
        width: 100%;
        height: 40px;
        background: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        position: fixed;
        top: 0;
        .searchInner{
            width: 345px;
            height: 30px;
            background: #F5F5F5;
            border-radius: 50px;
            display: flex;
            align-items: center;
            position: relative;
            .scanIcon{
                width: 18px;
                height: 18px;
                margin-left: 10px;
            }
            .colLine{
                width: 1px;
                height: 17px;
                background: #DDDDDD;
                margin-left: 10px;
            }
            .input{
                font-size: 14px;
                font-weight: 400;
                background: transparent;
                margin-left: 10px;
                padding-right: 10px;
                width: 100%;
                height: 30px;
                line-height: 30px;
            }
            .inputPopup{
                position: absolute;
                top: 34px;
                left: 48px;
                .scanArrow{
                    position: absolute;
                    top: -14px;
                    left: 12px;
                }
                .textBox{
                    padding: 6px 12px;
                    line-height: 20px;
                }
            }
            .scanPopup{
                position: absolute;
                top: 34px;
                .scanArrow{
                    position: absolute;
                    top: -14px;
                    left: 12px;
                }
                .textBox{
                    padding: 0 12px;
                    height: 34px;
                    line-height: 34px;
                }
            }
            .scanPopup,.inputPopup{
                .scanArrow{
                    width: 0;
                    height: 0;
                    border-bottom: 8px solid rgba(0, 0, 0, 0.75);
                    border-left: 8px solid transparent;
                    border-right: 8px solid transparent;
                    border-top: 8px solid transparent;
                    left: 12px;
                }
                .textBox{
                    width: fit-content;
                    border-radius: 8px;
                    background: rgba(0, 0, 0, 0.75);
                    font-size: 16px;
                    color: white;
                    display: flex;
                    align-items: center;
                    .closeIcon{
                        margin-left: 12px;
                    }
                }
                span{
                    text-align: left;
                }
            }
        }
    }
    .innerOuter{
        margin-top: 40px;
        .title{
            font-size: 15px;
            font-weight: 400;
            color: #0A0A0A;
            display: flex;
        }
        .useTitle{
            color: #0A0A0A;
        }
        .resTitle{
            color: #C0C6CC;
        }
        .useListMain{
            padding: 17px 15px;
            background: #F2F5F9;
            min-height: calc(100vh - 74px);   //padding 17*2+40（搜索栏高度）
            .useListOuter{
                display: flex;
                flex-wrap: wrap;
                .item{
                    width: fit-content;
                    // min-height: 28px;
                    display: flex;
                    align-items: center;
                    // justify-content: center;
                    line-height: 16px;
                    text-align: left;
                    padding: 6px 10px 5px;
                    color: #5A6266;
                    font-size: 12px;
                    font-weight: 400;
                    border-radius: 20px;
                    background: white;
                    margin: 8px 8px 0 0;
                    .leftName{
                        flex: 1;
                    }
                    .rightStatus{
                        width: 50px;
                        text-align: right;
                    }
                }
            }
            .showMoreBtn{
                width: 28px;
                height: 28px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
                background: white;
                margin-top: 8px;
            }
            .transformShowMOreBtn{
                transform: rotate(180deg);
            }
        }
        .resListMain{
            padding: 12px 28px;
            background: white;
            min-height: calc(100vh - 74px);  
            .resList{
                .splitLine{
                    background: #f0f3f8;
                    height: 5px;
                    margin: 0 -28px;
                }
                .loadMore{
                    color: #3388FF;
                    height: 44px;
                    line-height: 44px;
                }
                .labelTitle{
                    color: #C0C6CC;
                    padding: 12px 0 7px 0;
                }
                .commonTitle{
                    font-size: 15px;
                    text-align: left;
                    width: 100%;
                }
                .item{
                    font-size: 15px;
                    padding: 12px 0;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    .name{
                        flex: 1;
                        color: #5A6266;
                        display: inline-block;
                        line-height: 20px;
                        text-align: left;
                        word-break: break-word;
                    }
                    .spec{
                        color: #C0C6CC;
                        margin-left: 10px;
                    }
                    .rightStatus{
                        width: 50px;
                        text-align: right;
                        font-size: 13px;
                    }
                }
            }
            
        }
    }
    .noUse{
        color: #878F99;
    }
    .useing{
        color: #377CFB;
    }
    .noData{
        color: #CCCCCC;
        margin-top: 30%;
        font-size: 16px;
    }
}
.searchPop-enter-active {
  transition: all .3s linear;
}
.searchPop-leave-active {
  transition: all .3s linear;
}
.searchPop-enter, .searchPop-leave-to{
    // transform: translate(100px);
  opacity: 0;
}
.loading{
    position: fixed;
    top: 30%;
    left: 50%;
    transform: translate(-50%,0);
}
</style>