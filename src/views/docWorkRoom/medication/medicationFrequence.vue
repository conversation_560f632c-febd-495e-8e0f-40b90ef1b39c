<template>
  <div class="medicationFrequence">
    <!-- 用药频次 -->
    <div class="topSelectOuter">
      <div class="routeItem" v-for="(item, index) in medicationFrequenceArr" @click="select(item)">
        <div>{{ item.name }}</div>
        <van-icon v-if="item.id === frequenceObj.id" name="success" color="#3388FF" />
      </div>
    </div>
  </div>
</template>

<script>
import { getMedicationConfig } from '@/api/docWorkRoom.js'
export default {
  data() {
    return {
      medicationFrequenceArr: [],
      frequenceObj: {},  //路由传的频次参数
      isEditFrequence: false,  //是否编辑
      localFormData: {}
    }
  },
  methods: {
    getMedicationFrequence(){
      // 给药频次
      getMedicationConfig({type: 1}).then(res=>{
        if(res.status == 200){
          this.medicationFrequenceArr = res.data
        }else{
          this.$toast(res.msg)
        }
      })
    },
    select(e) {
      console.log(e)
      this.frequenceObj = e
      this.localFormData.frequenceObj = e
      this.localFormData.dataForm.dose_frequency = e.id
      this.localFormData.dataForm.dose_frequency_name = e.name
      localStorage.setItem('formData', JSON.stringify(this.localFormData))
      if(this.isEditFrequence){
        this.$router.go(-1)
      }else{
        this.$router.replace({
          path: '/docWorkRoom/medication/medicationDosage',
          query: {
            frequenceObj: JSON.stringify(e),
            returnFrom: 'frequence'
          },
        })
      }
      
    },
    handleRouterParams(){
      // 处理频次参数 如果有 说明是编辑/修改频次
      if(this.localFormData.frequenceObj && JSON.stringify(this.localFormData.frequenceObj) != '{}'){
        this.isEditFrequence = true
        this.frequenceObj = this.localFormData.frequenceObj
      }else{
        if(this.localFormData.dataForm.drug_dosage && this.localFormData.dataForm.drug_dosage.length > 0){
          this.isEditFrequence = true
        }
      }
    },
    init() {
      this.localFormData = JSON.parse(localStorage.getItem('formData'))
      this.handleRouterParams()
      this.getMedicationFrequence()
    },
  },
  mounted() {
    this.init();
  },
};
</script>

<style lang="scss">
.medicationFrequence {
  background: #f0f3f8;
  min-height: 100vh;
  padding-bottom: 100px;
  .topSelectOuter {
    background: white;
    .routeItem {
      height: 52px;
      padding: 0 20px;
      border-bottom: 1px solid #f1f1f1;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 17px;
      color: #5a6266;
    }
  }
}
</style>
