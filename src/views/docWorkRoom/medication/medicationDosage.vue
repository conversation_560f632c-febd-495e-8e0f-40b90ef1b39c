<template>
  <div class="medicationDosage">
    <!-- 用药剂量 -->
    <div class="topSelectOuter">
      <div class="routeItem" @click="dosageSelectedId = 1">
        <div>统一每次用量</div>
        <van-icon v-if="1 === dosageSelectedId" name="success" color="#3388FF" />
      </div>
      <div class="unifyFieldOuter" v-if="dosageSelectedId === 1">
        <div class="customOuter" style="margin: 0;">
          <div class="cellBar">
            <div class="label">剂量</div>
            <div class="rightVal">
              <input class="val" v-model="unifyDosageForm.dosage" type="number" placeholder="请填写剂量">
            </div>
          </div>
          <div class="cellBar">
            <div class="label">单位</div>
            <div class="rightVal" @click="customSelect(item,index,'unit')">
              <div class="placeHolder" v-if="!unifyDosageForm.unit">请选择单位</div>
              <div class="val" v-else>{{ unifyDosageForm.unit }}</div>
              <van-icon name="arrow" color="#CCCCCC" />
            </div>
          </div>
        </div>
      </div>
      <div class="routeItem" @click="dosageSelectedId = 2">
        <div>自定义每次用量</div>
        <van-icon v-if="2 === dosageSelectedId" name="success" color="#3388FF" />
      </div>
    </div>
    <div class="customList" v-if="dosageSelectedId === 2">
      <div class="customOuter" v-for="(item,index) in customTimeDosageData" :key="index">
        <div class="cellBar">
          <div class="label">时间</div>
          <div class="rightVal" @click="customSelect(item,index,'time')">
            <div class="placeHolder" v-if="item.time == '' && item.time_name == ''">请选择时间</div>
            <div class="val">{{ item.time || item.time_name }}</div>
            <!-- <div class="val" v-if="item.time_name">{{ item.time_name }}</div> -->
            <!-- <div class="val" v-if="item.time" v-html="item.showTime || item.time"></div> -->
            <van-icon name="arrow" color="#CCCCCC" />
          </div>
        </div>
        <div class="cellBar">
          <div class="label">剂量</div>
          <div class="rightVal">
            <input class="val" v-model="item.dosage" type="number" placeholder="请填写剂量">
          </div>
        </div>
        <div class="cellBar">
          <div class="label">单位</div>
          <div class="rightVal" @click="customSelect(item,index,'unit')">
            <div class="placeHolder" v-if="!item.unit">请选择单位</div>
            <div class="val" v-else>{{ item.unit }}</div>
            <van-icon name="arrow" color="#CCCCCC" />
          </div>
        </div>
        <div class="delBtnBar" @click="delCustomItem(item,index)" v-if="customTimeDosageData.length != 1">
          <van-icon name="delete-o" size="16" color="#3388FF" />
          <span>删除</span>
        </div>
      </div>
      <div class="btn addBtn" @click="addCustomItem">
        <van-icon name="plus" size="14" /> 添加
      </div>
    </div>
    <div class="btn saveBtn" @click="saveData">保存</div>
    <!-- <div class="btnOuter" @click="saveData">
      <div class="btn">保存</div>
    </div> -->

    <!-- 早中晚选择框 -->
    <van-popup v-model="isTimePickerShow" round position="bottom">
      <van-picker
        title="选择时间"
        value-key="val"
        show-toolbar
        :columns="timePickerArr"
        @cancel="isTimePickerShow = false"
        @confirm="pickerConfirm($event, 'time')"
      />
    </van-popup>
    <!-- 时分 时间选择框 -->
    <van-popup v-model="isHoursPickerShow" round position="bottom">
      <van-picker
        title="选择时间"
        value-key="val"
        show-toolbar
        :columns="hoursPickerArr"
        @cancel="isHoursPickerShow = false"
        @confirm="pickerConfirm($event, 'hour')"
      />
    </van-popup>
    <!-- 单位选择框 -->
    <van-popup class="untPicker" v-model="isUnitPickerShow" round position="bottom">
      <van-picker
        title="选择剂量单位"
        value-key="name"
        show-toolbar
        :columns="unitPickerArr"
        @cancel="isUnitPickerShow = false"
        @confirm="pickerConfirm($event, 'unit')"
      />
    </van-popup>

    <!-- 剂量选择框 -->
    <!-- <van-popup v-model="isDosagePickerShow" round position="bottom">
      <van-picker
        show-toolbar
        :columns="dosagePickerArr"
        @cancel="isDosagePickerShow = false"
        @confirm="pickerConfirm($event, 'dosage')"
      />
    </van-popup> -->
    <van-dialog v-model="isOtherTimeShow" :close-on-click-overlay="true" title="其他" show-cancel-button confirm-button-color="#3388FF" @confirm="otherTimePopConfirm" @cancel="isOtherTimeShow = false">
        <template #default>
            <div class="otherTimePop">
              <van-field
                class="textareaField"
                v-model="otherTimeText"
                rows="5"
                :autosize='{ maxHeight: 100, minHeight: 100 }'
                type="textarea"
                placeholder="请输入"
                show-word-limit
              />
            </div>
        </template>
    </van-dialog>
  </div>
</template>

<script>
import { getMedicationConfig } from '@/api/docWorkRoom.js'
import { Toast } from 'vant'
export default {
  data(){
    return {
      otherTimeText: '',
      isOtherTimeShow: false,  // 选择时间-其他 录入弹窗是否显示
      isDosagePickerShow: false, //剂量弹窗
      isTimePickerShow: false, //早中晚时间弹窗
      isHoursPickerShow: false, //时分时间弹窗
      isUnitPickerShow: false,  //单位弹窗
      dosageSelectedId: 1,  //选中的剂量Id  1：统一剂量  2：自定义剂量
      frequenceObj: {},  //路由传过来的 频次数据
      isEditFrequence: false,  //是否编辑
      timePickerArr: [
        {
          id: 0,
          val: "自定义时间",
        },
        {
          id: 1,
          val: "早",
        },
        {
          id: 2,
          val: "中",
        },
        {
          id: 3,
          val: "晚",
        },{
          id: 4,
          val: "其他",
        }
      ],
      hoursPickerArr: [
        // 第一列
        {
          values: [],
          defaultIndex: 12,
        },
        // 第二列
        {
          values: [],
          // defaultIndex: 2,
        },
      ],
      customTimeDosageData: [
        {
          "type": 2,
          "time_name": "",
          "time": "",
          "unit": "",
          "dosage": "",
          "unitId": "",
          "curSelect": false,  //是否当前选中项 
        }
      ],
      unifyDosageForm: {
        "type": 1,
        "time_name": "",
        "time": "",
        "unit": "",
        "dosage": "",
        "unitId": ""
      },
      unitPickerArr: [],  //单位数组
      localFormData: {}
    }
  },
  methods: {
    // 处理时分选择框的数据
    handleTimePickerData() {
      let hours = [];
      for (let i = 0; i < 24; i++) {
        hours.push({
          val: i > 9 ? i + " 时" : "0" + i + " 时",
          id: i > 9 ? i : "0" + i,
        });
      }
      let minutes = [];
      for (let i = 0; i < 60; i++) {
        minutes.push({
          val: i > 9 ? i + " 分" : "0" + i + " 分",
          id: i > 9 ? i : "0" + i,
        });
      }
      this.hoursPickerArr[0].values = hours;
      this.hoursPickerArr[1].values = minutes;
    },
    getUnit(){
      // 剂量单位
      getMedicationConfig({type: 3}).then(res=>{
        if(res.status == 200){
          this.unitPickerArr = res.data
        }else{
          this.$toast(res.msg)
        }
      })
    },
    otherTimePopConfirm(){
      this.customTimeDosageData.forEach((item) => {
        if(item.curSelect){
          item.time = this.otherTimeText
        }
      })
    },
    // 选择框确认
    pickerConfirm(e, type) {
      console.log(e, type);
      let curIndex = this.customTimeDosageData.findIndex(item=>{
        return item.curSelect
      })
      let item = this.customTimeDosageData[curIndex]
      switch (type) {
        case "time":
          // 自定义时间
          // this.customTimeDosageData.forEach((item) => {
          //   if(item.curSelect){
          //     item.time_name = e.val
          //     item.time = ''
          //     this.otherTimeText = item.time
          //   }
          // })
          item.time_name = e.val
          if(e.id == 0){
            this.isHoursPickerShow = true
          }else if(e.id == 4){
            this.otherTimeText = item.time
            this.isOtherTimeShow = true
          }else{
            item.time = ''
          }
          this.isTimePickerShow = false
          break;
        case "hour":
          item.showTime = e[0].id + '<div style="margin-top: -2px;"> : </div>' + e[1].id
          item.time = e[0].id + ':' + e[1].id
          // item.time_name = ''
          // this.customTimeDosageData.forEach((item) => {
          //   if(item.curSelect){
          //     item.showTime = e[0].id + '<div style="margin-top: -2px;"> : </div>' + e[1].id
          //     item.time = e[0].id + ':' + e[1].id
          //     item.time_name = ''
          //   }
          // })
          this.isHoursPickerShow = false
          break;
        case "unit":
          if(this.dosageSelectedId == 1){
            this.unifyDosageForm.unit = e.name
            this.unifyDosageForm.unitId = e.id
          }else{
            item.unit = e.name
            item.unitId = e.id
            // this.customTimeDosageData.forEach((item) => {
            //   if(item.curSelect){
            //     item.unit = e.name
            //     item.unitId = e.id
            //   }
            // })
          }
          this.isUnitPickerShow = false
          break;
      }
      console.log(this.customTimeDosageData,this.unifyDosageForm)
    },
    // 添加自定义剂量条目
    addCustomItem() {
      let obj = {
        "type": 2,
        "time_name": "",
        "time": "",
        "unit": "",
        "dosage": "",
        "unitId": "",
        "curSelect": false
      }
      this.customTimeDosageData.push(obj);
    },
    delCustomItem(item,index){
      this.customTimeDosageData.splice(index,1)
    },
    customSelect(item,e,type) {
      type == 'time' ? this.isTimePickerShow = true : this.isUnitPickerShow = true
      if(item){
        // 先将选中项去掉
        this.customTimeDosageData.map(innerItem=>{
          innerItem.curSelect = false
        })
        item.curSelect = true
      }
      console.log(item,e)
    },
    toastFun(item){
      if(!item.time && !item.time_name){
        this.$toast('请选择时间')
        return false
      }
      if(!item.dosage){
        this.$toast('请输入剂量')
        return false
      }
      if(!item.unit){
        this.$toast('请选择剂量单位')
        return false
      }
      return true
    },
    checkData(){
      if(this.dosageSelectedId == 1){
        if(!this.unifyDosageForm.dosage){
          this.$toast('请输入剂量')
          return false
        }
        if(!this.unifyDosageForm.unit){
          this.$toast('请选择剂量单位')
          return false
        }
        return true
      }else{
        if(this.customTimeDosageData.length == 1){
          let item = this.customTimeDosageData[0]
          return this.toastFun(item)
        }else{
          for(let i in this.customTimeDosageData){
            let index = Number(i)
            let item = this.customTimeDosageData[index]
            if(this.toastFun(item)){
              continue
            }else{
              return false
            }
          }
        }
        return true
      }
    },
    saveData(){
      if(this.checkData()){
        console.log('通过')
        console.log(this.customTimeDosageData,this.unifyDosageForm,this.frequenceObj)
        Toast({
          message: '保存成功',
          icon: 'success',
        })
        
        this.localFormData.dosageSelectedId = this.dosageSelectedId
        this.localFormData.dataForm.drug_dosage = this.dosageSelectedId == 1 ? [this.unifyDosageForm] : this.customTimeDosageData
        localStorage.setItem('formData', JSON.stringify(this.localFormData))
        setTimeout(()=>{
          this.$router.go('-1')
        },2000)
      }
    },
    handleRouterParams(){
      // 处理 选择完频次后传来的频次数据
      if(this.localFormData.frequenceObj && JSON.stringify(this.localFormData.frequenceObj) != '{}'){
        this.frequenceObj = this.localFormData.frequenceObj
      }
      // 处理 修改/编辑时路由剂量参数
      if(this.localFormData.dataForm.drug_dosage && this.localFormData.dataForm.drug_dosage.length > 0){
        this.isEditFrequence = true
        let dosageData = this.localFormData.dataForm.drug_dosage
        this.dosageSelectedId = dosageData[0].type
        this.dosageSelectedId === 1 ? this.unifyDosageForm = dosageData[0] : this.customTimeDosageData = dosageData
      }
    },
    init() {
      this.localFormData = JSON.parse(localStorage.getItem('formData'))
      this.handleRouterParams()
      this.handleTimePickerData()
      this.getUnit()
    },
  },
  mounted(){
    this.init()
  }
}
</script>

<style lang="scss">
.medicationDosage{
  background: #f0f3f8;
  min-height: 100vh;
  padding-bottom: 100px;
  .topSelectOuter {
    background: white;
    .routeItem {
      height: 52px;
      padding: 0 20px;
      border-bottom: 1px solid #f1f1f1;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 17px;
      color: #5a6266;
    }
  }
  .unifyFieldOuter{
    padding: 12px 15px;
    background: #f0f3f8;
  }
  .customList{
    height: calc(100% - 210px);
    overflow-y: auto;
    // padding-bottom: 20px;
  }
  .customOuter {
    background: white;
    margin: 15px;
    border-radius: 8px;
  }
  .cellBar {
    padding: 0 15px;
    font-size: 16px;
    height: 47px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .label {
      color: #5a6266;
    }
    .rightVal {
      display: flex;
      align-items: center;
      .placeHolder {
        color: #c0c6cc;
      }
      .val {
        color: #5a6266;
        text-align: right;
        display: flex;
      }
      input{
        padding-right: 2px;
      }
      input::-webkit-input-placeholder {
        color: #C0C6CC;
      }
    }
  }
  .delBtnBar {
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: #3388ff;
    border-top: 1px solid #f1f1f1;
    span {
      margin: 2px 0 0 2px;
    }
  }
  .btnOuter {
    height: 100px;
    width: 100%;
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.25);
    background: white;
    position: fixed;
    bottom: 0;
    left: 0;
  }
  .btn {
    width: 335px;
    text-align: center;
    height: 44px;
    line-height: 44px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    margin: 0 auto;
  }
  .addBtn{
    border: 1px solid #3388FF;
    color: #3388FF;
  }
  .saveBtn{
    background: #3388ff;
    color: white;
    margin-top: 20px;
  }
  .otherTimePop{
    padding: 10px 15px 15px;
  }
  .textareaField{
    border: 1px solid #DCDFE6;
    padding: 9px 16px;
  }
  .untPicker .van-picker-column__item{
    // height: 50px !important;
    line-height: 50px;
  }
  .van-picker__confirm{
    color: #3388ff;
  }
}
.van-toast__icon{
  font-size: 36px !important;
}
</style>