<template>
  <div class="medication">
    <!-- 如果是医生工作并且是用药展示 -->
    <div v-if="channel =='doctor' && scene=='hosp'">
        <van-tabs v-model="active" class="tabOuter" @click="changeTab" title-active-color="#000" title-inactive-color="#666" :color="platformColor" swipe-threshold="2">
            <van-tab v-for="item in medicationTabs" title-class="tabTitle" :title="item.name">
            </van-tab>
        </van-tabs>
        <search-bar v-if="source ==1" class="search" @submitStatusClick="submitStatusClick" @submitTimeClick="submitTimeClick" />
        <van-search
            shape="round"
            v-if="source == 3"
            v-model="hisSearchName"
            show-action
            placeholder="药品通用名"
    >
        <template #action>
            <div @click="onSearch">搜索</div>
        </template>
    </van-search>
    </div>
    <!-- 如果是小程序展示 -->
    <div v-if="channel == 'patient'">
        <van-tabs v-model="active" class="tabOuter"  @click="patientChangeTab" title-active-color="#000" title-inactive-color="#666" :color="platformColor" swipe-threshold="2">
                <van-tab v-for="item in medicationTabs" title-class="tabTitle" :title="item.name">
                </van-tab>
            </van-tabs>
    </div>
    <template v-if="channel == 'patient' && source ==1 && hospitalList.length">
        <div class="smallProgram-box">
            <img class="img" :src="selectHospital.logo"/>
            <div class="smallProgram">
                <div class="title">
                    <span >{{ selectHospital.hosp_name }}</span>
                    <span class="family">{{ selectHospital.dept_name }}</span>
                </div>
                <div class="toggle" @click="onToggle">
                    <img src="../../docWorkRoom/imgs/toggle.png" alt=""/>
                    <span class="toggleNmae">切换</span>
                </div>
            </div>
        </div>
    </template>
     <!-- 如果是医生工作并且是用药展示 -->
    <div class="content" v-if="channel == 'doctor' && scene == 'hosp'">
        <van-loading v-if="loading" style="margin-top: 200px;"/>
        <drugList v-show="!loading" :historicalData="historicalData" v-if="source ==1" :infoList="infoList"></drugList>
        <hisDetails v-show="!loading" :infoHisList="infoHisList" v-if="source == 3" ></hisDetails>
        <div  v-if="source == 1 && scene == 'hosp'" class="releaseBtn">
            <div v-if="isRecord" class="record">点击添加用药记录</div>
            <img @click="AddDrug" src="../../docWorkRoom/imgs/publishx2.png" alt="" />
        </div>
    </div>
     <!-- 如果是医生工作并且是家庭展示 -->
    <div class="content" v-if="channel == 'doctor' && scene =='family'">
        <van-loading v-if="loading" style="margin-top: 200px;"/>
        <drugList v-show="!loading" :historicalData="historicalData" :infoList="infoList"></drugList>
    </div>
    <div class="content" v-if="channel == 'patient' ">
        <van-loading v-if="loading" style="margin-top: 200px;"/>
        <drugList v-show="!loading" v-if="source == 3 || source == 1" :wx_changeActive="wx_changeActive" :historicalData="historicalData" :infoList="infoList"></drugList>
        <div  v-if="source == 3 " class="releaseBtn">
            <div v-if="isRecord" class="record">点击添加用药记录</div>
            <img @click="AddDrug" src="../../docWorkRoom/imgs/publishx2.png" alt="" />
        </div>
    </div>
    <van-popup v-model="showSmallProgram" round position="bottom" :style="{ height: '50%' }">
        <div class="wxPopup-box">
            <h2 class="title">请选择机构/医院</h2>
            <div class="wxPopup-smallProgram-box" v-for="(item,index) in hospitalList" :key="index">
                <img class="img" :src="item.logo"/>
                <div class="smallProgram">
                    <div class="smallProgramTitle">
                        <span style="line-height: 28px;">
                            {{ item.hosp_name }}
                        <span>{{item.dept_name }}</span></span>
                    </div>
                    <div class="toggle" @click="addToggle(item)">
                        <span class="present" v-if="selectHospital.room_id == item.room_id">当前</span>
                        <span class="select" v-else>选择</span>
                    </div>
                </div>
            </div>
            <div class="cancel" @click="wxOnCancel">取消</div>
        </div>
    </van-popup>
  </div>
</template>

<script>
const UA = navigator.userAgent
const isAndroid = UA.indexOf('Android') > -1 || UA.indexOf('Adr') > -1 // android终端
const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
import hisDetails from './hisDetails.vue'
import searchBar from './components/searchBar.vue'
import drugList from './drugList.vue'
import { getPatientDrugList ,getPatientDrugHisdList , getPatientDeptRoomList , getPatientHistory } from '@/api/docWorkRoom.js'
export default {
    components: {
        searchBar,
        drugList,
        hisDetails
    },
    data(){
        return {
            loading:false,
            source:1,
            infoList: [],
            infoHisList: [],
            hospitalList: [],
            selectHospital:{},
            isRecord: false,
            showSmallProgram:false,
            showPopover: false,
            active:0,
            hisSearchName:"",
            formData: {
                patient_id: "",
                medication_status: -1,
                end_time:''
            },
            channel: '',
            medicationTabs: [],
            scene: '',//hosp医院  family家庭
            user_id: '',
            historicalData: [],
            ext: {},
        }
    },
    created() {
        if (this.$route.query.channel) {
            this.channel = this.$route.query.channel;
            this.scene = this.$route.query.scene;
            this.user_id = this.$route.query.user_id;
            this.formData.patient_id = this.$route.query.patient_id;
            this.$store.dispatch('docWorkRoom/setPlatform', this.channel == 'doctor' ? 'saas' : 'wechat')
            if (this.channel == 'doctor' && this.scene == 'hosp') {
                let tabsTitle = JSON.parse(this.$route.query.medicationTabs);
                if (tabsTitle && tabsTitle.length > 1) {
                    let findDrugTreatmentPlan = tabsTitle.find((item) => { return item.code == 'drug_management_treatment_plan' });
                    let findhisRecord = tabsTitle.find((item) => { return item.code == "drug_management_his_record" });
                    this.medicationTabs = [findDrugTreatmentPlan, findhisRecord]
                } else {
                    this.medicationTabs = tabsTitle;
                }
            }
            if (this.channel == 'doctor' && this.scene == 'family') {
                this.medicationTabs = []
                this.getGainDrugList()
            }
            if (this.channel == 'patient') {
                this.medicationTabs = [{ name: '治疗方案', code: 'drug_management_treatment_plan' }, { name: '家庭记录', code: 'drug_management_treatment_plan' }]
                this.getWxPatientDeptRoomList()
            }
        } else {
            this.scene = localStorage.getItem('scene') || ''
            this.channel = localStorage.getItem('channel') || ''
            this.formData.patient_id = localStorage.getItem('patient_id') || ''
            this.wx_changeActive = localStorage.getItem('wx_changeActive') || ''
            this.user_id = localStorage.getItem('user_id') || ''
            this.medicationTabs = JSON.parse(localStorage.getItem('medicationTabs')) || []
            if (this.channel == 'patient') {
                this.getWxPatientDeptRoomList()
            }
        }
        let isFrom = this.$route.query.isFrom
        if(isFrom && isFrom == 'zzApp'){
            localStorage.setItem('isFrom','zzApp')
        }else{
            localStorage.removeItem('isFrom')
        }
        localStorage.setItem('patient_id', this.formData.patient_id)
        localStorage.setItem('channel', this.channel)
        localStorage.setItem('medicationTabs', JSON.stringify(this.medicationTabs))
        localStorage.setItem('scene', this.scene)
        localStorage.setItem('user_id', this.user_id)
    },
    
    computed: {
        // platform != saas  就是小程序
        
        platform() {
        return this.$store.getters['docWorkRoom/platform']
        },
        isApp() {
        return this.$store.getters['docWorkRoom/isApp']
        },
        platformColor() {
        return this.$store.getters['docWorkRoom/platformColor']
        }
    },
    methods: {
        wxOnCancel() {
            this.showSmallProgram = false
        },
        onSearch() {
            this.changeTab(1)
        },
        getWxPatientHistory(id) {
            if (id) {
                 getPatientHistory(id).then((data) => {
                    if (data.status == 200) {
                        this.historicalData = data.data.list;
                        localStorage.setItem('historicalData', JSON.stringify(this.historicalData))
                    }
                })
            }
        },
        getWxPatientDeptRoomList(item) {
            getPatientDeptRoomList(
                 this.formData.patient_id
            ).then((res) => {
                if (res.status == 200) {
                    if (!item) {
                        this.hospitalList = res && res.data.length ? res.data : [];
                        this.selectHospital = res && res.data.length ? res.data[0] : [];
                        this.ext = { room_id: this.selectHospital.room_id };
                        localStorage.setItem('wx_room_id', this.selectHospital.room_id)
                    } else {
                        this.ext = { room_id:item.room_id }
                    }
                }
            }).then(() => {
                this.getGainDrugList()
            })
        },
        getGainDrugList() {
            this.loading = true
            if (this.channel == 'patient' && this.active == 0) {
                this.source = 1;
            }
            if (this.channel == 'patient' && this.active == 1) {
                this.source = 3
            }
            if (this.channel == 'doctor' && this.scene == 'hosp') {
                this.source = 1
            }
            if (this.channel == 'doctor' && this.scene == 'family') {
                this.source = 3
            }
            getPatientDrugList({
                source: this.source,
                ...this.formData,
                ext: this.ext,
                page_size: 99999,
            }).then((res) => {
                this.loading = false
                if (res.status == 200) {
                    const { data } = res
                    this.infoList = data.data
                }
            }).catch(() => {
                this.loading = false
            })
        },
        submitStatusClick(val) {
            this.formData.medication_status = val
            this.getGainDrugList()
        },
        submitTimeClick(val) {
            this.formData.end_time = val
            this.getGainDrugList()
        },
        patientChangeTab(val) {
            this.wx_changeActive = val
            localStorage.setItem('wx_changeActive', val)
            this.getGainDrugList()
        },
        changeTab(val) {
            this.loading = true
            if (val == 0) {
                this.source = 1;
                this.formData.medication_status = -1
                this.formData.end_time = ''
                this.getGainDrugList();
                this.hisSearchName = '';
            }
            if (val == 1) {
                this.source = 3
                getPatientDrugHisdList({
                    page: 1,
                    page_size: 99999,
                    drug_name: this.hisSearchName,
                    patient_id: this.formData.patient_id
                }).then((res) => {
                    this.loading = false
                    if (res.status != 200) return []
                    const { data } = res;
                    this.infoHisList = data.data.reduce((pre, cur) => {
                         let finnder = pre.find(({ id }) => id === cur.list_id);
                         if (!finnder) {
                            finnder = {
                                id: cur.list_id,
                                pres_cribed_at: cur.pres_cribed_at,
                                drug_name: cur.drug_name,
                                drug_spec: cur.drug_spec,
                                dosage_frequency:cur.dosage_frequency,
                                dosage: cur.dosage,
                                dosage_unit:cur.dosage_unit,
                                list: []
                            }
                            pre.push(finnder);
                        }
                        finnder.list.push(cur);
                        return pre
                    }, [])
                }).catch(() => {
                    this.loading = false
                })
            }
        },
        onSelect() {
            this.showPopover = !this.showPopover
        },
        onToggle() {
            this.showSmallProgram = true;
        },
        addToggle(item) {
            this.selectHospital = item;
            this.showSmallProgram = false;
            this.getWxPatientDeptRoomList(item)
            localStorage.setItem('wx_room_id',item.room_id)
        },
        AddDrug() {
            localStorage.removeItem('searchStr')
            let source = 1
            if (!this.isRecord && !localStorage.getItem('isRecord')) {
                this.isRecord = true;
                setTimeout(() => {
                   this.isRecord  = false
                    localStorage.setItem('isRecord', false);
                }, 3000)
            }
            if (this.channel == 'doctor') {
                if (this.scene == 'hosp') {
                    source = 1
                }
                if (this.scene == 'family') {
                    source = 3
                }
            }
            if (this.channel == 'patient') {
                source = 3
            }
            let param = `/docWorkRoom/medication/search?source=${source}`
            location.assign(param)
            // if (process.env.NODE_ENV === "production" && this.isApp) {
            //     if (isAndroid) {
            //         window.android.breathMessage(encodeURI(param));
            //     }
            //     if (isIOS) {
            //         window.webkit.messageHandlers.breathMessage.postMessage(encodeURI(param));
            //     }
            // } else {
                // this.$router.push({
                //     path: param
                // })
            // }
            // this.$router.push({
            //     path: '/docWorkRoom/medication/search',
            //     query: {
            //         source: this.scene == 'hosp' ? 1 : 3
            //     }
            // })
            localStorage.removeItem('formData')
            localStorage.removeItem('editPageConfig')
        }
    },
    watch: {
        medicationTabs(newVal) {
            console.log(newVal)
            let findDrugTreatmentPlan = newVal.find((item) => {return  item.code == 'drug_management_treatment_plan' });
            let findhisRecord = newVal.find((item) => { return item.code == "drug_management_his_record" });
            if (this.channel == 'doctor') {
                if (findDrugTreatmentPlan && !findhisRecord) {
                    this.source = 1
                    this.changeTab(0)
                }
                if (findhisRecord && !findDrugTreatmentPlan) {
                    this.source = 3
                    this.changeTab(1);
                }
                if (findDrugTreatmentPlan && findhisRecord) {
                    this.source = 1
                    this.changeTab(0)
                }
            }
        }
    },
    mounted(){
        if (!localStorage.getItem('isRecord')) {
            this.isRecord = true;
            setTimeout(() => {
                this.isRecord = false
                localStorage.setItem('isRecord', false);
            }, 3000)
        }
        if (localStorage.getItem('wx_changeActive') && this.channel == 'patient') {
            console.log(localStorage.getItem('wx_changeActive'));
            this.active = +localStorage.getItem('wx_changeActive')
            this.wx_changeActive = +localStorage.getItem('wx_changeActive')
        }
        let isFrom = this.$route.query.isFrom
        if(isFrom && isFrom == 'zzApp'){
            this.active = 1
        }
        if (localStorage.getItem('user_id') && localStorage.getItem('user_id')!='undefined') {
            let id = localStorage.getItem('user_id')
            this.getWxPatientHistory(id)
        }
    }
}
</script>

<style lang="scss">
body,html{
    // height: 100%;
    // padding: 0;
	// margin: 0;
    // background-color: #f2f5f9;
}
.search{
    height: 40px;
}
.medication{
    position: absolute;
    width: 100%;
    min-height: 100%;
    background-color: #f2f5f9;
    padding-bottom:10px;
    .tabOuter {
        height: 47px;
        background-color: #fff;
        .tabTitle{
            font-size: 16px;
            font-weight: 500;
        }
    }
    .releaseBtn{
        position: fixed;
        bottom:50px;
        right: 15px;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        .record{
            position: absolute;
            top: -45px;
            right: 0px;
            width: 152px;
            height: 32px;
            font-size: 16px;
            border-radius: 8px;
            background-color: rgba(0, 0, 0, 0.75);
            color:#fff;
            line-height: 32px;
        }
        .record:before {
            content: "";
            position: absolute;
            bottom: -20px;
            right: 15px;
            border-width: 10px;
            border-style: solid;
            border-color: rgba(0, 0, 0, 0.75) transparent transparent transparent;
        }
        img{
            width: 50px;
            height: 50px;
        }
    }
}
.smallProgram-box{
    // width: 350px;
    height: 58px;
    background-color: #fff;
    display: flex;
    align-items: center;
    padding: 10px 15px 10px 16px;

    .img{
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: inline-block;
        flex: 0 0 auto;
    }
    .smallProgram{
        display: flex;
        width: 100%;
        justify-content: space-between;
        align-items: center;
        .title{
            margin-left: 8px;
            font-size: 17px;
            display: flex;
            flex-direction: column;
            text-align: left;
            .family{
                color: #5A6266;
                font-size: 14px;
                font-weight: 400;
                line-height: 20px;
            }
        }
        .toggle{
            display: flex;
            flex-direction: column;
            img{
                width: 28px;
                height: 28px;
                display: block;
            }
            span{
                color: #38F;
                text-align: center;
                font-size: 12px;
                font-weight: 400;
            }
            .toggleNmae{
                line-height: 20px;
            }

        }
    }
}

// position: fixed;
//     bottom: 0;
//     left: 0;
//     width: 100%;
//     height: 57px;
//     line-height: 57px;
//     border-top: 8px solid #F5F5F5;
.wxPopup-box{
    .title{
        height: 56px;
        color: #0A0A0A;
        text-align: center;
        font-size: 17px;
        font-weight: 500;
        display: inline-block;
        line-height: 56px;
        border-bottom: 1px solid #EEE;
        width: 100%;
    }
    .wxPopup-smallProgram-box{
        height: 87px;
        background-color: #fff;
        display: flex;
        align-items: center;
        padding:0px 15px;
        border-bottom: 1px solid #EEE;
        .img{
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: inline-block;
            flex: 0 0 auto;
        }
        .smallProgram{
            display: flex;
            width: 100%;
            justify-content: space-between;
            .smallProgramTitle{
                width: 216px;
                margin-left: 8px;
                font-size: 17px;
                display: flex;
                flex-direction: column;
                text-align: left;
            }
            .toggle{
                margin-right: 9px;
                width: 52px;
                height: 28px;
                display: flex;
                flex-direction: column;
                .present{
                    text-align: center;
                    font-size: 12px;
                    font-weight: 500;
                    line-height:28px ;
                    color:#1677FF;
                    background-color: #D4E6FF;
                    border-radius: 4px;
                }
                .select{
                    text-align: center;
                    font-size: 12px;
                    font-weight: 400;
                    line-height:28px ;
                    color:#38F;
                    background-color: #FFF;
                    border: 1px solid #38F;
                    border-radius: 4px;
                }
                .toggleNmae{
                    line-height: 20px;
                }

            }
        }
    }
    .cancel{
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 57px;
        line-height: 57px;
        border-top: 8px solid #F5F5F5;
        color: #0A0A0A;
        text-align: center;
        font-size: 18px;
        background-color: #fff;
        font-weight: 400;
    }
}
.van-popup{
    padding-bottom: 58px;
}
.van-tabs__line{
    background-color:#38F !important;
}
</style>