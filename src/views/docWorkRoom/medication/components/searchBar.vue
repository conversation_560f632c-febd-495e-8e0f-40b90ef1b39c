<template>
  <div :class="`platform platform-${platform}`">
    <div class="searchOuter">
        <dropdown-menu
        class="dropdownMenu"
        :close-on-click-outside="false"
        >
            <dropdown-item :title="statusTitle" @open="dropdownOpen('1')" :title-class="status !== null?'dropActiveTitle':''" ref="dropdown-status">
                <div class="status-content">
                    <div class="statusItem" :class="{'status-act': status == item.value}" v-for="item in statusArr" :key="item.value" @click="statusItemClick(item)">{{item.name}}</div>
                </div>
                <div class="project-btn">
                <div class="reset" @click="resetClick()">重置</div>
                <div class="submit" @click="submitClick()">提交</div>
                </div>
            </dropdown-item>

            <dropdown-item :title-class="timeTitle != '时间筛选'?'dropActiveTitle':''" :title="timeTitle" @open="dropdownOpen('2')" ref="dropdown-time">
                <div class="time-content">
                  <!-- <div class="subTitle">随访维度</div>
                  <div class="followPicker" :class="[followText.indexOf('请选择') < 0?'selectedFollowColor':'']" @click="showFollowPicker = true">
                    {{followText}} 
                    <van-icon name="arrow" size="8" color="#CCCCCC" />
                  </div>
                  <div class="followPicker" :class="[followText.indexOf('请选择') < 0?'selectedFollowColor':'']" @click="showFollowPicker = true">
                    {{followText}}
                    <van-icon name="arrow" size="8" color="#CCCCCC" />
                  </div> -->
                  <div class="subTitle">时间维度</div>
                  <!-- <div v-for="item in cycleList" :key="item.value"
                      class="time-name"
                      :class="{'time-act': time == item.value}"
                      @click="timeClick(item)">
                      <span>{{item.name}}</span>
                  </div> -->
                  <div class="time-custom" @click="showcCalendar = true">
                      <div class="time-custom-input">{{end_date || '请选择用药结束时间 >'}}</div>
                  </div>
                </div>
                <div class="project-btn">
                  <div class="reset" @click="resetClick()">重置</div>
                  <div class="submit" @click="submitClick()">提交</div>
                </div>
            </dropdown-item>
        </dropdown-menu>
        <!-- <div class="searchBox" @click="goSearch">
            <img class="searchIcon" src="../../imgs/searchIcon.png" alt="">
            <span>搜药品</span>
        </div> -->
    </div>
    

    <van-calendar
      class="search-record-calendar"
      color="#3388FF"
      v-model="showcCalendar"
      :default-date="todayTime"
      :max-range="365"
      :min-date="minDate"
      :max-date="maxDate"
      :allow-same-day="true"
      @confirm="calendarConfirm" ></van-calendar>

      <van-popup v-model="showFollowPicker" round position="bottom">
        <van-picker
          show-toolbar
          :columns="followArr"
          @cancel="showFollowPicker = false"
          @confirm="followPickConfirm"
        />
      </van-popup>
  </div>
</template>

<script>
import moment from 'moment'
import { DropdownMenu, DropdownItem, Toast, Calendar } from 'vant';
import {getAppDoctorDepartment, getDoctorDepartment} from '@/api/saas.js'

export default {
  name: "searchRecord",
  components: {
    'dropdown-menu': DropdownMenu,
    'dropdown-item': DropdownItem
  },
  data() {
    return {
      followText: '请选择随访方案及随访阶段',
      showFollowPicker: false,
      followArr: ['杭州', '宁波', '温州', '绍兴', '湖州', '嘉兴', '金华', '衢州'],
      loading: true,
      isSubmit: false,
      showcCalendar: false,
      dropdownTab: '',
      minDate: new Date(),
      maxDate: new Date(),
      todayTime: new Date(),
      start_date: '',
      end_date: '',
      project_id: '',
      sel_project_id: '',
      time: '',
      statusTitle: '所有状态',
      timeTitle: '时间筛选',
      projects: [],
      cycleList: [
        { value: '1', name: '一周' },
        { value: '2', name: '两周' },
        { value: '3', name: '一个月'},
        { value: '3m', name: '三个月'},
        { value: '6m', name: '半年'},
        { value: '1y', name: '一年'}
        // 7天、15天、一个月（30天）、三个月（90天）、半年（180天）、一年（365天）
      ],
      statusArr: [
        { value: 1, name: '使用中' },
        { value: 2, name: '已停用' },
      ],
      status: null
    }
  },
  computed: {
    platform() {
      return this.$store.getters['docWorkRoom/platform']
    },
    isApp() {
      return this.$store.getters['docWorkRoom/isApp']
    },
    platformColor() {
      return this.$store.getters['docWorkRoom/platformColor']
    }
  },
  created() {
    const nextTime = moment(new Date()).add('year',1).format("YYYY-MM-DD").split('-')
    this.maxDate = new Date(nextTime[0]*1, 12, 31)
    this.minDate = new Date(1970, 1, 1)
  },
  mounted() {
    // this.setTime(this.isApp)
  },
  methods: {
    dropdownOpen(e) {
      this.isSubmit = true
      this.dropdownTab = e
      if (e == 1) {

      } else {
        // this.time = this.searchInfo.time
        if (!this.time) {
          // this.start_date = this.searchInfo.start_date
          // this.end_date = this.searchInfo.end_date
        } else {
          this.start_date = ''
          this.end_date = ''
        }
      }
    },
    resetClick() {
      if (this.dropdownTab == '1') {
        this.statusTitle = '全部状态'
        this.status = null
        this.isSubmit = false
        this.$refs['dropdown-status'].toggle(false);
        this.$emit('submitStatusClick', -1)
      } else {
        this.time = ''
        this.end_date = '',
        this.timeTitle = '时间筛选',
        this.$emit('submitTimeClick', '')
        // this.setTime()
        this.todayTime = new Date();
        this.isSubmit = false
        this.$refs['dropdown-time'].toggle(false);
      }
    },
    statusItemClick(e){
        this.status = e.value
    },
    // timeClick({ value }) {
    //   this.time = value
    //   this.start_date = ''
    //   this.end_date = ''
    // },
    calendarConfirm(date) {
      // const [end] = date;
      // this.time = ''
      // // this.start_date = moment(start).format('YYYY-MM-DD')
      this.end_date = moment(date).format('YYYY-MM-DD')
      this.showcCalendar = false
    },
    submitClick() {
      if (this.dropdownTab == '1') {
        if(this.status !== null){
          this.statusTitle = this.statusArr[this.status -1].name;
          this.$emit('submitStatusClick', this.status)
        }
        this.isSubmit = false
        this.$refs['dropdown-status'].toggle(false);
      } else if (this.dropdownTab == '2') {
        console.log(!this.end_date,'ffff')
        if (!this.end_date) {
          this.timeTitle = '时间筛选'
        } else {
          this.timeTitle = this.end_date
        }
        this.$refs['dropdown-time'].toggle(false);
        this.$emit('submitTimeClick', this.end_date)
      }
    },
    setTime(isSearch = true) {
      let start_date = ''
      let end_date = ''
      if (this.time) {
        const { startTime, endTime } = this.getFollowupTime(this.time)
        start_date = startTime
        end_date = endTime
      } else {
        start_date = `${this.start_date.slice(0, 4)}-${this.start_date.slice(5, 7)}-${this.start_date.slice(8, 10)}`
        end_date = `${this.end_date.slice(0, 4)}-${this.end_date.slice(5, 7)}-${this.end_date.slice(8, 10)}`
      }
      this.isSubmit = false
      this.$refs['dropdown-time'].toggle(false);
      let title = '时间筛选'
      if (this.time) {
        title = this.cycleList.filter(v => v.value == this.time)[0].name
      } else {
        title = this.end_date
      }
      this.timeTitle = title
      if (isSearch) this.$emit('getCoreData')
    },
    getFollowupTime(type) {
      return {
        '1': { // 一周
          startTime: moment().subtract(7, 'days').format('YYYY-MM-DD'),
          endTime: moment().format('YYYY-MM-DD'),
        },
        '2': { // 两周
          startTime: moment().subtract(14, 'days').format('YYYY-MM-DD'),
          endTime: moment().format('YYYY-MM-DD'),
        },
        '3': { // 一个月
          startTime: moment().subtract(30, 'days').format('YYYY-MM-DD'),
          endTime: moment().format('YYYY-MM-DD'),
        },
        '3m': { // 近90天
          startTime: moment().subtract(90, 'days').format('YYYY-MM-DD'),
          endTime: moment().format('YYYY-MM-DD'),
        },
        '6m':{ // 近半年
          startTime: moment().subtract(180, 'days').format('YYYY-MM-DD'),
          endTime: moment().format('YYYY-MM-DD'),
        },
        '1y': { // 近1年
          startTime: moment().subtract(365, 'days').format('YYYY-MM-DD'),
          endTime: moment().format('YYYY-MM-DD'),
        },
        '2y':{ // 近2年
          startTime: moment().subtract(730, 'days').format('YYYY-MM-DD'),
          endTime: moment().format('YYYY-MM-DD'),
        },
        '3y':{ // 近3年
          startTime: moment().subtract(1095, 'days').format('YYYY-MM-DD'),
          endTime: moment().format('YYYY-MM-DD'),
        },
      }[type]
    },
    goSearch(){
      // this.$router.push({
      //   path: '/docWorkRoom/medication/addMedication'
      // })
      location.assign('/docWorkRoom/medication/addMedication')
    },
    followPickConfirm(e){
      console.log(e)
      this.followArr = ['v1','v2','v3']
      // this.showFollowPicker = false
    }
  }
}
</script>

<style lang="scss" scoped>
$app-color: #F7830D;
$wx-color: #3388FF;
$app-bg-color: #F8F8F8;
$wx-bg-color: #F5F7FB;
.platform{
  border: 1px solid #F2F5F9;
  ::v-deep .hideIcon{
    color: #5A6266 !important;
    &::after {
      opacity: 0;
      z-index: -1 !important;
    }
  }
  ::v-deep .dropActiveTitle{
    color: $wx-color !important;
  }
  ::v-deep .van-popup{
    padding-bottom:5px;
  }
  ::v-deep .van-dropdown-menu__bar{
    // padding: 0 10px;
    height: 40px;
    box-shadow: initial;
    .van-dropdown-menu__item{
    //   padding: 0 10px;
    }
    .van-dropdown-menu__title--active{
      color: #0A0A0A !important;
    }
    .van-dropdown-menu__title{
      color: #5a6266;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
    //   line-height: 20px;
      &::after {
        border-color: transparent transparent #5a6266 #5a6266;
      }
    }
    .van-dropdown-item__content{
      background: #FFF;
    }
  }
  .searchOuter{
    margin-bottom: 15px;
    display: flex;
    align-items: center;
  }
  .dropdownMenu{
    flex: 2;
  }
  .searchBox{
    height: 40px;
    color: #5A6266;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
    .searchIcon{
        width: 14px;
        height: 14px;
        margin-top: -2px;
    }
    span{
        margin-left: 3px;
    }
  }
  .status-content{
    height: 210px;
    padding: 12px 20px;
    // border-top: 1px solid #F2F5F9;
    display: flex;
    .statusItem:last-of-type{
        margin-left: 10px;
    }
    .statusItem{
        width: 80px;
        height: 32px;
        border-radius: 6px;
        background: #F5F6FB;
        color: #5A6266;
        font-size: 14px;
        text-align: center;
        line-height: 32px;
    }
    .status-act{
        background: #F5F6FB;
        color: $wx-color;
    }
  }
  .time-content::-webkit-scrollbar {
    width: 0;
  }
  .time-content {
    height: 260px;
    overflow-y: auto;
    margin: 0 20px 0 10px;

    .time-name {
      display: inline-block;
      width: 105px;
      height: 32px;
      line-height: 32px;
      border-radius: 6px;
      background: #F5F6FB;
      margin: 13px 0 0 10px;
      text-align: center;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      color: #5A6266;
    }

    .time-act {
      background: #F5F6FB;
      color: $wx-color;
    }

    .time-custom{
      // display: flex;
      // justify-content: space-between;
      // align-items: center;
      margin: 12px 0 0 10px;
      .time-custom-input{
        height: 32px;
        line-height: 34px;
        flex-shrink: 0;
        border-radius: 6px;
        background: #F5F6FB;
        text-align: center;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        color: #C0C6CC;
      }
      .time-custom-line{
        width: 11px;
        height: 2px;
        background: #E5E5E5;
      }
    }
    .time-custom-color{
      .time-custom-input{
        color: $wx-color;
      }
    }
  }
  .project-btn{
    padding: 20px;
    display: flex;
    justify-content: space-between;
    .reset{
      width: 162px;
      height: 44px;
      flex-shrink: 0;
      border-radius: 8px;
      background: $wx-bg-color;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 44px;
      color: #0A0A0A;
    }
    .submit{
      width: 162px;
      height: 44px;
      flex-shrink: 0;
      border-radius: 8px;
      background: $wx-color;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 44px;
      color: #fff;
    }
  }
  .search-record-calendar{
    ::v-deep .van-button--round{
      border-radius: 8px;
    }
    ::v-ddep .van-button--round{
      background-color: #3388FF;
    }
    ::v-deep .van-icon{
      position: absolute;
      font-size: 22px;
    }
  }
}

.platform-wechat{
  ::v-deep .van-dropdown-menu__bar{
    .van-dropdown-menu__title{
    //   color: $wx-color;
    }
  }
  .dropActiveTitle{
    color: $wx-color !important;
  }
  .project-btn{
    .reset{
      background: $wx-bg-color;
    }
    .submit{
      background: $wx-color;
    }
  }
  .status-act{
        background: #F5F6FB;
        color: $wx-color;
    }
  .time-content {
    .time-act {
      color: $wx-color;
    }
    .time-custom-color{
      .time-custom-input{
        color:$wx-color;
      }
    }
  }
}
.time-content{
  .subTitle{
    color: #878F99;
    font-size: 12px;
    font-weight: 500;
    text-align: left;
    margin: 10px 0 0 13px;
  }
  .followPicker{
    width: 332px !important;
    height: 32px !important;
    line-height: 32px;
    border-radius: 6px;
    background: #F5F6FB;
    color: #C0C6CC;
    font-size: 14px;
    margin: 10px 0 0 13px;
  }
  .selectedFollowColor{
    color: #0A0A0A;
  }

}

</style>
