<template>
  <div class="his-detail-box">
    <div class="his-detail">{{ infoList.pres_cribed_at }}</div>
    <div class="info-list">
      <van-collapse v-model="activeName" :border="false" v-for="(item, index) in infoList.list" :key="index">
        <van-collapse-item :name="`${index}`">
          <template #title>
            <div class="cart-box">
              <p >{{ item.drug_name }}</p>
              <p>{{ item.drug_spec }}</p>
            </div>
          </template>
             <div class="content-box">
                <div class="info">
                  <p>{{ item.start_time ? item.start_time.split(' ')[0] : '' }} 至 {{ item.end_time ? item.end_time.split(' ')[0] : '今' }}</p>
                  <span v-if="item.dosage_route">{{ item.dosage_route }};</span>
                    <span v-if="item.dosage_frequency">{{ item.dosage_frequency }};</span>
                  <span>{{ item.dosage }}{{ item.dosage_unit }}</span>
                </div>
              </div>
        </van-collapse-item>
      </van-collapse>
    </div>
    
  </div>
</template>

<script>
export default {
  data() {
    return {
      infoList: [],
      activeName:[]
    }
  },
  mounted() {
    this.infoList = JSON.parse(localStorage.getItem('hisListInfo'))
    this.activeName = this.infoList.list.reduce((pre, cur, index) => {
      pre.push(`${index}`)
      return pre
    }, [])
  }
}
</script>

<style lang="scss" scoped>
.his-detail-box{
  background-color: #f2f5f9;
  height: 100%;
  width: 100%;
  position: absolute;
}
.his-detail{
  height: 45px;
  color: #0A0A0A;
  padding-left: 18px;
  font-size: 17px;
  font-weight: 400;
  line-height: 45px; /* 141.176% */
  background-color: #fff;
  text-align: left;
}
 .cart-box {
    .status {
      width: 49px;
      height: 21px;
      position: absolute;
      text-align: center;
      top: 0;
      right: 0;
      padding: 2px 6px;
      align-items: flex-start;
      border-radius: 0px 8px;
      background-color: #07c160;
      color: #fff;
    }
    .stop {
      @extend .status;
      color: #ffffff;
      background-color: #ccc;
    }

    .detalTitle {
      color: #0a0a0a;
      font-size: 18px;
      width: 270px;
    }
    .title {
      color: #0a0a0a;
      font-size: 18px;
      width: 270px;
      word-break: break-all;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1; /* 超出几行省略 */
      overflow: hidden;
    }
    .detailSpec {
      color: #878f99;
      font-size: 14px;
      width: 270px;
    }
    .spec {
      color: #878f99;
      font-size: 14px;
      width: 270px;
      word-break: break-all;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1; /* 超出几行省略 */
      overflow: hidden;
    }
  }
 .content-box {
    background: linear-gradient(180deg, #deecff 0%, #fcfeff 100%);
    border-radius: 6px;
    padding-bottom: 14px;
    .info {
      text-align: left;
      padding: 11px 15px;
      // margin-top:13px;

      p {
        color: #5a6266;
        margin-bottom: 8px;
      }
      .dose {
        word-break: break-all;
        margin-bottom: 8px;
      }
    }
  }
  ::v-deep .van-collapse-item__content {
      padding: 0px 15px 14px 15px;
      border-radius: 8px;
      color: #5a6266;
    }
  ::v-deep .van-collapse{
    margin-bottom: 12px;
  }
  .info-list{
    padding: 16px;
  }
</style>