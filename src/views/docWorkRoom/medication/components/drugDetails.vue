<template>
  <div class="details">
    <van-loading v-if="loading" style="margin-top: 200px;background-color: #fff;"/>
    <template v-if="!loading">
      <drugList :propsActiveName="this.propsActiveName" :infoList="this.thisInfoDetails"/>
      <div class="records-box">
        <van-sticky>
            <div class="ceiling">
              <h3 class="title">变更记录</h3>
            </div>
        </van-sticky>
        <div class="drug-box">
          <van-collapse v-for="(item, index) in thisIsHistory" v-model="activeName" :border="false" :key="index">
            <van-collapse-item :title="item.year + '年'" :name="index">
              <div class="info" v-for="(childItem, childKey) in item.option" :key="childKey">
                <div class="info-status" @click="onViewHistory(childItem)">
                  <p>{{ ['新增用药', '变更用药', '停止用药', '重新用药'][childItem.handle_type - 1] }}</p>
                  <span v-if="childItem.isAlterData">
                      <p v-if="childItem.isAlterData.includes('drug_id')">药品名称:{{ childItem.generic_name }}{{ childItem.package }}</p>
                      <p v-if="childItem.isAlterData.includes('medication_status')" >用药状态:
                      {{ childItem.medication_status == 1 ? '使用中' : '已停用' }}
                    </p>
                      <p v-if="childItem.isAlterData.includes('start_time')" label="用药开始日期">用药开始日期:{{ childItem.start_time ? childItem.start_time.split(' ')[0] : '' }}</p>
                      <p v-if="childItem.isAlterData.includes('end_time') && childItem.end_time" label="用药结束日期">用药结束日期:{{ childItem.end_time ? childItem.end_time.split(' ')[0] : '' }}</p>
                      <p v-if="childItem.isAlterData.includes('change_time') && childItem.handle_type == 2">变更日期:{{ childItem.change_time ? childItem.change_time.split(' ')[0] : '' }}</p>
                      <p v-if="childItem.isAlterData.includes('route_of_administration')">给药途径:{{ childItem.route_of_administration.join() }}</p>
                      <p v-if="childItem.isAlterData.includes('dose_frequency')">用药频次:{{ childItem.dose_frequency_name }}</p>
                      <p v-if="childItem.isAlterData.includes('drug_dosage')">
                        <span>用药剂量:</span>
                        <span v-for="(item, index) in childItem.drug_dosage" :key="index">
                          <span v-if="item.type == 1">
                            <span v-if="item.dosage">{{ item.dosage }}</span>
                            <span v-if="item.unit">{{ item.unit }}/次</span>
                          </span>
                            <span v-if="item.type == 2">
                        <span v-if="item.time_name != '自定义时间' && item.time_name != '其他'">
                          <template v-if="item.time_name" >
                            <span>{{ item.time_name }}:{{ item.dosage }}{{ item.unit }};</span>
                          </template>
                        </span
                      >
                      <span v-if="item.time_name == '自定义时间' || item.time_name == '其他'"
                        >
                            <template v-if="item.time_name && item.time">
                              <span>{{ item.time }}:{{ item.dosage }}{{ item.unit }};</span>
                            </template>
                        </span
                      >
                    </span>
                      </span>
                      </p>
                      <p v-if="childItem.isAlterData.includes('compliance_type')">依从性:{{ useDrugDependent[childItem.compliance_type] }}</p>
                        <p v-if="childItem.isAlterData.includes('note') || childItem.isAlterData.includes('advice')">备注:
                          <span>{{ childItem.advice.split(',').map(index => ['餐前', '随餐', '餐后'][index - 1]).join(',') }}</span>
                          <span>{{ childItem.note }}</span>
                        </p>
                    </span>
                </div>
                <div class="info-operator">
                  <span>{{ childItem.change_time ? childItem.change_time.split(' ')[0] : '' }}</span>
                  <span>{{ childItem.operator_name }}</span>
                </div>
              </div>
            </van-collapse-item>
          </van-collapse>
          <div class="foot" v-if="channel == 'doctor' && scene == 'hosp'">
            <van-button class="cancel" type="default" @click="listDel()">删除</van-button>
            <van-button v-if="handle_type != 3" class="alter" type="info" @click="operateBtn(2)">变更用药</van-button>
            <van-button v-if="handle_type == 3"  class="verify" type="info" @click="operateBtn(4)">重新用药</van-button>
          </div>
          <div class="foot" v-if="channel == 'patient' && wx_changeActive == 1">
            <van-button class="cancel" type="default" @click="listDel()">删除</van-button>
            <van-button v-if="handle_type != 3" class="alter" type="info" @click="operateBtn(2)">变更用药</van-button>
            <van-button v-if="handle_type == 3"  class="verify" type="info" @click="operateBtn(4)">重新用药</van-button>
          </div>
        </div>

      </div>
    </template>
    
    <van-popup position="bottom" round :style="{ height: '80%' }" v-model="show">
      <div class="popup-box" v-if="this.popupData">
        <div class="popupTitle">
          <div class="showName">
            <span class="title">{{ drugNameData(this.popupData) }}</span>
            <span class="showUse" v-if="this.popupData.medication_status == 1">使用中</span>
            <span class="showStop" v-else>已停用</span>
          </div>
          <van-icon name="clear"  @click="clearPopup"/>
        </div>
        <span class="strength" v-if="this.popupData.strength">{{ this.popupData.strength }}</span>
        <div class="content">
          <p>{{ this.popupData.start_time ? this.popupData.start_time.split(' ')[0] : ''}}至{{ this.popupData.end_time ? this.popupData.end_time.split(' ')[0] : '今' }}</p>
          <p v-if="this.popupData.handle_type == 2">变更日期:{{ this.popupData.change_time ? this.popupData.change_time.split(' ')[0] : '' }}</p>
          <p v-if="this.popupData.route_of_administration.length">
            {{ this.popupData.route_of_administration.length ? this.popupData.route_of_administration.join() : "" }}
          </p>
          <p>{{ this.popupData.dose_frequency_name }}</p>
          <p v-if="this.popupData.drug_dosage.length" class="dose">
            <span v-for="(child, index) in this.popupData.drug_dosage" :key="index">
              <span v-if="child.type == 1">
                <span v-if="!child.dosage && !child.unit"></span>
                <span v-if="child.dosage">{{ child.dosage }}</span>
                <span v-if="child.unit">{{ child.unit }}/次</span>
              </span>
              <span v-if="child.type == 2">
                <span v-if="child.time_name != '自定义时间' &&child.time_name != '其他'">
                  <template v-if="child.time_name">
                    <span>{{ child.time_name }}:{{ child.dosage}}{{ child.unit }};</span>
                  </template>
                </span>
                <span v-if="child.time_name == '自定义时间' ||child.time_name == '其他'">
                  <template v-if="child.time">
                    <span>{{ child.time }}:{{ child.dosage}}{{ child.unit }};</span>
                  </template>
                </span>
              </span>
            </span>
          </p>
          <p>{{ useDrugDependent[this.popupData.compliance_type] }}</p>
          <p>
            <span v-if="this.popupData.advice.length">
              {{
                this.popupData.advice
                  .split(",")
                  .map((index) => ["餐前", "随餐", "餐后"][index - 1])
                  .join(",")
              }};
            </span>
            <span>{{ this.popupData.note }}</span>
          </p>
        </div>
        <div class="historyInfo">
          <p>
            <span>日期:</span>
            <span style="color:#5A6266">{{ this.popupData.updated_at ? this.popupData.updated_at.split(' ')[0] : '' }}</span>
          </p>
          <p>
            <span>操作人:</span>
            <span style="color:#5A6266">{{ this.popupData.operator_name }}</span>
          </p>
        </div>
      </div>
      <div class="foot" style="background-color: #F2F5F9;" v-show="channel == 'doctor' && scene =='hosp'" >
        <van-button style="background-color: #FFF;" @click="onRecordsDel" v-if="isOneData" class="cancel" type="default">删除</van-button>
        <van-button :class="isOneData ? 'alter' : 'noOneAlter'" type="info" @click="onRecordsEdit">编辑</van-button>
      </div>
      <div class="foot" style="background-color: #F2F5F9;"  v-show="channel == 'patient' && wx_changeActive == 1">
          <van-button style="background-color: #FFF;" @click="onRecordsDel" v-if="isOneData" class="cancel" type="default">删除</van-button>
          <van-button :class="isOneData?'alter':'noOneAlter'" type="info" @click="onRecordsEdit">编辑</van-button>
        </div>
    </van-popup>
  </div>
</template>

<script>
const UA = navigator.userAgent
const isAndroid = UA.indexOf('Android') > -1 || UA.indexOf('Adr') > -1 // android终端
const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
import drugList from '../drugList.vue'
import { getPatientRecordList , getPatientDrugDelete , getPatientDrugRecordDelete } from '@/api/docWorkRoom.js'
import { Toast, Dialog } from "vant";
export default {
  components: {
    drugList
  },
  data() {
    return {
      loading:false,
      container: null,
      thisInfoDetails: [],
      thisIsHistory: [],
      popupData:null,
      propsActiveName: '',
      activeName: [],
      isOneData:false,
      show: false,
      handle_type: null,
      scene: '',
      channel: '',
      patient_id: '',
      medicationTabs: [],
      wx_changeActive:0,
      useDrugDependent: {
        1: "规律服药",
        2: "间断服药",
        3: "不服药",
      },
    }
  },
  methods: {
    //区分出年
    historyData(data) {
      if(!data.length) return data
      return data.reduce((result, item) => {
        const year = item.change_time.substring(0, 4);
        const existingYear = result.find((entry) => entry.year === year);
        if (existingYear) {
          existingYear.option.push(item);
        } else {
          result.push({ year, option: [item] });
        }
        return result;
      }, []);
    },
    //处理字段标红,过滤id字段。
    findAndAddDifferentKeys(data) {
      console.log(data)
      const filteredData = data.map((item) => ({
        ...item,
        drug_dosage: item.drug_dosage.map((dosageItem) => ({
          time_name: dosageItem.time_name,
          time: dosageItem.time,
          dosage: dosageItem.dosage,
          unit: dosageItem.unit,
          type: dosageItem.type,
        })),
      }));
      for (let i = 1; i < data.length; i++) {
        const previousObj = filteredData[i - 1];
        const currentObj = filteredData[i];
        const differentKeys = [];
        for (const key in previousObj) {
          if (key == 'drug_dosage') {
            const preDrugDosage = previousObj[key].filter(({ time_name, time, dosage, unit }) => {
              // && !time && !dosage && !unit 如果需要判断单位和剂量就将下面的if换成以下代码。
              if (time_name === '自定义时间' && !time) {
                return false;
              }
              if (time_name === '其他' && !time) {
                return false;
              }
              return true;
            });
            const curDrugDosage = currentObj[key].filter(({ time_name, time, dosage, unit }) => {
              if (time_name === '自定义时间' && !time) {
                return false;
              }
              if (time_name === '其他' && !time) {
                return false;
              }
              return true;
            });
            if (preDrugDosage.length !== curDrugDosage.length) {
              differentKeys.push(key);
            }
            else {
              curDrugDosage.forEach((cur, index) => {
                if (
                  cur.time_name === preDrugDosage[index].time_name
                  && cur.dosage === preDrugDosage[index].dosage
                  && cur.unit === preDrugDosage[index].unit
                ) {
                  return;
                }
                differentKeys.push(key);
              })
            }
          } else if (key == 'route_of_administration') {
            if (previousObj[key].join(',') !== currentObj[key].join(',')) {
              differentKeys.push(key);
            }
          }
          // else if (key == 'advice') {
          //   if (previousObj[key].join(',') !== currentObj[key].join(',')) {
          //     differentKeys.push(key);
          //   }
          // }
          else if (previousObj[key] !== currentObj[key]) {
            differentKeys.push(key);
          }
        }
        if (differentKeys.length > 0) {
          previousObj['isAlterData'] = differentKeys;
        }
      }
      return filteredData;
    },
    drugNameData(item) {
      const { generic_name, trade_brand_name, company_name } = item;
      const drug_name = trade_brand_name || company_name;
      return `${generic_name}${drug_name ? `(${drug_name})` : ""}`;
    },
    onViewHistory(item) {
      if (item.id == this.thisIsHistory[0].option[0].id && item.handle_type !==1) {
        this.isOneData = true
      }else{
        this.isOneData = false
      }
      this.popupData = item
      this.show = true
    },
    fnDialog(text,api,id,type) {
      Dialog.confirm({
        message: text,
        confirmButtonText: '确定',
        confirmButtonColor: '#38F',
        width: '299px',
        className: 'dialog'
      })
        .then(() => {
          api({ ...id }).then((res) => {
            if (res.status == 200) {
              if (type == 'list') {
                let param = `/docWorkRoom/medication/index?user_id=${this.user_id}`;
                if (process.env.NODE_ENV === "production" && this.isApp) {
                  if (isAndroid) {
                    window.android.breathMessage(encodeURI(param));
                  }
                  if (isIOS) {
                    window.webkit.messageHandlers.breathMessage.postMessage(encodeURI(param));
                  }
                } else {
                  this.$router.replace({
                    path: param
                  })
                }
                // this.$router.push({
                //   path: '/docWorkRoom/medication/index',
                //   query: {
                //     patient_id: localStorage.getItem('patient_id'),
                //     channel: localStorage.getItem('channel'),
                //     scene: localStorage.getItem('scene'),
                //     medicationTabs:localStorage.getItem('medicationTabs')
                //   }
                // })
              }
              if (type == 'history') {
                this.getRecord()
                this.show = false
              }
            } else {
               this.$toast(res.msg);
            }
          })
        }).catch((err) => {
          // this.$toast(err.msg);
        })
        .catch(() => {
          // on cancel
        });
    },
    listDel() {
      let item = this.thisIsHistory[0].option[0]
      this.fnDialog('确定要删除这详情数据？删除后不可恢复，请谨慎操作。', getPatientDrugDelete, {
        id: item.drug_list_id
      },
      'list'
      )
    },
    operateBtn(val) {
      let item = this.thisIsHistory[0].option[0]
      let param = `/docWorkRoom/medication/addMedication?editType=change&drugListId=${item.drug_list_id}&recordId=${item.id}&editStatus=${val}`
      // this.$router.push({
      //   path: param
      // })
      location.assign(param)
      // if (process.env.NODE_ENV === "production" && this.isApp) {
      //   if (isAndroid) {
      //     window.android.breathMessage(encodeURI(param));
      //   }
      //   if (isIOS) {
      //     window.webkit.messageHandlers.breathMessage.postMessage(encodeURI(param));
      //   }
      // } else {
      //   this.$router.replace({
      //     path: param
      //   })
      // }
      localStorage.removeItem('formData')
      localStorage.removeItem('editPageConfig')
    },
    onRecordsDel() {
      this.fnDialog('确定要删除这条变更记录吗？删除后不可恢复，请谨慎操作。', getPatientDrugRecordDelete,
        {
          drug_list_id: this.popupData.drug_list_id,
          id: this.popupData.id
        },
        'history'
      )
    },
    onRecordsEdit() {
      let param = `/docWorkRoom/medication/addMedication?&editType=edit&drugListId=${this.popupData.drug_list_id}&recordId=${this.popupData.id}&editStatus=${this.popupData.handle_type}`
      // this.$router.push({
      //   path: param
      // })
      location.assign(param)
      // if (process.env.NODE_ENV === "production" && this.isApp) {
      //   if (isAndroid) {
      //     window.android.breathMessage(encodeURI(param));
      //   }
      //   if (isIOS) {
      //     window.webkit.messageHandlers.breathMessage.postMessage(encodeURI(param));
      //   }
      // } else {
      //   this.$router.replace({
      //     path: param
      //   })
      // }
      localStorage.removeItem('formData')
      localStorage.removeItem('editPageConfig')
    },
    clearPopup() {
      this.show = false
    },
    getRecord() {
      this.loading = true
      let ext ={}
      if (localStorage.getItem('wx_room_id')) {
        ext={room_id: localStorage.getItem('wx_room_id') }
      }
      getPatientRecordList({
        drug_list_id: this.$route.query.id,
        page_size: 99999,
        ext: ext
      }).then((res) => {
          this.loading = false
        if (res.status == 200) {
          const { data } = res
          this.thisInfoDetails = [data.data[0]]
          this.handle_type = data.data[0].handle_type
          this.propsActiveName = '0'
          const handleHistoryData = this.historyData(this.findAndAddDifferentKeys(data.data))
          this.thisIsHistory = handleHistoryData
          console.log(this.thisIsHistory)
          this.activeName = handleHistoryData.reduce((pre, cur, index) => {
            pre.push(index)
            return pre
          }, [])
        }
      }).catch(() => {
        this.loading = false
      })
    }
  },
  
  computed: {
    isApp() {
      return this.$store.getters['docWorkRoom/isApp']
    }
  },
  created() {
    this.scene = localStorage.getItem('scene') || ''
    this.channel = localStorage.getItem('channel') || ''
    this.patient_id = localStorage.getItem('patient_id') || ''
    this.wx_changeActive = localStorage.getItem('wx_changeActive') || ''
    this.user_id = localStorage.getItem('user_id') || ''
    this.medicationTabs = localStorage.getItem('medicationTabs') || []
    this.getRecord()
  }
}
</script>

<style lang="scss" scoped>
$app-color: #F7830D;
$wx-color: #3388FF;
$app-bg-color: #F8F8F8;
$wx-bg-color: #F5F7FB;
.details{
  background-color: #f2f5f9;
}
.records-box{
  text-align: center;
  // padding: 16px;
  // position: absolute;
  padding-bottom: 85px;
  background-color: #fff;
  .ceiling{
    // width: 100%;
    display: flex;
    align-items: center;
    height: 60px;
    padding: 0px 16px 0px 16px;
    background-color: #fff;
  }
  .title{
    text-align: left;
    color: #0A0A0A;
    font-size: 17px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    width: 90%;
  }
  .drug-box{
    .info{
      width: 311px;
      min-height: 50px;
      border-radius: 6px;
      background-color: #F5FAFF;
      padding: 10px 16px;
      margin-bottom: 12px;
      .info-status{
        text-align: left;
        min-height: 24px;
        line-height: 24px;
        margin-bottom: 3px;
        p{
          color:#0A0A0A;
          font-size: 17px;
          margin-bottom:3px;
          word-break: break-all;
        }
        span{
          font-size: 17px;
          color:#0A0A0A;
        }
      }
      .info-operator{
        height: 24px;
        line-height: 24px;
        display: flex;
        justify-content: space-between;
      }
    }
    ::v-deep .van-collapse .van-cell{
      min-height: 45px !important;
    }
    ::v-deep .van-collapse-item__title{
      padding: 0px 16px 0px 16px !important;
    }
  }
  
  ::v-deep .van-collapse .van-cell__title{
    flex:none;
  }
  ::v-deep .van-collapse .van-cell__right-icon{
    line-height:22px !important;
  }
  ::v-deep .van-cell__title{
    color: #0A0A0A;
    font-size: 17px;
    line-height: 24px
  }
}
::v-deep .van-collapse-item__content{
  padding-bottom: 15px;
}
::v-deep .van-popup{
  background-color: #F2F5F9;
}
.popup-box{
  padding: 20px 15px 85px 15px;
  display: flex;
  flex-direction: column;

  .popupTitle{
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    .showName{
      width: 90%;
      color:#0A0A0A;
      font-size: 18px;
      // height: 25px;
      display: flex;
      align-items: flex-start;
      .title{
        width: 90%;
        text-align: left;
        font-weight: 500;
        line-height:24px;
      }
      .showStop{
        width: 49px;
        height: 21px;
        line-height: 22px;
        align-items: flex-start;
        border-radius: 8px;
        background-color: #CCC;
        color: #FFF;
        font-size: 12px;
        font-weight: 500;
        letter-spacing: 0.333px;
        margin-left: 8px;
      }
      .showUse{
        width: 49px;
        height: 22px;
        line-height: 22px;
        align-items: flex-start;
        border-radius: 8px;
        background-color: #07C160;
        color: #FFF;
        font-size: 12px;
        font-weight: 500;
        letter-spacing: 0.333px;
        margin-left: 8px;
      }
      
    }
    ::v-deep .van-icon-clear{
      font-size: 18px;
    }
  }
  .strength{
    text-align: left;
    color: #878F99;
    font-size: 14px;
    font-weight: 400;
    margin-top: 6px;
    display: inline-block;
    line-height:22px;
  }
  .dose{
    word-break: break-all;
  }
  .content{
    width: 310px;
    // min-height: 103px;
    border-radius: 6px;
    background-color: #fff;
    margin-top:13px;
    color:#5A6266;
    font-size: 14px;
    text-align: left;
    padding: 12px 15px;
    p{
      // margin-bottom:8px;
      line-height: 20px;
      color:#5A6266;
      font-size: 14px;
    }
    // p:nth-last-child(1){
    //   margin-bottom:0px;
    // }
  }
  .historyInfo{
    margin-top: 11px;
    color:#878F99;
    font-size: 12px;
    line-height: 25px;
    text-align: left;
  }
}
.foot{
    position: fixed;
    display: flex;
    align-items: center;
    justify-content: space-around;
    left: 0;
    bottom: 0;
    height: 85px;
    width: 100%;
    background-color: #fff;
  .cancel{
    background-color: #F5F6FB;
    color:#0A0A0A;
    border:none;
    width:162px;
  }
  .alter{
    width:162px;
    color: #FFF;
    font-size: 16px;
    font-weight: 500;
  }
  .noOneAlter{
    width:80%;
    color: #FFF;
    font-size: 16px;
    font-weight: 500;
  }
  .verify{
    width:162px;
  }
  ::v-deep .van-button{
    border-radius: 8px
  }
}
.dialog{
  .van-dialog__content{
      ::v-deep .van-dialog__message{
      color: #363636;
      text-align: center;
      font-size: 17px !important;
      font-weight: 400;
      line-height: 28px;
    }
  }
}

</style>