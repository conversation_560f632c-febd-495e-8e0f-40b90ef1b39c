<template>
  <div >
    <div class="history" v-for="(item, index) in historicalData" :key="index">
      <span class="title">{{ item.medication }}</span>
      <span class="name">{{ item.medication_begin_at }}</span>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      historicalData:[]
    }
  },
  mounted() {
    this.historicalData = JSON.parse(localStorage.getItem('historicalData') || [])
  },
  methods: {
    
  }
}
</script>

<style lang="scss" scoped>
.history{
  height: 52px;
  line-height: 52px;
  background-color: #fff;
  display: flex;
  justify-content: space-between;
  padding: 0px 20px;
  align-items: center;
  .title{
    color: #0A0A0A;
    font-size: 17px;
    line-height: 24px; 
  }
  .name{
    color:#5A6266;
    font-size: 15px;
  }
}
</style>