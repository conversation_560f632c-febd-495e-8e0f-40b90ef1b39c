<template>
  <div class="addMedication">
    <div class="cellBar" :style="{'padding':(JSON.stringify(drugObj) == '{}' || drugObj == null)?'':'14px 0 12px'}">
        <div class="label">
            <div class="redDot">*</div>
            <div>药品名称</div>
            <img v-if="editType != 'edit' && editStatus != 4" class="scanIcon" @click="scanBarCode" src="../imgs/scanIcon.png" alt="">
        </div>
        <div class="rightVal" @click="goSearchDrug">
            <div class="placeHolder" v-if="JSON.stringify(drugObj) == '{}' || drugObj == null">请选择药品</div>
            <div style="line-height: 25px;margin-top: -4px;" :class="[isPageEdit?'disabled':'val']" v-else>{{drugObj.drug_name + ' ' + (drugObj.strength || '') + ' ' + (drugObj.package || '') + ' ' + (drugObj.approval_number?drugObj.approval_number : '')}}</div>
            <van-icon name="arrow" color="#CCCCCC" />
        </div>
    </div>
    <div class="cellBar" style="padding: 7px 0;">
        <div class="label" style="align-items: center;">
            <div class="redDot">*</div>
            <div>用药状态</div>
        </div>
        <div class="rightVal" :class="[isPageEdit ? 'rightDisabled' : '']">
            <div class="item" v-for="(item,index) in medicationStatusArr" :class="[item.id == dataForm.medication_status?'toggleSelect':'']" @click="toggleItem('medication_status',item)">{{item.val}}</div>
        </div>
    </div>
    <!-- <div class="cellBar" v-if="dataForm.medication_status == 1">
        <div class="label">
            <div class="redDot">*</div>
            <div>用药日期</div>
        </div>
        <div class="rightVal" @click="selectSingleDate('start_time')">
            <div class="placeHolder" v-if="start_time == ''">请选择用药日期</div>
            <div :class="[startTimeCanEdit?'val':'disabled']" v-else>{{start_time}}</div>
            <van-icon name="arrow" color="#CCCCCC" />
        </div>
    </div> -->
    <div class="cellBar" v-if="isMedicationStartDateShow">
        <div class="label">
            <div class="redDot">*</div>
            <!-- <div>{{dataForm.medication_status == 2 ? '用药开始日期' : '用药日期'}}</div> -->
            <div>用药开始日期</div>
        </div>
        <div class="rightVal" @click="selectSingleDate('start_time')">
            <div class="placeHolder" v-if="start_time == ''">请选择日期</div>
            <div :class="[startTimeCanEdit?'val':'disabled']" v-else>{{start_time}}</div>
            <van-icon name="arrow" color="#CCCCCC" />
        </div>
    </div>
    <div class="cellBar" v-if="isMedicationEndDateShow">
        <div class="label">
            <div class="redDot">*</div>
            <div>用药结束日期</div>
        </div>
        <div class="rightVal" @click="selectSingleDate('end_time')">
            <div class="placeHolder" v-if="end_time == ''">请选择日期</div>
            <div :class="[endTimeCanEdit?'val':'disabled']" v-else>{{end_time}}</div>
            <van-icon name="arrow" color="#CCCCCC" />
        </div>
    </div>
    <div class="cellBar" v-if="isChangeDateShow">
        <div class="label">
            <div class="redDot">*</div>
            <div>变更日期</div>
        </div>
        <div class="rightVal" @click="selectSingleDate('change_time')">
            <div class="placeHolder" v-if="change_time == ''">请选择变更日期</div>
            <div :class="[changeTimeCanEdit?'val':'disabled']" v-else>{{change_time}}</div>
            <van-icon name="arrow" color="#CCCCCC" />
        </div>
    </div>
    <div class="cellBar" :style="{'padding':dataForm.route_of_administration.length > 0?'14px 0 12px':''}">
        <div class="label">
            <div class="redDot"></div>
            <div>给药途径</div>
        </div>
        <div class="rightVal" @click="isRoutePickShow = true">
            <div class="placeHolder" v-if='dataForm.route_of_administration.length == 0'>请选择给药途径</div>
            <div style="line-height: 25px;margin-top: -4px;" class="val" v-else>{{dataForm.route_of_administration.join('，')}}</div>
            <van-icon name="arrow" color="#CCCCCC" />
        </div>
    </div>
    <div class="cellBar">
        <div class="label">
            <div class="redDot"></div>
            <div>用药频次</div>
        </div>
        <div class="rightVal" @click="goSelectFrequence">
            <div class="placeHolder" v-if="JSON.stringify(frequenceObj) == '{}' || frequenceObj == null">请选择用药频次</div>
            <div class="val" v-else>{{frequenceObj.name}}</div>
            <van-icon name="arrow" color="#CCCCCC" />
        </div>
    </div>
    <div class="cellBar">
        <div class="label">
            <div class="redDot"></div>
            <div>用药剂量</div>
        </div>
        <div class="rightVal" @click="goSelectDosage">
            <div class="placeHolder" v-if="dosageSelectedId == null">请选择用药剂量</div>
            <div class="val" v-else>{{dosageSelectedId == 1?'统一每次用量':'自定义每次用量'}}</div>
            <van-icon name="arrow" color="#CCCCCC" />
        </div>
    </div>
    <div class="cellBar" v-if="dosageSelectedId == 1 && dataForm.drug_dosage.length > 0 && dataForm.drug_dosage[0].dosage">
        <div class="label">
            <div class="redDot"></div>
            <div></div>
        </div>
        <div class="rightVal" @click="goSelectDosage">
            <div class="val">{{ (dataForm.drug_dosage[0].dosage || '') + (dataForm.drug_dosage[0].unit || '') }}</div>
            <van-icon name="arrow" color="#CCCCCC" />
        </div>
    </div>
    <div class="customTimeDosage" v-if="dosageSelectedId == 2 && dataForm.drug_dosage.length > 0">
        <div class="inner" @click="goSelectDosage">
            <div class="customItem" v-for="item in dataForm.drug_dosage">
                <span class="val" v-html="(item.time || item.time_name) + '，' + (item.dosage || '') + (item.unit || '') "></span>
                <!-- <span v-if="item.time_name">{{item.time_name}}，{{item.dosage + item.unit}}</span> -->
                <van-icon name="arrow" color="#CCCCCC" />
            </div>
        </div>
    </div>
    <div class="cellBar">
        <div class="label">
            <div class="redDot"></div>
            <div>依从性</div>
        </div>
        <div class="rightVal" @click="isPickerShow = true">
            <div class="placeHolder" v-if="compliance == ''">请选择依从性</div>
            <div class="val" v-else>{{compliance}}</div>
            <van-icon name="arrow" color="#CCCCCC" />
        </div>
    </div>
    <div class="cellBar noBorder" style="padding: 7px 0;">
        <div class="label" style="align-items: center;min-width: 60px;">
            <div class="redDot"></div>
            <div>备注</div>
        </div>
        <div class="rightVal">
            <div class="item" v-for="(item,index) in notesArr" :class="[item.selected?'toggleSelect':'']" @click="toggleItem('advice',item)">{{item.val}}</div>
        </div>
    </div>

    <div class="btnOuter" @click="saveData">
        <div class="btn">保存</div>
    </div>

    <van-field
        class="textareaField"
        v-model="dataForm.note"
        rows="5"
        :autosize='{ maxHeight: 100, minHeight: 100 }'
        type="textarea"
        maxlength="1000"
        placeholder="请输入留言"
        show-word-limit
        />

    <!-- 用药日期 多选日历 -->
    <!-- <van-calendar
      class="calendarCustomClass"
      :color="platformColor"
      v-model="isCalendarShow"
      type="range"
      :default-date="defaultDate"
      :max-range="365"
      :min-date="minDate"
      :max-date="maxDate"
      :allow-same-day="true"
      @confirm="calendarConfirm"></van-calendar> -->

      <!-- 单选日历 -->
    <van-calendar
      class="calendarCustomClass"
      color="#3388FF"
      :default-date="defaultDate"
      v-model="isSingleCalendarShow"
      :show-confirm="false"
      type="single"
      :max-range="365"
      :min-date="minDate"
      :max-date="maxDate"
      :allow-same-day="true"
      @confirm="singleCalendarConfirm"></van-calendar>
    
    <!-- 依从性 选择框 -->
    <van-popup v-model="isPickerShow" round position="bottom">
        <van-picker
          title="选择依从性"
          show-toolbar
          value-key="val"
          :columns="compliancePickerArr"
          @cancel="isPickerShow = false"
          @confirm="pickConfirm"
        />
    </van-popup>
    <!-- 用药途径 多选框 />-->
    <van-popup v-model="isRoutePickShow" round position="bottom" :style="{ 'max-height': '400px','overflow': 'hidden' }">
        <div class="medicationRoutePop">
            <div class="toolBar">
                <span class="cancel" @click="routePickerCancel">取消</span>
                <span class="title">给药途径</span>
                <span class="confirm" @click="routePickerConfirm">确定</span>
            </div>
            <van-checkbox-group v-model="tempRouteCheckedArr" @change="selectRoute">
                <div class="routeList">
                    <div class="routeItem" v-for="(item,index) in medicationRoutePickerArr" @click="toggleRouteItem(index)">
                        <div>{{item.name}}</div>
                        <van-checkbox :name="item.name" ref="checkboxes" />
                    </div>
                </div>
            </van-checkbox-group>
        </div>
    </van-popup>
    <!-- 扫描条码 药品与当前药品不一致时 提示框 -->
    <van-dialog v-model="isScanDrugInconsistentShow" :close-on-click-overlay="true" title="是否更换药品信息？" show-cancel-button confirm-button-text="确定更换" confirm-button-color="#3388FF" @confirm="scanPopConfirm" @cancel="isScanDrugInconsistentShow = false">
        <template #default>
            <div class="dialogOuter">
                <div class="line">
                    <div class="label">更换前：</div>
                    <div class="val" v-if="drugObj">
                        <span>{{drugObj.package || ''}}</span>
                        <span>{{drugObj.approval_number || ''}}</span>
                    </div>
                </div>
                <div class="line">
                    <div class="label">更换后：</div>
                    <div class="val">
                        <span>{{scanResdrug.package || ''}}</span>
                        <span>{{scanResdrug.approval_number || ''}}</span>
                    </div>
                </div>
            </div>
        </template>
    </van-dialog>
  </div>
</template>

<script>
const UA = navigator.userAgent;
const isAndroid = UA.indexOf("Android") > -1 || UA.indexOf("Adr") > -1; // android终端
const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); // ios终端
import moment from 'moment'
import {Dialog,Toast} from 'vant'
import { getMedicationConfig,addMedication,getDrugListByBarCode,getChangeList,addMedicationRecord,updateRecord } from '@/api/docWorkRoom.js'
import wx from "weixin-js-sdk";
import axios from 'axios'
import { jsToClient } from '@/utils/bfBridge'
export default {
    data(){
        return {
            dataForm: {
                "patient_id": "",
                "drug_id": "",
                "medication_status": 1,  //用药状态：1：用药中  2：已停用
                "start_time":"",
                "end_time":"",
                "dose_frequency": "",
                "compliance_type": "", //依从性 1规律服药 2间断服药 3不服药
                "advice": "",  //1餐前 2随餐  3餐后
                "note": "",
                "change_time": "",
                "source": "",  //类型 1治疗方案 2His记录 3家庭记录
                "handle_type": 1,  //1新增 2 变更 3停药 4重新用药
                "route_of_administration": [],
                "drug_dosage": []
            },
            tempRouteCheckedArr: [],
            scanResdrug: {},  //扫码得到的药品
            drugObj: null,  //药品 页面显示
            // medicationRouteStr: '',  //给药途径 页面显示字段
            compliance: '',  //依从性 页面显示字段
            frequenceObj: {},  //用药频次 
            dosageSelectedId: null,  //选中的剂量Id  1：统一剂量  2：自定义剂量
            defaultDate: new Date(),  //日历的默认/回填日期

            notesArr: [{id: 1,val:'餐前',selected:false}, {id:2,val:'随餐',selected:false}, {id:3,val:'餐后',selected:false}],
            medicationStatusArr: [{id: 1,val:'使用中'}, {id:2,val:'已停药'}],
            compliancePickerArr: [{id: 1,val:'规律用药'}, {id:2,val:'间断用药'}, {id:3,val:'未用药'}],
            medicationRoutePickerArr: [],  //给药途径 弹窗数据
            minDate: new Date(),
            maxDate: new Date(),
            // isCalendarShow: false,  //是否显示日历
            isPickerShow: false,  //是否显示选择框
            isRoutePickShow: false,  //给药途径多选框是否显示

            isSingleCalendarShow: false,  // 单选日历是否显示
            isMedicationStartDateShow: true,  // 用药开始日期是否显示
            isMedicationEndDateShow: false,  // 用药结束日期
            isChangeDateShow: false,  // 变更日期
            start_time: '',  //用药开始日期
            end_time: '',  //用药结束日期
            change_time: '',  //变更日期
            curDateLine: '',  // 单选用药日期/用药开始日期/结束日期/变更日期
            // 日期是否可编辑
            startTimeCanEdit: true,  
            endTimeCanEdit: true,
            changeTimeCanEdit: true,

            isScanDrugInconsistentShow: false,  //扫描出来的药品与当前药品不一致时提示

            isPageEdit: false,  //是否编辑
            editType: '',  //编辑类型 change 变更 / edit 编辑
            changeRecordList: [],  //变更记录列表
            drugListId: '',
            editStatus: '',
            patientId: '',
            channel: '',
            medicationTabs: '',
            scene: '',
        }
    },
    computed: {
        isApp() {
            return this.$store.getters['docWorkRoom/isApp']
        }
    },
    watch: {
        $route: {
            handler: function(val, oldVal){
                if (val.query.popType) {
                    this.$router.go(0)
                }
            },
            // 深度观察监听
            deep: true
        }
    },
    methods: {
        // 日历 日期选择
        // calendarConfirm(date) {
        //     const [start, end] = date;
        //     let startDate = moment(start).format('YYYY-MM-DD')
        //     let endDate = moment(end).format('YYYY-MM-DD')
        //     this.medicationDate = startDate + ' 至 ' + endDate
        //     this.isCalendarShow = false

        //     this.dataForm.start_time = `${startDate} 00:00:00`
        //     this.dataForm.end_time = `${endDate} 23:59:59`
        // },
        singleCalendarConfirm(date){
            console.log(date)
            let type = this.curDateLine
            let resDate = moment(date).format('YYYY-MM-DD')
            this[type] = resDate
            // type == 'start_time'? resDate = resDate + ' 00:00:00' : resDate = resDate + ' 23:59:59'
            // if(type == 'start_time') resDate = resDate + ' 00:00:00'
            // if(type == 'end_time') resDate = resDate + ' 23:59:59'
            // if(type == 'change_time') resDate = resDate + ' 00:00:00'
            this.dataForm[type] = resDate
            this.isSingleCalendarShow = false
        },
        selectSingleDate(type){
            if(type == 'start_time'){
                if(!this.isPageEdit){
                    this.end_time?this.maxDate = new Date(this.end_time):''
                    this.maxDate.setDate(this.maxDate.getDate() - 1)
                    this.minDate = new Date(1970, 1, 1)
                }
                if(!this.startTimeCanEdit){
                    this.$toast('用药开始日期不可编辑')
                    return
                }
            }
            if(type == 'end_time'){
                if(!this.isPageEdit){
                    this.start_time?this.minDate = new Date(this.start_time):''
                    this.minDate.setDate(this.minDate.getDate() + 1)
                    const nextTime = moment(new Date()).add('year',1).format("YYYY-MM-DD").split('-')
                    this.maxDate = new Date(nextTime[0]*1, 12, 31)
                }
                if(!this.endTimeCanEdit){
                    this.$toast('用药结束日期不可编辑')
                    return
                }
            }
            if(type == 'change_time' && !this.changeTimeCanEdit){
                this.$toast('用药变更日期不可编辑')
                return
            }
            this.isSingleCalendarShow = true
            this.curDateLine = type
        },
        // 用药状态和备注的点击事件
        toggleItem(type,item){
            if(type == 'medication_status'){
                if(this.isPageEdit){
                    this.$toast('用药状态不可编辑') 
                    return
                }
                this.dataForm[type] = item.id
                if(item.id == 1){
                    this.isMedicationStartDateShow = true
                    this.isMedicationEndDateShow = false
                }else{
                    this.isMedicationStartDateShow = true
                    this.isMedicationEndDateShow = true
                }
            }else{
                item.selected = !item.selected
                let tempArr = []
                this.notesArr.forEach(item=>{
                    if(item.selected){
                        tempArr.push(item.id)
                    }
                })
                this.dataForm[type] = tempArr.join(',')
                console.log(tempArr,this.dataForm[type])
            }
        },
        // 选择依从性
        pickConfirm(e){
            this.compliance = e.val
            this.dataForm.compliance_type = e.id
            this.isPickerShow = false
        },
        toggleRouteItem(index){
            this.$refs.checkboxes[index].toggle()
        },
        // 给药途径 复选框事件
        selectRoute(e){
            console.log(e,this.tempRouteCheckedArr)
            // let str = ''
            // this.dataForm.route_of_administration.forEach((item,index)=>{
            //     this.medicationRoutePickerArr.forEach((innerItem,innerIndex)=>{
            //         if(item == innerItem.id){
            //             index == this.dataForm.route_of_administration.length - 1 ? str = str + innerItem.name : str = str + innerItem.name + '，'
            //         }
            //     })
            // })
            // this.medicationRouteStr = str
            // console.log(e,this.dataForm.route_of_administration)
        },
        // 用药途径弹窗 确认事件
        routePickerConfirm(){
            this.dataForm.route_of_administration = this.tempRouteCheckedArr
            this.isRoutePickShow = false
        },
        // 用药途径弹窗 取消事件
        routePickerCancel(){
            this.tempRouteCheckedArr = this.dataForm.route_of_administration
            this.isRoutePickShow = false
        },
        // 选择频次
        goSelectFrequence(){
            this.storageFun()
            let param = `/docWorkRoom/medication/medicationFrequence`
            // if (process.env.NODE_ENV === "production" && this.isApp) {
            //     if (isAndroid) {
            //         window.android.breathMessage(encodeURI(param));
            //     }
            //     if (isIOS) {
            //         window.webkit.messageHandlers.breathMessage.postMessage(encodeURI(param));
            //     }
            // }else{
                this.$router.push({
                    path: param
                    // path: '/docWorkRoom/medication/medicationFrequence',
                    // query: {
                    //     frequenceObj: JSON.stringify(this.frequenceObj),
                    //     dosageData: JSON.stringify(this.dataForm.drug_dosage)
                    // }
                })
            // }
            
        },
        // 选择剂量
        goSelectDosage(){
            this.storageFun()
            let param = `/docWorkRoom/medication/medicationDosage`;
            // if (process.env.NODE_ENV === "production" && this.isApp) {
            //     if (isAndroid) {
            //         window.android.breathMessage(encodeURI(param));
            //     }
            //     if (isIOS) {
            //         window.webkit.messageHandlers.breathMessage.postMessage(encodeURI(param));
            //     }
            // }else{
                this.$router.push({
                    path: param
                    // path: '/docWorkRoom/medication/medicationDosage',
                    // query: {
                    //     dosageData: JSON.stringify(this.dataForm.drug_dosage),
                    // },
                })
            // }
            
        },
        goSearchDrug(){
            if(this.isPageEdit){
                return
            }
            this.storageFun()
            // this.$router.push({
            //     path: `/docWorkRoom/medication/search?source=${this.dataForm.source}`,
            // })
            location.assign(`/docWorkRoom/medication/search?source=${this.dataForm.source}`)
        },
        // 跳转到其他页面时 存储填过的数据 防止数据丢失
        storageFun(){
            let { drugObj,medicationRouteStr,compliance,frequenceObj,dosageSelectedId,start_time,end_time,change_time,isMedicationStartDateShow,isMedicationEndDateShow,tempRouteCheckedArr } = this
            let obj = {
                drugObj,
                // medicationRouteStr,
                compliance,
                frequenceObj,
                dosageSelectedId,
                start_time,
                end_time,
                change_time,
                isMedicationStartDateShow,
                isMedicationEndDateShow,
                tempRouteCheckedArr,
                dataForm: this.dataForm
            }
            localStorage.setItem('formData', JSON.stringify(obj))
        },
        backfilleStorageDatForm(){
            let queryData = this.$route.query
            let storageData = null
            if(localStorage.getItem('formData')){
                storageData = JSON.parse(localStorage.getItem('formData'))
            }
            let drugObj = null
            if(localStorage.getItem('drugObj')){
                let tempDrugObj = localStorage.getItem('drugObj')
                drugObj = JSON.parse(tempDrugObj)
            }
            console.log('storageData,',storageData)
            if(storageData){
                this.dataForm = storageData.dataForm
                this.drugObj = storageData.drugObj
                this.compliance = storageData.compliance
                this.frequenceObj = storageData.frequenceObj
                this.dosageSelectedId = storageData.dosageSelectedId
                this.start_time = storageData.start_time
                this.end_time = storageData.end_time
                this.change_time = storageData.change_time
                this.isMedicationStartDateShow = storageData.isMedicationStartDateShow
                this.isMedicationEndDateShow = storageData.isMedicationEndDateShow
                this.tempRouteCheckedArr = storageData.tempRouteCheckedArr
                // 备注标签回填
                if(this.dataForm.advice){
                    let tempArr = this.dataForm.advice.split(',')
                    this.notesArr.forEach((item,index)=>{
                        if(tempArr.indexOf(String(item.id)) != -1){
                            this.notesArr[index].selected = true
                        }
                    })
                }
                // 如果没选开始日期 默认当天
                if(!storageData.start_time){
                    let today = moment(new Date()).format('YYYY-MM-DD')
                    this.start_time = today
                    this.dataForm.start_time = today
                }
            }else{
                if(drugObj){
                    // 新增时 选择药品 清空参数并将有参数的字段回填
                    let today = moment(new Date()).format('YYYY-MM-DD')
                    this.start_time = today
                    this.dataForm.start_time = today
                    this.dataForm.end_time = ''
                    this.dataForm.compliance_type = ''
                    this.dataForm.advice = ''
                    this.dataForm.note = ''
                    this.dosageSelectedId = null
                    this.dataForm.drug_dosage = []
                    this.compliance = ''

                    this.drugObj = drugObj
                    this.dataForm.drug_id = this.drugObj.drug_id
                    this.dataForm.drug_sku_id = this.drugObj.drug_sku_id
                    this.dataForm.source = +queryData.source

                    // 途径
                    if(Array.isArray(this.drugObj.route_of_administration) && this.drugObj.route_of_administration.length > 0){
                        this.dataForm.route_of_administration = this.drugObj.route_of_administration
                        this.tempRouteCheckedArr = this.drugObj.route_of_administration
                    }else{
                        this.dataForm.route_of_administration = []
                        this.tempRouteCheckedArr = []
                    }
                    // 频次
                    if(this.drugObj.dose_frequency){
                        this.dataForm.dose_frequency = this.drugObj.dose_frequency
                        this.frequenceObj.id = this.drugObj.dose_frequency
                    }else{
                        this.dataForm.dose_frequency = ''
                        this.frequenceObj = {}
                    }
                    // 剂量
                    if(this.drugObj.dose || this.drugObj.unit){
                        let obj = {
                            "type": 1,
                            "time_name": "",
                            "time": "",
                            "unit": this.drugObj.unit,
                            "dosage": this.drugObj.dose
                        }
                        this.dataForm.drug_dosage.push(obj)
                        this.dosageSelectedId = 1
                    }else{
                        this.dataForm.drug_dosage = []
                        this.dosageSelectedId = null
                    }
                }
            }
        },
        checkData(){
            if(!this.dataForm.drug_id){
                this.$toast('请选择药品')
                return false
            }
            if(!this.dataForm.medication_status){
                this.$toast('请选择用药状态')
                return false
            }
            if(!this.dataForm.start_time && this.startTimeCanEdit && this.isMedicationStartDateShow){
                this.$toast('请选择用药日期')
                return false
            }
            if(!this.dataForm.end_time && this.endTimeCanEdit && this.isMedicationEndDateShow){
                this.$toast('请选择用药结束日期')
                return false
            }
            if(!this.dataForm.change_time && this.changeTimeCanEdit && this.isChangeDateShow){
                this.$toast('请选择变更日期')
                return false
            }
            return true
        },
        saveData(){
            console.log(this.dataForm)
            if(this.dataForm.drug_dosage.length == 0){
                let obj = {
                    "type": 1,
                    "time_name": "",
                    "time": "",
                    "unit": "",
                    "dosage": ""
                }
                this.dataForm.drug_dosage.push(obj)
            }
            if(this.dataForm.medication_status == 1){
                this.dataForm.end_time = ''
            }
            this.dataForm.patient_id = this.patientId
            if(this.checkData()){
                if(this.editType == ''){
                    this.addMedication()
                }else if(this.editType == 'change'){
                    this.addMedicationRecord()
                }else if(this.editType == 'edit'){
                    this.updateRecord()
                }
            }
        },
        subscribePop(fn){
            Dialog.confirm({
                message: '是否需要订阅提醒消息提醒您用药？',
                showCancelButton: true,
                confirmButtonText: '确定',
                confirmButtonColor: '#3388FF',
                closeOnClickOverlay: false,
            }).then(res=>{
                console.log('confirm')
                let ua = navigator.userAgent.toLowerCase()
                if(ua.indexOf('miniprogram') !== -1){
                    wx.miniProgram.redirectTo({
                        url: `/pages/my/pages/infoChoose/drugRemind?pageType=2`
                    })
                }else{
                    const params = {
                        action: 0,
                        data: {
                            title: "用药提醒",
                            url: `${process.env.VUE_APP_BF_BASE_URL}web/measure/reminder`,
                            fullScreen: false,
                            clearPrev: true
                        }
                    }
                    jsToClient('jumpTo', JSON.stringify(params)).catch((err) => {
                        Toast(JSON.stringify(err))
                    })
                }
            }).catch((err)=>{
                if(fn && typeof(fn) == 'function'){
                    fn()
                }
            })
        },
        // 新增用药
        addMedication(){
            addMedication(this.dataForm).then(res=>{
                if(res.status == 200){
                    this.$toast('操作成功')
                    setTimeout(()=>{
                        const fn = ()=>{
                            this.$router.replace('/docWorkRoom/medication/index')
                        }
                        let channel = localStorage.getItem('channel') || ''
                        if(channel == 'patient'){
                            this.subscribePop(fn)
                        }else{
                            fn()
                        }
                    },1500)
                    // let param = `/docWorkRoom/medication/index?medicationTabs=${JSON.stringify(this.medicationTabs)}&channel=${this.channel}&scene=${this.scene}&patient_id=${this.patientId}`;
                    // if (process.env.NODE_ENV === "production" && this.isApp) {
                    //     if (isAndroid) {
                    //         window.android.breathMessage(encodeURI(param));
                    //     }
                    //     if (isIOS) {
                    //         window.webkit.messageHandlers.breathMessage.postMessage(encodeURI(param));
                    //     }
                    // }else{
                        // this.$router.replace({
                        //     path: param
                        //     // path: `/docWorkRoom/medication/index?medicationTabs=${JSON.stringify(this.medicationTabs)}&channel=${this.channel}&scene=${this.scene}&patient_id=${this.patientId}`
                        // })
                    // }
                }else{
                    this.$toast(res.msg)
                }
            })
        },
        // 编辑变更记录 保存
        updateRecord(){
            this.dataForm.operator_id_type = this.isApp? 1 : 2
            let editPageConfig = JSON.parse(localStorage.getItem('editPageConfig'))
            updateRecord(this.dataForm).then(res=>{
                if(res.status == 200){
                    this.$toast('操作成功')
                    setTimeout(()=>{
                        this.$router.replace('/docWorkRoom/medication/index')
                    },1500)
                    // let param = `/docWorkRoom/medication/drugDetails?id=${this.drugListId}`;
                    // if (process.env.NODE_ENV === "production" && this.isApp) {
                    //     if (isAndroid) {
                    //         window.android.breathMessage(encodeURI(param));
                    //     }
                    //     if (isIOS) {
                    //         window.webkit.messageHandlers.breathMessage.postMessage(encodeURI(param));
                    //     }
                    // }else{
                        // this.$router.replace({
                        //     path: param
                            // path: '/docWorkRoom/medication/drugDetails',
                            // query: {
                            //     id: this.drugListId,
                            //     patientAactive: editPageConfig.patientAactive,
                            //     channel: channel,
                            //     scene: scene,
                            //     medicationTabs: medicationTabs
                            // }
                        // })
                    // }
                }else{
                    this.$toast(res.msg)
                }
            })
        },
        // 变更/停止/重开用药 保存
        addMedicationRecord(){
            this.dataForm.handle_type =  + this.editStatus
            addMedicationRecord(this.dataForm).then(res=>{
                if(res.status == 200){
                    this.$toast('操作成功')
                    if(this.editStatus == 2){
                        setTimeout(()=>{
                            const fn = ()=>{
                                this.$router.replace('/docWorkRoom/medication/index')
                            }
                            let channel = localStorage.getItem('channel') || ''
                            if(channel == 'patient'){
                                this.subscribePop(fn)
                            }else{
                                fn()
                            }
                        },1500)
                    }else{
                        setTimeout(()=>{
                            this.$router.replace('/docWorkRoom/medication/index')
                        },1500)
                    }
                    
                    // let param = `/docWorkRoom/medication/index`
                    // if (process.env.NODE_ENV === "production" && this.isApp) {
                    //     if (isAndroid) {
                    //         window.android.breathMessage(encodeURI(param));
                    //     }
                    //     if (isIOS) {
                    //         window.webkit.messageHandlers.breathMessage.postMessage(encodeURI(param));
                    //     }
                    // }else{
                        // this.$router.replace({
                        //     path: param
                            // path: `/docWorkRoom/medication/index?medicationTabs=${JSON.stringify(this.medicationTabs)}&channel=${this.channel}&scene=${this.scene}&patient_id=${this.patientId}`

                        // })
                    // }
                }else{
                    this.$toast(res.msg)
                }
            })
        },
        async getMedicationRoute(){
            // 给药途径
            let res = await getMedicationConfig({type: 2})
            if(res.status == 200){
                this.medicationRoutePickerArr = res.data
            }
        },
        // 编辑时 处理字段是否显示/可编辑 设置可选日期范围
        handleFieldDisabled(editType,editStatus){
            let curIndex = this.curEditItemIndex
            let len = this.changeRecordList.length
            let isEnd = len-1 == curIndex ? true : false  //是否最后一条
            if(editType == 'change'){
                switch(editStatus){
                    case '2':  //变更新增记录 只能变更 变更时间 显示开始和变更
                        document.title = '变更药品'
                        this.startTimeCanEdit = false
                        this.endTimeCanEdit = false
                        this.changeTimeCanEdit = true
                        
                        this.isMedicationStartDateShow = true
                        this.isMedicationEndDateShow = false
                        this.isChangeDateShow = true
                        break;
                    case '3':  //停止用药  只能变更 结束时间 显示结束
                        document.title = '停止用药'
                        this.startTimeCanEdit = false
                        this.endTimeCanEdit = true
                        this.changeTimeCanEdit = false

                        this.isMedicationStartDateShow = true
                        this.isMedicationEndDateShow = true
                        this.isChangeDateShow = false
                        break;
                    case '4':  //重新开始  只能变更 开始时间 显示开始
                        document.title = '重新用药'
                        this.startTimeCanEdit = true
                        this.endTimeCanEdit = false
                        this.changeTimeCanEdit = false

                        this.isMedicationStartDateShow = true
                        this.isMedicationEndDateShow = false
                        this.isChangeDateShow = false
                        break;
                }
                // 变更新增
                this.minDate = new Date(this.changeRecordList[curIndex].change_time)
                this.minDate.setDate(this.minDate.getDate() + 1)
                console.log(this.minDate,this.maxDate)
            }else{
                document.title = '编辑药品'
                switch(editStatus){
                    case '1':  //编辑新增记录 只能编辑 开始时间 且只显示开始时间
                        this.startTimeCanEdit = true
                        this.endTimeCanEdit = false
                        this.changeTimeCanEdit = false
                        
                        this.isMedicationStartDateShow = true
                        this.isMedicationEndDateShow = false
                        this.isChangeDateShow = false
                        break;
                    case '2':  //编辑变更记录  只能编辑 变更时间 显示开始/结束/变更
                        this.startTimeCanEdit = false
                        this.endTimeCanEdit = false
                        this.changeTimeCanEdit = true

                        this.isMedicationStartDateShow = true
                        this.isMedicationEndDateShow = true
                        this.isChangeDateShow = true
                        break;
                    case '3':  //编辑停止用药  只能编辑 结束时间 显示开始/结束时间
                        this.startTimeCanEdit = false
                        this.endTimeCanEdit = true
                        this.changeTimeCanEdit = false

                        this.isMedicationStartDateShow = true
                        this.isMedicationEndDateShow = true
                        this.isChangeDateShow = false
                        break;
                    case '4':  //编辑重新用药 只能编辑 开始时间 只显示开始时间
                        this.startTimeCanEdit = true
                        this.endTimeCanEdit = false
                        this.changeTimeCanEdit = false

                        this.isMedicationStartDateShow = true
                        this.isMedicationEndDateShow = false
                        this.isChangeDateShow = false
                        break;
                }
                // 索引从小到大 日期越来越小
                // 不是第一条 也不是最后一条：  中间数据（前后都有数据） 前后日期区间
                if(curIndex != 0 && !isEnd){
                    this.minDate = new Date(this.changeRecordList[curIndex + 1].change_time)
                    this.minDate.setDate(this.minDate.getDate() + 1)
                    this.maxDate = new Date(this.changeRecordList[curIndex - 1].change_time)
                    this.maxDate.setDate(this.maxDate.getDate() - 1)
                }
                // 第一条 且是 最后一条： 只有一条数据  任一日期
                if(curIndex == 0 && isEnd){
                    const nextTime = moment(new Date()).add('year',1).format("YYYY-MM-DD").split('-')
                    this.maxDate = new Date(nextTime[0]*1, 12, 31)
                    this.minDate = new Date(1970, 1, 1)
                }
                // 第一条 最新一条 索引最小 日期最新： 第一条数据且后面有数据  后面相邻数据日期之前的所有日期
                if(curIndex == 0 && !isEnd){
                    this.minDate = new Date(new Date(this.changeRecordList[curIndex + 1].change_time))
                    this.minDate.setDate(this.minDate.getDate() + 1)
                    const nextTime = moment(new Date()).add('year',1).format("YYYY-MM-DD").split('-')
                    this.maxDate = new Date(nextTime[0]*1, 12, 31)
                }
                // 最后一条 最老一条 索引最大 日期最老： 最后一条数据且前面有数据 前面相邻数据日期之后的所有日期
                if(curIndex != 0 && isEnd){
                    this.minDate = new Date(1970, 1, 1)
                    this.maxDate = new Date(new Date(this.changeRecordList[curIndex - 1].change_time))
                    this.maxDate.setDate(this.maxDate.getDate() - 1)
                    this.defaultDate = new Date(this.changeRecordList[curIndex].start_time)
                }
            }
            if(this.dataForm.medication_status == 1){
                this.isMedicationStartDateShow = true
                this.isMedicationEndDateShow = false
            }else{
                this.isMedicationStartDateShow = true
                this.isMedicationEndDateShow = true
            }
        },
        async getChangeList(drugListId,recordId){
            let obj = {
                "page_size": 999,
                "drug_list_id": drugListId
            }
            let res = await getChangeList(obj)
            if(res.status == 200){
                if(res.data.data.length == 0) return false
                this.changeRecordList = res.data.data
                this.changeRecordList.forEach(item=>{
                    if(item.change_time){
                        item.change_time = item.change_time.split(' ')[0]
                    }
                    if(item.end_time){
                        item.end_time = item.end_time.split(' ')[0]
                    }
                    if(item.start_time){
                        item.start_time = item.start_time.split(' ')[0]
                    }
                })
                let curEditItemIndex = 0
                this.curEditItemIndex = 0
                if(this.editType == 'edit'){
                    curEditItemIndex = this.curEditItemIndex = res.data.data.findIndex(item => {
                        return item.id == recordId
                    })
                    if(curEditItemIndex == -1){
                        this.$toast("当前记录不存在")
                        return false
                    }
                }
                let storageData = JSON.parse(localStorage.getItem('formData'))
                if(storageData){
                    this.backfilleStorageDatForm()
                }else{
                    let data = res.data.data[curEditItemIndex]
                    this.dataForm = data
                    let { drug_id,generic_name,strength,route_of_administration,compliance_type,dose_frequency_name,dose_frequency,drug_dosage,approval_number = '',trade_brand_name = '',company_name = '',drug_sku_id = ''} = data
                    // 药品数据
                    this.drugObj = {
                        drug_id,
                        drug_sku_id,
                        strength,
                        approval_number,
                        trade_brand_name,
                        company_name,
                        drug_name: generic_name + (trade_brand_name || company_name ?'（':'') + (trade_brand_name || company_name) + (trade_brand_name || company_name ?'）':''),
                        package: data.package,
                        data: {
                            generic_name,
                            trade_brand_name,
                            company_name,
                        }
                    }
                    if(this.editStatus == 3){
                        this.dataForm.medication_status = 2
                    }else if(this.editStatus == 4){
                        this.dataForm.medication_status = 1
                    }
                    
                    // 用药状态
                    //给药途径 页面显示字段
                    this.tempRouteCheckedArr = route_of_administration

                    //依从性显示字段回填
                    let complianceObj = this.compliancePickerArr.find(item=>{
                        return item.id == compliance_type
                    })
                    this.compliance = complianceObj ? complianceObj.val : ''
                    //用药频次
                    this.frequenceObj =  dose_frequency ? {id:dose_frequency,name:dose_frequency_name} : {}
                    //选中的剂量Id  1：统一剂量  2：自定义剂量
                    this.dosageSelectedId = drug_dosage && drug_dosage.length > 0 ? drug_dosage[0].type : null

                    // 备注标签回填
                    if(this.dataForm.advice){
                        let tempArr = this.dataForm.advice.split(',')
                        this.notesArr.forEach((item,index)=>{
                            if(tempArr.indexOf(String(item.id)) != -1){
                                this.notesArr[index].selected = true
                            }
                        })
                    }
                    // 编辑 一定有开始时间
                    let startTimeArr = data.start_time.split(' ')
                    this.start_time = startTimeArr[0]
                    if(data.end_time){
                        let endTimeArr = data.end_time.split(' ')
                        this.end_time = endTimeArr[0]
                    }
                    if(data.change_time){
                        let changeTimeArr = data.change_time.split(' ')
                        this.change_time = changeTimeArr[0]
                    }
                }
                console.log(this.dataForm)
                return true
            }else{
                this.$toast(res.msg)
                return false
            }
        },
        editPageInit(){

        },
        // 端上 扫码成功的回调
        getDrugListByBarCode(barCode){
            let obj = {
                barCode,
                patientId: this.patientId,
                scene: this.dataForm.source
            }
            getDrugListByBarCode(obj).then(res=>{
                if(res.status == 200){
                    if(res.data.length > 0){
                        let resDrug = this.scanResdrug = res.data[0]
                        // 如果是新增页
                        // alert(this.isPageEdit)
                        if(!this.isPageEdit){
                            // 非0 状态为使用中/已停用
                            if(resDrug.status != 0){
                                // alert('使用中')
                                Dialog.confirm({
                                    message: '该患者已使用此类药品，是否需要更新信息',
                                    showCancelButton: true,
                                    confirmButtonText: '去变更',
                                    confirmButtonColor: '#3388FF',
                                    closeOnClickOverlay: true,
                                }).then(() => {
                                    let param = `/docWorkRoom/medication/drugDetails?id=${resDrug.patient_drug_list_id}&drug_id=${resDrug.drug_id}&expand=0`
                                    // if (process.env.NODE_ENV === "production" && this.isApp) {
                                    //     if (isAndroid) {
                                    //         window.android.breathMessage(encodeURI(param));
                                    //     }
                                    //     if (isIOS) {
                                    //         window.webkit.messageHandlers.breathMessage.postMessage(encodeURI(param));
                                    //     }
                                    // }else{
                                        this.$router.replace({
                                            path: param
                                            // path: '/docWorkRoom/medication/drugDetails',
                                            // query: {
                                            //     drug_id: item.drug_id,
                                            //     expand: '0'
                                            // }
                                        })
                                    // }
                                })
                                
                            }else{
                                // alert('未使用')
                                if(resDrug.drug_id != this.drugObj.drug_id){
                                    this.drugObj = resDrug
                                    // let {drug_name,strength,approval_number=''} = resDrug
                                    // this.drugObj.drug_name = drug_name + ' ' + strength + ' ' + (resDrug.package || '') + ' ' + approval_number,
                                    this.dataForm.drug_id = resDrug.drug_id
                                    this.dataForm.drug_sku_id = resDrug.drug_sku_id
                                    // 途径
                                    if(Array.isArray(resDrug.route_of_administration) && resDrug.route_of_administration.length > 0){
                                        this.dataForm.route_of_administration = resDrug.route_of_administration
                                        this.tempRouteCheckedArr = resDrug.route_of_administration
                                    }
                                    // 频次
                                    if(resDrug.dose_frequency){
                                        this.dataForm.dose_frequency = resDrug.dose_frequency
                                        this.frequenceObj.id = resDrug.dose_frequency
                                    }
                                    // 剂量
                                    if(resDrug.dose || resDrug.unit){
                                        let obj = {
                                            "type": 1,
                                            "time_name": "",
                                            "time": "",
                                            "unit": resDrug.unit,
                                            "dosage": resDrug.dose
                                        }
                                        this.dataForm.drug_dosage.push(obj)
                                    }
                                }
                            }
                            return 
                        }
                        // 如果是编辑/变更
                        // 药品是否一致
                        // alert('变更')
                        // alert(JSON.stringify(resDrug))
                        // alert(JSON.stringify(this.drugObj))
                        if(resDrug.drug_id == this.drugObj.drug_id){
                            if(resDrug.drug_sku_id == this.drugObj.drug_sku_id){
                                Dialog.confirm({
                                    message: '该药品与当前使用药品一致无需变更',
                                    confirmButtonColor: '#3388FF',
                                })
                            }else{
                                this.isScanDrugInconsistentShow = true
                            }
                        }else{
                            Dialog.confirm({
                                message: '非同类药品无法直接补充信息，请新增药品或重新扫描',
                                showCancelButton: true,
                                confirmButtonText: '重新扫描',
                                confirmButtonColor: '#3388FF',
                                cancelButtonText: '新增药品',
                                cancelButtonColor: '#3388FF',
                                closeOnClickOverlay: true,
                            }).then(() => {
                                this.scanBarCode()
                            }).catch((res) => {
                                if(res == 'cancel'){
                                    localStorage.removeItem('formData')
                                    localStorage.removeItem('editPageConfig')
                                    localStorage.setItem('drugObj',JSON.stringify(resDrug))
                                    // let param = `/docWorkRoom/medication/addMedication?source=${this.dataForm.source}&a=${Math.rendom()*10}`
                                    // this.$router.replace({
                                    //     path: this.$route.path,
                                    //     query: {
                                    //         source: this.dataForm.source,
                                    //         popType: true,
                                    //     }
                                    //     // path: '/docWorkRoom/medication/search'
                                    // })
                                    location.replace(`/docWorkRoom/medication/addMedication?source=${this.dataForm.source}&popType=true`)
                                    // this.storageFun()
                                    // let param = `/docWorkRoom/medication/search?source=${this.dataForm.source}`
                                    // if (process.env.NODE_ENV === "production" && this.isApp) {
                                    //     if (isAndroid) {
                                    //         window.android.breathMessage(param);
                                    //     }
                                    //     if (isIOS) {
                                    //         window.webkit.messageHandlers.breathMessage.postMessage(param);
                                    //     }
                                    // }else{
                                    //     this.$router.push({
                                    //         path: param
                                    //         // path: '/docWorkRoom/medication/search'
                                    //     })
                                    // }
                                }
                            })
                            // 药品不一致时 判断是否同类
                            // resDrug.data.strength = resDrug.strength
                            // this.drugObj.data.strength = this.drugObj.strength
                            // 同类药品
                            // alert(JSON.stringify(resDrug.data))
                            // alert(JSON.stringify(this.drugObj.data))
                            // alert(JSON.stringify(resDrug.data) == JSON.stringify(this.drugObj.data))

                            // JSON.stringify判断对象是否一样 徐亚注意两个对象的key顺序一致才是true
                            // if(JSON.stringify(resDrug.data) == JSON.stringify(this.drugObj.data)){
                            //     if(this.drugObj.package != resDrug.package || this.drugObj.approval_number != resDrug.approval_number){
                            //         this.isScanDrugInconsistentShow = true
                            //     }
                            // }else{
                            //     // 非同类
                            // }
                        }
                    }else{
                        Dialog.confirm({
                            message: '未找到该药品，请手动录入',
                            showCancelButton: true,
                            confirmButtonText: '去录入',
                            confirmButtonColor: '#3388FF',
                            closeOnClickOverlay: true,
                        }).then(() => {
                            // this.storageFun()
                            localStorage.removeItem('formData')
                            localStorage.removeItem('drugObj')
                            let param = `/docWorkRoom/medication/search?source=${this.dataForm.source}`
                            location.replace(param)
                            // if (process.env.NODE_ENV === "production" && this.isApp) {
                            //     if (isAndroid) {
                            //         window.android.breathMessage(param);
                            //     }
                            //     if (isIOS) {
                            //         window.webkit.messageHandlers.breathMessage.postMessage(param);
                            //     }
                            // }else{
                                // this.$router.replace({
                                //     path: param
                                //     // path: '/docWorkRoom/medication/search'
                                // })
                            // }
                        }).catch(() => {
                            // on cancel
                        })
                    }
                }
            })
        },
        scanPopConfirm(){
            this.drugObj = this.scanResdrug
            this.dataForm.drug_id = this.scanResdrug.drug_id
            this.dataForm.drug_sku_id = this.scanResdrug.drug_sku_id
            // 途径
            // this.dataForm.route_of_administration = this.scanResdrug.route_of_administration
            // this.tempRouteCheckedArr = this.scanResdrug.route_of_administration
            // // 频次
            // this.dataForm.dose_frequency = this.scanResdrug.dose_frequency
            // this.frequenceObj.id = this.scanResdrug.dose_frequency
            // // 剂量
            // if(this.scanResdrug.dose || this.scanResdrug.unit){
            //     let obj = {
            //         "type": 1,
            //         "time_name": "",
            //         "time": "",
            //         "unit": this.scanResdrug.unit,
            //         "dosage": this.scanResdrug.dose
            //     }
            //     this.dataForm.drug_dosage.push(obj)
            // }
        },
        scanBarCode(){
            // 编辑变更记录 / 变更-重新用药 扫码不可用
            if(this.editType == 'edit' || (this.editType == 'change' && this.editStatus == 4)){
                return
            }
            if(this.isApp){
                // if (process.env.NODE_ENV === 'production') {
                if (isAndroid) {
                    window.android.scanDrugJSClick()
                }
                if (isIOS) {
                    window.webkit.messageHandlers.scanDrugJSClick.postMessage('')
                }
            // }
            }else{
                let self = this
                wx.ready(function () {   //需在用户可能点击分享按钮前就先调用
                     wx.scanQRCode({
                        needResult: 1, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果，
                        scanType: ["barCode"], // 可以指定扫二维码还是一维码，默认二者都有
                        success: function (res) {
                            let result = res.resultStr.split(',')[1]
                            self.getDrugListByBarCode(result)
                        }
                    })   
                })
            }
        },
        getWxConfig(){
            let url = window.location.href.split('#')[0]
            let obj = {
                url,
                account: 'ihec'
            }
            axios.post('https://patient-api.zz-med.com/api/v1/wechat/js_api/sign',obj).then(res=>{
            // getWxConfig(obj).then(res=>{
                if(res.data.status == 0){
                    let {appId,timestamp,nonceStr,signature} = res.data.data
                    wx.config({
                        debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
                        appId: appId, // 必填，公众号的唯一标识
                        timestamp: timestamp, // 必填，生成签名的时间戳
                        nonceStr: nonceStr, // 必填，生成签名的随机串
                        signature: signature, // 必填，签名
                        jsApiList: [
                            "scanQRCode"
                        ],
                    })
                }
            })
        },
        async init(){
            // 初始化日历的时间
            const nextTime = moment(new Date()).add('year',1).format("YYYY-MM-DD").split('-')
            this.maxDate = new Date(nextTime[0]*1, 12, 31)
            this.minDate = new Date(1970, 1, 1)
            this.patientId = localStorage.getItem('patient_id')
            this.channel = localStorage.getItem('channel')
            this.scene = localStorage.getItem('scene')
            this.medicationTabs = JSON.parse(localStorage.getItem('medicationTabs'))
            await this.getMedicationRoute()  // 获取给药途径
            window.getDrugListByBarCode = this.getDrugListByBarCode  //挂载扫码成功回调方法

            let {drugListId = null,recordId = null,editType = null,editStatus = null,source = 1} = this.$route.query
            if(drugListId && recordId){
                let obj = {
                    drugListId,
                    recordId,
                    editType,
                    editStatus,
                    source,
                }
                localStorage.setItem('editPageConfig',JSON.stringify(obj))
                this.isPageEdit = true
                this.editType = editType
                this.drugListId = drugListId
                this.editStatus = editStatus
                this.dataForm.source = source
                let res = await this.getChangeList(drugListId,recordId)
                console.log(res,'*************')
                if(!res) return
                this.handleFieldDisabled(editType,editStatus)  // 处理字段是否可编辑
            }else{
                this.isPageEdit = false
                this.backfilleStorageDatForm()
            }
            this.getWxConfig()
        }
    },
    mounted() {
        console.log('s刷新了')
        this.init()
    }
}
</script>

<style lang="scss">
$app-color: #e18326;
$wx-color: #3388FF;
.addMedication{
    font-family: 'PingFangSC-Regular';
    padding: 0 15px 120px;
    background: white;
    .noBorder{
        border: none !important;
    }
    .cellBar{
        font-size: 17px;
        padding: 14px 0;
        // min-height: 40px;
        display: flex;
        justify-content: space-between;
        // align-items: center;
        border-bottom: 1px solid #F1F1F1;
        .label{
            color: #5A6266;
            display: flex;
            // align-items: center;
            min-width: 120px;
            // margin-top: 2px;
            .redDot{
                color: #F53F3F;
                margin-right: 5px;
                width: 8px;
            }
            .scanIcon{
                width: 18px;
                height: 18px;
                margin-left: 5px;
                margin-top: -2px;
            }
        }
        .rightVal{
            display: flex;
            align-items: center;
            // margin-top: 2px;
            .placeHolder{
                color: #C0C6CC;
            }
            .val{
                color: #0A0A0A;
                text-align: right;
            }
            .item{
                border-radius: 6px;
                // width: 72px;
                // height: 30px;
                // line-height: 32px;
                // text-align: center;
                padding: 6px 13px;
                border: 1px solid #3388FF;
                color: #3388FF;
                margin-left: 12px;
            }
            .disabled{
                color: #C0C6CC !important;
                line-height: 25px;
                text-align: right;
            }
            .toggleSelect{
                background: linear-gradient(270deg, #38F 0%, #2596FF 100%);;
                color: white !important;
            }
        }
        .rightDisabled{
            .toggleSelect{
                background: #C0C6CC;
                color: white !important;
            }
            .item{
                border: 1px solid #C0C6CC;
                color: #C0C6CC;
            }
        }
    }
    .customTimeDosage{
        margin: 0 -15px;
        background: #f0f3f8;
        padding: 12px 15px;
        .inner{
            display: flex;
            flex-wrap: wrap;
            color: #5A6266;
            font-size: 16px;
            display: flex;
            justify-content: space-between;
            background: white;
            border-radius: 8px;
            padding: 6px 14px;
            .customItem{
                width: 46%;
                border-radius: 4px;
                padding: 8px 14px;
                margin: 8px 0;
                border: 1px solid #E5E5E5;
                display: flex;
                justify-content: space-between;
                align-items: center;
                box-sizing: border-box;
            }
            .val{
                display: flex;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                display: inline-block;
                line-height: 20px;
            }
        }
    }
    .medicationRoutePop{
        width: 100%;
        height: 100%;
        overflow: hidden;
        position: relative;
    }
    .toolBar{
        font-size: 15px;
        height: 45px;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 20px;
        box-sizing: border-box;
        position: absolute;
        top: 0;
        .confirm{
            color: #3388FF;
        }
        .cancel{
            color: #999999;
        }
        .title{
            color: #0A0A0A;
        }
    }
    .routeList{
        overflow-y: auto;
        max-height: 400px;
        margin-top: 45px;
        .routeItem{
            height: 52px;
            padding:  0 20px;
            border-bottom: 1px solid #F1F1F1;
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 17px;
            color: #5A6266;
        }
    }
    .btnOuter{
        height: 100px;
        width: 100%;
        box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.25);
        background: white;
        position: fixed;
        bottom: 0;
        left: 0;
        z-index: 100;
        .btn{
            width: 335px;
            text-align: center;
            height: 44px;
            line-height: 44px;
            border-radius: 8px;
            background: #3388FF;
            color: white;
            font-size: 16px;
            font-weight: 500;
            margin: 20px auto 0;
        }
    }
    .calendarCustomClass{
        .van-button--round{
            border-radius: 8px !important;
        }
        .van-icon{
            position: absolute !important;
            font-size: 22px !important;
        }
        ::v-deep .van-button--round{
            border-radius: 8px !important;
        }
        ::v-deep .van-icon{
            position: absolute !important;
            font-size: 22px !important;
        }
    }
    .textareaField{
        border: 1px solid #DCDFE6;
        padding: 9px 16px;
        margin-top: 5px;
    }
    .dialogOuter{
        display: flex;
        // align-items: center;
        justify-content: center;
        flex-direction: column;
        font-size: 17px;
        color: #363636;
        padding: 10px 30px;
        .line{
            display: flex;
            align-items: baseline;
            .label{
                width: 75px;
            }
            .val{
                text-align: left;
                span{
                    display: block;
                    padding: 3px 0;
                }
            }
        }
    }
    .van-dialog .van-dialog__header{
        font-size: 17px;
    }
    .van-picker__confirm{
        color: #3388ff;
    }
}
</style>