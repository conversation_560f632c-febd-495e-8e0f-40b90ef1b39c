<template>
  <div class="his-box">
    <div class="his-list" v-for="(item,index) in this.infoHisList" :key="index" @click="onClickInfo(item)">
      <div class="title">
        <span>{{item.pres_cribed_at }}</span>
      </div>
      <div class="content" v-for="(childItem,childIndex) in item.list" :key="childIndex">
        <span class="drugName">{{ childItem .drug_name }}</span>
        <span class="spec">{{ childItem.drug_spec }}</span>
      </div>
    </div>
     <div v-if="!this.infoHisList.length">
          <img class="nodrugresult" src="../imgs/nohislist.png" alt="" />
      </div>
  </div>

</template>

<script>
export default {
  props: {
    infoHisList: {
      type: Array,
      default: ()=>[]
    }
  },
  data(){
    return {
      // / docWorkRoom / medication / hisDetails
    }
  },
  methods: {
    onClickInfo(item) {
      localStorage.setItem('hisListInfo', JSON.stringify(item))
      this.$router.push({
        path: '/docWorkRoom/medication/hisDetails',
      })
    }
  },
  mounted() {
    console.log(this.infoHisList)
  }
}
</script>

<style lang="scss">
.his-box{
    display: flex;
    justify-content: center;
    margin-top:15px;
    flex-direction: column;
    align-items: center;
    .his-list{
      margin-bottom: 12px;
      padding: 5px 15px 11px 15px;
      width: 315px;
      min-height: 76px;
      background-color: #fff;
      border-radius: 8px;
    .title{
      display: flex;
      color:#878F99;
      font-size: 14px;
      line-height: 24px;
      border-bottom: 1px solid #FAFAFA;

    }
  }
  .content{
    display: flex;
    flex-direction: column;
    text-align: left;
    .drugName{
      margin-top: 13px;
      margin-bottom: 4px;
      color: #0A0A0A;
      font-size: 17px;
      font-weight: 400;
    }
    .spec{
      color: #878F99;
      font-size: 14px;
      line-height: 20px;
    }
  }
}

</style>