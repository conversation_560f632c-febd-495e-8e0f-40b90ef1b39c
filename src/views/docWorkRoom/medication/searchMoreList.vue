<template>
  <div class="searchMoreList">
    <div class="resListMain">
      <div class="labelTitle commonTitle">{{moreDrugObj.label || ''}}</div>
      <div v-for="item in moreDrugObj.values" :key="item.drug_id">
        <div class="item" @click="selectDrug(item)">
          <span class="name" v-html="item.showDrugName"></span>
          <span v-if="item.status && item.status != 0" class="rightStatus" :class="[item.status == 1 ? 'useing' : 'noUse']">{{ item.status == 1 ? "使用中" : "已停用" }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      moreDrugObj: {},
      source: '',
      channel: '',
      scene: '',
      medicationTabs: [],
      localFormData: {}
    };
  },
  methods: {
    toDrugDetails(item) {
      let param = `/docWorkRoom/medication/drugDetails?id=${item.patient_drug_list_id}&drug_id=${item.drug_id}&expand=0`;
      this.$router.replace({
        path: param,
      })
    },
    selectDrug(item) {
      delete item.showDrugName
      localStorage.removeItem('searchStr')
      if(!this.localFormData){
          localStorage.setItem('drugObj',JSON.stringify(item))
      }else{
          localStorage.removeItem('drugObj')
          
          this.localFormData.drugObj = item
          this.localFormData.dataForm.drug_id = item.drug_id
          this.localFormData.dataForm.drug_sku_id = item.drug_sku_id
          this.localFormData.dataForm.source = this.source
          // 如果药品携带途径等参数 即回填
          // 途径
          if(Array.isArray(item.route_of_administration) && item.route_of_administration.length > 0){
              this.localFormData.dataForm.route_of_administration = item.route_of_administration
              this.localFormData.tempRouteCheckedArr = item.route_of_administration
          }else{
              this.localFormData.dataForm.route_of_administration = []
              this.localFormData.tempRouteCheckedArr = []
          }
          // 频次
          if(item.dose_frequency){
              this.localFormData.dataForm.dose_frequency = item.dose_frequency
              this.localFormData.frequenceObj.id = item.dose_frequency
          }else{
              this.localFormData.dataForm.dose_frequency = ''
              this.localFormData.frequenceObj = {}
          }
          // 剂量
          if(item.dose || item.unit){
              let obj = {
                  "type": 1,
                  "time_name": "",
                  "time": "",
                  "unit": item.unit,
                  "dosage": item.dose
              }
              this.localFormData.dataForm.drug_dosage.push(obj)
              this.localFormData.dosageSelectedId = 1
          }else{
              this.localFormData.dataForm.drug_dosage = []
              this.localFormData.dosageSelectedId = null
          }
          localStorage.setItem('formData', JSON.stringify(this.localFormData))
      }
      if (item.drug_id && item.status != 0) {
        this.toDrugDetails(item);
      } else {
        localStorage.setItem('searchMoreListSelected',true)
        this.$router.go(-1)
        // let param = `/docWorkRoom/medication/addMedication`;
        // this.$router.replace({
        //   path: param,
        // })
      }
    },
  },
  mounted() {
    window.scrollTo(0,0)
    this.localFormData = JSON.parse(localStorage.getItem('formData'))
    this.moreDrugObj = localStorage.getItem("moreDrugObj") ? JSON.parse(localStorage.getItem("moreDrugObj")) : {}
    this.channel = localStorage.getItem('channel')
    this.scene = localStorage.getItem('scene')
    this.medicationTabs = JSON.parse(localStorage.getItem('medicationTabs'))
    this.source = this.$route.query.source
  },
};
</script>

<style lang="scss">
.searchMoreList {
  .resListMain {
    padding: 12px 28px;
    background: white;
    min-height: calc(100vh - 74px);
    .labelTitle{
        color: #C0C6CC;
        padding: 12px 0 7px 0;
    }
    .commonTitle{
        font-size: 15px;
        text-align: left;
        width: 100%;
        pointer-events: none;
    }
    .item {
      font-size: 15px;
      padding: 12px 0;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .name {
        flex: 1;
        color: #5a6266;
        display: inline-block;
        line-height: 20px;
        text-align: left;
        word-break: break-word;
      }
      .spec {
        color: #c0c6cc;
        margin-left: 10px;
      }
      .rightStatus {
        width: 50px;
        text-align: right;
        font-size: 13px;
      }
    }

    .noUse {
      color: #878f99;
    }
    .useing {
      color: #377cfb;
    }
  }
}
</style>
