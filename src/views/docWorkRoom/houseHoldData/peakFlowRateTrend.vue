<template>
    <div class="peakFlowRateTrend">
        <div class="peakFlowRateBox">
            <div class="pieBox">
                <div class="daysPicker">
                    <div
                    class="daysItem"
                    :class="[daysChekedIndex == index ? 'daysChecked' : '']"
                    v-for="(item, index) in daysPicker"
                    :key="index"
                    @click="daysSelect(index, item)"
                    >
                    {{ item.type }}
                    </div>
                </div>
                <div class="pieBlock">
                    <template v-if="pieLabelList.length > 0">
                        <div class="pieChart" ref="pieChart"></div>
                        <div class="typeList">
                            <div class="line lineTitle">
                            <span class="label">测量情况</span>
                            <span class="val">比例</span>
                            </div>
                            <div class="line" v-for="item in pieLabelList" :key="item.status_id">
                            <div class="label">
                                <div class="circle normal" :style="{ 'background': item.color }"></div>
                                <span class="labelText">{{ item.status_display }}({{ item.count }})</span>
                            </div>
                            <span class="val">{{ item.proportion }}</span>
                            </div>
                        </div>
                    </template>
                    <span class="noDataText" v-else>无测量数据</span>
                </div>
            </div>
        </div>
        <div class="bottomTips">我是有底线的</div>
    </div>
</template>

<script>
import { getPefPie,getBpConfig } from '@/api/docWorkRoom';
import * as echarts from 'echarts';

export default {
    data: () => {
        return {
            startDate: '',
            endDate: '',
            daysChekedIndex: '',
            pieLabelList: [],
            daysPicker: []
        }
    },
    computed: {},
    watch: {},
    created() {
    },
    mounted() {
        this.getBpConfig()
    },
    methods: {
        getBpConfig(){
            let obj = {
                is_patient_platform: this.$route.query.source == 'docApp' ? '0' : '1'
            }
            getBpConfig(obj).then(res=>{
                if(res.status == 0){
                    this.daysPicker = res.data.date_type
                    this.startDate = res.data.date_type[0].start_date.replace(/-/g,'/')
                    this.endDate = res.data.date_type[0].end_date.replace(/-/g,'/')
                    this.getData()
                }else{
                    this.$toast(res.msg)
                }
            })
        },
        getData(){
            let obj = {
                start_time: this.startDate.replace(/\//g,'-'),
                end_time: this.endDate.replace(/\//g,'-'),
                user_id: this.$route.query.patient_id || '',
                is_patient_platform: this.$route.query.source == 'docApp' ? '0' : '1'
            }
            getPefPie(obj).then(res=>{
                if(res.status == 0){
                    res.data.abnormal_list.forEach(item=>{
                        item.name = item.status_display
                        item.value = item.count
                        item.color = item.status_id == 235 ? '#07C060' : item.status_id == 233 ? '#B12323' : '#FC6161'
                    })
                    this.pieLabelList = res.data.abnormal_list
                    if(this.pieLabelList.length == 0){
                        return
                    }
                    this.$nextTick(()=>{
                        this.pieChartInit(res.data)
                    })
                }
            })
        },
        daysSelect(index,item){
            this.daysChekedIndex = index
            this.startDate = item.start_date.replace(/-/g,'/')
            this.endDate = item.end_date.replace(/-/g,'/')
            this.getData()
        },
        // 饼状图
        pieChartInit(info) {
            let myEchart = echarts.init(this.$refs.pieChart, 'light')
            const options = {
                tooltip: {
                show: false
                },
                color: this.pieLabelList.map(item=>item.color),
                series: [
                {
                    type: 'pie',
                    radius: ['80%', '100%'],
                    avoidLabelOverlap: false,
                    label: {
                    show: true,
                    position: 'center',
                    formatter: '{total|'+ info.total_count +'}'+'\n\r'+'{active|总测量次数}',
                    rich: {
                        total: {
                        color: '#333333',
                        fontSize: 26,
                        lineHeight: 37
                        },
                        active: {
                        color: '#999999',
                        fontSize: 12,
                        lineHeight: 17
                        }
                    }
                    },
                    silent: true,
                    emphasis: {
                    show: true,
                    scale: false
                    },
                    labelLine: {
                    show: false
                    },
                    data: info.abnormal_list || []
                }
                ]
            }
            myEchart.setOption(options)
        },
    }
}
</script>

<style lang="scss" scoped>
.peakFlowRateTrend {
    .daysPicker {
      font-family: "PingFang SC";
      height: 34px;
      line-height: 34px;
      border-radius: 30px;
      display: flex;
      background: #ecf5ff;
      font-size: 15px;
      font-weight: 500;
      padding: 3px;
      align-items: center;
      .daysItem {
        flex: 1;
        color: #909399;
      }
      .daysChecked {
        background: white;
        border-radius: 30px;
        color: #377cfb;
      }
    }
    .noDataText{
        color: #999999;
        font-size: 15px;
        text-align: center;
        width: 100%;
    }
    .peakFlowRateBox {
        background: white;
        border-radius: 8px;
        padding: 20px 14px;
        .pieBox {

            .tabBtn {
                height: 40px;
                // padding: 0 15px;

            }

            
        }

        

    }
    .pieBlock{
      display: flex;
      align-items: center;
      margin-top: 24px;
      .pieChart{
        width: 110px;
        height: 110px;
        margin-left: 10px;
      }
      .typeList{
        display: flex;
        flex-direction: column;
        flex: 1;
        margin-left: 26px;
        margin-right: 10px;
        font-size: 15px;
        .line:first-of-type{
          margin-top: 0;
        }
        .line{
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: 18px;
          color: #666666;
          .label{
            display: flex;
            align-items: center;
          }
          .labelText{
                flex: 1;
                text-align: left;
                line-height: 20px;
            }
            .val{
                margin-left: 4px;
            }
        }
        
        .lineTitle{
          color: #333333;
          font-weight: bold;
        }
        .circle{
          width: 12px;
          height: 12px;
          border-radius: 50%;
          margin-right: 6px;
        }
        .normal{
          background: #07C060;
        }
        .height{
          background: #FF515A;
        }
        .low{
          background: #008AFF;
        }
      }
    }
    .bottomTips{
        color: #999999;
        font-size: 12px;
        line-height: normal;
        margin-top: 12px;
    }
}
</style>