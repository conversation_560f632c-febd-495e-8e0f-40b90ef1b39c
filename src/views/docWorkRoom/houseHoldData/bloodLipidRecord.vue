<template>
    <div class="bloodLipidRecord">
        <van-list v-model="loading" :finished="finished" finished-text="没有更多了~" @load="onLoad">
            <div class="detailBox" v-for="(item, index) in dataList" :key="index">
                <div class="date">
                    <p>{{ item.measdate }}</p>
                </div>
                <div class="content">
                    <div class="content_box_left">
                        <div class="detail detail_box">
                            <div class="top">
                                <div class="left_top">总胆固醇</div>
                                <div class="left_content recordNum">
                                    <div class="name" :class="[ item['CHOL'].deflection ? [1,3].includes(item['CHOL'].deflection) ? 'blueColor' : 'redColor' : '']">{{ item['CHOL'].value || '--' }}</div>                
                                    <van-icon v-if="item['CHOL'].deflection" name="down" :class="[ [1,3].includes(item['CHOL'].deflection) ? '' : 'arrowRotate']" :color="[1,3].includes(item['CHOL'].deflection) ? '#008AFF' : '#FF1F33' " />
                                    <div class="unit">mmol/L</div>
                                </div>
                                <div class="left_bottom">
                                    <img v-if="item['CHOL'].deflection == 4" class="warnIcon" src="./imgs/warning.png" alt="">
                                    <img v-if="item['CHOL'].deflection == 3" class="warnIcon" src="./imgs/blueWarning.png" alt="">
                                    <div v-if="item['CHOL'].status_display" :class="[ item['CHOL'].deflection ? [1,3].includes(item['CHOL'].deflection) ? 'blueTag' : 'redTag' : 'greenTag']" class="left_tips" style="text-align: center;">{{ item['CHOL'].status_display }}</div>
                                </div>
                            </div>
                            <div class="top">
                                <div class="left_top">甘油三酯</div>
                                <div class="left_content recordNum">
                                    <div class="name" :class="[ item['TG-B'].deflection ? [1,3].includes(item['TG-B'].deflection) ? 'blueColor' : 'redColor' : '']">{{ item['TG-B'].value || '--' }}</div>                
                                    <van-icon v-if="item['TG-B'].deflection" name="down" :class="[ [1,3].includes(item['TG-B'].deflection) ? '' : 'arrowRotate']" :color="[1,3].includes(item['TG-B'].deflection) ? '#008AFF' : '#FF1F33' " />
                                    <div class="unit">mmol/L</div>
                                </div>
                                <div class="left_bottom">
                                    <img v-if="item['TG-B'].deflection == 4" class="warnIcon" src="./imgs/warning.png" alt="">
                                    <img v-if="item['TG-B'].deflection == 3" class="warnIcon" src="./imgs/blueWarning.png" alt="">
                                    <div v-if="item['TG-B'].status_display" :class="[ item['TG-B'].deflection ? [1,3].includes(item['TG-B'].deflection) ? 'blueTag' : 'redTag' : 'greenTag']" class="left_tips" style="text-align: center;">{{ item['TG-B'].status_display }}</div>
                                </div>
                            </div>
                        </div>
                        <div class="detail detail_box">
                            <div class="top">
                                <div class="left_top">低密度脂蛋白</div>
                                <div class="left_content recordNum">
                                    <div class="name" :class="[ item['ULDL'].deflection ? [1,3].includes(item['ULDL'].deflection) ? 'blueColor' : 'redColor' : '']">{{ item['ULDL'].value || '--' }}</div>                
                                    <van-icon v-if="item['ULDL'].deflection" name="down" :class="[ [1,3].includes(item['ULDL'].deflection) ? '' : 'arrowRotate']" :color="[1,3].includes(item['ULDL'].deflection) ? '#008AFF' : '#FF1F33' " />
                                    <div class="unit">mmol/L</div>
                                </div>
                                <div class="left_bottom">
                                    <img v-if="item['ULDL'].deflection == 4" class="warnIcon" src="./imgs/warning.png" alt="">
                                    <img v-if="item['ULDL'].deflection == 3" class="warnIcon" src="./imgs/blueWarning.png" alt="">
                                    <div v-if="item['ULDL'].status_display" :class="[ item['ULDL'].deflection ? [1,3].includes(item['ULDL'].deflection) ? 'blueTag' : 'redTag' : 'greenTag']" class="left_tips" style="text-align: center;">{{ item['ULDL'].status_display }}</div>
                                </div>
                            </div>
                            <div class="top">
                                <div class="left_top">高密度脂蛋白</div>
                                <div class="left_content recordNum">
                                    <div class="name" :class="[ item['UHDL'].deflection ? [1,3].includes(item['UHDL'].deflection) ? 'blueColor' : 'redColor' : '']">{{ item['UHDL'].value || '--' }}</div>                
                                    <van-icon v-if="item['UHDL'].deflection" name="down" :class="[ [1,3].includes(item['UHDL'].deflection) ? '' : 'arrowRotate']" :color="[1,3].includes(item['UHDL'].deflection) ? '#008AFF' : '#FF1F33' " />
                                    <div class="unit">mmol/L</div>
                                </div>
                                <div class="left_bottom">
                                    <img v-if="item['UHDL'].deflection == 4" class="warnIcon" src="./imgs/warning.png" alt="">
                                    <img v-if="item['UHDL'].deflection == 3" class="warnIcon" src="./imgs/blueWarning.png" alt="">
                                    <div v-if="item['UHDL'].status_display" :class="[ item['UHDL'].deflection ? [1,3].includes(item['UHDL'].deflection) ? 'blueTag' : 'redTag' : 'greenTag']" class="left_tips" style="text-align: center;">{{ item['UHDL'].status_display }}</div>
                                </div>
                            </div>
                        </div>
                        <div class="detail detail_box">
                            <div class="top">
                                <div class="left_top">非高密度脂蛋白</div>
                                <div class="left_content recordNum">
                                    <div class="name" :class="[ item['non-HDL-C'].deflection ? [1,3].includes(item['non-HDL-C'].deflection) ? 'blueColor' : 'redColor' : '']">{{ item['non-HDL-C'].value || '--' }}</div>                
                                    <van-icon v-if="item['non-HDL-C'].deflection" name="down" :class="[ [1,3].includes(item['non-HDL-C'].deflection) ? '' : 'arrowRotate']" :color="[1,3].includes(item['non-HDL-C'].deflection) ? '#008AFF' : '#FF1F33' " />
                                    <div class="unit">mmol/L</div>
                                </div>
                                <div class="left_bottom">
                                    <img v-if="item['non-HDL-C'].deflection == 4" class="warnIcon" src="./imgs/warning.png" alt="">
                                    <img v-if="item['non-HDL-C'].deflection == 3" class="warnIcon" src="./imgs/blueWarning.png" alt="">
                                    <div v-if="item['non-HDL-C'].status_display" :class="[ item['non-HDL-C'].deflection ? [1,3].includes(item['non-HDL-C'].deflection) ? 'blueTag' : 'redTag' : 'greenTag']" class="left_tips" style="text-align: center;">{{ item['non-HDL-C'].status_display }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="content_box_right">
                        <div class="rightBox">
                            <div class="content_right_name">
                                {{ item.measure_at.split(' ')[1] }}
                            </div>
                            <div class="content_right_name" v-if="item.measure_type_name">
                                {{ item.measure_type_name }}
                            </div>
                            <div class="content_right_name" v-if="item.measure_method_name">
                                {{ item.measure_method_name }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </van-list>
    </div>
</template>

<script>
import { getBloodfatList } from '@/api/docWorkRoom'
export default {
    data: () => {
        return {
            dataList: [],
            loading: false,
            finished: false,
            pageNo: 0,
        }
    },
    methods: {
        onLoad() {
            let obj = {
                page: this.pageNo += 1,
                size: 15,
                user_id: this.$route.query.patient_id || '',
                is_patient_platform: this.$route.query.source == 'docApp' ? '0' : '1'
            }
            getBloodfatList(obj).then(res=>{
                if(res.status == 0){
                    let {list,pagination} = res.data
                    let codeArr = [
                      "TG-B",
                      "CHOL",
                      "UHDL",
                      "ULDL",
                      "non-HDL-C"
                    ]
                    codeArr.forEach(code=>{
                        list.forEach(item=>{
                            let val = item[code]
                            // if(item.abnormal_info.length > 0){
                                let codeObj = item.abnormal_info.find(i=>i.code == code)
                                if(codeObj){
                                    codeObj.value = val
                                }else{
                                    codeObj = {value: val}
                                }
                                item[code] = codeObj
                            // }else{
                            //     let obj = {value: val}
                            //     item[code] = obj
                            // }
                        })
                    })
                    
                    this.dataList = this.dataList.concat(list)
                    this.loading = false
                    if(this.dataList.length >= pagination.total){
                        this.finished = true
                    }
                }else{
                    this.$toast(res.msg)
                }
            })
        }
    },
    mounted(){

    },
}
</script>

<style lang="scss" scoped>
.bloodLipidRecord {

    .header {
        border-radius: 8px;
        background: #fff;
        padding: 20px 14px;

        .tips {
            
            .headerTips {
                color: #0A0A0A;
                font-size: 17px;
                font-style: normal;
                font-weight: 500;
                text-align: left;
                margin-bottom: 12px;
            }

            .headerContent {
                color: #666;
                font-size: 15px;
                font-style: normal;
                font-weight: 400;
                line-height: 25px;
                text-align: left;
            }
        }
    }
    .detailBox:first-of-type{margin-top: 0;}
    .detailBox {
        background: #FFF;
        margin-top: 12px;
        flex-direction: column;
        gap: 12px;
        border-radius: 8px;

        .date {
            height: 44px;
            flex-shrink: 0;
            box-sizing: border-box;
            padding-left: 15px;
            border-bottom: 1px solid #F1F1F1;
            text-align: left;

            p {
                color: #666;
                font-size: 15px;
                font-style: normal;
                font-weight: 500;
                line-height: 44px;
                text-align: left;
            }
        }

        .content {
            // height: 319px;
            box-sizing: border-box;
            padding: 0 15px;
            display: flex;
            .content_box_left .detail:last-of-type{border: none;}
            .content_box_left {
                width: calc(100% - 80px);
                // height: 319px;

                .detail {
                    // height: 114px;
                    box-sizing: border-box;
                    padding: 16px 0;
                    border-bottom: 1px solid #F1F1F1;
                    display: flex;

                    .top {
                        width: 50%;
                        display: flex;
                        flex-direction: column;
                        // justify-content: space-between;

                        .left_top {
                            height: 20px;
                            line-height: 20px;
                            text-align: left;
                        }

                        .left_content {
                            // height: 28px;
                        }

                        .left_bottom {
                            // width: 56px;
                            // height: 16px;
                            display: flex;
                            align-items: center;
                            .left_tips {
                                width: max-content;
                                padding: 0 6px;
                                border-radius: 35px;
                                text-align: center;
                                font-size: 11px;
                                font-style: normal;
                                font-weight: 400;
                                line-height: 16px;
                            }

                            .redColor {
                                background: #FFEFF0;
                                border: 1px solid #FFB1B3;
                                color: #FF0019;
                            }

                            .buleColor {
                                border: 1px solid #A0C8FF;
                                background: #ECF4FF;
                                color: #057EFF;
                            }
                        }
                    }
                }

                .detail_box {
                    box-sizing: border-box;
                    padding: 16px 0;

                    .recordNum {
                        display: flex;
                        align-items: center;
                        padding: 8px 0;
                        .name {
                            color: #0A0A0A;
                            font-size: 20px;
                            font-style: normal;
                            font-weight: 500;
                            margin-right: 4px;
                            line-height: normal;
                        }

                        .unit {
                            height: 21px;
                            color: #84909A;
                            font-size: 15px;
                            font-style: normal;
                            font-weight: 400;
                            line-height: 21px;
                        }
                    }

                }

            }

            .content_box_right {
                width: 80px;
                // height: 319px;
                display: flex;
                flex-direction: column;
                justify-content: center;
                border-left: 1px solid #F1F1F1;

                .rightBox {
                    text-align: right;
                    // height: 82px;
                    display: flex;
                    flex-direction: column;
                    // justify-content: space-between;

                    .content_right_name {
                        height: 20px;
                        color: #586267;
                        text-align: right;
                        font-size: 15px;
                        font-style: normal;
                        font-weight: 400;
                        line-height: 20px;
                        margin-bottom: 12px;
                    }
                }
            }
        }
    }
    .redColor {
        color: #ff1f33 !important;
    }
    .blueColor{
        color: #008AFF !important;
    }
    .arrowRotate{
        transform: rotate(180deg);
    }
    .warnIcon{
        width: 20px;
        height: 20px;
    }
    .blueTag{
        color: #057EFF;
        border: 1px solid #A0C8FF;
        background: #ECF4FF;
    }
    .redTag{
        color: #FF0019;
        border: 1px solid #FFB1B3;
        background: #FFEFF0;
    }
    .greenTag{
        color: #07C060;
        border: 1px solid #07C060;
        background: #07c0601a;
    }
}
</style>