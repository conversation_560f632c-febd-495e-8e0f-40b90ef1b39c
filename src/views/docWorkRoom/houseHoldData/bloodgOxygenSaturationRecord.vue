<template>
    <div class="bloodgOxygenSaturationRecord">
        <!-- <div class="header">
            <div class="headerTips">血氧饱和度参考值</div>
            <div class="target">
                <p>
                    日常活动：一般>95%
                    <br>
                    夜间睡眠：一般>90%
                    <br>
                    需要关注：低于90%
                    <br>
                    数值越低风险越大
                </p>
            </div>
        </div> -->
        <bloodg-oxygen-saturation></bloodg-oxygen-saturation>
    </div>
</template>

<script>
import bloodgOxygenSaturation from './bloodgOxygenSaturation.vue'
export default {
    components: {
        'bloodg-oxygen-saturation': bloodgOxygenSaturation
    },
    data: () => {
        return {}
    },
    computed: {},
    watch: {},
    created() { },
    methods: {}
}
</script>

<style lang="scss" scoped>
.bloodgOxygenSaturationRecord {
    
    .header {
        text-align: left;
        background: white;
        border-radius: 8px;
        padding: 20px 14px;
        .headerTips {
            margin-bottom: 12px;
            color: #333;
            font-size: 17px;
            font-weight: 500;
        }

        .target {
            color: #666;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
        }
    }

}
</style>