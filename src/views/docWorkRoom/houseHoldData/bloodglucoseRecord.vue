<template>
    <div class="bloodglucose">
        <!-- <div class="standard">
            <div class="headLine">血糖正常范围</div>
            <div class="headContent">
                空腹：4.4-7.0mmol/L，
                <br>
                非空腹：4.4-10.0mmol/L
            </div>
        </div> -->
        <blood-glucose></blood-glucose>
    </div>
</template>

<script>
import bloodglucose from './bloodglucose.vue';

export default {
    components: {
        'blood-glucose': bloodglucose
    },
    data: () => {
        return {}
    },
    computed: {},
    watch: {},
    created() { },
    methods: {}
}
</script>

<style lang="scss" scoped>
.bloodglucose {
    overflow: hidden;
    box-sizing: border-box;
    // padding: 12px 15px;
    // background: #F5F5F5;

    .standard {
        padding: 20px 14px;
        background: white;
        border-radius: 8px;

        .headLine {
            color: #0A0A0A;
            font-size: 17px;
            font-style: normal;
            font-weight: 500;
            box-sizing: border-box;
            text-align: left;
        }

        .headContent {
            color: #333;
            text-align: justify;
            font-size: 15px;
            font-style: normal;
            font-weight: 400;
            line-height: 28px;
            box-sizing: border-box;
            margin-top: 16px;
        }
    }
}
</style>