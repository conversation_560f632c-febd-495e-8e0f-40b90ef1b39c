<template>
  <div class='bgTable'>
    <div class="legend">
      <span class="label">图例：</span>
      <div class="normalLegend">
        <div class="colorBlock"></div>
        <span>正常</span>
      </div>
      <div class="abnormalLegend">
        <div class="colorBlock"></div>
        <span>异常</span>
      </div>
    </div>
    <div class="tableBox">
      <table class="table">
        <thead>
          <tr>
            <td rowspan="2">检测日期</td>
            <td rowspan="2">凌晨</td>
            <td colspan="2">早餐</td>
            <td colspan="2">午餐</td>
            <td colspan="2">晚餐</td>
            <td rowspan="2">睡前</td>
          </tr>
          <tr>
            <td>餐前</td>
            <td>餐后</td>
            <td>餐前</td>
            <td>餐后</td>
            <td>餐前</td>
            <td>餐后</td>
          </tr>
        </thead>
        <tbody v-if="dataList.length > 0">
          <tr v-for="item in dataList" :key="item.date">
            <td class="date">{{ item.date }}</td>
            <td :style="{'color': item.value[8] && item.value[8].abnormal ? '#F05E5E' : '#333333'}">{{item.value[8] ? item.value[8].bg : ''}}</td>
            <td :style="{'color': item.value[1] && item.value[1].abnormal ? '#F05E5E' : '#333333'}">{{item.value[1] ? item.value[1].bg : ''}}</td>
            <td :style="{'color': item.value[2] && item.value[2].abnormal ? '#F05E5E' : '#333333'}">{{item.value[2] ? item.value[2].bg : ''}}</td>
            <td :style="{'color': item.value[3] && item.value[3].abnormal ? '#F05E5E' : '#333333'}">{{item.value[3] ? item.value[3].bg : ''}}</td>
            <td :style="{'color': item.value[4] && item.value[4].abnormal ? '#F05E5E' : '#333333'}">{{item.value[4] ? item.value[4].bg : ''}}</td>
            <td :style="{'color': item.value[5] && item.value[5].abnormal ? '#F05E5E' : '#333333'}">{{item.value[5] ? item.value[5].bg : ''}}</td>
            <td :style="{'color': item.value[6] && item.value[6].abnormal ? '#F05E5E' : '#333333'}">{{item.value[6] ? item.value[6].bg : ''}}</td>
            <td :style="{'color': item.value[7] && item.value[7].abnormal ? '#F05E5E' : '#333333'}">{{item.value[7] ? item.value[7].bg : ''}}</td>
          </tr>
        </tbody>
      </table>
    </div>
    <div class="loadMore" @click="loadMore">{{ bottomTips }}</div>
  </div>
</template>

<script>
import { getBgTable } from "@/api/docWorkRoom"
export default {
  data() {
    return {
      dataList: [],
      pageNo: 0,
      loading: true,
      bottomTips: '点击或上拉加载更多',
      source: ''
    }
  },
  methods: {
    loadMore(){
      if(!this.loading){
        this.getData()
      }
    },
    getData(){
      this.loading = true
      this.bottomTips = '加载中~'
      let obj = {
        page: this.pageNo += 1,
        page_size: 20,
        user_id: this.$route.query.patient_id || '',
        is_patient_platform: this.$route.query.source == 'docApp' ? '0' : '1'
      }
      getBgTable(obj).then(res=>{
        if(res.status == 0){
          let { list_table } = res.data
          this.dataList = this.dataList.concat(list_table)
          this.loading = false
        }else{
          this.loading = false
          this.$toast(res.msg)
        }
      })
    }
  },
  created() {
    
  },
  mounted() {
    let that = this
    window.onscroll = function(){
      //变量scrollTop是滚动条滚动时，距离顶部的距离
      var scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
      //变量windowHeight是可视区的高度
      var windowHeight = document.documentElement.clientHeight || document.body.clientHeight;
      //变量scrollHeight是滚动条的总高度
      var scrollHeight = document.documentElement.scrollHeight || document.body.scrollHeight;
      //滚动条到底部的条件
      if(scrollTop + windowHeight >= (scrollHeight - 100)){
        if(!that.loading){
          that.getData()
        }
      }
    }
    that.getData()
  }
}
</script>

<style lang='scss'>
.bgTable{
  .tableBox{
    // border-radius: 8px;
    background: white;
    overflow: hidden;
    
  }
  .legend{
    display: flex;
    align-items: center;
    font-size: 12px;
    color: #666666;
    padding-bottom: 12px;
    .label{
      color: #333333;
      font-size: 14px;
      line-height: normal;
    }
    .colorBlock{
      width: 12px;
      height: 12px;
      margin-right: 6px;
    }
    .normalLegend{
      display: flex;
      align-items: center;
      .colorBlock{
        background: #333333;
      }
    }
    .abnormalLegend{
      display: flex;
      align-items: center;
      margin-left: 16px;
      .colorBlock{
        background: #F05E5E;
      }
    }
  }
  .table{
    width: 100%;
    // margin-top: 30px;
    font-size: 12px;
    border-collapse: collapse;
    border-spacing: 0;
    
    td {
      vertical-align: middle;
      border: 1px solid #E7E7E7;
      padding: 12px 6px;
    }
    thead{
      color: #999999;
      line-height: 20px;
    }
    tbody{
      color: #0A0A0A;
      font-size: 12px;
      font-weight: 500;
    }
    .date{
      color: #333333;
      width: 20px;
    }
  }
  .loadMore{
    color: #333;
    font-size: 14px;
    line-height: normal;
    padding: 20px 0;
  }
}
</style>