<template>
  <div class="nebulized-medication-record">
    <div class="container">
      <div class="title">明细数据</div>
      <van-list v-model="loading" :finished="finished" finished-text="没有更多了~" @load="loadData">
        <div class="detail" v-for="(item, i) in list" :key="i">
          <div class="date">
            <p>{{ item.measdate ? item.measdate.replace(/\-/g, '/') : '' }}</p>
          </div>
          <div class="content" v-for="(v, j) in item.list" :key="j">
            <div class="content_box">
              <div class="content_left">
                <div class="content_left_name">雾化用药时长</div>
                <div class="content_left_num">
                  <span class="result">{{ v.nebuTime }}<span class="unit">{{ v.nebuUnit }}</span>
                    <van-icon :name="`arrow-${v.show_child ? 'up' : 'down'}`" @click="handleShowChild(i, j, v)" v-if="v.child && v.child.length > 0" />
                  </span>
                </div>
              </div>
              <div class="content_right">
                <div class="content_right_name">{{ v.measure_time }}</div>
                <div class="content_right_name mid">{{ v.measure_type_name }}</div>
                <div class="content_right_name">{{ v.measure_method_name }}</div>
              </div>
            </div>
          </div>
        </div>
      </van-list>
    </div>
  </div>
</template>

<script>
import { getNebuList } from '@/api/docWorkRoom'
export default {
  data() {
    return {
      loading: false,
      finished: false,
      page_no: 1,
      page_size: 15,
      origin_list: [],
      list: [],
    }
  },
  methods: {
    async loadData() {
      const res = await getNebuList({
        user_id: this.$route.query.patient_id || '',
        page_no: this.page_no,
        page_size: this.page_size,
        is_patient_platform: this.$route.query.source == 'docApp' ? '0' : '1'
      })
      if (res.status === 0) {
        const { current_page, total_page, measure_data } = res.data || {}

        if (measure_data && measure_data.length > 0) {
          const list = this.formatMeasureDate(measure_data)
          this.origin_list = this.origin_list.concat(list)
          this.list = this.groupData(this.origin_list)

          this.page_no += 1
        }

        if (current_page == total_page || total_page === 0) {
          this.finished = true
        }
      } else {
        this.$toast(res.msg)
      }

      this.loading = false
    },
    formatMeasureDate(data) {
      let list = []
      data.forEach(item => {
        list.push(...item.list)
      })
      return list
    },
    handleShowChild(i, j, v) {
      const child = v.child
      const list = this.list[i].list
      if (this.list[i].list[j].show_child) {
        this.list[i].list[j].show_child = 0
        list.splice(j + 1, child.length)
        this.list[i].list = list
      } else {
        this.list[i].list[j].show_child = 1
        list.splice(j + 1, 0, ...child)
        this.list[i].list = list
      }
    },
    groupData(arr) {
      if (!Array.isArray(arr) || arr.length == 0) {
        return
      }
      let groupObj = {}
      for (const item of arr) {
        if (item.abnormal) {
          item.abnormalInfo = item.abnormal_info[0]
        }
        if (groupObj[item.measdate]) {
          groupObj[item.measdate].push(item)
        } else {
          groupObj[item.measdate] = [item]
        }
      }
      let resArr = []
      Object.keys(groupObj).forEach(item => {
        let obj = {}
        obj.measdate = item
        obj.list = groupObj[item]
        resArr.push(obj)
      })
      return resArr
    },
  }
}
</script>

<style lang="scss" scoped>
.nebulized-medication-record {
  padding: 20px 14px;
  border-radius: 8px;
  background-color: #fff;
  .container {
    > .title {
      font-size: 17px;
      color: #0a0a0a;
      font-weight: 500;
      text-align: left;
    }
    .detail {
      .date {
        height: 35px;
        line-height: 35px;
        text-align: left;
        font-size: 15px;
        font-weight: 500;
        color: #666;
        margin-top: 16px;
      }
      .header {
        height: 35px;
        flex-shrink: 0;
        background: #f5f5f5;
        box-sizing: border-box;
        padding: 7px 0 7px 15px;
        text-align: left;
        p {
          font-size: 15px;
          font-style: normal;
          font-weight: 500;
          line-height: 21px;
          text-align: left;
        }
      }
      .content {
        .content_box {
          padding: 16px 0;
          box-sizing: border-box;
          border-bottom: 1px solid #f1f1f1;
          display: flex;
          justify-content: space-between;
          .content_left {
            text-align: left;
            .content_left_name {
              height: 21px;
              font-size: 15px;
              font-style: normal;
              font-weight: 400;
              line-height: 21px;
              color: #0a0a0a;
            }
            .content_left_num {
              display: flex;
              height: 28px;
              line-height: 28px;
              font-style: normal;
              margin: 10px 0;
              .result {
                font-size: 20px;
                font-weight: 500;
                color: #0a0a0a;
                .unit {
                  margin-left: 4px;
                  font-size: 15px;
                  color: #999;
                }
                .van-icon {
                  padding: 4px;
                  font-size: 15px;
                  color: #999;
                }
              }
              .redColor {
                color: #ff1f33;
              }
            }
          }
          .content_right {
            text-align: right;
            .content_right_name {
              height: 20px;
              color: #666;
              font-size: 15px;
              font-weight: 400;
              line-height: 20px;
              &.mid {
                margin: 6px 0;
              }
            }
          }
        }
      }
    }
  }
}
</style>