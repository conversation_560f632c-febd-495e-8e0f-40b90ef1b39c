<template>
    <div class="peakFlowRateTrend">
        <div class="peakFlowRateBox">
            <div class="pieBox">
                <div class="daysPicker">
                    <div
                    class="daysItem"
                    :class="[daysChekedIndex == index ? 'daysChecked' : '']"
                    v-for="(item, index) in daysPicker"
                    :key="index"
                    @click="daysSelect(index, item)"
                    >
                    {{ item.type }}
                    </div>
                </div>
                <div class="dateLine">
                    <div class="icon">
                        <van-icon name="clock-o" size="15px" />
                        <span> {{ startDate }} ~ {{ endDate }}</span>
                    </div>
                    <!-- <img src="../imgs/fullScreen.png" alt="" /> -->
                </div>
                <div class="chartBlock">
                    <div class="chart" ref="chart"></div>
                    <div class="avgNum">平均 {{ avgNum }} 步/天</div>
                </div>
            </div>
        </div>
        <div class="bottomTips">我是有底线的</div>
    </div>
</template>

<script>
import { getStepHistogram,getBpConfig } from '@/api/docWorkRoom';
import * as echarts from 'echarts';

export default {
    data: () => {
        return {
            startDate: '',
            endDate: '',
            daysChekedIndex: '',
            daysPicker: [],
            avgNum: 0
        }
    },
    computed: {},
    watch: {},
    created() {
    },
    mounted() {
        this.getBpConfig()
    },
    methods: {
        getBpConfig(){
            let obj = {
                is_patient_platform: this.$route.query.source == 'docApp' ? '0' : '1'
            }
            getBpConfig(obj).then(res=>{
                if(res.status == 0){
                    this.daysPicker = res.data.date_type
                    this.startDate = res.data.date_type[0].start_date.replace(/-/g,'/')
                    this.endDate = res.data.date_type[0].end_date.replace(/-/g,'/')
                    this.getData()
                }else{
                    this.$toast(res.msg)
                }
            })
        },
        getData(){
            let obj = {
                start_time: this.startDate.replace(/\//g,'-'),
                end_time: this.endDate.replace(/\//g,'-'),
                user_id: this.$route.query.patient_id || '',
                is_patient_platform: this.$route.query.source == 'docApp' ? '0' : '1',
                source: this.$route.query.source
            }
            getStepHistogram(obj).then(res=>{
                if(res.status == 0){
                    this.avgNum = res.data.avg
                    this.$nextTick(()=>{
                        this.chartInit(res.data.list)
                    })
                }
            })
        },
        daysSelect(index,item){
            this.daysChekedIndex = index
            this.startDate = item.start_date.replace(/-/g,'/')
            this.endDate = item.end_date.replace(/-/g,'/')
            this.getData()
        },
        chartInit(info) {
            let myEchart = echarts.init(this.$refs.chart, 'light')
            const options = {
                grid:{
                    right: '0',
                    top: '20px',
                    bottom: '0',
                    left: '0',
                    containLabel: true,
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'none' // 指示器类型：'line' | 'shadow' | 'none'
                    },
                },
                xAxis: {
                    type: 'category',
                    data: info.map(item=>item.date),
                    axisLine: {
                        color: 'rgba(0,0,0,0.15)',
                        lineStyle: {
                            color: '#878F99'
                        }
                    },
                },
                yAxis: {
                    type: 'value',
                    // 不显示Y轴轴线  
                    axisLine: {  
                        show: false,
                        lineStyle: {
                        color: '#878F99'  //Y轴文字颜色
                        }
                    },  
                    axisTick: {
                        show: false  
                    },  
                },
                series: [
                    {
                        data: info.map(item=>item.num),
                        type: 'bar',
                        itemStyle: {
                            // color: '#FC9547'
                        },
                        markLine: {  
                            silent: true, // 不响应和触发鼠标事件  
                            symbol: ['none','none'],
                            data: [  
                                {  
                                    yAxis: this.avgNum, // 设置标准线的Y轴值  
                                    lineStyle: {  
                                        color: '#FFD0AC', // 线条颜色  
                                        type: 'dashed', // 线条类型，虚线  
                                        width: 1 // 线条宽度  
                                    },  
                                    // 如果你不需要显示标签，可以省略以下label配置  
                                    label: {  
                                        show: true, // 显示标签  
                                        position: 'end', // 标签位置  
                                        formatter: '' // 自定义标签文本  
                                    }  
                                }  
                            ]  
                        }  
                    }
                ]
            };
            myEchart.setOption(options)
        },
    }
}
</script>

<style lang="scss" scoped>
.peakFlowRateTrend {
    .daysPicker {
      font-family: "PingFang SC";
      height: 34px;
      line-height: 34px;
      border-radius: 30px;
      display: flex;
      background: #ecf5ff;
      font-size: 15px;
      font-weight: 500;
      padding: 3px;
      align-items: center;
      .daysItem {
        flex: 1;
        color: #909399;
      }
      .daysChecked {
        background: white;
        border-radius: 30px;
        color: #377cfb;
      }
    }
    .noDataText{
        color: #999999;
        font-size: 15px;
        text-align: center;
        width: 100%;
    }
    .peakFlowRateBox {
        background: white;
        border-radius: 8px;
        padding: 20px 14px;

    }
    .chartBlock{
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-top: 24px;
      width: 100%;
      height: 300px;
      .chart{
        width: 100%;
        height: 100%;
      }
    }
    .avgNum{
        color: #333;
        text-align: center;
        font-size: 15px;
        font-weight: 500;
        line-height: normal;
        margin-top: 26px;
    }
    .dateLine{
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 26px;
        span{
            color: #333333;
            font-size: 15px;
            font-weight: 500;
        }
        img{
            width: 20px;
            height: 20px;
        }

    }
    .bottomTips{
        color: #999999;
        font-size: 12px;
        line-height: normal;
        margin-top: 12px;
    }
}
</style>