<template>
  <div class="wheezingSound">
    <div class="inner">
      <div class="innerTitle">明细数据</div>
      <van-list v-model="loading" :finished="finished" finished-text="没有更多了~" @load="getWheezesList">
        <div class="detail" v-for="(item, index) in list" :key="index">
          <div class="date">
            <p>{{ item.group_date }}</p>
          </div>
          <div class="content" v-for="(v, ind) in item.list" :key="ind">
            <div class="content_box">
              <div class="content_left">
                <div class="content_left_name">哮鸣音检测</div>
                <div class="content_left_num">
                  <span :class="`result result_${v.abnormal}`">{{ v.result_content }}</span>
                </div>
                <div class="content_left_tips" v-if="v.abnormal === 1">
                  <div class="showBox">
                    <div class="left_tips" style="text-align: center">{{ v.abnormalInfo.status_display }}</div>
                  </div>
                </div>
              </div>
              <div class="content_right">
                <div class="content_right_name">{{ formateTime(v.measuretimestamp) }}</div>
                <div class="content_right_name mid">{{ v.measure_type_name }}</div>
                <div class="content_right_name">{{ v.measure_method_name }}</div>
              </div>
            </div>
          </div>
        </div>
      </van-list>
    </div>
  </div>
</template>

<script>
import { getWheezesfatList } from "@/api/docWorkRoom";
export default {
  data: () => {
    return {
      loading: false,
      finished: false,
      page_no: 1,
      page_size: 15,
      origin_list: [],
      list: [],
    };
  },
  methods: {
    async getWheezesList() {
      const res = await getWheezesfatList({
        user_id: this.$route.query.patient_id || '',
        page_no: this.page_no,
        page_size: this.page_size,
        is_patient_platform: this.$route.query.source == 'docApp' ? '0' : '1'
      })
      if (res.status === 0) {
        const { current_page, total_page, measure_data } = res.data || {}

        if (measure_data && measure_data.length > 0) {
          this.origin_list = this.origin_list.concat(measure_data)
          this.list = this.groupData(this.origin_list)

          this.page_no += 1
        }

        if (current_page === total_page || total_page === 0) {
          this.finished = true
        }
      } else {
        this.$toast(res.msg)
      }

      this.loading = false
    },
    groupData(arr) {
      if (!Array.isArray(arr) || arr.length == 0) {
        return
      }
      let groupObj = {}
      for (const item of arr) {
        if (item.abnormal) {
          item.abnormalInfo = item.abnormal_info[0]
        }
        if (groupObj[item.group_date]) {
          groupObj[item.group_date].push(item)
        } else {
          groupObj[item.group_date] = [item]
        }
      }
      let resArr = []
      Object.keys(groupObj).forEach(item => {
        let obj = {}
        obj.group_date = item
        obj.list = groupObj[item]
        resArr.push(obj)
      })
      return resArr
    },
    formateTime(ts) {
      if (ts)  {
        return ts.split(' ')[1].slice(0, 5)
      }
      return ''
    },
  },
};
</script>

<style lang="scss" scoped>
.wheezingSound {
  .inner:first-of-type {
    margin-top: 0;
  }
  .inner {
    border-radius: 8px;
    background: #fff;
    padding: 20px 14px;
    margin-top: 16px;
    .innerTitle {
      font-size: 17px;
      color: #0a0a0a;
      font-weight: 500;
      text-align: left;
    }
    .date {
      height: 35px;
      line-height: 35px;
      text-align: left;
      font-size: 15px;
      font-weight: 500;
      color: #666;
      margin-top: 16px;
    }
  }
  .detail {
    .header {
      height: 35px;
      flex-shrink: 0;
      background: #f5f5f5;
      box-sizing: border-box;
      padding: 7px 0 7px 15px;
      text-align: left;
      p {
        font-size: 15px;
        font-style: normal;
        font-weight: 500;
        line-height: 21px;
        text-align: left;
      }
    }
    .content {
      .content_box {
        padding: 16px 0;
        box-sizing: border-box;
        border-bottom: 1px solid #f1f1f1;
        display: flex;
        justify-content: space-between;
        .content_left {
          text-align: left;
          .content_left_name {
            height: 21px;
            font-size: 15px;
            font-style: normal;
            font-weight: 400;
            line-height: 21px;
            color: #0a0a0a;
          }
          .content_left_num {
            display: flex;
            height: 28px;
            line-height: 28px;
            font-style: normal;
            margin: 10px 0;
            .result {
              font-size: 20px;
              font-weight: 500;
              color: #0a0a0a;
              &.result_0 {
                color: #0a0a0a;
              }
              &.result_1 {
                color: #F53F3F;
              }
            }
            .redColor {
              color: #ff1f33;
            }
          }
          .content_left_tips {
            height: 20px;
            .showBox {
              display: flex;
              align-items: center;
              .left_tips {
                width: 34px;
                height: 16px;
                border-radius: 35px;
                border: 1px solid #ffb1b3;
                background: #ffeff0;
                color: #ff0019;
                text-align: center;
                font-size: 11px;
                font-style: normal;
                font-weight: 400;
                line-height: 16px;
              }
            }
          }
        }
        .content_right {
          text-align: right;
          .content_right_name {
            height: 20px;
            color: #666;
            font-size: 15px;
            font-weight: 400;
            line-height: 20px;
            &.mid {
              margin: 5px 0;
            }
          }
        }
      }
    }
  }
}
</style>