<template>
    <div class="peakFlowRateRecord">
        <!-- <div class="header">
            <div class="tips">
                <div class="headerTips">
                    PEF正常范围
                </div>
                <div class="headerContent">
                    您的正常预计值为：362
                    <br />
                    呼气峰值（PEF），是指用力呼气时的最高流量（L/min，又称最高（大）呼气流量，呼气峰流量（速）等。
                </div>
            </div>
        </div> -->
        <peak-flow-rate></peak-flow-rate>
    </div>
</template>

<script>
import { getPefList } from '@/api/docWorkRoom.js';
import peakFlowRate from './peakFlowRate.vue';
export default {
    components: {
        'peak-flow-rate': peakFlowRate
    },
    data: () => {
        return {
            
        }
    },
    computed: {},
    watch: {},
    created() { },
    methods: {

    }
}
</script>

<style lang="scss" scoped>
.peakFlowRateRecord {
    // overflow: hidden;
    // background: #F5F5F5;

    .header {

        .tips {
            border-radius: 8px;
            background: #FFF;
            padding: 20px 14px;
            text-align: left;

            .headerTips {
                color: #0A0A0A;
                font-size: 17px;
                font-weight: 500;
                margin-bottom: 12px;
            }

            .headerContent {
                height: 100px;
                color: #666;
                font-size: 15px;
                font-style: normal;
                font-weight: 400;
                line-height: 25px;
            }
        }
    }

}
</style>