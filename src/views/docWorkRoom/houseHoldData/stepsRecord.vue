<template>
    <div class="stepsRecord">
        <div class="stepsBox">
            <div class="recordTitle">
                明细数据
            </div>
            <van-list v-model="loading" :finished="finished" finished-text="没有更多了~" @load="onLoad">
                <div class="detail" v-for="(item, index) in dataList" :key="index">
                    <div class="header">
                        <p>{{ item.motion_at }}</p>
                    </div>
                    <div class="content">
                        <div class="content_box">
                            <div class="content_left box">
                                <div class="content_left_name">
                                    步数
                                </div>
                                <div class="content_left_num">
                                    <div class='num'>{{ item.step }}</div>
                                    <div class='unit'>步</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </van-list>
        </div>
    </div>
</template>

<script>
import { getmotionList } from '@/api/docWorkRoom';
import moment from 'moment'
export default {
    data: () => {
        return {
            dataList: [],
            loading: false,
            finished: false,
            page_no: 0,
            page_size: 15,
        }
    },
    computed: {},
    watch: {},
    created() { },
    methods: {
        onLoad() {
            let that = this
            let obj = {
                product_id: 15,
                page_size: this.page_size,
                page_no: this.page_no += 1,
                user_id: this.$route.query.patient_id || '',
                is_patient_platform: this.$route.query.source == 'docApp' ? '0' : '1',
                source: this.$route.query.source
            }
            getmotionList(obj).then(res=>{
                if(res.status == 0){
                    let {motion_data,total_page} = res.data
                    if(that.page_no == 1){
                        let date = moment(new Date()).format('YYYY/MM/DD')
                        if(motion_data.length == 0 || motion_data[0].motion_at != date){
                            that.$parent.isStepAuthBlockShow = true
                        }else{
                            that.$parent.isStepAuthBlockShow = false
                        }
                    }
                    that.dataList = that.dataList.concat(motion_data)
                    that.loading = false
                    if (that.page_no >= total_page) {
                        that.finished = true
                    } else {
                        that.finished = false
                    }
                }
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.stepsRecord {
    // overflow: hidden;
    // box-sizing: border-box;
    padding: 20px 14px;
    background: white;
    border-radius: 8px;
    .stepsBox {

        .recordTitle {
            color: #0A0A0A;
            font-size: 17px;
            font-weight: 500;
            text-align: left;
        }

        .detail {
            .header {
                p{
                    text-align: left;
                    font-size: 15px;
                    font-weight: 500;
                    margin-top: 16px;
                    color: #666666;
                }
            }

            .content {
                // height: 114px;
                box-sizing: border-box;
                padding: 15px 0;
                border-bottom: 1px solid #F1F1F1;
                .content_box {
                    box-sizing: border-box;
                    height: 56px;
                    display: flex;
                    justify-content: space-between;

                    .box {
                        display: flex;
                        flex-direction: column;
                        justify-content: space-between;
                    }

                    .content_left {
                        text-align: left;

                        .content_left_name {
                            height: 20px;
                            font-size: 15px;
                            font-style: normal;
                            font-weight: 500;
                            line-height: 20px;

                        }

                        .content_left_num {
                            display: flex;
                            height: 28px;
                            line-height: 28px;
                            font-weight: 700;
                            font-style: normal;

                            .num {
                                font-size: 20px;
                                font-weight: 500;
                            }

                            .unit {
                                color: #999;
                                font-size: 15px;
                                font-style: normal;
                                font-weight: 400;
                                line-height: 28px;
                                margin-left: 4px;
                            }
                        }

                        .content_left_tips {
                            // height: 20px;

                            .showBox {
                                display: flex;
                                align-items: center;

                                img {
                                    width: 20px;
                                    height: 20px;
                                }

                                .left_tips {
                                    width: 34px;
                                    height: 16px;
                                    border-radius: 35px;
                                    border: 1px solid #FFB1B3;
                                    background: #FFEFF0;
                                    color: #FF0019;
                                    text-align: center;
                                    font-size: 11px;
                                    font-style: normal;
                                    font-weight: 400;
                                    line-height: 16px;
                                }
                            }
                        }
                    }

                    .content_right {
                        text-align: right;

                        .content_right_name {
                            height: 20px;
                            color: #586267;
                            text-align: right;
                            font-family: "PingFang SC";
                            font-size: 15px;
                            font-style: normal;
                            font-weight: 400;
                            line-height: 20px;
                        }
                    }
                }
            }
        }
    }

}
</style>