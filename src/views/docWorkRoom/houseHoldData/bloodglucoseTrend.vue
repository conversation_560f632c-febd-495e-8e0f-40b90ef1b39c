<template>
  <div class="peakFlowRateTrend">
    <div class="peakFlowRateBox">
      <div class="pieBox">
        <div class="daysPicker">
          <div
            class="daysItem"
            :class="[daysChekedIndex == index ? 'daysChecked' : '']"
            v-for="(item, index) in daysPicker"
            :key="index"
            @click="daysSelect(index, item)"
          >
            {{ item.type }}
          </div>
        </div>
        <div class="date-seven" v-if="daysCheckedId == '7Report'">
          <div @click="preStage" class="date-arrow">
            <van-icon name="arrow-left" class="arrow-icon" />
            <span class="arrow-text">上阶段</span>
          </div>
          <div class="date-comp">
            <div class="date-comp-cont">
              <span>{{ dateFormat(startDate, ".") }}</span>
              <b class="date-comp-sign">-</b>
              <span>{{ dateFormat(endDate, ".") }}</span>
              <cc-svg-icon icon-class="date" class="date-comp-icon" />
            </div>
            <input
              type="date"
              v-model="inputDate"
              :max="dateFormat(new Date())"
              @change="handleInputDate"
              class="date-comp-input"
            />
          </div>
          <div @click="nextStage" class="date-arrow">
            <span class="arrow-text" :style="{ color: nextClick ? '' : '#ccc' }"
              >下阶段</span
            >
            <van-icon
              name="arrow"
              class="arrow-icon"
              :style="{ color: nextClick ? '' : '#ccc' }"
            />
          </div>
        </div>
        <div class="pieBlock">
          <template v-if="pieLabelList.length > 0">
            <div class="pieChart" ref="pieChart"></div>
            <div class="typeList">
              <div class="line lineTitle">
                <span class="label">测量情况</span>
                <span class="val">比例</span>
              </div>
              <div class="line" v-for="item in pieLabelList" :key="item.status_id">
                <div class="label">
                  <div class="circle normal" :style="{ 'background': item.color }"></div>
                  <span class="labelText">{{ item.status_display }}({{ item.count }})</span>
                </div>
                <span class="val">{{ item.proportion }}</span>
              </div>
            </div>
          </template>
          <span class="noDataText" v-else>无测量数据</span>
        </div>
      </div>

      <div class="maximumAndMinimumValues" v-if="JSON.stringify(maxData) != '{}' || JSON.stringify(minData) != '{}'">
        <div class="valueBox">
          <div class="valueBoxTop">
            <p class="valueTitle">最高值</p>
            <p class="valueDate">{{ maxData.measure_at && maxData.measure_at.split(' ')[0].replace(/-/g,'/') }}</p>
          </div>
          <div class="valueBoxBottom">
            <div class="title">{{ maxData.dining_status_display }}</div>
            <van-progress
              :percentage="getPercent(maxData.value)"
              stroke-width="8"
              :show-pivot="false"
              :color="maxData.abnormal_info && maxData.abnormal_info.length > 0 ? abnormalLineColor : normalLineColor"
              track-color="#F5F5F5"
            />
            <p class="num" :class="[maxData.abnormal_info && maxData.abnormal_info.length > 0 ? 'abnormalColor' : 'normalColor']">{{ maxData.value }}</p>
          </div>
        </div>
        <div class="valueBox minimumValue">
          <div class="valueBoxTop">
            <div class="valueTitle">最低值</div>
            <div class="valueDate">{{ minData.measure_at && minData.measure_at.split(' ')[0].replace(/-/g,'/') }}</div>
          </div>
          <div class="valueBoxBottom">
            <div class="title">{{ minData.dining_status_display }}</div>
            <van-progress
              :percentage="getPercent(minData.value)"
              stroke-width="8"
              :show-pivot="false"
              :color="minData.abnormal_info && minData.abnormal_info.length > 0 ? abnormalLineColor : normalLineColor"
              track-color="#F5F5F5"
            />
            <p class="num" :class="[minData.abnormal_info && minData.abnormal_info.length > 0 ? 'abnormalColor' : 'normalColor']">{{ minData.value }}</p>
          </div>
        </div>
      </div>
      <div class="averageValue" v-else>
        <div class="symptomTitle">最大值/最小值</div>
        <div class="noDataText">无测量数据</div>
      </div>
      <div class="lineBox" v-if="lineOriginData.length > 0">
        <div class="timeSlot" style="margin-bottom: 12px">
            <div
              class="timeItem"
              v-for="item in avgDataArr"
              :key="item.dining_status"
              :class="{ timeChecked: diningStatus == item.dining_status }"
              @click="timeSlotChange(item)">{{item.dining_status_display}}</div>
          </div>
        <div class="date">
          <div class="icon">
            <van-icon name="clock-o" size="15px" />
            {{ startDate }} ~ {{ endDate }}
          </div>
          <!-- <img src="../imgs/fullScreen.png" alt="" /> -->
        </div>
        <div class="lineMain">
          <div ref="lineChart" class="lineEchartsBox"></div>
        </div>
        <!-- <div class="annotate">
          图表中“&nbsp;
          <div class="three"></div>
          &nbsp;”表示收缩压正常范围区域；
        </div> -->
      </div>

      <div class="averageValue">
        <div class="symptomTitle">各时段平均值</div>
        <div class="symptomContent" v-if="avgDataArr.length > 0">
          <div class="valueBoxBottom" v-for="item in avgDataArr" :key="item.dining_status">
            <p class="title">{{ item.dining_status_display }}</p>
            <van-progress
              :percentage="getPercent(item.avg)"
              stroke-width="8"
              :show-pivot="false"
              color="linear-gradient(to right,#98CBFF, #008AFF)"
              track-color="#F5F5F5"
            />
            <p class="num" style="color: #0a0a0a">{{ item.avg }}</p>
          </div>
        </div>
        <div class="noDataText" v-else>无测量数据</div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts"
import moment from 'moment'
import { getBpConfig,getBgPie,getBgLine } from "@/api/docWorkRoom"
export default {
  data: () => {
    return {
      normalLineColor: 'linear-gradient(to right, #A4E9C5, #07C060)',
      abnormalLineColor: 'linear-gradient(to right, #FFBEC2, #FF525A)',
      active: "7",
      lastDate: "",
      choosed: 0,
      daysChekedIndex: 0,
      daysCheckedId: "",
      daysPicker: [],
      startDate: "",
      endDate: "",
      nextClick: false,
      diningStatus: '',
      pieLabelList: [],
      maxData: {},
      minData: {},
      avgDataArr: [],
      lineOriginData: [],
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {
    this.getBpConfig()
  },
  methods: {
    timeSlotChange(item){
      this.diningStatus = item.dining_status
      this.$nextTick(()=>{
        setTimeout(()=>{
          this.lineChartInit()
        },500)
      })
    },
    handleInputDate() {
      this.endDate = this.inputDate ? moment(this.inputDate).format('YYYY/MM/DD') : moment(new Date()).format('YYYY/MM/DD')
      this.startDate = this.handleStage(6, this.endDate, "pre")
      this.getAllData()
    },
    dateFormat(date, sep = "-") {
      let oDate = new Date(date);
      let y = oDate.getFullYear();
      let m = oDate.getMonth() + 1;
      let d = oDate.getDate();
      if (m < 10) m = `0${m}`;
      if (d < 10) d = `0${d}`;
      return `${y}${sep}${m}${sep}${d}`;
    },
    preStage() {
      this.startDate = this.handleStage(7,this.startDate,'pre')
      this.endDate = this.handleStage(7,this.endDate,'pre')
      this.nextClick = true
      this.getAllData()
    },
    nextStage() {
      if(!this.nextClick) return
      this.startDate = this.handleStage(7,this.startDate,'next')
      this.endDate = this.handleStage(7,this.endDate,'next')
      let todayDate = new Date()
      this.endDate == moment(todayDate).format('YYYY/MM/DD') ? this.nextClick = false : this.nextClick = true
      console.log(this.startDate,this.endDate,this.nextClick)
      this.getAllData()
    },
    handleStage(daysAgo,date,type){
      const currentDate = new Date(date);
      if(type == 'pre'){
        currentDate.setDate(currentDate.getDate() - daysAgo)
      }else{
        currentDate.setDate(currentDate.getDate() + daysAgo)
      }
      const year = currentDate.getFullYear()
      const month = String(currentDate.getMonth() + 1).padStart(2, '0')
      const day = String(currentDate.getDate()).padStart(2, '0')
      
      // if (format === 'YYYY-MM-DD') {
      //   return `${year}-${month}-${day}`
      // } else if (format === 'MM/DD/YYYY') {
      //   return `${month}/${day}/${year}`
      // }
      // 默认返回 YYYY-MM-DD 格式
      return `${year}/${month}/${day}`
    },
    formatDate(date) {
      let tempArr = date.split("-");
      let resDate = tempArr[1] + "/" + tempArr[2];
      return resDate;
    },
    getPercent(p){
      if(!p){
        return 0
      }
      let num = 0
      if(p > 15 || p === 15){
        num = 100
      }else{
        num = (p / 15) * 100
      }
      return num
    },
    getBgLineData(){
      let obj = {
        ding_status: '',
        start_time: this.startDate.replace(/\//g,'-'),
        end_time: this.endDate.replace(/\//g,'-'),
        user_id: this.$route.query.patient_id || '',
        is_patient_platform: this.$route.query.source == 'docApp' ? '0' : '1'
      }
      getBgLine(obj).then(res=>{
        if(res.status == 0){
          let {list_table} = res.data
          if(list_table.length == 0){
            this.lineOriginData = []
            return
          }
          this.lineOriginData = list_table
          this.$nextTick(()=>{
            setTimeout(()=>{
              this.lineChartInit()
            },500)
          })
        }
      })
    },
    getAllData(){
      this.getBgPieData()
      this.getBgLineData()
    },
    getBgPieData(){
      let obj = {
        ding_status: '',
        start_time: this.startDate.replace(/\//g,'-'),
        end_time: this.endDate.replace(/\//g,'-'),
        user_id: this.$route.query.patient_id || '',
        is_patient_platform: this.$route.query.source == 'docApp' ? '0' : '1'
      }
      getBgPie(obj).then(res=>{
        if(res.status == 0){
          let {abnormal_list,total_count,max,min,avg} = res.data
          if(total_count == 0){
            this.pieLabelList = []
            this.maxData = {}
            this.minData = {}
            this.avgDataArr = []
            return
          }
          this.maxData = max
          this.minData = min
          this.avgDataArr = avg
          this.diningStatus = avg[0].dining_status
          abnormal_list.forEach(item=>{
            item.name = item.status_display
            item.value = item.count
            item.color = item.status_id == 49 ? '#07C060' : '#FC6161'
          })
          this.pieLabelList = abnormal_list
          this.$nextTick(()=>{
            this.pieChartInit(res.data)
          })
        }
      })
    },
    getBpConfig() {
      let obj = {
        is_patient_platform: this.$route.query.source == 'docApp' ? '0' : '1'
      }
      getBpConfig(obj).then((res) => {
        if (res.status == 0) {
          this.startDate = res.data.date_type[0].start_date.replace(/-/g, "/");
          this.endDate = res.data.date_type[0].end_date.replace(/-/g, "/");
          this.daysPicker = res.data.date_type.concat({
            start_date: this.startDate,
            end_date: this.endDate,
            type: "7日报告",
            id: "7Report",
          });
          this.getAllData()
        } else {
          this.$toast(res.msg);
        }
      });
    },
    daysSelect(index,item){
      this.daysChekedIndex = index
      this.daysCheckedId = item.id
      this.startDate = item.start_date.replace(/-/g,'/')
      this.endDate = item.end_date.replace(/-/g,'/')
      this.getAllData()
      this.nextClick = false
      this.daysCheckedId == '7Report' ? this.showTable = true : this.showTable = false
    },
    // 折线图
    lineChartInit(){
      if(!this.$refs.lineChart) return
      let myEchart = echarts.init(this.$refs.lineChart, 'light')
      let options = {
          grid: {
            top: '40px',
            right: '20px',
            bottom: '20px',
            // left: '30px'
          },
          xAxis: {
          boundaryGap: false,
          type: 'category',
          axisLine: {
              color: 'rgba(0,0,0,0.15)',
              lineStyle: {
              color: '#878F99'
              }
          },
          data: this.lineOriginData.map(item=>item.date)
          },
          yAxis: {
          type: 'value',
          // 不显示Y轴轴线  
          axisLine: {  
              show: false,
              lineStyle: {
              color: '#878F99'  //Y轴文字颜色
              }
          },  
          // 不显示Y轴刻度线  
          axisTick: {
              show: false  
          },  
          // 显示Y轴标签  
          axisLabel: {  
              show: true  
          }  
          },
          tooltip: {
          trigger: 'axis',
          },
          series: [
              {   
                  // connectNulls: false,
                  name: '血糖',
                  type: 'line',
                  symbol: "circle",
                  showAllSymbol: true,
                  symbolSize: 4,
                  data: this.handleLineData(),
                  markArea: {
                      silent: true,
                  },
                  // lineStyle: {color: '#FC9547'},
                  // itemStyle: {color: '#FC9547'},
              }
          ]
      }
      myEchart.setOption(options)
    },
    handleLineData(){
      if(this.lineOriginData.length == 0) return []
      let res = []
      this.lineOriginData.forEach(item=>{
        let val = item.value[this.diningStatus] ? item.value[this.diningStatus].bg : ''
        res.push(val)
      })
      return res
    },
    // 饼状图
    pieChartInit(info) {
      let myEchart = echarts.init(this.$refs.pieChart, "light");
      const options = {
        tooltip: {
          show: false,
        },
        color: this.pieLabelList.map(item=>item.color),
        series: [
          {
            type: "pie",
            radius: ["80%", "100%"],
            avoidLabelOverlap: false,
            label: {
              show: true,
              position: "center",
              formatter: "{total|" + info.total_count + "}" + "\n\r" + "{active|总测量次数}",
              rich: {
                total: {
                  color: "#333333",
                  fontSize: 26,
                  lineHeight: 37,
                },
                active: {
                  color: "#999999",
                  fontSize: 12,
                  lineHeight: 17,
                },
              },
            },
            silent: true,
            emphasis: {
              show: true,
              scale: false,
            },
            labelLine: {
              show: false,
            },
            data: info.abnormal_list || []
          },
        ],
      };
      myEchart.setOption(options);
    },
  },
};
</script>

<style lang="scss" scoped>
.peakFlowRateTrend {
  min-height: 100vh;
  overflow: hidden;
  box-sizing: border-box;
  // padding: 11px 15px 0;
  // background: #F5F5F5;

  .peakFlowRateBox {
    .pieBox {
      box-sizing: border-box;
      // height: 234px;
      border-radius: 8px;
      background: #fff;
      padding: 20px 14px;
    }

    .maximumAndMinimumValues {
      height: 164px;
      border-radius: 10px;
      background: #fff;
      box-sizing: border-box;
      padding: 20px 14px;
      margin-top: 16px;
      .valueBox {
        height: 54px;

        .valueBoxTop {
          display: flex;
          justify-content: space-between;
          margin-bottom: 12px;

          .valueTitle {
            height: 20px;
            color: #0a0a0a;
            font-size: 17px;
            font-style: normal;
            font-weight: 700;
            line-height: 20px;
          }

          .valueDate {
            height: 20px;
            color: #666;
            font-size: 15px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px;
          }
        }
      }

      .minimumValue {
        margin-top: 24px;
      }
    }

    .lineBox {
      // height: 438px;
      border-radius: 8px;
      background: #fff;
      box-sizing: border-box;
      padding: 20px 14px;
      margin-top: 16px;

      .date {
        color: #3f4447;
        font-size: 15px;
        font-weight: 500;
        // margin-bottom: 25px;
        box-sizing: border-box;
        // padding: 0 15px;
        text-align: left;
        display: flex;
        justify-content: space-between;

        img {
          width: 20px;
          height: 20px;
        }
      }

      .lineMain {
        box-sizing: border-box;
        // padding: 0 20px;

        .lineEchartsBox {
          width: 100%;
          height: 280px;
        }
      }

      .annotate {
        margin-top: 17px;
        display: flex;
        height: 20px;
        color: #84909a;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
        box-sizing: border-box;
        // padding: 0 15px;

        .three {
          height: 12px;
          width: 12px;
          background: #cdf2df;
          line-height: 20px;
          margin: auto 0;
        }
      }
    }

    .averageValue {
      // height: 308px;
      box-sizing: border-box;
      padding: 20px 14px;
      border-radius: 8px;
      background: #fff;
      text-align: left;
      margin-top: 16px;
      .symptomTitle {
        color: #0a0a0a;
        font-size: 17px;
        font-style: normal;
        font-weight: 500;
        line-height: normal;
        margin-bottom: 16px;
      }

      .symptomContent {
        display: flex;
        flex-direction: column;
      }
    }
  }
  .valueBoxBottom:last-of-type{margin-bottom: 0;}
  .valueBoxBottom {
          display: flex;
          margin-bottom: 10px;
          .title {
            color: #666;
            font-size: 15px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
            width: 60px;
            text-align: left;
          }

          .van-progress {
            width: 220px;
            height: 10px;
            margin: auto 0;
          }

          .num {
            margin-left: 18px;

            font-size: 15px;
            font-style: normal;
            font-weight: 500;
            line-height: normal;
          }
        }
  .daysPicker {
    font-family: "PingFang SC";
    height: 34px;
    line-height: 34px;
    border-radius: 30px;
    display: flex;
    background: #ecf5ff;
    font-size: 15px;
    font-weight: 500;
    padding: 3px;
    align-items: center;
    .daysItem {
      flex: 1;
      color: #909399;
    }
    .daysChecked {
      background: white;
      border-radius: 30px;
      color: #377cfb;
    }
  }
  .date-seven {
    height: 28px;
    line-height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 12px -14px 0;
    .date-arrow {
      width: 60px;
      display: flex;
      color: #333333;
      align-items: center;
      justify-content: space-between;
      .arrow-text {
        font-size: 13px;
      }
      .arrow-icon {
        font-size: 18px;
      }
    }
    .date-comp {
      width: 200px;
      margin: 0 8px;
      color: #f67710;
      position: relative;
      .date-comp-cont {
        height: 25px;
        line-height: 25px;
        display: flex;
        font-size: 13px;
        color: #f67710;
        border-radius: 5px;
        align-items: center;
        justify-content: center;
        border: 1px solid #ffecdd;
        .date-comp-sign {
          margin: 0 2px;
        }
        .date-comp-icon {
          margin-left: 5px;
        }
      }
      .date-comp-input {
        width: 200px;
        height: 25px;
        font-size: 13px;
        text-align: center;
        border-radius: 5px;
        border: 1px solid #ffecdd;
        position: absolute;
        top: 0;
        left: 0;
        opacity: 0;
        z-index: 999999999;
      }
    }
  }
  .pieBlock {
    display: flex;
    align-items: center;
    margin-top: 24px;
    .pieChart {
      width: 110px;
      height: 110px;
      margin-left: 10px;
    }
    .typeList {
      display: flex;
      flex-direction: column;
      flex: 1;
      margin-left: 46px;
      margin-right: 10px;
      font-size: 15px;
      .line:first-of-type {
        margin-top: 0;
      }
      .line {
        display: flex;
        justify-content: space-between;
        margin-top: 18px;
        color: #666666;
        .label {
          display: flex;
          align-items: center;
        }
      }
      .lineTitle {
        color: #333333;
        font-weight: bold;
      }
      .circle {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 6px;
      }
    }
  }
  .normal {
    background: #07c060;
  }
  .height {
    background: #ff515a;
  }
  .low {
    background: #008aff;
  }
  .abnormalColor {
    color: #ff1f33;
  }
  .normalColor{
      color: #07c060;
  }
  .timeSlot{
    display: flex;
    flex-wrap: wrap;
    .timeItem{
      padding: 5px 14px;
      color: #999999;
      background: #F5F5F5;
      border-radius: 30px;
      font-size: 14px;
      border: 1px solid transparent;
      margin: 0 8px 6px 0;
    }
    .timeChecked{
      color: #3796fa;
      border: 1px solid #3796fa;
      background: rgba(55, 150, 250, 0.10);
    }
  }
  .noDataText{
    color: #999999;
    font-size: 15px;
    width: 100%;
    text-align: center;
  }
}
</style>
