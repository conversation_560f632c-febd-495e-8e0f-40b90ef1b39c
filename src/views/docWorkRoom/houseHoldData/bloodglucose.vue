<template>
  <div class="bloodglucoseDetail">
    <div class="headline">明细数据</div>
    <van-list
      v-model="loading"
      :finished="finished"
      finished-text="没有更多了~"
      @load="getList"
    >
      <div class="detail" v-for="(item, index) in dataList" :key="index">
        <div class="header">
          <p>{{ item.group_date }}</p>
        </div>
        <div class="content" v-for="(v, ind) in item.list" :key="ind">
          <div class="content_box">
            <div class="content_left box">
              <div class="content_left_name">
                {{ v.dining_status || '空腹' }}&nbsp;<van-icon @click="handleEdit(v)" v-if="v.z_bg_id != 0 && source != 'docApp'" name="edit" color="#999999" />
              </div>
              <div class="content_left_num">
                <div class="num" :class="[ v.abnormalInfo ? [1,3].includes(v.abnormalInfo.deflection) ? 'blueColor' : 'redColor' : '']">
                  {{ v.bg }}<van-icon v-if="v.abnormalInfo" name="down" :class="[ [1,3].includes(v.abnormalInfo.deflection) ? '' : 'arrowRotate']" :color="[1,3].includes(v.abnormalInfo.deflection) ? '#008AFF' : '#FF1F33' " />
                </div>
                <div class="unit">mmol/L</div>
              </div>
              <div class="bgWarn" v-if="v.abnormalInfo">
                <img class="warnIcon" v-if="v.abnormalInfo.deflection == 3" src="../imgs/blueWarning.png" alt="">
                <img class="warnIcon" v-if="v.abnormalInfo.deflection == 4" src="../imgs/warning.png" alt="">
                <div class="warnTag" :class="[[1,3].includes(v.abnormalInfo.deflection) ? 'blueTag' : 'redTag']">{{v.abnormalInfo.status_display}}</div>
              </div>
            </div>
            <div class="content_right box">
              <div class="content_right_name">
                {{ v.time }}
              </div>
              <div class="content_right_name">
                {{ v.data_type_name }}
              </div>
              <div v-if="v.is_normal != 1" class="content_right_name"></div>
            </div>
          </div>
          <div class="reasonLine" v-if="v.reason_type_name">
            <span class="label">异常原因：</span>
            <span class="val">{{ v.reason_type_name }}</span>
          </div>
          <div class="remarkLine" v-if="v.reason">
            <span class="label">备注：</span>
            <span class="val">{{ v.reason }}</span>
          </div>
        </div>
      </div>
    </van-list>
    <HandlePopup :show="isHandlePopupSow"></HandlePopup>
  </div>
</template>

<script>
import { getBgList,delBgOne,addReason } from "@/api/docWorkRoom.js";
import HandlePopup from "./components/handlePopup.vue"
export default {
    components: {
        HandlePopup
    },
  data: () => {
    return {
        originData: [],
        dataList: [],
        loading: false,
        finished: false,
        page_no: 0,
        page_size: 15,
        isHandlePopupSow: false,
        curItem: {},  //操作的当前条目
        source: ''
    };
  },
  computed: {},
  watch: {},
  created() {},
  methods: {
    getList() {
        this.source = this.$route.query.source
        let obj = {
            page_no: this.page_no += 1,
            page_size: this.page_size,
            user_id: this.$route.query.patient_id || '',
            is_patient_platform: this.$route.query.source == 'docApp' ? '0' : '1'
        }
        getBgList(obj).then((res) => {
            const { measure_data } = res.data
            this.originData = this.originData.concat(measure_data)
            this.dataList = this.groupData(this.originData)
            this.loading = false
            if(this.page_no >= res.data.total_page){
                this.finished = true
            }
          }
        )
    },
    groupData(arr){
      if(!Array.isArray(arr) || arr.length == 0){
        return
      }
      let groupObj = {};
      for (const item of arr) {
        if(item.abnormal){
            item.abnormalInfo = item.abnormal_info[0]
        }
        if (groupObj[item.group_date]) {
          groupObj[item.group_date].push(item);
        } else {
          groupObj[item.group_date] = [item];
        }
      }
      console.log(groupObj)
      let resArr = []
      Object.keys(groupObj).forEach(item=>{
        let obj = {}
        obj.group_date = item
        obj.list = groupObj[item]
        resArr.push(obj)
      })
      console.log(resArr)
      return resArr
    },
    handleEdit(value) {
      console.log(value)
      this.isHandlePopupSow = true
      this.curItem = value
    },
    delOne(){
      delBgOne({id: this.curItem.id}).then(res=>{
        if(res.status == 0){
          this.$toast('删除成功')
          this.isHandlePopupSow = false
          setTimeout(()=>{
            this.page_no = 0
            this.originData = []
            this.getList()
          },500)
        }else{
          this.$toast(res.msg)
          this.isHandlePopupSow = false
        }
      })
    },
    addReason(obj){
      let params = {
        ...obj,
        id: this.curItem.id,
        type: 1,  // 1:血糖 2:血压
        data_from_saas: 1
      }
      addReason(params).then(res=>{
        if(res.status == 0){
          this.$toast('添加成功')
          this.isHandlePopupSow = false
          setTimeout(()=>{
            this.page_no = 0
            this.originData = []
            this.getList()
          },500)
        }else{
          this.$toast(res.msg)
          this.isHandlePopupSow = false
        }
      })
    }
  },
};
</script>

<style lang="scss" scoped>
.bloodglucoseDetail {
  overflow: hidden;
  box-sizing: border-box;
  padding: 20px 14px;
  background: white;
  // margin-top: 16px;
  border-radius: 8px;
  .van-cell {
    padding: 0;
  }

  .van-action-sheet__close {
    right: -80px;
  }

  .headline {
    color: #0a0a0a;
    font-size: 17px;
    font-style: normal;
    font-weight: 500;
    box-sizing: border-box;
    text-align: left;
  }

  .detail {
    .header {
      text-align: left;
      margin-top: 16px;
      p {
        font-size: 15px;
        font-style: normal;
        font-weight: 500;
        text-align: left;
        color: #666666;
      }
    }

    .content {
      // min-height: 114px;
      box-sizing: border-box;
      padding: 16px 0;
      border-bottom: 1px solid #f1f1f1;
      .content_box {
        box-sizing: border-box;
        // border-bottom: 1px solid #F1F1F1;
        // max-height: 96px;
        display: flex;
        justify-content: space-between;
        // padding-bottom: 12px;

        .box {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
        }

        .content_left {
          text-align: left;

          .content_left_name {
            height: 21px;
            font-size: 15px;
            font-style: normal;
            font-weight: 400;
            line-height: 21px;
            display: flex;
            align-items: center;
            .edit {
              height: 16px;
              margin-left: 4px;
              margin-top: 3px;
            }
          }

          .content_left_num {
            display: flex;
            height: 28px;
            line-height: 28px;
            line-height: normal;
            // padding: 8px 0;
            align-items: center;
            .num {
              font-size: 20px;
              font-weight: 500;
              display: flex;
              align-items: center;
            }

            .redColor {
              color: #ff1f33;
            }
            .blueColor{
                color: #008AFF;
            }
            .unit {
              margin-left: 4px;
              color: #84909a;
              font-size: 15px;
              font-weight: 400;
            }
          }

          .bgWarn{
            height: 20px;
            display: flex;
            .warnIcon{
                width: 20px;
                height: 20px;
                // position: relative;
                // top: 2px;
            }
            .warnTag{
                font-size: 10px;
                border-radius: 10px;
                padding: 4px;
                margin-left: 2px;
            }
            .blueTag{
                color: #057EFF;
                border: 1px solid #A0C8FF;
                background: #ECF4FF;
            }
            .redTag{
                color: #FF0019;
                border: 1px solid #FFB1B3;
                background: #FFEFF0;
            }
          }
        }

        .content_right {
          text-align: right;

          .content_right_name {
            height: 28px;
            color: #586267;
            text-align: right;
            font-size: 15px;
            font-style: normal;
            font-weight: 400;
            line-height: 28px;
          }
        }
      }
      .reasonLine{
        margin-top: 12px;
      }
      .reasonLine,.remarkLine{
        display: flex;
        font-size: 15px;
        line-height: 20px;
        .label{
          color: #333333;
          // width: max-content;
        }
        .val{
          color: #666666;
          text-align: left;
          flex: 1;
        }
      }
      .remarkLine {
        margin-top: 12px;
      }
    }
  }
  .arrowRotate{
    transform: rotate(180deg);
  }
  
}
</style>