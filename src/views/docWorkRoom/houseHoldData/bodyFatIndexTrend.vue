<template>
    <div class="bodyFatIndexTrend">
        <div class="bodyFatIndexBox">
            <div class="pieBox">
                <div class="lineBox">
                    <div class="daysPicker">
                        <div
                        class="daysItem"
                        :class="[daysChekedIndex == index ? 'daysChecked' : '']"
                        v-for="(item, index) in daysPicker"
                        :key="index"
                        @click="daysSelect(index, item)"
                        >
                        {{ item.type }}
                        </div>
                    </div>
                    <div class="date-seven" v-if="daysCheckedId == '7Report'">
                        <div @click="preStage" class="date-arrow">
                        <van-icon name="arrow-left" class="arrow-icon"/>
                        <span class="arrow-text">上阶段</span>
                        </div>
                        <div class="date-comp">
                        <div class="date-comp-cont">
                            <span>{{ dateFormat(startDate, '.') }}</span>
                            <b class="date-comp-sign">-</b>
                            <span>{{ dateFormat(endDate, '.') }}</span>
                            <cc-svg-icon icon-class="date" class="date-comp-icon"/>
                        </div>
                        <input
                            type="date"
                            v-model="inputDate"
                            :max="dateFormat(new Date())"
                            @change="handleInputDate"
                            class="date-comp-input"
                        >
                        </div>
                        <div @click="nextStage" class="date-arrow">
                        <span class="arrow-text" :style="{ 'color': nextClick ? '' : '#ccc'}">下阶段</span>
                        <van-icon name="arrow" class="arrow-icon" :style="{ 'color': nextClick ? '' : '#ccc'}"/>
                        </div>
                    </div>
                    <div class="date">
                        <div class="leftDateRange">
                            <van-icon color="#878F99" class="clockIcon" name="clock-o" /> {{ startDate }} ~ {{ endDate }}
                        </div>
                        <!-- <img src="../imgs/fullScreen.png" alt=""> -->
                    </div>
                    <div v-if="resChartData.length > 0">
                        <div class="lineMain">
                            <div ref="lineChart" class="lineEchartsBox"></div>
                        </div>
                        <div class="btn">
                            <van-button :class="{ choosed: choosed == 'weight' }"
                                @click="changeChoosed('weight')">体重</van-button>
                            <van-button :class="{ choosed: choosed == 'bmi' }" style='margin-left: 24px;'
                                @click="changeChoosed('bmi')">肥胖指数(BMI)</van-button>
                        </div>
                    </div>
                    <div v-else class="noDataText" style="margin-top: 24px;">无测量数据</div>
                </div>

                <!-- <div class="BMI">
                    <div class="bmiNum">BMI：29.9</div>
                    <div class="bmiScale">
                        <img src="./imgs/scale.png" alt="">
                    </div>
                    <div class="bmiTip">
                        提示：通过体重可以辅助了解自身健康程度。若短时间内，体重变化过快需要进一步关注分析。
                    </div>
                </div> -->
            </div>
        </div>
        <div class="bottomTips">我是有底线的</div>
    </div>
</template>

<script>
import moment from 'moment'
import * as echarts from 'echarts';
import { getBpConfig,getBMILineChart } from '@/api/docWorkRoom'
export default {
    data: () => {
        return {
            inputDate: '',
            active: '7',
            lastDate: '',
            choosed: 'weight',
            startDate: '',
            endDate: '',
            daysPicker: [],
            daysCheckedId: '',
            daysChekedIndex: 0,
            nextClick: false,
            lineChartData: [],
            resChartData: []
        }
    },
    computed: {},
    watch: {},
    created() {
    },
    mounted() {
        this.getBpConfig()
    },
    methods: {
        getBMILineData(){
            let obj = {
                period: 0,
                start_at: this.startDate.replace(/\//g,'-'),
                end_at: this.endDate.replace(/\//g,'-'),
                user_id: this.$route.query.patient_id || '',
                is_patient_platform: this.$route.query.source == 'docApp' ? '0' : '1'
            }
            getBMILineChart(obj).then(res=>{
                if(res.status == 0){
                    this.lineChartData = res.data.period
                    this.resChartData = this.handleLineData().filter(item=> item != '')
                    if(this.resChartData.length == 0){
                        return
                    }
                    this.$nextTick(()=>{
                        this.getLineEcharts()
                    })
                }
            })
        },
        getAllData(){
            this.getBMILineData()
        },
        handleInputDate(){
            this.endDate = this.inputDate ? moment(this.inputDate).format('YYYY/MM/DD') : moment(new Date()).format('YYYY/MM/DD')
            this.startDate = this.handleStage(6,this.endDate,'pre')
            this.getAllData()
        },
        dateFormat(date, sep = '-') {
            let oDate = new Date(date)
            let y = oDate.getFullYear()
            let m = oDate.getMonth() + 1
            let d = oDate.getDate()
            if (m < 10) m = `0${m}`
            if (d < 10) d = `0${d}`
            return `${y}${sep}${m}${sep}${d}`
        },
        preStage() {
            this.startDate = this.handleStage(7,this.startDate,'pre')
            this.endDate = this.handleStage(7,this.endDate,'pre')
            this.nextClick = true
            this.getAllData()
        },
        nextStage() {
            if(!this.nextClick) return
            this.startDate = this.handleStage(7,this.startDate,'next')
            this.endDate = this.handleStage(7,this.endDate,'next')
            let todayDate = new Date()
            this.endDate == moment(todayDate).format('YYYY/MM/DD') ? this.nextClick = false : this.nextClick = true
            console.log(this.startDate,this.endDate,this.nextClick)
            this.getAllData()
        },
        handleStage(daysAgo,date,type){
            const currentDate = new Date(date);
            if(type == 'pre'){
                currentDate.setDate(currentDate.getDate() - daysAgo)
            }else{
                currentDate.setDate(currentDate.getDate() + daysAgo)
            }
            const year = currentDate.getFullYear()
            const month = String(currentDate.getMonth() + 1).padStart(2, '0')
            const day = String(currentDate.getDate()).padStart(2, '0')
            return `${year}/${month}/${day}`
        },
        daysSelect(index,item){
            this.daysChekedIndex = index
            this.daysCheckedId = item.id
            this.startDate = item.start_date.replace(/-/g,'/')
            this.endDate = item.end_date.replace(/-/g,'/')
            this.nextClick = false
            this.daysCheckedId == '7Report' ? this.showTable = true : this.showTable = false
            this.getAllData()
        },
        getBpConfig(){
            let obj = {
                is_patient_platform: this.$route.query.source == 'docApp' ? '0' : '1'
            }
            getBpConfig(obj).then(res=>{
                if(res.status == 0){
                    this.startDate = res.data.date_type[0].start_date.replace(/-/g,'/')
                    this.endDate = res.data.date_type[0].end_date.replace(/-/g,'/')
                    this.daysPicker = res.data.date_type.concat(
                        {
                            start_date: this.startDate,
                            end_date: this.endDate,
                            type: '7日报告',
                            id: '7Report'
                        }
                    )
                    this.getAllData()
                }else{
                    this.$toast(res.msg)
                }
            })
        },
        getLineEcharts(){
            let myEchart = echarts.init(this.$refs.lineChart, 'light')
            let options = {
                grid: {
                    right: '20px',
                    top: '8px',
                    bottom: '0',
                    left: '10px',
                    containLabel: true,
                    // borderColor: '#E7ECF4',
                },
                xAxis: {
                boundaryGap: false,
                type: 'category',
                axisLine: {
                    color: 'rgba(0,0,0,0.15)',
                    lineStyle: {
                    color: '#878F99'
                    }
                },
                data: this.lineChartData.map(item=>item.date)
                },
                yAxis: {
                type: 'value',
                // 不显示Y轴轴线  
                axisLine: {  
                    show: false,
                    lineStyle: {
                    color: '#878F99'  //Y轴文字颜色
                    }
                },  
                // 不显示Y轴刻度线  
                axisTick: {
                    show: false  
                },  
                // 显示Y轴标签  
                axisLabel: {  
                    show: true  
                }  
                },
                tooltip: {
                trigger: 'axis',
                },
                series: [
                    {   
                        // connectNulls: false,
                        name: this.choosed == 'weight' ? '体重' : 'BMI',
                        type: 'line',
                        showAllSymbol: true,
                        symbol: "circle",
                        symbolSize: 4,
                        data: this.handleLineData(),
                        markArea: {
                            silent: true,
                        },
                        // lineStyle: {color: '#FC9547'},
                        // itemStyle: {color: '#FC9547'},
                    }
                ]
            }
            myEchart.setOption(options)
        },
        handleLineData(){
            let res = []
            this.lineChartData.forEach(item=>{
                if(Array.isArray(item.data)){
                    res.push('')
                }else{
                    res.push(item.data[this.choosed])
                }
            })
            return res
        },
        changeChoosed(type) {
            this.choosed = type
            this.getLineEcharts()
        }
    }
}
</script>

<style lang="scss" scoped>
#app {
    background-color: red;
}

.bodyFatIndexTrend {

    .bodyFatIndexBox {

        .pieBox {
            box-sizing: border-box;
            border-radius: 8px;

            .lineBox {
                border-radius: 8px;
                background: #FFF;
                box-sizing: border-box;
                padding: 20px 14px;

                .date {
                    margin-top: 24px;
                    // margin-bottom: 25px;
                    box-sizing: border-box;
                    // padding: 0 15px;
                    text-align: left;
                    display: flex;
                    justify-content: space-between;

                    span {
                        margin-left: 4px;
                        color: #333;
                        font-size: 15px;
                        font-style: normal;
                        font-weight: 500;
                        line-height: 21px;
                    }

                    img {
                        width: 20px;
                        height: 20px;
                    }
                }

                .lineMain {
                    box-sizing: border-box;
                    // padding: 0 20px;
                    width: 100%;
                    margin-top: 16px;
                    .lineEchartsBox {
                        width: 100%;
                        height: 233px;
                    }
                }

                .btn {
                    margin-top: 24px;

                    .van-button--normal {
                        height: 30px;
                        padding: 5px 18px;
                        justify-content: center;
                        align-items: center;
                        gap: 10px;
                        border-radius: 30px;
                        background: #F6F6F6;
                    }

                    .van-button__content {
                        color: #999;
                        font-size: 14px;
                        font-style: normal;
                        font-weight: 400;
                        line-height: normal;
                    }

                    .choosed {
                        border-radius: 30px;
                        border: 1px solid #3796fa;
                        background: rgba(55, 150, 250, 0.10);
                        color: #3796fa;
                        font-size: 14px;
                        font-style: normal;
                        font-weight: 500;
                        line-height: normal;

                        .van-button__content {
                            color: #3796fa;
                        }
                    }
                }
            }

            .BMI {
                margin-top: 16px;
                border-radius: 8px;
                background: #FFF;
                box-sizing: border-box;
                padding: 20px 14px;

                .bmiNum {
                    color: #333;
                    font-size: 17px;
                    font-style: normal;
                    font-weight: 500;
                    line-height: normal;
                    text-align: left;
                    box-sizing: border-box;
                    // padding-left: 13px;
                }

                .bmiScale {
                    width: 100%;
                    img {
                        height: 55px;
                        margin: 16px 0;
                    }
                }

                .bmiTip {
                    text-align: left;
                    color: #999;
                    font-size: 14px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: normal;
                    box-sizing: border-box;
                    // padding: 0 20px 16px 16px;
                }
            }
        }

    }
    .daysPicker {
      font-family: "PingFang SC";
      height: 34px;
      line-height: 34px;
      border-radius: 30px;
      display: flex;
      background: #ecf5ff;
      font-size: 15px;
      font-weight: 500;
      padding: 3px;
      align-items: center;
      .daysItem {
        flex: 1;
        color: #909399;
      }
      .daysChecked {
        background: white;
        border-radius: 30px;
        color: #377cfb;
      }
    }
    .date-seven {
    height: 28px;
    line-height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 12px -14px 0;
    .date-arrow {
        width: 60px;
        display: flex;
        color: #333333;
        align-items: center;
        justify-content: space-between;
        .arrow-text {
            font-size: 13px;
        }
        .arrow-icon {
            font-size: 18px;
        }
    }
    .date-comp {
      width: 200px;
      margin: 0 8px;
      color: #F67710;
      position: relative;
      .date-comp-cont {
        height: 25px;
        line-height: 25px;
        display: flex;
        font-size: 13px;
        color: #F67710;
        border-radius: 5px;
        align-items: center;
        justify-content: center;
        border: 1px solid #ffecdd;
        .date-comp-sign {
          margin: 0 2px;
        }
        .date-comp-icon {
          margin-left: 5px;
        }
      }
      .date-comp-input {
        width: 200px;
        height: 25px;
        font-size: 13px;
        text-align: center;
        border-radius: 5px;
        border: 1px solid #ffecdd;
        position: absolute;
        top: 0;
        left: 0;
        opacity: 0;
        z-index: 999999999;
      }
    }
  }
  .dateRange {
      color: #3f4447;
      font-size: 15px;
      font-weight: 500;
      text-align: left;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .clockIcon {
        margin-right: 4px;
      }
    }
    .bottomTips{
        color: #999999;
        font-size: 12px;
        line-height: normal;
        margin-top: 12px;
    }
    .noDataText{
        color: #999999;
        font-size: 15px;
        text-align: center;
        width: 100%;
    }
}
</style>