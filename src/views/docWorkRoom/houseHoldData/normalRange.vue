<template>
  <div class="normalRange">
    <div class="inner" :class="[['bg','bloodfat'].indexOf(keyword) != -1 ? 'fixedH' : 'maxH']">
      <div class="titleLine">
        <span class="title">正常范围</span>
        <span class="reference">参考指南：{{ reference || '--' }}</span>
        <van-icon @click="closeFun" class="closeIcon" color="#999999" size="18" name="cross" />
      </div>
      <div ref="descInfoBox" class="descInfo" v-html="descInfo"></div>
    </div>
    
  </div>
</template>

<script>
import { getNormalRange } from '@/api/docWorkRoom'
export default {
  data(){
    return {
      descInfo: '',
      reference: ''
    }
  },
  props: {
    keyword: {
      type: String,
      default: ''
    }
  },
  watch: {
    keyword: {
      handler(newVal,oldVal){
        if(newVal != oldVal){
          this.getNormalRangeFun()
        }
      }
    }
  },
  methods: {
    closeFun(){
      this.$parent.isNormalPopShow = false
    },
    getNormalRangeFun(){
      if(['bmi','bp','bg','pef','oxygen_saturation','wheezing_sound','bloodfat','whr','body_tw'].indexOf(this.keyword) == -1){
        return
      }
      let channel = this.$route.query.source == 'docApp' ? 1 : 2  // 1：医生端  2：患者端
      // bmi,bp,bg,pef,oxygen_saturation,wheezing_sound,bloodfat
      let obj = {
        keyword: this.keyword,
        is_patient_platform: this.$route.query.source == 'docApp' ? '0' : '1'
      }
      getNormalRange(obj).then(res=>{
        if(res.status == 0){
          let {group,indicators} = res.data
          let resInfo = ''
          switch(this.keyword){
            case 'bp':
              this.reference = group.reference
              resInfo = group.desc_info_list.find(item=>item.user_type == channel).desc_info
              this.descInfo = resInfo
              break;
            case 'bmi':
            case 'pef':
            case 'oxygen_saturation':
            case 'wheezing_sound':
              let obj = indicators[0]
              this.reference = obj.reference
              resInfo = obj.desc_info_list.find(item=>item.user_type == channel).desc_info
              this.descInfo = resInfo
              break;
            case 'bloodfat':
            case 'bg':
            case 'whr':
              this.reference = indicators[0].reference
              indicators.forEach(item => {
                let codeInfo = `<div class="codeTitle">${item.name}：</div>`
                let itemInfo = codeInfo + item.desc_info_list.find(item=>item.user_type == channel).desc_info
                resInfo = resInfo += itemInfo
              });
              this.descInfo = resInfo
              break;
          }
        }
      })
    }
  },
  mounted(){
    this.getNormalRangeFun()
  }
}
</script>

<style lang="scss">
.normalRange{
  .fixedH{
    height: 80%;
  }
  .maxH{
    max-height: 80%;
  }
  .inner{
    width: 80%;
    // max-height: 80%;
    position: fixed;
    left: 50%;
    top: 50%;
    transform: translate(-50%,-50%);
    background: #fff;
    padding: 12px;
    border-radius: 8px;
    .titleLine{
      position: relative;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      text-align: left;
      min-height: 40px;
      padding-bottom: 10px;
      .title{
        font-size: 16px;
        font-weight: 500;
        color: #0A0A0A;
      }
      .reference{
        color: #999999;
        font-size: 12px;
        margin-top: 10px;
      }
      .closeIcon{
        position: absolute;
        right: 0;
        top: 0;
      }
    }
  }
  .descInfo{
    height: calc(100% - 60px);
    overflow: auto;
    // box-shadow: inset 0 0 8px 2px #E8E8E8;
  }
  table{
    margin-top: 10px !important;
    width: auto !important;
    border-collapse: collapse;
    border-spacing: 0;
    font-size: 12px;
  }
  .codeTitle{
    text-align: left;
    margin-top: 10px;
    font-size: 14px;
    font-weight: bold;
  }
  td, th {
    white-space: nowrap;
    border: 1px solid;
    padding: 6px;
  }
  // .descInfo::-webkit-scrollbar {
  //   display: none;
  // }
}
</style>