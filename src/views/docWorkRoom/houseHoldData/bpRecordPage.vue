<template>
  <div class="bpRecordPage">
    <!-- <div class="inner">
      <div class="innerTitle">正常范围</div>
      <NormalRange type="bp"></NormalRange>
    </div> -->
    <div class="inner">
      <div class="innerTitle">明细数据</div>
      <BpRecord from="forPatient"></BpRecord>
    </div>
  </div>
</template>

<script>
import BpRecord from './bpRecord.vue'
export default {
  components: {
    BpRecord
  },
  data(){
    return {
      reference: '',
      descInfo: ''
    }
  },
  methods: {
    init(){
      
    }
  },
  mounted(){
    this.init()
  }
};
</script>

<style lang="scss">
.bpRecordPage {
  .inner:first-of-type {
    margin-top: 0;
  }
  .inner {
    border-radius: 8px;
    background: #fff;
    padding: 20px 14px;
    margin-top: 16px;
    .innerTitle {
      font-size: 17px;
      color: #0a0a0a;
      font-weight: 500;
      text-align: left;
    }
    .subTitle {
      font-size: 12px;
      color: #878f99;
    }
  }
}
</style>
