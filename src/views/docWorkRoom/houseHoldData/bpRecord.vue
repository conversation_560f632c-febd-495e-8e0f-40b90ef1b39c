<template>
  <div class="bpRecord">
    <van-list
      v-model="loading"
      :finished="finished"
      finished-text="没有更多了~"
      @load="getList"
    >
      <div class="list" v-for="(line,index) in bpList" :key="index">
        <!-- :style="{'background-color': from == 'forPatient' ? '' : '#F5F5F5','padding': from == 'forPatient' ? '' : '0 15px','margin-top': from == 'forPatient' ? '16px' : ''}" -->
        <div class="date">{{ line.group_date }}</div>
        <!-- :style="{'padding': from == 'forPatient' ? '16px 0' : '16px 15px'}" -->
        <div class="item" v-for="(item,index) in line.list" :key="index">
          <div class="topInner">
            <div class="col col1">
              <span class="label">血压 <van-icon @click="handleEdit(item)" v-if="item.z_bp_id != 0 && source != 'docApp'" name="edit" color="#999999" /></span>
              <div class="bpLine">
                <div class="bpVal" ><span :class="[ item.sbpInfo.deflection ? [1,3].includes(item.sbpInfo.deflection) ? 'blueColor' : 'redColor' : '']">{{item.sbp}}</span><van-icon v-if="item.sbpInfo.deflection" name="down" :class="[ [1,3].includes(item.sbpInfo.deflection) ? '' : 'arrowRotate']" :color="[1,3].includes(item.sbpInfo.deflection) ? '#008AFF' : '#FF1F33' " />
                  / <span :class="[ item.dbpInfo.deflection ? [1,3].includes(item.dbpInfo.deflection) ? 'blueColor' : 'redColor' : '']">{{item.dbp}}</span><van-icon v-if="item.dbpInfo.deflection" name="down" :class="[ [1,3].includes(item.dbpInfo.deflection) ? '' : 'arrowRotate']" :color="[1,3].includes(item.dbpInfo.deflection) ? '#008AFF' : '#FF1F33' " />
                </div>
                <span class="unit">mmHg</span>
              </div>
              <div class="bpWarn">
                <img class="warnIcon" v-if="item.sbpInfo.deflection == 3 || item.dbpInfo.deflection == 3 || item.pulseInfo.deflection == 3" src="../imgs/blueWarning.png" alt="">
                <img class="warnIcon" v-else-if="item.sbpInfo.deflection == 4 || item.dbpInfo.deflection == 4 || item.pulseInfo.deflection == 4" src="../imgs/warning.png" alt="">
                <div v-if="item.tagDeflection && item.tag" class="warnTag" :class="[[1,3].includes(item.tagDeflection) ? 'blueTag' : 'redTag']">{{item.tag}}</div>
              </div>
            </div>
            <div class="col">
              <span class="label">脉搏</span>
              <div class="pulse">
                <span class="pulseVal" :class="[ item.pulseInfo.deflection ? [1,3].includes(item.pulseInfo.deflection) ? 'blueColor' : 'redColor' : '']">{{item.pulse}}</span>
                <van-icon v-if="item.pulseInfo.deflection" name="down" :class="[ [1,3].includes(item.pulseInfo.deflection) ? '' : 'arrowRotate']" :color="[1,3].includes(item.pulseInfo.deflection) ? '#008AFF' : '#FF1F33' " />
                <span class="unit">次/分</span>
              </div>
            </div>
            <div class="col col3">
              <span class="time">{{item.time}}</span>
              <span class="scene">{{item.data_type_name}}</span>
              <div class="source">{{item.measure_method}}</div>
            </div>
          </div>
          <div class="bottomInner">
            <div class="topInner">
              <div class="col">
                <span class="label">心律</span>
                <div class="pulse">
                  <span v-if="item.af_flg_name == '正常' || !item.af_flg_name" class="rhythm blackColor">{{item.af_flg_name || '--'}}</span>
                  <span v-else class="rhythm redColor">{{item.af_flg_name}}</span>
                </div>
              </div>
              <!-- v-if="item.third_uuid_ecg_result" -->
              <div class="col" v-if="item.third_uuid_ecg_result">
                <span class="label">心电结果</span>
                <div class="pulse">
                  <span class="rhythm blackColor">{{item.third_uuid_ecg_result || '--'}}</span>
                </div>
              </div>
              <!-- v-if="item.third_uuid_ecg_file_url" -->
              <div class="col" @click="previewReport(item)" v-if="item.third_uuid_ecg_file_url">
                <span class="pulse label" style="color: #666666">
                  <van-icon name="description" />
                  查看报告
                </span>
              </div>
            </div>
            <div class="reasonLine" v-if="item.reason_type">
              <div class="label">异常原因：</div>
              <div class="val">{{ item.reason_type }}</div>
            </div>
            <div class="remarkLine" v-if="item.reason">
              <div class="label">备注：</div>
              <div class="val">{{ item.reason }}</div>
            </div>
          </div>
        </div>
      </div>
    </van-list>
    <HandlePopup :show="isHandlePopupSow"></HandlePopup>
  </div>
</template>

<script>
import { getBpList,addReason,delBpOne } from '@/api/docWorkRoom'
import HandlePopup from "./components/handlePopup.vue"
import { getSystemType } from '@/utils/utils'
export default {
  components: {  
    HandlePopup
  },
  data(){
    return {
      originData: [],
      bpList: [],
      finished: false,
      loading: false,
      pageNo: 0,
      totalPage: 0,
      isHandlePopupSow: false,
      curItem: {},
      source: ''
    }
  },
  props: {
    from: {
      type: String,
      default: ''
    }
  },
  methods: {
    init(){
      this.source = this.$route.query.source
      // this.getBpList()
    },
    getList(){
      let obj = {
        page_no: this.pageNo += 1,
        page_size: 15,
        user_id: this.$route.query.patient_id || '',
        is_patient_platform: this.$route.query.source == 'docApp' ? '0' : '1'
      }
      getBpList(obj).then(res=>{
        if(res.status == 0){
          res.data.measure_data.forEach(item=>{
            item.blueCritical = item.abnormal_info.filter(i=>i.deflection == 3).length > 0 ? true : false
            item.redCritical = item.abnormal_info.filter(i=>i.deflection == 4).length > 0 ? true : false
            item.sbpInfo = item.abnormal_info.find(i=>i.code == 'sbp') || {}
            item.dbpInfo = item.abnormal_info.find(i=>i.code == 'dbp') || {}
            item.pulseInfo = item.abnormal_info.find(i=>i.code == 'pulse') || {}
            item.tag = item.abnormal_info.length > 0 ? item.abnormal_info[0].status_display_group : ''
            item.tagDeflection = item.abnormal_info.length > 0 ? item.abnormal_info[0].deflection_group : 0
            // if(item.ihb_flg == 0 && item.af_flg == 0){
            //   item.rhythmText = "正常"
            // }
            // if(item.ihb_flg == 1 && item.af_flg == 1){
            //   item.rhythmText = "疑似房颤"
            // }
            // if(item.ihb_flg == 1 && item.af_flg == 0){
            //   item.rhythmText = "不规则脉波"
            // }
            // if(item.ihb_flg == 0 && item.af_flg == 1){
            //   item.rhythmText = "疑似房颤"
            // }
          })
          this.originData = this.originData.concat(res.data.measure_data)
          this.bpList = this.groupData(this.originData)
          this.loading = false
          if(this.pageNo >= res.data.total_page){
            this.finished = true
          }
        }else{
          this.$toast(res.msg)
        }
      })
    },
    groupData(arr){
      if(!Array.isArray(arr) || arr.length == 0){
        return
      }
      // let resMap = new Map()
      // arr.forEach(item => {
      //   if(resMap.has(item.group_date)){
      //     let tempArr = resMap.get(item.group_date)
      //     tempArr.push(item)
      //     resMap.set(item.group_date, tempArr)
      //   }else{
      //     resMap.set(item.group_date, [item])
      //   }
      // })
      let groupObj = {};
      for (const item of arr) {
        if (groupObj[item.group_date]) {
          groupObj[item.group_date].push(item);
        } else {
          groupObj[item.group_date] = [item];
        }
      }
      // console.log(groupObj)
      let resArr = []
      Object.keys(groupObj).forEach(item=>{
        let obj = {}
        obj.group_date = item
        obj.list = groupObj[item]
        resArr.push(obj)
      })
      // console.log(resArr)
      return resArr
    },
    handleEdit(value) {
      // console.log(value)
      this.isHandlePopupSow = true
      this.curItem = value
    },
    delOne(){
      delBpOne({id: this.curItem.id}).then(res=>{
        if(res.status == 0){
          this.$toast('删除成功')
          this.isHandlePopupSow = false
          setTimeout(()=>{
            this.pageNo = 0
            this.originData = []
            this.getList()
          },500)
        }else{
          this.$toast(res.msg)
          this.isHandlePopupSow = false
        }
      })
    },
    addReason(obj){
      let params = {
        ...obj,
        id: this.curItem.id,
        type: 2,  // 1:血糖 2:血压
        data_from_saas: 1
      }
      addReason(params).then(res=>{
        if(res.status == 0){
          this.$toast('添加成功')
          this.isHandlePopupSow = false
          setTimeout(()=>{
            this.pageNo = 0
            this.originData = []
            this.getList()
          },500)
        }else{
          this.$toast(res.msg)
          this.isHandlePopupSow = false
        }
      })
    },
    previewReport(item){
      let url = `/docWorkRoom/projectMain/previewData?format=${this.formatJudge(item.third_uuid_ecg_file_url)}&isPreview=1&urlPath=${encodeURIComponent(item.third_uuid_ecg_file_url)}&titleNmae=${encodeURIComponent('心电报告')}&sourcePlatform=${this.$route.query.source}`
      if (this.$route.query.source == 'docApp') {
        if (getSystemType() == 'ios') {
          window.webkit.messageHandlers.breathMessage.postMessage(url)
        } else {
          window.android.breathMessage(url)
        }
      }else{
        // let url = `https://patient-h5.zz-med-test.com/docWorkRoom/projectMain/previewData?format=${this.formatJudge(item.third_uuid_ecg_file_url)}&isPreview=1&urlPath=${encodeURIComponent(item.third_uuid_ecg_file_url)}&titleNmae=${encodeURIComponent('心电报告')}`
        // window.open(url)
        this.$router.push(url)
      }
    },
    formatJudge(url) {
      let dcode = decodeURIComponent(url);
      let parsedURL = new URL(dcode);
      let pathname = parsedURL.pathname;
      let fileExtension = pathname.split(".").pop().toLowerCase();
      return fileExtension;
    },
  },
  mounted(){
    this.init()
  }
}
</script>

<style lang="scss">
.bpRecord{
  .date{
    // height: 35px;
    // line-height: 35px;
    text-align: left;
    font-weight: 500;
    color: #666666;
    margin-top: 16px;
    font-size: 15px;
  }
  .item{
    display: flex;
    padding: 16px 0 8px;
    border-bottom: 1px solid #F1F1F1;
    flex-direction: column;
    .topInner{
      width: 100%;
      display: flex;
    }
    .bottomInner{
      display: flex;
      flex-direction: column;
      margin-top: 12px;
      .reasonLine,.remarkLine{
        display: flex;
        font-size: 15px;
        line-height: 20px;
        .label{
          color: #333333;
          // width: max-content;
        }
        .val{
          color: #666666;
          text-align: left;
          flex: 1;
        }
      }
      .remarkLine{
        margin-top: 12px;
      }
    }
    .col1{
      flex: 12 !important;
    }
    .col3{
      display: flex;
      flex-direction: column;
      align-items: end !important;
      justify-content: space-between;
    }
    .col{
      display: flex;
      flex-direction: column;
      align-items: baseline;
      flex: 6;
      .bpLine{
        display: flex;
        align-items: baseline;
      }
      .unit{
        font-size: 12px;
        color: #84909A;
      }
      .bpVal,.pulse{
        font-size: 18px;
        font-weight: 500;
        span{
          font-weight: 500;
        }
      }
      .label,.time,.scene,.source{
        font-size: 15px;
      }
      .label{
        color: #666666;
      }
      .time,.scene,.source{
        color: #586267;
      }
      .bpLine,.pulse,.scene{
        // height: 42px;
        // line-height: 42px;
        padding: 8px 0;
        line-height: normal;
      }
      .bpLine,.pulse{
        color: #0A0A0A;
      }
      .rhythm{
        font-size: 16px;
        font-weight: 500;
      }
      .redColor {
        color: #ff1f33;
      }
      .blueColor{
        color: #008AFF;
      }
      .blackColor{
        color: #0A0A0A;
      }
      .bpWarn{
        display: flex;
        .warnIcon{
          width: 20px;
          height: 20px;
          position: relative;
          top: 2px;
        }
        .warnTag{
          font-size: 10px;
          border-radius: 10px;
          padding: 4px;
          margin-left: 2px;
        }
        .blueTag{
          color: #057EFF;
          border: 1px solid #A0C8FF;
          background: #ECF4FF;
        }
        .redTag{
          color: #FF0019;
          border: 1px solid #FFB1B3;
          background: #FFEFF0;
        }
      }
      
    }
  }
  .arrowRotate{
    transform: rotate(180deg);
  }
}
</style>