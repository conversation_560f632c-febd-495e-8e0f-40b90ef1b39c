<template>
    <div class="bloodgOxygenSaturation">
        <div class="bloodgOxygenBox">
            <div class="recordTitle">
                明细数据
            </div>
            <van-list v-model="loading" :finished="finished" finished-text="没有更多了~" @load="getList">
                <div class="detail" v-for="(item, index) in dataList" :key="index">
                    <div class="header">
                            <p>{{ item.group_date }}</p>
                        </div>
                        <div class="content" v-for="(v, ind) in item.list" :key="ind">
                            <div class="content_box">
                                <div class="content_left box">
                                    <div class="content_left_name">
                                        血氧饱和度
                                    </div>
                                    <div class="content_left_num">
                                        <div class="num" :class="[ v.abnormalInfo ? [1,3].includes(v.abnormalInfo.deflection) ? 'blueColor' : 'redColor' : '']">
                                            {{ v.spo2 }}<van-icon v-if="v.abnormalInfo" name="down" :class="[ [1,3].includes(v.abnormalInfo.deflection) ? '' : 'arrowRotate']" :color="[1,3].includes(v.abnormalInfo.deflection) ? '#008AFF' : '#FF1F33' " />
                                        </div>
                                        <div class="unit">%</div>
                                    </div>
                                    <div class="content_left_tips">
                                        <div class="showBox">
                                            <img class="warnIcon" v-if="v.abnormalInfo && v.abnormalInfo.deflection == 3" src="../imgs/blueWarning.png" alt="">
                                            <img class="warnIcon" v-if="v.abnormalInfo && v.abnormalInfo.deflection == 4" src="../imgs/warning.png" alt="">
                                            <div class="left_tips" :class="[v.abnormalInfo && [1,3].includes(v.abnormalInfo.deflection) ? 'blueTag' : 'redTag']" v-if="v.abnormalInfo" style="text-align: center;">{{v.abnormalInfo.status_display}}</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="content_right box">
                                    <div class="content_right_name">
                                        {{ v.time }}
                                    </div>
                                    <div class="content_right_name">
                                        {{ v.measure_type_text }}
                                    </div>
                                    <div class="content_right_name">
                                        {{ v.measure_method }}
                                    </div>
                                </div>
                            </div>
                        </div>
                </div>
            </van-list>
        </div>
    </div>
</template>

<script>
import { getSpo2List } from '@/api/docWorkRoom.js'
export default {
    data: () => {
        return {
            dataList: [],
            loading: false,
            finished: false,
            originData: [],
            page_no: 0,
            page_size: 15
        }
    },
    computed: {},
    watch: {},
    created() { },
    methods: {
        getList() {
            let obj = {
                page_no: this.page_no += 1,
                page_size: this.page_size,
                user_id: this.$route.query.patient_id || '',
                is_patient_platform: this.$route.query.source == 'docApp' ? '0' : '1'
            }
            getSpo2List(obj).then((res) => {
                const { measure_data,total_page } = res.data
                this.originData = this.originData.concat(measure_data)
                this.dataList = this.groupData(this.originData)
                this.loading = false
                if(this.page_no >= total_page){
                    this.finished = true
                }
            }
            )
        },
        groupData(arr){
            if(!Array.isArray(arr) || arr.length == 0){
                return
            }
            let groupObj = {};
            for (const item of arr) {
                if(item.abnormal){
                    item.abnormalInfo = item.abnormal_info[0]
                }
                if (groupObj[item.group_date]) {
                groupObj[item.group_date].push(item);
                } else {
                groupObj[item.group_date] = [item];
                }
            }
            console.log(groupObj)
            let resArr = []
            Object.keys(groupObj).forEach(item=>{
                let obj = {}
                obj.group_date = item
                obj.list = groupObj[item]
                resArr.push(obj)
            })
            console.log(resArr)
            return resArr
        },
    }
}
</script>

<style lang="scss" scoped>
.bloodgOxygenSaturation {
    background: white;
    border-radius: 8px;
    padding: 20px 14px;
    // margin-top: 16px;
    .bloodgOxygenBox {
        
        .recordTitle {
            color: #0A0A0A;
            font-size: 17px;
            font-weight: 500;
            text-align: left;
        }

        .detail {
            .header {
                margin-top: 16px;

                p {
                    color: #666666;
                    font-size: 15px;
                    font-weight: 500;
                    text-align: left;
                }
            }

            .content {
                // height: 114px;
                // box-sizing: border-box;
                padding: 16px 0;
                border-bottom: 1px solid #F1F1F1;
                .content_box {
                    box-sizing: border-box;
                    // border-bottom: 1px solid #F1F1F1;
                    // height: 97px;
                    display: flex;
                    justify-content: space-between;
                    // padding-bottom: 15px;

                    .box {
                        display: flex;
                        flex-direction: column;
                        justify-content: space-between;
                    }

                    .content_left {
                        text-align: left;

                        .content_left_name {
                            height: 21px;
                            font-size: 15px;
                            font-style: normal;
                            font-weight: 400;
                            line-height: 21px;

                        }

                        .content_left_num {
                            display: flex;
                            height: 42px;
                            line-height: 42px;
                            font-style: normal;

                            .num {
                                font-size: 20px;
                                font-weight: 900;
                            }

                            .buleColor {
                                color: #008AFF;
                            }

                            .unit {
                                margin-left: 4px;
                                color: #84909A;
                                font-size: 15px;
                                font-weight: 400;
                            }
                        }

                        .content_left_tips {
                            height: 20px;

                            .showBox {
                                display: flex;
                                align-items: center;

                                img {
                                    width: 20px;
                                    height: 20px;
                                }

                                .left_tips {
                                    width: 34px;
                                    height: 16px;
                                    border-radius: 35px;
                                    // border: 1px solid #A0C8FF;
                                    // background: #ECF4FF;
                                    // color: #057EFF;
                                    text-align: center;
                                    font-size: 11px;
                                    font-style: normal;
                                    font-weight: 400;
                                    line-height: 16px;
                                }
                            }
                        }
                    }

                    .content_right {
                        text-align: right;

                        .content_right_name {
                            height: 20px;
                            color: #586267;
                            text-align: right;
                            font-family: "PingFang SC";
                            font-size: 15px;
                            font-style: normal;
                            font-weight: 400;
                            line-height: 20px;
                        }
                    }
                }
            }
        }
    }
    .blueTag{
        color: #057EFF;
        border: 1px solid #A0C8FF;
        background: #ECF4FF;
    }
    .redTag{
        color: #FF0019;
        border: 1px solid #FFB1B3;
        background: #FFEFF0;
    }
    .redColor {
        color: #ff1f33 !important;
    }
    .blueColor{
        color: #008AFF !important;
    }
}
</style>