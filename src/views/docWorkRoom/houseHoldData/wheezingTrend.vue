<template>
  <div class="wheezingTrend">
    <div class="inner">
      <div class="daysPicker">
        <div
          :class="`daysItem ${daysChekedIndex == index ? 'daysChecked' : ''}`"
          v-for="(item, index) in daysPicker"
          :key="index"
          @click="daysSelect(index, item)"
        >
          {{ item.type }}
        </div>
      </div>
      <div class="pieBlock" v-if="report.length > 0">
        <div class="pieChart" ref="pieChart"></div>
        <div class="typeList">
          <div class="line lineTitle">
            <span class="label">测量情况</span>
            <span class="val">比例</span>
          </div>
          <div class="line" v-for="(item, index) in report" :key="index">
            <div class="label">
              <div :class="`circle ${item.st}`"></div>
              <span>{{ item.name }}({{ item.cnt }})</span>
            </div>
            <span class="val">{{ item.percentage }}%</span>
          </div>
        </div>
      </div>
      <div class="no-data-text" v-else>无测量数据</div>
    </div>
    <div class="bottomTips">我是有底线的</div>
  </div>
</template>

<script>
import { getBpConfig, getWheezesfatReport } from '@/api/docWorkRoom'
import echarts from 'echarts'

export default {
  data() {
    return {
      daysPicker: [],
      daysChekedIndex: 0,
      daysCheckedId: '',
      startDate: '',
      endDate: '',
      user_id: '',
      report: [],
    };
  },
  created() {
    this.user_id = this.$route.query.patient_id
    this.getConfig()
  },
  methods: {
    async getWheezesReport(t) {
      const res = await getWheezesfatReport({
        user_id: this.user_id,
        start_at: this.startDate,
        end_at: this.endDate,
        is_patient_platform: this.$route.query.source == 'docApp' ? '0' : '1'
      })

      if (res.status === 0) {
        this.formateReport(res.data);
      } else {
        this.$toast(res.msg);
      }
    },
    formateReport(data) {
      if (data.normal || data.abnormal)  {
        const {
          normal,
          abnormal,
        } = data
        const percentage = this.calculatePercentages(normal.cnt, abnormal.cnt)

        normal.st = 'normal'
        normal.color = '#07C060'
        normal.percentage = percentage.p1

        abnormal.st = 'abnormal'
        abnormal.color = '#FC6161'
        abnormal.percentage = percentage.p2

        normal.value = normal.cnt
        abnormal.value = abnormal.cnt
        this.report = [normal, abnormal]
        this.$nextTick(() => {
          this.pieChartInit(data, this.report)
        })
      } else {
        this.report = data || []
      }
    },
    calculatePercentages(n1, n2) {
      const total = n1 + n2

      if (total === 0) {
        throw new Error('总计不能为0')
      }

      const p1 = ((n1 / total) * 100).toFixed(2)
      const p2 = ((n2 / total) * 100).toFixed(2)

      return {
        p1: parseFloat(p1),
        p2: parseFloat(p2)
      }
    },
    getConfig() {
      let obj = {
        is_patient_platform: this.$route.query.source == 'docApp' ? '0' : '1'
      }
      getBpConfig(obj).then((res) => {
        if (res.status === 0) {
          this.startDate = res.data.date_type[0].start_date
          this.endDate = res.data.date_type[0].end_date
          this.daysPicker = res.data.date_type

          this.getWheezesReport()
        } else {
          this.$toast(res.msg)
        }
      });
    },
    daysSelect(index, item) {
      this.daysChekedIndex = index
      this.daysCheckedId = item.id
      this.startDate = item.start_date
      this.endDate = item.end_date
      this.getWheezesReport()
    },
    pieChartInit(data, report) {
      const {
        measure_cnt,
      } = data

      const myEchart = echarts.init(this.$refs.pieChart, "light");
      const options = {
        tooltip: {
          show: false,
        },
        color: report.map(item => item.color),
        series: [
          {
            type: "pie",
            radius: ["80%", "100%"],
            avoidLabelOverlap: false,
            label: {
              show: true,
              position: "center",
              formatter: "{total|" + measure_cnt + "}" + "\n\r" + "{active|总测量次数}",
              rich: {
                total: {
                  color: "#333333",
                  fontSize: 26,
                  lineHeight: 37,
                },
                active: {
                  color: "#999999",
                  fontSize: 12,
                  lineHeight: 17,
                },
              },
            },
            silent: true,
            emphasis: {
              show: true,
              scale: false,
            },
            labelLine: {
              show: false,
            },
            data: report,
          },
        ],
      };
      myEchart.setOption(options);
    },
  },
};
</script>

<style lang="scss">
.wheezingTrend {
  .inner {
    border-radius: 8px;
    background: #fff;
    padding: 20px 14px;
  }
  .daysPicker {
    font-family: "PingFang SC";
    height: 34px;
    line-height: 34px;
    border-radius: 30px;
    display: flex;
    background: #ecf5ff;
    font-size: 15px;
    font-weight: 500;
    padding: 3px;
    align-items: center;
    .daysItem {
      flex: 1;
      color: #909399;
    }
    .daysChecked {
      background: white;
      border-radius: 30px;
      color: #377cfb;
    }
  }
  .pieBlock {
    display: flex;
    align-items: center;
    margin-top: 24px;
    .pieChart {
      width: 110px;
      height: 110px;
      margin-left: 10px;
    }
    .typeList {
      display: flex;
      flex-direction: column;
      flex: 1;
      margin-left: 46px;
      margin-right: 10px;
      font-size: 15px;
      .line:first-of-type {
        margin-top: 0;
      }
      .line {
        display: flex;
        justify-content: space-between;
        margin-top: 18px;
        color: #666666;
        .label {
          display: flex;
          align-items: center;
        }
      }
      .lineTitle {
        color: #333333;
        font-weight: bold;
      }
      .circle {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 6px;
        &.normal {
          background: #07C060;
        }
        &.abnormal {
          background: #FC6161;
        }
      }
    }
  }
  .no-data-text {
    margin-top: 24px;
    font-size: 15px;
    text-align: center;
    color: #999;
  }
  .bottomTips{
    color: #999999;
    font-size: 12px;
    line-height: normal;
    margin-top: 12px;
  }
}
</style>