<template>
  <div class='noData'>
    <div class="noDataText">暂无数据</div>
    <div class="goMeasure" @click="goMeasureFun">去录入血压数据</div>
  </div>
</template>

<script>
import wx from 'weixin-js-sdk'
export default {
  data() {
    return {
      
    }
  },
  methods: {
    goMeasureFun(){
      wx.miniProgram.navigateTo({
        url: `/pages/pressure/pages/manualMeasurementBP/newmanualMeasurementBP`
      })
    },
  },
  created() {
    
  },
  mounted() {
    
  }
}
</script>

<style lang='scss'>
.noData{
  margin-top: 30px;
  font-size: 15px;
  .noDataText{
    line-height: 24px;
    color: #3F4447;
  }
  .goMeasure{
    font-weight: 500;
    text-decoration: underline;
    text-underline-offset: 2px;
    color: #3796fa;
    margin-top: 8px;
  }
}
</style>