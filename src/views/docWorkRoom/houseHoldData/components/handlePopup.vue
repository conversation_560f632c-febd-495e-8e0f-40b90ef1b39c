<template>
  <div class="handlePopup">
    <van-dialog
      v-model="show"
      :show-cancel-button="false"
      :show-confirm-button="false"
      class="editDialog"
    >
      <div class="content">
        请选择您对条记录的操作
      </div>
      <van-button block border-color="#999999" hairline @click="handleRemarks">
        <span style="color: #fc9547;font-size: 18px;">补充备注</span>
      </van-button>
      <van-button block hairline @click="handleDelete">
        <span style="color: #ff3333;font-size: 18px;">删除</span>
      </van-button>
      <van-button block hairline @click="handleCancel">
        <span style="color: #999999;font-size: 18px;">取消</span>
      </van-button>
    </van-dialog>

    <van-action-sheet
      class="actionSheet"
      v-model="actionSheetShow"
      title="备注"
      :closeable="false"
    >
      <div class="sheetInner">
        <div class="btnBox">
          <div class="contentBtn" :class="[item.checked ? 'btnChecked' : '']" v-for="item in reasonArr" :key="item.value" @click="selectReason(item)" >{{item.name}}</div>
        </div>
      </div>
      <van-field class="textArea" v-model="reasonText" rows="3" autosize type="textarea" maxlength="50" show-word-limit placeholder="您可以在此填写其他诱因" />
      <div class="footer">
        <van-button type="primary" round block class="submitBtn" @click="submitReason">提交</van-button>
      </div>
    </van-action-sheet>
  </div>
</template>

<script>
import { Dialog } from "vant"
export default {
  props:{
    show: {
      type: Boolean,
      default: false,
    }
  },
  watch:{
    show: {
      handler(val){
        if(!val){
          this.actionSheetShow = false
          this.reasonArr.forEach(item=>item.checked = false)
          this.reasonText = ''
          this.reasonCheckedArr = []
        }
      }
    }
  },
  data(){
    return{
      actionSheetShow: false,
      reasonArr: [
        {value: 1, name: '饮食', checked: false},
        {value: 2, name: '运动', checked: false},
        {value: 3, name: '用药', checked: false},
        {value: 4, name: '睡眠', checked: false},
        {value: 5, name: '喝酒', checked: false},
        {value: 6, name: '抽烟', checked: false},
        {value: 7, name: '情绪', checked: false},
        {value: 9, name: '胰岛素', checked: false},
        {value: 8, name: '其他', checked: false}
      ],
      reasonText: '',
      reasonCheckedArr: []
    }
  },
  methods:{
    selectReason(item){
      item.checked = !item.checked
      this.reasonCheckedArr = this.reasonArr.filter(item=>item.checked).map(item=>item.value)
    },
    handleRemarks() {
      this.actionSheetShow = true;
    },
    
    handleDelete() {
      Dialog.confirm({
        title: "确定删除？",
        message: "删除后无法恢复",
        confirmButtonText: "删除",
        confirmButtonColor: "#F53F3F",
        cancelButtonText: "取消",
        cancelButtonColor: "#3388FF",
      })
        .then(() => {
          this.$parent.delOne()
        })
        .catch(() => {});
    },
    handleCancel() {
      this.$parent.isHandlePopupSow = false
    },
    submitReason(){
      let obj = {
        reason: this.reasonText,
        reason_type: this.reasonCheckedArr.join(',')
      }
      this.$parent.addReason(obj)
    }
  },
  mounted(){
    
  }
}
</script>

<style lang="scss">
.handlePopup{
  .editDialog {
    width: 280px;

    .content {
      height: 88px;
      box-sizing: border-box;
      padding: 20px 12px;
      color: #0a0a0a;
      text-align: center;
      font-size: 17px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }
  }

  .sheet {
    .btn {
      width: 80px;
      height: 30px;
      border: 1px solid;
    }
  }

  .actionSheet {
    background: #fff;
    padding: 0 20px;
    box-sizing: border-box;
    .sheetInner {
      .btnBox {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        .contentBtn {
          width: 98px;
          text-align: center;
          height: 40px;
          line-height: 40px;
          border-radius: 8px;
          background: #f9f9f9;
          margin-bottom: 16px;
          border: 1px solid transparent;
        }
        .btnChecked{
          color: #3796fa;
          border: 1px solid #3796fa;
          background: rgba(55, 150, 250, 0.10);
        }
      }
    }

    .textArea {
      width: 100%;
      border-radius: 8px;
      box-sizing: border-box;
      border: 1px solid #DCDCDC;
      color: #666666;
      padding: 12px;
    }

    .footer {
      box-sizing: border-box;
      padding: 20px 42px 20px;
      .submitBtn{
        background: #3796fa;
        border: none;
      }
    }
  }
}
</style>