<template>
    <div class="nebulizedMedicationTrend">
        <div class="nebulizedMedicationBox">
            <div class="tabBtn">
                <van-tabs type="card" v-model="active" background="#FFF6EC" title-active-color="#FC9547" border="none"
                    @change="handleChange">
                    <van-tab title="近7天" name="7"></van-tab>
                    <van-tab title="近30天" name="30"></van-tab>
                    <van-tab title="近60天" name="60"></van-tab>
                    <van-tab title="近90天" name="90"></van-tab>
                </van-tabs>
            </div>
            <div class="date">
                <div class="icon">
                    <van-icon name="clock-o" size="15px" />
                    <span>{{ lastDate }}</span>
                </div>
                <!-- <img src="../imgs/fullScreen.png" alt=""> -->
            </div>
            <div class="main">
                <div id="main" class="echartsBox"></div>
            </div>

        </div>
    </div>
</template>

<script>
import { getWheezesfatList } from '@/api/docWorkRoom';
import * as echarts from 'echarts';

export default {
    data: () => {
        return {
            active: '7',
            lastDate: ''
        }
    },
    computed: {},
    watch: {},
    created() {
    },
    mounted() {
        this.handleChange()
    },
    methods: {
        handleChange() {
            this.getDate(Number(this.active))
        },
        getDate(numOfDays) {
            let date = new Date();
            let year = date.getFullYear();
            let month = date.getMonth() + 1;
            if (month < 10) {
                month = '0' + month
            }
            let day = date.getDate();
            date.setDate(date.getDate() - numOfDays);
            let lastYear = date.getFullYear();
            let lastMonth = date.getMonth() + 1;
            if (lastMonth < 10) {
                lastMonth = '0' + lastMonth
            }
            let lastDay = date.getDate();
            this.lastDate = lastYear + "/" + lastMonth + "/" + lastDay + " ~ " + year + "/" + month + "/" + day

            this.getEcharts()
        },
        getEcharts() {
            var chartDom = document.getElementById('main');
            var myChart = echarts.init(chartDom);
            var option;

            option = {
                color: '#FC9547',
                backgroundColor: '#FFFFFF',
                grid: {
                    right: '0',
                    top: '7px',
                    bottom: '0',
                    left: '0',
                    containLabel: true,
                    borderColor: '#E7ECF4',
                },
                xAxis: {
                    type: 'category',
                    axisTick: {
                        inside: true,

                    },
                    axisLine: {
                        color: '#E7ECF4',
                        lineStyle: {
                            color: '#999BA2'
                        }
                    }
                },
                yAxis: {
                    type: 'value',
                    interval: 20,
                    min: 80,
                    max: 200,
                    axisTick: {
                        show: false
                    },
                    nameTextStyle: {
                        textBorderWidth: '18px',
                        height: '14px'
                    },
                    axisLine: {
                        show: false,
                        lineStyle: {
                            color: '#999BA2'
                        }
                    }
                },
                series: [
                    {
                        type: 'line',
                        symbol: "circle",
                        symbolSize: 8,
                    }
                ]
            };

            option.xAxis.data = ['06/11', '06/12', '06/13', '06/14', '06/15', '06/16', '06/17', '06/18']
            option.series[0].data = [90, 90, 192, 99, 120, 118, 178, 111]
            option && myChart.setOption(option);
        }
    }
}
</script>

<style lang="scss" scoped>
.nebulizedMedicationTrend {
    background: white;
    border-radius: 8px;
    .nebulizedMedicationBox {
        box-sizing: border-box;
        height: 370px;
        border-radius: 10px;
        background: #FFF;
        padding-top: 12px;



        .tabBtn {
            height: 40px;
            padding: 0 15px;

        }

        .date {
            margin: 24px 0;
            box-sizing: border-box;
            padding: 0 15px;
            text-align: left;
            display: flex;
            justify-content: space-between;

            span {
                margin-left: 4px;
                color: #333;
                font-size: 15px;
                font-style: normal;
                font-weight: 500;
                line-height: 21px;
            }

            img {
                width: 20px;
                height: 20px;
            }
        }

        .main {
            box-sizing: border-box;
            padding: 0 20px;

            .echartsBox {
                // width: 305px;
                height: 233px;
            }
        }
    }
}

::v-deep .van-tabs__nav--card {
    height: 40px !important;
    border: none !important;
    margin: 0 4px !important;
}

::v-deep .van-tabs__wrap {
    height: 40px !important;
    border-radius: 20px !important;
    background: #FFF6EC !important;
}

::v-deep .van-tabs__nav--card .van-tab {
    border: none !important;
    color: #999;
    font-size: 14px;
    font-weight: 400;
}


::v-deep .van-tabs__nav--card .van-tab.van-tab--active {
    border-radius: 16px !important;
    background: #FFF !important;
    width: 76px !important;
    height: 32px !important;
    flex-shrink: 0 !important;
    margin-top: 4px !important;
}
</style>