<template>
  <div class="bpTrendReport">
    <div class="inner">
      <div class="daysPicker">
        <div
          class="daysItem"
          :class="[daysChekedIndex == index ? 'daysChecked' : '']"
          v-for="(item, index) in daysPicker"
          :key="index"
          @click="daysSelect(index, item)"
        >
          {{ item.type }}
        </div>
      </div>
      <div class="date-seven" v-if="daysCheckedId == '7Report'">
        <div @click="preStage" class="date-arrow">
          <van-icon name="arrow-left" class="arrow-icon"/>
          <span class="arrow-text">上阶段</span>
        </div>
        <div class="date-comp">
          <div class="date-comp-cont">
            <span>{{ dateFormat(startDate, '.') }}</span>
            <b class="date-comp-sign">-</b>
            <span>{{ dateFormat(endDate, '.') }}</span>
            <cc-svg-icon icon-class="date" class="date-comp-icon"/>
          </div>
          <input
            type="date"
            v-model="inputDate"
            :max="dateFormat(new Date())"
            @change="handleInputDate"
            class="date-comp-input"
          >
        </div>
        <div @click="nextStage" class="date-arrow">
          <span class="arrow-text" :style="{ 'color': nextClick ? '' : '#ccc'}">下阶段</span>
          <van-icon name="arrow" class="arrow-icon" :style="{ 'color': nextClick ? '' : '#ccc'}"/>
        </div>
      </div>
      <div class="pieTitle">家庭数据</div>
      <div class="pieBlock">
        <template v-if="pieLabelList.length > 0">
          <div class="pieChart" ref="pieChart"></div>
          <div class="typeList">
            <div class="line lineTitle">
              <span class="label">测量情况</span>
              <span class="val">比例</span>
            </div>
            <div class="line" v-for="item in pieLabelList" :key="item.status_id">
              <div class="label">
                <div class="circle normal" :style="{ 'background': item.color }"></div>
                <span class="labelText">{{ item.status_display }}({{ item.count }})</span>
              </div>
              <span class="val">{{ item.proportion }}</span>
            </div>
          </div>
        </template>
        <span class="noDataText" v-else>无测量数据</span>
      </div>
      <div class="pieTitle hospPieTitle" v-if="source != 'docApp'">门诊数据</div>
      <div class="pieBlock" v-if="source != 'docApp'">
        <template v-if="pieLabelListForHosp.length > 0">
          <div class="pieChart" ref="pieChartForHosp"></div>
          <div class="typeList">
            <div class="line lineTitle">
              <span class="label">测量情况</span>
              <span class="val">比例</span>
            </div>
            <div class="line" v-for="item in pieLabelListForHosp" :key="item.status_id">
              <div class="label">
                <div class="circle normal" :style="{ 'background': item.color }"></div>
                <span class="labelText">{{ item.status_display }}({{ item.count }})</span>
              </div>
              <span class="val">{{ item.proportion }}</span>
            </div>
          </div>
        </template>
        <span class="noDataText" v-else>无测量数据</span>
      </div>
    </div>
    <div class="inner">
      <div class="bpRangeLine">
        <div class="leftSbp">
          <div class="label">
            <img src="../imgs/sbpIcon.png" alt="" />
            <span class="text">收缩压</span>
            <span class="unit">mmHg</span>
          </div>
          <div class="bpValLine">
            <div class="bpValItem">
              <div class="valLin">
                <span class="val" v-if="sbpRange.min" :class="[sbpRange.min.deflection ? [1,3].includes(sbpRange.min.deflection) ? 'blueColor' : 'redColor' : '']">{{ sbpRange.min.value }}</span>
                <span class="val" v-else>--</span>
                <van-icon v-if="sbpRange.min && sbpRange.min.deflection" name="down" :class="[ [1,3].includes(sbpRange.min.deflection) ? '' : 'arrowRotate']" :color="[1,3].includes(sbpRange.min.deflection) ? '#008AFF' : '#FF1F33' " />
              </div>
              <span class="valLabel">最低值</span>
            </div>
            <div class="bpValItem">
              <div class="valLin">
                <span class="val" v-if="sbpRange.max" :class="[sbpRange.max.deflection ? [1,3].includes(sbpRange.max.deflection) ? 'blueColor' : 'redColor' : '']">{{ sbpRange.max.value }}</span>
                <span class="val" v-else>--</span>
                <van-icon v-if="sbpRange.max && sbpRange.max.deflection" name="down" :class="[ [1,3].includes(sbpRange.max.deflection) ? '' : 'arrowRotate']" :color="[1,3].includes(sbpRange.max.deflection) ? '#008AFF' : '#FF1F33' " />
              </div>
              <span class="valLabel">最高值</span>
            </div>
          </div>
        </div>
        <div class="rightDbp">
          <div class="label rightLabel">
            <img src="../imgs/dbpIcon.png" alt="" />
            <span class="text">舒张压</span>
            <span class="unit">mmHg</span>
          </div>
          <div class="bpValLine">
            <div class="bpValItem">
              <div class="valLin">
                <span class="val" v-if="dbpRange.min" :class="[dbpRange.min.deflection ? [1,3].includes(dbpRange.min.deflection) ? 'blueColor' : 'redColor' : '']">{{ dbpRange.min.value }}</span>
                <span class="val" v-else>--</span>
                <van-icon v-if="dbpRange.min && dbpRange.min.deflection" name="down" :class="[ [1,3].includes(dbpRange.min.deflection) ? '' : 'arrowRotate']" :color="[1,3].includes(dbpRange.min.deflection) ? '#008AFF' : '#FF1F33' " />
              </div>
              <span class="valLabel">最低值</span>
            </div>
            <div class="bpValItem">
              <div class="valLin">
                <span class="val" v-if="dbpRange.max" :class="[dbpRange.max.deflection ? [1,3].includes(dbpRange.max.deflection) ? 'blueColor' : 'redColor' : '']">{{ dbpRange.max.value }}</span>
                <span class="val" v-else>--</span>
                <van-icon v-if="dbpRange.max && dbpRange.max.deflection" name="down" :class="[ [1,3].includes(dbpRange.max.deflection) ? '' : 'arrowRotate']" :color="[1,3].includes(dbpRange.max.deflection) ? '#008AFF' : '#FF1F33' " />
              </div>
              <span class="valLabel">最高值</span>
            </div>
          </div>
        </div>
      </div>
      <div class="avgBpLine">
        <div class="bpVal">
          <span :class="[ bpAvgData.sbp && bpAvgData.sbp.abnormalInfo && bpAvgData.sbp.abnormalInfo.deflection ? [1,3].includes(bpAvgData.sbp.abnormalInfo.deflection) ? 'blueColor' : 'redColor' : '']">{{ bpAvgData.sbp ? bpAvgData.sbp.value : '--' }}</span>
          <van-icon v-if="bpAvgData.sbp && bpAvgData.sbp.abnormalInfo" name="down" :class="[ [1,3].includes(bpAvgData.sbp.abnormalInfo.deflection) ? '' : 'arrowRotate']" :color="[1,3].includes(bpAvgData.sbp.abnormalInfo.deflection) ? '#008AFF' : '#FF1F33' " />
          /
          <span :class="[ bpAvgData.dbp && bpAvgData.dbp.abnormalInfo && bpAvgData.dbp.abnormalInfo.deflection ? [1,3].includes(bpAvgData.dbp.abnormalInfo.deflection) ? 'blueColor' : 'redColor' : '']">{{ bpAvgData.dbp ? bpAvgData.dbp.value : '--' }}</span>
          <van-icon v-if="bpAvgData.dbp && bpAvgData.dbp.abnormalInfo" name="down" :class="[ [1,3].includes(bpAvgData.dbp.abnormalInfo.deflection) ? '' : 'arrowRotate']" :color="[1,3].includes(bpAvgData.dbp.abnormalInfo.deflection) ? '#008AFF' : '#FF1F33' " />
        </div>
        <span class="text" v-if="bpAvgData.sbp && bpAvgData.sbp.abnormalInfo" :class="[ bpAvgData.sbp && bpAvgData.sbp.abnormalInfo && bpAvgData.sbp.abnormalInfo.deflection ? [1,3].includes(bpAvgData.sbp.abnormalInfo.deflection) ? 'blueColor' : 'redColor' : '']">{{bpAvgData.avg_abnormal || '--'}}</span>
        <span class="text" v-else-if="bpAvgData.dbp && bpAvgData.dbp.abnormalInfo" :class="[ bpAvgData.dbp && bpAvgData.dbp.abnormalInfo && bpAvgData.dbp.abnormalInfo.deflection ? [1,3].includes(bpAvgData.dbp.abnormalInfo.deflection) ? 'blueColor' : 'redColor' : '']">{{bpAvgData.avg_abnormal || '--'}}</span>
        <span class="text" v-else>{{bpAvgData.avg_abnormal || '--'}}</span>
      </div>
    </div>
    <div class="inner">
      <div class="dateRange">
        <div class="leftDateRange">
          <van-icon color="#878F99" class="clockIcon" name="clock-o" /> {{ startDate }} ~ {{ endDate }}
        </div>
        <div class="rightChangeIcon" @click="changeTableOrLineChart" v-if="daysCheckedId == '7Report'">
          <img class="changeIcon" :src="showTable ? tableCheckedIcon : tableIcon" alt="">
          <img class="changeIcon" :src="showTable ? lineChartIcon : lineChartCheckedIcon" alt="">
        </div>
        <!-- <img style="width: 15px;height: 15px;" @click="fullScreenFun" v-else src="../imgs/fullScreen.png" alt=""> -->
      </div>
      <!-- 此处不能用template标签 可能会导致折线图不清空  -->
      <div v-if="JSON.stringify(chartsAndTableData) != '{}'">
        <template v-if="!showTable">
          <div class="lineChart" ref="lineChart"></div>
          <!-- <div class="echartsTips">
            图表中"
            <div class="colorBlock sbpColor"></div>
            "表示收缩压正常范围区间； "
            <div class="colorBlock dbpColor"></div>
            "表示舒张压正常范围区间
          </div> -->
        </template>
        <template v-else>
          <table class="table">
            <thead>
              <tr>
              <td>全天</td>
              <td>收缩压<br><span style="color: #999999;">mmHg</span></td>
              <td>舒张压<br><span style="color: #999999;">mmHg</span></td>
              <td>脉搏<br><span style="color: #999999;">mmHg</span></td>
            </tr>
            </thead>
            <tbody>
              <tr  v-for="(item, index) in tableData" :key=index>
                <td class="date" style="color: #474747;">{{item.date}}</td>
                <td class="val">{{ item.sbp }}</td>
                <td class="val">{{ item.dbp }}</td>
                <td class="val">{{ item.pulse }}</td>
              </tr>
            </tbody>
          </table>
        </template>
      </div>
      <div class="noDataText" style="margin-top: 24px;" v-else>无测量数据</div>
      
      <!-- <template v-else>
        <div class="noData">
          <div class="tips">暂时没有血压数据呢</div>
          <div class="goInputBp" @click="goInputBp">去录入血压值</div>
        </div>
      </template> -->
      <div class="timeSlot">
        <div @click="timeSlotChange(index)" :class="[timeSlotChecked == index ? 'timeChecked' : '']" v-for="(item,index) in ['全天','早上','晚上']" :key="index" class="timeItem">{{item}}</div>
      </div>
    </div>
    <div class="inner">
      <div class="innerTitle">血压图谱</div>
      <BloodPic style="margin-top: 20px;" :data="bpMapData"></BloodPic>
      <div class="guideText">参考《2019家庭血压检测指南》得出标准结论；实际控制指标以医生建议为准</div>
    </div>
    <template v-if="progressData.length > 0">
      <div class="inner" v-for="(item,index) in progressData" :key="index">
        <div style="display: flex;align-items: baseline;">
          <div class="innerTitle">{{item.name}}</div>
          <span class="subTitle">&nbsp;({{item.unit}})</span>
        </div>
        <div class="avgProgress">
          <div class="label">平均值</div>
          <div class="progress-bar"> <div class="progress" :style="{'width': getPercentage(item.avg,299)+ '%', 'background': item.colorLine}"></div></div>
          <div class="num">{{ item.avg }}</div>
        </div>
        <div class="avgProgress">
          <div class="label">最高值</div>
          <div class="progress-bar"> <div class="progress" :style="{'width': getPercentage(item.max,299)+ '%', 'background': item.colorLine}"></div></div>
          <div class="num">{{ item.max }}</div>
        </div>
        <div class="avgProgress">
          <div class="label">最低值</div>
          <div class="progress-bar"> <div class="progress" :style="{'width': getPercentage(item.min,299)+ '%', 'background': item.colorLine}"></div></div>
          <div class="num">{{ item.min }}</div>
        </div>
      </div>
    </template>
    <!-- <van-loading class="loading" size="50px" v-if="isLoadingShow" vertical color="#1989fa">加载中</van-loading>
    <van-overlay :show="isLoadingShow" /> -->
  </div>
</template>

<script>
import { getBpLineCharts,getBpConfig,getBpTrend,getBpPieCharts,getBpTableAndCharts,getBpGraph } from '@/api/docWorkRoom'
import BloodPic from '@/components/bloodPic.vue'
import echarts from 'echarts'
import wx from 'weixin-js-sdk'
import moment from 'moment'
export default {
  components: {
    BloodPic
  },
  data(){
    return {
      greenLine: 'linear-gradient(to right, #A4E9C5, #07C060)',
      redLine: 'linear-gradient(to right, #FFBEC2, #FF525A)',
      blueLine: 'linear-gradient(to right,#98CBFF, #008AFF)',
      tableIcon: require('../imgs/tableIcon.png'),
      tableCheckedIcon: require('../imgs/tableCheckedIcon.png'),
      lineChartCheckedIcon: require('../imgs/lineChartCheckedIcon.png'),
      lineChartIcon: require('../imgs/lineChartIcon.png'),
      showTable: false,  //7日报告切换表格和折线图标示
      hasData: true,
      daysPicker: [],
      daysChekedIndex: 0,
      daysCheckedId: '',
      startDate: '',
      endDate: '',
      sbpRange: {},  //收缩压 最大最小值
      dbpRange: {},  //舒张压 最大最小值
      bpAvgData: {}, //血压平均值
      sbpArr: [],  //折线图数据
      dbpArr: [],  //折线图数据
      dateArr: [],  //折线图数据
      bpMapData: [],  //血压图谱
      progressData: [],  //百分比数据
      tableData: [],  //表格数据
      nextClick: false,  //下阶段是否可点击
      inputDate: '',  //7日报告 日期输入框选中的日期
      timeSlotChecked: 0,  //早中晚选中值
      pieLabelList: [],
      chartsAndTableData: {},  //折线图和表格的原始数据
      isLoadingShow: true,
      pieLabelListForHosp: [],
      source: '',  //平台来源 
    }
  },
  methods: {
    timeSlotChange(index){
      this.timeSlotChecked = index
      this.showTable ? this.handleTableData() : this.handleEchartsData()
    },
    handleInputDate(){
      this.endDate = this.inputDate ? moment(this.inputDate).format('YYYY/MM/DD') : moment(new Date()).format('YYYY/MM/DD')
      this.startDate = this.handleStage(6,this.endDate,'pre')
      this.getAllData()
    },
    dateFormat(date, sep = '-') {
      let oDate = new Date(date)
      let y = oDate.getFullYear()
      let m = oDate.getMonth() + 1
      let d = oDate.getDate()
      if (m < 10) m = `0${m}`
      if (d < 10) d = `0${d}`
      return `${y}${sep}${m}${sep}${d}`
    },
    preStage() {
      this.startDate = this.handleStage(7,this.startDate,'pre')
      this.endDate = this.handleStage(7,this.endDate,'pre')
      this.nextClick = true
      this.getAllData()
    },
    nextStage() {
      if(!this.nextClick) return
      this.startDate = this.handleStage(7,this.startDate,'next')
      this.endDate = this.handleStage(7,this.endDate,'next')
      let todayDate = new Date()
      this.endDate == moment(todayDate).format('YYYY/MM/DD') ? this.nextClick = false : this.nextClick = true
      console.log(this.startDate,this.endDate,this.nextClick)
      this.getAllData()
    },
    handleStage(daysAgo,date,type){
      const currentDate = new Date(date);
      if(type == 'pre'){
        currentDate.setDate(currentDate.getDate() - daysAgo)
      }else{
        currentDate.setDate(currentDate.getDate() + daysAgo)
      }
      const year = currentDate.getFullYear()
      const month = String(currentDate.getMonth() + 1).padStart(2, '0')
      const day = String(currentDate.getDate()).padStart(2, '0')
      
      // if (format === 'YYYY-MM-DD') {
      //   return `${year}-${month}-${day}`
      // } else if (format === 'MM/DD/YYYY') {
      //   return `${month}/${day}/${year}`
      // }
      // 默认返回 YYYY-MM-DD 格式
      return `${year}/${month}/${day}`
    },
    daysSelect(index,item){
      this.daysChekedIndex = index
      this.daysCheckedId = item.id
      this.startDate = item.start_date.replace(/-/g,'/')
      this.endDate = item.end_date.replace(/-/g,'/')
      this.getAllData()
      this.nextClick = false
      this.daysCheckedId == '7Report' ? this.showTable = true : this.showTable = false
    },
    changeTableOrLineChart(){
      this.showTable = !this.showTable
      if(this.showTable || Object.keys(this.chartsAndTableData).length == 0) return
      this.$nextTick(()=>{
        this.handleEchartsData()
      })
    },
    goInputBp(){
      wx.miniProgram.navigateTo({
        url: `/pages/pressure/pages/manualMeasurementBP/newmanualMeasurementBP`
      })
    },
    getAllData(){
      this.getBpTableAndChartsData()
      this.getBpGraphData()
      this.getBpPieData('family')
      if(this.$route.query.source != 'docApp'){
        this.getBpPieData('hosp')
      }
    },
    getBpConfig(){
      let obj = {
        is_patient_platform: this.$route.query.source == 'docApp' ? '0' : '1'
      }
      getBpConfig(obj).then(res=>{
        if(res.status == 0){
          this.startDate = res.data.date_type[0].start_date.replace(/-/g,'/')
          this.endDate = res.data.date_type[0].end_date.replace(/-/g,'/')
          this.getAllData()
          this.daysPicker = res.data.date_type.concat(
            {
              start_date: this.startDate,
              end_date: this.endDate,
              type: '7日报告',
              id: '7Report'
            }
          )
        }else{
          this.$toast(res.msg)
        }
      })
    },
    getBpGraphData(){
      let obj = {
        start_time: this.startDate.replace(/\//g,'-'),
        end_time: this.endDate.replace(/\//g,'-'),
        user_id: this.$route.query.patient_id || '',
        is_patient_platform: this.$route.query.source == 'docApp' ? '0' : '1'
      }
      getBpGraph(obj).then(res=>{
        if(res.status == 0){
          this.bpMapData = res.data
        }
      })
    },
    handleEchartsData(){
      // if(Object.keys(this.chartsAndTableData).length == 0){
      //   this.sbpArr = []
      //   this.dbpArr = []
      // }else{
      //   let timeSlot = this.timeSlotChecked == 0 ? 'all' : this.timeSlotChecked == 1 ? 'morning' : 'night'
      //   this.sbpArr = this.chartsAndTableData[timeSlot + '_sbp']
      //   this.dbpArr = this.chartsAndTableData[timeSlot + '_dbp']
      // }
      if(Object.keys(this.chartsAndTableData).length == 0) return
      let timeSlot = this.timeSlotChecked == 0 ? 'all' : this.timeSlotChecked == 1 ? 'morning' : 'night'
      this.sbpArr = this.chartsAndTableData[timeSlot + '_sbp']
      this.dbpArr = this.chartsAndTableData[timeSlot + '_dbp']
      this.lineChartInit()
    },
    handleTableData(){
      if(Object.keys(this.chartsAndTableData).length == 0) return
      this.tableData = []
      let timeSlot = this.timeSlotChecked == 0 ? 'all' : this.timeSlotChecked == 1 ? 'morning' : 'night'
      let sbpArr = this.chartsAndTableData[timeSlot + '_sbp']
      let dbpArr = this.chartsAndTableData[timeSlot + '_dbp']
      let pulseArr = this.chartsAndTableData[timeSlot + '_pulse']
      sbpArr.forEach((item,index)=>{
        let obj = {
          sbp: sbpArr[index],
          dbp: dbpArr[index],
          pulse: pulseArr[index],
          date: this.dateArr[index],
        }
        this.tableData.push(obj)
      })
    },
    getBpTableAndChartsData(){
      this.isLoadingShow = true
      let obj = {
        start_time: this.startDate.replace(/\//g,'-'),
        end_time: this.endDate.replace(/\//g,'-'),
        user_id: this.$route.query.patient_id || '',
        is_patient_platform: this.$route.query.source == 'docApp' ? '0' : '1'
      }
      getBpTableAndCharts(obj).then(res=>{
        if(res.status == 0){
          this.isLoadingShow = false
          if(Array.isArray(res.data) && res.data.length == 0){
            this.chartsAndTableData = {}
            // this.handleEchartsData()  //无数据时销毁折线图
            return
          }
          let { chart_time,table_list } = res.data
          this.dateArr = chart_time
          this.chartsAndTableData = table_list
          this.$nextTick(()=>{
            this.showTable ? this.handleTableData() : this.handleEchartsData()
          })
        }else{
          this.$toast(res.msg)
          this.isLoadingShow = false
        }
      })
    },
    lineChartInit(){
      let tempSbpArr = this.sbpArr.filter(item=> item != '')
      let tempDbpArr = this.dbpArr.filter(item=> item != '')
      if(!this.$refs.lineChart) return
      let myEchart = echarts.init(this.$refs.lineChart, 'light')
      // if(this.sbpArr.length == 0 && this.dbpArr.length == 0){
      //   myEchart.dispose()
      //   return
      // }
      let options = {
        grid: {
          top: '40px',
          right: '25px',
          bottom: '40px',
          // left: '30px'
        },
        xAxis: {
          boundaryGap: false,
          type: 'category',
          axisLine: {
            color: 'rgba(0,0,0,0.15)',
            lineStyle: {
              color: '#878F99'
            }
          },
          // axisLabel: {  
          //   interval: 0,  //显示左右坐标
          //   rotate: 30,  //倾斜角度
          // },
          data: this.dateArr
        },
        yAxis: {
          type: 'value',
          // 不显示Y轴轴线  
          min: tempSbpArr.length == 0 && tempDbpArr.length == 0 ? 0 : null, // 默认最小值
          max: tempSbpArr.length == 0 && tempDbpArr.length == 0 ? 180 : null, // 默认最大值
          interval: tempSbpArr.length == 0 && tempDbpArr.length == 0 ? 30 : null, // 默认刻度间隔
          axisLine: {  
            show: false,
            lineStyle: {
              color: '#878F99'  //Y轴文字颜色
            }
          },  
          // 不显示Y轴刻度线  
          axisTick: {
            show: false  
          },  
          // 显示Y轴标签  
          axisLabel: {  
            show: true  
          }  
        },
        tooltip: {
          trigger: 'axis',
          formatter: function (params) {
            return `<div style="color: white;padding: 2px 6px;">
              <div style="border-bottom: 1px solid white;padding-bottom: 2px;color: white;">${params[0].name}</div>
              <div style="padding-top: 2px;"><span style="font-weight: bold;font-size: 15px;">${params[0].value || '--'} / ${params[1].value || '--'}</span> <span> mmHg</span></div>
              </div>`
          }
        },
        series: [
          {
            name: '收缩压',
            data: this.sbpArr,
            type: 'line',
            showAllSymbol: true,
            connectNulls: true,
            lineStyle: {
              color: '#3388FF'
            },
            itemStyle: {
              color: '#3388FF'
            },
            symbol: 'circle',
            symbolSize: 4,
            markLine: {
              silent: true,
              symbol: "none", // 移除箭头
              lineStyle: {
                color: "#FF0000", // 线的颜色
                type: "dashed", // 虚线
              },
              data: [
                { yAxis: 90 },
                { yAxis: 140 },
              ],
            },
            // 正常范围
            // markArea: {
            //   silent: true,
            //   itemStyle: {
            //     color: '#EBF3FF'
            //   },
            //   data: [
            //     [
            //       {xAxis: this.dateArr[0], yAxis: 135},
            //       {xAxis: this.dateArr[this.dateArr.length - 1], yAxis: 90}
            //     ]
            //   ],
            // }
          },
          {
            name: '舒张压',
            data: this.dbpArr,
            type: 'line',
            showAllSymbol: true,
            connectNulls: true,
            lineStyle: {
              color: '#89D245'
            },
            itemStyle: {
              color: '#89D245'
            },
            symbol: 'react',
            symbolSize: 4,
            // markArea: {
            //   silent: true,
            //   itemStyle: {
            //     color: '#89d24533'
            //   },
            //   data: [
            //     [
            //       {xAxis: this.dateArr[0], yAxis: 60},
            //       {xAxis: this.dateArr[this.dateArr.length - 1], yAxis: 85}
            //     ]
            //   ],
            // }
          }
        ]
      }
      myEchart.setOption(options)
    },
    getBpPieData(type){
      this.isLoadingShow = true
      let obj = {
        start_time: this.startDate.replace(/\//g,'-'),
        end_time: this.endDate.replace(/\//g,'-'),
        user_id: this.$route.query.patient_id || '',
        is_patient_platform: this.$route.query.source == 'docApp' ? '0' : '1',
        is_in_hospital: type == 'hosp' ? 1 : 0,
      }
      getBpPieCharts(obj).then(res=>{
        if(res.status == 0){
          this.isLoadingShow = false
          let { abnormal_list,avg,avg_abnormal,dbp,sbp,total_count,sbp_column,dbp_column,pulse_column,last_bp_data } = res.data
          if(type == 'family' && Array.isArray(last_bp_data)){
            this.pieLabelList = []
            this.sbpRange = {}
            this.dbpRange = {}
            this.bpAvgData = {}
            this.progressData = []
            return
          }
          if(type == 'hosp'){
            if(total_count == 0){
              this.pieLabelListForHosp = []
            }else{
              abnormal_list.forEach(item=>{
                item.name = item.status_display
                item.value = item.count
                item.color = this.handlePieColor(item.status_id)
              })
              this.pieLabelListForHosp = abnormal_list
              this.$nextTick(()=>{
                this.pieChartInit(res.data,'hosp')
              })
            }
            return
          }
          this.progressData = [
            {
              type: 'sbp',
              name: '收缩压',
              unit: 'mmHg',
              colorLine: this.redLine,
              ...sbp_column
            },
            {
              type: 'dbp',
              name: '舒张压',
              unit: 'mmHg',
              colorLine: this.blueLine,
              ...dbp_column
            },
            {
              type: 'pulse',
              name: '脉搏',
              unit: '次/分',
              colorLine: this.greenLine,
              ...pulse_column
            }
          ]
          let sbpMax = this.handleBpRange(sbp,'sbp','max')
          let sbpMin = this.handleBpRange(sbp,'sbp','min')
          let dbpMax = this.handleBpRange(dbp,'dbp','max')
          let dbpMin = this.handleBpRange(dbp,'dbp','min')
          this.sbpRange = {max: sbpMax,min: sbpMin}
          this.dbpRange = {max: dbpMax,min: dbpMin}
          Object.keys(avg).forEach(key=>{
            avg[key] = {value: avg[key]}
            avg_abnormal.indicators.forEach(item=>{
              if(item.code == key){
                item.value = avg[key]
                avg[key].abnormalInfo = item
              }
            })
          })
          avg.avg_abnormal = avg_abnormal.avg_abnormal
          console.log(avg,'*-*-*--*-*-*-*-*-*---*-')
          this.bpAvgData = avg
          abnormal_list.forEach(item=>{
            item.name = item.status_display
            item.value = item.count
            item.color = this.handlePieColor(item.status_id)
          })
          this.pieLabelList = abnormal_list
          if(this.pieLabelList.length == 0) return 
          this.$nextTick(()=>{
            this.pieChartInit(res.data,'family')
          })
        }
      })
    },
    handleBpRange(data,type,rangeType){
      let obj = {}
      if(data[rangeType].abnormal_info.length > 0){
        let temp = data[rangeType].abnormal_info.find(item=>{
          return item.code == type
        })
        obj = temp || {}
      }
      obj['value'] = data[rangeType].value
      return obj
    },
    handlePieColor(id){
      let color = ''
      switch(id){
        case 30:
        case 33:
        case 0:
          color = '#07C060'
          break;
        case 31:
        case 34:
          color = '#3388FF'
          break;
        case 32:
          color = '#FC6161'
          break;
        case 27:
          color = '#CA4E4E'
          break;
        case 28:
          color = '#973A3A'
          break;
        case 29:
          color = '#652727'
          break;
      }
      return color
    },
    getPercentage(val,max) {
      let num = 0
      if(!val){
        num = 0
      }else if(val > max || val === max){
        num = 100
      }else{
        num = (val / max) * 100
      }
      return num
    },
    pieChartInit(info,type) {
      let dom = type == 'family' ? this.$refs.pieChart : this.$refs.pieChartForHosp
      let myEchart = echarts.init(dom, 'light')
      const options = {
        tooltip: {
          show: false
        },
        color: info.abnormal_list.map(item=>item.color),
        series: [
          {
            type: 'pie',
            radius: ['80%', '100%'],
            avoidLabelOverlap: false,
            label: {
              show: true,
              position: 'center',
              formatter: '{total|'+ (info.total_count || 0) +'}'+'\n\r'+'{active|总测量次数}',
              rich: {
                total: {
                  color: '#333333',
                  fontSize: 26,
                  lineHeight: 37
                },
                active: {
                  color: '#999999',
                  fontSize: 12,
                  lineHeight: 17
                }
              }
            },
            silent: true,
            emphasis: {
              show: true,
              scale: false
            },
            labelLine: {
              show: false
            },
            // data: [
            //   { value: info.normal, name: '正常' },
            //   { value: info.high, name: '偏高' },
            //   { value: info.low, name: '偏低' },
            // ]
            data: info.abnormal_list || []
          }
        ]
      }
      myEchart.setOption(options)
    },
    init(){
      this.source = this.$route.query.source
      this.getBpConfig()
    }
  },
  mounted(){
    this.init()
  }
};
</script>

<style lang="scss">
.bpTrendReport {
  .inner:first-of-type {
    margin-top: 0;
  }
  .inner {
    border-radius: 8px;
    background: #fff;
    padding: 20px 14px;
    margin-top: 16px;
    .pic-wrapper{
      padding: 0 50px 20px !important;
    }
    .guideText{
      color: #84909A;
      font-size: 12px;
      text-align: left;
      text-align: left;
      margin-top: 10px;
      line-height: 20px;
    }
    .daysPicker {
      font-family: "PingFang SC";
      height: 34px;
      line-height: 34px;
      border-radius: 30px;
      display: flex;
      background: #ecf5ff;
      font-size: 15px;
      font-weight: 500;
      padding: 3px;
      align-items: center;
      .daysItem {
        flex: 1;
        color: #909399;
      }
      .daysChecked {
        background: white;
        border-radius: 30px;
        color: #377cfb;
      }
    }
    .pieTitle{
      font-size: 14px;
      color: #333333;
      font-weight: bold;
      padding-top: 20px;
    }
    .hospPieTitle{
      margin-top: 20px;
      border-top: 1px solid #EFEFEF;
    }
    .pieBlock{
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 20px;
      .pieChart{
        width: 110px;
        height: 110px;
        margin-left: 10px;
      }
      .typeList{
        display: flex;
        flex-direction: column;
        flex: 1;
        margin-left: 26px;
        margin-right: 10px;
        font-size: 15px;
        .line:first-of-type{
          margin-top: 0;
        }
        .line{
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: 18px;
          color: #666666;
          .label{
            display: flex;
            align-items: center;
          }
          .labelText{
            flex: 1;
            text-align: left;
            line-height: 20px;
          }
          .val{
            margin-left: 4px;
          }
        }
        .lineTitle{
          color: #333333;
          font-weight: bold;
        }
        .circle{
          width: 12px;
          height: 12px;
          border-radius: 50%;
          margin-right: 6px;
        }
        // .normal{
        //   background: #07C060;
        // }
        // .height{
        //   background: #FF515A;
        // }
        // .low{
        //   background: #008AFF;
        // }
      }
    }
    .bpRangeLine {
      display: flex;
      .leftSbp {
        border-right: 1px solid #f2f6fc;
      }
      .rightDbp {
        padding-left: 10px;
      }
      .leftSbp,
      .rightDbp {
        flex: 1;
        .label {
          font-size: 15px;
          font-size: 500;
          text-align: left;
          display: flex;
          img {
            width: 16px;
            height: 16px;
          }
          .unit {
            font-size: 12;
            color: #c0c6cc;
            margin-left: 4px;
          }
          .text {
            font-size: 15px;
            font-weight: 500;
            margin-left: 8px;
          }
        }
        .bpValLine {
          margin-top: 8px;
          display: flex;
          justify-content: space-around;
          .bpValItem {
            display: flex;
            flex-direction: column;
            .valLin{
              display: flex;
              align-items: center;
            }
            .val {
              font-size: 20px;
              font-weight: 500;
              color: #000000;
            }
            .valLabel {
              font-size: 12px;
              color: #878f99;
              margin-top: 6px;
            }
          }
        }
      }
    }
    .avgBpLine{
      width: 100%;
      height: 80px;
      background: rgba(255, 142, 45, 0.20);
      border-radius: 100px;
      margin-top: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      .bpVal{
        font-size: 20px;
        font-weight: 500;
      }
      .text{
        font-size: 16px;
        color: #333333;
        margin-top: 8px;
      }
    }
    .dateRange {
      color: #3f4447;
      font-size: 15px;
      font-weight: 500;
      text-align: left;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .clockIcon {
        margin-right: 4px;
      }
      .changeIcon{
        width: 48px;
        height: 28px;
      }
    }
    .lineChart {
      width: 100%;
      height: 280px;
      // border: 1px solid;
    }
    .echartsTips {
      width: 100%;
      color: #878f99;
      font-size: 12px;
      text-align: left;
      line-height: 16px;
      .colorBlock {
        width: 12px;
        height: 12px;
        display: inline-block;
        position: relative;
        top: 2px;
      }
      .sbpColor {
        background-color: #ebf3ff;
      }
      .dbpColor {
        background-color: #e7f6da;
      }
    }
    .innerTitle {
      font-size: 17px;
      color: #0a0a0a;
      font-weight: 500;
      text-align: left;
    }
    .subTitle {
      font-size: 12px;
      color: #878f99;
    }
  }
  .noData{
    margin-top: 30px;
    font-size: 15px;
    .tips{
      color: #8B959C;
    }
    .goInputBp{
      color: #3388FF;
      border-bottom: 1px solid #3388FF;
      margin-top: 10px;
      display: inline-block;
      padding-bottom: 2px;
    }
  }
  .progress-bar {
    width: 100%;
    height: 10px;
    background-color: #F5F5F5;
    border-radius: 10px;
    overflow: hidden;
  }
  
  .progress {
    // width: 50%;
    height: 10px;
    // background: linear-gradient(90deg, #8FEBC3 50%, #00C453 100%);
    border-radius: 10px;
    transition: width 0.3s ease;
  }
  .avgProgress{
    margin-top: 22px;
    display: flex;
    align-items: center;
    font-size: 15px;
    color: #666666;
    .label{
      width: 80px;
      text-align: left;
    }
    .num{
      width: 50px;
      text-align: right;
    }
  }
  .table{
    width: 100%;
    margin-top: 30px;
    // margin: 0 -14px;
    td, th {
      vertical-align: middle;
      border: 1px solid #F1F1F1;
      padding: 10px 16px;
    }
    thead{
      background: #F8F8F8;
      color: #111111;
      font-size: 13px;
    }
    tbody{
      color: #0A0A0A;
      font-size: 15px;
      font-weight: 500;
    }
  }
  .date-seven {
    height: 28px;
    line-height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 12px -14px 0;
    .date-arrow {
        width: 60px;
        display: flex;
        color: #333333;
        align-items: center;
        justify-content: space-between;
        .arrow-text {
            font-size: 13px;
        }
        .arrow-icon {
            font-size: 18px;
        }
    }
    .date-comp {
      width: 200px;
      margin: 0 8px;
      color: #F67710;
      position: relative;
      .date-comp-cont {
        height: 25px;
        line-height: 25px;
        display: flex;
        font-size: 13px;
        color: #F67710;
        border-radius: 5px;
        align-items: center;
        justify-content: center;
        border: 1px solid #ffecdd;
        .date-comp-sign {
          margin: 0 2px;
        }
        .date-comp-icon {
          margin-left: 5px;
        }
      }
      .date-comp-input {
        width: 200px;
        height: 25px;
        font-size: 13px;
        text-align: center;
        border-radius: 5px;
        border: 1px solid #ffecdd;
        position: absolute;
        top: 0;
        left: 0;
        opacity: 0;
        z-index: 999999999;
      }
    }
  }
  .timeSlot{
    display: flex;
    justify-content: space-around;
    margin-top: 26px;
    .timeItem{
      padding: 5px 18px;
      color: #999999;
      background: #F5F5F5;
      border-radius: 30px;
      font-size: 14px;
      border: 1px solid transparent;
    }
    .timeChecked{
      color: #3796fa;
      border: 1px solid #3796fa;
      background: rgba(55, 150, 250, 0.10);
    }
  }
  .arrowRotate{
    transform: rotate(180deg);
    position: relative;
  }
  .noDataText{
    color: #999999;
    font-size: 15px;
  }
  .redColor {
    color: #ff1f33 !important;
  }
  .blueColor{
    color: #008AFF !important;
  }
  .loading{
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 999999;
  }
}

</style>
