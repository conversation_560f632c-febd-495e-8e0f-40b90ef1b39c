<!--
 * @Descripttion: 医生工作室对接-患者详情
 * @version:
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-27 10:02:10
 * @LastEditors: guxiang
 * @LastEditTime: 2022-01-12 13:55:36
-->
<template>
  <div class="wrapper">
    <van-pull-refresh
      v-model="isLoading"
      @refresh="getCompletePatientExampleFun"
    >
      <div class="header">
        <van-row gutter="20">
          <van-col>
            <!-- <van-image
                            round
                            src="../../assets/images/<EMAIL>"
                        ></van-image> -->
            <img
              v-if="data.patient_info && data.patient_info.head_img_url"
              class="img"
              :src="data.patient_info.head_img_url"
              alt=""
            />
            <img
              v-else
              class="img"
              src="../../assets/images/<EMAIL>"
              alt=""
            />
          </van-col>
          <van-col span="18">
            <div class="content">
              <div class="item">
                <div class="item-1">
                  <span v-if="data.patient_info" class="name">{{
                    data.patient_info.name
                  }}</span>
                  <!-- <span> v2</span> -->
                </div>
                <div class="item-2">
                  {{ sexComputed }} |
                  {{ data.patient_info && data.patient_info.age }}岁 BMI
                  {{
                    data.vital_sign &&
                    data.vital_sign.bmi &&
                    data.vital_sign.bmi.bmi
                  }}
                </div>
              </div>
              <!-- <div class="item">
                                <div class="item-1 border">
                                    <span>1型糖尿病</span>
                                </div>
                                <div class="item-2">
                                    <span>3年3月</span>
                                </div>
                            </div> -->
            </div>
          </van-col>
        </van-row>
      </div>
      <div class="abnormal">
        <div class="title"><img src="./imgs/warn.png" /> 异常指标</div>
        <van-divider />
        <table class="table">
          <tr class="tr">
            <td></td>
            <td>结果</td>
            <td>参考值</td>
            <td>时间</td>
          </tr>
          <tr v-if="data.abnormal_pwv && data.abnormal_pwv.LbaPWV > 1400">
            <td>左侧 BA PWV</td>
            <td>
              <span v-if="!data.abnormal_pwv">-</span>
              <span v-else>
                <span v-if="data.abnormal_pwv.LbaPWV <= 1400">{{
                  data.abnormal_pwv.LbaPWV
                }}</span>
                <span v-else class="red">{{ data.abnormal_pwv.LbaPWV }} ⬆</span>
              </span>
            </td>
            <td>≤1400</td>
            <td>
              <span v-if="!data.abnormal_pwv">-</span>
              <span v-else>{{
                data.abnormal_pwv.created_at
                  ? data.abnormal_pwv.created_at.slice(0, 10)
                  : "-"
              }}</span>
            </td>
          </tr>
          <tr v-if="data.abnormal_pwv && data.abnormal_pwv.RbaPWV > 1400">
            <td>右侧 BA PWV</td>
            <td>
              <span v-if="!data.abnormal_pwv">-</span>
              <span v-else>
                <span v-if="data.abnormal_pwv.RbaPWV <= 1400">{{
                  data.abnormal_pwv.RbaPWV
                }}</span>
                <span v-else class="red">{{ data.abnormal_pwv.RbaPWV }} ⬆</span>
              </span>
            </td>
            <td>≤1400</td>
            <td>
              <span v-if="!data.abnormal_pwv">-</span>
              <span v-else>{{
                data.abnormal_pwv.created_at
                  ? data.abnormal_pwv.created_at.slice(0, 10)
                  : "-"
              }}</span>
            </td>
          </tr>
          <tr
            v-if="
              data.abnormal_chest &&
              data.abnormal_chest.fev_one_dot_zero_fvc != '' &&
              data.abnormal_chest.fev_one_dot_zero_fvc < 70
            "
          >
            <td>FEV1.0/FVC</td>
            <td>
              <span v-if="!data.abnormal_chest">-</span>
              <span v-else>
                <span v-if="data.abnormal_chest.fev_one_dot_zero_fvc >= 70">{{
                  data.abnormal_chest.fev_one_dot_zero_fvc
                }}</span>
                <span v-else class="red"
                  >{{ data.abnormal_chest.fev_one_dot_zero_fvc }} ⬇</span
                >
              </span>
            </td>
            <td>≥70</td>
            <td>
              <span v-if="!data.abnormal_chest">-</span>
              <span v-else>{{
                data.abnormal_chest.created_at
                  ? data.abnormal_chest.created_at.slice(0, 10)
                  : "-"
              }}</span>
            </td>
          </tr>
          <tr
            v-if="
              data.abnormal_chest &&
              data.abnormal_chest.fvc_perc_pred != '' &&
              data.abnormal_chest.fvc_perc_pred < 80
            "
          >
            <td>FVC(%Pred)</td>
            <td>
              <span v-if="!data.abnormal_chest">-</span>
              <span v-else>
                <span v-if="data.abnormal_chest.fvc_perc_pred >= 80">{{
                  data.abnormal_chest.fvc_perc_pred
                }}</span>
                <span v-else class="red"
                  >{{ data.abnormal_chest.fvc_perc_pred }} ⬇</span
                >
              </span>
            </td>
            <td>≥80</td>
            <td>
              <span v-if="!data.abnormal_chest">-</span>
              <span v-else>{{
                data.abnormal_chest.created_at
                  ? data.abnormal_chest.created_at.slice(0, 10)
                  : "-"
              }}</span>
            </td>
          </tr>
          <tr
            v-if="
              data.abnormal_chest &&
              data.abnormal_chest.svc_perc_pred != '' &&
              data.abnormal_chest.svc_perc_pred < 80
            "
          >
            <td>SVC(%Pred)</td>
            <td>
              <span v-if="!data.abnormal_chest">-</span>
              <span v-else>
                <span v-if="data.abnormal_chest.svc_perc_pred >= 80">{{
                  data.abnormal_chest.svc_perc_pred
                }}</span>
                <span v-else class="red"
                  >{{ data.abnormal_chest.svc_perc_pred }} ⬇</span
                >
              </span>
            </td>
            <td>≥80</td>
            <td>
              <span v-if="!data.abnormal_chest">-</span>
              <span v-else>{{
                data.abnormal_chest.created_at
                  ? data.abnormal_chest.created_at.slice(0, 10)
                  : "-"
              }}</span>
            </td>
          </tr>
          <tr
            v-if="
              data.abnormal_chest &&
              (data.abnormal_chest.qc_grade == 'D' ||
                data.abnormal_chest.qc_grade == 'F')
            "
          >
            <td>QC等级</td>
            <td>
              <span v-if="!data.abnormal_chest">-</span>
              <span v-else>
                <span
                  class="red"
                  v-if="
                    data.abnormal_chest.qc_grade == 'D' ||
                    data.abnormal_chest.qc_grade == 'F'
                  "
                  >{{ data.abnormal_chest.qc_grade }}</span
                >
                <span v-else>{{ data.abnormal_chest.qc_grade }}</span>
              </span>
            </td>
            <td>-</td>
            <td>
              <span v-if="!data.abnormal_chest">-</span>
              <span v-else>{{
                data.abnormal_chest.created_at
                  ? data.abnormal_chest.created_at.slice(0, 10)
                  : "-"
              }}</span>
            </td>
          </tr>
        </table>
      </div>
      <van-divider />
      <div class="life">
        <div class="title">
          <div class="left">生命体征</div>
          <div class="right" @click="rightClick(0)">
            {{
              data.vital_sign && data.vital_sign.update_at
                ? data.vital_sign.update_at.slice(0, 10).replace(/-/g, "/")
                : ""
            }}更新
            <van-icon v-if="extend" name="arrow-up" />
            <van-icon v-if="!extend" name="arrow-down" />
          </div>
        </div>
        <div v-if="extend" class="content">
          <div class="item">
            <div class="label">血压</div>
            <div v-if="data.vital_sign && data.vital_sign.bp">
              {{ data.vital_sign.bp.sbp }}/{{ data.vital_sign.bp.dbp }}
              <span class="unit">mmHg</span>
            </div>
            <div v-else>-</div>
          </div>
          <div class="item">
            <div class="label">心率</div>
            <div v-if="data.vital_sign && data.vital_sign.bp">
              {{ data.vital_sign.bp.pulse }} <span class="unit">bpm</span>
            </div>
            <div v-else>-</div>
          </div>
          <div class="item">
            <div class="label">脉压差</div>
            <div v-if="data.vital_sign && data.vital_sign.bp">
              {{ data.vital_sign.bp.sbp - data.vital_sign.bp.dbp }}
              <span class="unit">m²</span>
            </div>
            <div v-else>-</div>
          </div>
          <div class="item">
            <div class="label">身高</div>
            <div v-if="data.vital_sign && data.vital_sign.bmi">
              {{ data.vital_sign.bmi.height }} <span class="unit">cm</span>
            </div>
            <div v-else>-</div>
          </div>
          <div class="item">
            <div class="label">体重</div>
            <div v-if="data.vital_sign && data.vital_sign.bmi">
              {{ data.vital_sign.bmi.weight }} <span class="unit">kg</span>
            </div>
            <div v-else>-</div>
          </div>
          <div class="item">
            <div class="label">BMI</div>
            <div v-if="data.vital_sign && data.vital_sign.bmi">
              {{ data.vital_sign.bmi.bmi }} <span class="unit">kg/m²</span>
            </div>
            <div v-else>-</div>
          </div>
          <div class="sugar">血糖</div>
          <div class="item">
            <div class="label">午餐前</div>
            <div
              v-if="
                data.vital_sign &&
                data.vital_sign.bg &&
                data.vital_sign.bg.lunch_before
              "
            >
              {{ data.vital_sign.bg.lunch_before.bg }}
              <span class="unit">mmol/L</span>
            </div>
            <div v-else>-</div>
          </div>
          <div class="item">
            <div class="label">午餐后</div>
            <div
              v-if="
                data.vital_sign &&
                data.vital_sign.bg &&
                data.vital_sign.bg.lunch_after
              "
            >
              {{ data.vital_sign.bg.lunch_after.bg }}
              <span class="unit">mmol/L</span>
            </div>
            <div v-else>-</div>
          </div>
          <div class="item">
            <div class="label">晚餐前</div>
            <div
              v-if="
                data.vital_sign &&
                data.vital_sign.bg &&
                data.vital_sign.bg.dinner_before
              "
            >
              {{ data.vital_sign.bg.dinner_before.bg }}
              <span class="unit">mmol/L</span>
            </div>
            <div v-else>-</div>
          </div>
        </div>
      </div>
      <div class="life">
        <div class="title">
          <div class="left">核心指标</div>
          <div class="right" @click="rightClick(1)">
            {{
              data.core_indicator && data.core_indicator.update_at
                ? data.core_indicator.update_at.slice(0, 10).replace(/-/g, "/")
                : ""
            }}更新
            <van-icon v-if="extend2" name="arrow-up" />
            <van-icon v-if="!extend2" name="arrow-down" />
          </div>
        </div>
        <div v-if="extend2" class="content2">
          <div class="subtitle"><img src="./imgs/check.png" /> 辅助检查</div>
          <van-divider />
          <table class="table">
            <tr class="tr">
              <td>项目</td>
              <td>结果</td>
              <td>参考值</td>
              <td>时间</td>
            </tr>
            <tr>
              <td>左侧 BA PWV</td>
              <td>
                <span
                  v-if="
                    data.core_indicator &&
                    data.core_indicator.as &&
                    data.core_indicator.as.LbaPWV
                  "
                >
                  <span v-if="data.core_indicator.as.LbaPWV > 1400" class="red">
                    {{ data.core_indicator.as.LbaPWV }} ⬆
                  </span>
                  <span v-else>
                    {{ data.core_indicator.as.LbaPWV }}
                  </span>
                </span>
                <span v-else>-</span>
              </td>
              <td>≤1400</td>
              <td>
                <span
                  v-if="
                    data.core_indicator &&
                    data.core_indicator.as &&
                    data.core_indicator.as.created_at
                  "
                >
                  {{
                    data.core_indicator.as.created_at
                      ? data.core_indicator.as.created_at.slice(0, 10)
                      : "-"
                  }}
                </span>
                <span v-else>-</span>
              </td>
            </tr>
            <tr>
              <td>右侧 BA PWV</td>
              <td>
                <span
                  v-if="
                    data.core_indicator &&
                    data.core_indicator.as &&
                    data.core_indicator.as.RbaPWV
                  "
                >
                  <span v-if="data.core_indicator.as.RbaPWV > 1400" class="red">
                    {{ data.core_indicator.as.RbaPWV }} ⬆
                  </span>
                  <span v-else>
                    {{ data.core_indicator.as.RbaPWV }}
                  </span>
                </span>
                <span v-else>-</span>
              </td>
              <td>≤1400</td>
              <td>
                <span
                  v-if="
                    data.core_indicator &&
                    data.core_indicator.as &&
                    data.core_indicator.as.created_at
                  "
                >
                  {{
                    data.core_indicator.as.created_at
                      ? data.core_indicator.as.created_at.slice(0, 10)
                      : "-"
                  }}
                </span>
                <span v-else>-</span>
              </td>
            </tr>
            <tr>
              <td>内脏脂肪(cm²)</td>
              <td>
                <span
                  v-if="
                    data.core_indicator &&
                    data.core_indicator.vf &&
                    data.core_indicator.vf.Vat
                  "
                >
                  <span v-if="data.core_indicator.vf.Vat > 100" class="red">
                    {{ data.core_indicator.vf.Vat }} ⬆
                  </span>
                  <span v-else>
                    {{ data.core_indicator.vf.Vat }}
                  </span>
                </span>
                <span v-else>-</span>
              </td>
              <td>≤100</td>
              <td>
                <span
                  v-if="
                    data.core_indicator &&
                    data.core_indicator.as &&
                    data.core_indicator.as.created_at
                  "
                >
                  {{
                    data.core_indicator.as.created_at
                      ? data.core_indicator.as.created_at.slice(0, 10)
                      : "-"
                  }}
                </span>
                <span v-else>-</span>
              </td>
            </tr>
            <tr>
              <td>神经传导</td>
              <td>{{((data.auxiliary_examination && data.auxiliary_examination.dpn) ? data.auxiliary_examination.dpn.interpretation : '') || '-'}}</td>
              <td></td>
              <td>{{((data.auxiliary_examination && data.auxiliary_examination.dpn) ? data.auxiliary_examination.dpn.measdate : '') || '-'}}</td>
            </tr>
            <tr>
              <td>颈动脉超声</td>
              <td>-</td>
              <td></td>
              <td>-</td>
            </tr>
            <tr>
              <td>眼底检查</td>
              <td>-</td>
              <td></td>
              <td>-</td>
            </tr>
          </table>
        </div>
      </div>
      <div class="life">
        <div class="title">
          <div class="left">辅助检查</div>
          <div class="right" @click="rightClick(2)">
            {{
              data.auxiliary_examination && data.auxiliary_examination.update_at
                ? data.auxiliary_examination.update_at
                    .slice(0, 10)
                    .replace(/-/g, "/")
                : ""
            }}更新
            <van-icon v-if="extend3" name="arrow-up" />
            <van-icon v-if="!extend3" name="arrow-down" />
          </div>
        </div>
        <div v-if="extend3" class="content2">
          <div
            @click="
              lookDetailAsFun(
                data.auxiliary_examination.as
                  ? data.auxiliary_examination.as.id
                  : ''
              )
            "
            class="row"
          >
            <div class="left">PWV+ABI</div>
            <div class="right">
              <van-icon name="arrow" />
            </div>
          </div>

          <div
            @click="
              lookDetailEcgFun(
                data.auxiliary_examination.ecg
                  ? data.auxiliary_examination.ecg
                  : ''
              )
            "
            class="row"
          >
            <div class="left">心电图</div>
            <div class="right">
              <van-icon name="arrow" />
            </div>
          </div>

          <div
            @click="
              lookDetailVfFun(
                data.auxiliary_examination.vf
                  ? data.auxiliary_examination.vf.id
                  : ''
              )
            "
            class="row"
          >
            <div class="left">内脏脂肪</div>
            <div class="right">
              <van-icon name="arrow" />
            </div>
          </div>
          <div
            @click="
              lookDetailFmdFun(
                data.auxiliary_examination.fmd
                  ? data.auxiliary_examination.fmd.id
                  : ''
              )
            "
            class="row"
          >
            <div class="left">血管内皮</div>
            <div class="right">
              <van-icon name="arrow" />
            </div>
          </div>
          <div
            @click="
              lookDetailFundsFun(
                data.auxiliary_examination.eye
                  ? data.auxiliary_examination.eye.id
                  : ''
              )
            "
            class="row"
          >
            <div class="left">眼底检查</div>
            <div class="right">
              <van-icon name="arrow" />
            </div>
          </div>

          <div
            @click="
              lookDetailBreathFun(
                data.auxiliary_examination.chest
                  ? data.auxiliary_examination.chest.id
                  : ''
              )
            "
            class="row"
          >
            <div class="left">呼吸检查</div>
            <div class="right">
              <van-icon name="arrow" />
            </div>
          </div>
          <div
            @click="
              lookDetailLiverFun(
                data.auxiliary_examination.liver
                  ? data.auxiliary_examination.liver.id
                  : ''
              )
            "
            class="row"
          >
            <div class="left">肝脏瞬时弹性硬度检测</div>
            <div class="right">
              <van-icon name="arrow" />
            </div>
          </div>
          <div
            @click="
              lookDetailHumancomponentFun(
                data.auxiliary_examination.composition
                  ? data.auxiliary_examination.composition.id
                  : ''
              )
            "
            class="row"
          >
            <div class="left">人体成分分析</div>
            <div class="right">
              <van-icon name="arrow" />
            </div>
          </div>

          <div
            @click="
              lookDetailNerveconductionFun(
                data.auxiliary_examination.dpn
                  ? data.auxiliary_examination.dpn.id
                  : ''
              )
            "
            class="row"
          >
            <div class="left">神经传导</div>
            <div class="right">
              <van-icon name="arrow" />
            </div>
          </div>
        </div>
      </div>
    </van-pull-refresh>
  </div>
</template>

<script>
import { getCompletePatientExampleApi } from "@/api/saas.js";
const UA = navigator.userAgent;
const isAndroid = UA.indexOf("Android") > -1 || UA.indexOf("Adr") > -1; // android终端
const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); // ios终端
export default {
  data() {
    return {
      extend: true,
      extend2: true,
      extend3: true,
      isLoading: false,
      data: {},
    };
  },
  computed: {
    sexComputed() {
      if (this.data.patient_info && this.data.patient_info.sex == 1) {
        return "男";
      }
      if (this.data.patient_info && this.data.patient_info.sex == 2) {
        return "女";
      }
      return "未知";
    },
  },
  created() {
    this.getCompletePatientExampleFun(1);
  },
  methods: {
    rightClick(param) {
      if (param === 0) {
        this.extend = !this.extend;
      }
      if (param === 1) {
        this.extend2 = !this.extend2;
      }
      if (param === 2) {
        this.extend3 = !this.extend3;
      }
    },
    async getCompletePatientExampleFun(params) {
      this.isLoading = true;
      let res = await getCompletePatientExampleApi({
        patient_id: this.$route.query.patient_id,
        project_id: this.$route.query.project_id,
      });
      if (res.status == 200) {
        this.data = res.data;
        console.log(
          "%c [ this.data  ]-664",
          "font-size:13px; background:pink; color:#bf2c9f;",
          this.data
        );
        this.isLoading = false;
        if (!params) {
          this.$toast("刷新成功");
        }
      } else {
        this.$toast(res.msg);
        this.isLoading = false;
      }
    },
    // PWV + ABI + TBI
    lookDetailAsFun(id) {
      if (!id) {
        this.$toast("暂无数据");
        return;
      }
      let patient_id = this.$route.query.patient_id;
      let param = `/docWorkRoom/pwvAbiTbi?id=${id}&patient_id=${patient_id}`;
      if (process.env.NODE_ENV === "production") {
        if (isAndroid) {
          window.android.pwvAbiTbiMessage(param);
        }
        if (isIOS) {
          window.webkit.messageHandlers.pwvAbiTbiMessage.postMessage(param);
        }
      }
      if (process.env.NODE_ENV === "development") {
        this.$router.push({
          path: "/docWorkRoom/pwvAbiTbi",
          query: {
            id: id,
            patient_id: this.$route.query.patient_id,
          },
        });
      }
    },
    // 内脏脂肪
    lookDetailVfFun(id) {
      if (!id) {
        this.$toast("暂无数据");
        return;
      }
      let patient_id = this.$route.query.patient_id;
      let param = `/docWorkRoom/visceralFat?id=${id}&patient_id=${patient_id}`;
      if (process.env.NODE_ENV === "production") {
        if (isAndroid) {
          window.android.visceralFatMessage(param);
        }
        if (isIOS) {
          window.webkit.messageHandlers.visceralFatMessage.postMessage(param);
        }
      }
      if (process.env.NODE_ENV === "development") {
        this.$router.push({
          path: "/docWorkRoom/visceralFat",
          query: {
            id: id,
            patient_id: this.$route.query.patient_id,
          },
        });
      }
    },
    // 血管内皮
    lookDetailFmdFun(id) {
      if (!id) {
        this.$toast("暂无数据");
        return;
      }
      let patient_id = this.$route.query.patient_id;
      let param = `/docWorkRoom/vec?id=${id}&patient_id=${patient_id}`;
      if (process.env.NODE_ENV === "production") {
        if (isAndroid) {
          window.android.vecMessage(param);
        }
        if (isIOS) {
          window.webkit.messageHandlers.vecMessage.postMessage(param);
        }
      }
      if (process.env.NODE_ENV === "development") {
        this.$router.push({
          path: "/docWorkRoom/vec",
          query: {
            id: id,
            patient_id: this.$route.query.patient_id,
          },
        });
      }
    },
    // 眼底检测
    lookDetailFundsFun(id) {
      if (!id) {
        this.$toast("暂无数据");
        return;
      }
      let patient_id = this.$route.query.patient_id;
      let project_id = this.$route.query.project_id;
      let measdate = this.data.auxiliary_examination.eye.measdate;
      let param = `/docWorkRoom/fundusExam?patient_id=${patient_id}&project_id=${project_id}&measdate=${measdate}`;
      if (process.env.NODE_ENV === "production") {
        if (isAndroid) {
          window.android.fundusMessage(param);
        }
        if (isIOS) {
          window.webkit.messageHandlers.fundusMessage.postMessage(param);
        }
      }
      if (process.env.NODE_ENV === "development") {
        this.$router.push({
          path: "/docWorkRoom/fundusExam",
          query: {
            patient_id: patient_id,
            project_id: project_id,
            measdate: measdate,
          },
        });
      }
    },
    // 呼吸检测
    lookDetailBreathFun(id) {
      if (!id) {
        this.$toast("暂无数据");
        return;
      }
      let patient_id = this.$route.query.patient_id;
      let measdate = this.data.auxiliary_examination.chest.measdate;
      // let dept_id =
      //   this.data.vital_sign.bmi && this.data.vital_sign.bmi.department_id;
      let param = `/docWorkRoom/breathExam?patient_id=${patient_id}&measdate=${measdate}`;
      if (process.env.NODE_ENV === "production") {
        if (isAndroid) {
          window.android.breathMessage(param);
        }
        if (isIOS) {
          window.webkit.messageHandlers.breathMessage.postMessage(param);
        }
      }
      if (process.env.NODE_ENV === "development") {
        this.$router.push({
          path: "/docWorkRoom/breathExam",
          query: {
            patient_id: this.$route.query.patient_id,
            measdate: measdate,
            // measdate: dept_id,
          },
        });
      }
    },

    lookDetailEcgFun(ecg) {
      if (!ecg) {
        this.$toast("暂无数据");
        return;
      }
      let patient_id = this.$route.query.patient_id;
      // let measdate = this.data.auxiliary_examination.chest.measdate;
      let measdate = "";

      // let param = `/docWorkRoom/ecgChartScreen?patient_id=${patient_id}&measdate=${measdate}`;
      let param = `/docWorkRoom/ecgExam?patient_id=${patient_id}&measdate=${measdate}`;
      if (process.env.NODE_ENV === "production") {
        if (isAndroid) {
          window.android.breathMessage(param);
        }
        if (isIOS) {
          window.webkit.messageHandlers.breathMessage.postMessage(param);
        }
      }
      if (process.env.NODE_ENV === "development") {
        this.$router.push({
          path: "/docWorkRoom/ecgExam",
          query: {
            patient_id: this.$route.query.patient_id,
            measdate: measdate,
            // measdate: dept_id,
          },
        });
      }
    },

    // 肝脏瞬时弹性硬度检测
    lookDetailLiverFun(liver) {
      if (!liver) {
        this.$toast("暂无数据");
        return;
      }
      let patient_id = this.$route.query.patient_id;
      let param = `/docWorkRoom/liverHardness?patient_id=${patient_id}`;
      if (process.env.NODE_ENV === "production") {
        if (isAndroid) {
          window.android.breathMessage(param);
        }
        if (isIOS) {
          window.webkit.messageHandlers.breathMessage.postMessage(param);
        }
      }
      if (process.env.NODE_ENV === "development") {
        this.$router.push({
          path: "/docWorkRoom/liverHardness",
          query: {
            patient_id: this.$route.query.patient_id,
          },
        });
      }
    },

    // 人体成分分析
    lookDetailHumancomponentFun(liver) {
      if (!liver) {
        this.$toast("暂无数据");
        return;
      }
      let patient_id = this.$route.query.patient_id;
      let param = `/docWorkRoom/humancomponent?patient_id=${patient_id}`;
      if (process.env.NODE_ENV === "production") {
        if (isAndroid) {
          window.android.breathMessage(param);
        }
        if (isIOS) {
          window.webkit.messageHandlers.breathMessage.postMessage(param);
        }
      }
      if (process.env.NODE_ENV === "development") {
        this.$router.push({
          path: "/docWorkRoom/humancomponent",
          query: {
            patient_id: this.$route.query.patient_id,
          },
        });
      }
    },

    // 神经传导
    lookDetailNerveconductionFun(liver) {
      if (!liver) {
        this.$toast("暂无数据");
        return;
      }
      let patient_id = this.$route.query.patient_id;
      let param = `/docWorkRoom/nerveconduction?patient_id=${patient_id}`;
      if (process.env.NODE_ENV === "production") {
        if (isAndroid) {
          window.android.breathMessage(param);
        }
        if (isIOS) {
          window.webkit.messageHandlers.breathMessage.postMessage(param);
        }
      }
      if (process.env.NODE_ENV === "development") {
        this.$router.push({
          path: "/docWorkRoom/nerveconduction",
          query: {
            patient_id: this.$route.query.patient_id,
          },
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.wrapper {
  background: white;
  font-size: 12px;
  padding: 10px 0;
  box-sizing: border-box;
  .red {
    color: #d0021b;
  }
  .header {
    padding: 0 22px;
    text-align: left;
    .img {
      width: 68px;
      height: 68px;
    }
    .content {
      box-sizing: border-box;
      height: 68px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      font-size: 14px;
      color: #000;
      .name {
        font-size: 21px;
      }
    }
    .item {
      display: flex;
      &:nth-child(2) {
        margin-top: 8px;
      }
      .item-1 {
        width: 80px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .item-2 {
        display: inline-flex;
        align-items: center;
        margin-left: 9px;
      }
      .border {
        background-color: #09acf8;
        color: #fff;
        border-radius: 9px;
        box-sizing: border-box;
        padding: 4px;
        text-align: center;
      }
    }
  }
  .abnormal {
    margin: 20px 10px 18px;
    box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    .van-divider {
      margin: 0;
    }
    .title {
      color: #000;
      font-size: 16px;
      padding: 8px;
      text-align: left;
      img {
        width: 16px;
        height: 16px;
      }
    }
  }
  .table {
    width: 100%;
    text-align: center;
    color: #333;
    .tr {
      td {
        width: 60px;
        font-weight: 600;
      }
    }
    td {
      padding: 10px;
    }
  }
  .life {
    .title {
      display: flex;
      justify-content: space-between;
      margin-top: 15px;
      padding: 0 19px;
      .left {
        font-size: 16px;
        color: #333333;
      }
      .right {
        font-size: 12px;
        color: #666666;
        &:hover {
          cursor: pointer;
        }
      }
    }
    .content {
      margin-top: 20px;
      display: flex;
      justify-content: space-around;
      flex-wrap: wrap;
      border-bottom: 1px solid #ebedf0;
      .item {
        width: 33%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        margin-bottom: 16px;
        font-size: 12px;
        color: #333333;
        font-weight: 600;
        &:nth-child(1),
        &:nth-child(2),
        &:nth-child(4),
        &:nth-child(5),
        &:nth-child(7),
        &:nth-child(8) {
          border-right: 1px solid #f1f1f1;
        }
        .label {
          margin-bottom: 5px;
        }
        .unit {
          color: #999;
          font-weight: 400;
        }
      }
      .sugar {
        width: 100%;
        margin-bottom: 6px;
      }
    }
    .content2 {
      margin-top: 20px;
      border-bottom: 1px solid #ebedf0;
      padding: 0 19px;

      .title {
        color: #333;
        font-weight: 600;
        font-size: 14px;
      }
      .subtitle {
        color: #333;
        font-weight: 600;
        font-size: 14px;
        text-align: left;
        img {
          width: 18px;
          height: 18px;
          vertical-align: text-top;
        }
      }
      .row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 16px;
        font-size: 12px;
        // color: #333333;
        font-weight: 600;
        &:hover {
          cursor: pointer;
        }
      }
      .right {
        .van-icon {
          font-size: 14px;
          color: #666666;
        }
      }
    }
  }
}
</style>
