<template>
  <div class="content">
    <van-list
      v-model="loading"
      :finished="finished"
      :immediate-check="false"
      finished-text="没有更多了"
      @load="getListFunc"
      offset="300"
    >
      <div v-for="(item, index) in list" :key="item.id">
        <div class="title cursor" @click="getDetailFun(index, item)">
          <div :class="[{'active-bold':item.extend==true}]" class="left" v-if=" item.measured_at"> {{measureAtChangeFunc(item.measured_at)[0]}}</div>
          <div v-if=" item.measured_at" class="right unit">
            {{measureAtChangeFunc(item.measured_at)[1]}}
            <van-icon class="unit" v-if="item.extend" name="arrow-up" />
            <van-icon class="unit" v-if="!item.extend" name="arrow-down" />
          </div>
        </div>
        <div v-if="item.extend" class="result">
          <div class="flex">
            <div class="word">基础代谢率</div>
            <div class="word-sub">
                <abnormalIndicator
                  code="bmr"
                  :record="item"
                  :codeValue="item.bmr"
                />
                <span class="unit">kcal</span>
            </div>
          </div>
          <div class="flex">
            <div  class="word">骨骼肌</div>
            <div  class="word-sub">
              <abnormalIndicator
                  code="smm"
                  :record="item"
                  :codeValue="item.smm"
                />
              <span class="unit">kg</span>
            </div>
          </div>
          <div class="flex">
            <div  class="word">体脂肪</div>
            <div  class="word-sub">
              <abnormalIndicator
                  code="fm"
                  :record="item"
                  :codeValue="item.fm"
                />
              <span class="unit">kg</span>
            </div>
          </div>
          <div class="flex">
            <div  class="word">体脂百分比</div>
            <div  class="word-sub">
              <abnormalIndicator
                  code="pbf"
                  :record="item"
                  :codeValue="item.pbf"
                />
              <span class="unit">%</span>
            </div>
          </div>
          <div class="flex">
            <div  class="word">体内水分</div>
            <div  class="word-sub">
              <abnormalIndicator
                  code="tbw"
                  :record="item"
                  :codeValue="item.tbw"
                />
               <span class="unit">L</span>
              </div>
          </div>
          <div class="flex">
            <div  class="word">去脂肪质量</div>
            <div  class="word-sub">
              <abnormalIndicator
                  code="ffm"
                  :record="item"
                  :codeValue="item.ffm"
                />
              <span class="unit">kg</span>
            </div>
          </div>
          <div class="flex">
            <div  class="word">蛋白质</div>
            <div  class="word-sub">
              <abnormalIndicator
                  code="pm"
                  :record="item"
                  :codeValue="item.pm"
                />
              <span class="unit">kg</span>
            </div>
          </div>
          <!--报告-->
            <report-item
                v-if="item.file_paths && item.file_paths.length > 0"
                :fileList="item.file_paths"
                pageName="人体成分分析"
                pageType="humancomponent">
            </report-item>
        </div>
      </div>
    </van-list>
  </div>
</template>

<script>
import reportItem from './sickerMedicalRecord/components/reportItem.vue'
import { getCompositionList } from '@/api/saas.js'
const UA = navigator.userAgent;
const isAndroid = UA.indexOf("Android") > -1 || UA.indexOf("Adr") > -1; // android终端
const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); // ios终端
import abnormalIndicator from './sickerMedicalRecord/components/abnormalIndicator.vue';
export default {
  components: {
    reportItem,
    abnormalIndicator
  },
  data() {
    return {
      list: [],
      loading: false,
      finished: true,
    };
  },
  computed: {
    searchInfo() {
        return this.$store.getters['docWorkRoom/searchInfo']
    },
  },
  methods: {
    async getListFunc() {
      let {patient_id} = this.$route.query;

      let {data} =  await getCompositionList({
        patient_id, 
        //...this.searchInfo
        })
      this.list = data;
      this.list.forEach(i=>{
        this.$set(i , 'extend' , false)
      })

    },

    measureAtChangeFunc(time){
      return time.split(' ')

    },
    getDetailFun(index) {
      this.list[index].extend = !this.list[index].extend;
    },
  },
  mounted() {
    this.getListFunc();
  },
};
</script>

<style lang="scss" scoped>
.content {
  font-size: 14px;

  .title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 16px;
    padding: 16px 16px;
    border-bottom: 1px solid #F1F1F1;


  }

  .right{
    font-size: 14px;
  }

  .result{
    font-size: 12px;

  }
  .flex{
    display: flex;
    justify-content: space-between;
        padding: 14px 32px;
    border-bottom: 1px solid #F1F1F1 ;
    background: #FBFBFB;

    .word{
      font-size: 16px;
    }

    .word-sub{
      font-size: 14px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  .flex-comments{
    padding: 14px 32px;
    border-bottom: 1px solid #F1F1F1 ;
    background: #FBFBFB;
    text-align: left;

    .word{
      font-size: 16px;
      margin-bottom: 14px;
    }

    .word-sub{
      font-size: 14px;
      line-height: 18px;
    }
  }

  .unit{
    color: #909399;
    margin-left: 4px;
  }
}
.cursor{
  cursor: pointer;
}


.setflex{
  display: flex;
  justify-content: space-between;
  margin: 20px 0;

}

::v-deep  .van-tab__text{
  font-size: 16px;
}

.result{
  // display: flex;
  // justify-content: space-between;


}
.date-class{
  color: #999;
  padding: 4px 12px;
  border: 1px solid #999;
  border-radius: 4px;
}

.ml{
  margin-left: 16px;
}
.mr{
  margin-right: 16px;
}

.result-chart{
  display: flex;
  justify-content: space-between;

  .result-title{
    margin-left: 16px;
  }
  .result-allscreen{
    margin-right: 16px;

  }
}
//::v-deep .van-tab--active {
//  color: #000000;
//  font-weight: 500;
//}
//::v-deep .van-tabs__line {
//  background-color: #EE7800;
//  width: 33px !important;
//  height: 4px;
//  border-radius: 2px;
//}
.active{
  border: 1px solid #FAAD14;
  color: #FAAD14;
  background: #FCF6EC;
}

.active-bold{
  font-weight: bold;
}
.mobile-png{
  width: 14px;
  height: 14px;
}
</style>
