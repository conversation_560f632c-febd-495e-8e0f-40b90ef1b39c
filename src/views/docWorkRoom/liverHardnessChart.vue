<template>
  <div>
    <div class="chart-box" ref="stiffnessChartBox"></div>
    <div class="chart-box" ref="capChartBox"></div>
  </div>
</template>

<script>
import echarts from "echarts";

import { getLiverChart } from "@/api/saas.js";

export default {
  props: {
    range_date: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      key: "",
      capData: {},
      stiffnessData: {},
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.getList()

    });
  },
  watch: {
    range_date(newValue) {
      // if(newValue){
      this.$nextTick(() => {
        this.getList()
      });
      // }
    },
  },
  computed: {
      searchInfo() {
          return this.$store.getters['docWorkRoom/searchInfo']
      },
  },
  methods: {
    async getList() {
      let { patient_id } = this.$route.query;
      let { cap, stiffness } = (
        await getLiverChart({
          period: this.range_date ? this.range_date : '',
          patient_id: patient_id,
          // ...this.searchInfo
        })
      ).data;
      this.capData = cap
      this.stiffness = stiffness
      this.liverHardnessChart("stiffnessChartBox"); // 肝脏硬度
      this.liverHardnessChart("capChartBox"); // 脂肪衰减
    },
    async liverHardnessChart(ref) {
      let myEcharts = echarts.init(this.$refs[ref]);

      const legend = ref === 'stiffnessChartBox' ? '肝脏硬度' : '脂肪衰减'
      const lineColor = ref === 'stiffnessChartBox' ? '#E6CFB1' : '#81D3F8'
      const data = ref === 'stiffnessChartBox' ? this.stiffness : this.capData
      const xData = data.date.map((i) => i.split(' ')[0]);

      myEcharts.setOption({
        legend: {
          data: [legend],
          itemWidth: 15,
          itemGap: 44,
          icon: 'circle',
          x: 'center',
          y: 'top'
        },
        dataZoom: [
          //1.横向使用滚动条
          {
            type: "slider", //有单独的滑动条，用户在滑动条上进行缩放或漫游。inside是直接可以是在内部拖动显示
            show: data.date.length >= 10 ? true : false, //是否显示 组件。如果设置为 false，不会显示，但是数据过滤的功能还存在。
            start: 70, //数据窗口范围的起始百分比0-100
            end: 100, //数据窗口范围的结束百分比0-100
            zoomLock: true, //锁定选择的区域，不可以缩放，只能平移。不设置的话直接有默认值就行。
            filterMode: "empty",
            showDetail: true,
            minValueSpan: 10,
            xAxisIndex: [0], // 此处表示控制第一个xAxis，设置 dataZoom-slider 组件控制的 x轴 可是已数组[0,2]表示控制第一，三个；xAxisIndex: 2 ，表示控制第二个。yAxisIndex属性同理
            brushSelect: true,
            height: 20,
            bottom: 20,
          },
            //2.在内部可以横向拖动
          {
            type: "inside", // 内置于坐标系中
            start: 0,
            end: 100,
            zoomLock: true, //锁定选择的区域，不可以缩放，只能平移。不设置的话直接有默认值就行。
            xAxisIndex: [0],
          },
        ],

        grid: {
          containLabel: true,
          left: 12,
          right: 30,
          bottom: 60,
        },

        toolbox: {
          show: false,
          feature: {
            dataZoom: {
              yAxisIndex: "none",
            },
            dataView: { readOnly: false },
            magicType: { type: ["line", "bar"] },
            restore: {},
            saveAsImage: {},
          },
        },

        xAxis: {
          type: "category",
          boundaryGap: true,
          data: xData
        },

        yAxis: {
          name: ref === 'stiffnessChartBox' ? 'kPa' : 'dB/m',
          axisLabel: {
            formatter: "{value}",
          },
          type: "value",
          nameGap: 32,
          nameTextStyle: {
            color: '#74787C',
            align: 'right'
          },
          splitLine: {
            //横坐标变虚线
            show: true,
            lineStyle: {
              type: [2, 16],
              color: "rgba(0, 0, 0, 0.25)",
              dashOffset: 16,
            },
          },
        },
        series: [
          {
            type: 'line',
            name: legend,
            symbol: 'circle',
            symbolSize: 8,
            data: data.value,
            itemStyle: {
              normal: {
                color: lineColor,
                lineStyle: {
                  color: lineColor,
                },
              },
            },
          }
        ],
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: (val) => {
            const { dataIndex } = val[0]

            return `
              <div>${data.date[dataIndex]}</div>
              <hr style="margin:8px 0;" />
              <div >
                <div style="display: flex;justify-content: space-between;"><div>肝脏硬度：</div>  ${
                !this.stiffness.value[dataIndex] ? '--' : this.stiffness.value[dataIndex]
              } kPa</div>
                <div style="display: flex;justify-content: space-between;"><div>脂肪衰减：</div> ${
              !this.capData.value[dataIndex] ? '--' : this.capData.value[dataIndex]
            } dB/m</div>
              </div>

            `;
          },
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.chart-box {
  width: 100%;
  height: calc(100vh - 80px);
}

.box100 {
  width: 100%;
  height: 100vh;
}
</style>
