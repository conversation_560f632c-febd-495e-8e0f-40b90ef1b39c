<!--
 * @Descripttion: 身高体重7日变化
 * @version: 
 * @Author: <PERSON><PERSON><PERSON><PERSON>n
 * @Date: 2021-06-22 14:45:01
 * @LastEditors: l<PERSON><PERSON><PERSON>n
 * @LastEditTime: 2021-07-02 13:59:03
-->
<template>
  <div class="wrapper">
    <div class="content">
      <div class="date-seven">
        <div @click="beforeStage" class="date-arrow">
            <van-icon name="arrow-left" class="arrow-icon" />
            <span class="arrow-text">上阶段</span>
        </div>
        <div class="date-comp">
            <div class="date-comp-cont">
                <span>{{ dateFormat(startDate, '.') }}</span>
                <b class="date-comp-sign">-</b>
                <span>{{ dateFormat(endDate, '.') }}</span>
                <cc-svg-icon icon-class="date" class="date-comp-icon"/>
            </div>
            <input
              type="date"
              v-model="endDate"
              :max="dateFormat(new Date())"
              class="date-comp-input"
            >
        </div>
        <div @click="afterStage" class="date-arrow" v-if="nextClick == false">
            <span class="arrow-text">下阶段</span>
            <van-icon name="arrow" class="arrow-icon"/>
        </div>
        <div class="date-arrow" v-if="nextClick == true">
          <span class="arrow-text" style="color:#ccc;">下阶段</span>
          <van-icon name="arrow" class="arrow-icon" color="#ccc"/>
        </div>
      </div>
      <p class="date-tips">根据所选日期往前7日体重变化</p>
      <div class="chart-style">
        <div class="chart-box" ref="chartBox"></div>
      </div>
      <div class="btnChart">
        <span @click="changeTab('weight')" :class="{'btn': 1, 'btn-select': select}">体重</span>
        <span @click="changeTab('BMI')" :class="{'btn': 1, 'btn-select': !select}">肥胖指数(BMI)</span>
      </div>
    </div>
  </div>
</template>

<script>
import moment from 'moment'
import echarts from 'echarts'
import { heightWeightGraphData } from '@/api/docWorkRoom.js'

export default {
  data: () => {
    return {
      select: true,
      endDate: '',
      dinStatus: 0,
      chartData: [],
      chartDataBMI: [],
      chartDate: [],
      weekDate: [],
      isLoading: false,
      nextClick: false
    }
  },
  computed: {
    // 开始时间
    startDate() {
      let endDate = this.endDate
      if (isNaN(Date.parse(endDate))) endDate = new Date()
      return this.dateCount(endDate, -6)
    }
  },
  watch: {
    endDate(newVal, oldVal) {
      let todayDate = new Date()
      if (newVal == moment(todayDate).format('YYYY-MM-DD')){
        this.nextClick = true
      } else {
        this.nextClick = false
      }
      // 全部日期
      let weekDate = []
      for (var i = 0; i < 7; i++) {
        let oDate = this.dateCount(this.endDate, -i, '/')
        let year = oDate.slice(0, 5)
        let date = oDate.slice(5)
        weekDate.push({ year, date })
      }
      this.weekDate = weekDate.reverse()
      // 初始化改变时 返回
      if (isNaN(Date.parse(oldVal))) return
      // 如果不是日期格式 返回
      if (isNaN(Date.parse(newVal))) {
        this.endDate = this.dateFormat(new Date())
        this.getHeightWeightGraphData()
        return
      }
      // 获取运动步数趋势
      this.getHeightWeightGraphData()
    }
  },
  created () {
    // 初始化
    this.init()
  },
  methods: {
    /**
     * 初始化
     */
    init () {
      this.endDate = this.dateFormat(new Date())
      // 获取运动步数趋势
      this.getHeightWeightGraphData()
    },
    /**
     * 获取运动步数趋势
     */
     getHeightWeightGraphData() {
      this.isLoading = true
      heightWeightGraphData(this.endDate, this.$route.query.user_id).then(res => {
        if (res.code === 200) {
          this.chartDate = res.data.date
          this.chartData = res.data.weight
          this.chartDataBMI = res.data.bmi
          this.$nextTick(() => {
            // 折线图初始化
            this.drawHW('chartBox', this.chartDate, this.chartData)
          })
          this.isLoading = false
        } else {
          this.$toast(res.msg)
        }
      })
    },
    /**
     * 日期格式化
     * @param {String | Date} date 日期
     * @param {String} sep 分隔符
     * @return {String} 格式化日期
     */
    dateFormat(date, sep = '-') {
      let oDate = new Date(date)
      let y = oDate.getFullYear()
      let m = oDate.getMonth() + 1
      let d = oDate.getDate()
      if (m < 10) m = `0${m}`
      if (d < 10) d = `0${d}`
      return `${y}${sep}${m}${sep}${d}`
    },
    /**
     * 日期计算
     * @param {String} date 日期
     * @param {Number} index 未来或过去的天数（正数未来，负数过去）
     * @param {String} sep 分隔符
     */
    dateCount(date, index, sep = '-') {
      let oDate = new Date(date)
      // 天数转换为毫秒数
      let oMin = index * 24 * 60 * 60 * 1000
      let newDate = new Date(oDate.getTime() + oMin)
      return this.dateFormat(newDate, sep)
    },
    /**
     * 折线图初始化
     * @param {String} ref DOM
     * @param {Array} yAxisData y轴数据
     * @param {Array} xAxisData x轴数据
     * @param {String} type 类型（0：全部; -1:单日趋势图）
     */
    drawHW (ref, date, wData) {
      // console.log(xAxisData)
      let myEchart = echarts.init(this.$refs[ref])
      let myOption = {
        title: {
                    text: 'kg',
                    left: '4rem',
                    textStyle: {
                        color: '#666666',
                        fontSize: '0.25rem'
                    }
                },
                tooltip: {
                    trigger: 'axis'
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: true,
                    axisLine: {
                        lineStyle: {
                            color: "#ccc"
                        }
                    },
                    axisLabel: {
                        color: '#666'
                    },
                    data: date
                },
                yAxis: {
                    type: 'value',
                    axisLine: {
                        lineStyle: {
                            color: "#ccc"
                        }
                    },
                    axisLabel: {
                        color: '#666'
                    },
                    min: 0,
                    max: 300,
                    splitLine:{
                        lineStyle:{
                            color: "#eee"
                        }
                    }
                },
                series: [
                    {
                        name:'体重',
                        type:'line',
                        symbol: 'circle',
                        smooth: true,
                        symbolSize: 9,
                        itemStyle: {
                            normal: {
                                color: '#61B1F0',
                                label: {
                                    show: true,
                                    color: '#333333'
                                },
                                lineStyle: {
                                    color: '#10A2F6',
                                    width: 1
                                }
                            }
                        },
                        data: wData
                    }
                ]
      }

      myEchart.setOption(myOption)
    },
    drawBMI(ref, date, bData) {
      let myEchart = echarts.init(this.$refs[ref])
      let myOption = {
                title: {
                    text: 'kg/m^2',
                    left: '4rem',
                    textStyle: {
                        color: '#666666',
                        fontSize: '0.25rem'
                    }
                },
                tooltip: {
                    trigger: 'axis'
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: true,
                    axisLine: {
                        lineStyle: {
                            color: "#ccc"
                        }
                    },
                    axisLabel: {
                        color: '#666'
                    },
                    data: date
                },
                yAxis: {
                    type: 'value',
                    axisLine: {
                        lineStyle: {
                            color: "#ccc"
                        }
                    },
                    axisLabel: {
                        color: '#666'
                    },
                    min: 0,
                    max: function (value) {
                        return value.max + 20;
                    },
                    splitLine:{
                        lineStyle:{
                            color: "#eee"
                        }
                    }
                },
                series: [
                    {
                        name:'BMI',
                        type:'line',
                        symbol: 'circle',
                        smooth: true,
                        symbolSize: 9,
                        itemStyle: {
                            normal: {
                                color: '#61B1F0',
                                label: {
                                    show: true,
                                    color: '#333333'
                                },
                                lineStyle: {
                                    color: '#10A2F6',
                                    width: 1
                                }
                            }
                        },
                        data:bData
                    }
                ]
            };
            if (bData.length == 0) {
              myOption.yAxis.max = 50;
            }
            myEchart.setOption(myOption)
    },
    /**
     * 上阶段
     */
    beforeStage() {
      if (this.isLoading) return

      this.endDate = this.dateCount(this.startDate, -1)
    },
    /**
     * 下阶段
     */
    afterStage() {
      if (this.isLoading) return

      // 开始时间为计算属性，因此从开始日期推未来推13天
      let oDate = this.dateCount(this.startDate, 13)
      let newDate = new Date(oDate)
      let todayDate = new Date()
      if (newDate.getTime() > todayDate.getTime()) oDate = this.dateFormat(todayDate)
      this.endDate = oDate
    },
    // 体重， 肥胖指数BMI切换
    changeTab(flag) {
      if (flag === 'weight') {
        this.select = true
        this.drawHW('chartBox', this.chartDate, this.chartData)
        return
      }
      if (flag === 'BMI') {
        this.select = false
        this.drawBMI('chartBox', this.chartDate, this.chartDataBMI)
        return
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "@/assets/scss/trendStat.scss";
.btnChart {
  display: flex;
  flex-wrap: wrap;
  margin-top: 20px;
  justify-content: space-around;
  .btn {
    width: 40%;
    height: 30px;
    font-size: 16px;
    color: #666666;
    font-weight: 400;
    line-height: 30px;
    border-radius: 3px;
    box-sizing: border-box;
    border: 1px solid #E8E8E8;
  }
  .btn-select {
    color: #FF9600;
    border: 1px solid #FF9600;
  }
}
.chart-style {
  width: 100%;
  height: 282px;
  margin-top: 20px;
  .chart-box {
    width: 100%;
    height: 100%;
  }
}

</style>
