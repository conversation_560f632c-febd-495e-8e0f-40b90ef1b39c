<!-- 核销码 -->
<template>
  <div class="equityVerificationCode">
    <div class="flower"></div>
    <div class="box">
      <div class="header">
        <div class="title"></div>
      </div>
      <div class="barcode">
        <div class="code">
          {{ this.$route.query.code }}
        </div>
      </div>
      <div class="explain_title">
        <div>权益码说明</div>
      </div>
      <div class="explain_content">
        <div style="display: flex;">
          <div>1.</div>
          <div style="margin-left: 10px;">权益码需要按规则使用，并进行记录。</div>
        </div>
        <div style="display: flex;">
          <div>2.</div>
          <div style="margin-left: 10px;">关闭本页面后，可以重新点击患者已核销的券重新查看权益码。</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      codeList: []
    };
  },
  created() {
    this.init()
  },
  mounted() {
  },
  methods: {
    init() {
      if (this.$route.query.code) {
        this.codeList = this.$route.query.code
      }
    },
    // 返回首页
    handleConfirm() {
      this.noDoubleTap(() => {
        wx.miniProgram.switchTab({
          url: `/pages/main/home/<USER>
        })
      })
    }
  },
  watch: {}
};
</script>
<style lang="scss">
.equityVerificationCode {
  width: 100vw;
  height: 100vh;
  background-image: url('../imgPage/equitybg.png');
  background-repeat: no-repeat;

  position: fixed;
  top: 0;
  left: 0;
  z-index: -10;
  zoom: 1;
  background-size: cover;
  -webkit-background-size: cover;
  -o-background-size: cover;
  background-position: center 0;
  padding: 0 30px;
  box-sizing: border-box;
  // position: relative;

  .flower {
    background-image: url('../imgPage/equityflower.png');
    background-repeat: no-repeat;
    background-size: contain;
    width: 162px;
    height: 207px;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: 70px;
  }

  .box {
    // width: 255px;
    height: 443px;
    flex-shrink: 0;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: 196px;
    border-radius: 24px;
    background: #FFF;
    box-shadow: 0px 16px 16px 0px rgba(228, 191, 116, 0.10);
    padding: 38px 30px 0;

    .header {
      .title {
        background-image: url('../imgPage/verificationCode.png');
        background-repeat: no-repeat;
        background-size: contain;
        width: 234px;
        height: 24px;
        margin-left: 11px;
      }
    }

    .barcode {
      width: 255px;
      height: 72px;
      margin-top: 29px;
      display: flex;
      border-radius: 8px;
      background: #FFF5E5;
      line-height: 72px;
      display: flex;
      justify-content: center;
      align-items: center;

      .code {
        height: 39px;
        color: #000;
        font-family: DIN;
        font-size: 32px;
        font-style: normal;
        font-weight: 700;
        line-height: 39px;
        letter-spacing: 6.08px;
      }
    }

    .explain_title {
      width: 70px;
      height: 20px;
      margin-top: 44px;
      display: flex;
      padding: 2px 8px;
      justify-content: center;
      align-items: center;
      border-radius: 71px;
      background: #FFE8D6;
      line-height: 24px;

      div {
        color: #000;
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 700;
        line-height: normal;
      }
    }
  }

  .explain_content {
    margin-left: 8px;
    margin-top: 15px;
    width: 240px;
    min-height: 186px;
    flex-shrink: 0;
    color: #797979;
    text-align: justify;
    font-family: "PingFang SC";
    font-size: 15px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
  }
}
</style>