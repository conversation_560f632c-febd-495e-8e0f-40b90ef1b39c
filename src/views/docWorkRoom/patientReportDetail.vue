<template>
    <div class="div">
        <img 
            @click="showLargeImage"
            class="img" 
            :src="imgs[0]" 
        >
    </div>

</template>

<script>
import { getPatientReportDetailApi } from '@/api/saas.js'
import { decode } from 'js-base64'
export default {
    data () {
        return {
            imgs: [],
            imgsList: []
        }
    },
    created () {
        this.getPatientReportDetailFun()
        this.imgsList = [decode(this.$route.query.pngUrl)]
    },
    methods: {
        async getPatientReportDetailFun () {
            let res = await getPatientReportDetailApi({
                report_record_id: this.$route.query.report_record_id
            })
            let blob = new Blob([res]);
            let reader = new FileReader()
            reader.readAsDataURL(blob)
            reader.onload = (e) => {
                this.imgs = [reader.result]
            }
        },
        // 显示大图
        showLargeImage () {
            let param = JSON.stringify({
                index: 0,
                imgs: this.imgsList
            })
            const UA = navigator.userAgent
            const isAndroid = UA.indexOf('Android') > -1 || UA.indexOf('Adr') > -1 // android终端
            const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
             // 原生app方法名称：showLargeImage
            if (isAndroid) {
                window.android.showLargeImage(param)
            } else if (isIOS) {
                window.webkit.messageHandlers.showLargeImage.postMessage(param)
            } else {
                Toast({
                    message: '无法放大图片',
                    type: 'html',
                    forbidClick: true,
                    duration: 1000
                })
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.div {
    font-size: 12px;
    height: 100vh;
    // overflow-y: auto;
}

.img {
    width: 100%;
    // height: 100%;
}
</style>