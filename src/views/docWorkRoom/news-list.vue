<template>
  <div class="news">
    <!-- <div class="header">
      <img src="https://zz-im-static.zz-med-test.com/project_applied%2F120010%2F20240408122913_aa6205b9-48eb-4747-be4b-447650899c87.png?auth_key=1733368157-1211938-0-2ccc63f1331cb8031a75a3a07e63e77f" alt="icon" />
    </div> -->

    <van-list
      v-model="loading"
      :finished="finished"
      finished-text="没有更多了"
      @load="handleLoad"
    >
      <ul>
        <li v-for="(item, index) in news_data.list" :key="index" @click="handleClick(item)">
          <div class="cover">
            <img :src="item.icon" alt="cover" />
          </div>
          <div class="content">
            <div class="title">{{ item.title }}</div>
            <!-- <div class="date">{{ item.date_at ? item.date_at.split(' ')[0] : '' }}</div> -->
            <span class="type" v-show="isApphome" v-text="item.project_name || ''"></span>
          </div>
        </li>
      </ul>
    </van-list>
    <template v-if="isShare">
      <wx-open-launch-app
        id="top_btn"
        appid="wxd4d251ee3ba85b70"
        :extinfo="appExtInfo"
        style="position: fixed;left: 0;top: 0;width: 100%;background-color: transparent;z-index: 9999999999;"
      >
        <script type="text/wxtag-template">
          <style>
            .topOpenAppOuter {
              width: 100%;
              height: 50px;
              line-height: 50px;
              background-color: rgba(255,255,255,0.6);
              position: relative;
            }
            .logo {
              width: 35px;
              height: 35px;
              position: absolute;
              left: 16px;
              top: 8px;
            }
            .title {
              font-size: 16px;
              color: #181818;
              height: 50px;
              line-height: 50px;
              position: absolute;
              left: 56px;
            }
            .topOpenAppBtn {
              width: 80px;
              border-radius: 50px;
              text-align: center;
              height: 30px;
              line-height: 30px;
              background: #F7830D;
              font-size: 14px;
              color: white;
              position: absolute;
              right: 16px;
              top: 10px;
            }
          </style>
          <div class="topOpenAppOuter">
            <img class="logo" src="https://ares.zz-med.com/doctorapp/docAppLogo.png" alt="">
            <span class="title">医生工作室</span>
            <div class="topOpenAppBtn">打开APP</div>
          </div>
        </script>
      </wx-open-launch-app>
      <wx-open-launch-app
        id="bottom_btn"
        appid="wxd4d251ee3ba85b70"
        :extinfo="appExtInfo"
        style="position: fixed;left: 0;bottom: 120px;width: 100%;background-color: transparent;z-index: 9999999999;"
      >
        <script type="text/wxtag-template">
          <style>
            .bottomOpenAppBtn {
              width: 260px;
              border-radius: 50px;
              text-align: center;
              height: 50px;
              line-height: 50px;
              margin-left: 50%;
              position: relative;
              left: -130px;
              background: #F7830D;
              font-size: 18px;
              color: white;
              box-shadow: 0 4px 8px #FDD2AB;
            }
          </style>
          <div class="bottomOpenAppBtn">医生工作室APP内打开</div>
        </script>
      </wx-open-launch-app>
    </template>
  </div>
</template>

<script>
  import { getSystemType, isMobile } from '@/utils/utils'
  import { getNewsList,getHomeNewsList } from '@/api/docWorkRoom'
  import { openAppMixin } from "./projectMain/mixin/openApp";
  import { getTrainingPlatformUrl } from "@/api/common/projectMain.js";
  export default {
    mixins: [ openAppMixin ],
    data() {
      return {
        column_id: '',
        loading: false,
        finished: false,
        news_data: {
          page: 1,
          size: 10,
          total: 0,
          list: [],
        },
        origin:'',
        isApphome: false,
      } 
    },
    created() {
      this.origin = this.$route.query.origin;
      this.column_id = this.$route.query.column_id;
      if(this.origin === 'appHome'){
        document.title = '案例动态'
        this.isApphome = true;
        this.getAppHomeNewsList();
      }else{
        this.getList()
      }
    },
    methods: {
      handleLoad(){
        if(this.origin === 'appHome'){
          this.getAppHomeNewsList();
        }else{
          this.getList();
        }
      },
      async getList() {
        this.loading = true
        const res = await getNewsList({
          column_id: this.column_id,
          page_no: this.news_data.page,
          page_size: this.news_data.size
        })
        this.loading = false
        if (res.code === 200) {
          const { list, pager } = res.data
          this.news_data.list = this.news_data.list.concat(list)
          this.news_data.page++

          if (!pager.has_next) {
            this.finished = true
          }
        }
      },
      async getAppHomeNewsList() {
        this.loading = true
        const res = await getHomeNewsList({
          page_no: this.news_data.page,
          page_size: this.news_data.size
        })
        this.loading = false
        if (res.code === 200) {
          const { items, pager } = res.data
          items.forEach(item => {
            item.icon = item.share_custom_icon;
            item.title = item.share_custom_title;
            item.date_at = item.updated_at;
            item.url = item.link_text;
          })  

          this.news_data.list = this.news_data.list.concat(items)
          this.news_data.page++

          if (pager.current_page == pager.last_page) {
            this.finished = true
          }
        }
      },
      handleClick(item) {
        if(this.isApphome){
          this.goCaseDetail(item);
        }else if(item.url && isMobile()){
          if (getSystemType() === 'ios') {
            window.webkit.messageHandlers.breathMessage.postMessage(item.url)
          } else {
            window.android.breathMessage(item.url)
          }
        }
      },
      goCaseDetail(item) {
      // 分享页面 点击按钮 跳转下载工作室
      // if(this.isShare){
      //   this.goDownloadDocApp()
      //   return
      // }
      if(!item.is_click) return

      // if(!item.link_text){
      //   Toast('暂无配置功能链接，请联系管理员')
      //   return
      // }
      if (item.children && item.children.length) {
        if (
          item.extra_info.project_apply_mmc_type != -1 &&
          item.extra_info.project_apply_mmc_type != 0
        ) {
          let params = "";
          item.children.forEach((item) => {
            if (item.extra_info.project_apply_mmc_type_name == item.name) {
              params = item.id;
            }
          });
          this.openUrl( 2, `/docWorkRoom/projectMain/dataList?signId=${params}&titleNmae=${encodeURIComponent( item.name )}`,item);
        } else {
          this.show = true;
          this.actions = item.children;
        }
      } else if (item.column_type == 5) {
        this.openUrl( 2,`/docWorkRoom/projectMain/dataList?signId=${item.id}&titleNmae=${encodeURIComponent(item.name)}`,item);
      } else if (item.column_type == 8) {
        if(!isMobile()){
          this.isPromotePopShow = true
          return
        }
        this.getTrainingPlatformUrlFun(item)
      } else {
        this.downloadIsOpen(item);
      }
    },
    getTrainingPlatformUrlFun(item){
      getTrainingPlatformUrl(item.extra_info.project_id).then(res=>{
        if(res.code == 200){
          if(isMobile()){
            if(getSystemType() == 'ios'){
              let params = JSON.stringify({type: 1,url: res.data.link})
              window.webkit.messageHandlers.openOtherPage.postMessage(params)
            }else{
              window.android.openOtherPage(1,res.data.link)
            }
          }else{
            if(res.data.link.indexOf('weixin') != -1){
              window.openNewWindowFromSaas ? window.openNewWindowFromSaas(res.data.link) : window.open(res.data.link)
            }else{
              window.location.href = res.data.link
            }
            // window.location.href = res.data.link
          }
        }
      })
    },
    downloadIsOpen(item) {
      if (
        item.column_type == 5 ||
        (item.column_type == 6 && item.resource_type == 3)
      ) {
        this.openUrl(2,`/docWorkRoom/projectMain/dataList?signId=${item.id}&titleNmae=${encodeURIComponent(item.name)}`,item);
      } else {
        if (item.resource_type == 1) {
          this.openUrl(1, item.link_text,item);
        } else {
          if (item.link_text) {
            this.openUrl(1, item.link_text,item);
          } else {
            if (item.extra_info.project_attr_info.media[0].chunk_urls && item.extra_info.project_attr_info.media[0].chunk_urls.length) {
              this.openUrl(
                2,
                `/docWorkRoom/projectMain/previewData?format=${this.formatJudge(item.extra_info.project_attr_info.media[0].media_url)}&isPreview=1&urlPath=${encodeURIComponent(item.extra_info.project_attr_info.media[0].media_url)}&media_img=${encodeURIComponent(item.extra_info.project_attr_info.media[0].cover_image)}&titleNmae=${encodeURIComponent(item.name)}&flag=true`,
                item
              );
              localStorage.setItem("chunk_urls",JSON.stringify(item.extra_info.project_attr_info.media[0].chunk_urls));
            } else {
              this.openUrl(
                2,
                `/docWorkRoom/projectMain/previewData?format=${this.formatJudge(item.extra_info.project_attr_info.media[0].media_url)}&isPreview=1&urlPath=${encodeURIComponent(item.extra_info.project_attr_info.media[0].media_url)}&media_img=${encodeURIComponent(item.extra_info.project_attr_info.media[0].cover_image)}&titleNmae=${encodeURIComponent(item.name)}`,
                item
              );
            }
          }
        }
      }
    },
    formatJudge(url) {
      let dcode = decodeURIComponent(url);
      let parsedURL = new URL(dcode);
      let pathname = parsedURL.pathname;
      let fileExtension = pathname.split(".").pop().toLowerCase();
      return fileExtension;
    },
    openUrl(type, url, item = {}) {
      const UA = navigator.userAgent;
      const isAndroid = UA.indexOf("Android") > -1 || UA.indexOf("Adr") > -1; // android终端
      const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); // ios终端
      if (!url.startsWith('http')) {
        url = process.env.VUE_APP_BASE_URL + url.slice(1, url.length)
      }
      let param = JSON.stringify({
        type,
        url,
        shareTitle: item.share_custom_title,
        shareIcon: item.share_custom_icon,
        shareContent: ''
      });
      console.log(url);
      console.log(param);
      if(isMobile()){
        if (isAndroid) {
          try {
            window.android.openOtherPage(type, url);
          } catch (error) {
          }
          try {
            window.android.openOtherPage(type, url, item.share_custom_title, item.share_custom_icon, '')
          } catch (error) {
          }
        } else if (isIOS) {
          window.webkit.messageHandlers.openOtherPage.postMessage(param);
        }
      }else{
        if(url.slice(0,1) == '/'){
          // let tempUrl = url.slice(0,1) == '/' ? url.slice(1,url.length) : url
          url = process.env.VUE_APP_BASE_URL + url.slice(1,url.length)
        }
        if(url.indexOf('previewData') != -1 && url.indexOf('.mp4') != -1){
          window.location.href = url
          return
        }
        if(url.indexOf('weixin') != -1 || (url.indexOf('previewData') != -1 && (url.indexOf('.pdf') != -1 || url.indexOf('.jpg') != -1 || url.indexOf('.png') != -1 || url.indexOf('.webp') != -1))){
          window.openNewWindowFromSaas ? window.openNewWindowFromSaas(url) : window.open(url)
          return
        }
        if([1,6].indexOf(item.column_type) != -1 && item.resource_type == 1){
          // this.isPromotePopShow = true
          // window.parent.postMessage({type: 'showNavBar',data: true},`${process.env.VUE_APP_SAAS_PC_URL}`)
          // window.location.href = url
          window.openNewWindowFromSaas ? window.openNewWindowFromSaas(url) : window.open(url)
          return
        }
        window.location.href = url
        // if(url.indexOf('weixin') != -1 || (url.indexOf('previewData') != -1 && (url.indexOf('pdf') != -1 || url.indexOf('jpg') != -1 || url.indexOf('png') != -1 || url.indexOf('webp') != -1))){
        //   window.openNewWindowFromSaas ? window.openNewWindowFromSaas(url) : window.open(url)
        // }else{
        //   window.location.href = url
        // }
      }
      
      // if (process.env.NODE_ENV === "production") {
      //   if (isAndroid) {
      //     window.android.openOtherPage(type, url);
      //   } else if (isIOS) {
      //     window.webkit.messageHandlers.openOtherPage.postMessage(param);
      //   }
      // } else {
      //   this.$router.push(url);
      // }
    },
    },
  }
</script>

<style lang="scss" scoped>
.news {
  width: 100vw;
  min-height: 100vh;
  padding: 15px 15px 0;
  background: #F5F5F5;
  box-sizing: border-box;
  img {
    vertical-align: top;
  }
  .header {
    img {
      width: 100%;
    }
  }
  ul li {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 12px;
    padding: 8px;
    border-radius: 8px;
    background-color: #fff;
    &:first-of-type {
      margin-top: 0;
    }
    .cover {
      width: 75px;
      img {
        width: 75px;
        height: 75px;
        border-radius: 8px;
      }
    }
    .content {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      width: calc(100% - 90px);
      height: 70px;
      text-align: left;
      .title {
        display: -webkit-box;
        line-clamp: 2;
        -webkit-line-clamp: 2;
        box-orient: vertical;
        -webkit-box-orient: vertical;
        overflow: hidden;
        line-height: 1.5;
        font-size: 15px;
        color: #333;
        font-weight: bold;
      }
      .date {
        font-size: 13px;
        color: #999;
      }
      .type {
        font-size: 12px;
        color: #999;
        max-width: 150px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}
</style>