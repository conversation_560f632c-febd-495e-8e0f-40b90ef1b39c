<template>
    <div class="diagnose">
        <van-pull-refresh 
            v-model="refreshing"
            @refresh="handleRefresh"
            class="refresh"
        >
            <van-list
                v-model="loading"
                :finished="finished"
                finished-text="没有更多了"
                @load="getDiagnoseList"
                offset="300"
            >
                <div
                    v-for="(item, index) in diagnoseList"
                    :key="index"
                    class="item"
                >
                    <div class="title">{{ item.diagnoses_name }}</div>
                    <div class="wrapper">
                        <label class="label">确诊时间</label>
                        <div class="content">{{ item.created_at }}</div>
                    </div>
                    <div class="wrapper">
                        <label class="label">诊断备注</label>
                        <div class="content">{{ item.remark }}</div>
                    </div>
                    <div class="foot">
                        <div class="left">
                            <div class="label">最近一次修改时间</div>
                            <div class="content">{{ item.updated_at }}</div>
                        </div>
                        <div class="center"></div>
                        <div class="right">
                            <div class="label">修改人</div>
                            <div class="content">{{ item.doctor_name }}</div>
                        </div>
                    </div>
                </div>
            </van-list>
        </van-pull-refresh>
    </div>
</template>

<script>
import { getDiagnosisList } from '@/api/saas.js'

export default {
    data () {
        return {
            diagnoseList: [],
            page: 1,
            page_size: 10,
            refreshing: false,
            loading: false,
            finished: false,
            total: 0
        }
    },
    methods: {
        handleRefresh () {
            this.finished = false
            this.loading = false
            this.page = 1
            this.getDiagnoseList('refresh')
        },
        async getDiagnoseList (flag) {
            if (flag != 'refresh' && this.diagnoseList.length < this.total) {
                this.page += 1
            }
            let { data } = await getDiagnosisList({
                project_id: this.$route.query.project_id,
                patient_id: this.$route.query.patient_id,
                page: this.page, 
                page_size: this.page_size
            })
            this.total = data.total
            if (this.refreshing || this.page == 1) {
                this.diagnoseList = []
                this.refreshing = false
            }

            for (let i = 0; i < data.data.length; i++) {
                this.diagnoseList.push(data.data[i])
            }
            this.loading = false 
            if (this.diagnoseList.length >= this.total) {
                this.finished = true
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.diagnose {
    .refresh {
        min-height: 100vh;
        background: #F2F2F2;
        padding: 15px;
        text-align: left;
        font-size: 14px;
    }
    .item {
        background-color: white;
        border-radius: 6px;
        padding: 8px;
        margin-bottom: 12px;
        .title {
            color: #0A0A0A;
            font-size: 17px;
            padding: 9px 7px;
        }
       
        .wrapper {
            display: flex;
            font-size: 15px;
            padding: 9px 7px;
            line-height: 20px;
            .label {
                color: #878F99;
                width: 76px;
            }
            .content {
                color: #5A6266;
                flex: 1;
            }
        }
        .foot {
            background-color: rgba(235, 243, 255, 0.5);
            border-radius: 4px;
            padding: 6px 12px;
            font-size: 12px;
            display: flex;
            .label {
                color: #878F99;
                padding: 5px 0;
            }
            .content {
                color: #5A6266;
                padding: 5px 0;
            }
            .center {
                border: 1px solid #EBF3FF;
                margin: 0 12px;
                transform: scaleX(0.5);
            }
            .left, .right {
                width: 50%;
            }
        }
    }
}
</style>