<template>
  <div class='commonIframe'>
    <iframe :src="iframeUrl" width="100%" height="100%" frameborder="0"></iframe>
    <template v-if="isShare">
      <wx-open-launch-app
        id="top_btn"
        appid="wxd4d251ee3ba85b70"
        :extinfo="appExtInfo"
        style="position: fixed;left: 0;top: 0;width: 100%;background-color: transparent;z-index: 9999999999;"
      >
        <script type="text/wxtag-template">
          <style>
            .topOpenAppOuter {
              width: 100%;
              height: 50px;
              line-height: 50px;
              background-color: rgba(255,255,255,0.6);
              position: relative;
            }
            .logo {
              width: 35px;
              height: 35px;
              position: absolute;
              left: 16px;
              top: 8px;
            }
            .title {
              font-size: 16px;
              color: #181818;
              height: 50px;
              line-height: 50px;
              position: absolute;
              left: 56px;
            }
            .topOpenAppBtn {
              width: 80px;
              border-radius: 50px;
              text-align: center;
              height: 30px;
              line-height: 30px;
              background: #F7830D;
              font-size: 14px;
              color: white;
              position: absolute;
              right: 16px;
              top: 10px;
            }
          </style>
          <div class="topOpenAppOuter">
            <img class="logo" src="https://ares.zz-med.com/doctorapp/docAppLogo.png" alt="">
            <span class="title">医生工作室</span>
            <div class="topOpenAppBtn">打开APP</div>
          </div>
        </script>
      </wx-open-launch-app>
      <wx-open-launch-app
        id="bottom_btn"
        appid="wxd4d251ee3ba85b70"
        :extinfo="appExtInfo"
        style="position: fixed;left: 0;bottom: 120px;width: 100%;background-color: transparent;z-index: 9999999999;"
      >
        <script type="text/wxtag-template">
          <style>
            .bottomOpenAppBtn {
              width: 260px;
              border-radius: 50px;
              text-align: center;
              height: 50px;
              line-height: 50px;
              margin-left: 50%;
              position: relative;
              left: -130px;
              background: #F7830D;
              font-size: 18px;
              color: white;
              box-shadow: 0 4px 8px #FDD2AB;
            }
          </style>
          <div class="bottomOpenAppBtn">医生工作室APP内打开</div>
        </script>
      </wx-open-launch-app>
    </template>
  </div>
</template>

<script>
import { openAppMixin } from "./mixin/openApp";
export default {
  mixins: [ openAppMixin ],
  data() {
    return {
      iframeUrl: ''
    }
  },
  methods: {
    
  },
  created() {
    let url = this.$route.query.url
    this.iframeUrl = atob(url)
  },
  mounted() {
    
  }
}
</script>

<style lang='scss'>
.commonIframe{
  width: 100vw;
  height: 100vh;
}
</style>