<template>
  <div class="commonList">
    <div
      class="list"
      v-for="(i, index) in docList"
      :key="index"
      @click="stairCatalog(i)"
    >
      <div class="listTitle">{{ i.name }}</div>
      <div
        class="line"
        v-for="(item, chilDrenIndex) in i.children"
        :key="chilDrenIndex"
        @click="goToNext(item)"
      >
        <div class="leftText">
          <img
            v-if="!item.children && !item.children.length"
            style="width: 20px; height: 20px"
            src="../../imgs/single1.png"
            alt=""
          />
          <img
            v-else
            src="../../imgs/single.png"
            style="width: 20px; height: 20px"
            alt=""
          />
          <div class="name">{{ item.name }}</div>
        </div>
        <van-icon
          v-if="item.children && item.children.length"
          color="#B3B3B3"
          size="18"
          class="rightIcon"
          name="arrow"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { isMobile } from '@/utils/utils'
export default {
  props: {
    docList: {
      type: Object,
      default: () => {},
    },
    canLoad: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      loading: false,
    };
  },
  methods: {
    openUrl(type, url, close,item) {
      const UA = navigator.userAgent;
      const isAndroid = UA.indexOf("Android") > -1 || UA.indexOf("Adr") > -1; // android终端
      const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); // ios终端
      let param = JSON.stringify({
        type,
        url,
        close,
      });
      if(isMobile()){
        if (process.env.NODE_ENV === "production") { 
          if (isAndroid) {
            window.android.openOtherPage(type, url, close);
          } else if (isIOS) {
            window.webkit.messageHandlers.openOtherPage.postMessage(param);
          }
        } else {
          this.$router.push(url)
        }
      }else{
        if(item.content_type && ['pdf','png','jpg','webp'].indexOf(item.content_type) != -1){
          window.openNewWindowFromSaas ? window.openNewWindowFromSaas(url) : window.open(url)
          return
        }
        // console.log(this.$router)
        window.location.href = url
      }
    },
    goToNext(item) {
      if (item.children && item.children.length) {
        this.openUrl(
          2,
          `/docWorkRoom/projectMain/levelDatalist/${item.tierid}?itemId=${item.id}`,
          2,
          item
        );
        // this.$router.push({
        //   path: `/docWorkRoom/projectMain/levelDatalist/${item.tierid}?itemId=${item.id}`,
        // })
      } else {
        if (item.chunk_path && item.chunk_path.length) {
          this.openUrl(
            2,
            `/docWorkRoom/projectMain/previewData?format=${
              item.content_type
            }&urlPath=${encodeURIComponent(item.path)}&flag=true&isPreview=${this.canLoad?0:1}&media_img=${encodeURIComponent(item.video_cover_img)}`,
            2,
            item
          );
          localStorage.setItem('chunk_urls', JSON.stringify(item.chunk_path))
        } else {
          this.openUrl(
            2,
            `/docWorkRoom/projectMain/previewData?format=${
              item.content_type
            }&urlPath=${encodeURIComponent(item.path)}&isPreview=${this.canLoad?0:1}&media_img=${encodeURIComponent(item.video_cover_img)}`,
            2,
            item
          );
        }
        //  this.$router.push({
        //   path: '/docWorkRoom/projectMain/previewData',
        //    query: {
        //     format: item.content_type,
        //     urlPath: item.path,
        //   }
        // })

        // if (isAndroid) {
        //   window.android.showFilePdf(encodeURIComponent(item.path));
        // } else if (isIOS) {
        //   window.webkit.messageHandlers.showFilePdf.postMessage(
        //     encodeURIComponent(item.path)
        //   );
        // }
      }
    },
    stairCatalog(item) {
      console.log(item);
      if (!item.is_child) {
        if (item.chunk_path && item.chunk_path.length) {
          this.openUrl(
            2,
            `/docWorkRoom/projectMain/previewData?format=${
              item.content_type
            }&urlPath=${encodeURIComponent(item.path)}&flag=true&isPreview=${this.canLoad?0:1}&media_img=${encodeURIComponent(item.video_cover_img)}`,
            2,
            item
          );
          localStorage.setItem('chunk_urls', JSON.stringify(item.chunk_path))
        } else {
          this.openUrl(
            2,
            `/docWorkRoom/projectMain/previewData?format=${
              item.content_type
            }&urlPath=${encodeURIComponent(item.path)}&isPreview=${this.canLoad?0:1}&media_img=${encodeURIComponent(item.video_cover_img)}`,
            2,
            item
          );
        }
        // this.$router.push({
        //   path: '/docWorkRoom/projectMain/previewData',
        //   query: {
        //     format: item.content_type,
        //     urlPath: item.path,
        //   }
        // })
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.list {
  border-radius: 8px;
  width: 100%;
  padding: 0 15px;
  box-sizing: border-box;
  background: white;
  font-size: 17px;
  margin-bottom: 16px;
  .listTitle {
    height: 56px;
    line-height: 56px;
    text-align: left;
    color: #878f99;
  }
  .line {
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 40px;
    padding-bottom: 16px;
    cursor: pointer;
    .leftText {
      display: flex;
      align-items: center;

      .name {
        margin-left: 12px;
        color: #0a0a0a;
        text-align: left;
        line-height: 28px;
        word-wrap: break-word;
        overflow-wrap: break-word;
        // width: 258px;
        word-break: break-all;
      }
      .van-icon-orders-o {
      }
    }
  }
}
</style>
