<template>
  <div>
    <van-popup @close="$emit('closeShow', false)" round v-model="show" position="bottom" :style="{ height: '44%' }" >
      <div class="title">
          <span>{{ title }}</span>
      </div>
      <div class="content">
          <span class="next-title">{{ nextTitle }}</span>
          <span class="describe">{{ describe }}</span>
          <van-field readonly :class="verify?'url-Noinput':'url-input'" id="url" v-model="downloadUrl.netdisk_url" />
          <div class="baidu" v-if="downloadType == 'disc'" >
            <span>提取码</span>
            <van-field readonly class="code-input"  id="code"  v-model="downloadUrl.netdisk_secret" />
          </div>
      </div>
      <div></div>
      <div class="copyFoot"  data-clipboard-target="#code" @click="copyCode">
        {{ btnText }}
      </div>
    </van-popup>
  </div>
</template>

<script>
import Clipboard from 'clipboard'
import { Toast } from 'vant'
export default {
  props: {
    title: {
      type: String,
      default:'百度网盘'
    },
    nextTitle: {
      type: String,
      default: '百度网盘下载：'
    },
    describe: {
      type: String,
      default: '复制以下链接，即可查看或下载文件'
    },
    btnText: {
      type: String,
      default: '复制链接及提取码'
    },
    isEmail: {
      type:Boolean ,
      default: false
    },
    show: {
      type: Boolean,
      default:false
    },
    downloadType: {
      type: String,
      default:''
    },
    downloadUrl: {
      type: Object,
      default:()=>{}
    }
  },
  data() {
    return {
      verify:false
    }
  },
  watch: {
    show() {
      this.verify = false
    }
  },
  methods: {
    verifyEmail(account) {
      console.log(account)
      const regex = /^[^\s@]+@[^\s@]+\.[a-zA-Z]{2,}$/
      if (regex.test(account)) {
        this.verify = false
      } else {
        this.verify = true
        Toast('邮箱格式错误');
      }
    },
    copyCode() {
      let name = this.downloadUrl.netdisk_url + '\n提取码:' + this.downloadUrl.netdisk_secret
      if (this.downloadType == 'disc') {
        let clipboard = new Clipboard('.copyFoot', {
          text: function (trigger) {
            return name;
          }
        })
        clipboard.on('success', function (e) {
          if (e) {
            Toast('复制成功');
            e.clearSelection()
          } else {
            Toast('复制失败,请重试');
          }
        })
      } else {
        this.verifyEmail(this.url)
      }
    }
  }
}
</script>
<style lang="scss" scoped>

.title{
  width: 100%;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #5A6266;
  font-size: 17px;
  font-weight: 500;
  border-bottom: 1px solid #F1F1F1;
}
.content{
  padding: 16px 24px;
  text-align: left;
  ::v-deep .van-cell::after{
    border: none;
  }
  .next-title{
    color: #000;
    font-size: 17px;
    font-weight: 500;
    display: block;
    line-height: 24px;
  }
  .describe{
    color: #5A6266;
    font-size: 15px;
    margin-top: 12px;
    line-height: 22px;
    display: inline-block;
    margin-bottom: 15px;
  }
  .url-input{
    border-radius: 8px;
    border: 1px solid #DDD;
    height: 42px;
  }

  .url-Noinput{
    border-radius: 8px;
    height: 42px;
    border: 1px solid #F53F3F;
    background-color: #FFF1F1;
  }
  .baidu{
    display: flex;
    margin-top: 15px;
    align-items: center;
    .code-input{
      border-radius: 8px;
      border: 1px solid #DDD;
      width: 101px;
      height: 42px;
    }
    span{
      color: #5A6266;
      font-size: 15px;
      font-weight: 400;
      white-space: nowrap;
      display: inline-block;
      margin-right: 8px;
    }
  }
}

.copyFoot{
  width: 315px;
  height: 52px;
  border: 1px solid #FC6A1E;
  margin: 16px auto 0px auto;
  border-radius: 10px;
  color: #FC6A1E;
  font-size: 17px;
  font-weight: 500;
  line-height: 52px;
  position: fixed;
  bottom: 25px;
  left: 50%;
  transform: translateX(-50%);
}
</style>