<template>
  <div class='navBar'>
    <div class="main">
      <van-icon @click="goBackForPc" class="icon" name="arrow-left" size="26" :color="color" />
    </div>
  </div>
</template>

<script>
import  {getUrlParams } from '@/utils/utils'
export default {
  data() {
    return {
      
    }
  },
  props: {
    color: {
      type: String,
      default: 'white'
    }
  },
  methods: {
    goBackForPc(){
      let url = window.location.href
      // alert(url)
      if(url.indexOf('projectMain/newProjectMain') > -1){
        let authorization = getUrlParams(url,'authorization')
        if(authorization){
          // alert(authorization)
          let temp = `${process.env.VUE_APP_BASE_URL}docWorkRoom/appHome?authorization=${authorization}`
          // alert(temp)
          window.location.href = temp
        }
        return
      }
      window.history.back()
    }
  },
  created() {
    
  },
  mounted() {
    
  }
}
</script>

<style lang='scss'>
.navBar{
  .main{
    height: 44px;
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 999;
    text-align: left;
    display: flex;
    align-items: center;
    padding-left: 6px;
  }
  .icon{
    font-weight: bold;
    cursor: pointer;
  }
}
</style>