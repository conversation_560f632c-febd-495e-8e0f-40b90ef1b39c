<template>
  <div class='promoteDownloadPop'>
    <van-dialog v-model="show" title="" @close="closeFun" close-on-click-overlay :lock-scroll="false" :overlay="false">
      <div class="content">
        <img class="qrcode" src="https://ares.zz-med.com/doctorapp/docAppDownloadQrCode.png" />
        <div class="text">请扫码下载医生工作室App，体验完整功能</div>
      </div>
    </van-dialog>
    <div class="overlayClass" @click="closeFun"></div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      show: true
    }
  },
  methods: {
    closeFun(){
      this.$parent.isPromotePopShow = false
    }
  },
  created() {
    
  },
  mounted() {
    
  }
}
</script>

<style lang='scss'>
.promoteDownloadPop{
  .content{
    padding: 30px 0 16px 0;
  }
  .qrcode{
    width: 120px;
    height: 120px;
  }
  .text{
    font-size: 14px;
    font-weight: 500;
    line-height: normal;
    color: #333333;
  }
  .overlayClass{
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.2);
    z-index: 0;
  }
}
</style>