<template>
  <div
    class="projectMain"
    :style="`background-color: ${info.project_color || ''}`"
  >
    <van-loading v-if="loading" style="padding-top: 200px" />
    <div v-if="!loading">
      <div class="logoOuter">
        <img class="logo" :src="info.project_logo" alt="" />
      </div>
      <div class="projectName">{{ info.project_name }}</div>
      <div
        class="swipeOuter"
        v-if="info.official_video_list && info.official_video_list.length"
      >
        <van-swipe class="swipe" v-model="activeIndex" @change="onChange">
          <van-swipe-item
            class="swipeItem"
            v-for="(item, index) in info.official_video_list"
            :key="index"
          >
            <!-- <video autoplay @onloadeddata='0' ref="videoRef" controls class="videoItem" :src="item" poster=""></video> -->
            <video
              controlslist="nodownload noplaybackrate  noremoteplayback"
              :disablePictureInPicture="true"
              ref="video"
              @play="toggleVideo"
              controls
              class="videoItem"
              :src="item.media_url"
              :poster="item.cover_image"
            ></video>
          </van-swipe-item>
        </van-swipe>
      </div>
      <div class="operateOuter">
        <div
          class="operateItem"
          v-for="(item, index) in info.project_attr_info"
          :key="index"
          @click="toDetail(item)"
        >
          <div
            class="bgDiv"
            v-if="
              item.column_type == 1 ||
              item.column_type == 3 ||
              item.column_type == 5
            "
            :style="{
              background: `url(${officialWebBg})`,
              'background-size': '100%',
            }"
          >
            <img class="bgDivImg" :src="item.icon" alt="" />
          </div>
          <div
            v-else
            class="specialBgDiv"
            :style="{
              background: `url(${customBtnBg})`,
              'background-size': '100%',
            }"
          ></div>
          <div
            class="text"
            :class="[
              item.column_type == 1 ||
              item.column_type == 3 ||
              item.column_type == 5
                ? 'fixedItem'
                : 'customItem',
            ]"
          >
            {{ item.name }}
          </div>
        </div>
      </div>
      <div
        class="bottomBtnOuter"
        @click="anewBtn"
        v-if="
          this.info.project_apply_link_data &&
          JSON.stringify(this.info.project_apply_link_data) != '{}'
        "
      >
        <div
          class="btn"
          :style="{
            color: 'white',
            background: info.project_color || '',
            'box-shadow':
              '0px 4px 14.7px 0px rgba(255, 255, 255, 0.30) inset, 0px -4px 6px 0px rgba(255, 255, 255, 0.28) inset',
          }"
          v-if="
            info.project_apply_status == 1 || info.project_apply_status == 0
          "
        >
          {{ btnName(info.project_apply_status) }}
        </div>
        <!-- <div class="btn" :style="{'color': 'white','background': 'rgba(0,0,0,0.2)'}">审核中...</div> -->
        <div class="btn fail" v-if="info.project_apply_status == 3">
          {{ btnName(info.project_apply_status) }}
        </div>
        <div
          class="btnText"
          :style="{ color: info.project_color || '' }"
          v-if="info.project_apply_status == 2"
        >
          已成功报名项目
        </div>
      </div>
      <van-action-sheet
        style="padding-bottom: 30px"
        v-model="show"
        @select="select"
        :actions="actions"
        cancel-text="取消"
        description="请选择分级"
        close-on-click-action
      />
    </div>
  </div>
</template>

<script>
import { getProjectDetail } from "@/api/common/projectMain.js";
import { Toast } from "vant";
export default {
  data() {
    return {
      show: false,
      actions: [],
      info: {},
      officialWebBg: require("../imgs/banner1.png"),
      projectIntroductionBg: require("../imgs/projectIntroductionBg.png"),
      dataDownloadBg: require("../imgs/dataDownloadBg.png"),
      customBtnBg: require("../imgs/customBtnBg.png"),
      loading: false,
      isVideoPlaying: false,
      activeIndex: 0,
    };
  },

  methods: {
    onChange(index) {
      if (this.isVideoPlaying) {
        this.pauseCurrentVideo();
      }
      this.activeIndex = index;
    },
    toggleVideo() {
      const video = this.$refs.video[this.activeIndex];
      if (this.isVideoPlaying) {
        video.pause();
      } else {
        video.play();
      }
      this.isVideoPlaying = !this.isVideoPlaying;
    },
    pauseCurrentVideo() {
      const video = this.$refs.video[this.activeIndex];
      video.pause();
      this.isVideoPlaying = false;
    },
    select(item) {
      this.downloadIsOpen(item);
    },
    openUrl(type, url) {
      const UA = navigator.userAgent;
      const isAndroid = UA.indexOf("Android") > -1 || UA.indexOf("Adr") > -1; // android终端
      const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); // ios终端
      let param = JSON.stringify({
        type,
        url,
      });
      console.log(url);
      console.log(param);
      if (process.env.NODE_ENV === "production") { 
        if (isAndroid) {
          window.android.openOtherPage(type, url);
        } else if (isIOS) {
          window.webkit.messageHandlers.openOtherPage.postMessage(param);
        }
      } else {
        this.$router.push(url)
      }
      
    },
    formatJudge(url) {
      let dcode = decodeURIComponent(url);
      let parsedURL = new URL(dcode);
      let pathname = parsedURL.pathname;
      let fileExtension = pathname.split(".").pop().toLowerCase();
      return fileExtension;
    },
    backGo(type, url) {
      const UA = navigator.userAgent;
      const isAndroid = UA.indexOf("Android") > -1 || UA.indexOf("Adr") > -1; // android终端
      const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); // ios终端
      let param = JSON.stringify({
        type,
        url,
      });
      if (isAndroid) {
        console.log("安卓");
        window.android.backGohomePage(type, url);
      } else if (isIOS) {
        window.webkit.messageHandlers.backGohomePage.postMessage(param);
      }
    },
    downloadIsOpen(item) {
      if (
        item.column_type == 5 ||
        (item.column_type == 6 && item.resource_type == 3)
      ) {
        this.openUrl(
          2,
          `/docWorkRoom/projectMain/dataList?signId=${
            item.id
          }&titleNmae=${encodeURIComponent(item.name)}`
        );
      } else {
        if (item.resource_type == 1) {
          this.openUrl(1, item.link_text);
        } else {
          if (item.link_text) {
            this.openUrl(1, item.link_text);
          } else {
            if (item.media[0].chunk_urls && item.media[0].chunk_urls.length) {
              this.openUrl(
                2,
                `/docWorkRoom/projectMain/previewData?format=${this.formatJudge(
                  item.media[0].media_url
                )}&isPreview=1&urlPath=${encodeURIComponent(
                  item.media[0].media_url
                )}&media_img=${encodeURIComponent(
                  item.media[0].cover_image
                )}&titleNmae=${encodeURIComponent(item.name)}&flag=true`
              );
              localStorage.setItem('chunk_urls', JSON.stringify(item.media[0].chunk_urls))
            } else {
              this.openUrl(
                2,
                `/docWorkRoom/projectMain/previewData?format=${this.formatJudge(
                  item.media[0].media_url
                )}&isPreview=1&urlPath=${encodeURIComponent(
                  item.media[0].media_url
                )}&media_img=${encodeURIComponent(
                  item.media[0].cover_image
                )}&titleNmae=${encodeURIComponent(item.name)}`
              );
            }
            
            //  this.$router.push({
            //    path: '/docWorkRoom/projectMain/previewData',
            //    query: {
            //     format: this.formatJudge(item.media[0].media_url),
            //     isPreview: 1,
            //     urlPath: item.media[0].media_url,
            //     media_img: encodeURIComponent(item.media[0].cover_image)
            //    }
            // })
          }
        }
      }
    },
    btnName(key) {
      let name = "";
      switch (key) {
        case 0:
          name = "立即报名";
          break;
        case 1:
          name = "审核中...";
          break;
        case 2:
          name = "已成功报名项目";
          break;
        case 3:
          name = "审核未通过，重新申请";
          break;
        default:
          name = "立即报名";
          break;
      }
      return name;
    },
    getUrlFn(k) {
      let objId = k.children.find((item) => {
        return this.info.project_apply_mmc_type_name == item.name;
      });
      return objId.id || "";
    },
    toDetail(item) {
      console.log(11)
      if (item.children && item.children.length) {
        if (
          this.info.project_apply_mmc_type != -1 &&
          this.info.project_apply_mmc_type != 0
        ) {
          let params = "";
          item.children.forEach((item) => {
            if (this.info.project_apply_mmc_type_name == item.name) {
              params = item.id;
            }
          });
          this.openUrl(
            2,
            `/docWorkRoom/projectMain/dataList?signId=${params}&titleNmae=${encodeURIComponent(
              item.name
            )}`
          );
        } else {
          this.show = true;
          this.actions = item.children;
        }
      } else if (item.column_type == 5) {
        this.openUrl(
          2,
          `/docWorkRoom/projectMain/dataList?signId=${
            item.id
          }&titleNmae=${encodeURIComponent(item.name)}`
        );
      } else {
        this.downloadIsOpen(item);
      }
    },
    pauseVideo(index) {
      console.log(index);
      // this.$refs.videoRef[index].currentTime = 0;
      // this.$refs.videoRef[index].pause();
    },
    getDetail() {
      this.loading = true;
      const params = this.$route.query.project_id;
      getProjectDetail(params)
        .then((res) => {
          if (res.code == 200) {
            this.loading = false;
            this.info = res.data;
          } else {
            this.loading = false;
            Toast(res.msg);
          }
        })
        .catch((err) => {
          this.loading = false;
          Toast(err);
        });
    },

    anewBtn() {
      if (
        this.info.project_apply_link_data.children &&
        this.info.project_apply_link_data.children.length &&
        this.info.project_apply_status == 0
      ) {
        this.show = true;
        this.actions = this.info.project_apply_link_data.children;
      } else {
        if (this.info.project_apply_status == 3) {
          this.openUrl(
            2,
            `/docWorkRoom/projectMain/registerStatus?project_id=${
              this.info.project_id
            }&gotoApply=${encodeURIComponent(
              this.info.project_apply_link_data.link_text
            )}`
          );
          //  this.$router.push({
          //    path: '/docWorkRoom/projectMain/registerStatus',
          //    query: {
          //     project_id: this.info.project_id,
          //     gotoApply: this.info.project_apply_link_data.link_text
          //    }
          // })
        } else if (this.info.project_apply_status == 0) {
          this.openUrl(1, `${this.info.project_apply_link_data.link_text}`);
          // let url = this.info.project_apply_link_data.link_text
          // window.location.href = url
        }
      }
    },
  },
  created() {
    this.getDetail();
  },
  mounted() {
    // this.handleVideo()
  },
};
</script>

<style scoped lang="scss">
.projectMain {
  width: 100%;
  min-height: 100vh;
  padding: 48px 15px 116px;
  box-sizing: border-box;
  .logoOuter {
    background: url("../imgs/logoBgLight.png");
    background-size: 100%;
    width: 120px;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: auto;
    .logo {
      width: 70px;
      height: 70px;
      border-radius: 50%;
    }
  }
  .projectName {
    font-size: 18px;
    font-weight: 500;
    color: white;
  }
  .swipeOuter {
    width: 100%;
    height: 194px;
    margin-top: 32px;
    .swipe,
    .swipeItem {
      width: 99%;
      height: 194px;
      border-radius: 8px;
    }
    .videoItem {
      width: 100%;
      height: 195px;
      border-radius: 8px;
      // background: white;
    }
  }
  .operateOuter {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    .operateItem {
      width: 165px;
      height: 80px;
      margin-top: 16px;
      position: relative;
      .specialBgDiv {
        width: 100%;
        height: 100%;
        background-size: 100%;
      }
      .bgDiv {
        width: 100%;
        height: 100%;
        background-size: 100%;
        display: flex;
        align-items: center;
        .bgDivImg {
          width: 46px;
          height: 46px;
          border-radius: 50%;
          margin-left: 16px;
        }
      }
      .text {
        color: #6b6b6b;
        font-size: 14px;
        font-weight: 500;
        width: 70px;
        text-align: left;
        line-height: 20px;
      }
      .customItem {
        position: absolute;
        top: 50%;
        transform: translate(0, -50%);
        right: 16px;
      }
      .fixedItem {
        position: absolute;
        top: 36px;
        right: 16px;
      }
    }
  }
  .bottomBtnOuter {
    width: 100%;
    height: 84px;
    position: fixed;
    bottom: 0;
    left: 0;
    background: rgba(255, 255, 255, 0.9);
    .btn {
      width: 315px;
      height: 52px;
      margin: 16px auto 0;
      border-radius: 8px;
      // background: #FC671A;
      font-size: 20px;
      font-weight: 600;
      // color: white;
      text-align: center;
      line-height: 52px;
    }
    .btnText {
      font-size: 20px;
      font-weight: 600;
      margin-top: 16px;
    }
    .fail {
      color: white;
      background: #fc1a1a;
    }
  }
}

// video::-webkit-media-controls-enclosure {
//     display:none !important;
// }
</style>
