<template>
  <div class="projectMain">
    <div ref="topColorBlock" class="topColorBlock" :style="`background-color: ${info.project_color || ''}`"></div>
    <!-- saas使用 -->
    <NavBar v-if="!isMobile()"></NavBar>
    <van-loading v-if="loading && isMobile()" style="padding-top: 200px" />
    <img v-if="!isMobile() && loading" class="peojectIframe" src="../imgs/peojectIframe.png" alt="">
    <div v-if="!loading">
      <div class="topLogoOuter" :style="`background-color: ${info.project_color || ''}`">
        <div class="logoOuter">
          <img class="logo" :src="info.project_logo" alt="" />
        </div>
        <div class="projectName">{{ info.project_name }}</div>
        <div class="toolbarOuter" :style="{ 'justify-content': info.project_attr_info.group_item.length > 5 ? '' : 'center' }" v-if="info.project_attr_info.group_item.length > 0">
          <div class="toolItem" :class="[info.project_attr_info.group_item.length > 5 ? 'avg' : 'flex1' ]" v-for="item in info.project_attr_info.group_item" :key="item.id" @click="toDetail(item)">
            <img class="toolIcon" :src="item.icon" alt="" />
            <span class="toolName">{{ item.name }}</span>
          </div>
        </div>
      </div>
      <div class="innerOuter" :style="info.project_attr_info.group_item.length == 0 ? 'margin-top: -20px' : ''">
        <div class="swipeOuter" v-if="info.official_video_list && info.official_video_list.length">
          <van-swipe class="swipe" v-model="activeIndex" @change="onChange">
            <van-swipe-item class="swipeItem" v-for="(item, index) in info.official_video_list" :key="index">
              <video
                controlslist="nodownload noplaybackrate  noremoteplayback"
                :disablePictureInPicture="true"
                webkit-playsinline='true'
                playsinline='true'
                ref="video"
                @play="playVideo($event,item)"
                @pause="pauseVideo"
                controls
                class="videoItem"
                :src="item.media_url"
                :poster="item.cover_image"></video>
                <div class="videoCoverImg" v-if="isvideoCoverImgShow" @click="playVideo($event,item)"></div>
            </van-swipe-item>
          </van-swipe>
        </div>
        <div class="fastImgsOuter">
          <div v-for="item in info.project_attr_info.quick_item" :key="item.id">
            <div v-if="item.column_type === 9">
              <div class="news" v-if="show_news">
                <div class="bar">
                  <img :src="item.icon" alt="icon" />
                </div>
                <ul>
                  <li v-for="(news, j) in item.children" :key="j" @click="getMoreNews(news)">
                    <div class="cover">
                      <img :src="news.icon" alt="cover" />
                    </div>
                    <div class="content">
                      <div class="title">{{ news.title }}</div>
                      <div class="date">{{ news.date_at ? news.date_at.split(' ')[0] : '' }}</div>
                    </div>
                  </li>
                </ul>
              </div>
            </div>
            <div v-else-if="item.column_type === 10">
              <img v-if="show_news" class="fastImg" :src="item.icon" :alt="item.name" @click="getMoreNews(item)" />
            </div>
            <img v-else class="fastImg" :src="item.icon" :alt="item.name" @click="toDetail(item)" />
          </div>
          <!-- <img class="fastImg" v-for="item in info.project_attr_info.quick_item" :key="item.id" :src="item.icon" alt="" @click="toDetail(item)" /> -->
        </div>
      </div>

      <template v-if="info.prject_studio_button_status">
        <div class="bottomBtnOuter btn-join-clinic">
          <div
            class="btn"
            @click="onJoinOnlineClinic(info.prject_studio_button_info)"
            :style="{ background: info.project_color || ''}"
          >{{ info.prject_studio_button_info.btn }}</div>
        </div>
      </template>
      <template v-else>
        <!-- 普通项目 -->
        <template v-if="info.prject_only_status == 0">
          <!-- 按钮开关 打开 -->
          <template v-if="info.project_apply_is_enable">
            <div class="bottomBtnOuter">
              <div v-if="info.concat_cell && info.concat_cell.concat_cell" class="leftContact" @click="callFun">
                <img class="kefuIcon" src="../imgs/kefuIcon.png" alt="">
                <span>客服</span>
              </div>
              <div class="btn"
              @click="anewBtn"
              :style="{ color: 'white',background: info.project_color || '', 'box-shadow': '0px 4px 14.7px 0px rgba(255, 255, 255, 0.30) inset, 0px -4px 6px 0px rgba(255, 255, 255, 0.28) inset',}"
              v-if="info.project_apply_status == 1 || (info.project_apply_status == 0 && info.project_apply_link_data && JSON.stringify(info.project_apply_link_data) != '{}')">
                {{ btnName(info.project_apply_status) }}
              </div>
              <div class="btn fail" v-if="info.project_apply_status == 3" @click="anewBtn">
                {{ btnName(info.project_apply_status) }}
              </div>
              <div class="btnText" :style="{ color: info.project_color || '' }" v-if="info.project_apply_status == 2">
                已成功报名项目
              </div>
            </div>
          </template>
          <!-- 按钮开关 关闭 -->
          <template v-else>
            <div class="bottomBtnOuter" v-if="info.concat_cell && info.concat_cell.concat_cell">
              <div class="btn"
                @click="callFun"
                :style="{ color: 'white',background: info.project_color || '', 'box-shadow': '0px 4px 14.7px 0px rgba(255, 255, 255, 0.30) inset, 0px -4px 6px 0px rgba(255, 255, 255, 0.28) inset',}" >
                客服
              </div>
            </div>
          </template>
        </template>
        <!-- 网上社区、服务包等特殊项目 -->
        <template v-else>
          <div class="bottomBtnOuter" v-if="info.project_only_info && info.project_only_info.btn">
            <div v-if="info.concat_cell && info.concat_cell.concat_cell" class="leftContact" @click="callFun">
              <img class="kefuIcon" src="../imgs/kefuIcon.png" alt="">
              <span>客服</span>
            </div>
            <div class="btn"
            @click="specialBottomBtnFun"
            :style="{ color: 'white',background: info.project_color || '','box-shadow': '0px 4px 14.7px 0px rgba(255, 255, 255, 0.30) inset, 0px -4px 6px 0px rgba(255, 255, 255, 0.28) inset',}" >
              {{ info.project_only_info.btn }}
            </div>
          </div>
          <template v-else>
            <div class="bottomBtnOuter" v-if="info.concat_cell && info.concat_cell.concat_cell">
              <div class="btn"
                @click="callFun"
                :style="{ color: 'white',background: info.project_color || '', 'box-shadow': '0px 4px 14.7px 0px rgba(255, 255, 255, 0.30) inset, 0px -4px 6px 0px rgba(255, 255, 255, 0.28) inset',}" >
                客服
              </div>
            </div>
          </template>
        </template>
      </template>
      <van-action-sheet style="padding-bottom: 30px" v-model="show" @select="select" :actions="actions" cancel-text="取消"
        description="请选择分级" close-on-click-action />
    </div>
    <PromoteDownloadPop v-if="isPromotePopShow"></PromoteDownloadPop>
    <template v-if="isShare">
      <wx-open-launch-app
        id="top_btn"
        appid="wxd4d251ee3ba85b70"
        :extinfo="appExtInfo"
        style="position: fixed;left: 0;top: 0;width: 100%;background-color: transparent;z-index: 9999999999;"
      >
        <script type="text/wxtag-template">
          <style>
            .topOpenAppOuter {
              width: 100%;
              height: 50px;
              line-height: 50px;
              background-color: rgba(255,255,255,0.6);
              position: relative;
            }
            .logo {
              width: 35px;
              height: 35px;
              position: absolute;
              left: 16px;
              top: 8px;
            }
            .title {
              font-size: 16px;
              color: #181818;
              height: 50px;
              line-height: 50px;
              position: absolute;
              left: 56px;
            }
            .topOpenAppBtn {
              width: 80px;
              border-radius: 50px;
              text-align: center;
              height: 30px;
              line-height: 30px;
              background: #F7830D;
              font-size: 14px;
              color: white;
              position: absolute;
              right: 16px;
              top: 10px;
            }
          </style>
          <div class="topOpenAppOuter">
            <img class="logo" src="https://ares.zz-med.com/doctorapp/docAppLogo.png" alt="">
            <span class="title">医生工作室</span>
            <div class="topOpenAppBtn">打开APP</div>
          </div>
        </script>
      </wx-open-launch-app>
      <wx-open-launch-app
        id="bottom_btn"
        appid="wxd4d251ee3ba85b70"
        :extinfo="appExtInfo"
        style="position: fixed;left: 0;bottom: 120px;width: 100%;background-color: transparent;z-index: 9999999999;"
      >
        <script type="text/wxtag-template">
          <style>
            .bottomOpenAppBtn {
              width: 260px;
              border-radius: 50px;
              text-align: center;
              height: 50px;
              line-height: 50px;
              margin-left: 50%;
              position: relative;
              left: -130px;
              background: #F7830D;
              font-size: 18px;
              color: white;
              box-shadow: 0 4px 8px #FDD2AB;
            }
          </style>
          <div class="bottomOpenAppBtn">医生工作室APP内打开</div>
        </script>
      </wx-open-launch-app>
    </template>
  </div>
</template>
<script>
import { newGetProDetail,getTrainingPlatformUrl,getDefaultProDetail } from "@/api/common/projectMain.js";
import { getWxConfig } from "@/api/docWorkRoom.js";
import { Toast } from "vant";
import { getSystemType,isMobile } from '@/utils/utils'
import NavBar from './components/navBar.vue'
import PromoteDownloadPop from './components/promoteDownloadPop.vue';
import wx from "weixin-js-sdk"
import { openAppMixin } from "./mixin/openApp";
export default {
  components: { NavBar,PromoteDownloadPop },
  mixins: [ openAppMixin ],
  data() {
    return {
      isvideoCoverImgShow: true,
      show: false,
      actions: [],
      info: {},
      show_news: true,
      officialWebBg: require("../imgs/banner1.png"),
      projectIntroductionBg: require("../imgs/projectIntroductionBg.png"),
      dataDownloadBg: require("../imgs/dataDownloadBg.png"),
      customBtnBg: require("../imgs/customBtnBg.png"),
      loading: false,
      isVideoPlaying: false,
      activeIndex: 0,
      // isShare: false,
      isPromotePopShow: false,
      isSaasWInApp: null,
      // appExtInfo: null
    };
  },

  methods: {
    isMobile,
    onChange(index) {
      this.pauseVideo();
      this.activeIndex = index;
    },
    playVideo(e,item) {
      if(!isMobile()){
        if(!this.isSaasWInApp){
          e.stopPropagation()
          this.isPromotePopShow = true
          return
        }
        e.preventDefault()
        let obj = {
          src: item.media_url,
          title: item.title || '',
          cover_img: item.cover_img
        }
        window.parent.postMessage({type: 'playVideo',data: obj},`${process.env.VUE_APP_SAAS_PC_URL}`)
        return
      }
      const video = this.$refs.video[this.activeIndex];
      video.play()
      if(this.isvideoCoverImgShow) this.isvideoCoverImgShow = false
    },
    pauseVideo() {
      const video = this.$refs.video[this.activeIndex];
      video.pause();
    },
    select(item) {
      this.downloadIsOpen(item);
    },
    openUrl(type, url, item = {}) {
      const UA = navigator.userAgent;
      const isAndroid = UA.indexOf("Android") > -1 || UA.indexOf("Adr") > -1; // android终端
      const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); // ios终端
      if(url.indexOf('qr61.cn') !== -1 || url.indexOf('preview-static') !== -1){
        url = `/docWorkRoom/projectMain/commonIframe?url=${btoa(url)}`
      }
      let param = JSON.stringify({
        type,
        url,
        shareTitle: item.share_custom_title,
        shareIcon: item.share_custom_icon,
        shareContent: ''
      });
      console.log(url);
      console.log(param);
      if(isMobile()){
        if (isAndroid) {
          try {
            window.android.openOtherPage(type, url);
          } catch (error) {
            // alert(JSON.stringify(error))
          }
          try {
            window.android.openOtherPage(type, url, item.share_custom_title, item.share_custom_icon, item.suggest_word);
          } catch (error) {
            // alert(JSON.stringify(error))
          }
        } else if (isIOS) {
          window.webkit.messageHandlers.openOtherPage.postMessage(param);
        }
      }else{
        if(url.slice(0,1) == '/'){
          // let tempUrl = url.slice(0,1) == '/' ? url.slice(1,url.length) : url
          url = process.env.VUE_APP_BASE_URL + url.slice(1,url.length)
        }
        if(url.indexOf('previewData') != -1 && url.indexOf('.mp4') != -1){
          window.location.href = url
          return
        }
        if(url.indexOf('weixin') != -1 || (url.indexOf('previewData') != -1 && (url.indexOf('.pdf') != -1 || url.indexOf('.jpg') != -1 || url.indexOf('.png') != -1 || url.indexOf('.webp') != -1))){
          window.openNewWindowFromSaas ? window.openNewWindowFromSaas(url) : window.open(url)
          return
        }
        if([1,6].indexOf(item.column_type) != -1 && item.resource_type == 1){
          // this.isPromotePopShow = true
          // window.parent.postMessage({type: 'showNavBar',data: true},`${process.env.VUE_APP_SAAS_PC_URL}`)
          // window.location.href = url
          window.openNewWindowFromSaas ? window.openNewWindowFromSaas(url) : window.open(url)
          return
        }
        window.location.href = url
        // if(url.indexOf('weixin') != -1 || (url.indexOf('previewData') != -1 && (url.indexOf('pdf') != -1 || url.indexOf('jpg') != -1 || url.indexOf('png') != -1 || url.indexOf('webp') != -1))){
        //   window.openNewWindowFromSaas ? window.openNewWindowFromSaas(url) : window.open(url)
        // }else{
        //   window.location.href = url
        // }
      }
      
      // if (process.env.NODE_ENV === "production") {
      //   if (isAndroid) {
      //     window.android.openOtherPage(type, url);
      //   } else if (isIOS) {
      //     window.webkit.messageHandlers.openOtherPage.postMessage(param);
      //   }
      // } else {
      //   this.$router.push(url);
      // }
    },
    formatJudge(url) {
      let dcode = decodeURIComponent(url);
      let parsedURL = new URL(dcode);
      let pathname = parsedURL.pathname;
      let fileExtension = pathname.split(".").pop().toLowerCase();
      return fileExtension;
    },
    backGo(type, url) {
      const UA = navigator.userAgent;
      const isAndroid = UA.indexOf("Android") > -1 || UA.indexOf("Adr") > -1; // android终端
      const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); // ios终端
      let param = JSON.stringify({
        type,
        url,
      });
      if (isAndroid) {
        console.log("安卓");
        window.android.backGohomePage(type, url);
      } else if (isIOS) {
        window.webkit.messageHandlers.backGohomePage.postMessage(param);
      }
    },
    downloadIsOpen(item) {
      if (
        item.column_type == 5 ||
        (item.column_type == 6 && item.resource_type == 3)
      ) {
        this.openUrl(2,`/docWorkRoom/projectMain/dataList?signId=${item.id}&titleNmae=${encodeURIComponent(item.name)}`,item);
      } else {
        if (item.resource_type == 1) {
          this.openUrl(1, item.link_text,item);
        } else {
          if (item.link_text) {
            this.openUrl(1, item.link_text,item);
          } else {
            if (item.media[0].chunk_urls && item.media[0].chunk_urls.length) {
              this.openUrl(
                2,
                `/docWorkRoom/projectMain/previewData?format=${this.formatJudge(item.media[0].media_url)}&isPreview=1&urlPath=${encodeURIComponent(item.media[0].media_url)}&media_img=${encodeURIComponent(item.media[0].cover_image)}&titleNmae=${encodeURIComponent(item.name)}&flag=true`,
                item
              );
              localStorage.setItem("chunk_urls",JSON.stringify(item.media[0].chunk_urls));
            } else {
              this.openUrl(
                2,
                `/docWorkRoom/projectMain/previewData?format=${this.formatJudge(item.media[0].media_url)}&isPreview=1&urlPath=${encodeURIComponent(item.media[0].media_url)}&media_img=${encodeURIComponent(item.media[0].cover_image)}&titleNmae=${encodeURIComponent(item.name)}`,
                item
              );
            }
          }
        }
      }
    },
    btnName(key) {
      let name = "";
      switch (key) {
        case 0:
          name = "立即报名";
          break;
        case 1:
          name = "审核中...";
          break;
        case 2:
          name = "已成功报名项目";
          break;
        case 3:
          name = "审核未通过，重新申请";
          break;
        default:
          name = "立即报名";
          break;
      }
      return name;
    },
    getUrlFn(k) {
      let objId = k.children.find((item) => {
        return this.info.project_apply_mmc_type_name == item.name;
      });
      return objId.id || "";
    },
    getMoreNews(item) {
      let url = item.link_text || item.jump_url

      if(!isMobile()){
        this.isPromotePopShow = true
        return
      } else if (url) {
        const params = {
          parameter: url,
          shareTitle: item.share_custom_title || '',
          shareIcon: item.share_custom_icon || '',
          shareContent: '',
        }

        if (getSystemType() === 'ios') {
          window.webkit.messageHandlers.breathMessage.postMessage(JSON.stringify(params))
        } else {
          try {
            window.android.openOtherPage(0, url);
          } catch (error) {
          }
          try {
            window.android.openOtherPage(0, url, params.shareTitle, params.shareIcon, params.shareContent)
          } catch (error) {
          }
        }
      }

      // this.toDetail(item)
    },
    toDetail(item) {
      // 分享页面 点击按钮 跳转下载工作室
      if(this.isShare){
        this.goDownloadDocApp()
        return
      }
      if(!item.is_click) return
      // if(!item.link_text){
      //   Toast('暂无配置功能链接，请联系管理员')
      //   return
      // }
      
      if (item.children && item.children.length) {
        if (
          this.info.project_apply_mmc_type != -1 &&
          this.info.project_apply_mmc_type != 0
        ) {
          let params = "";
          item.children.forEach((item) => {
            if (this.info.project_apply_mmc_type_name == item.name) {
              params = item.id;
            }
          });
          this.openUrl( 2, `/docWorkRoom/projectMain/dataList?signId=${params}&titleNmae=${encodeURIComponent( item.name )}`,item);
        } else {
          this.show = true;
          this.actions = item.children;
        }
      } else if (item.column_type == 5) {
        this.openUrl( 2,`/docWorkRoom/projectMain/dataList?signId=${item.id}&titleNmae=${encodeURIComponent(item.name)}`,item);
      } else if (item.column_type == 8) {
        if(!isMobile()){
          this.isPromotePopShow = true
          return
        }
        this.getTrainingPlatformUrlFun()
      } else {
        this.downloadIsOpen(item);
      }
    },
    getTrainingPlatformUrlFun(){
      getTrainingPlatformUrl(this.info.project_id).then(res=>{
        if(res.code == 200){
          if(isMobile()){
            if(getSystemType() == 'ios'){
              let params = JSON.stringify({type: 1,url: res.data.link})
              window.webkit.messageHandlers.openOtherPage.postMessage(params)
            }else{
              // window.android.openOtherPage(1,res.data.link)
              try {
                window.android.openOtherPage(1,res.data.link)
              } catch (error) {
                // alert(JSON.stringify(error))
              }
              try {
                window.android.openOtherPage(1,res.data.link,'','','')
              } catch (error) {
                // alert(JSON.stringify(error))
              }
            }
          }else{
            if(res.data.link.indexOf('weixin') != -1){
              window.openNewWindowFromSaas ? window.openNewWindowFromSaas(res.data.link) : window.open(res.data.link)
            }else{
              window.location.href = res.data.link
            }
            // window.location.href = res.data.link
          }
        }
      })
    },
    getDetail() {
      this.loading = true;
      const params = this.$route.query.project_id;
      newGetProDetail(params)
        .then((res) => {
          if (res.code == 200) {
            this.loading = false;
            this.info = res.data;
            this.show_news = this.filterNewsItem(this.info.project_attr_info.quick_item || [])
            console.log('show_news: ', this.show_news)
            // this.info.project_attr_info.group_item = this.info.project_attr_info.group_item.concat(this.info.project_attr_info.group_item.slice(0,2))
            // this.info.project_apply_status = 0;
          } else {
            this.loading = false;
            Toast(res.msg);
          }
        })
        .catch((err) => {
          this.loading = false;
          Toast(err);
        });
    },

    filterNewsItem(list) {
      const news = list.find((item) => item.column_type === 9)

      return news && news.children && news.children.length > 0
    },

    anewBtn() {
      if(!isMobile()){
        this.isPromotePopShow = true
        // this.$toast('请下载医生工作室体验完整功能')
        return
      }
      if(this.isShare){
        this.goDownloadDocApp()
        return
      }
      this.noDoubleTap(() => {
        if (
          this.info.project_apply_link_data.children &&
          this.info.project_apply_link_data.children.length &&
          this.info.project_apply_status == 0
        ) {
          this.show = true;
          this.actions = this.info.project_apply_link_data.children;
        } else {
          if (this.info.project_apply_status == 3) {
            this.openUrl(2,`/docWorkRoom/projectMain/registerStatus?project_id=${this.info.project_id}&gotoApply=${encodeURIComponent(this.info.project_apply_link_data.link_text)}`);
          } else if (this.info.project_apply_status == 0) {
            this.openUrl(1, `${this.info.project_apply_link_data.link_text}`);
          }
        }
      })
    },
    specialBottomBtnFun(){
      if(!isMobile()){
        this.isPromotePopShow = true
        // this.$toast('请下载医生工作室体验完整功能')
        return
      }
      if(this.isShare){
        this.goDownloadDocApp()
        return
      }
      if(getSystemType() == 'ios'){
        window.webkit.messageHandlers.jumpNativeMethod.postMessage(JSON.stringify({'parameter': this.info.project_only_info.key}))
      }else{
        window.android.jumpNativeMethod(this.info.project_only_info.key)
      }
    },
    callFun(){
      if(!isMobile()){
        this.isPromotePopShow = true
        // this.$toast('请下载医生工作室体验完整功能')
        return
      }
      if(this.isShare){
        window.location.href = `tel:${this.info.concat_cell.concat_cell}`
        return
      }
      if(getSystemType() == 'ios'){
        window.webkit.messageHandlers.callPhoneMethod.postMessage(JSON.stringify({'phone': this.info.concat_cell.concat_cell}))
      }else{
        window.android.callPhoneMethod(this.info.concat_cell.concat_cell)
      }
    },
    // 医生工作室下载页面
    goDownloadDocApp(){
      return
      window.location.href = "https://patient-h5.zz-med.com/static/doc/View/download/Index.html"
    },
    getDefaultData(){
      this.loading = true;
      const params = this.$route.query.project_id;
      getDefaultProDetail(params).then(res=>{
        if (res.code == 200) {
            this.loading = false;
            this.info = res.data;
          } else {
            this.loading = false;
            Toast(res.msg);
          }
      })
    },
    handleMessage(e){
      let { type, data } = e.data
      console.log('H5接收到saas父页面的消息',type,data)
      // 播放视频
      if(type == 'isSaasWInApp'){
        this.isSaasWInApp = data
        console.log('接收到了isSaasWInApp', data)
        // alert('接收到了isSaasWInApp')
        // alert(data)
      }
    },
    onJoinOnlineClinic(o) {
      const { key, type } = o
      console.log(type, key)
      if(!isMobile()){
        this.isPromotePopShow = true
        return
      } else {
        const params = {
          parameter: key,
          shareTitle: '',
          shareIcon: '',
          shareContent: '',
        }
        if (type === 'h5') {
          //会员管理需要在暴风的h5页面打开
          if (getSystemType() === 'ios') {
            window.webkit.messageHandlers.openMemberPage.postMessage(JSON.stringify({'url': key,'title': '会员管理'})) 
          } else {
            window.android.openMemberPage(key,'会员管理')
          }
        } else if (type === 'origin') {
          if (getSystemType() === 'ios') {
            window.webkit.messageHandlers.jumpNativeMethod.postMessage(JSON.stringify(params))
          } else {
            window.android.jumpNativeMethod(key)
          }
        }
      }
    },
  },
  created() {
    this.isShare = this.$route.query.isShare
    if(this.isShare){
      this.getDefaultData()
    }else{
      this.getDetail()
    }
  },
  mounted() {
    // 非分享页面 下滑出现顶部色块
    if(!this.isShare){
      // 监听滚动条距离顶部距离，改变顶部区块透明度
      let topColorBlock = this.$refs.topColorBlock;
      window.addEventListener('scroll', function() {
        // 获取滚动条滚动距离
        let scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
        if(scrollTop > 300) {
          topColorBlock.style.opacity = 1
          return
        }
        topColorBlock.style.opacity = scrollTop / 300;
      })
    }
    if(!isMobile()){
      this.$nextTick(()=>{
        window.parent.postMessage({type: 'showNavBar',data: false},`${process.env.VUE_APP_SAAS_PC_URL}`)
        window.parent.postMessage({type: 'isLoaded',data: ''},`${process.env.VUE_APP_SAAS_PC_URL}`)
      })
    }
    window.addEventListener('message', this.handleMessage, false);
  },
};
</script>

<style scoped lang="scss">
.projectMain {
  width: 100%;
  min-height: 100vh;
  padding-bottom: 84px;
  background: #F5F5F5;
  box-sizing: border-box;
  .topColorBlock{
    height: 44px;
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 999;
    opacity: 0;
  }
  .topLogoOuter {
    // padding-top: 40px;
    // margin: 48px -15px 0;
    padding-bottom: 40px;
  }

  .logoOuter {
    background: url("../imgs/logoBgLight.png");
    background-size: 100%;
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: auto;

    .logo {
      width: 46px;
      height: 46px;
      border-radius: 50%;
    }
  }

  .projectName {
    font-size: 18px;
    font-weight: 500;
    color: white;
  }

  .toolbarOuter {
    display: flex;
    flex-wrap: wrap;
    background: linear-gradient(180deg,
        rgba(0, 0, 0, 0.12) 0%,
        rgba(0, 0, 0, 0) 75.44%);
    border-radius: 20px;
    margin: 16px 15px 0;
    padding: 16px 0 10px;
    .flex1{
      flex: 1;
    }
    .avg{
      width: 20%;
    }
    .toolItem {
      // flex: 1;
      // width: 20%;
      display: flex;
      flex-direction: column;
      // justify-content: center;
      align-items: center;
      margin-bottom: 4px;
      cursor: pointer;
      .toolIcon {
        width: 32px;
        height: 32px;
      }

      .toolName {
        color: rgba(255, 255, 255, 0.6);
        font-size: 13px;
        margin-top: 6px;
        display: -webkit-box;
        word-break: break-all;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        text-overflow: ellipsis;
        line-height: 16px;
      }
    }
  }

  .innerOuter {
    background: #F8F8F8;
    margin-top: -40px;
    padding: 16px;
    border-radius: 20px 20px 0 0;
    min-height: 50px;
  }

  .swipeOuter {
    width: 100%;
    height: 194px;
    position: relative;
    .videoCoverImg{
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 99;
      border-radius: 16px;
    }
    // margin-top: 32px;
    .swipe,
    .swipeItem {
      width: 99%;
      height: 194px;
      border-radius: 16px;
    }

    .videoItem {
      width: 100%;
      height: 194px;
      border-radius: 16px;
      // background: white;
    }
  }
  .fastImgsOuter{
    img {
      vertical-align: top;
    }
    .news {
      margin-top: 12px;
      .bar {
        img {
          // margin-top: 12px;
          width: 100%;
        }
      }
      ul li {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 12px;
        padding: 8px;
        border-radius: 8px;
        background-color: #fff;
        .cover {
          width: 75px;
          img {
            width: 75px;
            height: 75px;
            border-radius: 8px;
          }
        }
        .content {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          width: calc(100% - 90px);
          height: 70px;
          text-align: left;
          .title {
            display: -webkit-box;
            line-clamp: 2;
            -webkit-line-clamp: 2;
            box-orient: vertical;
            -webkit-box-orient: vertical;
            overflow: hidden;
            line-height: 1.5;
            font-size: 15px;
            color: #333;
            font-weight: bold;
          }
          .date {
            font-size: 13px;
            color: #999;
          }
        }
      }
    }
    cursor: pointer;
    .fastImg{
      width: 100%;
      margin-top: 16px;
      // box-shadow: 0px 12px 12px 0px #AEAEAE0f;
      border-radius: 16px;
    }
  }
  .bottomBtnOuter {
    width: 100%;
    height: 70px;
    position: fixed;
    bottom: 0;
    left: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    padding: 0 15px;
    box-sizing: border-box;
    &.btn-join-clinic {
      .btn {
        color: #fff;
        box-shadow: 0px 4px 14.7px 0px rgba(255, 255, 255, 0.30) inset, 0px -4px 6px 0px rgba(255, 255, 255, 0.28) inset;
      }
    }
    .leftContact{
      display: flex;
      flex-direction: column;
      color: #000;
      text-align: center;
      font-size: 14px;
      font-weight: 500;
      padding-right: 20px;
      .kefuIcon{
        width: 32px;
        height: 32px;
      }
      span{
        margin-top: 2px;
      }
    }
    .btn {
      flex: 1;
      // width: 315px;
      height: 52px;
      // margin: 10px 20px 0;
      // margin: 0 20px;
      border-radius: 30px;
      font-size: 20px;
      font-weight: 600;
      text-align: center;
      line-height: 52px;
      cursor: pointer;
    }

    .btnText {
      font-size: 20px;
      font-weight: 600;
      // margin-top: 20px;
      flex: 1;
    }

    .fail {
      color: white;
      background: #fc1a1a;
    }
  }
  .peojectIframe{
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 0;
  }
}
</style>
