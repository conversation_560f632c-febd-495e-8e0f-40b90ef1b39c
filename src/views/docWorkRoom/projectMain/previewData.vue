<template>
  <div class="container">
    <NavBar v-if="!isMobile() && ['pdf','png','jpg','webp'].indexOf(format) == -1" color="#333333"></NavBar>
    <div ref="content" class="content" v-if="!isNoData">
      <div v-if="format == 'pdf' && !flag" class="pdf-box" id="zoomBox">
        <van-loading v-if="loading" style="padding-top: 200px"
          >内容加载中...</van-loading
        >
        <pdf ref="pdf" v-for="i in numPages" :key="i" :src="pdfUrl" :page="i">
        </pdf>

        <!-- 分页 -->
        <div class="pageButton" v-if="false">
          <van-button
            size="small"
            @click="changePage(0)"
            round
            style="margin-right: 10px"
            >上一页</van-button
          >
          <span> {{ currentPage }} / {{ pageCount }} </span>
          <van-button
            style="margin-left: 10px"
            size="small"
            @click="changePage(1)"
            round
            >下一页</van-button
          >
        </div>
      </div>
      <div v-else-if="format == 'pdf' && flag" class="pdf-box jpgClass" id="zoomBox">
        <img
          v-for="(item, index) in chunk_urls"
          :key="index"
          v-lazy="item"
          style="width: 100%" alt="" />
      </div>
      <div ref="videoOuter" class="vidio" v-else-if="format == 'mp4'">
        <video
          controlslist="nodownload noplaybackrate noremoteplayback"
          ref="videoRef"
          webkit-playsinline='true'
          playsinline='true'
          controls
          @play="playVideo"
          @pause="pauseVideo"
          class="videoItem"
          :src="urlPath"
          :poster="mediaImg"
        ></video>
        <div class="videoCoverImg" v-if="isvideoCoverImgShow" @click="playVideo"></div>
      </div>
      <div
        class="jpgClass"
        id="zoomBox"
        v-else-if="format == 'jpg' || format == 'png' || format == 'webp' || format == 'jpeg'"
        @click="openImg"
      >
        <img :src="urlPath" style="width: 100%" alt="" />
      </div>
      <div class="textClass" v-else @click="openImg">
        <img class="img" src="../imgs/noview.png" />
        <span>暂不支持打开此文件<br />请下载后查看</span>
      </div>
    </div>
    <div class="noData" v-else>暂无数据</div>
    <div class="foot" @click="downPhone" v-if="isPreview != 1">
      <img class="icon" :src="iconImg" alt="" />
      <span class="text">下载到手机</span>
    </div>
    <div v-if="sourcePlatform && sourcePlatform != 'docApp'" class="goDocListBtn">
      <van-button block @click="goDocList" style="color: black;font-weight: bold;font-size: 16px;">咨询医生</van-button>
    </div>
    <PromoteDownloadPop v-if="isPromotePopShow"></PromoteDownloadPop>
    <template v-if="isShare">
      <wx-open-launch-app
        id="top_btn"
        appid="wxd4d251ee3ba85b70"
        :extinfo="appExtInfo"
        style="position: fixed;left: 0;top: 0;width: 100%;background-color: transparent;z-index: 9999999999;"
      >
        <script type="text/wxtag-template">
          <style>
            .topOpenAppOuter {
              width: 100%;
              height: 50px;
              line-height: 50px;
              background-color: rgba(255,255,255,0.6);
              position: relative;
            }
            .logo {
              width: 35px;
              height: 35px;
              position: absolute;
              left: 16px;
              top: 8px;
            }
            .title {
              font-size: 16px;
              color: #181818;
              height: 50px;
              line-height: 50px;
              position: absolute;
              left: 56px;
            }
            .topOpenAppBtn {
              width: 80px;
              border-radius: 50px;
              text-align: center;
              height: 30px;
              line-height: 30px;
              background: #F7830D;
              font-size: 14px;
              color: white;
              position: absolute;
              right: 16px;
              top: 10px;
            }
          </style>
          <div class="topOpenAppOuter">
            <img class="logo" src="https://ares.zz-med.com/doctorapp/docAppLogo.png" alt="">
            <span class="title">医生工作室</span>
            <div class="topOpenAppBtn">打开APP</div>
          </div>
        </script>
      </wx-open-launch-app>
      <wx-open-launch-app
        id="bottom_btn"
        appid="wxd4d251ee3ba85b70"
        :extinfo="appExtInfo"
        style="position: fixed;left: 0;bottom: 120px;width: 100%;background-color: transparent;z-index: 9999999999;"
      >
        <script type="text/wxtag-template">
          <style>
            .bottomOpenAppBtn {
              width: 260px;
              border-radius: 50px;
              text-align: center;
              height: 50px;
              line-height: 50px;
              margin-left: 50%;
              position: relative;
              left: -130px;
              background: #F7830D;
              font-size: 18px;
              color: white;
              box-shadow: 0 4px 8px #FDD2AB;
            }
          </style>
          <div class="bottomOpenAppBtn">医生工作室APP内打开</div>
        </script>
      </wx-open-launch-app>
    </template>
  </div>
</template>

<script>
import pdf from "vue-pdf";
import wxSDK from 'weixin-js-sdk'
import { jsToClient } from '@/utils/bfBridge'
import { EasyScroller } from "easyscroller";
import { ImagePreview } from "vant";
import { getUrlParams,isMobile,getSystemType } from '@/utils/utils.js'
import NavBar from './components/navBar.vue'
import PromoteDownloadPop from './components/promoteDownloadPop.vue';
import wx from "weixin-js-sdk"
import { getWxConfig } from "@/api/docWorkRoom.js";
import { openAppMixin } from "./mixin/openApp";
export default {
  components: {
    pdf,
    NavBar,
    PromoteDownloadPop
    // http://storage.xuetangx.com/public_assets/xuetangx/PDF/PlayerAPI_v1.0.6.pdf
  },
  mixins: [ openAppMixin ],
  data() {
    return {
      isvideoCoverImgShow: true,
      isPromotePopShow: false,
      format: "",
      firstImage: "",
      isPreview: "",
      urlPath: "",
      pageNum: 1,
      pageTotalNum: 1,
      iconImg: require("../imgs/phoneLoadIcon.png"),
      numPages: undefined,
      pdfUrl: "",
      scroller: null,
      mediaImgTest: "",
      loading: false,
      currentPage: 0,
      pageCount: 0,
      chunk_urls: [], // pdf 是否转成图片列表
      flag: false, // pdf 是否转成图片
      isNoData: false,
      isSaasWInApp: null,
      sourcePlatform: ''
    };
  },
  beforeDestroy() {
    this.scroller.destroy(); // 销毁
  },
  methods: {
    isMobile,
    playVideo() {
      if(!isMobile()){
        if(!this.isSaasWInApp){
          this.isPromotePopShow = true
          return
        }
        let obj = {
          src: this.urlPath,
          title: '',
          cover_img: this.mediaImg
        }
        window.parent.postMessage({type: 'playVideo',data: obj},`${process.env.VUE_APP_SAAS_PC_URL}`)
        return
      }
      const video = this.$refs.videoRef;
      video.play()
      if(this.isvideoCoverImgShow) this.isvideoCoverImgShow = false
    },
    pauseVideo() {
      const video = this.$refs.videoRef;
      video.pause();
    },
    // 翻页
    changePage(val) {
      if (val === 0 && this.currentPage > 1) {
        this.currentPage--;
      }
      if (val === 1 && this.currentPage < this.pageCount) {
        this.currentPage++;
      }
    },
    // pdf加载时
    loadPdf() {
      console.log(this.currentPage);
      this.currentPage = 1; // 加载的时候先加载第一页
    },
    openImg() {
      // ImagePreview([this.urlPath]);
    },
    zoomBox() {
      if(!isMobile()){
        return
      }
      const ele = document.querySelector("#zoomBox");
      this.scroller = new EasyScroller(ele, {
        scrollingX: true,
        scrollingY: true,
        zooming: true,
        minZoom: 1, // 最小缩放
        maxZoom: 5, // 最大缩放
        zoomLevel: 1, // 初始值缩放
        bouncing: false,
      });
    },
    getTotal() {
      let loadingTask = this.$route.query.urlPath;
      console.log(this.$route.query.urlPath);
      this.pdfUrl = pdf.createLoadingTask({
        url: loadingTask,
      });
      this.loading = true;
      this.pdfUrl.promise
        .then((pdf) => {
          this.loading = false;
          this.numPages = pdf.numPages;
        })
        .catch((error) => {
          this.loading = false;
        });
    },

    pdfError(error) {
      console.error(error);
    },
    downPhone() {
      if(!isMobile()){
        this.isPromotePopShow = true
        // this.$toast('请下载医生工作室App体验完整功能')
        return
      }
      const UA = navigator.userAgent;
      const isAndroid = UA.indexOf("Android") > -1 || UA.indexOf("Adr") > -1; // android终端
      const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); // ios终端
      let params = JSON.stringify({
        type: this.format,
        url: this.urlPath,
      });
      if (isAndroid) {
        console.log("安卓");
        window.android.downloadFile(this.format, this.urlPath);
      } else if (isIOS) {
        window.webkit.messageHandlers.downloadFile.postMessage(params);
      }
    },
    handleVideo(){
      let urlPath = decodeURIComponent(this.urlPath)
      let width = getUrlParams(urlPath,'width')
      let height = getUrlParams(urlPath,'height')
      console.log('宽-高',width,height)
      if(width && height && Number(width) > 0 && Number(height) > 0){
        if(Number(width) < Number(height)){
          setTimeout(()=>{
            this.$nextTick(()=>{
              let video = this.$refs.videoRef
              let videoOuter = this.$refs.videoOuter
              let content = this.$refs.content
              if(video){
                video.style.width = '100%'
                video.style.height = '100%'
                video.style.borderRadius = '0'
                videoOuter.style.width = '100vw'
                videoOuter.style.height = '100vh'
                content.style.padding = '0'
              }else{
                console.log('还没加载完')
              }
            })
          })
        }
      }
    },
    handleMessage(e){
      let { type, data } = e.data
      console.log('H5接收到saas父页面的消息',type,data)
      // 播放视频
      if(type == 'isSaasWInApp'){
        this.isSaasWInApp = data
        console.log('接收到了isSaasWInApp', data)
      }
    },
    goDocList(){
      const UA = navigator.userAgent
      if(UA.indexOf("miniProgram") > -1 || UA.indexOf("MiniProgram") > -1){
        wxSDK.miniProgram.navigateTo({
          url: `/pages/healthStation/docList/docList?quick=quick&type=other`
        })
      }else{
        const params = {
          action: 0,
          data: {
            title: '在线问诊',
            url: `${process.env.VUE_APP_BF_BASE_URL}web/consultation/index`,
            fullScreen: false,
          }
        }
        jsToClient('jumpTo', JSON.stringify(params))
      }
    }
  },
  mounted() {
    if (this.format == "pdf") {
      this.getTotal();
      this.zoomBox();
    }
    if (this.format == "jpg" || this.format == "png" || this.format == "webp") {
      this.zoomBox();
    }
    document.title = this.$route.query.titleNmae || "项目资料";
    this.sourcePlatform = this.$route.query.sourcePlatform
    if(this.format == 'mp4'){
      this.handleVideo()
    }

    if(!isMobile()){
      this.$nextTick(()=>{
        window.parent.postMessage({type: 'isLoaded',data: ''},`${process.env.VUE_APP_SAAS_PC_URL}`)
      })
    }
    window.addEventListener('message', this.handleMessage, false);
  },
  created() {
    this.format = this.$route.query.format.toLowerCase();
    this.urlPath = this.$route.query.urlPath;
    this.isPreview = this.$route.query.isPreview;
    this.mediaImg = this.$route.query.media_img;
    this.flag = this.$route.query.flag
    if (this.flag) {
      this.chunk_urls = JSON.parse(localStorage.getItem('chunk_urls'))
    }
    if(!this.urlPath){
      this.isNoData = true
    }
  },
};
</script>

<style scoped lang="scss">
.container {
  height: 100%;
  // background-color: #F5F6FA;
  background: #F5F5F5;
  padding-bottom: 100px;
  .content {
    height: 100vh;
    padding: 16px;
  }
  .pdf-box {
    padding-top: 16px;
    padding-bottom: 200px;
    .pageButton {
      margin-top: 50px;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
    }
  }
  .foot-box {
    position: fixed;
    height: 90px;
    width: 100%;
    background: rgba(255, 255, 255, 0.9);
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    bottom: 0px;
  }
  .foot {
    position: fixed;
    height: 56px;
    width: 100%;
    bottom: 0px;
    color: #fc6a1e;
    font-size: 17px;
    font-weight: 500;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .vidio {
    // padding: 20px 10px;
    position: relative;
    .videoCoverImg{
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 99;
    }
  }
  .videoItem {
    width: 100%;
    height: 194px;
    border-radius: 16px;
    background: white;
  }
  .jpgClass {
    width: 100%;
    padding-bottom: 160px;
  }
  .textClass {
    font-size: 22px;
    color: #000;
    padding-top: 65%;
    display: flex;
    flex-direction: column;
    align-items: center;
    .img {
      width: 93px;
      height: 97px;
    }
    span {
      color: #5f5f5f;
      font-size: 17px;
      line-height: 30px;
    }
  }
  .icon {
    width: 24px;
    height: 24px;
    margin-right: 8px;
  }
  .text {
    color: #5a6266;
    font-size: 15px;
  }
  .noData{
    color: #CCCCCC;
    margin-top: 30%;
    font-size: 16px;
  }
  .goDocListBtn{
    width: 100%;
    position: fixed;
    bottom: 20px;
    padding: 20px;
    box-sizing: border-box;
  }
}
</style>
