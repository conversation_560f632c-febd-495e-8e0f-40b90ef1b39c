<template>
  <div class="dataList">
    <NavBar v-if="!isMobile()" color="#333333"></NavBar>
    <van-loading v-if="loading" style="padding-top: 200px;"/>
    <div v-if="!loading">
      <div class="tipsOuter" v-if="this.docList.resource_desc">
        <div class="tipsTitle">温馨提示！</div>
        <img class="tipsImg" src="../imgs/bgIcon.png" alt="">
        <div :class="!isSuccess?'tipsText':'click-tipsText'" @click="clickTips" ref="tipsText">{{ this.docList.resource_desc }}</div>
      </div>
      <div class="listOuter">
        <commonList :docList = this.docList.children :canLoad="docList.netdisk_url?true:false"></commonList>
      </div>
      <div class="foot" v-if="docList.netdisk_url">
         <div class="bottomBtnOuter">
          <div class="btnItem" v-for="(item, index) in btnArr" :key="index" @click="donloadType(item.type)">
            <img class="icon" :src="item.path" alt="">
            <span class="text">{{ item.label }}</span>
          </div>
        </div>
      </div>
      <downloadDialog 
        :downloadUrl="this.docList || {}"
        :show="show"
        :downloadType="downloadType"
        :title="title"
        :nextTitle="nextTitle"
        :describe="describe"
        :btnText="btnText"
        @closeShow="closeShow"
      ></downloadDialog>
    </div>
    <div class="noData" v-if="!loading && isNoData">暂无数据</div>
    <PromoteDownloadPop v-if="isPromotePopShow"></PromoteDownloadPop>
    <template v-if="isShare">
      <wx-open-launch-app
        id="top_btn"
        appid="wxd4d251ee3ba85b70"
        :extinfo="appExtInfo"
        style="position: fixed;left: 0;top: 0;width: 100%;background-color: transparent;z-index: 9999999999;"
      >
        <script type="text/wxtag-template">
          <style>
            .topOpenAppOuter {
              width: 100%;
              height: 50px;
              line-height: 50px;
              background-color: rgba(255,255,255,0.6);
              position: relative;
            }
            .logo {
              width: 35px;
              height: 35px;
              position: absolute;
              left: 16px;
              top: 8px;
            }
            .title {
              font-size: 16px;
              color: #181818;
              height: 50px;
              line-height: 50px;
              position: absolute;
              left: 56px;
            }
            .topOpenAppBtn {
              width: 80px;
              border-radius: 50px;
              text-align: center;
              height: 30px;
              line-height: 30px;
              background: #F7830D;
              font-size: 14px;
              color: white;
              position: absolute;
              right: 16px;
              top: 10px;
            }
          </style>
          <div class="topOpenAppOuter">
            <img class="logo" src="https://ares.zz-med.com/doctorapp/docAppLogo.png" alt="">
            <span class="title">医生工作室</span>
            <div class="topOpenAppBtn">打开APP</div>
          </div>
        </script>
      </wx-open-launch-app>
      <wx-open-launch-app
        id="bottom_btn"
        appid="wxd4d251ee3ba85b70"
        :extinfo="appExtInfo"
        style="position: fixed;left: 0;bottom: 120px;width: 100%;background-color: transparent;z-index: 9999999999;"
      >
        <script type="text/wxtag-template">
          <style>
            .bottomOpenAppBtn {
              width: 260px;
              border-radius: 50px;
              text-align: center;
              height: 50px;
              line-height: 50px;
              margin-left: 50%;
              position: relative;
              left: -130px;
              background: #F7830D;
              font-size: 18px;
              color: white;
              box-shadow: 0 4px 8px #FDD2AB;
            }
          </style>
          <div class="bottomOpenAppBtn">医生工作室APP内打开</div>
        </script>
      </wx-open-launch-app>
    </template>
  </div>
</template>

<script>
import commonList from './components/commonList.vue'
import downloadDialog from './components/downloadDialog.vue'
import { getInformationList } from '@/api/common/projectMain.js'
import { isMobile } from '@/utils/utils'
import NavBar from './components/navBar.vue'
import PromoteDownloadPop from './components/promoteDownloadPop.vue';
import wx from "weixin-js-sdk"
import { getWxConfig } from "@/api/docWorkRoom.js";
import { openAppMixin } from "./mixin/openApp";
export default {
  components: {
    commonList,
    downloadDialog,
    NavBar,
    PromoteDownloadPop
  },
  mixins: [ openAppMixin ],
  data(){
    return {
      isPromotePopShow: false,
      btnArr: [
        // {
        //   path: require('../imgs/msgIcon.png'),
        //   type:'email',
        //   label: '发送到邮箱'
        // },
        {
          path: require('../imgs/netdiskLoadIcon.png'),
          type: 'disc',
          label: '网盘下载'
        },
        {
          path: require('../imgs/phoneLoadIcon.png'),
          type: 'phone',
          label: '下载到手机'
        }
      ],
      docList:[],
      downloadType: '',
      title: '',
      nextTitle: '',
      describe: '',
      btnText: '',
      show: false,
      loading:false,
      isSuccess:false,
      isNoData: false,
    }
  },
  created() {
    this.getCatalog()
  },
  mounted() {
    document.title = decodeURIComponent(this.$route.query.titleNmae) || ''
  },
  methods: {
    isMobile,
    closeShow() {
      this.show = false
    },
    clickTips() {
      this.isSuccess = !this.isSuccess
    },
    formatJudge(url) {
      let parsedURL = new URL(url);
      let pathname = parsedURL.pathname;
      let fileExtension = pathname.split('.').pop().toLowerCase();
      return fileExtension
    },
    downPhone(url) {
      const UA = navigator.userAgent
      const isAndroid = UA.indexOf('Android') > -1 || UA.indexOf('Adr') > -1 // android终端
      const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
      let params = JSON.stringify({
        type: this.docList.content_type,
        url: url
      })
      if (isAndroid) {
        window.android.downloadFile(this.docList.content_type, url)
      } else if (isIOS) {
        window.webkit.messageHandlers.downloadFile.postMessage(params)
      }
    },
    donloadType(type) {
      if(!isMobile()){
        this.isPromotePopShow = true
        // this.$toast('请下载医生工作室体验完整功能')
        return
      }
      this.downloadType = type
      switch (type) {
        case 'email':
          this.show = true
          this.title = '发送到邮箱';
          this.nextTitle = '请填写有效的邮箱地址：';
          this.describe = '由于文件较大，我们会以附件的形式发送给您， 请留意邮箱信息';
          this.btnText = '确认邮箱地址并发送'
          break;
        case 'disc':
          this.show = true
          this.title = '网盘下载';
          this.nextTitle = '百度网盘下载：';
          this.describe = '复制以下链接，即可查看或下载文件';
          this.btnText = '复制链接及提取码'
          break;
        case 'phone':
          this.downPhone(this.docList.path)
          break;
        default:
          break;
      }
    },
    addTierId(data, tier = 0) {
      const newData = { ...data, tierid: tier };
      if (newData.children && newData.children.length > 0) {
        newData.children = newData.children.map(child =>
          this.addTierId(child, tier + 1)
        );
      }
      return newData;
    },
    getCatalog() {
      this.loading = true
      getInformationList(this.$route.query.signId).then((res) => {
        this.loading = false
        if(JSON.stringify(res.data) === '{}'){
          this.isNoData = true
          return
        }
        this.docList = this.addTierId(res.data)
        localStorage.setItem('nextChildren', JSON.stringify(this.docList))
      }).catch(() => {
        this.loading = false
      })
    }
  }
}
</script>

<style scoped lang="scss">
.dataList{
  padding: 0 15px 120px;
  box-sizing: border-box;
  background: #F5F5F5;
  min-height: 100vh;
  .tipsOuter{
    overflow: hidden;
    z-index:99;
    position: relative;
    width: 100%;
    padding: 12px 20px;
    background: #FFF3E8;
    color: #D8865C;
    border-radius: 0 0 8px 8px;
    box-sizing: border-box;
    text-align: left;
    border-bottom: 1px solid #FFE6CD;
    border-left: 1px solid #FFE6CD;
    border-right: 1px solid #FFE6CD;
    // margin-bottom: 16px;
    .tipsTitle{
      font-size: 14px;
      font-weight: 500;
    }
    .tipsText{
      font-size: 12px;
      margin-top: 8px;
      line-height: 20px;
      position: relative;
      z-index: 99;
      // background-image: url('../imgs/group3093.png');
      // background-repeat: no-repeat;
      // background-size: 90px 70px;
      // background-position: top right;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical
    }
    .click-tipsText{
      font-size: 12px;
      margin-top: 8px;
      line-height: 20px;
      position: relative;
      z-index: 99;
      // background-image: url('../imgs/group3093.png');
      // background-repeat: no-repeat;
      // background-size: 90px 70px;
      // background-position: top right;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 9999999;
      -webkit-box-orient: vertical
    }
  }
  .listOuter{
      padding-top: 16px;

  }
  .tipsImg{
    width:90px;
    height: 77px;
    position: absolute;
    top: 0;
    right: 0;
  }
  // .foot{
  //   width: 100%;
  //   height: 90px;
  //   background: rgba(255,255,255,0.9);
  //   position: fixed;
  //   left: 0;
  //   bottom: 0px;
  // }
  .bottomBtnOuter{
    width: 100%;
    height: 56px;
    position: fixed;
    bottom: 0px;
    left: 0;
    background: rgba(255,255,255,0.9);
    padding-top: 4px;
    display: flex;
    justify-content: space-around;
    align-items: center;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    .btnItem{
      display: flex;
      justify-content: space-around;
      align-items: center;
      .icon{
        width: 24px;
        height: 24px;
        margin-right: 8px;
      }
      .text{
        margin-top: 4px;
        color: #5A6266;
        font-size: 14px;
      }
    }
  }
  .noData{
    color: #CCCCCC;
    margin-top: 30%;
    font-size: 16px;
  }
}
</style>