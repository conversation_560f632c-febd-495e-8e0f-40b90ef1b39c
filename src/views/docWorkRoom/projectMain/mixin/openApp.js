import wx from "weixin-js-sdk"
import { getWxConfig } from "@/api/docWorkRoom.js";
export const openAppMixin = {
  data() {
    return {
      isShare: false,
      appExtInfo: null
    };
  },
  methods: {
    getWxConfig(){
      let url = window.location.href
      let obj = {
          url,
          account: 'ihec'
      }
      getWxConfig(obj).then(res=>{
        if(res.status == 0){
          wx.config({
            debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
            appId: res.data.appId, // 必填，公众号的唯一标识
            timestamp: res.data.timestamp, // 必填，生成签名的时间戳
            nonceStr: res.data.nonceStr, // 必填，生成签名的随机串
            signature: res.data.signature, // 必填，签名
            jsApiList: [],
            openTagList: ["wx-open-launch-app"]
          });
          wx.error(function (res) {
            alert('wx.err')
            alert(JSON.stringify(res))
          })
        }
        this.openAppBtnListener()
      })
    },
    openAppBtnListener(){
      const buttons = ['top_btn', 'bottom_btn'];
      buttons.forEach((btnId) => {
        const btn = document.getElementById(btnId);
        if (btn) {
          btn.addEventListener('launch', handleEvent);
          btn.addEventListener('error', handleEvent);
        }
      });
      function handleEvent(e) {
        //  && e.detail.errMsg == 'launch:fail'
        if(e.type == 'error'){
          setTimeout(() => {
            window.location.href = "https://patient-h5.zz-med.com/static/doc/View/download/Index.html"
          },2000)
          console.log(e.detail.errMsg)
        }
        if(e.type == 'launch'){
          console.log('launch')
          console.log(JSON.stringify(e))
          console.log(JSON.stringify(e.detail))
        }
      }
    },
    getResUrl(url, param) {
      // 创建一个 URL 对象
      const urlObj = new URL(url);
      
      // 检查是否支持 URLSearchParams
      if (typeof URLSearchParams !== 'undefined') {
        // 使用 URLSearchParams 删除参数
        const searchParams = new URLSearchParams(urlObj.search);
        searchParams.delete(param);
        urlObj.search = searchParams.toString();
      } else {
        // 回退方案：手动解析和构建查询字符串，排除指定参数
        const params = {};
        const queryString = urlObj.search.slice(1); // 去掉开头的 '?'
        const pairs = queryString.split('&');
     
        for (let pair of pairs) {
          if (pair) {
            const [key, val] = pair.split('=').map(decodeURIComponent);
            if (key !== param) { // 排除指定参数
              params[key] = val;
            }
          }
        }
     
        // 重新构建查询字符串
        const updatedQueryString = Object.keys(params)
          .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
          .join('&');
        urlObj.search = updatedQueryString ? `?${updatedQueryString}` : '';
      }
     
      // 返回修改后的 URL 字符串
      return urlObj.toString();
    }
  },
  created() {
    this.isShare = this.$route.query.isShare
    console.log(123456)
    if(this.isShare){
      let url = location.href
      if(url.indexOf('previewData') !== -1 && url.indexOf('reload') == -1){
        let newUrl = `${url}&reload=true`
        location.assign(newUrl)
      }
      let appUrl = this.getResUrl(url, 'isShare')
      // 项目首页
      if(url.indexOf('projectMain/newProjectMain') != -1){
        this.appExtInfo = JSON.stringify({
          key: 'projectMain',
          url: appUrl
        })
      }else if(url.indexOf('promotionDocApp') != -1){  // 推广页
        this.appExtInfo = JSON.stringify({
          key: 'appHome',
          url: ''
        })
      }else{
        this.appExtInfo = JSON.stringify({  // 其他内层页面
          key: 'innerPage',
          url: appUrl
        })
      }
      this.getWxConfig()
    }
  },
  mounted() {
    
  },
};