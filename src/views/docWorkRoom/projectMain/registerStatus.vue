<template>
  <div class="registerStatus">
    <div class="main">
      <van-icon size="94" color="#FC671A" name="clock" v-if="this.result.project_apply_audit_status == 1"/>
      <van-icon size="94" color="#F53F3F" name="warning" v-if="this.result.project_apply_audit_status == 3" />
      <div v-if="this.result.project_apply_audit_status == 3">
        <div class="statusTitle">申请未通过</div>
        <!-- <div class="tips">由于{{ this.result.project_apply_audit_reason }}原因，您提交的材料未通过请重新提交</div> -->
        <div class="rejectReason">
          <div class="label">原因：</div>
          <div class="rejectMsg">{{ result.project_apply_audit_reason }}，请修正后重新提交。</div>
        </div>
        <div class="btn" @click="anew">重新提交报名材料</div>
      </div>
      <div  class="audit" v-if="this.result.project_apply_audit_status == 1">
        <p>报名材料已提交成功</p>
        <p> 审核中...</p>
        <div class="btn" @click="backHome()">返回</div>
      </div>
    </div>
    <van-action-sheet 
        style="padding-bottom: 30px;"
        v-model="show"
        @select="select"
        :actions="actions"
        cancel-text="取消"
        description="请选择分级"
        close-on-click-action />
  </div>
</template>

<script>
import { getProjectResult } from '@/api/common/projectMain.js'
export default {
  data(){
    return {
      result: '',
      show: false,
      actions:[]
    }
  },
  methods: {
    getResult() {
      const project_id = this.$route.query.project_id
      getProjectResult(project_id).then((res) => {
        if (res && res.code == 200) {
          this.result = res.data
        }
      })
    },
    select(item) {
       this.openUrl(1, item.link_text, 1)
    },
    anew() {
      if (this.result.project_apply_link_data && this.result.project_apply_link_data.children && this.result.project_apply_link_data.children.length) {
        this.show = true
        this.actions = this.result.project_apply_link_data.children
      } else {
        const gotoApply = decodeURIComponent(this.$route.query.gotoApply || '');
        this.openUrl(1, gotoApply, 1)
      }
      
    },
    openUrl(type, url, close) {
      const UA = navigator.userAgent
      const isAndroid = UA.indexOf('Android') > -1 || UA.indexOf('Adr') > -1 // android终端
      const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
      let param = JSON.stringify({
        type,
        url,
        close
      })
      if (isAndroid) {
        console.log('安卓')
        window.android.openOtherPage(type, url, close)
      } else if (isIOS) {
        window.webkit.messageHandlers.openOtherPage.postMessage(param)
      }
    },
    goBackUrl() {
      const UA = navigator.userAgent
      const isAndroid = UA.indexOf('Android') > -1 || UA.indexOf('Adr') > -1 // android终端
      const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
      if (isAndroid) {
        console.log('安卓')
        window.android.backGoHomePage()
      } else if (isIOS) {
        window.webkit.messageHandlers.backGoHomePage.postMessage('')
      }
    },
    backHome() {
      this.goBackUrl()
    },
  },
  created() {
    this.getResult()
  },

  
}
</script>

<style scoped lang="scss">
.registerStatus{
  width: 100%;
  height: 100vh;
  padding: 15px;
  box-sizing: border-box;
  background: #F5F5F5;
  .main{
    width: 100%;
    height: 100%;
    background: #fff;
    border-radius: 12px;
    box-sizing: border-box;
    padding-top: 80px;
    position: relative;
    .statusTitle{
      font-size: 20px;
      font-weight: 400;
      margin-top: 20px;
    }
    .tips{
      margin-top: 16px;
      color: #5A6266;
      font-size: 16px;
      line-height: 20px;
      padding: 0 45px;
      overflow: auto;
      max-height: 300px;
    }
    .rejectReason{
      width: 256px;
      color: #F7830D;
      font-size: 16px;
      margin: 16px auto 0;
      display: flex;
      line-height: 24px;
      .label{
        font-weight: 500;
        min-width: 55px;
      }
      .rejectMsg{
        text-align: left;
      }
    }
    .btn{
      color:#FC671A;
      margin: 0 auto;
      width: calc(100% - 30px);
      height: 52px;
      text-align: center;
      line-height: 52px;
      font-size: 17px;
      font-weight: 500;
      border: 1px solid #FC671A;
      border-radius: 8px;
      position: absolute;
      bottom: 22px;
      left: 50%;
      transform: translate(-50%,0);
    }
  }
  .audit{
    margin-top: 20px;
    color: #000;
    text-align: center;
    font-size: 20px;
    font-style: normal;
  }
}
</style>