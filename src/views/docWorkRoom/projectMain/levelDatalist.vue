<template>
   <div class="listOuter">
    <NavBar v-if="!isMobile()" color="#333333"></NavBar>
      <van-loading v-if="loading" style="padding-top: 200px;"/>
      <commonList :docList = this.docList></commonList>
    </div>
</template>

<script>
import commonList from './components/commonList.vue'
import { isMobile } from '@/utils/utils.js'
import NavBar from './components/navBar.vue'
export default {
  components: {
    commonList,
    NavBar
  },
  data() {
    return {
      docList: [],
      loading:false
    }
  },
  
  methods: {
    isMobile,
    findItemsByTierId(data, targetTierId) {
      const searchItems = (items, tierId, result = []) => {
        for (const item of items) {
          if (item.tierid == tierId) {
            result.push(item);
          }
          if (item.children && item.children.length > 0) {
            searchItems(item.children, tierId, result);
          }
        }
        return result;
      };
      return searchItems(data, targetTierId);
    }
  },
  created() {
    this.loading = true
    const patId = this.$route.params.id
    const itemId = this.$route.query.itemId
    const maxData = JSON.parse(localStorage.getItem('nextChildren'))
    const getData = this.findItemsByTierId(maxData.children, patId) || []
    const params = getData.reduce((pre, cur) => {
      if (itemId == cur.id) {
        pre.push(cur)
      }
      return pre
    }, [])
    this.loading = false
    this.docList = params
    
  },
  mounted() {
    document.title = this.docList[0].name
  }
}
</script>

<style scoped lang="scss">
.listOuter{
  padding: 16px 15px 120px;
  box-sizing: border-box;
  background: #F5F5F5;
  min-height: 100vh;
}
</style>