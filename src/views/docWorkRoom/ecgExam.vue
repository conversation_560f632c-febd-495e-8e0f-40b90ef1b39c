<template>
  <div class="content">
    <van-tabs v-model="active">
      <van-tab name="1" title="检查记录">
        <van-list
          v-model="loading"
          :finished="finished"
          :immediate-check="false"
          finished-text="没有更多了"
          @load="getBreatheListsFun"
          offset="300"
        >
          <div v-for="(item, index) in list" :key="item.id">
            <div class="title cursor" @click="getEcgDetailFun(index, item)">
              <div :class="[{'active-bold':item.extend==true}]" class="left" v-if=" item.measure_at"> {{measureAtChangeFunc(item.measure_at)[0]}}</div>
              <div v-if=" item.measure_at" class="right unit">
                {{measureAtChangeFunc(item.measure_at)[1]}}
                <van-icon class="unit" v-if="item.extend" name="arrow-up" />
                <van-icon class="unit" v-if="!item.extend" name="arrow-down" />
              </div>
            </div>
            <div v-if="item.extend" class="result">
              <div class="flex">
                <div class="word">心率</div>
                <div class="word-sub">
                  <abnormalIndicator
                    code="hr"
                    :record="item"
                    :codeValue="item.hr"
                  />
                  <span class="unit">bpm</span>
                </div>
              </div>
              <div class="flex"> 
                <div  class="word">P-R</div>
                <div  class="word-sub">
                  <abnormalIndicator
                    code="pr"
                    :record="item"
                    :codeValue="item.pr"
                  />
                  <span class="unit">ms</span>
                </div>
              </div>
              <div class="flex">
                <div  class="word">QT/QTc</div>
                <div  class="word-sub">
                  <abnormalIndicator
                    code="qt"
                    :record="item"
                    :codeValue="item.qt"
                  />
                  /
                  <abnormalIndicator
                    code="qtc"
                    :record="item"
                    :codeValue="item.qtc"
                  />
                  <span class="unit">ms</span>
                </div>
              </div>
              <div class="flex">
                <div  class="word">QRS</div>
                <div  class="word-sub">
                  <abnormalIndicator
                    code="qrs"
                    :record="item"
                    :codeValue="item.qrs"
                  />
                  <span class="unit">ms</span>
                </div>
              </div>
              <!--报告-->
                <report-item 
                    v-if="item.file_paths && item.file_paths.length > 0"
                    :fileList="item.file_paths" 
                    pageType="ecg"
                    pageName="心电图"
                    >
                </report-item>
            </div>
          </div>
        </van-list>
      </van-tab>
      <van-tab name="2" title="心率分布图">
        <!-- <div class="setflex">
         <div class="date-class" @click="handleDateChange(item.id)" :class="[{active: activeDate == item.id} , {ml: item.class=='mrleft'} ,  {mr: item.class=='mrright'}]" v-for="(item , index) in dateList" :key="item.id">{{ item.name }}</div>
        </div> -->

        <div class="result-chart">
          <div class="result-title">结果统计</div>
          <div v-if="!isWeixin" @click="handleAllScreen" class="result-allscreen">
            <img class="mobile-png" src="./imgs/mobile.png" alt="">
            全屏
          </div>
        </div>


        <!-- <div @click="handleAllScreen">全屏</div> -->
        <ecgChart :range_date="activeDate" ></ecgChart>


        <!-- <div @click="handleAllScreen" style="width: 100px; height:100px; background: red">全屏</div> -->
      </van-tab>
    </van-tabs>
  </div>
</template>

<script>
import ecgChart from './ecgChart.vue'
import { getEcgLists } from '@/api/saas.js'
const UA = navigator.userAgent;
const isAndroid = UA.indexOf("Android") > -1 || UA.indexOf("Adr") > -1; // android终端
const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); // ios终端
const isWeixin = UA.indexOf("micromessenger") !== -1 || UA.indexOf("MicroMessenger") !== -1;    // 微信

import reportItem from './sickerMedicalRecord/components/reportItem.vue'
import abnormalIndicator from './sickerMedicalRecord/components/abnormalIndicator.vue';
export default {
  components: {
    ecgChart,
    reportItem,
    abnormalIndicator
  },
  data() {
    return {
      active: "1", // 1 检查记录 还是 2 心率分布图 
      // dateList:{
      //   1:'近90天',
      //   2:'近半年',
      //   3:'近一年',
      //   4:'近三年'
      // },
      dateList: [{
        id: '1' ,
        name:'近90天',
        class: 'mrleft'
      },{
        id: '2' ,
        name:'近半年',
        class: ''
      },{
        id: '3' ,
        name:'近一年',
        class: ''
      },{
        id: '4' ,
        name:'近三年',
        class: 'mrright'
      }],
      activeDate:0, //默认0 为全部
      list: [],
      loading: false,
      finished: true,
      isWeixin: isWeixin
    };
  },
  computed: {
      searchInfo() {
          return this.$store.getters['docWorkRoom/searchInfo']
      },
  },
  methods: {
    handleDateChange(id){
      if(id == this.activeDate){
        this.activeDate = 0
      }else{
        this.activeDate = id
      }


    },
    async getListFunc() {
      console.log(this.$route.query)
      let {patient_id} = this.$route.query;

      let {data} =  await getEcgLists({
        patient_id, 
        //...this.searchInfo
        })
      this.list = data;
      this.list.forEach(i=>{
        this.$set(i , 'extend' , false)
      })
      console.log('%c [ this.list ]-124', 'font-size:13px; background:pink; color:#bf2c9f;', this.list)
  
    },

    measureAtChangeFunc(time){
      return time.split(' ')

    },

    getEcgDetailFun(index, item) {
      this.list[index].extend = !this.list[index].extend;
    },
    handleAllScreen(){
      // this.$toast('全屏方法')
            let patient_id = this.$route.query.patient_id;
      let param = `/docWorkRoom/ecgChartScreen?id=${1}&patient_id=${patient_id}`;
      if (process.env.NODE_ENV === "production") {
        if (isAndroid) {
         window.android.showLandScape(param);
        }
        if (isIOS) {
          window.webkit.messageHandlers.showLandScape.postMessage(param);
        }
      }
      if (process.env.NODE_ENV === "development") {
        this.$router.push({
          path: "/docWorkRoom/ecgChartScreen",
          query: {
            id: 1,
            patient_id: this.$route.query.patient_id,
          },
        });
      }
    }
  },
  mounted() {
    this.getListFunc();
  },
};
</script>

<style lang="scss" scoped>
.content {
  font-size: 14px;

  .title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 16px;
    padding: 16px 16px;
    border-bottom: 1px solid #F1F1F1;


  }

  .right{
    font-size: 14px;
  }

  .result{
    font-size: 12px;

  }
  .flex{
    display: flex;
    justify-content: space-between;
        padding: 14px 32px;
    border-bottom: 1px solid #F1F1F1 ;
    background: #FBFBFB;

    .word{
      font-size: 16px;
    }

    .word-sub{
      font-size: 14px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  .unit{
    color: #909399;
    margin-left: 4px;
  }
}
.cursor{
  cursor: pointer;
}


.setflex{
  display: flex;
  justify-content: space-between;
  margin: 20px 0;

}

::v-deep  .van-tab__text{
  font-size: 16px;
}

.result{
  // display: flex;
  // justify-content: space-between;


}
.date-class{
  color: #999;
  padding: 4px 12px;
  border: 1px solid #999;
  border-radius: 4px;
}

.ml{
  margin-left: 16px;
}
.mr{
  margin-right: 16px;
}

.result-chart{
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
  .result-title{
    margin-left: 16px;
  }
  .result-allscreen{
    margin-right: 16px;

  }
}

.active{
  border: 1px solid #FAAD14;
  color: #FAAD14;
  background: #FCF6EC;
}

.active-bold{
  font-weight: bold;
}
.mobile-png{
  width: 14px;
  height: 14px;
}
</style>