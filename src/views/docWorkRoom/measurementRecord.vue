<template>
  <div class="measurementRecord">
    <div class="topTabs">
      <van-tabs class="tabs" :active="tabActive" title-inactive-color="#84909A" title-active-color="#0A0A0A" :line-width="34" background="#FFFFFF" @change="tabChange">
        <van-tab v-for="item in tabArr" :name="item.id" :key="item.id" :title="item.val"></van-tab>
      </van-tabs>
      <div class="subTabs">
        <div class="leftTabs">
          <div class="subTabItem" v-for="item in cloneSubTabs" :key="item.id" :class="[item.id == subTabActive ? 'subTabActive' : '']" @click="subTabClick(item)">{{ item.val }}</div>
        </div>
        <van-icon v-if="['bmi','bp','bg','pef','oxygen_saturation','wheezing_sound','bloodfat','whr','body_tw'].indexOf(tabActive) != -1" @click="showNormalPop" name="question-o" />
      </div>
    </div>
    <div :class="`main ${tabActive}`">
      <template v-if="tabActive == 'bp'">
        <BpRecordPage v-if="subTabActive == 0"></BpRecordPage>
        <BpTrendReport v-if="subTabActive == 1"></BpTrendReport>
      </template>
      <template v-else-if="tabActive == 'bg'">
        <BloodglucoseRecord v-if="subTabActive == 0"></BloodglucoseRecord>
        <BloodglucoseTrend v-if="subTabActive == 1"></BloodglucoseTrend>
        <BgTable v-if="subTabActive == 2"></BgTable>
      </template>
      <template v-else-if="tabActive == 'am'">
        <NebulizedMedicationRecord v-if="subTabActive == 0"></NebulizedMedicationRecord>
        <NebulizedMedicationTrend v-if="subTabActive == 1"></NebulizedMedicationTrend>
      </template>
      <template v-else-if="tabActive == 'wheezing_sound'">
        <WheezingSound v-if="subTabActive == 0"></WheezingSound>
        <WheezingTrend v-if="subTabActive == 1"></WheezingTrend>
      </template>
      <template v-else-if="tabActive == 'bloodfat'">
        <BloodFatRecord v-if="subTabActive == 0"></BloodFatRecord>
        <BloodFatTrend v-if="subTabActive == 1"></BloodFatTrend>
      </template>
      <template v-else-if="tabActive == 'step'">
        <div v-if="subTabActive == 0 && source == 'mini' && isStepAuthBlockShow" class="stepAuthBlock" @click="goMiniAuth">
          <van-notice-bar mode="link">授权/同步数据</van-notice-bar>
        </div>
        <StepRecord v-if="subTabActive == 0"></StepRecord>
        <StepTrend v-if="subTabActive == 1"></StepTrend>
      </template>
      <template v-else-if="tabActive == 'bmi'">
        <BodyFatIndexRecord  v-if="subTabActive == 0"></BodyFatIndexRecord>
        <BodyFatIndexTrend  v-if="subTabActive == 1"></BodyFatIndexTrend>
      </template>
      <template v-else-if="tabActive == 'pef'">
        <PeakFlowRateRecord v-if="subTabActive == 0"></PeakFlowRateRecord>
        <PeakFlowRateTrend v-if="subTabActive == 1"></PeakFlowRateTrend>
      </template>
      <template v-else-if="tabActive == 'oxygen_saturation'">
        <BloodOxygenRecord v-if="subTabActive == 0"></BloodOxygenRecord>
        <BloodOxygenTrend v-if="subTabActive == 1"></BloodOxygenTrend>
      </template>
      <template v-else-if="tabActive == 'cgm'">
        <CgmBs v-if="subTabActive == 1" entry="record"></CgmBs>
      </template>
      <template v-else-if="tabActive == 'whr'">
        <whr v-if="subTabActive == 0"></whr>
      </template>
      <template v-else-if="tabActive == 'body_tw'">
        <tw v-if="subTabActive == 0"></tw>
      </template>
    </div>
    <van-overlay z-index="999999999" :show="isNormalPopShow" @click="isNormalPopShow = false" :lock-scroll="false">
      <div @click.stop>
        <NormalRange ref="normalPop" @click.stop :keyword="tabActive"></NormalRange>
      </div>
    </van-overlay>
  </div>
</template>

<script>
import wx from 'weixin-js-sdk'
import NormalRange from './houseHoldData/normalRange.vue'
import BpRecordPage from './houseHoldData/bpRecordPage'
import BpTrendReport from './houseHoldData/bpTrendReport.vue'
// 血糖
import BloodglucoseRecord from './houseHoldData/bloodglucoseRecord.vue'
import BloodglucoseTrend from './houseHoldData/bloodglucoseTrend.vue'
import BgTable from './houseHoldData/bgTable.vue'
// BMI
import BodyFatIndexRecord from './houseHoldData/bodyFatIndexRecord.vue'
import BodyFatIndexTrend from './houseHoldData/bodyFatIndexTrend.vue'
// 血氧
import BloodOxygenRecord from './houseHoldData/bloodgOxygenSaturationRecord.vue'
import BloodOxygenTrend from './houseHoldData/bloodgOxygenSaturationTrend.vue'
//流速峰值
import PeakFlowRateRecord from './houseHoldData/peakFlowRateRecord.vue'
import PeakFlowRateTrend from './houseHoldData/peakFlowRateTrend.vue'
// 哮鸣音
import WheezingSound from './houseHoldData/wheezingSound.vue'
import WheezingTrend from './houseHoldData/wheezingTrend.vue'
// 步数
import StepRecord from './houseHoldData/stepsRecord.vue'
import StepTrend from './houseHoldData/stepsTrend.vue'
// 雾化用药
import NebulizedMedicationRecord from './houseHoldData/nebulizedMedicationRecord.vue'
import NebulizedMedicationTrend from './houseHoldData/nebulizedMedicationTrend.vue'
// 血脂
import BloodFatRecord from './houseHoldData/bloodLipidRecord.vue'
import BloodFatTrend from './houseHoldData/bloodLipidRecord.vue'
// 动态血糖
import CgmBs from './sickerMedicalRecord/cgmBs/index.vue'
// 腰臀比
import whr from  './houseHoldData/whr.vue'
// 温度
import tw from './houseHoldData/tw.vue'
export default {
  components: {
    BpRecordPage,
    BpTrendReport,
    BloodglucoseRecord,
    BloodglucoseTrend,
    BgTable,
    BodyFatIndexRecord,
    BodyFatIndexTrend,
    BloodOxygenRecord,
    BloodOxygenTrend,
    PeakFlowRateRecord,
    PeakFlowRateTrend,
    WheezingSound,
    WheezingTrend,
    StepRecord,
    StepTrend,
    NebulizedMedicationRecord,
    NebulizedMedicationTrend,
    BloodFatRecord,
    BloodFatTrend,
    CgmBs,
    NormalRange,
    whr,
    tw
  },
  created(){
  },
  data(){
    return {
      isNormalPopShow: false,
      tabArr: [
        {
          id: 'bp',
          val: '血压'
        },
        {
          id: 'bg',
          val: '血糖'
        },
        {
          id: 'am',
          val: '雾化用药'
        },
        {
          id: 'wheezing_sound',
          val: '哮鸣音'
        },
        {
          id: 'bloodfat',
          val: '血脂'
        },
        {
          id: 'step',
          val: '步数'
        },
        {
          id: 'body_tw',
          val: '体温'
        },
        {
          id: 'bmi',
          val: 'BMI'
        },
        {
          id: 'pef',
          val: '流速峰值'
        },
        {
          id: 'oxygen_saturation',
          val: '血氧饱和度'
        },
        {
          id: 'whr',
          val: '腰围臀围'
        },
        {
          id: 'cgm',
          val: '动态血糖'
        },
      ],
      tabActive: 'bp',
      subTabActive: 0,
      subTabs: [
        {
          id: 0,
          val: '明细记录'
        },
        {
          id: 1,
          val: '趋势报告'
        }
      ],
      cloneSubTabs: [],
      source: '',
      isStepAuthBlockShow: false
    }
  },
  methods: {
    goMiniAuth(){
      wx.miniProgram.redirectTo({
        url: `/pages/home/<USER>/stepAuth/stepAuth`
      })
    },
    showNormalPop(){
      this.isNormalPopShow = true
      this.$nextTick(()=>{
        let descInfoBox = this.$refs.normalPop.$refs.descInfoBox
        descInfoBox.scrollTo({
          top: 0,
          left: 0,
          // behavior: 'smooth'
        })
      })
    },
    init(){
      let query = this.$route.query
      this.tabActive = query.active || 'bp'
      let source = query.source
      this.source = query.source
      this.handleTabs(source)
      this.handleSubTabs()
    },
    handleTabs(source){
      // 医生工作室没有血脂
      if(source == 'docApp'){
        let index = this.tabArr.findIndex(item=> item.id == 'bloodfat')
        this.tabArr.splice(index,1)
      } else if (source == 'mini') {
        let index = this.tabArr.findIndex(item=> item.id == 'cgm')
        this.tabArr.splice(index,1)
      }
    },
    handleSubTabs(){
      let temp = JSON.parse(JSON.stringify(this.subTabs))
      if(this.tabActive == 'bg'){
        let obj = {
          id: 2,
          val: '血糖表格'
        }
        temp.push(obj)
      }
      if(this.tabActive == 'bloodfat' || this.tabActive == 'am' || this.tabActive == 'whr' || this.tabActive == 'whr' || this.tabActive == 'body_tw'){
        temp.splice(1,1)
      } else if (this.tabActive === 'cgm') {
        temp.splice(0, 1)
        this.subTabActive = 1
      }
      this.cloneSubTabs = temp
    },
    tabChange(name){
      this.tabActive = name
      this.handleSubTabs()
      this.$nextTick(() => {
        window.scrollTo({
          top: 0,
          behavior: "smooth"
        })
      })
      this.subTabActive = 0
      if (this.tabActive === 'cgm') {
        this.subTabActive = 1
      }
      console.log(this.tabActive)
    },
    subTabClick(item){
      console.log(item)
      this.subTabActive = item.id
      this.$nextTick(() => {
        window.scrollTo({
          top: 0,
          behavior: "smooth"
        })
      })
    }
  },
  mounted(){
    this.init();
  }
}
</script>

<style lang="scss">
.measurementRecord{
  width: 100%;
  min-height: 100vh;
  background: #F5F7FB;
  font-family: "PingFang SC";
  padding-top: 96px;
  .topTabs{
    position: fixed;
    top: 0;
    left: 0;
    z-index: 999999;
    width: 100%;
    .subTabs{
      height: 50px;
      background: white;
      display: flex;
      align-items: center;
      padding: 0 15px;
      width: 100%;
      box-sizing: border-box;
      justify-content: space-between;
      box-shadow: 0 10px 10px -12px #eeeeee;
      .leftTabs{
        display: flex;
        align-items: center;
      }
      .subTabItem{
        font-size: 15px;
        color: #666666;
        padding: 5px 12px;
        background: #EEEEEE;
        border-radius: 50px;
        margin-right: 16px;
      }
      .subTabActive{
        color: white;
        // background: #FF8E2D;
        background: #3796fa;
      }
    }
  }
  .main{
    padding: 12px 14px;
    &.cgm {
      padding: 0;
    }
  }
  .van-tab__text{
    font-size: 17px;
  }
  .van-tab--active{
    font-weight: 500;
  }
  .van-tabs__line {
    position: absolute;
    bottom: 18px;
  }
  .stepAuthBlock{
    margin: -12px -14px 6px;
    .van-notice-bar{
      font-size: 16px !important;
    }
    
  }
}

</style>