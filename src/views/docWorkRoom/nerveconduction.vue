<template>
    <div class="content">
        <van-list
            v-model="loading"
            :finished="finished"
            :immediate-check="false"
            finished-text="没有更多了"
            @load="getVfListsFun"
            offset="300"
        >
            <div
                v-for="(item, index) in list"
                :key="item.id"
            >
                <div class="title" @click="getVfDetailFun(index, item.id)">
                  <div :class="[{'active-bold':item.extend==true}]" class="left" v-if="item.measure_at"> {{measureAtChangeFunc(item.measure_at)[0]}}</div>
                    <div class="right">
                        {{ item.measure_at }}
                        <van-icon v-if="item.extend" name="arrow-up"></van-icon>
                        <van-icon v-if="!item.extend" name="arrow-down"></van-icon>
                    </div>
                </div>
                <div v-if="item.extend">
                  <div class="title-name">1.运动神经传导</div>
                  <table class="table">
                    <tr>
                      <td rowspan="2">运动传导速度</td><td>潜伏期</td><td>波幅</td><td>距离</td><td>传导速度</td><td>平均F-M潜伏期</td>
                    </tr>
                    <tr>
                      <td>ms</td><td>mv</td><td>mm</td><td>m/s</td><td>ms</td>
                    </tr>
                    <tr><td>尺神经运动左</td><td class="grey"></td><td class="grey"></td><td class="grey"></td><td class="grey"></td><td class="grey"></td></tr>
                    <tr>
                      <td>腕-ADM</td>
                      <td>{{item.lradmla || '--'}}</td><td>{{item.lbbolfam || '--'}}</td><td>{{item.lradmla_d || '--'}}</td><td>{{item.lradmla_s || '--'}}</td><td>{{item.lradmla_fm || '--'}}</td>
                    </tr>
                    <tr>
                      <td>肘下-腕</td>
                      <td>{{item.lradmlb || '--'}}</td><td>{{item.lradmf || '--'}}</td><td>{{item.lradmd || '--'}}</td><td>{{item.lsepam || '--'}}</td><td>{{item.lradmlb_fm || '--'}}</td>
                    </tr>

                    <tr><td>尺神经运动右</td><td class="grey"></td><td class="grey"></td><td class="grey"></td><td class="grey"></td><td class="grey"></td></tr>
                    <tr>
                      <td>腕-ADM</td>
                      <td>{{item.rradmla || '--'}}</td><td>{{item.rbbolfam || '--'}}</td><td>{{item.rradmla_d || '--'}}</td><td>{{item.rradmla_s || '--'}}</td><td>{{item.rradmla_fm || '--'}}</td>
                    </tr>
                    <tr>
                      <td>肘下-腕</td>
                      <td>{{item.rradmlb || '--'}}</td><td>{{item.rradmf || '--'}}</td><td>{{item.rradmd || '--'}}</td><td>{{item.rsepam || '--'}}</td><td>{{item.rradmlb_fm || '--'}}</td>
                    </tr>

                    <tr><td>正中神经运动左</td><td class="grey"></td><td class="grey"></td><td class="grey"></td><td class="grey"></td><td class="grey"></td></tr>
                    <tr>
                      <td>腕-APB</td>
                      <td>{{item.lbwapbla || '--'}}</td><td>{{item.lbwapb || '--'}}</td><td>{{item.lbwapbla_d || '--'}}</td><td>{{item.lbwapbla_s || '--'}}</td><td>{{item.lbwapbla_fm || '--'}}</td>
                    </tr>
                    <tr>
                      <td>肘-腕</td>
                      <td>{{item.lsafapbla || '--'}}</td><td>{{item.lsafapbm || '--'}}</td><td>{{item.lsafapbd || '--'}}</td><td>{{item.lsafapb || '--'}}</td><td>{{item.lsafapbla_fm || '--'}}</td>
                    </tr>

                    <tr><td>正中神经运动右</td><td class="grey"></td><td class="grey"></td><td class="grey"></td><td class="grey"></td><td class="grey"></td></tr>
                    <tr>
                      <td>腕-APB</td>
                      <td>{{item.rbwapbla || '--'}}</td><td>{{item.rbwapb || '--'}}</td><td>{{item.rbwapbla_d || '--'}}</td><td>{{item.rbwapbla_s || '--'}}</td><td>{{item.rbwapbla_fm || '--'}}</td>
                    </tr>
                    <tr>
                      <td>肘-腕</td>
                      <td>{{item.rsafapbla || '--'}}</td><td>{{item.rsafapbm || '--'}}</td><td>{{item.rsafapbd || '--'}}</td><td>{{item.rsafapb || '--'}}</td><td>{{item.rsafapbla_fm || '--'}}</td>
                    </tr>

                    <tr><td>胫神经运动左</td><td class="grey"></td><td class="grey"></td><td class="grey"></td><td class="grey"></td><td class="grey"></td></tr>
                    <tr>
                      <td>踝-AH</td>
                      <td>{{item.lbaahla || '--'}}</td><td>{{item.lbaah || '--'}}</td><td>{{item.lbaahla_d || '--'}}</td><td>{{item.lbaahla_s || '--'}}</td><td>{{item.lbaahla_fm || '--'}}</td>
                    </tr>
                    <tr>
                      <td>腘窝-踝</td>
                      <td>{{item.lspfahla || '--'}}</td><td>{{item.lspfahf || '--'}}</td><td>{{item.lspfahd || '--'}}</td><td>{{item.lspfah || '--'}}</td><td>{{item.lspfahla_fm || '--'}}</td>
                    </tr>

                    <tr><td>胫神经运动右</td><td class="grey"></td><td class="grey"></td><td class="grey"></td><td class="grey"></td><td class="grey"></td></tr>
                    <tr>
                      <td>踝-AH</td>
                      <td>{{item.rbaahla || '--'}}</td><td>{{item.rbaah || '--'}}</td><td>{{item.rbaahla_d || '--'}}</td><td>{{item.rbaahla_s || '--'}}</td><td>{{item.rbaahla_fm || '--'}}</td>
                    </tr>
                    <tr>
                      <td>腘窝-踝</td>
                      <td>{{item.rspfahla || '--'}}</td><td>{{item.rspfahf || '--'}}</td><td>{{item.rspfahd || '--'}}</td><td>{{item.rspfah || '--'}}</td><td>{{item.rspfahla_fm || '--'}}</td>
                    </tr>

                  </table>
                  <div class="title-name">2.感觉神经传导</div>
                  <table class="table">
                    <tbody>
                      <tr>
                        <td rowspan="2">感觉传导速度</td><td>潜伏期</td><td>波幅</td><td>距离</td><td>传导速度</td>
                      </tr>
                      <tr>
                        <td>ms</td><td>mv</td><td>mm</td><td>m/s</td>
                      </tr>
                      <tr><td>正中神经感觉左</td><td class="grey"></td><td class="grey"></td><td class="grey"></td><td class="grey"></td></tr>
                      <tr>
                        <td>指Ⅱ-腕</td>
                        <td>{{item.lbrttilm || '--'}}</td><td>{{item.lbrttib || '--'}}</td><td>{{item.lsrttilm || '--'}}</td><td>{{item.lsrttib || '--'}}</td>
                      </tr>

                      <tr><td>正中神经感觉右</td><td class="grey"></td><td class="grey"></td><td class="grey"></td><td class="grey"></td></tr>
                      <tr>
                        <td>指Ⅱ-腕</td>
                        <td>{{item.rbrttilm || '--'}}</td><td>{{item.rbrttib || '--'}}</td><td>{{item.rsrttilm || '--'}}</td><td>{{item.rsrttib || '--'}}</td>
                      </tr>

                      <tr><td>腓肠神经感觉左</td><td class="grey"></td><td class="grey"></td><td class="grey"></td><td class="grey"></td></tr>
                      <tr>
                        <td>小腿中-外踝</td>
                        <td>{{item.lbtaitmolm || '--'}}</td><td>{{item.lbtaitmota || '--'}}</td><td>{{item.lstaitmolm || '--'}}</td><td>{{item.lstaitmota || '--'}}</td>
                      </tr>

                      <tr><td>腓肠神经感觉右</td><td class="grey"></td><td class="grey"></td><td class="grey"></td><td class="grey"></td></tr>
                      <tr>
                        <td>小腿中-外踝</td>
                        <td>{{item.rbtaitmolm || '--'}}</td><td>{{item.rbtaitmota || '--'}}</td><td>{{item.rstaitmolm || '--'}}</td><td>{{item.rstaitmota || '--'}}</td>
                      </tr>

                    </tbody>
                  </table>
                  <div class="title-name">3.最后结论</div>
                  <van-field
                    v-model="item.interpretation"
                    type="textarea"
                    autosize
                    rows="4"
                    readonly
                  />
                  <!--报告-->
                  <report-item 
                      v-if="item.file_paths && item.file_paths.length > 0"
                      :fileList="item.file_paths" 
                      pageName="神经传导"
                      pageType="nerveconduction">
                  </report-item>
                </div>

            </div>
        </van-list>


    </div>
</template>

<script>
import reportItem from './sickerMedicalRecord/components/reportItem.vue'

import { getDpnList, getDpnDetail } from '@/api/saas.js'

export default {
  components: {
        reportItem
    },
    data () {
        return {
            list: [],
            total: 0,
            page: 1,
            limit: 20,
            loading: false,
            finished: false,
        }
    },
    computed: {
      searchInfo() {
          return this.$store.getters['docWorkRoom/searchInfo']
      },
    },
    created () {
        this.getVfListsFun()
    },
    methods: {
        // 获取内脏脂肪列表
        async getVfListsFun () {
            if (this.list.length < this.total) {
                this.page += 1
            }
            this.loading = true
            let res = await getDpnList({
                patient_id: this.$route.query.patient_id,
                limit: 20,
                page: 1,
                //...this.searchInfo
            })
            if (res.status == 200) {
                this.total = res.data.total
                let temp = res.data.data.map(item => {
                    item.extend = false
                    return item
                })
                for (let i = 0; i < temp.length; i++) {
                    this.list.push(temp[i])
                }
                this.loading = false
                if (this.list.length >= this.total) {
                    this.finished = true
                }
            } else {
                this.loading = false
                this.$toast(res.msg)
            }
        },
        // 获取内脏脂肪详情
        async getVfDetailFun (index, id) {
            this.list[index].extend = !this.list[index].extend
            if (!this.list[index].extend || this.list[index].data && Object.keys(this.list[index].data).length > 0) {
                return
            }
            this.loading = true
            let res = await getDpnDetail({
                id: id,
                patient_id: this.$route.query.patient_id
            })
            this.loading = false
            if (res.status == 200) {
              this.$set(this.list, index, { ...res.data, ...this.list[index] })
            } else {
                this.$toast(res.msg)
            }
        },
      measureAtChangeFunc(time){
        return time.split(' ')

      },
    }
}
</script>

<style lang="scss" scoped>
.content {
    background: #ffffff;
    font-size: 14px;
    text-align: center;
    .title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #f1f1f1;
        padding: 15px 19px;
        .left {
            font-size: 16px;
            color: #333333;
        }
        .right {
            font-size: 12px;
            color: #666666;
            font-weight: 400;
            &:hover {
                cursor: pointer;
            }
        }
    }
  .title-name{
    font-size: 14px;
    text-align: left !important;
    padding: 16px 16px 12px;
  }
  .van-cell{
    padding: 0 0.5rem;
  }
  ::v-deep .van-field__value{
    border: 1px solid #f1f1f1 !important;
    min-height: 40px;
  }
  .table {
    width: 100%;
    text-align: center;
    border: 1px solid #ddd;
    color: #262626;

  thead {
    background-color: #f9f9f9;
    tr th {
      height: 30px;
      line-height: 30px;
      text-align: center;
      border: 1px solid #ddd;
    }
  }

    td {
      padding: 5px 10px;
      border: 1px solid #ddd;
      vertical-align: middle;
    }

    tbody {
      tr {
        &:nth-of-type(even) {
          background-color: #f9f9f9;
        }
        td {
          color: rgb(102, 102, 102);
          text-align: center;
          font-size: 12px;
          &:first-of-type {
            font-size: 14px;
          }
          &.hide-border-right {
            position: relative;
            background-color: #fff;
            &:after {
              position: absolute;
              content: '';
              right: -1px;
              top: 0;
              height: 29px;
              width: 1px;
              background-color: #fff;
            }
          }
        }
      }
    }
  }
  .grey {
    background: #D1D1D1 !important;
  }
  .active-bold{
    font-weight: bold;
  }
}
</style>
