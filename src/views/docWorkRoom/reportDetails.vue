<template>
  <div class="edit-conten">
    <van-field input-align="right" readonly v-model="pithy.name" label="项目名称" placeholder="请输入" />
    <van-field input-align="right" ref="vaninput" @click="getFocus" @input="inputMatch" v-model="pithy.value" label="结果浓度"
      placeholder="请输入" />
    <van-field input-align="right" @click="openSelect" readonly right-icon="arrow" v-model="pithy.unit" label="项目单位"
      placeholder="请选择" />
    <div class="foot-btn">
      <van-button class="resetting" @click="rest">重置</van-button>
      <van-button class="submit" @click="submit">保存</van-button>
    </div>
    <van-popup v-model="show" @close="close" round position="bottom" :style="{ height: '40%' }">
      <van-picker title="所属单位" show-toolbar :default-index="defaultIndex" :columns="pithy.unitOption" @confirm="onConfirm"
        @cancel="onCancel" @change="onChange" />
    </van-popup>
  </div>
</template>

<script>
export default {
  data() {
    return {
      show: false,
      unitOption: {},
      params: null,
      query: null,
      defaultIndex: '',
      workroom_id: '',
      record_id: '',
      user_id: "",
      localIndex:0,
      is_disable_unit: undefined
    }
  },

  created() {
    // this.workroom_id = this.$route.query.workroom_id
    // this.record_id = this.$route.query.record_id
    // this.user_id = this.$route.query.user_id
  },
  mounted() {
    this.workroom_id = this.$route.query.workroom_id
    this.record_id = this.$route.query.record_id
    this.user_id = this.$route.query.user_id
    this.is_disable_unit = this.$route.query.is_disable_unit
  },
  computed: {
    pithy() {
      return this.getPithyValue();
    }
  },
  methods: {
    //详情页传来的数据，元数据，未处理。
    getPithyValue() {
      this.query = this.$route.query;
      const localData = JSON.parse(localStorage.getItem('ocrData')) || []
      this.localIndex = localData.findIndex((item) => {
        return item.record_id == this.$route.query.record_id
      })
      console.log(this.localIndex)
      this.params = localData[this.localIndex].infoData
      return this.params.indicator_groups[this.query.index].fields[this.query.childIndex];
    },
    //后端返回来的验证规则，输入时验证
    inputMatch(val) {
      let valTest = val
      if (this.pithy.regex) {
        const reg = new RegExp(this.pithy.regex, 'g')
        valTest = val.match(reg)[0] || '';
      }
      this.pithy.value = valTest
    },
    getFocus() {
      this.$refs.vaninput.focus();
    },
    //打开select选择器。如有参数则回显到数据的index
    openSelect() {
      if (this.is_disable_unit == 1) {
        return
      } 
      this.show = true
      this.defaultIndex = this.pithy.unitOption.findIndex((item) => { return item.id === this.pithy.unit_id })
    },
    close() {
      this.defaultIndex = ''
    },
    onCancel() {
      this.show = false
    },
    //选择单位
    onConfirm(val) {
      if (val) {
        this.pithy.unit = val.name
        this.pithy.unit_id = val.id
      }
      this.show = false
    },
    submit() {
      if (this.pithy.is_required && !this.pithy.value) {
        this.$toast('请输入结果浓度')
        return
      }
      const local = JSON.parse(localStorage.getItem('ocrData')) || []
      local[this.localIndex].infoData = this.params
      localStorage.setItem('ocrData', JSON.stringify(local))
      this.$router.go(-1)
    },
    rest() {
      this.pithy.value = '';
      this.pithy.unit = '';
      this.pithy.unit_id = 0
    }
  }
}
</script>

<style lang="scss" scoped>
.edit-conten {
  height: 100vh;

  ::v-deep.van-cell {
    display: flex;
    align-items: center;
    height: 52px;
  }

  ::v-deep.van-cell__title {
    font-size: 17px;
    font-weight: 400;
    line-height: 24px;
    color: #5A6266;
  }

  ::v-deep.van-picker__confirm,
  ::v-deep.van-picker__cancel {
    color: #F7830D;
  }

  ::v-deep.van-field__right-icon {
    color: #ccc;
  }

  .foot-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    position: fixed;
    bottom: 0;
    left: 0;
    height: 100px;
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.25);
    width: 100%;
    background-color: #fff;

    .resetting,
    .submit {
      width: 162px;
      height: 44px;
      border-radius: 8px;
      font-size: 16px;
      font-weight: 500;
    }

    .submit {
      background-color: #F7830D;
      color: #fff;
      margin-left: 11px;
    }

    .resetting {
      background-color: #F5F6FB;
      color: #0A0A0A
    }
  }
}
</style>

