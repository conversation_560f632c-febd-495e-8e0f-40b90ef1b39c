<template>
    <div class="patient-report">
        
        <van-pull-refresh v-model="refreshing" @refresh="onRefresh" style="min-height: 100vh;">
            <van-list
                v-model="loading"
                :finished="finished"
                finished-text="没有更多了"
                @load="searchReportList"
                offset="300"
            >
                <div 
                    v-for="(item, index) in list"
                    :key="index"
                    @click="lookReportFun(item)"
                    class="row">
                    <div class="top">
                        <div class="left">
                            {{ item.hospital_name }}
                        </div>
                        <div class="right">
                            <van-icon name="arrow"/>
                        </div>
                    </div>
                    <div class="bottom">
                        <div class="left">
                            {{ item.department_name }}
                        </div>
                        <div class="right">
                            {{ item.created_at }}
                        </div>
                    </div>
                </div> 
            </van-list>
        </van-pull-refresh>
    </div>
</template>

<script>
import { getPatientReportApi } from '@/api/saas.js'
import { encode } from 'js-base64'
export default {
    data () {
        return {
            page: 1,
            page_size: 10,
            list: [],
            refreshing: false,
            loading: false,
            finished: false,
            total: 0
        }
    },
    created () {

    },
    methods: {
        onRefresh () {
            // 清空列表数据
            this.finished = false
            // 重新加载数据
            // 将loading 设置为true,表示处于加载状态
            this.loading = false 
            this.page = 1
            this.searchReportList('refresh')
        },
        // 查询报告列表
        async searchReportList (flag) {
            if (flag != 'refresh' && this.list.length < this.total) {
                this.page += 1
            }
            let res = await getPatientReportApi({
                patient_id: this.$route.query.patient_id,
                page: this.page,
                page_size: this.page_size,
                project_id: this.$route.query.project_id
            })
            this.total = res.data.total
            if (this.refreshing || this.page == 1) {
                this.list = []
                this.refreshing = false
            }
           
            for (let i = 0; i < res.data.list.length; i++) {
                this.list.push(res.data.list[i])
            }
            // this.list = this.list.concat(res.data.list)
            
            
            this.loading = false
            if (this.list.length >= this.total) {
                this.finished = true
            }
        },
        // 查看报告
        lookReportFun ({ report_path, report_png_path, id }) {
            if (!report_path) {
                this.$toast('暂无数据')
                return
            }
            const UA = navigator.userAgent
            const isAndroid = UA.indexOf('Android') > -1 || UA.indexOf('Adr') > -1 // android终端
            const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
            let param = `/docWorkRoom/patientReportDetail?report_record_id=${id}&pngUrl=${encode(report_png_path)}`
            if (process.env.NODE_ENV === 'production') {
                if (isAndroid) {
                    window.android.patientReportDetailMessage(param)
                }
                if (isIOS) {
                    window.webkit.messageHandlers.patientReportDetailMessage.postMessage(param)
                }
            }
            if (process.env.NODE_ENV === 'development') {
                this.$router.push({
                    path: '/docWorkRoom/patientReportDetail',
                    query: {
                        report_record_id: id,
                        pngUrl: encode(report_png_path)
                    }
                })
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.patient-report {
    // padding: 20px 16px 16px;
    .row {
        padding: 15px 16px;
        font-size: 12px;
        font-weight: 600;
        border-bottom: 1px solid #ebedf0;
        &:hover {
            cursor: pointer;
        }
        .top {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            .left {
                font-size: 14px;
                color: #333333;
                font-weight: 500;
            }
        }
        .bottom {
            display: flex;
            justify-content: space-between;
            .left {
                font-size: 12px;
                color: #212121;
            }
            .right {
                margin-right: 5px;
                font-size: 12px;
                color: #333333;
                display: flex;
                justify-content: flex-end;
                align-items: center;
            }
        }
    }
}
</style>
