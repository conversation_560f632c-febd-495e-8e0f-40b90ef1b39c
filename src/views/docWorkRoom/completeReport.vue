<!--
 * @Descripttion: 
 * @version: 
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-16 14:37:21
 * @LastEditors: liu<PERSON><PERSON>n
 * @LastEditTime: 2021-07-23 18:43:03
-->
<template>
    <div class="wrapper" ref="wrapper">
        <div
            v-for="(item, index) in imgs"
            :key="index"
            class="item"
            @click="showBigImgFun(index, item)"
            :style="{backgroundImage: 'url(' + item + ')'}"
        >
            <!--:style="{backgroundImage: 'url(' + item + ')'}"-->
            <!-- <img :src="item" alt=""> -->
        </div>
    </div>
</template>

<script>

import { reportImgFun } from '@/api/operationReport.js'
export default {
    data () {
        return {
            imgs: []
        }
    },
    created () {
        this.reportImgFun()
        var ios = navigator.userAgent.indexOf('iPhone'); // 判断是否为ios
        if (ios !== -1) { // ios下运行
            let el = document.querySelector('body'); // el -- 需要滑动的元素
            el.addEventListener('touchmove', function (e) {
                if (el.offsetHeight < el.scrollHeight)  e.isSCROLL = true;
            });
            document.body.addEventListener('touchmove', function (e) {
                if (!e.isSCROLL) e.preventDefault(); // 阻止默认事件(上下滑动)
            });
        }
    },
    methods: {
        // 查询完整报告
        reportImgFun () {
            reportImgFun({
                user_id: this.$route.query.user_id,
                id: this.$route.query.id
            }).then(res => {
                if (res.code === 200) {
                    this.imgs = res.data.imgs
                } else {
                    this.$toast(res.msg)
                }
            })
        },
        // 显示大图
        showBigImgFun (index, link) {
            const UA = navigator.userAgent
            const isAndroid = UA.indexOf('Android') > -1 || UA.indexOf('Adr') > -1 // android终端
            const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
            let param = JSON.stringify({
                index,
                imgs: this.imgs
            })
            // 原生app方法名称：showLargeImage
            if (isAndroid) {
                window.android.showLargeImage(param)
            } else if (isIOS) {
                window.webkit.messageHandlers.showLargeImage.postMessage(param)
            } else {
                Toast({
                    message: '无法放大图片',
                    type: 'html',
                    forbidClick: true,
                    duration: 1000
                })
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.wrapper {
    background: white;
    overflow-y: auto;
    &::-webkit-scrollbar {
        display: none;
    }
    // padding: 5px;
    height: 100vh;
    width: 100%;
    .item {
        width: 100%;
        height: 100%;
        background-size: 100% 100%;
        // img {
        //     width: 100%;
        //     height: 100%;
        // }
    }
}
</style>