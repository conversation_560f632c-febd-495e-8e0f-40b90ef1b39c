<!--
 * @Descripttion: PWV+ABI+TBI
 * @version:
 * @Author: l<PERSON>qiannan
 * @Date: 2021-07-27 14:50:26
 * @LastEditors: liu<PERSON><PERSON>n
 * @LastEditTime: 2021-08-18 19:51:10
-->
<template>
    <div class="content">
        <van-list
            v-model="loading"
            :finished="finished"
            :immediate-check="false"
            finished-text="没有更多了"
            @load="getAsListsFun"
            offset="300"
        >
            <div
                v-for="(item, index) in list"
                :key="item.id"
            >
                <div class="title" @click="getAsDetailFun(index, item.id)">
                    <div class="left">PWV+ABI</div>
                    <div class="right">
                        {{ item.measure_at }}
                        <van-icon v-if="item.extend" name="arrow-up"></van-icon>
                        <van-icon v-if="!item.extend" name="arrow-down"></van-icon>
                    </div>
                </div>
                <div v-if="item.extend">
                    <h3>主要参数</h3>
                    <table class="table">
                        <tr>
                            <td>项目</td>
                            <td>结果</td>
                            <td>项目</td>
                            <td>结果</td>
                        </tr>
                        <tr>
                            <td>左踝臂指数<br/>(LABI)</td>
                            <td>
                                <abnormalIndicator
                                    code="Labi"
                                    :record="item.data"
                                    :codeValue="item.data.Labi"
                                />
                            </td>
                            <td>右踝臂指数<br/>(RABI)</td>
                            <td>
                                <abnormalIndicator
                                    code="Rabi"
                                    :record="item.data"
                                    :codeValue="item.data.Rabi"
                                />
                            </td>
                        </tr>
                        <tr>
                            <td>左肱踝脉搏波传导速度<br/>(LBAPWV)</td>
                            <td>
                                <abnormalIndicator
                                    code="LbaPWV"
                                    :record="item.data"
                                    :codeValue="item.data.LbaPWV"
                                />
                            </td>
                            <td>右肱踝脉搏波传导速度<br/>(RBAPWV)</td>
                            <td>
                                <abnormalIndicator
                                    code="RbaPWV"
                                    :record="item.data"
                                    :codeValue="item.data.RbaPWV"
                                />
                            </td>
                        </tr>
                        <tr>
                            <td>左趾臂指数<br/>(LTBI)</td>
                            <td>
                                <abnormalIndicator
                                    code="Ltbi"
                                    :record="item.data"
                                    :codeValue="item.data.Ltbi"
                                />
                            </td>
                            <td>右趾臂指数<br/>(RTBI)</td>
                            <td>
                                <abnormalIndicator
                                    code="Rtbi"
                                    :record="item.data"
                                    :codeValue="item.data.Rtbi"
                                />
                            </td>
                        </tr>
                    </table>
                    <h3>次要参数</h3>
                    <table class="table">
                        <tr>
                            <td>项目</td>
                            <td>结果</td>
                            <td>项目</td>
                            <td>结果</td>
                        </tr>
                        <tr>
                            <td>PEP<br/>(射血前期)</td>
                            <td>
                                <abnormalIndicator
                                    code="PEP"
                                    :record="item.data"
                                    :codeValue="item.data.PEP"
                                />
                            </td>
                            <td>ET<br/>(射血时间ms)</td>
                            <td>
                                <abnormalIndicator
                                    code="ET"
                                    :record="item.data"
                                    :codeValue="item.data.ET"
                                />
                            </td>
                        </tr>
                        <tr>
                            <td>ET/PEP<br/>(射血指数)</td>
                            <!-- <td>{{ item.data.ET }}/{{ item.data.PEP }}</td> -->
                            <td>{{ item.data.PEP == 0 || !item.data.PEP ? '' : (item.data.ET / item.data.PEP).toFixed(2)  }}</td>
                            <td>HR<br/>(心率)</td>
                            <td>
                                <abnormalIndicator
                                    code="HR"
                                    :record="item.data"
                                    :codeValue="item.data.HR"
                                />
                            </td>
                        </tr>
                    </table>
                    <h3>其它参数</h3>
                    <table class="table">
                        <tr>
                            <td></td>
                            <td>Rb<br/>(右上臂)</td>
                            <td>Lb<br/>(左上臂)</td>
                            <td>Ra<br/>(右脚踝)</td>
                            <td>La<br/>(左脚踝)</td>
                        </tr>
                        <tr>
                            <td>haPWV<br/>(心踝脉搏波传导速度)</td>
                            <td>-</td>
                            <td>-</td>
                            <td>{{ item.data.RhaPWV }}</td>
                            <td>{{ item.data.LhaPWV }}</td>
                        </tr>
                        <tr>
                            <td>SYS<br/>(收缩压，四肢)</td>
                            <td>{{ item.data.RbSYS }}</td>
                            <td>{{ item.data.LbSYS }}</td>
                            <td>{{ item.data.RaSYS }}</td>
                            <td>{{ item.data.LaSYS }}</td>
                        </tr>
                        <tr>
                            <td>MAP<br/>(平均动脉压力，四肢)</td>
                            <td>{{ item.data.RbMAP }}</td>
                            <td>{{ item.data.LbMAP }}</td>
                            <td>{{ item.data.RaMAP }}</td>
                            <td>{{ item.data.LaMAP }}</td>
                        </tr>
                        <tr>
                            <td>DIA<br/>(舒张压，四肢)</td>
                            <td>{{ item.data.RbDIA }}</td>
                            <td>{{ item.data.LbDIA }}</td>
                            <td>{{ item.data.RaDIA }}</td>
                            <td>{{ item.data.LaDIA }}</td>
                        </tr>
                        <tr>
                            <td>PP<br/>(脉压差，四肢)</td>
                            <td>{{ (item.data.RbSYS - item.data.RbDIA).toFixed(1) }}</td>
                            <td>{{ (item.data.LbSYS - item.data.LbDIA).toFixed(1) }}</td>
                            <td>{{ (item.data.RaSYS - item.data.RaDIA).toFixed(1) }}</td>
                            <td>{{ (item.data.LaSYS - item.data.LaDIA).toFixed(1) }}</td>
                        </tr>
                        <tr>
                            <td>UT<br/>(脉波上行时间ms)</td>
                            <td>{{ item.data.RbUT }}</td>
                            <td>{{ item.data.LbUT  }}</td>
                            <td>{{ item.data.RaUT }}</td>
                            <td>{{ item.data.LaUT }}</td>
                        </tr>
                    </table>
                    <!--报告-->
                    <report-item 
                        v-if="item.data.file_paths && item.data.file_paths.length > 0"
                        :fileList="item.data.file_paths" 
                        pageName="PWV"
                        pageType="pwv">
                    </report-item>
                </div>
            </div>
        </van-list>
    </div>
</template>

<script>
import reportItem from './sickerMedicalRecord/components/reportItem.vue'
import { getAsLists, getAsDetailApi } from '@/api/saas.js'
import abnormalIndicator from './sickerMedicalRecord/components/abnormalIndicator.vue'
export default {
    components: {
        reportItem,
        abnormalIndicator
    },
    data () {
        return {
            list: [],
            total: 0,
            page: 1,
            limit: 20,
            loading: false,
            finished: false,
        }
    },
    computed: {
        searchInfo() {
            return this.$store.getters['docWorkRoom/searchInfo']
        },
    },
    created () {
        this.getAsListsFun()
    },
    methods: {
        // 获取pwv列表
        async getAsListsFun () {
            if (this.list.length < this.total) {
                this.page += 1
            }
            let res = await getAsLists({
                patient_id: this.$route.query.patient_id,
                limit: this.limit,
                page: this.page,
                // ...this.searchInfo
            })
            if (res.status == 200) {
                this.total = res.data.total
                let temp = res.data.data.map(item => {
                    item.extend = false
                    item.data = {}
                    return item
                })
                for (let i = 0; i < temp.length; i++) {
                    this.list.push(temp[i])
                }
                this.loading = false
                if (this.list.length >= this.total) {
                    this.finished = true
                }
            } else {
                this.$toast(res.msg)
            }
        },
        // 获取pwv详情
        async getAsDetailFun (index, id) {
            this.list[index].extend = !this.list[index].extend
            if (!this.list[index].extend || this.list[index].data && Object.keys(this.list[index].data).length > 0) {
                return
            }
            let res = await getAsDetailApi({
                id: id,
                patient_id: this.$route.query.patient_id
            })
            if (res.status == 200) {
                this.list[index].data = res.data
            } else {
                this.$toast(res.msg)
            }
        },
    }
}
</script>

<style lang="scss" scoped>
.content {
    background: #ffffff;
    font-size: 14px;
    // overflow-y: auto;
    // height: 100vh;
    .title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #f1f1f1;
        padding: 15px 19px;
        .left {
            font-size: 16px;
            color: #333333;
        }
        .right {
            font-size: 12px;
            color: #666666;
            font-weight: 400;
            &:hover {
                cursor: pointer;
            }
        }
    }
    h3 {
        // margin-bottom: 16px;
        // padding-left: 19px;
        padding: 10px 19px;
        text-align: left;
        color: #333;
        font-weight: 600;
    }
    .table {
        width: 100%;
        // margin-bottom: 20px;
        border: 1px solid #F1F1F1;
        tr {
            border: 1px solid #F1F1F1;
            height: 46px;
            &:nth-child(1) {
                background: #F7F7F7;
                td {
                    border: none;
                }
            }
            td {
                border: 1px solid #F1F1F1;
                vertical-align: middle;
                text-align: center;
            }
        }
    }
}
</style>
