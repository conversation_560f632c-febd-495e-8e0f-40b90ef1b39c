<template>
  <div class="content" ref="homePage">
    <div class="main">
      <div class="tips">帮助</div>
      <div class="listBox">
        <div class="list" v-for="(item, index) in helpList" :key="index">
          <a :href="item.src" class="listA">
            <div>{{item.name}}</div>
            <img src="https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/wechat/arrow_forward_ios%20grey%402x.png" alt="">
          </a>
        </div>
      </div>
    </div>
    <div class="btn">问题反馈</div>
  </div>
</template>
<script>

  export default {
    data() {
      return {
        clientHeight: '',
        helpList: [
          {
            name: '如何打开定位服务及微信授权？',
            src: 'https://mp.weixin.qq.com/s/yzcjrimrUrH2MCmNvM0F_g'
          },
          {
            name: '如何开启蓝牙？',
            src: 'https://mp.weixin.qq.com/s/SqQN-QdLQg4_LEPQiwoNOA'
          },
          {
            name: '如何将定位服务授权微信？',
            src: 'https://mp.weixin.qq.com/s/0ysf_rfqqwN2Lq8P6D_WMg'
          },
          {
            name: '如何断开已经配对的设备？',
            src: 'https://mp.weixin.qq.com/s/6RTsi3tbAQDmy5sRYb7aYg'
          },
          {
            name: '蓝牙无法搜索到或无法连接怎么办？',
            src: 'https://mp.weixin.qq.com/s/fNh092FXf9qQ-AeQn8iGkg'
          },
          {
            name: '如何重新绑定设备？',
            src: 'https://mp.weixin.qq.com/s/wjv8T0GGxU6HB5MwVN5Ang'
          },
          {
            name: '未弹出配对/PIN怎么办？',
            src: 'https://mp.weixin.qq.com/s/h3D-Z91k_K6fdxhoBTB3-w'
          }
        ]
      }
    },
    mounted() {
      // 获取浏览器可视区域高度
      this.clientHeight = `${document.documentElement.clientHeight}`
      window.onresize = function temp() {
        this.clientHeight = `${document.documentElement.clientHeight}`
      }
    },
    watch: {
      // 如果 `clientHeight` 发生改变，这个函数就会运行
      clientHeight: function () {
        this.changeFixed(this.clientHeight)
      }
    },
    created() {

    },
    methods: {
      //动态修改样式
      changeFixed(clientHeight) {
        this.$refs.homePage.style.height = clientHeight + 'px'
      }
  }
  }
</script>

<style scoped>
  .content {
    width: 375px;
    background: #f2f2f2;
    display: flex;
    flex-direction: column;
  }
  .main{
    padding-left: 15px;
    width: 360px;
    background: #fff;
    display: flex;
    flex-direction: column;
  }
  .tips{
    color: #999;
    font-size: 13px;
    height: 44px;
    display: flex;
    align-items: center;
  }
  .listBox{
    display: flex;
    flex-direction: column;
  }
  .list{
    border-top: 1px #E5E5E5 solid;
  }
  .listA{
    width: 360px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .listA div{
    width: 320px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #000;
    font-size: 17px;
    text-align: left;
  }
  .listA img{
    width: 16px;
    height: 16px;
    margin-right: 15px;
  }
  .btn{
    width: 375px;
    height: 50px;
    line-height: 50px;
    text-align: center;
    background: #4A9BFC;
    position: fixed;
    bottom: 0;
    font-size: 18px;
    color: #fff;
  }
</style>
