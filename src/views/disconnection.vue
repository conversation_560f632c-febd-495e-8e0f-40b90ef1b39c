<template>
  <div class="main" ref="height">
    <div class="content">
      <img class="img" src="../assets/images/disconnetion.png" alt="">
      <div class="p1">网络加载失败</div>
      <div class="p2">请检查您的手机是否联网</div>
      <div class="btn" @click="onRefresh()">重新加载</div>
    </div>
  </div>
</template>

<script>
  import { mapState } from 'vuex'
  export default {
    name: '',
    data() {
      return {

      }
    },
    created() {
      this.$nextTick(() => {
        this.$refs.height.style.height = window.innerHeight + 'px'
      })
    },
    methods: {
      onRefresh() {
        this.$router.go(-1) // 返回之前点击的页面
      }
    },
    computed: {
      // ...mapState(['networkSuccess'])
    }
  }
</script>

<style lang="scss" scoped>
.main{
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  .content{
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    margin: auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .img{
      width: 371px;
      height: 200px;
    }
    .p1{
      color: #333;
      font-size: 19px;
      line-height: 26px;
      margin: 10px 0;
    }
    .p2{
      color: #999;
      font-size: 17px;
      line-height: 24px;
    }
    .btn{
      width: 185px;
      height: 42px;
      line-height: 42px;
      text-align: center;
      background: linear-gradient(90deg, #FF8100 0%, #FD9A57 100%);
      border-radius: 5px;
      margin-top: 50px;
      font-size: 20px;
      color: #fff;
      cursor: pointer;
    }
  }
}
</style>
