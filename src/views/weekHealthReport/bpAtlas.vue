<template>
  <div class="wrapper">
    <div class="content">
      <div class="date-seven">
        <div @click="beforeStage" class="date-arrow">
          <van-icon name="arrow-left" class="arrow-icon"/>
          <span class="arrow-text">上阶段</span>
        </div>
        <div class="date-comp">
          <div class="date-comp-cont">
            <span>{{ dateFormat(startDate, '.') }}</span>
            <b class="date-comp-sign">-</b>
            <span>{{ dateFormat(endDate, '.') }}</span>
            <cc-svg-icon icon-class="date" class="date-comp-icon"/>
          </div>
          <input
            type="date"
            v-model="endDate"
            :max="dateFormat(new Date())"
            class="date-comp-input"
          >
        </div>
        <div @click="afterStage" class="date-arrow" v-if="nextClick == false">
          <span class="arrow-text">下阶段</span>
          <van-icon name="arrow" class="arrow-icon"/>
        </div>
        <div class="date-arrow" v-if="nextClick == true">
          <span class="arrow-text" style="color:#ccc;">下阶段</span>
          <van-icon name="arrow" class="arrow-icon" color="#ccc"/>
        </div>
      </div>

      <div v-if="!empty">
        <div class="top_banner_info">
          <div class="title">
            七天家庭血压（平均值）
          </div>
          <div class="avg_info">
            <div class="bp_info">
              <span>平均血压{{info.status_text}}</span>
              <span style="margin-left: 10px">{{info.avg_sbp | showVal}}/{{info.avg_dbp | showVal}}</span>
            </div>
            <div class="tips">
              {{info.tips}}
            </div>
          </div>
        </div>

        <div class="blood">
          <div class="bp_info_title">
            7天血压详情
            <div class="tabChange"
                 ref="tabChange">
              <span @click="tab(1)"></span>
              <span @click="tab(2)"></span>
            </div>
          </div>
          <div class="tabOne"
               v-show="tabIndex == 1">
            <div class="allday" v-show="sbu_tabIndex == 1">
              <div class="allday">

                <table class="allday_table">
                  <thead>
                  <tr >
                    <th style="150px">全天</th>
                    <th>收缩压 <br><span class="unit">mmHg</span></th>
                    <th>舒张压<br><span class="unit">mmHg</span></th>
                    <th>脉搏<br><span class="unit">bpm</span></th>
                  </tr>
                  </thead>
                  <tbody v-if="!empty">
                  <tr v-for="(item, index) in info.chart_time" :key=index>
                    <td class="date">{{item}}</td>
                    <td class="bp_val">{{info.table_list.all_sbp[index] == 0 ? '--' :
                      info.table_list.all_sbp[index]}}
                    </td>
                    <td class="bp_val">{{info.table_list.all_dbp[index] == 0 ? '--' :
                      info.table_list.all_dbp[index]}}
                    </td>
                    <td class="bp_val">{{info.table_list.all_pulse[index] == 0 ? '--' :
                      info.table_list.all_pulse[index]}}
                    </td>
                  </tr>
                  </tbody>
                </table>
              </div>
            </div>
            <div class="morning" v-show="sbu_tabIndex == 2">

              <div class="morningday">
                <table class="morningday_table">
                  <thead>
                  <tr>
                    <th style="150px">早间</th>
                    <th>收缩压 <br><span class="unit">mmHg</span></th>
                    <th>舒张压<br><span class="unit">mmHg</span></th>
                    <th>脉搏<br><span class="unit">bpm</span></th>
                  </tr>
                  </thead>
                  <tbody v-if="!empty">
                  <tr v-for="(item, index) in info.chart_time" :key=index>
                    <td class="date">{{item}}</td>
                    <td class="bp_val">{{info.table_list.morning_sbp[index] == 0 ? '--' :
                      info.table_list.morning_sbp[index]}}
                    </td>
                    <td class="bp_val">{{info.table_list.morning_dbp[index] == 0 ? '--' :
                      info.table_list.morning_dbp[index]}}
                    </td>
                    <td class="bp_val">{{info.table_list.morning_pulse[index] == 0 ? '--' :
                      info.table_list.morning_pulse[index]}}
                    </td>
                  </tr>
                  </tbody>
                </table>
              </div>
            </div>
            <div class="evening" v-show="sbu_tabIndex == 3">
              <div class="eveningday">

                <table class="eveningday_table">
                  <thead>
                  <tr>
                    <th style="150px">晚间</th>
                    <th>收缩压 <br><span class="unit">mmHg</span></th>
                    <th>舒张压<br><span class="unit">mmHg</span></th>
                    <th>脉搏<br><span class="unit">bpm</span></th>
                  </tr>
                  </thead>
                  <tbody v-if="!empty">
                  <tr  v-for="(item, index) in info.chart_time" :key=index>
                    <td class="date">{{item}}</td>
                    <td class="bp_val">{{info.table_list.night_sbp[index] == 0 ? '--' :
                      info.table_list.night_sbp[index]}}
                    </td>
                    <td class="bp_val">{{info.table_list.night_dbp[index] == 0 ? '--' :
                      info.table_list.night_dbp[index]}}
                    </td>
                    <td class="bp_val">{{info.table_list.night_pulse[index] == 0 ? '--' :
                      info.table_list.night_pulse[index]}}
                    </td>
                  </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
          <div class="tabTwo"
               v-show="tabIndex == 2">
            <div class="bloodChart"
                 ref="bloodRef"></div>
          </div>
          <div class="change_sub_tab">
            <img :src="sbu_tabIndex===1?require('./imgs/allday_active.png'):require('./imgs/allday_icon.png')"
                 @click="changeSubTab(1)" alt="">
            <img :src="sbu_tabIndex===2?require('./imgs/moning_active.png'):require('./imgs/moning_icon.png')"
                 @click="changeSubTab(2)" alt="">
            <img :src="sbu_tabIndex===3?require('./imgs/evening_active.png'):require('./imgs/evening_icon.png')"
                 @click="changeSubTab(3)" alt="">
          </div>
        </div>

        <div class="atlas">
          <div class="title">
            7天血压图谱
          </div>
          <blood-pic :data="bpData"/>
        </div>
      </div>
      <div v-else class="no_data">
        <img src="./imgs/empty.png" alt="">
        <div class="no_tip">暂无数据</div>
        <div class="sub_tip">这7天您未进行测量</div>
      </div>
      <!--<p class="foot-tips">参考《2019家庭血压监测指南》得出标准结论。实际控制指标以医生建议为准。</p>-->
    </div>
  </div>
</template>

<script>
  import moment from 'moment'
  import bloodPic from '@/components/bloodPic.vue'
  import {bpGraphData, reportBp} from '@/api/weekHealthReport.js'
  import echarts from 'echarts'

  export default {
    data: () => {
      return {
        endDate: '',
        bpData: [],
        isLoading: false,
        nextClick: false,
        tabIndex: 1,
        sbu_tabIndex: 1,
        empty: true,
        info: {
          chart: [],
          avg_msg: '',
          avg_sbp: '',
          avg_dbp: '',
          number: '',
          normal: '',
          m_sbp: [],
          m_dbp: [],
          s_sbp: [],
          s_dbp: [],
          unusual: '',
          chart_time: [],
          table_list: {
            morning_sbp: [],
            morning_dbp: [],
            morning_pulse: [],
            night_sbp: [],
            night_dbp: [],
            night_pulse: []
          },
          bp_distribution: [],
          time_slot: ''
        }
      }
    },
    computed: {
      // 开始时间
      startDate() {
        let endDate = this.endDate
        if (isNaN(Date.parse(endDate))) endDate = new Date()
        return this.dateCount(endDate, -6)
      }
    },
    filters: {
      showVal(val) {
        if (val == '') {
          return '--'
        } else {
          return val
        }
      }
    },
    watch: {
      endDate(newVal, oldVal) {
        let todayDate = new Date()
        if (newVal == moment(todayDate).format('YYYY-MM-DD')) {
          this.nextClick = true
        } else {
          this.nextClick = false
        }
        // 初始化改变时 返回
        if (isNaN(Date.parse(oldVal))) return
        // 如果不是日期格式 返回
        if (isNaN(Date.parse(newVal))) {
          this.endDate = this.dateFormat(new Date())
          return
        }
        // 获取血糖趋势
        this.getBpGraphData()

        this.getBPInfo()
      }
    },
    components: {
      'blood-pic': bloodPic
    },
    created() {
      // 初始化
      this.init()
    },
    methods: {
      /**
       * 初始化
       */
      init() {
        this.endDate = this.dateFormat(new Date())
        // 获取血压图谱
        this.getBpGraphData()

        this.getBPInfo()

      },
      getBPInfo() {
        let that = this
        reportBp({
          start_date: this.startDate, // 开始时间
          end_date: this.endDate // 结束时间
        }).then(res => {
          if (res.status == 0) {
            if (res.data.length != 0) {
              that.empty = false
              that.info = res.data
            } else {
              that.empty = true
              that.info = {
                chart: [],
                avg_msg: '',
                avg_sbp: '',
                avg_dbp: '',
                number: '',
                normal: '',
                m_sbp: [],
                m_dbp: [],
                s_sbp: [],
                s_dbp: [],
                unusual: '',
                chart_time: [],
                table_list: {
                  morning_sbp: [],
                  morning_dbp: [],
                  morning_pulse: [],
                  night_sbp: [],
                  night_dbp: [],
                  night_pulse: []
                },
                bp_distribution: [],
                time_slot: ''
              }
            }
          } else {
            this.$toast(res.msg)
          }
        }).then(() => {
          if (!that.empty) {
            that.bloodPic(that.$refs.bloodRef, that.info.chart_time, that.info.all_sbp, that.info.all_dbp, 1)
          }
        })
      },
      /**
       * 获取血压图谱
       */
      getBpGraphData() {
        let that = this
        this.isLoading = true
        bpGraphData(this.startDate, this.endDate).then(res => {
          if (res.status === 0) {
            that.bpData = res.data
            that.isLoading = false
          } else {
            this.$toast(res.msg)
          }
        })
      },
      /**
       * 日期计算
       * @param {String} date 日期
       * @param {Number} index 未来或过去的天数（正数未来，负数过去）
       * @param {String} sep 分隔符
       */
      dateCount(date, index, sep = '-') {
        let oDate = new Date(date)
        // 天数转换为毫秒数
        let oMin = index * 24 * 60 * 60 * 1000
        let newDate = new Date(oDate.getTime() + oMin)
        return this.dateFormat(newDate, sep)
      },
      /**
       * 日期格式化
       * @param {String | Date} date 日期
       * @param {String} sep 分隔符
       * @return {String} 格式化日期
       */
      dateFormat(date, sep = '-') {
        let oDate = new Date(date)
        let y = oDate.getFullYear()
        let m = oDate.getMonth() + 1
        let d = oDate.getDate()
        if (m < 10) m = `0${m}`
        if (d < 10) d = `0${d}`
        return `${y}${sep}${m}${sep}${d}`
      },
      /**
       * 上阶段
       */
      beforeStage() {
        if (this.isLoading) return
        this.endDate = this.dateCount(this.startDate, -1)
      },
      tab(index) {
        let that = this
        this.tabIndex = index
        if (index == 1) {
          this.$refs.tabChange.style.backgroundImage = "url('https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/mmc-site/3.8.0/left.png')"
        } else if (index == 2) {
          this.$refs.tabChange.style.backgroundImage = "url('https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/mmc-site/3.8.0/right.png')"

          if (this.sbu_tabIndex == 1) {
            that.bloodPic(that.$refs.bloodRef, that.info.chart_time, that.info.all_sbp, that.info.all_dbp, this.sbu_tabIndex)
          } else if (this.sbu_tabIndex == 2) {
            that.bloodPic(that.$refs.bloodRef, that.info.chart_time, that.info.m_sbp, that.info.m_dbp, this.sbu_tabIndex)
          } else {
            that.bloodPic(that.$refs.bloodRef, that.info.chart_time, that.info.s_sbp, that.info.s_dbp, this.sbu_tabIndex)
          }
        }
      },
      changeSubTab(index) {
        let that = this
        this.sbu_tabIndex = index
        if (this.tabIndex == 2) {
          if (index == 1) {
            that.bloodPic(that.$refs.bloodRef, that.info.chart_time, that.info.all_sbp, that.info.all_dbp, index)
          } else if (index == 2) {
            that.bloodPic(that.$refs.bloodRef, that.info.chart_time, that.info.m_sbp, that.info.m_dbp, index)
          } else {
            that.bloodPic(that.$refs.bloodRef, that.info.chart_time, that.info.s_sbp, that.info.s_dbp, index)
          }
        }
      },
      /**
       * 下阶段
       */
      afterStage() {
        if (this.isLoading) return

        // 开始时间为计算属性，因此从开始日期推未来推13天
        let oDate = this.dateCount(this.startDate, 13)
        let newDate = new Date(oDate)
        let todayDate = new Date()
        if (newDate.getTime() > todayDate.getTime()) oDate = this.dateFormat(todayDate)
        this.endDate = oDate
      },
      sbpColor(num) {
        if (num > 0 && num < 90) {
          return 'colorDown'
        } else if (num >= 90 && num <= 135 || num == 0) {
          return ''
        } else {
          return 'colorHigh'
        }
      },
      dbpColor(num) {
        if (num > 0 && num < 60) {
          return 'colorDown'
        } else if (num >= 60 && num <= 85 || num == 0) {
          return ''
        } else {
          return 'colorHigh'
        }
      },
      // 血压明细图
      bloodPic(dom, datas, sbp, dbp, tab) {
        let myEcharts = echarts.init(dom)
        if (tab == 1) {
          myEcharts.setOption({
            title: {
              text: 'mmHg',
              left: '8px',
              textStyle: {
                color: '#666666',
                fontSize: '24px'
              }
            },
            tooltip: {
              trigger: 'none'
            },
            legend: {
              y: 'bottom',
              data: ['高压', '低压'],
              textStyle: {
                color: '#666666',
                fontSize: '20px'
              }
            },
            grid: {
              left: '3%',
              right: '4%',
              bottom: '10%',
              top: '12%',
              containLabel: true
            },
            xAxis: {
              type: 'category',
              axisLine: {
                lineStyle: {
                  color: '#ccc'
                }
              },
              axisLabel: {
                color: '#666'
              },
              boundaryGap: true,
              data: datas
            },
            yAxis: {
              type: 'value',
              axisLine: {
                lineStyle: {
                  color: '#ccc'
                }
              },
              axisLabel: {
                color: '#666'
              },
              min: 0,
              max: 200,
              splitLine: {
                lineStyle: {
                  color: '#eee'
                }
              }
            },
            series: [
              {
                name: '高压',
                type: 'line',
                symbol: 'circle',
                symbolSize: 9,
                itemStyle: {
                  normal: {
                    color: '#C3E198',
                    label: {
                      show: true,
                      color: '#333333',
                      fontSize: '14'
                    },
                    lineStyle: {
                      color: '#C3E198',
                      width: 1
                    }
                  }
                },
                data: sbp
              },
              {
                name: '低压',
                type: 'line',
                symbol: 'triangle',
                symbolSize: 9,
                itemStyle: {
                  normal: {
                    color: '#C3E198',
                    label: {
                      show: true,
                      color: '#333333',
                      fontSize: '14'
                    },
                    lineStyle: {
                      color: '#C3E198',
                      width: 1
                    }
                  }
                },
                data: dbp
              }
            ]
          })
        } else if (tab == 2) {
          myEcharts.setOption({
            title: {
              text: 'mmHg',
              left: '8px',
              textStyle: {
                color: '#666666',
                fontSize: '24px'
              }
            },
            tooltip: {
              trigger: 'none'
            },
            legend: {
              y: 'bottom',
              data: ['早晨高压', '早晨低压'],
              textStyle: {
                color: '#666666',
                fontSize: '20px'
              }
            },
            grid: {
              left: '3%',
              right: '4%',
              bottom: '10%',
              top: '12%',
              containLabel: true
            },
            xAxis: {
              type: 'category',
              axisLine: {
                lineStyle: {
                  color: '#ccc'
                }
              },
              axisLabel: {
                color: '#666'
              },
              boundaryGap: true,
              data: datas
            },
            yAxis: {
              type: 'value',
              axisLine: {
                lineStyle: {
                  color: '#ccc'
                }
              },
              axisLabel: {
                color: '#666'
              },
              min: 0,
              max: 200,
              splitLine: {
                lineStyle: {
                  color: '#eee'
                }
              }
            },
            series: [
              {
                name: '早晨高压',
                type: 'line',
                symbol: 'circle',
                symbolSize: 9,
                itemStyle: {
                  normal: {
                    color: '#FF8E00',
                    label: {
                      show: true,
                      color: '#333333',
                      fontSize: '14'
                    },
                    lineStyle: {
                      color: '#F67710',
                      width: 1
                    }
                  }
                },
                data: sbp
              },
              {
                name: '早晨低压',
                type: 'line',
                symbol: 'triangle',
                symbolSize: 9,
                itemStyle: {
                  normal: {
                    color: '#FF8E00',
                    label: {
                      show: true,
                      color: '#333333',
                      fontSize: '14'
                    },
                    lineStyle: {
                      color: '#F67710',
                      width: 1
                    }
                  }
                },
                data: dbp
              }
            ]
          })
        } else {
          myEcharts.setOption({
            title: {
              text: 'mmHg',
              left: '8px',
              textStyle: {
                color: '#666666',
                fontSize: '24px'
              }
            },
            tooltip: {
              trigger: 'none'
            },
            legend: {
              y: 'bottom',
              data: ['晚上高压', '晚上低压'],
              textStyle: {
                color: '#666666',
                fontSize: '20px'
              }
            },
            grid: {
              left: '3%',
              right: '4%',
              bottom: '10%',
              top: '12%',
              containLabel: true
            },
            xAxis: {
              type: 'category',
              axisLine: {
                lineStyle: {
                  color: '#ccc'
                }
              },
              axisLabel: {
                color: '#666'
              },
              boundaryGap: true,
              data: datas
            },
            yAxis: {
              type: 'value',
              axisLine: {
                lineStyle: {
                  color: '#ccc'
                }
              },
              axisLabel: {
                color: '#666'
              },
              min: 0,
              max: 200,
              splitLine: {
                lineStyle: {
                  color: '#eee'
                }
              }
            },
            series: [
              {
                name: '晚上高压',
                type: 'line',
                symbol: 'circle',
                symbolSize: 9,
                itemStyle: {
                  normal: {
                    color: '#61B1F0',
                    label: {
                      show: true,
                      color: '#333333',
                      position: 'bottom',
                      fontSize: '14'
                    },
                    lineStyle: {
                      color: '#10A2F6',
                      width: 1
                    }
                  }
                },
                data: sbp
              },
              {
                name: '晚上低压',
                type: 'line',
                symbol: 'triangle',
                symbolSize: 9,
                itemStyle: {
                  normal: {
                    color: '#61B1F0',
                    label: {
                      show: true,
                      color: '#333333',
                      position: 'bottom',
                      fontSize: '15'
                    },
                    lineStyle: {
                      color: '#10A2F6',
                      width: 1
                    }
                  }
                },
                data: dbp
              }
            ]
          })
        }


      }
    }
  }
</script>

<style lang="scss" scoped>
  @import "@/assets/scss/bpAtlas.scss";

  .top_banner_info {
    margin: 20px 10px;
    .title {
      font-size: 14px;
      font-weight: 500;
      color: #333333;
      line-height: 20px;
      text-align: left;
      padding-bottom: 10px;
    }
    .avg_info {
      padding: 15px 10px;
      background: linear-gradient(180deg, #FFF7F1 0%, #FFECDE 100%);
      border-radius: 9px;
      display: flex;
      justify-content: center;
      flex-direction: column;
      .bp_info {
        font-size: 20px;
        font-weight: 400;
        color: #333333;
        line-height: 28px;
      }
      .tips {
        font-size: 16px;
        font-weight: 400;
        color: #333333;
        line-height: 22px;
        padding-top: 10px;
      }
    }
  }

  .tabOne {
    width: 355px;
    height: 338px;
    margin: 0 auto;

    .allday_table,.morningday_table,.eveningday_table {
      width: 100%;
      td, th {
        vertical-align: middle;
        border: 1px solid #CCCCCC;
      }
      th {
        vertical-align: middle;
        padding: 10px;
        .unit {
          font-size: 12px;
          font-weight: 400;
          color: #666666;
          line-height: 17px;
        }
      }

      thead {
        background: #C3E198;
        font-size: 15px;
        font-weight: 400;
        color: #333333;
        line-height: 21px;
      }
      tbody {
        tr {
          height: 37px;
          background: #Fff;
          .date {
            font-size: 15px;
            font-weight: 500;
            color: #666666;
            line-height: 37px;
          }
          .bp_val {
            font-size: 18px;
            font-weight: 400;
            color: #333333;
            line-height: 37px;
          }
        }
        tr:nth-child(even) {
          background: rgba(195, 225, 152, 0.3);
        }

      }

    }
    .morningday_table{
      thead {
        background: #F7D4B0;
      }
      tbody {
        tr:nth-child(even) {
          background: rgba(247, 212, 176, 0.3);
        }
      }
    }
    .eveningday_table{
      thead {
        background: #BCD8F0;
      }

      tbody {
        tr:nth-child(even) {
          background: rgba(188, 216, 240, 0.5);
        }
      }
    }
  }

  .tabTwo {
    width: 355px;
    height: 338px;
    margin: 0 auto;
    .bloodChart {
      width: 355px;
      height: 320px;
    }
  }

  .change_sub_tab {
    padding-top: 10px;
    display: flex;
    justify-content: space-between;
    height: 60px;
    width: 70%;
    align-items: center;
    margin: 0 auto;
    img {
      width: 48px;
      height: 48px;
    }
  }

  .bp_info_title {
    margin-left: 15px;
    font-size: 14px;
    font-weight: 500;
    color: #333333;
    line-height: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .tabChange {
      margin-right: 10px !important;
      width: 136px;
      height: 30px;
      margin: 10px auto;
      background: url('https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/mmc-site/3.8.0/left.png') no-repeat;
      background-size: 100% 100%;
      span {
        display: block;
        width: 50%;
        height: 100%;
        float: left;
        font-size: 15px;
        font-weight: 400;
        color: #FFFFFF;
        line-height: 21px;
      }
      span:nth-child(1) {

      }
    }
  }

  .atlas {
    .title {
      margin-left: 15px;
      font-size: 14px;
      font-weight: 500;
      color: #333333;
      line-height: 20px;
      text-align: left;
      padding-bottom: 10px;
    }
  }

  .table_bp_info {
    width: 100%;
    height: 160px;
    font-size: 14px;
    font-weight: 400;
    color: #333333;
    line-height: 32px;
    align-items: center;
    td {
      border: solid 1px #cccccc;
    }
  }

  .no_data{
    height: 70vh;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    img{
      width: 300px;
      height:161px;
    }
    .no_tip{
      padding-top: 10px;
      font-size: 19px;
      font-weight: 400;
      color: #333333;
      line-height: 26px;
    }
    .sub_tip{
      padding-top: 10px;
      font-size: 17px;
      font-weight: 400;
      color: #999999;
      line-height: 24px;
    }

  }

</style>
