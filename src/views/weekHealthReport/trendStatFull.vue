<template>
  <div class="wrapper">
    <div class="content">
      <div class="tab">
        <span v-for="tab in tabList"
          :key="tab.id"
          @click="changeTab(tab)"
          :class="{'tab-item': 1, 'tab-item-select': tab.select}"
        >{{tab.name}}</span>
      </div>
      <div :key="allDate" :class="{'chart': true, 'chart-all': allDate}">
        <div class="chart-box" ref="chartBox"></div>
      </div>
      <div v-if="allDate" class="all-date">
        <span
          v-for="(item, index) in weekDate"
          :key="index"
          @click="showSingle(item.date, item.year)"
          class="chart-date"
        >{{ item.date }}</span>
      </div>
    </div>
    <!-- 单日趋势图 -->
    <div v-if="singleDialog" class="single-dialog">
      <div class="single-wrapper">
        <h1 class="single-title">{{singleTitle}}</h1>
        <div class="single-chart">
          <div class="chart-box" ref="singleChartBox"></div>
        </div>
        <div @click="closeSingle" class="single-footer">
          <span class="single-footer-btn">关闭</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import echarts from 'echarts'
import { bgGraphData, bgOneDyData } from '@/api/weekHealthReport.js'

export default {
  data: () => {
    return {
      tabList: [
        {
          id: 0,
          name: '全部',
          select: true
        },
        {
          id: 1,
          name: '空腹',
          select: false
        },
        {
          id: 2,
          name: '早餐后',
          select: false
        },
        {
          id: 3,
          name: '午餐前',
          select: false
        },
        {
          id: 4,
          name: '午餐后',
          select: false
        },
        {
          id: 5,
          name: '晚餐前',
          select: false
        },
        {
          id: 6,
          name: '晚餐后',
          select: false
        },
        {
          id: 7,
          name: '睡前',
          select: false
        },
        {
          id: 8,
          name: '凌晨',
          select: false
        }
      ],
      endDate: '',
      startDate: '',
      dinStatus: 0,
      chartData: [],
      chartDate: [],
      weekDate: [],
      singleDialog: false,
      singleTitle: '',
      singleData: [],
      singleDate: [],
      isLoading: false
    }
  },
  computed: {
    // 全部状态下显示7日按钮开关
    allDate() {
      return this.tabList[0].select
    }
  },
  created() {
    let query = this.$route.query
    this.endDate = query.endDate
    this.startDate = query.startDate
    // 初始化
    this.init()
  },
  methods: {
    /**
     * 初始化
     */
    init () {
      // 全部日期
      let weekDate = []
      for (var i = 0; i < 7; i++) {
        let oDate = this.dateCount(this.endDate, -i, '/')
        let year = oDate.slice(0, 5)
        let date = oDate.slice(5)
        weekDate.push({ year, date })
      }
      this.weekDate = weekDate.reverse()
      // 获取血糖趋势
      this.getBgGraphData()
    },
    /**
     * 获取血糖趋势
     */
    getBgGraphData() {
      this.isLoading = true
      bgGraphData(this.startDate, this.endDate, this.dinStatus).then(res => {
        if (res.status === 0) {
          this.chartDate = res.data.date
          this.chartData = res.data.bg
          this.$nextTick(() => {
            // 折线图初始化
            this.lineChartInit('chartBox', this.chartData, this.chartDate, this.dinStatus)
          })
          this.isLoading = false
        } else {
          this.$toast(res.msg)
        }
      })
    },
    /**
     * 日期格式化
     * @param {String | Date} date 日期
     * @param {String} sep 分隔符
     * @return {String} 格式化日期
     */
    dateFormat(date, sep = '-') {
      let oDate = new Date(date)
      let y = oDate.getFullYear()
      let m = oDate.getMonth() + 1
      let d = oDate.getDate()
      if (m < 10) m = `0${m}`
      if (d < 10) d = `0${d}`
      return `${y}${sep}${m}${sep}${d}`
    },
    /**
     * 日期计算
     * @param {String} date 日期
     * @param {Number} index 未来或过去的天数（正数未来，负数过去）
     * @param {String} sep 分隔符
     */
    dateCount(date, index, sep = '-') {
      let oDate = new Date(date)
      // 天数转换为毫秒数
      let oMin = index * 24 * 60 * 60 * 1000
      let newDate = new Date(oDate.getTime() + oMin)
      return this.dateFormat(newDate, sep)
    },
    /**
     * 改变tab
     */
    changeTab (tab) {
      // 已经聚焦的tab 或者 正在加载中 直接返回
      if (tab.select || this.isLoading) return

      this.tabList.forEach(item => {
        item.select = false
        if (item.id === tab.id) {
          item.select = true
          this.dinStatus = item.id
        }
      })
      // 获取血糖趋势
      this.getBgGraphData()
    },
    /**
     * 折线图初始化
     * @param {String} ref DOM
     * @param {Array} yAxisData y轴数据
     * @param {Array} xAxisData x轴数据
     *  @param {String} type 类型（0：全部; -1:单日趋势图）
     */
    lineChartInit (ref, yAxisData, xAxisData, type = 0) {
      let myEchart = echarts.init(this.$refs[ref])
      let minData = []
      let maxData = []
      if (type === 1) {
        maxData = [[0, 2.6], [1, 2.6], [2, 2.6], [3, 2.6], [4, 2.6], [5, 2.6], [6, 2.6]]
        minData = [[0, 4.4], [1, 4.4], [2, 4.4], [3, 4.4], [4, 4.4], [5, 4.4], [6, 4.4]]
      } else if (type > 1) {
        maxData = [[0, 5.6], [1, 5.6], [2, 5.6], [3, 5.6], [4, 5.6], [5, 5.6], [6, 5.6]]
        minData = [[0, 4.4], [1, 4.4], [2, 4.4], [3, 4.4], [4, 4.4], [5, 4.4], [6, 4.4]]
      }

      let myOption = {
        tooltip: {
          show: true,
          trigger: 'axis'
        },
        grid: {
          top: '30px',
          bottom: type === 0 ? '-10px' : '5px',
          right: '15px',
          left: '10px',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          axisLabel: {
            interval: 0,
            color: type === 0 ? 'rgba(0, 23, 11, 0)' : 'rgba(0, 23, 11, 1)',
            rotate: 0
          },
          axisTick: {
            show: type !== 0,
            alignWithLabel: true
          },
          axisLine: {
            lineStyle: {
              color: '#ccc'
            }
          },
          data: xAxisData
        },
        yAxis: {
          type: 'value',
          name: 'mmol/L',
          axisLine: {
            lineStyle: {
              color: '#ccc'
            }
          },
          axisLabel: {
            color: '#666'
          },
          splitLine: {
            lineStyle: {
              color: '#eee'
            }
          }
        },
        series: [
          {
            type: 'line',
            symbol: 'circle',
            symbolSize: 9,
            itemStyle: {
              normal: {
                color: '#FF8E00',
                label: {
                  show: true,
                  color: '#333333'
                },
                lineStyle: {
                  color: '#F67710',
                  width: 1
                }
              }
            },
            data: yAxisData
          },
          {
            type: 'line',
            name: 'min',
            stack: '正常',
            symbol: 'none',
            lineStyle: { opacity: 0 },
            data: minData
          },
          {
            type: 'line',
            name: 'max',
            stack: '正常',
            symbol: 'none',
            lineStyle: { opacity: 0 },
            areaStyle: { color: '#B8E986' },
            data: maxData
          }
        ]
      }

      myEchart.setOption(myOption)
    },
    /**
     * 显示单日趋势图
     * @param {String} date 月日('05/13')
     * @param {String} year 年('2020/')
     */
    showSingle(date, year) {
      let oDate = this.dateFormat(year + date)
      // 获取某一天的血糖数据
      bgOneDyData(oDate).then(res => {
        if (res.status === 0) {
          this.singleData = res.data.bg
          this.singleDate = res.data.din_status
          this.singleDialog = true
          this.$nextTick(() => {
            this.lineChartInit('singleChartBox', this.singleData, this.singleDate, -1)
          })
        } else {
          this.$toast(res.msg)
        }
      })
      this.singleTitle = `${date}血糖趋势变化`
    },
    /**
     * 关闭单日趋势图
     */
    closeSingle() {
      this.singleDialog = false
      this.singleTitle = ''
      this.singleData = []
      this.singleDate = []
    }
  }
}
</script>

<style lang="scss" scoped>
@import "@/assets/scss/trendStatFull.scss"
</style>
