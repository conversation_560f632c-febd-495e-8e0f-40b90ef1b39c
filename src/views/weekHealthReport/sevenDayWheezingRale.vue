<template>
  <div class="wrapper">
    <div class="date-seven">
      <div @click="beforeStage" class="date-arrow">
        <van-icon name="arrow-left" class="arrow-icon"/>
        <span class="arrow-text">上阶段</span>
      </div>
      <div class="date-comp">
        <div class="date-comp-cont">
          <span>{{ dateFormat(startDate, '.') }}</span>
          <b class="date-comp-sign">-</b>
          <span>{{ dateFormat(endDate, '.') }}</span>
          <cc-svg-icon icon-class="date" class="date-comp-icon"/>
        </div>
        <input type="date"
               v-model="endDate"
               :max="dateFormat(new Date())"
               class="date-comp-input">
      </div>
      <div @click="afterStage" class="date-arrow" v-if="nextClick == false">
        <span class="arrow-text">下阶段</span>
        <van-icon name="arrow" class="arrow-icon"/>
      </div>
      <div class="date-arrow" v-if="nextClick == true">
        <span class="arrow-text" style="color:#ccc;">下阶段</span>
        <van-icon name="arrow" class="arrow-icon" color="#ccc"/>
      </div>
    </div>

    <p class="date-tips">根据所选日期往前7日哮鸣音测量报告</p>
    <div class="chart">
      <p class="chart-title">七日总结</p>
      <div class="chart-wrapper">
        <div class="chart-box" v-if="count.total>0" ref="chartBox"></div>
        <img class="chart-box2" v-if="count.total==0" src="./imgs/no_data.png">
        <div class="times">
          <p class="timesName">测量次数</p>
          <p class="timesNum">{{ count.total }}次</p>
        </div>
      </div>
    </div>

    <div class="table-box">
      <table class="table">
        <tr>
          <td>哮鸣音</td>
          <td>正常</td>
          <td>有哮鸣音</td>
          <td>错误次数</td>
        </tr>
        <tr>
          <td>
            <p>次数</p>
          </td>
          <td class="table-val">{{  count.no_wheezes }}</td>
          <td class="table-val">{{  count.wheezes }}</td>
          <td class="table-val">{{  count.wheezes_error }}</td>
        </tr>

      </table>
    </div>
  </div>
</template>

<script>
import moment from 'moment'
import echarts from 'echarts'
import {getWheezingRateDateRecords} from '@/api/weekHealthReport.js'

export default {
  data: () => {
    return {
      userId: 0,
      workroomId:0,
      endDate: '',
      count: {},
      statistic: {},
      duration: [],
      isLoading: false,
      nextClick: false
    }
  },
  computed: {
    // 开始时间
    startDate() {
      let endDate = this.endDate
      if (isNaN(Date.parse(endDate))) endDate = new Date()
      return this.dateCount(endDate, -6)
    }
  },
  watch: {
    endDate(newVal, oldVal) {
      let todayDate = new Date()
      if (newVal == moment(todayDate).format('YYYY-MM-DD')) {
        this.nextClick = true
      } else {
        this.nextClick = false
      }
      // 初始化改变时 返回
      if (isNaN(Date.parse(oldVal))) return
      // 如果不是日期格式 返回
      if (isNaN(Date.parse(newVal))) {
        this.endDate = this.dateFormat(new Date())
        return
      }
      // 获取血糖数据
      this.getBgData()
    }
  },
  created() {
    // 初始化
    this.init()
  },
  methods: {
    /**
     * 初始化
     */
    init() {
      this.userId = this.$route.query.user_id
      this.workroomId = this.$route.query.workroom_id
      this.endDate = this.dateFormat(new Date())
      // 获取血糖数据
      this.getBgData()
    },
    /**
     * 获取血糖数据
     */
    getBgData() {
      this.isLoading = true
      let that = this
      getWheezingRateDateRecords(this.userId, this.workroomId,this.startDate, this.endDate).then(res => {
        if (res.code === 200) {
          that.isLoading = false
          that.count = res.data.count
          that.duration = res.data.duration
          if (that.count.total > 0) {
            // 饼状图初始化
            that.$nextTick(() => {
              that.pieChartInit('chartBox')
            })
          }
        } else {
          this.$toast(res.msg)
        }
      })
    },
    /**
     * 日期格式化
     * @param {String | Date} date 日期
     * @param {String} sep 分隔符
     * @return {String} 格式化日期
     */
    dateFormat(date, sep = '-') {
      let oDate = new Date(date)
      let y = oDate.getFullYear()
      let m = oDate.getMonth() + 1
      let d = oDate.getDate()
      if (m < 10) m = `0${m}`
      if (d < 10) d = `0${d}`
      return `${y}${sep}${m}${sep}${d}`
    },
    /**
     * 日期计算
     * @param {String} date 日期
     * @param {Number} index 未来或过去的天数（正数未来，负数过去）
     * @param {String} sep 分隔符
     */
    dateCount(date, index, sep = '-') {
      let oDate = new Date(date)
      // 天数转换为毫秒数
      let oMin = index * 24 * 60 * 60 * 1000
      let newDate = new Date(oDate.getTime() + oMin)
      return this.dateFormat(newDate, sep)
    },
    /**
     * 上阶段
     */
    beforeStage() {
      if (this.isLoading) return
      this.endDate = this.dateCount(this.startDate, -1)
    },
    /**
     * 下阶段
     */
    afterStage() {
      if (this.isLoading) return

      // 开始时间为计算属性，因此从开始日期推未来推13天
      let oDate = this.dateCount(this.startDate, 13)
      let newDate = new Date(oDate)
      let todayDate = new Date()
      if (newDate.getTime() > todayDate.getTime()) oDate = this.dateFormat(todayDate)
      this.endDate = oDate
    },
    /**
     * 饼状图初始化
     * @param {String} ref DOM
     */
    pieChartInit(ref) {
      let myEcharts = echarts.init(this.$refs[ref])
      let result = []
      let nameColor = {
        '正常': '#07C160',
        '有哮鸣音': '#F5222D',
        '错误次数': '#7B62FF'
      }
      this.duration.forEach((item) => {
        if (item.value > 0) {
          result.push({value: item.value, name: item.name, itemStyle: {color: nameColor[item.name]}})
        }
      })
      myEcharts.setOption({
        tooltip: {
          trigger: 'item',
          formatter: '{b}<br/>{c}次 ({d}%)'
        },
        legend: {
          y: 'bottom',
          textStyle: {
            fontSize: 10
          },
          itemWidth: 7,
          itemHeight: 7
        },
        series: [{
          type: 'pie',
          radius: ['50%', '35%'],
          center: ['50%', '50%'],
          label: {
            normal: {
              show: true,
              formatter: '{b}: {d}%',
              textStyle: {
                fontSize: 12
              }
            }
          },
          labelLine: {
            normal: {
              show: false
            }
          },
          itemStyle: {
            borderWidth: 3,
            borderColor: '#fff',
            emphasis: {
              shadow: 10,
              shadowOffsetX: 0,
              shadowColor: '#rgba(0,0,0,0.5)'
            }
          },
          data: result
        }]
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.wrapper {
  overflow: hidden;
  padding: 10px 15px;
  background: #fff;

  .date-seven {
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;

    .date-arrow {
      width: 60px;
      display: flex;
      color: #333333;
      align-items: center;
      justify-content: space-between;

      .arrow-text {
        font-size: 13px;
      }

      .arrow-icon {
        font-size: 18px;
      }
    }

    .date-comp {
      width: 200px;
      margin: 0 8px;
      color: #F67710;
      position: relative;

      .date-comp-cont {
        height: 25px;
        display: flex;
        font-size: 13px;
        color: #F67710;
        border-radius: 5px;
        align-items: center;
        justify-content: center;
        border: 1px solid #ffecdd;

        .date-comp-sign {
          margin: 0 2px;
        }

        .date-comp-icon {
          margin-left: 5px;
        }
      }

      .date-comp-input {
        width: 200px;
        height: 25px;
        font-size: 13px;
        text-align: center;
        border-radius: 5px;
        border: 1px solid #ffecdd;
        position: absolute;
        top: 0;
        left: 0;
        opacity: 0;
      }
    }
  }

  .date-tips {
    font-size: 13px;
    color: #999999;
    margin-top: 10px;
  }

  .chart {
    width: 100%;
    height: 200px;
    margin-top: 20px;

    .chart-title {
      color: #333;
      font-size: 16px;
      text-align: left;
      font-weight: 600;
      line-height: 20px;
    }

    .chart-wrapper {
      width: 100%;
      height: 180px;
      position: relative;

      .times {
        transform: translate(-50%, -50%);
        position: absolute;
        left: 50%;
        top: 50%;
        z-index: 2;
      }

      .timesName {
        font-size: 10px;
        color: #666666;
        text-align: center;
        margin-top: 13px;
        display: block;
      }

      .timesNum {
        font-size: 12px;
        color: #333333;
        text-align: center;
        margin-top: 10px;
        display: block;
      }
    }

    .chart-box {
      width: 100%;
      height: 190px;
    }

    .chart-box2 {
      width: 130px;
      height: 130px;
      transform: translate(-50%, -50%);
      position: absolute;
      left: 50%;
      top: 50%;
      z-index: 2;
    }
  }

  .table-box {
    margin-top: 30px;

    .table {
      width: 100%;

      td {
        width: 25%;
        height: 40px;
        color: #666;
        font-size: 14px;
        font-weight: 400;
        vertical-align: middle;
        border: 1px solid #E6E6E6;
      }

      .table-val {
        color: #333;
        font-size: 17px;
        font-weight: normal;
      }
    }
  }
}
</style>
