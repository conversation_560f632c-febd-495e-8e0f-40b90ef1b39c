<template>
  <div class="content">
    <ul class="clearfix">
      <li v-for="(item, index) in reportList" :key="index">
        <p class="date">
          <i :class="[statusClass(item.status), 'date-icon']"></i>
          <span class="date-txt">{{ item.week_start_date }}~{{ item.week_end_date }}</span>
        </p>
        <div @click="checkDetail(item.id)" class="box">
          <p class="status">{{ item.desc.title }}</p>
          <p class="details">{{ item.desc.content }}</p>
          <p class="lookDetails">
            <span>查看详情</span>
            <van-icon name="arrow" class="lookDetails-arrow" />
          </p>
        </div>
      </li>
    </ul>
    <div class="footer">
      <span v-if="isLastReport">没有更多报告啦</span>
      <div v-if="!isLastReport && isLoading" class="footer-loading">
        <span>加载更多报告</span>
        <van-loading type="spinner" size="18" class="footer-icon" />
      </div>
    </div>
  </div>
</template>

<script>
  import { weeklyReportList } from '@/api/weekHealthReport.js'
  import { getClientHeight, getScrollTop, getScrollHeight } from '@/utils/utils.js'

  export default {
    data() {
      return {
        reportList: [],
        isClientBottom: false,
        isLastReport: false,
        isLoading: false,
        pageNum: 1
      }
    },
    watch: {
      isClientBottom(newVal, oldVal) {
        // 滑倒底部并且还有更多报告时，以及不在加载中（防止重复加载）
        if (newVal && !this.isLastReport && !this.isLoading) {
          // 获取往期报告列表
          this.getWeeklyReportList()
        }
      }
    },
    created() {
      // 初始化
      this.init()
    },
    mounted() {
      // 判断是否滑到底部
      this.checkScroll()
    },
    methods: {
      /**
       * 初始化
       */
      init() {
        // 获取往期报告列表
        this.getWeeklyReportList()
      },
      /**
       * 获取往期报告列表
       */
      getWeeklyReportList() {
        this.isLoading = true
        weeklyReportList(this.pageNum++).then(res => {
          this.isLoading = false
          if (res.status === 0) {
            this.reportList = this.reportList.concat(res.data.items)
            // 如果当前页等于最后一页
            this.isLastReport = res.data.pager.current_page === res.data.pager.last_page
          } else {
            this.$toast(res.msg)
          }
        })
      },
      statusClass(num) {
        if (num === 1) {
          return 'colorNormal'
        } else if (num === 2) {
          return 'colorDown'
        } else if (num === 3) {
          return 'colorHigh'
        }
      },
      changeTab(index) {
        this.tabIndex = index
      },
      /**
       * 判断是否滑到底部
       */
      checkScroll() {
        let scrollTop = getScrollTop()
        window.onscroll = () => {
          let newSrollTop = getScrollTop() // 当前滚动高度
          let scrollHeight = getScrollHeight() // 文档完整的高度
          let clientHeight = getClientHeight() // 可视范围的高度
          let distance = 2 // 底部间距

          // 如果当前滚动高度大于之前滚动高度 -> 下滑
          if (newSrollTop > scrollTop) {
            // 如果到底部 this.isClientBottom = true；如果不在底部 this.isClientBottom = false
            if (newSrollTop + clientHeight + distance < scrollHeight) {
              // console.log('没到底')
              this.isClientBottom = false
            } else {
              // console.log('到底了')
              this.isClientBottom = true
            }
          } else {
            this.isClientBottom = false
          }

          // 每次滚动后，把当前滚动高度赋值给之前滚动高度
          scrollTop = newSrollTop
        }
      },
      /**
       * 查看详情
       */
      checkDetail(id) {
        this.$router.push({
          path: '/report/details',
          query: { id }
        })
      }
    }
  }
</script>
<style lang="scss" scoped>
  .content{
    padding: 15px;
    padding-bottom: 10px;
    ul{
      li{
        &:not(:first-of-type) {
          margin-top: 20px;
        }
        .date{
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          .date-icon{
            width: 18px;
            height: 18px;
            border-radius: 50%;
          }
          .colorHigh{
            background: #EF001D;
          }
          .colorDown{
            background: #FF9600;
          }
          .colorNormal{
            background: #07C160;
          }
          .date-txt {
            color: #666;
            font-size: 18px;
            font-weight: 400;
            margin-left: 4px;
          }
        }
        .box{
          background: #F5F6FA;
          padding: 15px 15px 0 15px;
          border-radius: 6px;
          .status{
            width: 100%;
            line-height: 28px;
            font-size: 20px;
            color: #000;
            text-align: left;
          }
          .details{
            width: 100%;
            line-height: 28px;
            font-size: 18px;
            color: #333;
            margin-top: 5px;
            text-align: left;
            padding-bottom: 5px;
            border-bottom: 1px solid #E8E8E8;
          }
          .lookDetails{
            width: 100%;
            height: 36px;
            line-height: 36px;
            font-size: 16px;
            color: #666;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .lookDetails-arrow {
              font-size: 14px;
              color: rgb(211, 208, 208);
            }
          }
        }
      }
    }
    .footer{
      height: 24px;
      line-height: 24px;
      margin-top: 10px;
      font-size: 14px;
      color: #999;

      .footer-loading {
        display: flex;
        align-items: center;
        justify-content: center;

        .footer-icon {
          margin-left: 4px;
        }
      }
    }
  }
</style>
