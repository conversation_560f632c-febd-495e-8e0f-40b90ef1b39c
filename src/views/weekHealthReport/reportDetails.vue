<template>
  <div class="content">
    <div class="blood">
      <div class="bloodTitle">
        <span class="name">{{ dateRange }}</span>
        <!-- <span class="before">往期周报<img src="./../../assets/images/<EMAIL>"></span> -->
      </div>
      <div :class="['reportStatus', allBg(descStatus)]">
        <p class="report-title">{{ desc.title }}</p>
        <p class="report-content">{{ desc.content }}</p>
      </div>
      <div class="sugar" @click="jumpBgTo(reportId, bloodSugarStatus)">
        <p :class="['p1', sugarBg(bloodSugarStatus)]"></p>
        <div class='p2'>
          <span class="title">血糖</span>
          <p>
            <span :class="sugarColor(bloodSugarStatus)">控制{{ bloodSugarStatusText }}</span>
            <img v-if="bloodSugarStatus === 1" src="./../../assets/images/<EMAIL>"/>
            <img v-if="bloodSugarStatus === 2" src="./../../assets/images/<EMAIL>"/>
            <img v-if="bloodSugarStatus === 3 || bloodSugarStatus === 4" src="./../../assets/images/<EMAIL>"/>
          </p>
        </div>
        <p class='p3'>
          <span>{{ bloodSugarQualifiedRatio }}</span><span>%</span>
          <span>达标比例</span>
        </p>
        <p class='p4'>
          <span>{{ bloodSugarAbnormalTimes }}</span>
          <span>异常次数</span>
        </p>
      </div>
      <div class="bloods" @click="jumpBpTo(reportId, pressureStatus)">
        <p :class="['p1', bloodBg(pressureStatus)]"></p>
        <div class='p2'>
          <span class="title">血压<i>（平均值）</i></span>
          <p>
            <span :class="bloodColor(pressureStatus)">控制{{ pressureStatusText }}</span>
            <img v-if="pressureStatus === 1" src="./../../assets/images/<EMAIL>"/>
            <img v-if="pressureStatus === 2" src="./../../assets/images/<EMAIL>"/>
          </p>
        </div>
        <p class='p3'>
          <span>{{ avgSystolicPressure }}</span><span></span>
          <span>收缩压</span>
        </p>
        <p class='p4'>
          <span>{{ avgDiastolicPressure }}</span>
          <span>舒张压</span>
        </p>
      </div>
      <!-- 生活方式建议 -->
      <div class="advice">
        <p class="advice-title">生活方式建议</p>
        <div class="advice-box">
          <div v-if="lifestyleAdvice.food" class="advice-group">
            <img src="./../../assets/images/report-diet.png" class="advice-icon">
            <p class="advice-text">{{ lifestyleAdvice.food }}</p>
          </div>
          <div v-if="lifestyleAdvice.sport" class="advice-group">
            <img src="./../../assets/images/report-sport.png" class="advice-icon">
            <p class="advice-text">{{ lifestyleAdvice.sport }}</p>
          </div>
          <div v-if="lifestyleAdvice.note" class="advice-group">
            <img src="./../../assets/images/report-notice.png" class="advice-icon">
            <p class="advice-text">{{ lifestyleAdvice.note }}</p>
          </div>
          <div v-if="lifestyleAdvice.weight" class="advice-group">
            <img src="./../../assets/images/report-weight.png" class="advice-icon">
            <p class="advice-text">{{ lifestyleAdvice.weight }}</p>
          </div>
        </div>
      </div>
      <!-- 健康检查建议 -->
      <div v-if="healthAdvice.bp !== '' || healthAdvice.bg !== ''" class="advice">
        <p class="advice-title">健康检查建议</p>
        <div class="advice-box">
          <div v-if="healthAdvice.bg" class="advice-group">
            <img src="./../../assets/images/report-bg.png" class="advice-icon">
            <p class="advice-text">{{ healthAdvice.bg }}</p>
          </div>
          <div v-if="healthAdvice.bp" class="advice-group">
            <img src="./../../assets/images/report-bp.png" class="advice-icon">
            <p class="advice-text">{{ healthAdvice.bp }}</p>
          </div>
        </div>
      </div>
      <div class="consult">参考《2019家庭血压监测指南》和《中国2型糖尿病防治指南2017年版》得出标准结论。实际控制指标以医生建议为准。</div>
    </div>
  </div>
</template>

<script>
  import { weeklyReportDetail } from '@/api/weekHealthReport.js'
  import { Toast } from 'vant'

  export default {
    data() {
      return {
        reportId: '', // 周报的id
        dateRange: '', // 周报日期范围
        bloodSugarQualifiedRatio: '', // 血糖达标比例
        bloodSugarAbnormalTimes: '', // 血糖异常次数
        avgSystolicPressure: '', // 平均高血压
        avgDiastolicPressure: '', // 平均低血压
        pressureStatus: 1, // 血压状态（2种）：1理想，2不稳定，0代表无数据
        pressureStatusText: '', // 血压状态描述
        bloodSugarStatus: 1, // 血糖状态（4种）：1理想，2欠佳，3不理想，4不理想低血糖，0代表无数据
        bloodSugarStatusText: '', // 血糖状态描述
        bmiStatus: 1, // bmi状态（4种）：1正常，2消瘦，3过重，4肥胖
        desc: { // 测量结果
          title: '', // 标题
          content: '' // 内容
        },
        lifestyleAdvice: {
          food: '',
          note: '',
          sport: '',
          weight: ''
        },
        healthAdvice: {
          bg: '',
          bp: ''
        }
      }
    },
    computed: {
      /**
       * 血压和血糖整体状态
       * @return {Number} 状态（3种）：1 normal、2 high、3 down
       */
      descStatus() {
        let pressureStatus = this.pressureStatus
        let bloodSugarStatus = this.bloodSugarStatus
        let status = ''
        // 血压或血糖有无数据情况下
        if (pressureStatus === 0 || bloodSugarStatus === 0) {
          if (pressureStatus !== 0) {
            if (pressureStatus === 1) {
              status = 1
            } else if (pressureStatus === 2) {
              status = 3
            }
          }
          if (bloodSugarStatus !== 0) {
            if (bloodSugarStatus === 1) {
              status = 1
            } else if (bloodSugarStatus === 3 || bloodSugarStatus === 4) {
              status = 2
            } else if (bloodSugarStatus === 2) {
              status = 3
            }
          }
        } else {
          // 当血压和血糖都为 1 理想时，返回 1 normal；
          // 当血糖为 3 不理想、4 不理想低血糖时，返回 2 high；
          // 其他状态时返回 3 down
          if (pressureStatus === 1 && bloodSugarStatus === 1) {
            status = 1
          } else if (bloodSugarStatus === 3 || bloodSugarStatus === 4) {
            status = 2
          } else {
            status = 3
          }
        }
        return status
      }
    },
    created() {
      // 路由获取开始时间
      this.reportId = this.$route.query.id || ''
      // 初始化
      this.init()
    },
    methods: {
      /**
       * 初始化
       */
      init() {
        // 获取往期报告详细
        weeklyReportDetail(this.reportId).then(res => {
          if (res.status === 0) {
            // 处理报告详细数据
            this.handleData(res.data)
          } else {
            this.$toast(res.msg)
          }
        })
      },
      /**
       * 处理报告详细数据
       * @param {Object} data 详细数据
       */
      handleData(data) {
        // 日期格式化
        this.dateRange = data.week_start_date + '~' + data.week_end_date
        // 血糖达标比例
        this.bloodSugarQualifiedRatio = data.bg_qualified_ratio
        // 血糖异常次数
        this.bloodSugarAbnormalTimes = data.bg_abnormal_times
        // 平均高血压
        this.avgSystolicPressure = data.bp_avg_sbp
        // 平均低血压
        this.avgDiastolicPressure = data.bp_avg_dbp
        // 血压状态（2种）：1代表理想、2代表不稳定、0代表无数据
        this.pressureStatus = data.bp_status
        this.pressureStatusText = data.bp_status_text
        // 血糖状态（4种）：1理想、2欠佳、3不理想、4不理想低血糖、0代表无数据
        this.bloodSugarStatus = data.bg_status
        this.bloodSugarStatusText = data.bg_status_text
        // bmi状态（4种）：1正常、2消瘦、3过重、4肥胖
        this.bmiStatus = data.bmi_status
        // 测量结果标题
        this.desc.title = data.desc.title
        // 测量结果内容
        this.desc.content = data.desc.content
        // 生活方式建议
        this.lifestyleAdvice = data.lifestyle_advice
        // 健康检查建议
        this.healthAdvice = data.health_advice
      },
      allBg(num) {
        if (num === 1) {
          return 'normal'
        } else if (num === 2) {
          return 'high'
        } else if (num === 3) {
          return 'down'
        }
      },
      sugarBg(num) {
        if (num === 1) {
          return 'sugarnormal'
        } else if (num === 2) {
          return 'sugardown'
        } else if (num === 3 || num === 4) {
          return 'sugarhigh'
        } else if (num === 0) {
          return 'sugarno'
        }
      },
      sugarColor(num) {
        if (num === 1) {
          return 'colornormal'
        } else if (num === 2) {
          return 'colordown'
        } else if (num === 3 || num === 4) {
          return 'colorhigh'
        } else if (num === 0) {
          return 'colorno'
        }
      },
      bloodBg(num) {
        if (num === 1) {
          return 'bloodnormal'
        } else if (num === 2) {
          return 'bloodhigh'
        } else if (num === 0) {
          return 'bloodno'
        }
      },
      bloodColor(num) {
        if (num === 1) {
          return 'colornormal'
        } else if (num === 2) {
          return 'colorhigh'
        } else if (num === 0) {
          return 'colorno'
        }
      },
      jumpBgTo(reportId, status) {
        if (status === 0) {
          Toast({
            message: '暂无数据',
            type: 'html',
            forbidClick: true,
            duration: 1000
          })

          return false
        }

        this.$router.push({
          path: '/sugar/report',
          query: { reportId: reportId }
        })
      },
      jumpBpTo(reportId, status) {
        if (status === 0) {
          Toast({
            message: '暂无数据',
            type: 'html',
            forbidClick: true,
            duration: 1000
          })

          return false
        }

        this.$router.push({
          path: '/blood/report',
          query: { reportId: reportId }
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  @import '@/assets/scss/reportDetails.scss';
</style>
