<template>
  <div class="wrapper">
    <!-- 兑换模块 -->
    <div v-if="!redeemSuccess" class="activate">
      <img src="./images/bg.png" class="activate-bg">
      <!--<p class="activate-text">购买后会通过短信发送给您</p>-->
      <div class="activate-box">
        <van-field ref="redeemCode" @click="redeemCodeClicked"
          @focus="kefuFlag = false"
          @blur="kefuFlag = true"
          v-model="redeemCode"
          type="digit"
          class="activate-input"
        />
        <div class="activate-tips">
          <span>{{ activateTips }}</span>
        </div>
        <div @click="openCellOverlayFlag" class="activate-btn">验证兑换</div>
      </div>
      <div
        v-if="kefuFlag"
        @click="goKeFu"
        class="activate-bottom"
      >联系客服</div>
      <!-- 开通服务弹框 -->
      <van-overlay
        :show="cellOverlayFlag"
        @click="cellOverlayFlag = false"
      >
        <div class="cell-wrapper" @click.stop>
          <h1 class="cell-title">开通服务</h1>
          <div class="cell-group">
            <div class="cell-group-input-box">
              <img src="./images/<EMAIL>" class="cell-group-icon">
              <input
                type="text"
                name="username"
                v-model="username"
                placeholder="请输入使用人姓名"
                class="cell-group-input"
              >
            </div>
          </div>
          <div class="cell-group">
            <div class="cell-group-input-box">
              <img src="./images/<EMAIL>" class="cell-group-icon">
              <input
                type="cell"
                name="cell"
                v-model="cell"
                placeholder="请输入使用人手机号"
                class="cell-group-input"
              >
            </div>
          </div>
          <div class="cell-group">
            <div class="cell-group-input-box cell-group-input-verify">
              <div class="cell-group-input-verify-box">
                <input
                  type="cell"
                  name="verify"
                  v-model="verifyCode"
                  placeholder="请输入验证码"
                  class="cell-group-input"
                >
              </div>
              <div
                v-if="sendableFlag"
                @click="getVerifyCode"
                class="cell-group-input-verify-btn"
              >获取验证码</div>
              <div
                v-else
                class="cell-group-input-verify-btn cell-group-input-verify-btn-sended"
              >已发送{{ sendedSecond }}s</div>
            </div>
          </div>
          <div class="cell-group">
            <div class="cell-group-checkbox">
              <van-checkbox
                v-model="agreeProtocol"
                checked-color="#FFA63D"
              ></van-checkbox>
              <span @click="goUserProtocol" class="cell-group-checkbox-text">用户协议</span>
            </div>
          </div>
          <div @click="subscribeService" class="cell-bottom">开通</div>
          <div class="cell-close">
            <van-icon
              name="close"
              color="#ffffff"
              @click="cellOverlayFlag = false"
            />
          </div>
        </div>
      </van-overlay>
    </div>
    <!-- 开通成功模块 -->
    <div v-else class="success">
      <img src="./images/<EMAIL>" class="success-bg">
      <p class="success-tips">我们已经为您注册并开通了MMC管家<br>M90血糖管理计划</p>
      <div class="success-box">
        <a
          v-if="isIOS"
          class="success-box-download"
          href="https://itunes.apple.com/us/app/mmc管家/id1198430389?l=zh&ls=1&mt=8"
        >下载管家APP</a>
        <a
          v-if="isAndroid"
          class="success-box-download"
          href="https://download-zz-med-pro.oss-cn-hangzhou.aliyuncs.com/Mmc_Mobile_Android_Application/new/fuwuduihuan.apk"
        >下载管家APP</a>
        <p class="success-box-text">通过使用人手机号登录<br>点击“健康管家”进入管理计划</p>
      </div>
    </div>
  </div>
</template>

<script>
import {
  postRedeemCodeUse,
  postRedeemCodeSendMsg,
  postRedeemCodeCheck
} from '@/api/redeemCode'
import { getBanFontSize } from '@/utils/utils.js'

export default {
  data: () => {
    return {
      redeemCode: '', // 兑换码
      activateTips: '', // 兑换码提示语
      redeemSuccess: false, // 兑换成功标志
      cellOverlayFlag: false, // 开通服务
      cell: '', // 使用人手机号
      username: '', // 使用人姓名
      verifyCode: '', // 验证码
      agreeProtocol: '', // 是否同意用户协议
      isAndroid: false, // 安卓端
      isIOS: false, // iOS端
      kefuFlag: true, // 联系客服flag
      sendableFlag: true, // 可发送flag
      sendedSecond: 60 // 已发送倒计时
    }
  },
  created() {
    var userAgent = navigator.userAgent
		var isAndroid = userAgent.indexOf('Android') > -1 || userAgent.indexOf('Linux') > -1
		var isIOS = !!userAgent.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)
		if (isAndroid) this.isAndroid = true
    if (isIOS) this.isIOS = true
    // 判断是否安卓系统微信页面修改了字体
    getBanFontSize()
  },
  methods: {
    redeemCodeClicked() {
      if (this.$refs.redeemCode) {
        this.$refs.redeemCode.focus()
      }
    },
    /**
     * 打开 开通服务 弹框
     */
    openCellOverlayFlag() {
      if (!this.redeemCode) {
        this.$toast('请输入兑换码')
        return
      }

      // 清空 兑换码提示语
      this.activateTips = ''

      // 兑换码验证
      postRedeemCodeCheck(this.redeemCode).then(res => {
        if (res.status === 0) {
          this.cellOverlayFlag = true
        } else {
          this.activateTips = res.msg
        }
      })
    },
    /**
     * 获取验证码
     */
    getVerifyCode() {
      if (!this.cell) {
        this.$toast('请输入使用人手机号')
        return
      }

      // 发送短信
      postRedeemCodeSendMsg({ 'cell': this.cell, 'sign_type': 1 }).then(res => {
        if (res.status === 0) {
          this.$toast('验证码发送成功')
          this.sendableFlag = false
          let interval = setInterval(() => {
            this.sendedSecond--

            if (this.sendedSecond === 0) {
              this.sendableFlag = true
              this.sendedSecond = 60
              clearInterval(interval)
            }
          }, 1000)
        } else {
          this.$toast(res.msg)
        }
      })
    },
    /**
     * 开通服务
     */
    subscribeService() {
      if (!this.username) {
        this.$toast('请输入使用人姓名')
        return
      }
      if (!this.cell) {
        this.$toast('请输入使用人手机号')
        return
      }
      if (!this.verifyCode) {
        this.$toast('请输入验证码')
        return
      }
      if (!this.agreeProtocol) {
        this.$toast('请勾选用户协议')
        return
      }

      // 血糖兑换码兑换
      postRedeemCodeUse({
        name: this.username,
        cell: this.cell,
        redeem_code: this.redeemCode,
        verify_code: this.verifyCode
      }).then(res => {
        if (res.status === 0) {
          this.redeemSuccess = true
        } else {
          this.$toast(res.msg)
        }
      })
    },
    /**
     * 联系客服
     */
    goKeFu() {
      window.location.href = 'https://shop45922568.youzan.com/v3/im/index?c=wsc&v=2&kdt_id=45730400&target=%2F&reft=1597753198745&spm=f.84711518#/index'
    },
    /**
     * 用户协议
     */
    goUserProtocol() {
      window.location.href = 'https://patient-h5.zz-med.com/static/h5/iHECUserProtocol2.html'
    }
  }
}
</script>

<style lang="scss" scoped>
.wrapper {
  width: 100%;
  overflow: hidden;

  .activate {
    width: 100%;
    line-height: 0;
    position: relative;

    .activate-bg {
      width: 100%;
    }

    .activate-text {
      width: 100%;
      font-size: 18px;
      font-weight: 400;
      color: #FFFFFF;
      line-height: 24px;
      position: absolute;
      top: 142px;
      left: 50%;
      transform: translateX(-50%);
    }

    .activate-box {
      height: 461px;
      width: 375px;
      position: absolute;
      top: 100px;
      left: 50%;
      transform: translateX(-50%);
      background: url("./images/shuruk.png");
      background-size: cover;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      .activate-input {
        width: 258px;
        height: 43px;
        border-radius: 4px;
        background: rgba(239,243,255,1);
        margin-top: 100px;
      }

      .activate-tips {
        height: 30px;
        line-height: 30px;
        font-size: 14px;
        color: #FF1515;
      }

      .activate-btn {
        width: 258px;
        height: 46px;
        line-height: 46px;
        font-size: 19px;
        font-weight: 500;
        color: #391915;
        border-radius: 23px;
        background: linear-gradient(180deg, rgba(253, 177, 1, 1), rgba(251, 227, 15, 1));
      }
    }

    .activate-bottom {
      width: 64px;
      line-height: 16px;
      font-size: 14px;
      font-weight: 400;
      padding-bottom: 2px;
      color: #FFFFFF;
      position: absolute;
      left: 50%;
      bottom: 70px;
      transform: translateX(-50%);
      border-bottom: 1px solid #fff;
    }

    .cell-wrapper {
      width: 305px;
      height: 363px;
      border-radius: 5px;
      box-sizing: border-box;
      padding: 30px 24px 40px;
      background-color: #FFFFFF;
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);

      .cell-title {
        font-size: 18px;
        font-weight: 400;
        color: #333333;
        line-height: 19px;
      }

      .cell-group {
        width: 100%;
        height: 43px;
        margin-top: 11px;

        &:first-of-type {
          margin-top: 22px;
        }

        .cell-group-input-box {
          width: 100%;
          height: 43px;
          box-sizing: border-box;
          display: flex;
          flex-wrap: nowrap;
          align-items: center;
          justify-content: flex-start;
          background-color: #F0F4FF;

          .cell-group-icon {
            width: 16px;
            margin-left: 13px;
          }

          .cell-group-input {
            width: 100%;
            height: 43px;
            line-height: 43px;
            margin: 0;
            padding: 0;
            font-size: 16px;
            margin-left: 9px;
            color: #333333;
            background-color: #F0F4FF;

            &::placeholder {
              opacity: 0.8;
              line-height: 43px;
              font-size: 16px;
              font-weight: 400;
              color: #999999;
            }
          }
        }

        .cell-group-input-verify {
          background-color: #fff;
          justify-content: space-between;

          .cell-group-input-verify-box {
            width: 166px;
            height: 43px;
            background-color: #F0F4FF;

            .cell-group-input {
              width: 166px;
              height: 43px;
              box-sizing: border-box;
              margin: 0;
              padding-left: 13px;
            }
          }

          .cell-group-input-verify-btn {
            width: 87px;
            height: 43px;
            line-height: 43px;
            font-size: 14px;
            font-weight: 400;
            color: #FFFFFF;
            border-radius: 4px;
            background: #FFA63D;
          }

          .cell-group-input-verify-btn-sended {
            background: #666;
          }
        }

        .cell-group-checkbox {
          display: flex;
          align-items: center;

          .cell-group-checkbox-text {
            font-size: 15px;
            font-weight: 400;
            color: #FF9423;
            margin-left: 8px;
          }
        }
      }

      .cell-bottom {
        width: 258px;
        height: 46px;
        line-height: 46px;
        font-size: 19px;
        font-weight: 500;
        color: #391915;
        border-radius: 23px;
        background: linear-gradient(180deg, rgba(253, 177, 1, 1), rgba(251, 227, 15, 1));
      }

      .cell-close {
        position: absolute;
        bottom: -20px;
        left: 50%;
        transform: translate(-50%, 100%);
      }
    }
  }

  .success {
    width: 100%;
    line-height: 0;
    position: relative;

    .success-bg {
      width: 100%;
    }

    .success-tips {
      width: 100%;
      font-size: 18px;
      font-weight: 400;
      color: #FFFFFF;
      line-height: 24px;
      position: absolute;
      top: 173px;
      left: 50%;
      transform: translateX(-50%);
    }

    .success-box {
      position: absolute;
      top: 407px;
      left: 50%;
      transform: translateX(-50%);

      .success-box-download {
        width: 215px;
        height: 42px;
        line-height: 42px;
        display: block;
        font-size: 17px;
        font-weight: 400;
        color: #FFFFFF;
        border-radius: 20px;
        background: linear-gradient(45deg,rgba(235,104,43,1),rgba(238,132,50,1));
      }

      .success-box-text {
        font-size: 16px;
        font-weight: 400;
        color: #BB5929;
        margin-top: 35px;
        line-height: 23px;
      }
    }
  }
}
</style>
