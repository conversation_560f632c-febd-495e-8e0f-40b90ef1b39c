<template>
  <div class="wrapper" ref="wrapper">
    <div class="banner">
      <img src="./images/banner.png">
    </div>
    <div class="content">
      <div class="box" @click="gotoFirst">
        <div class="desc">
          <div class="first">代谢中心MMC</div>
          <div class="two">糖尿病周围神经病变症状评分表</div>
          <div class="three">MSS-DPN <span>去答题></span></div>
        </div>
      </div>
      <div class="pt20"></div>
      <div class="box2" @click="gotoSecond">
        <div class="desc">
          <div class="first">糖尿病周围神经病变</div>
          <div class="two">总体感觉性症状评分表</div>
          <div class="three">TSS-DPN <span>去答题></span></div>
        </div>
      </div>

    </div>

  </div>
</template>

<script>
  import topView from './components/top.vue'
  import currentIndex from './components/currentIndex'
  import radio from './components/radio'

  export default {
    data: () => {
      return {}
    },
    created() {

    },
    methods: {
      gotoFirst() {
        this.$router.push({
          path: '/com/diabetes/questionnaire/first'
        })
      },
      gotoSecond() {
        this.$router.push({
          path: '/com/diabetes/questionnaire/second'
        })
      }
    }
  }
</script>
<style lang="scss" scoped>
  .wrapper {
    width: 100%;
    .banner {
      img {
        height: 230px;
      }
    }
    .content {
      display: flex;
      justify-content: center;
      flex-direction: column;
      margin: 18px 20px;
      .pt20 {
        margin-top: 20px;
      }
      .box {
        width: 335px;
        height: 140px;
        margin: 0 auto;
        background: url('./images/q_two.png') no-repeat left center;
        background-size: 335px 140px;
        .desc {
          text-align: left;
          padding-left: 20px;
          padding-top: 20px;
          .first {
            font-size: 22px;
            font-weight: 500;
            color: #74491C;
            line-height: 30px;
          }
          .two {
            padding-top: 7px;
            font-size: 20px;
            font-weight: 400;
            color: #74491C;
            line-height: 30px;
          }
          .three {
            padding-top: 10px;
            font-size: 20px;
            font-weight: 600;
            color: #F3955F;
            line-height: 28px;
            span {
              padding: 2px 7px;
              width: 72px;
              height: 26px;
              background: linear-gradient(360deg, #FF8100 0%, #FD9A57 100%);
              border-radius: 19px;
              font-size: 14px;
              font-weight: 400;
              color: #FFFFFF;
              line-height: 28px;
            }
          }
        }
      }
      .box2 {
        width: 335px;
        height: 140px;
        margin: 0 auto;
        background: url('./images/q_one.png') no-repeat left center;
        background-size: 335px 140px;
        .desc {
          text-align: left;
          padding-left: 20px;
          padding-top: 20px;
          .first {
            font-size: 22px;
            font-weight: 500;
            color: #74491C;
            line-height: 30px;
          }
          .two {
            padding-top: 7px;
            font-size: 20px;
            font-weight: 400;
            color: #74491C;
            line-height: 30px;
          }
          .three {
            padding-top: 10px;
            font-size: 20px;
            font-weight: 600;
            color: #F28B7C;
            line-height: 28px;
            span {
              padding: 2px 7px;
              width: 72px;
              height: 26px;
              background: linear-gradient(360deg, #F05C41 0%, #FF8044 100%);
              border-radius: 19px;
              font-size: 14px;
              font-weight: 400;
              color: #FFFFFF;
              line-height: 28px;
            }
          }
        }
      }
    }

  }
</style>
