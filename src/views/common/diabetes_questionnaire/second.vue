<template>
  <div class="wrapper" ref="wrapper">
    <top :step="step" :total="total"></top>
    <div class="content">
      <div class="head">
        <!--<div class="tip"><span class="line"></span>{{ questionType |formatType}}</div>-->
        <current-index :step="step" :total="total"></current-index>
      </div>
      <div class="main">
        <div class="title" v-html="questionTitle">
        </div>
        <block v-if="step<=1">
          <div class="answer" v-if="questionType=='radio'">
            <radio v-model="questionList[step - 1].answer" :options="questionOption"></radio>
          </div>
          <div class="answer" v-if="questionType=='checkbox'">
            <checkbox v-model="questionList[step - 1].answer" @onChange="onChange" :options="questionOption"></checkbox>
          </div>
        </block>
        <block v-else>
          <div class="answer" v-if="questionType=='radio'">
            <radio v-model="subQuestionList[step - 2].answer" :input_val="subQuestionList[step - 2].num"
                   :options="questionOption"></radio>
          </div>
          <div class="answer" v-if="questionType=='checkbox'">
            <checkbox v-model="subQuestionList[step - 2].answer"
                      @onChange="onChange"
                      :options="questionOption"></checkbox>
          </div>
        </block>
      </div>
      <div class="footer">
        <div class="btn" v-if="step==total" @click="btnSave">
          <span>提交</span>
        </div>
        <div class="btn" v-else @click="btnNext">
          <span>下一题</span>
        </div>
      </div>
    </div>
  </div>

</template>

<script>
  import topView from './components/top.vue'
  import currentIndex from './components/currentIndex'
  import radio from './components/radio'
  import checkbox from './components/checkbox'

  export default {
    data: () => {
      return {
        step: 1,
        total: 1,
        answerList: [],
        questionList: [
          {
            'id': 1,
            'title': '您的不适主要表现为以下哪种类型（可多选）：',
            'answer': [],
            'type': 'checkbox',
            options: [{ value: 1, name: '麻木' },
              { value: 2, name: '疼痛（刺痛）' },
              { value: 3, name: '烧灼感' }, {
                value: 4,
                name: '感觉异常（如蚁走感）'
              }]
          }
        ],
        subQuestionList: []
      }
    },
    components: {
      'top': topView,
      'current-index': currentIndex,
      'radio': radio,
      'checkbox': checkbox
    },
    computed: {
      questionTitle() {
        if (this.step <= 1) {
          return this.questionList[this.step - 1].title
        } else {
          return this.subQuestionList[this.step - 2].title
        }
      },
      questionType() {
        if (this.step <= 1) {
          return this.questionList[this.step - 1].type
        } else {
          return this.subQuestionList[this.step - 2].type
        }
      },
      questionAnswer() {
        if (this.step <= 1) {
          return this.questionList[this.step - 1].answer
        } else {
          return this.subQuestionList[this.step - 2].answer
        }
      },
      questionOption() {
        if (this.step <= 1) {
          return this.questionList[this.step - 1].options
        } else {
          return this.subQuestionList[this.step - 2].options
        }
      }
    },
    filters: {
      formatType(val) {
        if (val === 'radio') {
          return '单选题'
        } else if (val === 'checkbox') {
          return '多选题'
        } else if (val === 'solid') {
          return '测量题'
        } else {
          return '其他'
        }
      }
    },
    created() {

    },
    methods: {
      onChange(val) {
        this.subQuestionList = []
        val.sort()
        this.total = 1 + val.length * 2
        val.sort()
        val.forEach((item) => {
          let name = ''
          if (item == 1) {
            name = '麻木'
          } else if (item == 2) {
            name = '疼痛（刺痛）'
          } else if (item == 3) {
            name = '烧灼感'
          } else {
            name = '感觉异常（如蚁走感）'
          }

          let question = [{
            'id': 1,
            'title': '针对<span class="title_tip">' + name + '</span>您感觉身体不适的程度是多少？',
            'answer': '',
            'num': '',
            'type': 'radio',
            options: [{ value: 1, name: '轻度' }, { value: 2, name: '中度' }, { value: 3, name: '重度' }]
          }, {
            'id': 2,
            'title': '针对<span class="title_tip">' + name + '</span>您的不适感平均一天之内的频次是多少',
            'answer': '',
            'num': '',
            'type': 'radio',
            options: [{ value: 1, name: '偶尔' }, { value: 2, name: '经常' }, { value: 3, name: '持续' }]
          }]
          this.subQuestionList = this.subQuestionList.concat(question)
          console.log(this.subQuestionList, 1234)
        })
      },
      btnNext() {
        this.noDoubleTap(() => {
          if (this.questionAnswer === '' || this.questionAnswer === [] || this.questionAnswer === undefined) {
            this.$toast('请填写答案')
          } else {
            this.step = this.step + 1
          }
        })
      },
      btnBefore() {
        this.noDoubleTap(() => {
          if (this.step > 1) {
            this.step = this.step - 1
          }
        })
      },
      btnSave() {
        if (this.questionAnswer === '' || this.questionAnswer === [] || this.questionAnswer === undefined) {
          this.$toast('请填写答案')
        } else {
          this.$toast('计算中')
          this.noDoubleTap(() => {
            let scroe = this.calc()
            this.$router.push({
              path: '/com/diabetes/questionnaire/success?type=2&score=' + scroe
            })
          })
        }
      },
      calc() {
        let res = 0
        for (var i = 0; i < this.subQuestionList.length; i += 2) {
          res = res + parseInt(this.subQuestionList[i].answer) + (parseInt(this.subQuestionList[i + 1].answer) - 1) * 0.33
        }
        return res
      }
    }
  }
</script>
<style lang="scss">
  .title .title_tip {
    font-weight: 500;
    color: #FF8100;
    font-size: 22px;
  }
</style>
<style lang="scss" scoped>
  .wrapper {
    padding-top: 48px;
    .head {
      display: flex;
      justify-content: center;
      padding: 20px 10px;
      border-bottom: 2px solid #E8E8E8;
      .tip {
        display: inline-block;
        font-size: 18px;
        font-weight: 600;
        color: #333333;
        line-height: 24px;
        &:before {
          margin-right: 5px;
          content: "-";
          background-color: #F3955F;
          color: #F3955F;
          line-height: 18px;
        }
        /*.line{*/
        /*margin-right: 2px;*/
        /*display: inline-block;*/
        /*width: 5px;*/
        /*height: 18px;*/
        /*padding-top: 3px;*/
        /*background: #F3955F;*/
        /*}*/
      }

    }
    .main {
      margin: 20px 15px;
      display: flex;
      flex-direction: column;
      .title {
        text-align: left;
        font-size: 18px;
        font-weight: 400;
        color: #333333;
        line-height: 25px;
        span {
          color: #FF8100;
          font-size: 22px;
        }
      }
      .answer {

      }
    }
    .footer {
      padding-top: 30px;
      padding-bottom: 20px;
      margin: 0 auto;
      display: flex;
      justify-content: center;
      .btn {
        width: 144px;
        height: 39px;
        background: linear-gradient(360deg, #FF8100 0%, #FD9A57 100%);
        border-radius: 20px;
        span {
          font-size: 18px;
          font-weight: 400;
          color: #FFFFFF;
          line-height: 25px;
        }
      }
    }

  }
</style>
