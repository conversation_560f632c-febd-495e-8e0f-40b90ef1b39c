<template>
  <div class="radio-wrapper">
    <van-radio-group v-model="value" @change="changeSelect">
      <block v-for="(item, index) in options">

        <van-radio :class="value==item.value?'active gird':' gird'" :name="item.value"
                   checked-color="#F3955F">
          <template #default="props" v-if="item.input!==true">
            <label class="lab">{{item.name}}</label>
          </template>
          <template #default="props" v-else>
            <label class="lab">{{item.name|FormatName}}</label>
          </template>
          <template #icon="props">
            <img class="img-icon" :src="props.checked ? activeIcon : inactiveIcon"/>
          </template>

        </van-radio>
        <div class="extend_input" v-if="value==item.value">
          <div :class="tabId==1?'active tab':'tab'" class="tab" @click="changeTab(1)">
            a.{{subTitle|FormatName}}
            <input v-if="tabId==1" @blur="onblur" class="van-field__control" type="tel" ref="currentInputVal"
                   v-model="inputVal">
            <input v-if="tabId!=1" @blur="onblur" class="van-field__control" type="tel" ref="currentInputVal">

            {{subTitle|FormatUnit}}
          </div>
          <div :class="tabId==2?'active tab':'tab'" @click="changeTab(2)">
            b.不清楚
          </div>
        </div>

      </block>

    </van-radio-group>
  </div>
</template>
<script>
  export default {
    props: {
      input_val: {
        default: ''
      },
      subTitle: {
        default: ''
      },
      tabId: {
        default: ''
      },
      value: {
        type: String
      },
      options: {
        type: Object,
        default: []
      }
    },
    watch: {
      value(val, oldVal) {
        if (val !== oldVal) {
          this.$emit('input', val)
        }
      },
      input_val(val, oldVal) {
        if (val !== oldVal) {
          this.$nextTick(() => {
            if (this.tabId == 1) {
              this.inputVal = val
            }
          })
        }
      },
      tabId(val, oldVal) {
        if (val !== oldVal) {
          this.$nextTick(() => {
            if (val == 1) {
              this.$nextTick(() => {
                this.$refs.currentInputVal[0].focus()
              })
            }
            if (val == '') {
              this.inputVal = ''
            }
          })
        }
      }
    },
    components: {},
    filters: {
      FormatName(val) {
        let list = val.split('|')
        return list[0]
      },
      FormatUnit(val) {
        let list = val.split('|')
        return list[1]
      }
    },
    data: () => {
      return {
        activeIcon: require('./../images/checked.png'),
        inactiveIcon: require('./../images/nochecked.png'),
        inputVal: ''
      }
    },
    created() {
      console.log(this.input_val, 'this.input_val')
    },
    mounted() {
      console.log(this.input_val, 'this.input_val222')
      this.inputVal = this.input_val
    },
    methods: {
      onblur() {
        this.$emit('onChange', this.inputVal)
      },
      changeTab(id) {
        if (id == 2) {
          this.inputVal = ''
          var newArr = this.options.filter(item => item.value === this.value)
          if (newArr.length > 0) {
            console.log(newArr[0].num, 1234)
            this.$emit('onChange', newArr[0].num)
          }
        } else {

          this.$nextTick(() => {
            this.$refs.currentInputVal[0].focus()
          })
        }
        this.tabId = id
        this.$emit('onChangeTab', id)
      },
      changeSelect(val) {
        if (this.tabId == 1) {
          this.$nextTick(() => {
            this.$refs.currentInputVal[0].focus()
          })
        }
      }
    }
  }
</script>
<style lang="scss" scoped>
  .radio-wrapper {
    .extend_input {
      font-size: 18px;
      font-weight: 400;
      color: #333333;
      line-height: 25px;
      display: inline;
      .tab {
        display: inline-block;
        background: #FAFBFB;
        border-radius: 4px;
        border: 1px solid #EEEEEE;
        padding: 11px 10px;
        margin-top: 12px;
        margin-left: 10px;
        input {
          outline: none;
          -webkit-appearance: none; /*去除系统默认的样式*/
          -webkit-tap-highlight-color: rgba(0, 0, 0, 0); /* 点击高亮的颜色*/
          display: inline-block;
          width: 50px;
          margin: 0 5px;
          background: #FAFBFB;
          border-radius: 4px;
          border: 1px solid #EEEEEE;
          padding: 2px 2px;
        }
      }
      .active {
        background: #FFF8F3;
        border: 1px solid #EEEEEE;
      }
    }
    .gird {
      background: #FAFBFB;
      border-radius: 4px;
      border: 1px solid #EEEEEE;
      padding: 11px 20px;
      margin-top: 12px;
      .lab {
        font-size: 18px;
        font-weight: 400;
        color: #333333;
        line-height: 25px;
        input {
          outline: none;
          -webkit-appearance: none; /*去除系统默认的样式*/
          -webkit-tap-highlight-color: rgba(0, 0, 0, 0); /* 点击高亮的颜色*/
          display: inline-block;
          width: 50px;
          margin: 0 5px;
          background: #FAFBFB;
          border-radius: 4px;
          border: 1px solid #EEEEEE;
          padding: 2px 2px;
        }
      }
      .lab_info {
        display: inline-block;
        .input {
          width: 20px;
        }
      }
      .activeIcon {
        width: 22px;
        height: 22px;
        background: #F3955F;
      }
      .inactiveIcon {
        width: 22px;
        height: 22px;
        background: #CCCCCC;
      }
    }
    .img-icon {
      width: 22px;
      height: 22px;
    }
    .active {
      background: #FFF8F3;
      border: 1px solid #EEEEEE;
    }

  }
</style>
