<template>
  <div class="top-wrapper">
    <div class="content">
      <div @click="leftClick" :class="leftDisable?'disable':''" style="margin-left: 16px;">
        <div class="imgs"><img
          :src="leftDisable?require('./../images/left.png'):require('./../images/left_two.png')"
          alt="">
        </div>
        <div class="infos">
          上一题
        </div>
      </div>
      <div @click="rightClick" :class="rightDisable?'disable':''" style="margin-right: 16px;">
        <div class="infos">
          下一题
        </div>
        <div class="imgs"><img
          :src="rightDisable?require('./../images/right.png'):require('./../images/right_two.png')" alt="">
        </div>
      </div>
    </div>
  </div>
</template>
<script>
  export default {
    props: {
      step: {
        type: Number,
        default: 1
      },
      total: {
        type: Number,
        default: 3
      }
    },
    components: {},
    computed: {
      leftDisable() {
        if (this.step <= 1) {
          return true
        } else {
          return false
        }
      },
      rightDisable() {
        if (this.step >= this.total) {
          return true
        } else {
          return false
        }
      }
    },
    created() {
    },
    methods: {
      leftClick() {
        if (!this.leftDisable) {
          this.$parent.btnBefore()
        }
      },
      rightClick() {
        if (!this.rightDisable) {
          this.$parent.btnNext()
        }
      }
    }
  }
</script>
<style lang="scss" scoped>
  .top-wrapper {
    .content {
      display: flex;
      justify-content: space-between;
      position: absolute;
      align-items: center;
      width: 100%;
      top: 0;
      height: 48px;
      background: #F5F6FA;
      color: #666666;
      .imgs {
        display: inline-block;
        vertical-align: middle;
        img:first-child {
          margin-top: 5px;
          width: 28px;
          height: 28px;
          margin-left: 5px;

        }
        img:last-child {
          width: 28px;
          height: 28px;
          margin-right: 5px;
        }
      }
      .infos {
        display: inline-block;
        font-size: 16px;
        font-weight: 400;
        line-height: 22px;
      }
      .disable {
        color: #CCCCCC;
      }
    }
  }
</style>
