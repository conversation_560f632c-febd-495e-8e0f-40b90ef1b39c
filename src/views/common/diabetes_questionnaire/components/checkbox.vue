<template>
  <div class="checkbox-wrapper">
    <van-checkbox-group v-model="value">
      <van-checkbox shape="square"  :class="value==item.value?'active gird':' gird'" :name="item.value" checked-color="#F3955F" v-for="(item, index) in options">
        <template #default="props">
          <label class="lab">{{item.name}}</label>
        </template>
        <!--<template #icon="props">-->
        <!--<div :class="props.checked ? activeIcon : inactiveIcon"> </div>-->
        <!--</template>-->
      </van-checkbox>
    </van-checkbox-group>
  </div>
</template>
<script>
  export default {
    props: {
      value: {
        type: Object,
        default: []
      },
      options: {
        type: Object,
        default: []
      }
    },
    watch: {
      value(val, oldVal) {
        if (val !== oldVal) {
          this.$emit('input', val)
          this.$emit('onChange', val)
        }
      }
    },
    components: {},
    created() {

    },
    methods: {}
  }
</script>
<style lang="scss" scoped>
.checkbox-wrapper {
  .gird {
    background: #fafbfb;
    border-radius: 4px;
    border: 1px solid #eeeeee;
    padding: 11px 20px;
    margin-top: 12px;
    .lab {
      font-size: 18px;
      font-weight: 400;
      color: #333333;
      line-height: 25px;
    }
    .lab_info {
      display: inline-block;
      .input {
        width: 20px;
      }
    }
    .activeIcon {
      width: 22px;
      height: 22px;
      background: #f3955f;
    }
    .inactiveIcon {
      width: 22px;
      height: 22px;
      background: #cccccc;
    }
  }
  .active {
    background: #fff8f3;
    border: 1px solid #eeeeee;
  }
}
</style>
