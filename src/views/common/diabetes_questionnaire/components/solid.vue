<template>
  <div class="solid-wrapper">
    <div style="padding-top: 20px"></div>
    <div class="box">
      <div class="line_img">
        <van-row type="flex" align="middle" style="display: flex;align-items: center;">
          <van-col span="4"><img :class="value==0?'active':''" src="./../images/xiaolian1.png" alt="">
          </van-col>
          <van-col span="4"><img :class="(value==1 ||value==2) ?'active':''"
                                 src="./../images/xiaolian2.png" alt="">
          </van-col>
          <van-col span="4"><img :class="(value==3 ||value==4) ?'active':''" src="./../images/xiaolian3.png" alt="">
          </van-col>
          <van-col span="4"><img :class="(value==5 ||value==6) ?'active':''" src="./../images/xiaolian4.png" alt="">
          </van-col>
          <van-col span="4"><img :class="(value==7 ||value==8) ?'active':''" src="./../images/xiaolian5.png" alt="">
          </van-col>
          <van-col span="4"><img :class="(value==9 ||value==10) ?'active':''" src="./../images/xiaolian6.png" alt="">
          </van-col>
        </van-row>
      </div>
      <div>
        <div class="progress pt20">
          <div class="solider_button" v-bind:style="{ left: left+distanceX +'px' }" v-on:touchstart="bodyTouchStart"
               v-on:touchmove="bodyTouchMove"
               v-on:touchend="bodyTouchEnd">
            <div class="btn">
              <span>{{value}}</span>
            </div>
          </div>
          <div class="progress-box" ref="progressBox">
            <div class="progress-abnormal">
              0
            </div>
            <div class="progress-abnormal">
              1
            </div>
            <div class="progress-abnormal ">
              2
            </div>
            <div class="progress-abnormal">
              3
            </div>
            <div class="progress-abnormal">
              4
            </div>
            <div class="progress-abnormal">
              5
            </div>
            <div class="progress-abnormal">
              6
            </div>
            <div class="progress-abnormal">
              7
            </div>
            <div class="progress-abnormal">
              8
            </div>
            <div class="progress-abnormal">
              9
            </div>
            <div class="progress-abnormal">
              10
            </div>
          </div>
        </div>
      </div>
      <div class="success pt20">
        “0”表示：无症状
      </div>
      <div class="error pt10">
        “10”表示：您能想像的最严重症状
      </div>
    </div>
  </div>
</template>
<script>
  export default {
    data: () => {
      return {
        value: '0',
        left: 0,
        distanceX: 0,
        oneWidth: 0,
        progressBox: 0
      }
    },
    watch: {
      value(val, oldVal) {
        if (val !== oldVal) {
          this.$emit('input', val)
        }
      }
    },
    components: {},
    created() {
      this.$nextTick(() => {
        this.progressBox = this.$refs.progressBox.clientWidth
        this.oneWidth = (this.progressBox - 5) / 10
        this.left = 2
      })
    },
    methods: {
      bodyTouchStart: function (event) {
        // 获得起点X坐标，初始化distance为0
        this.touchStartPointX = event.targetTouches[0].pageX
        this.touchStartPointY = event.targetTouches[0].pageY
        this.distanceX = 0
        this.distanceY = 0
        console.log(this.distanceX, this.touchStartPointX, 12341324)
      },
      bodyTouchMove: function (event) {
        console.log(222)
        if (this.left + this.distanceX <= this.progressBox && this.left + this.distanceX > 0) {
          // 只监听单指划动，多指划动不作响应
          if (event.targetTouches.length > 1) {
            return
          }
          // console.log(event.targetTouches[0].pageX);
          // 实时计算distance
          this.distanceX = event.targetTouches[0].pageX - this.touchStartPointX
          this.distanceY = event.targetTouches[0].pageY - this.touchStartPointY
        }

        // 根据distance在页面上做出反馈。这里演示通过返回按钮的背景变化作出反馈
      },
      bodyTouchEnd: function (event) {
        console.log(333)

        if (this.left + this.distanceX >= this.progressBox - 28) {
          this.left = this.progressBox - 28
          this.value = 10
        } else if (this.left + this.distanceX <= 2) {
          this.left = 2
          this.value = 0
        } else {
          this.left = this.left + Math.round(this.distanceX / this.oneWidth) * (this.oneWidth - 3)
          this.value = parseInt(this.value) + parseInt(Math.round(((this.distanceX / this.oneWidth))))
        }

        this.distanceX = 0
        this.distanceY = 0
        console.log(this.distanceX, this.distanceY)
      }
    }
  }
</script>
<style lang="scss" scoped>
  .solid-wrapper {
    .pt20 {
      padding-top: 20px;
    }
    .box {
      padding: 20px;
      background: #F5F6FA;
      border-radius: 4px;
      text-align: left;
      .line_img {
        text-align: center;
        width: 100%;
        height: 46px;
        img {
          width: 26px;
          height: 26px;
        }
        .active {
          width: 46px;
          height: 46px;
        }
      }
      .progress {
        position: relative;
        .solider_button {
          position: absolute;
          top: 12px;
          .btn {
            background: url('./../images/solid_button.png');
            background-size:30px 40px;
            background-repeat:no-repeat;
            display: block;
            width: 30px;
            height: 40px;
            display: flex;
            justify-content: center;
            align-items: center;
            span {
              font-size: 14px;
              font-weight: 600;
              color: #787878;
              line-height: 20px;
            }
          }
        }
        .progress-box {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          height: 21px;
          line-height: 21px;
          background: rgba(121, 177, 234, 1);
          background: linear-gradient(90deg, #C3EBCB 0%, #FFCE41 51%, #FFA55E 75%, #FF7B7B 100%);
          border-radius: 10px;
          width: 100%;
          .progress-abnormal {
            text-align: center;
            width: 50%;
            height: 21px;
            line-height: 21px;
            font-size: 16px;
            font-weight: 400;
            color: #fff;
          }
        }
        .progress-value {
          width: 21px;
          height: 25px;
          display: block;
          background: url('~@/assets/images/biaochi.png') no-repeat;
          background-size: 21px 25px;
          position: absolute;
          top: 0;
          z-index: 1;
        }
      }
      .success {
        font-size: 14px;
        font-weight: 400;
        color: #11B240;
        line-height: 20px;
      }
      .error {
        font-size: 14px;
        font-weight: 400;
        color: #EB6048;
        line-height: 20px;
      }
    }

  }
</style>
