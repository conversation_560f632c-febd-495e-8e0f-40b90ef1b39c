<template>
  <div class="currentIndex_wrapper">
    <div class="content">
      <span class="current">{{step}}</span>/{{total}} 基础题
    </div>
  </div>
</template>
<script>
  export default {
    props: {
      step: {
        type: Number,
        default: 1
      },
      total: {
        type: Number,
        default: 3
      }
    },
    components: {},
    created() {

    },
    methods: {}
  }
</script>
<style lang="scss" scoped>
  .currentIndex_wrapper {
    display: block;
    .content {
      font-size: 15px;
      font-weight: 500;
      color: #999999;
      line-height: 24px;
      .current {
        color: #333333;
        font-weight: 600;
        font-size: 23px;
      }
    }
  }
</style>
