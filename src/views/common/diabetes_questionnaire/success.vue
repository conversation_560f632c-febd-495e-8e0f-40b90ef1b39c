<template>
  <div class="wrapper" ref="wrapper">
    <div class="banner">
      <img v-if="type==1" src="./images/success_banner_one.png">
      <img v-if="type==2" src="./images/success_banner_tow.png">
      <div class="process" ref="progressBox" v-if="type==1">
        <div class="inner">
          <img :style="{ left: left+'px' }" src="./images/solid_button.png" alt="">
        </div>
        <div class="tip">0</div>
        <div class="tip">100</div>
      </div>
      <span>{{score}}分</span>
    </div>
    <div class="content">
      <div class="box" @click="gotoFirst" v-if="type==2">
        <div class="desc">
          <div class="first">代谢中心MMC</div>
          <div class="two">糖尿病周围神经病变症状评分表</div>
          <div class="three">MSS-DPN <span>去答题></span></div>
        </div>
      </div>
      <div class="box2" @click="gotoSecond" v-if="type==1">
        <div class="desc">
          <div class="first">糖尿病周围神经病变</div>
          <div class="two">总体感觉性症状评分表</div>
          <div class="three">TSS-DPN <span>去答题></span></div>
        </div>
      </div>

    </div>

  </div>
</template>

<script>
  import topView from './components/top.vue'
  import currentIndex from './components/currentIndex'
  import radio from './components/radio'

  export default {
    data: () => {
      return {
        type: 1,
        score: 0,
        left: 0
      }
    },
    created() {
      this.type = this.$route.query.type || 1
      this.score = this.$route.query.score || ''

      if (this.type == 1) {
        this.$nextTick(() => {
          this.progressBox = this.$refs.progressBox.clientWidth
          this.left = parseInt((this.score / 100) * this.progressBox)
        })
      }
    },
    methods: {
      gotoFirst() {
        this.$router.push({
          path: '/com/diabetes/questionnaire/first'
        })
      },
      gotoSecond() {
        this.$router.push({
          path: '/com/diabetes/questionnaire/second'
        })
      }
    }
  }
</script>
<style lang="scss" scoped>
  .wrapper {
    width: 100%;
    .banner {
      img {
        width: 100%;
        height: 230px;
      }
      position: relative;
      span {
        position: absolute;
        font-size: 40px;
        color: #fff;
        z-index: 4;
        top: 119px;
        left: 30px;
        font-weight: 700;
      }
      .process {
        position: absolute;
        top: 169px;
        left: 30px;
        width: 160px;
        height: 9px;
        background: linear-gradient(90deg, #C3EBCB 0%, #FFCE41 51%, #FFA55E 75%, #FF7B7B 100%);
        border-radius: 16px;
        border: 1px solid #FFFFFF;
        .inner {
          position: relative;
          img {
            position: absolute;
            top: 2px;
            width: 13px;
            height: 20px;
          }
        }
        .tip {
          display: inline-block;
          font-size: 14px;
          font-weight: 400;
          color: #FFFFFF;
          line-height: 20px;
          padding-top: 15px;
          float: left;
        }
        .tip:last-child {
          float: right;
        }

      }
    }
    .content {
      display: flex;
      justify-content: center;
      flex-direction: column;
      margin: 18px 20px;
      .pt20 {
        margin-top: 20px;
      }
      .box {
        width: 335px;
        height: 140px;
        margin: 0 auto;
        background: url('./images/q_two.png') no-repeat left center;
        background-size: 335px 140px;
        .desc {
          text-align: left;
          padding-left: 20px;
          padding-top: 20px;
          .first {
            font-size: 22px;
            font-weight: 500;
            color: #74491C;
            line-height: 30px;
          }
          .two {
            padding-top: 7px;
            font-size: 20px;
            font-weight: 400;
            color: #74491C;
            line-height: 30px;
          }
          .three {
            padding-top: 10px;
            font-size: 20px;
            font-weight: 600;
            color: #F3955F;
            line-height: 28px;
            span {
              padding: 2px 7px;
              width: 72px;
              height: 26px;
              background: linear-gradient(360deg, #FF8100 0%, #FD9A57 100%);
              border-radius: 19px;
              font-size: 14px;
              font-weight: 400;
              color: #FFFFFF;
              line-height: 28px;
            }
          }
        }
      }
      .box2 {
        width: 335px;
        height: 140px;
        background: url('./images/q_one.png') no-repeat left center;
        background-size: 335px 140px;
        .desc {
          text-align: left;
          padding-left: 20px;
          padding-top: 20px;
          .first {
            font-size: 22px;
            font-weight: 500;
            color: #74491C;
            line-height: 30px;
          }
          .two {
            padding-top: 7px;
            font-size: 20px;
            font-weight: 400;
            color: #74491C;
            line-height: 30px;
          }
          .three {
            padding-top: 10px;
            font-size: 20px;
            font-weight: 600;
            color: #F28B7C;
            line-height: 28px;
            span {
              padding: 2px 7px;
              width: 72px;
              height: 26px;
              background: linear-gradient(360deg, #F05C41 0%, #FF8044 100%);
              border-radius: 19px;
              font-size: 14px;
              font-weight: 400;
              color: #FFFFFF;
              line-height: 28px;
            }
          }
        }
      }
    }

  }
</style>
