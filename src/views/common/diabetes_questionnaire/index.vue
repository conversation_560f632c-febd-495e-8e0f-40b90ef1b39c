<template>
  <div class="wrapper" ref="wrapper">
    <top :step="step" :total="total"></top>
    <div class="content">
      <div class="head">
        <!--<div class="tip"><span class="line"></span>{{ questionType |formatType}}</div>-->
        <current-index :step="step" :total="total"></current-index>
      </div>
      <div class="main">
        <div class="title" v-html="questionTitle">
        </div>
        <block v-if="step<=3">
          <div class="answer" v-if="questionType=='solid'">
            <solid></solid>
          </div>
          <div class="answer" v-if="questionType=='radio'">
            <radio v-model="questionList[step - 1].answer" :options="questionOption"></radio>
          </div>
          <div class="answer" v-if="questionType=='radioInput'">
            <radio-input v-model="questionList[step - 1].answer"
                         :input_val="questionList[step - 1].num" :subTitle="questionList[step - 1].subTitle"
                         :tabId="questionList[step - 1].tabId"
                         :options="questionOption" @onChangeTab="onChangeTab"></radio-input>
          </div>
          <div class="answer" v-if="questionType=='checkbox'">
            <checkbox v-model="questionList[step - 1].answer" @onChange="onChange" :options="questionOption"></checkbox>
          </div>
        </block>
        <block v-else>
          <div class="answer" v-if="questionType=='solid'">
            <solid v-model="subQuestionList[step - 4].answer"></solid>
          </div>
          <div class="answer" v-if="questionType=='radio'">
            <radio v-model="subQuestionList[step - 4].answer" @onChange="onChange2"
                   :input_val="subQuestionList[step - 4].num" :options="questionOption"></radio>
          </div>

          <div class="answer" v-if="questionType=='radioInput'">
            <radio-input v-model="subQuestionList[step - 4].answer"
                         :input_val="subQuestionList[step - 4].num" :subTitle="subQuestionList[step - 4].subTitle"
                         :tabId="subQuestionList[step - 4].tabId" @onChange="onChange2"
                         :options="questionOption" @onChangeTab="onChangeTab"></radio-input>
          </div>
          <div class="answer" v-if="questionType=='checkbox'">
            <checkbox v-model="subQuestionList[step - 4].answer" @onChange="onChange"
                      :options="questionOption"></checkbox>
          </div>
        </block>
      </div>
      <div class="footer">
        <div class="btn" v-if="step==total" @click="btnSave">
          <span>提交</span>
        </div>
        <div class="btn" v-else @click="btnNext">
          <span>下一题</span>
        </div>
      </div>
    </div>
  </div>

</template>

<script>
  import topView from './components/top.vue'
  import currentIndex from './components/currentIndex'
  import radio from './components/radio'
  import radioInput from './components/radioInput'
  import checkbox from './components/checkbox'
  import solid from './components/solid'

  export default {
    data: () => {
      return {
        step: 1,
        cacl: [{value: 20, score: [1.45, 2.80, 4.05, 5.20, 6.25, 7.20, 8.05, 8.80, 9.45, 10.00]},
          {value: 50, score: [2.05, 3.96, 5.73, 7.35, 8.84, 10.18, 11.38, 12.45, 13.36, 14.14]},
          {value: 100, score: [2.51, 4.85, 7.01, 9.01, 10.83, 12.47, 13.94, 15.24, 16.37, 17.32]},
          {
            value: 180, score: [2.90, 5.60, 8.10, 10.40, 12.50, 14.40, 16.10, 17.6, 18.90, 20.00]
          },
          {value: 240, score: [3.24, 6.26, 9.02, 11.63, 13.98, 16.10, 18.00, 19.68, 21.13, 22.36]},
          {
            value: 360,
            score: [3.55, 6.86, 9.92, 12.74, 15.31, 17.64, 19.72, 21.56, 23.15, 24.49]
          },
          {
            value: 480,
            score: [3.84, 7.41, 10.72, 13.76, 16.54, 19.05, 21.30, 23.28, 25.00, 26.46]
          }, {
            value: 720,
            score: [4.10, 7.92, 11.46, 14.71, 17.68, 20.36, 22.77, 24.89, 26.73, 28.28]
          }, {
            value: 960,
            score: [4.35, 8.4, 12.15, 15.60, 18.75, 21.60, 24.15, 26.40, 28.35, 30.30]
          }, {
            value: 10000000000000,
            score: [4.59, 8.85, 12.82, 16.44, 19.76, 22.77, 25.46, 27.83, 29.88, 31.62]
          }],
        total: 3,
        answerList: [],
        questionList: [
          {
            'id': 1,
            'title': '您的症状存在于身体双侧还是单侧的：',
            'answer': '',
            'type': 'radio',
            options: [{value: 1, name: '双侧'}, {value: 2, name: '单侧'}]
          },
          {
            'id': 2,
            'title': '感觉<span class="title_tip">最</span>不舒服的位置：',
            'answer': '',
            'type': 'radio',
            options: [{value: 1, name: '上肢'}, {value: 2, name: '手'}, {value: 3, name: '下肢'}, {value: 4, name: '足'}]
          },
          {
            'id': 3,
            'title': '您的不适主要表现为以下哪种类型（可多选）：',
            'answer': [],
            'type': 'checkbox',
            options: [{value: 1, name: '麻木'}, {value: 2, name: '疼痛（刺痛）'}, {value: 3, name: '烧灼感'}, {
              value: 4,
              name: '感觉异常（如蚁走感）'
            }]
          }
        ],
        subQuestionList: []
      }
    },
    components: {
      'top': topView,
      'current-index': currentIndex,
      'radio-input': radioInput,
      'radio': radio,
      'solid': solid,
      'checkbox': checkbox
    },
    computed: {
      questionTitle() {
        if (this.step <= 3) {
          return this.questionList[this.step - 1].title
        } else {
          return this.subQuestionList[this.step - 4].title
        }
      },
      questionType() {
        if (this.step <= 3) {
          return this.questionList[this.step - 1].type
        } else {
          return this.subQuestionList[this.step - 4].type
        }
      },
      questionTabId() {
        if (this.step <= 3) {
          return this.questionList[this.step - 1].tabId
        } else {
          return this.subQuestionList[this.step - 4].tabId
        }
      },
      questionNum() {
        if (this.step <= 3) {
          return this.questionList[this.step - 1].num
        } else {
          return this.subQuestionList[this.step - 4].num
        }
      },
      questionAnswer() {
        if (this.step <= 3) {
          return this.questionList[this.step - 1].answer
        } else {
          return this.subQuestionList[this.step - 4].answer
        }
      },
      questionOption() {
        if (this.step <= 3) {
          return this.questionList[this.step - 1].options
        } else {
          return this.subQuestionList[this.step - 4].options
        }
      }
    },
    filters: {
      formatType(val) {
        if (val === 'radio') {
          return '单选题'
        } else if (val === 'checkbox') {
          return '多选题'
        } else if (val === 'solid') {
          return '测量题'
        } else {
          return '其他'
        }
      }
    },
    created() {

    },
    methods: {
      onChange2(val) {
        if (this.subQuestionList[this.step - 4] !== undefined) {
          console.log(val, 'onChange2')
          this.subQuestionList[this.step - 4].num = val
          console.log(this.subQuestionList, 1234)
        }
      },
      onChangeTab(val) {
        if (this.subQuestionList[this.step - 4] !== undefined) {
          this.subQuestionList[this.step - 4].tabId = val
        }
      },
      onChange(val) {
        console.log(val.length, 1234)
        this.subQuestionList = []
        this.total = 3 + val.length * 3
        val.sort()
        val.forEach((item) => {
          let name = ''
          if (item == 1) {
            name = '麻木'
          } else if (item == 2) {
            name = '疼痛（刺痛）'
          } else if (item == 3) {
            name = '烧灼感'
          } else {
            name = '感觉异常（如蚁走感）'
          }

          let question = [
            {
              'id': 1,
              'title': '请参照以下10个不同的程度，对您的<span class="title_tip">' + name + '</span>感觉进行评分',
              'answer': 0,
              'num': '',
              'type': 'solid'
            }, {
              'id': 2,
              'title': '您的<span class="title_tip">' + name + '</span>平均一天之内的频次是多少',
              'answer': '',
              'num': '',
              'type': 'radioInput',
              'tabId': '',
              'subTitle': '具体次数：|次',
              options: [{value: 1, num: 2, name: '1-4次/天'}, {
                value: 2,
                num: 7.5,
                name: '5-10次/天'
              }, {value: 3, num: 15, name: '11-20次/天'}, {
                value: 4,
                num: 25,
                name: '21-30次/天'
              }, {value: 5, num: 35, name: '>30或持续存在(可不包含入睡后)'}]
            }, {
              'id': 2,
              'title': '您的<span class="title_tip">' + name + '</span>平均每次持续的时间是多少',
              'answer': '',
              'num': '',
              'type': 'radioInput',
              'tabId': '',
              'subTitle': '具体时间：|分',
              options: [{value: 1, num: 1, name: '<1 min/次'}, {
                value: 2,
                num: 2.5,
                name: '1-5 min/次'
              }, {value: 3, num: 7.5, name: '5-10 min/次'}, {
                value: 4,
                num: 30,
                name: '10-60 min/次'
              }, {value: 5, num: 90, name: '60-120 min/次'}, {value: 6, num: 135, name: '>120 min/次'}]
            }]
          this.subQuestionList = this.subQuestionList.concat(question)
        })
      },
      btnNext() {
        this.noDoubleTap(() => {
          if (this.questionType == 'radioInput') {
            if (this.questionAnswer === '' || this.questionAnswer === [] || this.questionAnswer === undefined || this.questionTabId === '' || this.questionNum === '') {
              this.$toast('请填写答案')
            } else {
              this.step = this.step + 1
            }
          } else {
            if (this.questionAnswer === '' || this.questionAnswer === [] || this.questionAnswer === undefined) {
              this.$toast('请填写答案')
            } else {
              this.step = this.step + 1
            }
          }
        })
      },
      btnBefore() {
        this.noDoubleTap(() => {
          if (this.step > 1) {
            this.step = this.step - 1
          }
        })
      },
      btnSave() {
        if (this.questionAnswer === '' || this.questionAnswer === [] || this.questionAnswer === undefined) {
          this.$toast('请填写答案')
        } else {
          this.$toast('计算中')
          this.noDoubleTap(() => {
            let score = this.calc()
            let one = 0
            let two = 0
            for (var i = 0; i < 4; i += 1) {
              if (score[i] !== undefined) {
                one = one + score[i].score * (score[i].score + 20)
                two = two + score[i].score
              } else {
                one = one + 0
                two = two + 0
              }
            }
            let resScore = (one / (two + 80)) * Math.sqrt(10)
            console.log(resScore, 134)
            this.$router.push({
              path: '/com/diabetes/questionnaire/success?type=1&score=' + resScore.toFixed(2)
            })
          })
        }
      },
      calc() {
        let res = []
        for (var i = 0; i < this.subQuestionList.length; i += 3) {
          let min = this.subQuestionList[i + 1].num * this.subQuestionList[i + 2].num
          let score = 0
          for (var j = 0; j < this.cacl.length; j += 1) {
            console.log(this.cacl[j].value, 1341324)
            if (this.cacl[j].value > min) {
              if (parseInt(this.subQuestionList[i].answer) > 0) {
                score = this.cacl[j].score[parseInt(this.subQuestionList[i].answer) - 1]
              }
              break
            }
          }
          res.push({
            score: score,
            min: min,
            cs: this.subQuestionList[i].answer
          })
        }
        console.log(res, 134)
        return res
      }
    }
  }
</script>
<style lang="scss">
  .title .title_tip {
    font-weight: 500;
    color: #FF8100;
    font-size: 22px;
  }
</style>
<style lang="scss" scoped>
  .wrapper {
    padding-top: 48px;
    .head {
      display: flex;
      justify-content: center;
      padding: 20px 10px;
      border-bottom: 2px solid #E8E8E8;
      .tip {
        display: inline-block;
        font-size: 18px;
        font-weight: 600;
        color: #333333;
        line-height: 24px;
        &:before {
          margin-right: 5px;
          content: "-";
          background-color: #F3955F;
          color: #F3955F;
          line-height: 18px;
        }
        /*.line{*/
        /*margin-right: 2px;*/
        /*display: inline-block;*/
        /*width: 5px;*/
        /*height: 18px;*/
        /*padding-top: 3px;*/
        /*background: #F3955F;*/
        /*}*/
      }

    }
    .main {
      margin: 20px 15px;
      display: flex;
      flex-direction: column;
      .title {
        text-align: left;
        font-size: 18px;
        font-weight: 400;
        color: #333333;
        line-height: 25px;
      }
      .answer {

      }
    }
    .footer {
      padding-top: 30px;
      padding-bottom: 20px;
      margin: 0 auto;
      display: flex;
      justify-content: center;
      .btn {
        width: 144px;
        height: 39px;
        background: linear-gradient(360deg, #FF8100 0%, #FD9A57 100%);
        border-radius: 20px;
        span {
          font-size: 18px;
          font-weight: 400;
          color: #FFFFFF;
          line-height: 25px;
        }
      }
    }

  }
</style>
