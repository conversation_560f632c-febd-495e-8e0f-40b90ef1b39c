<!--
 * @Descripttion: 分享组件
 * @version: 
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-29 13:33:18
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2021-07-30 15:09:27
-->
<template>
    <div class="share" @click="shareFun">
        <!-- <van-icon name="./../../assets/images/operationReport/share.png" /> -->
        <img src="@/assets/images/operationReport/share.png" alt="">
        <span>分享</span>
    </div>
</template>

<script>
export default {
    methods: {
        shareFun() {
            this.$emit('shareEvent')
        }
    }
}
</script>

<style lang="scss" scoped>
.share {
    font-size: 14px;
    color: #999999;
    display: flex;
    img {
        width: 20px;
        height: 20px;
    }
    span {
        margin-left: 8px;
        line-height: 22.5px;
    }
}
</style>