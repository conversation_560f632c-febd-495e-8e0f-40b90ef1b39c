<template>
  <div class="item-wrapper">
    <div class="item-top">
      <div @click="goDocDetail" class="item-avatar">
        <avatar :avatarProps="avatarProps"/>
      </div>
      <div @click="goDocDetail" class="item-info">
        <div class="item-name">{{ docNameProps }}
          <img v-if="isAuth==1" style="width: 14px;height: 16px" src="../images/<EMAIL>" alt="">
          <!-- <img v-if="sourceType==2 && isAuth==1" style="width: 14px;height: 16px" src="../images/<EMAIL>" alt="">
          <van-icon v-if="sourceType!=2 &&isAuth==1" name="checked" color="#1789FC"/> -->
        </div>
        <div class="item-status">
          <span class="item-status-site">{{ releaseTimeProps }}</span>
          <span class="item-status-time">{{ hospNameProps }}</span>
        </div>
      </div>
      <template v-if="!env">
        <div
          v-if="!docAttendStatus"
          class="concat"
          @click="btnDocFollowDetachStatusChange(1,DocIdProps)"
        >
          <van-icon name="plus"/>
          <!-- 关注 -->
          <img src="../images/follow.png" alt="">
        </div>
        <div
          v-else
          @click="btnDocFollowDetachStatusChange(2,DocIdProps)"
          class="concated">
          <!-- 已关注 -->
          <img src="../images/followed.png" alt="">
        </div>
      </template>
      <div v-if="env" class="item_follow_status">
        <van-icon size="30" name="like-o" @click="btnNewsDetachStatusChange(1,activityId)" v-if="!attendStatus"/>
        <van-icon size="30" name="like" @click="btnNewsDetachStatusChange(2,activityId)" v-if="attendStatus"
                  color="red"/>
        <div class="count">{{ attendNum }}</div>
      </div>
    </div>
    <!--动态详情-->
    <template v-if="currentRoute === '/common/publish/activity/detail'">
      <div class="item-detail" ref="contentRef">
        <div v-html="resContent"></div>
      </div>
    </template>
    <!--动态列表 需要最多只能展示六行文字-->
    <template v-else>
      <div v-if="resContent.length > 120" class="item-detail" ref="contentRef" @click="goActivityDetail">
        <div v-html="resContent.slice(0, 120)"></div>
        <br/>
        <span class="detail">查看详情</span>
      </div>
      <div v-else class="item-detail" ref="contentRef" @click="goActivityDetail">
          <div v-html="resContent"></div>
      </div>
    </template>
    <!--链接-->
    <div
      v-if="links && links.length > 0"
      class="item-links"
    >
      <div
        v-for="(item, index) in links"
        :key="index"
      >
        <a :href="item.url">
          <img src="./../../../assets/images/operationReport/link.png" alt="">{{ item.title }}
        </a>
      </div>
    </div>
    <div class="item-imgs">
      <div v-for="(img, index) in imagesProps" :key="index" class="item-imgs-box">
        <img :src="img |formatImg" @click="bigImg(index)">
      </div>
    </div>
    <div class="item-videos">
      <div v-for="(video, index) in videosProps" :key="index" class="item-videos-box">
        <!-- <img :src="img |formatImg" @click="bigImg(index)"> -->
        <video-player class="video-player vjs-custom-skin"
          ref="videoPlayer"
          :playsinline="true"
          :options="playerOptions[index]">
        </video-player>
      </div>
    </div>
    <template v-if="!env">
      <van-divider/>
      <div class="footer">
        <!--点赞组件-->
        <like
          :activityId="activityId"
          :attendNum="attendNum"
          :attendStatus="attendStatus"
        ></like>
        <div class="split"></div>
        <!--分享组件-->
        <share @shareEvent="shareFun"></share>
      </div>
    </template>

  </div>
</template>

<script>
import avatar from '../components/avatar.vue'
import {ImagePreview} from 'vant'
import {newsDetachStatusChange, docFollowDetachStatusChange} from '@/api/common/publish.js'

import Like from './like.vue'
import Share from './share.vue'

import {videoPlayer} from 'vue-video-player'
import 'video.js/dist/video-js.css'

export default {
  props: {
    activityId: {
      type: String,
      default: ''
    },
    isAuth: {
      type: Number,
      default: 0
    },
    avatarProps: {
      type: String,
      default: ''
    },
    docNameProps: {
      type: String,
      default: ''
    },
    hospNameProps: {
      type: String,
      default: ''
    },
    contentProps: {
      type: String,
      default: ''
    },
    releaseTimeProps: {
      type: String,
      default: ''
    },
    DocIdProps: {
      type: Number
    },
    attendNum: {
      type: Number,
      default: 0
    },
    attendStatus: {
      type: Boolean
    },
    sourceType: {
      type: Number
    },
    imagesProps: {
      type: Array,
      default: () => {
        return []
      }
    },
    videosProps: {
      type: Array,
      default: () => {
        return []
      }
    },
    resourcesProps: {
      type: Array,
      default: () => {
        return []
      }
    },
    index: {
      type: Number
    },
    docAttendStatus: {  // 是否关注医生
      type: Boolean
    },
    links: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      env: false,      // 判断是否为微信环境
      currentRoute: this.$route.path,
      playerOptions: []
    }
  },
  created() {
    let ua = window.navigator.userAgent.toLowerCase()
    // 判断是否为微信环境
    if (ua.match(/MicroMessenger/i) == 'micromessenger') {
      this.env = true
    }
    this.init()
  },

  filters: {
    formatImg(val) {
      return val + '?x-oss-process=image/resize,m_fixed,h_84,w_84'
    }
  },
  components: {
    'avatar': avatar,
    Like,
    Share,
    videoPlayer
  },
  computed: {
    /**
     * 页脚跳转路由数据
     */
    resContent() {
      let str = ''
      this.resourcesProps.forEach((item) => {
        if (item.type == 0) {
          str = str + item.res_content
        }
      })
      return str
    }
  },
  methods: {
    init(){
      for(let viedo of this.videosProps){
        let arrStr = {
          playbackRates: [0.5, 1.0, 1.5, 2.0], // 可选的播放速度
          autoplay: false, // 如果为true,浏览器准备好时开始回放。
          muted: false, // 默认情况下将会消除任何音频。
          loop: false, // 是否视频一结束就重新开始。
          preload: 'auto', // 建议浏览器在<video>加载元素后是否应该开始下载视频数据。auto浏览器选择最佳行为,立即开始加载视频（如果浏览器支持）
          language: 'zh-CN',
          fluid: false, // 当true时，Video.js player将拥有流体大小。换句话说，它将按比例缩放以适应其容器。
          sources: [{
            src: viedo.url // url地址
          }],
          width: document.body.clientWidth+ "px",
          height: document.body.clientHeight  + "px",
          poster: viedo.snapshot_url, // 封面地址
          notSupportedMessage: '此视频暂无法播放，请稍后再试', // 允许覆盖Video.js无法播放媒体源时显示的默认信息。
          controlBar: {
            timeDivider: true, // 当前时间和持续时间的分隔符
            durationDisplay: true, // 显示持续时间
            remainingTimeDisplay: false, // 是否显示剩余时间功能
            fullscreenToggle: true // 是否显示全屏按钮
          }
        }
        this.playerOptions.push(arrStr);
      }
    },
    btnNewsDetachStatusChange(type, id) {
      this.noDoubleTap(() => {
        newsDetachStatusChange(type, id).then(res => {
          if (res.status === 0) {
            if (type == 1) {
              this.attendStatus = !this.attendStatus
              this.attendNum = this.attendNum + 1
            } else {
              this.attendStatus = !this.attendStatus
              this.attendNum = this.attendNum - 1
            }
          }
        })
      })
    },
    /**
     * 前往医生详情
     */
    goDocDetail() {
      this.$router.push({
        path: '/common/publish/doctor',
        query: {
          'doc_id': this.DocIdProps,
          'source_type': this.sourceType
        }
      })
    },
    /**
     * 图片放大
     */
    bigImg(index) {
      ImagePreview({
        images: this.imagesProps,
        startPosition: index,
        maxZoom: 10
      })
    },
    /**
     * 前往动态详情
     */
    goActivityDetail() {
      this.$router.push({
        path: '/common/publish/activity/detail',
        // path: '/common/publish/activity/shareActivityDetail',
        query: {
          'activity_id': this.activityId,
          'source_type': this.sourceType
        }
      })
    },
    // 关注取消关注
    btnDocFollowDetachStatusChange(type, id) {
      let that = this
      this.noDoubleTap(() => {
        docFollowDetachStatusChange(type, id).then(res => {
          if (res.status === 0) {
            if (type == 1) {
              that.docAttendStatus = true
            } else {
              that.docAttendStatus = false
            }
            // 告诉父级 关注状态已经更新，遍历所有父级列表，改同一个医生的状态
            this.$emit('statusChangeEvent', {
              id: id,
              docAttendStatus: this.docAttendStatus
            })
          }
        })
      })
    },
    // 分享
    shareFun() {
      const UA = navigator.userAgent
      const isAndroid = UA.indexOf('Android') > -1 || UA.indexOf('Adr') > -1 // android终端
      const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
      let param = JSON.stringify({
        shareTitle: '分享医生动态',
        shareDescr: this.contentProps,
        shareUrl: `${process.env.VUE_APP_BASE_URL}common/publish/activity/shareActivityDetail?activity_id=${this.activityId}&source_type=${this.sourceType}`
      })
      if (isAndroid) {
        console.log('安卓')
        window.android.shareWeChat(param)
      } else if (isIOS) {
        console.log('苹果')
        window.webkit.messageHandlers.shareWeChat.postMessage(param)
      } else {
        Toast({
          message: '暂时无法全屏放大',
          type: 'html',
          forbidClick: true,
          duration: 1000
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.item-wrapper {
  overflow: hidden;
  background: #fff;
  // padding: 21px 34px 25px 17px;
  padding: 16px 17px;

  .item-top {
    display: inline-block;
    width: 100%;

    .item-avatar {
      float: left;
      width: 43px;
      height: 43px;

      img {
        width: 100%;
      }
    }

    .item-info {
      float: left;
      text-align: left;
      margin-left: 8px;
      width: 230px;

      .item-name {
        font-size: 17px;
        font-weight: 500;
        color: #253457;
        line-height: 26px;

        img {
          width: 17px;
          height: 17px;
          margin-left: 5px;
          margin-top: 4px;
        }
      }

      .item-status {
        font-size: 13px;
        font-weight: 400;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        color: #AAAAAA;
        margin-top: 4px;

        .item-status-time {
          margin-left: 10px;
        }
      }
    }

    .concat {
      background: #FF8100;
      float: right;
      color: white;
      font-size: 11px;
      width: 48px;
      height: 20px;
      border-radius: 10px;
      display: inline-flex;
      justify-content: center;
      align-items: center;

      img {
        width: 100%;
        height: 100%;
      }

      .van-icon-plus::before {
        content: "\F0A2";
        display: none;
      }
    }

    .concated {
      background: #fff;
      float: right;
      color: #666666;
      font-size: 11px;
      width: 48px;
      height: 20px;
      border-radius: 10px;
      display: inline-flex;
      justify-content: center;
      align-items: center;

      img {
        width: 100%;
        height: 100%;
      }
    }

    .item_follow_status {
      float: right;
      margin-left: 5px;

      img {
        width: 20px;
        height: 20px;
      }

      .count {
        font-size: 13px;
        font-weight: 400;
        color: #555555;
        opacity: 0.8;
      }
    }
  }

  .item-detail {
    font-size: 17px;
    font-weight: 400;
    color: #000000;
    line-height: 24px;
    text-align: left;
    margin-top: 16px;

    .detail {
      color: #576B95;
      font-size: 16px;
    }
  }

  .item-links {
    font-size: 16px;
    color: #576B95;
    text-align: left;
    margin-top: 8px;
    margin-bottom: 8px;

    img {
      width: 12px;
      height: 12px;
      margin-right: 5px;
    }

    div {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin-top: 5px;
    }

  }

  .item-imgs {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: flex-start;

    .item-imgs-box {
      width: 84px;
      height: 84px;
      overflow: hidden;
      margin-top: 10px;
      margin-right: 11px;

      &:nth-of-type(3n) {
        margin-right: 0;
      }

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }

  .item-videos {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: flex-start;

    .item-videos-box {
      width: 340px;
      height: 200px;
      overflow: hidden;
      margin-top: 10px;
      margin-right: 11px;
      position: relative;
      .video-player{
        width: 100%;
        height: 100%;
        ::v-deep .video-js {
          width: 100%;
          height: 100%;
          .vjs-big-play-button{
            border-width: 3px;
            border-color: rgb(255, 255, 255);
            border-style: solid;
            border-radius: 50%;
            width: 56px;
            height: 56px;
            line-height: 50px;
            position: absolute;
            top: calc(50% - 23px);
            left: calc(50% - 23px);
            margin: auto;
          }
          .vjs-control-bar{
            width: 100%;
          }
        }
      }

      // img {
      //   width: 100%;
      //   height: 100%;
      //   object-fit: cover;
      // }
    }
  }

  .footer {
    display: flex;
    width: 100%;
    justify-content: space-around;
    align-items: center;

    .split {
      width: 1px;
      height: 14px;
      // color: #F2F2F2;
      background-color: #F2F2F2;
      transform: scale(0.5, 1);
    }
  }
}
</style>
