<template>
  <div class="schedule-wrapper">
    <div class="schedule-col">
      <div class="schedule-grid"></div>
      <div class="schedule-grid">一</div>
      <div class="schedule-grid">二</div>
      <div class="schedule-grid">三</div>
      <div class="schedule-grid">四</div>
      <div class="schedule-grid">五</div>
      <div class="schedule-grid">六</div>
      <div class="schedule-grid">日</div>
    </div>
    <div v-for="group in scheduleData" class="schedule-col">
      <div v-for="(item, index) in group" class="schedule-grid">
        <span v-if="index === 'duration_type'">{{ item === 1 ? '上午' : '下午' }}</span>
        <span v-else-if="item === 1"
          :class="{'schedule-grid-edit': editFlag}"
          @click="changeSchedule(item, index, group['duration_type'])"
        >坐诊</span>
        <span v-else-if="editFlag && item === 0"
          class="schedule-grid-edit-no"
          @click="changeSchedule(item, index, group['duration_type'])"
        >坐诊</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data: () => {
    return {
      scheduleData: [
        {
          duration_type: 1, // 1上午 2下午
          mon: 0, // 周一 1坐诊 0不坐诊
          tue: 0, // 周二 1坐诊 0不坐诊
          wed: 0, // 周三 1坐诊 0不坐诊
          thu: 0, // 周四 1坐诊 0不坐诊
          fri: 0, // 周五 1坐诊 0不坐诊
          sat: 0, // 周六 1坐诊 0不坐诊
          sun: 0 // 周日 1坐诊 0不坐诊
        },
        {
          duration_type: 2, // 1上午 2下午
          mon: 0, // 周一 1坐诊 0不坐诊
          tue: 0, // 周二 1坐诊 0不坐诊
          wed: 0, // 周三 1坐诊 0不坐诊
          thu: 0, // 周四 1坐诊 0不坐诊
          fri: 0, // 周五 1坐诊 0不坐诊
          sat: 0, // 周六 1坐诊 0不坐诊
          sun: 0 // 周日 1坐诊 0不坐诊
        }
      ]
    }
  },
  props: {
    editFlag: {
      type: Boolean,
      default: false
    },
    scheduleProp: {
      type: Array,
      default: () => {
        return [
          {
            duration_type: 1, // 1上午 2下午
            mon: 0, // 周一 1坐诊 0不坐诊
            tue: 0, // 周二 1坐诊 0不坐诊
            wed: 0, // 周三 1坐诊 0不坐诊
            thu: 0, // 周四 1坐诊 0不坐诊
            fri: 0, // 周五 1坐诊 0不坐诊
            sat: 0, // 周六 1坐诊 0不坐诊
            sun: 0 // 周日 1坐诊 0不坐诊
          },
          {
            duration_type: 2, // 1上午 2下午
            mon: 0, // 周一 1坐诊 0不坐诊
            tue: 0, // 周二 1坐诊 0不坐诊
            wed: 0, // 周三 1坐诊 0不坐诊
            thu: 0, // 周四 1坐诊 0不坐诊
            fri: 0, // 周五 1坐诊 0不坐诊
            sat: 0, // 周六 1坐诊 0不坐诊
            sun: 0 // 周日 1坐诊 0不坐诊
          }
        ]
      }
    }
  },
  watch: {
    scheduleProp (newVal, oldVal) {
      this.scheduleData = newVal.map(item => {
        return {
          duration_type: item.duration_type,
          mon: item.mon,
          tue: item.tue,
          wed: item.wed,
          thu: item.thu,
          fri: item.fri,
          sat: item.sat,
          sun: item.sun
        }
      })
    }
  },
  created() {
    this.scheduleData = this.scheduleProp
  },
  methods: {
    /**
     * 改变排期
     * @param {Number} flag 坐诊flag（1坐诊 0不坐诊）
     * @param {String} day 星期
     * @param {Number} durationType 上下午flag（1上午 2下午）
     */
    changeSchedule (flag, day, durationType) {
      if (!this.editFlag) return

      this.scheduleData.forEach((item, index) => {
        if (item.duration_type === durationType) {
          if (flag === 1) this.$set(this.scheduleData[index], day, 0)
          else this.$set(this.scheduleData[index], day, 1)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.schedule-wrapper {
  border: 1px solid #e6e6e6 {
    right: 0px;
    bottom: 0px;
  }

  .schedule-col {
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;

    .schedule-grid {
      flex: 1;
      height: 31px;
      font-size: 15px;
      color: #000000;
      font-weight: 400;
      line-height: 31px;
      text-align: center;
      border: 1px solid #e6e6e6 {
        top: 0px;
        left: 0px;
      }

      span {
        display: block;
      }

      .schedule-grid-edit-no {
        color: #cccccc;
      }

      .schedule-grid-edit {
        color: #ee7800;
        background-color: #ffecde;
      }
    }
  }
}
</style>
