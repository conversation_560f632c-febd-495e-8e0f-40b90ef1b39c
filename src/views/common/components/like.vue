<!--
 * @Descripttion: 点赞组件
 * @version: 
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-29 13:33:10
 * @LastEditors: l<PERSON><PERSON><PERSON>n
 * @LastEditTime: 2021-07-30 13:19:17
-->
<template>
    <div class="like">
        <van-icon
            v-if="!attendStatus"
            name="like-o"
            @click="btnNewsDetachStatusChange(1,activityId)"
        ></van-icon>
        <van-icon
            v-if="attendStatus"
            name="like"
            color="red"
            @click="btnNewsDetachStatusChange(2,activityId)"
        ></van-icon>
        <div class="count">
            <span class="left">点赞</span>
            <span class="left">{{ attendNum }}</span>
        </div>
    </div>
</template>

<script>
import { newsDetachStatusChange } from '@/api/common/publish.js'
export default {
    props: {
        activityId: {
            type: String,
            default: ''
        },
        attendNum: {        // 点赞数量
            type: Number,
            default: 0
        },
        attendStatus: {
            type: Boolean   // 是否已经点赞
        }
    },
    methods: {
        btnNewsDetachStatusChange (type, id) {
            this.noDoubleTap(() => {
                newsDetachStatusChange(type, id).then(res => {
                    if (res.status === 0) {
                        if (type == 1) {
                            this.attendStatus = !this.attendStatus
                            this.attendNum = this.attendNum + 1
                        } else {
                            this.attendStatus = !this.attendStatus
                            this.attendNum = this.attendNum - 1
                        }
                    }
                })
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.like {
    font-size: 14px;
    color: #999999;
    display: flex;
    .van-icon {
        font-size: 20px;
    }
    .left {
        margin-left: 8px;
        line-height: 22.5px;
    }
}
</style>