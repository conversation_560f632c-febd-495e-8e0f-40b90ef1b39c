<template>
  <div class="activity-wrapper"  @click="goActivityDetail()">
    <div class="activity-left">
      <p class="activity-left-p" @click="goActivityDetail(1)">{{ introProps }}</p>
      <div class="item-videos">
        <div v-for="(video, index) in videosProps" :key="index" class="item-videos-box">
          <!-- <img :src="img |formatImg" @click="bigImg(index)"> -->
          <video-player class="video-player vjs-custom-skin"
                        ref="videoPlayer"
                        :playsinline="true"
                        :options="playerOptions[index]">
          </video-player>
        </div>
      </div>
      <div class="activity-left-status">
        <span>{{ timeProps }}</span> <span class="zan">{{ attendNum }}次点赞</span>
      </div>
    </div>
    <div v-if="imgSrcProps" class="activity-right">
      <img :src="imgSrcProps">
    </div>
  </div>
</template>

<script>
import {videoPlayer} from 'vue-video-player'
import 'video.js/dist/video-js.css'
export default {
  props: {
    introProps: {
      type: String,
      default: ''
    },
    attendNum: {
      type: Number,
      default: 0
    },
    activityId: {
      type: Number,
      default: 0
    },
    timeProps: {
      type: String,
      default: ''
    },
    imgSrcProps: {
      type: String,
      default: ''
    },
    videosProps: {
      type: Array,
      default: () => {
        return []
      }
    },
  },
  components: {
    videoPlayer
  },
  data() {
    return {
      sourceType:'',
      playerOptions: []
    }
  },
  created() {
    let query = this.$route.query
    this.sourceType = query.source_type

    this.init()
  },

  methods: {
    /**
     * 前往动态详情
     * @param {Number} activityId 动态ID
     */
    goActivityDetail(flg=0) {
      if(this.videosProps.length>0 && flg==0){
        return
      }
      this.$router.push({
        path: '/common/publish/activity/detail',
        query: {
          'activity_id': this.activityId,
          'source_type': this.sourceType
        }
      })
    },
    init() {
      for (let viedo of this.videosProps) {
        let arrStr = {
          playbackRates: [0.5, 1.0, 1.5, 2.0], // 可选的播放速度
          autoplay: false, // 如果为true,浏览器准备好时开始回放。
          muted: false, // 默认情况下将会消除任何音频。
          loop: false, // 是否视频一结束就重新开始。
          preload: 'auto', // 建议浏览器在<video>加载元素后是否应该开始下载视频数据。auto浏览器选择最佳行为,立即开始加载视频（如果浏览器支持）
          language: 'zh-CN',
          fluid: false, // 当true时，Video.js player将拥有流体大小。换句话说，它将按比例缩放以适应其容器。
          sources: [{
            src: viedo.url // url地址
          }],
          width: document.body.clientWidth + "px",
          height: document.body.clientHeight + "px",
          poster: viedo.snapshot_url, // 封面地址
          notSupportedMessage: '此视频暂无法播放，请稍后再试', // 允许覆盖Video.js无法播放媒体源时显示的默认信息。
          controlBar: {
            timeDivider: true, // 当前时间和持续时间的分隔符
            durationDisplay: true, // 显示持续时间
            remainingTimeDisplay: false, // 是否显示剩余时间功能
            fullscreenToggle: true // 是否显示全屏按钮
          }
        }
        this.playerOptions.push(arrStr);
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.activity-wrapper {
  width: 100%;
  display: flex;
  overflow: hidden;
  align-items: flex-start;
  justify-content: space-between;

  .activity-left {
    flex: 1;

    .activity-left-p {
      font-size: 17px;
      max-height: 80px;
      font-weight: 400;
      color: #000000;
      overflow: hidden;
      line-height: 26px;
    }

    .item-videos {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      justify-content: flex-start;

      .item-videos-box {
        width: 340px;
        height: 200px;
        overflow: hidden;
        margin-top: 10px;
        margin-right: 11px;
        position: relative;
        .video-player{
          width: 100%;
          height: 100%;
          ::v-deep .video-js {
            width: 100%;
            height: 100%;
            .vjs-big-play-button{
              border-width: 3px;
              border-color: rgb(255, 255, 255);
              border-style: solid;
              border-radius: 50%;
              width: 56px;
              height: 56px;
              line-height: 50px;
              position: absolute;
              top: calc(50% - 23px);
              left: calc(50% - 23px);
              margin: auto;
            }
            .vjs-control-bar{
              width: 100%;
            }
          }
        }

        // img {
        //   width: 100%;
        //   height: 100%;
        //   object-fit: cover;
        // }
      }
    }


    .activity-left-status {
      font-size: 15px;
      margin-top: 12px;
      font-weight: 400;
      color: #9F9F9F;
      line-height: 24px;
    }
  }

  .activity-right {
    width: 80px;
    height: 80px;
    overflow: hidden;
    margin-left: 36px;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .zan {
    padding-left: 20px;
  }
}
</style>
