<template>
  <div class="avatar">
    <img :src="avatarProps==''?'https://zz-med-pub.oss-cn-hangzhou.aliyuncs.com/shared/images/default/doc_logo.jpg':avatarProps">
  </div>
</template>

<script>
  export default {
    data: () => {
      return {
        imgSrc: ''
      }
    },
    watch: {
      avatarProps(newVal, oldVal) {
        if (newVal) {
          this.imgSrc = newVal
        }
      }
    },
    props: {
      avatarProps: {
        type: String,
        default: '' // TODO 默认头像
      }
    },
    created() {

    }
  }
</script>

<style lang="scss" scoped>
  .avatar {
    width: 100%;
    height: 100%;
    overflow: hidden;
    border-radius: 50%;

    img {
      width: 100%;
    }
  }
</style>
