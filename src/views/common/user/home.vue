<template>
  <div class="wrapper">
    <div class="group">
      <group-top
        :typeIcon="require('./images/<EMAIL>')"
        :editIcon="require('./images/<EMAIL>')"
        @goEdit="goEdit('/common/user/edit', 'intro', intro,1)"
        titleName="医生简介"
      />
      <!-- <div v-if="intro && !dispose" class="group-p">{{ intro }}</div> -->
      <div v-if="intro" class="group-p">
        <div class="docDetail">{{ intro }}</div>
        <div class="docDetails">{{ intro }}</div>
        <div class="buttonText" v-if="isOpenOrClose = true" @click="open">{{buttonText}}</div>
      </div>

      <p
        v-if="!intro"
        @click="goEdit('/common/user/edit', 'intro', intro,1)"
        class="group-p group-p-no"
      >
        <span class="group-p-span">暂无内容</span>
        <van-icon name="arrow" class="group-p-icon" />
      </p>
    </div>
    <div class="group">
      <group-top
        :typeIcon="require('./images/<EMAIL>')"
        :editIcon="require('./images/<EMAIL>')"
        @goEdit="goEdit('/common/user/edit', 'expert', expert,2)"
        titleName="擅长病症"
      />
      <!-- <div v-if="dispose && listInput.length > 0" class="group-p">
        <div class="illnessDetail clearfix">
          <p v-for="(item, index) in listInput" :key=index>{{item}}</p>
        </div>
        <div class="illnessDetails clearfix">
          <p v-for="(item, index) in listInput" :key=index>{{item}}</p>
        </div>
        <div class="buttonText" v-if="isOpenOrClose1 = true" @click="open1">{{buttonText1}}</div>
      </div> -->
      <!-- <div v-if="expert && !dispose" class="group-p">{{ expert }}</div> -->
      <div v-if="expert" class="group-p">{{expert}}</div>
      <p
        v-if="!expert"
        @click="goEdit('/common/user/edit', 'expert', expert,2)"
        class="group-p group-p-no"
      >
        <span class="group-p-span">您还未填写，去编辑</span>
        <van-icon name="arrow" class="group-p-icon" />
      </p>
    </div>
    <div class="group" v-if="is_show&&showSetting == 1">
      <group-top
        :typeIcon="require('./images/<EMAIL>')"
        :editIcon="require('./images/<EMAIL>')"
        @goEdit="goSetting"
        titleName="线上咨询服务" 
      />
      <div v-if="is_audit_pass == false" class="group-p">
        <div class="pic1" @click="goSetting">您还未在智众互联网医院备案，暂时无法开通此服务<span>去开通 ></span></div>
      </div>
      <div v-if="is_audit_pass == true && is_open == false" class="group-p">
        <div class="pic2">
          <div class="lf"><img src="./images/<EMAIL>" /></div>
          <p class="cen"><span>图文咨询</span><span>未开通</span></p>
          <div class="lr" @click="goSetting">去设置</div>
        </div>
      </div>
      <div v-if="is_audit_pass == true && is_open == true" class="group-p">
        <div class="pic2">
          <div class="lf"><img src="./images/<EMAIL>" /></div>
          <p class="cen"><span>图文咨询</span><span class="price">{{price}}元/次</span></p>
          <div class="lr" @click="goSetting">去设置</div>
        </div>
      </div>
    </div>
    <!-- <div class="group">
      <group-top
        :typeIcon="require('./images/<EMAIL>')"
        :editIcon="require('./images/<EMAIL>')"
        @goEdit="goEdit('/common/user/outpatient')"
        titleName="门诊时间"
      />
      <outpatient
        :editFlag="false"
        :hospProp="hosp"
        :addrProp="addr"
        :clinicAddrProp="clinicAddr"
        :scheduleProp="scheduleData"
      />
    </div> -->
  </div>
</template>

<script>
  import outpatient from "./outpatient.vue";
  import homeGroupTop from "./components/homeGroupTop.vue";
  import { userResum, getExpertLabel, getService } from "@/api/common/user.js";

  export default {
    data: () => {
      return {
        workRoomId: "",
        intro: "",
        hosp: "",
        addr: "",
        expert: "",
        clinicAddr: "",
        scheduleData: [
          {
            duration_type: 1, // 1上午 2下午
            mon: 0, // 周一 1坐诊 0不坐诊
            tue: 0, // 周二 1坐诊 0不坐诊
            wed: 0, // 周三 1坐诊 0不坐诊
            thu: 0, // 周四 1坐诊 0不坐诊
            fri: 0, // 周五 1坐诊 0不坐诊
            sat: 0, // 周六 1坐诊 0不坐诊
            sun: 0, // 周日 1坐诊 0不坐诊
          },
          {
            duration_type: 2, // 1上午 2下午
            mon: 0, // 周一 1坐诊 0不坐诊
            tue: 0, // 周二 1坐诊 0不坐诊
            wed: 0, // 周三 1坐诊 0不坐诊
            thu: 0, // 周四 1坐诊 0不坐诊
            fri: 0, // 周五 1坐诊 0不坐诊
            sat: 0, // 周六 1坐诊 0不坐诊
            sun: 0, // 周日 1坐诊 0不坐诊
          },
        ],
        isOpenOrClose: false,
        buttonText: '',
        isOpenOrClose1: false,
        buttonText1: '',
        listInput: [],
        picSet: 3,
        is_audit_pass: false, // 是否备案
        is_open: false, // 是否开通
        is_show: false, // 是否展示
        price: '',
        // dispose: true, // true显示模块新功能， false不显示
        docheight: 0,
        docheights: 0,
        illheight: 0,
        illheights: 0,
        showSetting: 0 //老版本闪退bug判断参数
      };
    },
    components: {
      outpatient,
      "group-top": homeGroupTop,
    },
    mounted() {
      // 如果检测到页面是从“往返缓存”中读取的，刷新页面
      window.addEventListener(
        "pageshow",
        (e) => {
          if (
            e.persisted ||
            (window.performance && window.performance.navigation.type == 2)
          ) {

            // 获取个人主页-简历、排班
            this.getUserResum();
          }
        },
        false
      );
    },
    created() {
      // 工作室id
      this.workRoomId = this.$route.query.workroom_id;
      this.showSetting = this.$route.query.showSetting

      // 获取个人主页-简历、排班
      this.getUserResum();
    },
    methods: {
      /**
       * 获取个人主页-简历、排班
       */
      getUserResum() {
        userResum(this.workRoomId).then((res) => {
          if (res.code === 200) {
            let data = res.data;
            let hosp = data.hosp || {};
            let resume = data.resume || {};

            this.hosp = hosp.name || "";
            this.addr = hosp.full_addr || "";

            this.intro = resume.intro || "";
            this.expert = resume.expert || "";
            this.clinicAddr = resume.clinic_addr || "";

            if (data.clinic_time.length > 0)
              this.scheduleData = data.clinic_time;
          } else {
            this.$toast(res.msg);
          }
        }).then(() => {
          let docheight = this.docheight = $('.docDetail').height()
          let docheights = this.docheights = $('.docDetails').height()
          console.log(docheight, docheights)
          $('.docDetails').css({"display":"none"});
          if(docheights > docheight) {
            this.isOpenOrClose = true
            this.buttonText = '展开'
          } else {
            this.isOpenOrClose = false
            this.buttonText = ''
          }
          let illheight = this.illheight = $('.illnessDetail').height()
          let illheights = this.illheights = $('.illnessDetails').height()
          console.log(illheight, illheights)
          if(illheights > illheight) {
            this.isOpenOrClose1 = true
            this.buttonText1 = '展开'
          } else {
            this.isOpenOrClose1 = false
            this.buttonText1 = ''
          }
        })
        .catch((err) => {
          this.$toast(err);
        });
        // getExpertLabel(this.workRoomId).then(res => {
        //   console.log(res)
        //   if (res.code === 200) {
        //     this.listInput = []
        //     res.data.forEach((v, k) => {
        //       this.listInput.push(v.label)
        //     })
        //   } else {
        //     this.$toast(res.msg)
        //   }
        // })
        getService(this.workRoomId).then(res => {
          console.log(res)
          if (res.code === 200) {
            this.is_audit_pass = res.data.is_audit_pass
            this.is_open = res.data.is_open
            this.is_show = res.data.is_show
            this.price = res.data.price
          } else {
            this.$toast(res.msg)
          }
        })
      },
      open() {
        let height = $('.docDetail').height()
        if(height < this.docheights) {
          $('.docDetail').animate({'height': this.docheights + 'px'})
          this.buttonText = '折叠'
        } else {
          this.buttonText = '展开'
          $('.docDetail').animate({'height': this.docheight + 'px'})
        }
      },
      open1() {
        let height = $('.illnessDetail').height()
        if(height < this.illheights) {
          $('.illnessDetail').animate({'height': this.illheights + 'px'})
          this.buttonText1 = '折叠'
        } else {
          this.buttonText1 = '展开'
          $('.illnessDetail').animate({'height': this.illheight + 'px'})
        }
      },
      goSetting() {
        console.log('hahahahahh')
        const UA = navigator.userAgent
        const isAndroid = UA.indexOf('Android') > -1 || UA.indexOf('Adr') > -1 // android终端
        const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
        if (isAndroid) {
          console.log('安卓')
          if(this.is_audit_pass == true) {
            // window.android.goDoctorConsultSet()
            window.mmc_doctor.postMessage(JSON.stringify({"operator":"goDoctorConsultSet","code":"0","msg":"success","data":{}}))
          } else {
            // window.android.goDoctorRecordation()
            window.mmc_doctor.postMessage(JSON.stringify({"operator":"goDoctorRecordation","code":"0","msg":"success","data":{}}))
          }
        } else if (isIOS) {
          console.log('苹果')
          if(this.is_audit_pass == true) {
            window.webkit.messageHandlers.goDoctorConsultSet.postMessage('1')
          } else {
            window.webkit.messageHandlers.goDoctorRecordation.postMessage('1')
          }
        } else {
          Toast({
            message: '暂时无法查看',
            type: 'html',
            forbidClick: true,
            duration: 1000
          })
        }
      },
      /**
       * 前往编辑
       * @param {String} path 路径
       * @param {String} key 键
       * @param {String} val 值
       */
      goEdit(path, key = "", val = "",type='') {
        let query;
        // if (key === "") {
        //   query = {};
        // } else {
        //   query = {
        //       [key]: val,
        //     };
        //   // if(type == 2) {
        //   //   console.log(this.listInput)
        //   //   query = {
        //   //     [key]: this.listInput.join(),
        //   //   };
        //   // } else {
        //   //   query = {
        //   //     [key]: val,
        //   //   };
        //   // }
        // }
        this.$router.push({
          path,
          query: {
            workRoomId: this.workRoomId,
            type:type,
            // dispose: this.dispose,
            // ...query,
            key:key
          },
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .wrapper {
    overflow: hidden;
    background: #f7f7f7;

    .group {
      margin-top: 10px;
      background: #fff;

      &:first-of-type {
        margin-top: 0;
      }

      .group-p {
        font-size: 18px;
        text-align: left;
        font-weight: 400;
        color: #333333;
        padding: 9px 18px;
        line-height: 30px;
        white-space: pre-wrap;
        white-space: -moz-pre-wrap;
        white-space: -o-pre-wrap;
        word-wrap: break-word;
        .docDetail{
          // height: 60px;
          height: 120px;
          overflow: hidden;
        }
        // .docDetails{
        //   display: none;
        // }
        .buttonText{
          color: #EE7800;
          font-size: 16px;
        }
        .illnessDetail, .illnessDetails{
          width: 345px;
          p{
            display: inline-block;
            height: 24px;
            padding: 0 8px;
            margin-right: 10px;
            margin-bottom: 10px;
            background: #F7F7F7;
            border: 1px solid #F7F7F7;
            border-radius: 18px;
            float: left;
            text-align: center;
            line-height: 24px;
            font-size: 14px;
            font-weight: 400;
            color: #000;
          }
        }
        .illnessDetail{
          height: 72px;
          overflow: hidden;
        }
        .illnessDetails{
          display: none;
        }
        .pic1{
          font-size: 16px;
          font-weight: 400;
          color: #A7A7A7;
          line-height: 24px;
          span{
            display: inline-block;
            width: 67px;
            height: 20px;
            text-align: center;
            border-radius: 13px;
            border: 1px solid #EE7800;
            font-size: 14px;
            font-weight: 400;
            color: #EE7800;
            margin-left: 10px;
          }
        }
        .pic2{
          width: 100%;
          height: 53px;
          .lf{
            width: 53px;
            height: 53px;
            float: left;
            line-height: 53px;
            text-align: center;
            border-radius: 100%;
            position: relative;
            margin-right: 16px;
            img{
              width: 100%;
              height: 100%;
              display: inline-block;
            }
          }
          .cen{
            width: 82px;
            float: left;
            span:nth-child(1) {
              font-size: 18px;
              font-weight: 400;
              color: #000000;
              line-height: 28px;
              display: block;
            }
            span:nth-child(2) {
              font-size: 16px;
              font-weight: 400;
              color: #999999;
              line-height: 22px;
              display: block;
            }
            span.price {
              font-size: 16px;
              font-weight: 400;
              color: #EE7800;
              line-height: 22px;
              display: block;
            }
          }
          .lr{
            width: 67px;
            height: 22px;
            line-height: 22px;
            float: right;
            text-align: center;
            border-radius: 13px;
            border: 1px solid #EE7800;
            font-size: 14px;
            font-weight: 400;
            color: #EE7800;
            margin-top: 15px;
          }
        }
      }
      .group-p-no {
        display: flex;
        color: #cccccc;
        align-items: center;
        justify-content: space-between;
      }
    }
  }
</style>
