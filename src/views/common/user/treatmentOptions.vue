<!--
 * @Descripttion: 
 * @version: 
 * @Author: xulh
 * @Date: 2021-05-26 13:53:06
 * @LastEditors: xulh
 * @LastEditTime: 2021-06-04 15:08:39
-->
<template>
  <div id="treatmentOptions">
    <div class="head flexHead">
      <img class="imgsize" src="./images/<EMAIL>" alt="" />
      截止至V{{drugOptionList.visitLevel}}
      &nbsp;&nbsp;&nbsp;&nbsp;
      数据来源于MMC标准化代谢性管理中心
    </div>

    <van-collapse v-model="activeName" accordion>
      <van-collapse-item title="全部药物" name="1">
        <div class="drug" v-for="(drugList, name) in filterDrugOptionList" :key="name">
          <div class="drugType" :style="{ paddingBottom: drugList.length == 0 ? '0' : '10px' }">
            {{ dictionaryDrugs[name] }}
          </div>
          <div @click="handleClick(content)" v-for="(content, j) in drugList" :key="j" class="drugContent word" :class="[{marBottom:j+1!=drugList.length}]">
            <div class="drugContentName hidden">
              {{usedrugNameFunc(content.general_name, content.drug_alias,content.drug_name)}}
            </div>
            <div class="detail flex">
              <div class="flexLeft bottom">
                {{ usemedicineTimeFunc(content.drug_dose, content.week_dose) }}
              </div>
              <div class="flexRight">
                {{usemedicineUseFunc(content.drug_dose,content.week_dose,content.unit)}}
              </div>
            </div>
            <div class="detail flex">
              <div class="flexLeft">用法用量</div>
              <div class="flexRight">
                {{ usedetailmedicineUseFunc(content.usage_dosage_morning,content.usage_dosage_noon,content.usage_dosage_night,content.usage_dosage_bedtime,content.usage_dosage_other,content.unit)}}
              </div>
            </div>
          </div>
        </div>
      </van-collapse-item>
      <van-collapse-item title="正在使用的药物" name="2">
        <div class="drug" v-for="(usingList, name) in usingLists" :key="name">
          <div class="drugType" :style="{ paddingBottom: usingList.length == 0 ? '0' : '10px' }">
            {{ dictionaryDrugs[name] }}
          </div>
          <div @click="handleClick(content)" v-for="(content, j) in usingList" :key="j" class="drugContent word" :class="[{marBottom:j+1!=usingList.length}]">
            <div class="drugContentName hidden">
              {{usedrugNameFunc(content.general_name, content.drug_alias,content.drug_name)}}
            </div>
            <div class="detail flex">
              <div class="flexLeft bottom">
                {{ usemedicineTimeFunc(content.drug_dose, content.week_dose) }}
              </div>
              <div class="flexRight">
                {{usemedicineUseFunc(content.drug_dose,content.week_dose,content.unit)}}
              </div>
            </div>
            <div class="detail flex">
              <div class="flexLeft">用法用量</div>
              <div class="flexRight">
                {{ usedetailmedicineUseFunc(content.usage_dosage_morning,content.usage_dosage_noon,content.usage_dosage_night,content.usage_dosage_bedtime,content.usage_dosage_other,content.unit)}}
              </div>
            </div>
          </div>
        </div> 
      </van-collapse-item>
      <van-collapse-item title="已经停用的药物" name="3">
        <div class="drug" v-for="(noUsingList, name) in noUsingLists" :key="name">
          <div class="drugType" :style="{ paddingBottom: noUsingList.length == 0 ? '0' : '10px' }">
            {{ dictionaryDrugs[name] }}
          </div>
          <div @click="handleClick(content)" v-for="(content, j) in noUsingList" :key="j" class="drugContent word" :class="[{marBottom:j+1!=noUsingList.length}]">
            <div class="drugContentName hidden">
              {{usedrugNameFunc(content.general_name, content.drug_alias,content.drug_name)}}
            </div>
            <div class="detail flex">
              <div class="flexLeft bottom">
                {{ usemedicineTimeFunc(content.drug_dose, content.week_dose) }}
              </div>
              <div class="flexRight">
                {{usemedicineUseFunc(content.drug_dose,content.week_dose,content.unit)}}
              </div>
            </div>
            <div class="detail flex">
              <div class="flexLeft">用法用量</div>
              <div class="flexRight">
                {{ usedetailmedicineUseFunc(content.usage_dosage_morning,content.usage_dosage_noon,content.usage_dosage_night,content.usage_dosage_bedtime,content.usage_dosage_other,content.unit)}}
              </div>
            </div>
          </div>
        </div>         
      </van-collapse-item>
    </van-collapse>
  </div>
</template>

<script>
import {drugNameFunc , medicineTimeFunc , medicineUseFunc , detailmedicineUseFunc } from './publicFunc.js';
import { GetMedicalDrugList } from '@/api/common/treatmentOptions.js'

export default {
  data() {
    return {
      activeName:'2',//默认展开正在使用中的药物
      activeDrugsNames: ["1"],
      dictionaryDrugs: {
        jty: "降糖药物",
        other: "其他药物",
      },
      drugOptionList: {},
      usingOptionList:{},
      noUsingOptionList:{}
    };
  },
  methods: {
    handleClick(content) {
      let queryContent = JSON.stringify(content);
      this.$router.push({
        path: "/common/user/selectOptions",
        query: {
          content: queryContent,
          visitLevel: this.drugOptionList.visitLevel
        },
      });
    },
    usedrugNameFunc(general_name = null, drug_alias = null, drug_name = null) {
     return drugNameFunc(general_name ,drug_alias, drug_name);
    },
    usemedicineTimeFunc(drug_dose = 0, week_dose = 0) {
      return medicineTimeFunc(drug_dose , week_dose)
    },

    usemedicineUseFunc(drug_dose = 0, week_dose = 0, unit) {
      return medicineUseFunc(drug_dose , week_dose , unit)
    },
    usedetailmedicineUseFunc(
      usage_dosage_morning = "",
      usage_dosage_noon = "",
      usage_dosage_night = "",
      usage_dosage_bedtime = "",
      usage_dosage_other = [],
      unit=""
    ) {
     return detailmedicineUseFunc(usage_dosage_morning ,usage_dosage_noon,usage_dosage_night,usage_dosage_bedtime,usage_dosage_other , unit )
    },
    async getListFunc() {
      let user_id = this.$route.query.user_id;
      // user_id = '2597942'
      let { code , data } =  await GetMedicalDrugList({user_id});
      if(code == 200){
        this.drugOptionList = data;
        var useJtyList=[];
        var noUseJtyList=[];
        var useOtherList=[];
        var noUseOtherList=[];

        if(data && Array.isArray(data.jty)){
          useJtyList = data.jty.filter((item)=>{
            return item.is_used == 1
          });
          noUseJtyList = data.jty.filter((item)=>{
            return item.is_used == 0
          });
        }
        if(data && Array.isArray(data.other)){
          useOtherList = data.other.filter((item)=>{
            return item.is_used == 1
          });
          noUseOtherList = data.other.filter((item)=>{
            return item.is_used == 0
          });
        }
        this.usingLists = {
          jty:[...useJtyList],
          other:[...useOtherList]
        };
        this.noUsingLists = {
          jty:[...noUseJtyList],
          other:[...noUseOtherList]
        };
      } 
    },
  },

  created() {
    this.getListFunc();
  },

  computed: {
    filterDrugOptionList() {
      let { visitLevel, ...content } = this.drugOptionList;
      return content;
    }
  },
};
</script>

<style lang="scss" scoped>
#treatmentOptions {
  text-align: left;
  font-size: 16px;
  // background: #f7f7f7;
  background: #fff;

  //   height: 100vh;
  position: fixed;
  height: 100%;
  width: 100%;
  // background-color: red;
  overflow-y: auto;

  .imgsize {
    width: 16px;
    height: 16px;
    margin-right: 4px;
  }

  .head {
    font-size: 13px;
    font-weight: 400;
    background: #F7F7F7;
    color: #a7a7a7;
    line-height: 18px;
    padding: 12px 20px;
  }

  .drug {
    background: #f9f9f9;
    padding: 14px 20px;
    // margin-bottom: 12px;
    margin:15px 12px;
    border-radius: 8px;

    .drugType {
      font-size: 14px;
      color: #333;
      // font-weight: 600;
      line-height: 16px;
      //   padding-bottom: 20px;
    }

    .drugContent {
      background: #ffffff;
      box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.1);
      border-radius: 6px;
      border: 1px solid #f7f7f7;
      padding: 14px 11px;

      .drugContentName {
        margin-bottom: 13px;
        color: #333;
      }

      .label_width {
        width: 96px;
        min-width: 96px;
        color: #000;
        margin-right: 8px;
      }
    }
    .marBottom {
      margin-bottom: 12px;
    }
  }
}

.flex {
  display: flex;

  .flexLeft {
    width: 104px;
    line-height: 24px;
  }
  .flexRight {
    flex: 1;
    line-height: 24px;
  }
}
.flexHead {
  display: flex;
  align-items: center;
}

.fontsize {
  font-size: 16px;
}

.word {
  color: #666666;
}

.bottom {
  margin-bottom: 8px;
}
// .van-collapse-item__content{
//     padding: 16px 0 16px 16px;
// }


.hidden{
  	white-space: nowrap;   //使文本单行显示
	text-overflow: ellipsis;  //多余的部分用省略号来代替
	overflow: hidden;  
}
</style>
<style lang="scss">
#treatmentOptions{
  .van-cell__title, .van-cell__value{
    font-size: 0.5rem;
    font-weight: 600;
    font-family: PingFangSC-Medium, PingFang SC;
    line-height: 22px;
    padding-left: 20px;
  }
}
</style>