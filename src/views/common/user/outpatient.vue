<template>
  <div :class="{'outpatient-wrapper': 1, 'outpatient-edit': editFlag}">
    <group-field
      name="医院"
      :value="hospData"
    />
    <group-field
      name="地址"
      :value="addrData"
    />
    <group-field
      ref="clinicComp"
      :editFlag="editFlag"
      name="门诊地址"
      :value="clinicAddrData"
    />
    <group-field name="坐诊时间" />
    <schedule
      ref="scheduleComp"
      :editFlag="editFlag"
      :scheduleProp="scheduleData"
      class="group-schedule"
    />
    <div v-if="editFlag" class="group-warning">
      <van-icon
        name="warning-o"
        class="group-warning-icon"
      />
      <span class="group-warning-text">注意：点击坐诊，选中坐诊时间</span>
    </div>
    <!-- 底部按钮 -->
    <footer-edit
      v-if="editFlag"
      @cancel="cancel"
      @save="save"
      :setting="dispose"
    />
  </div>
</template>

<script>
import groupField from './components/groupField.vue'
import footerEdit from './components/footerEdit.vue'
import schedule from '../components/schedule.vue'
import { userResum, userResumSave } from '@/api/common/user.js'

export default {
  data: () => {
    return {
      workRoomId: '',
      hospData: '',
      addrData: '',
      clinicAddrData: '',
      dispose: false,
      scheduleData: [
        {
          duration_type: 1, // 1上午 2下午
          mon: 0, // 周一 1坐诊 0不坐诊
          tue: 0, // 周二 1坐诊 0不坐诊
          wed: 0, // 周三 1坐诊 0不坐诊
          thu: 0, // 周四 1坐诊 0不坐诊
          fri: 0, // 周五 1坐诊 0不坐诊
          sat: 0, // 周六 1坐诊 0不坐诊
          sun: 0 // 周日 1坐诊 0不坐诊
        },
        {
          duration_type: 2, // 1上午 2下午
          mon: 0, // 周一 1坐诊 0不坐诊
          tue: 0, // 周二 1坐诊 0不坐诊
          wed: 0, // 周三 1坐诊 0不坐诊
          thu: 0, // 周四 1坐诊 0不坐诊
          fri: 0, // 周五 1坐诊 0不坐诊
          sat: 0, // 周六 1坐诊 0不坐诊
          sun: 0 // 周日 1坐诊 0不坐诊
        }
      ]
    }
  },
  props: {
    editFlag: {
      type: Boolean,
      default: true
    },
    hospProp: {
      type: String,
      default: ''
    },
    addrProp: {
      type: String,
      default: ''
    },
    clinicAddrProp: {
      type: String,
      default: ''
    },
    scheduleProp: {
      type: Array,
      default: () => {
        return [
          {
            duration_type: 1, // 1上午 2下午
            mon: 0, // 周一 1坐诊 0不坐诊
            tue: 0, // 周二 1坐诊 0不坐诊
            wed: 0, // 周三 1坐诊 0不坐诊
            thu: 0, // 周四 1坐诊 0不坐诊
            fri: 0, // 周五 1坐诊 0不坐诊
            sat: 0, // 周六 1坐诊 0不坐诊
            sun: 0 // 周日 1坐诊 0不坐诊
          },
          {
            duration_type: 2, // 1上午 2下午
            mon: 0, // 周一 1坐诊 0不坐诊
            tue: 0, // 周二 1坐诊 0不坐诊
            wed: 0, // 周三 1坐诊 0不坐诊
            thu: 0, // 周四 1坐诊 0不坐诊
            fri: 0, // 周五 1坐诊 0不坐诊
            sat: 0, // 周六 1坐诊 0不坐诊
            sun: 0 // 周日 1坐诊 0不坐诊
          }
        ]
      }
    }
  },
  watch: {
    hospProp(newVal, oldVal) {
      if (!this.editFlag) {
        this.hospData = newVal
      }
    },
    addrProp(newVal, oldVal) {
      if (!this.editFlag) {
        this.addrData = newVal
      }
    },
    clinicAddrProp(newVal, oldVal) {
      if (!this.editFlag) {
        this.clinicAddrData = newVal
      }
    },
    scheduleProp(newVal, oldVal) {
      if (!this.editFlag) {
        this.scheduleData = newVal
      }
    },
  },
  components: {
    schedule,
    'group-field': groupField,
    'footer-edit': footerEdit
  },
  created() {
    let query = this.$route.query
    this.dispose = query.dispose
    if (this.editFlag) {
      // 初始化
      this.init()
    } else {
      this.scheduleData = this.scheduleProp
    }
  },
  methods: {
    /**
     * 初始化
     */
    init() {
      // 工作室id
      this.workRoomId = this.$route.query.workRoomId
      // 获取个人主页-简历、排班
      this.getUserResum()
    },
    /**
     * 获取个人主页-简历、排班
     */
    getUserResum() {
      userResum(this.workRoomId).then(res => {
        if (res.code === 200) {
          let data = res.data
          let hosp = data.hosp || {}
          let resume = data.resume || {}

          this.hospData = hosp.name || ''
          this.addrData = hosp.full_addr || ''
          this.clinicAddrData = resume.clinic_addr || ''

          if (data.clinic_time && data.clinic_time.length > 0) this.scheduleData = data.clinic_time
        } else {
          this.$toast(res.msg)
        }
      }).catch(err => {
        this.$toast(err)
      })
    },
    /**
     * 保存
     */
    save() {
      let clinicAddrData = this.$refs.clinicComp.text
      let scheduleData = this.$refs.scheduleComp.scheduleData
      userResumSave({
        workroom_id: this.workRoomId,
        clinic_addr: clinicAddrData,
        clinic_time: JSON.stringify(scheduleData)
      }).then(res => {
        if (res.code === 200) {
          this.$toast('保存成功')
          this.$router.go(-1)
        } else {
          this.$toast(res.msg)
        }
      }).catch(err => {
        this.$toast(err)
      })
    },
    /**
     * 取消
     */
    cancel() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.outpatient-wrapper {

  .group-schedule {
    margin: 0 10px;
  }

  .group-warning {
    height: 40px;
    display: flex;
    padding-left: 20px;
    align-items: center;
    justify-content: flex-start;

    .group-warning-icon {
      font-size: 18px;
      color: #999999;
    }

    .group-warning-text {
      font-size: 15px;
      font-weight: 400;
      color: #333333;
      margin-left: 7px;
    }
  }
}

.outpatient-edit {
  min-height: 100vh;
  position: relative;
}
</style>
