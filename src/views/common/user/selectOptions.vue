<!--
 * @Descripttion: 
 * @version: 
 * @Author: xulh
 * @Date: 2021-05-26 13:53:06
 * @LastEditors: xulh
 * @LastEditTime: 2021-06-04 15:04:52
-->
<template>
  <div id="selectOptions">
    <div class="head flexHead">
      <img class="imgsize" src="./images/<EMAIL>" alt="" />
      截止至V{{visitLevel}}
    </div>

    <div class="drug">
      <!-- <div class="drugType">
        {{ typeCategoryDictionary[drug.type_category] }}
      </div> -->

      <!-- <van-collapse :border="false" v-model="activeDrugsNames">
        <van-collapse-item :name="drug.drug_id"> -->
            <!-- <div class="fontsize">{{ drugNameFunc(drug.general_name, drug.drug_alias , drug.drug_name ) }}</div> -->
          <div class="drugContent">
            <div class="flex nameDetail">
              <div class="label_width">药品名称</div>
              <div class="word">{{ drugNameFunc(drug.general_name, drug.drug_alias , drug.drug_name ) }}</div>
            </div>
            <div class="flex detail">
              <div class="label_width">类型</div>
              <div class="word">{{ drug.type_name }}</div>
            </div>

            <div class="flex detail">
              <div class="label_width">{{medicineTimeFunc(drug.drug_dose , drug.week_dose)}}</div>
              <div class="word">{{medicineUseFunc(drug.drug_dose , drug.week_dose , drug.unit)}}</div>
            </div>

            <div class="flex detail">
              <div class="label_width">正在使用</div>
              <div class="word">{{ drug.is_used ? "是" : "否" }}</div>
            </div>

            <div class="flex detail">
              <div class="label_width">开始时间</div>
              <div class="word">{{ timeFunc(drug.start_date , '开始') }}</div>
            </div>

            <div class="flex detail">
              <div class="label_width">最新调整日期</div>
              <div class="word">{{ timeFunc(drug.adjust_date,'调整') }}</div>
            </div>

            <div class="flex detail">
              <div class="label_width">结束日期</div>
              <div class="word">{{ timeFunc(drug.end_date,'结束') }}</div>
            </div>

            <div class="flex detail">
              <div class="label_width">用法用量</div>
              <div class="word">
                <div v-if="drug.usage_dosage_morning">早 {{ drug.usage_dosage_morning }}{{drug.unit}}</div>
                <div v-if="drug.usage_dosage_noon">中 {{ drug.usage_dosage_noon }}{{drug.unit}}</div>
                <div v-if="drug.usage_dosage_night">晚 {{ drug.usage_dosage_night }}{{drug.unit}}</div>
                <div v-if="drug.usage_dosage_bedtime">睡前 {{ drug.usage_dosage_bedtime }}{{drug.unit}}</div>
                <div v-for="(i,j) in usage_dosage_other" :key="j">
                   <span v-if="i.text">{{i.text}} {{i.dose}}{{drug.unit}}</span> 
                </div>
              </div>
            </div>

            <div class="flex detail">
              <div class="label_width">备注</div>
              <div class="word">{{ drug.remark }}</div>
            </div>
          </div>
        <!-- </van-collapse-item>
      </van-collapse> -->
    </div>
  </div>
</template>

<script>
import {
  drugNameFunc,
  medicineTimeFunc,
  medicineUseFunc,
  detailmedicineUseFunc,
  typeCategoryDictionary,
  timeFunc
} from "./publicFunc.js";
export default {
  data() {
    return {
      activeDrugsNames: [],
      visitLevel:'',
      drugDetail: null,
      typeCategoryDictionary: typeCategoryDictionary,
    };
  },
  methods: {
    drugNameFunc(general_name = null, drug_alias = null, drug_name = null) {
      return drugNameFunc(general_name, drug_alias, drug_name);
    },
    medicineTimeFunc(drug_dose = 0, week_dose = 0) {
      return medicineTimeFunc(drug_dose, week_dose);
    },
    timeFunc(time , type="结束"){
        return timeFunc(time , type)
    },

    medicineUseFunc(drug_dose = 0, week_dose = 0, unit) {
      return medicineUseFunc(drug_dose, week_dose, unit);
    },
    detailmedicineUseFunc(
      usage_dosage_morning = "",
      usage_dosage_noon = "",
      usage_dosage_night = "",
      usage_dosage_bedtime = "",
      usage_dosage_other = [],
      unit = ""
    ) {
      return detailmedicineUseFunc(
        usage_dosage_morning,
        usage_dosage_noon,
        usage_dosage_night,
        usage_dosage_bedtime,
        usage_dosage_other,
        unit
      );
    },
  },

  created() {
    console.log(this.$route.query.content);
    console.log(
      "%c [ this.$router ]: ",
      "color: #bf2c9f; background: pink; font-size: 13px;",
      JSON.parse(this.$route.query.content)
    );
    this.drug = JSON.parse(this.$route.query.content);
    this.visitLevel = this.$route.query.visitLevel;
    this.activeDrugsNames = [this.drug.drug_id];
  },
};
</script>

<style lang="scss" scoped>
#selectOptions {
  text-align: left;
  font-size: 16px;
//   background: #f7f7f7;
  background: #fff;

  //   height: 100vh;
  position: fixed;
  height: 100%;
  width: 100%;
  // background-color: red;
  overflow-y: auto;

  .imgsize {
    width: 16px;
    height: 16px;
    margin-right: 4px;
  }

  .head {
    font-size: 13px;
    font-weight: 400;
  background: #f7f7f7;
    color: #a7a7a7;
    line-height: 18px;
    padding: 12px 20px;
  }

  .drug {
    background: #fff;
    padding: 14px 20px;

    // margin-bottom: 12px;

    .drugType {
      font-size: 16px;
      color: #333;
      font-weight: 600;
      line-height: 16px;
      //   padding-bottom: 20px;
    }

    .drugContent {
    //   margin-left: 29px;
    color: #333;
    line-height: 22px;
    font-weight: 600;

    .nameDetail{
        font-size: 16px;
        padding-bottom: 17px;

         .word {
          color: #666666;
          font-weight: 400;
        }
    }

      .detail {
        // height: 55px;
        // line-height: 55px;
        font-size: 16px;
        padding: 17px 0;

        .word {
          color: #666666;
          font-weight: 400;

        }
      }

      .label_width {
        width: 96px;
        min-width: 96px;
        color: #000;
        margin-right: 8px;
      }
    }
  }
}

.flex {
  display: flex;
  border-bottom: 1px solid #eee;
}
.flexHead {
  display: flex;
  align-items: center;
}

.fontsize {
  font-size: 16px;
}

// .van-collapse-item__content{
//     padding: 16px 0 16px 16px;
// }
</style>