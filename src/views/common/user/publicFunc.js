/*
 * @Descripttion:
 * @version:
 * @Author: xulh
 * @Date: 2021-06-04 10:50:14
 * @LastEditors: xulh
 * @LastEditTime: 2021-06-10 11:35:15
 */
export const drugNameFunc = (general_name = null, drug_alias = null, drug_name = null) => {
    if (drug_alias) return `${drug_alias}`;
    return general_name;
}

export const medicineTimeFunc = (drug_dose = 0, week_dose = 0) => {
    if (drug_dose > 0) return "每日总剂量";
    if (week_dose > 0) return "每周总剂量";
    return "";
}

export const medicineUseFunc = (drug_dose = 0, week_dose = 0, unit) => {
    if (drug_dose > 0) return `${drug_dose}${unit}`;
    if (week_dose > 0) return `${week_dose}${unit}`;
    return `0${unit}`;
}

export const detailmedicineUseFunc = (
    usage_dosage_morning = "",
    usage_dosage_noon = "",
    usage_dosage_night = "",
    usage_dosage_bedtime = "",
    usage_dosage_other = [],
    unit = ""
) => {
    let morning = "";
    let noon = "";
    let night = "";
    let bed = "";
    let other = "";
    if (usage_dosage_morning) morning = `早 ${usage_dosage_morning}${unit}; `;
    if (usage_dosage_noon) noon = `中 ${usage_dosage_noon}${unit}; `;
    if (usage_dosage_night) night = `晚 ${usage_dosage_night}${unit}; `;
    if (usage_dosage_bedtime) bed = `睡前 ${usage_dosage_bedtime}${unit}; `;
    if (usage_dosage_other == '') usage_dosage_other = [];
    if (usage_dosage_other.length > 0) {
        // console.log('[ usage_dosage_other ] >', usage_dosage_other)
        usage_dosage_other.forEach(i => {
            if (i.text) other += `${i.text} ${i.dose}${unit}`
        })
    }
    return morning + noon + night + bed + other;
}


export const typeCategoryDictionary = {
    1:'降糖药物',
    2:'其他药物'
}

export const timeFunc =(time , type='结束')=>{
    if(time == '0001-01-01'){
        return `无${type}时间`
    }

    return time;
}
