<template>
  <div class="wrapper">
    <div class="title">{{ type_name }}</div>
    <div @click.stop.prevent="inputPaentClick('textarea')">
      <textarea
        v-model="text"
        ref="textarea"
        :placeholder="placeholder"
        maxlength="1000"
        class="textarea"
      ></textarea>
    </div>
    <!-- <van-field
      v-model="text"
      rows="8"
      autosize
      type="textarea"
      :placeholder="placeholder"
      maxlength="1000"
      class="textarea"
    /> -->
    <!-- <div class="footer-div"></div> -->
    <!-- 底部按钮 -->
    <footer-edit @cancel="cancel" @save="save" />
  </div>
</template>

<script>
import footerEdit from "./components/footerEdit.vue";
import { userResum, userResumSave } from "@/api/common/user.js";

export default {
  data: () => {
    return {
      workRoomId: "",
      key: "",
      text: "",
      placeholder: "",
      type_name: "",
    };
  },
  watch: {
    text (nv, ov) {
      if (nv === ov) {
        return
      }
      this.currentValue = nv
      // console.log('value changed')
      this.changeHeight()
    }
  },
  components: {
    "footer-edit": footerEdit,
  },
  created() {
    let query = this.$route.query;
    let type = query.type;
    if (type == 1) {
      this.type_name = "医生简介";
      this.placeholder = "输入您的从业经历等信息（1000字以内）";
    } else if (type == 2) {
      this.type_name = "擅长病症";
      this.placeholder = "输入您的专业领域、擅长病症等信息（1000字以内）";
    } else if (type == 3) {
      this.type_name = "简介编辑";
      this.placeholder = "输入您的从业经历等信息（1000字以内）";
    }
    document.title = this.type_name;
    this.workRoomId = query.workRoomId;
    userResum(query.workRoomId).then((res) => {
      if (res.code === 200) {
        // console.log(query.key)
        this.key = query.key
        this.text = res.data.resume[query.key]
      }
    })
    // if (Reflect.has(query, "intro")) {
    //   this.key = "intro";
    //   this.text = query.intro;
    // } else if (Reflect.has(query, "expert")) {
    //   this.key = "expert";
    //   this.text = query.expert;
    // }
  },
  mounted() {
  },
  methods: {
    changeHeight () {
      let _this = this
      this.$nextTick(() => {
        var textArea = _this.$refs.textarea
        var scrollHeight = textArea.scrollHeight // 控件所有的高度，包含滚动的那部分(不可见也会有高度)
        var height = textArea.offsetHeight // 屏幕上显示的高度
        if (height <= scrollHeight) {
          textArea.style.height = 'auto' // 恢复默认值，这个作用就是根据内容自适应textarea高度
          textArea.style.height = textArea.scrollHeight + 'px' // 拿到最新的高度改变textarea的高度
        }
      })
    },
    /**
     * 保存
     */
    save() {
      this.noDoubleTap(() => {
        userResumSave({
          workroom_id: this.workRoomId,
          [this.key]: this.text,
        })
          .then((res) => {
            if (res.code === 200) {
              this.$toast("保存成功");
              this.$router.go(-1);
            } else {
              this.$toast(res.msg);
            }
          })
          .catch((err) => {
            this.$toast(err);
          });
      });
    },
    /**
     * 取消
     */
    cancel() {
      this.$router.go(-1);
    },
    inputPaentClick(refName) {
      this.$nextTick(() => {
        this.$refs[refName] && this.$refs[refName].focus();
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.wrapper {
  .title {
    margin: 11px 31px;
    font-size: 16px;
    font-weight: 500;
    color: #000000;
    line-height: 22px;
    text-align: left;
  }
  .textarea {
    display: block;
    box-sizing: border-box;
    width: calc(100% - 62px);
    min-height: 250px;
    font-size: 18px;
    font-weight: 400;
    padding: 10px 16px;
    margin: 11px 31px;
    background: #f7f7f7;
    color: #333333;
    text-align: left;
    border: 0;
    resize: none;
  }
  // .textarea {
  //   width: initial;
  //   font-size: 18px;
  //   font-weight: 400;
  //   color: #333333;
  //   margin: 11px 31px;
  //   background: #f7f7f7;
  //   box-sizing: border-box;
  // }
  // .footer-div{
  //   height: 46px;
  // }
}
::v-deep .footer {
  position: relative;
}
</style>
