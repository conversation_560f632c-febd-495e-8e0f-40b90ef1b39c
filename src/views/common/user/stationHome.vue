<template>
  <div class="wrapper">
    <div class="group">
      <group-top
        :typeIcon="require('./images/<EMAIL>')"
        :editIcon="require('./images/<EMAIL>')"
        @goEdit="goEdit('/common/user/edit', 'intro', intro,3)"
        titleName="个人简介"
      />
      <div v-if="intro" class="group-p">{{ intro }}</div>
      <p
        v-else
        @click="goEdit('/common/user/edit', 'intro', intro,3)"
        class="group-p group-p-no"
      >
        <span class="group-p-span">您还未填写，去编辑</span>
        <van-icon name="arrow" class="group-p-icon" />
      </p>
    </div>
  </div>
</template>

<script>
  import homeGroupTop from "./components/homeGroupTop.vue";
  import { userResum } from "@/api/common/user.js";

  export default {
    data: () => {
      return {
        workRoomId: "",
        intro: "",
      };
    },
    components: {
      "group-top": homeGroupTop,
    },
    mounted() {
      // 如果检测到页面是从“往返缓存”中读取的，刷新页面
      window.addEventListener(
        "pageshow",
        (e) => {
          if (
            e.persisted ||
            (window.performance && window.performance.navigation.type == 2)
          ) {

            // 获取个人主页-简历、排班
            this.getUserResum();
          }
        },
        false
      );
    },
    created() {
      // 工作室id
      this.workRoomId = this.$route.query.workroom_id;

      // 获取个人主页-简历、排班
      this.getUserResum();
    },
    methods: {
      /**
       * 获取个人主页-简历、排班
       */
      getUserResum() {
        userResum(this.workRoomId)
          .then((res) => {
            if (res.code === 200) {
              let data = res.data;
              let resume = data.resume || {};
              this.intro = resume.intro || "";
            } else {
              this.$toast(res.msg);
            }
          })
          .catch((err) => {
            this.$toast(err);
          });
      },
      /**
       * 前往编辑
       * @param {String} path 路径
       * @param {String} key 键
       * @param {String} val 值
       */
      goEdit(path, key = "", val = "",type='') {
        let query;
        if (key === "") {
          query = {};
        } else {
          query = {
            [key]: val,
            key,
          };
        }

        this.$router.push({
          path,
          query: {
            workRoomId: this.workRoomId,
            type:type,
            ...query,
          },
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .wrapper {
    overflow: hidden;
    background: #f7f7f7;

    .group {
      margin-top: 10px;
      background: #fff;

      &:first-of-type {
        margin-top: 0;
      }

      .group-p {
        font-size: 18px;
        text-align: left;
        font-weight: 400;
        color: #333333;
        padding: 9px 18px;
        line-height: 30px;
        white-space: pre-wrap;
        white-space: -moz-pre-wrap;
        white-space: -o-pre-wrap;
        word-wrap: break-word;
      }

      .group-p-no {
        display: flex;
        color: #cccccc;
        align-items: center;
        justify-content: space-between;
      }
    }
  }
</style>
