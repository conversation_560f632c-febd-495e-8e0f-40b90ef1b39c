<template>
  <div class="top-wrapper">
    <div class="left">
      <div class="left-icon">
        <img class="default-img" :src="typeIcon" >
      </div>
      <span class="left-title">{{ titleName }}</span>
    </div>
    <div class="right" @click="goEdit">
      <img class="default-img" :src="editIcon" >
    </div>
  </div>
</template>

<script>
export default {
  data: () => {
    return {}
  },
  props: {
    typeIcon: {
      type: String,
      default: ''
    },
    editIcon: {
      type: String,
      default: ''
    },
    titleName: {
      type: String,
      default: ''
    }
  },
  methods: {
    /**
     * 前往编辑
     */
    goEdit() {
      this.$emit('goEdit')
    }
  }
}
</script>

<style lang="scss" scoped>
.top-wrapper {
  height: 50px;
  display: flex;
  padding: 0 18px;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #F7F7F7;

  .left {
    display: flex;
    align-items: center;
    justify-content: flex-start;

    .left-icon {
      width: 18px;
      line-height: 0;
    }

    .left-title {
      font-size: 20px;
      font-weight: 400;
      color: #333333;
      margin-left: 10px;
    }
  }

  .right {
    width: 21px;
    line-height: 0;
  }
}

.default-img {
  width: 100%;
}
</style>
