<template>
  <div class="footer">
    <van-button
      v-if="setting == 'false' || setting == false"
      round
      type="info"
      class="footer-btn"
      @click="cancel"
    >取消</van-button>
    <van-button
      round
      type="info"
      :class="[setting == 'false' || setting == false ? 'footer-btn footer-save-btn' : 'new-footer-btn new-footer-save-btn']"
      @click="save"
    >保存</van-button>
  </div>
</template>

<script>
export default {
  props: {
    setting: {
      type: Boolean,
      default: false
    }
  },
  created() {
    console.log(typeof this.setting)
  },
  methods: {
    /**
     * 保存
     */
    save() {
      this.$emit('save')
    },
    /**
     * 取消
     */
    cancel() {
      this.$emit('cancel')
    }
  }
}
</script>

<style lang="scss" scoped>
.footer {
  width: 100%;
  transform: translateX(-50%);
  position: absolute;
  bottom: 10px;
  left: 50%;
  .footer-btn {
    width: 150px;
    height: 40px;
    line-height: 40px;
    font-size: 18px;
    font-weight: 500;
    color: #EE7800;
    border-radius: 20px;
    background: #FFFFFF;
    border: 1px solid #EE7800;
  }

  .footer-save-btn {
    color: #FFFFFF;
    margin-left: 23px;
    background: linear-gradient(90deg, #FF8100 0%, #FD9A57 100%);
  }


  .new-footer-btn {
    width: 335px;
    height: 40px;
    line-height: 40px;
    font-size: 18px;
    font-weight: 500;
    color: #EE7800;
    border-radius: 6px;
    border: 1px solid #EE7800;
  }

  .new-footer-save-btn {
    color: #FFFFFF;
    background: linear-gradient(90deg, #FF8100 0%, #FD9A57 100%);
  }
}
</style>
