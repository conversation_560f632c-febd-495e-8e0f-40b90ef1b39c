<template>
  <div class="field-wrapper">
    <span class="field-wrapper-span">{{ name }}：</span>
    <span v-if="!editFlag" class="field-wrapper-span field-wrapper-span-value">{{ value }}</span>
    <van-field
      v-else
      v-model="text"
      rows="1"
      autosize
      type="textarea"
      placeholder="请输入"
      class="field-input"
    />
  </div>
</template>

<script>
export default {
  data: () => {
    return {
      text: ''
    }
  },
  watch: {
    value(newVal, oldVal) {
      if (this.editFlag) {
        this.text = newVal
      }
    }
  },
  props: {
    name: {
      type: String,
      default: ''
    },
    value: {
      type: String,
      default: ''
    },
    editFlag: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    /**
     * 初始化
     */
    init() {}
  }
}
</script>

<style lang="scss" scoped>
.field-wrapper {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  font-size: 18px;
  font-weight: 400;
  color: #333333;
  text-align: left;
  padding: 10px 0 10px 18px;
  border-bottom: 1px solid #F7F7F7;

  .field-wrapper-span {
    line-height: 24px;
  }

  .field-wrapper-span-value {
    flex: 1;
  }

  &:last-of-type {
    border-bottom: 0;
  }

  .field-input {
    flex: 1;
    padding: 0;
    width: initial;
    font-size: 18px;
    font-weight: 400;
    color: #333333;
    box-sizing: border-box;
  }
}
</style>
