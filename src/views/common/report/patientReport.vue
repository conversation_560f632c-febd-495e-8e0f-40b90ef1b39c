<template>
  <div class="wrapper">
    <h1 class="report-title">患者报告</h1>
    <p class="report-time">报告时间：{{ measureInfo.date.start }} 至 {{ measureInfo.date.end }}</p>

    <!-- 血压测量结果 -->
    <div
      v-if="measureInfo.bp.number != undefined && measureInfo.bp.number >= 2"
      :class="{'bloodPressure': true, 'bloodPressure1': measureInfo.bp.is_normal == 1, 'bloodPressure3': measureInfo.bp.is_normal == 3}"
      @click="bloodReport"
    >
      <div class="blood">
        <img v-if="measureInfo.bp.is_normal == 1" src="../images/<EMAIL>" />
        <img v-if="measureInfo.bp.is_normal == 3" src="../images/<EMAIL>" />
        <span>血压<span style="font-size:12px;">(平均值)</span></span>
      </div>
      <div class="high">
        <span>{{ measureInfo.bp.avg_sbp }}</span>
        <span>高压</span>
      </div>
      <div class="down">
        <span>{{ measureInfo.bp.avg_dbp }}</span>
        <span>低压</span>
      </div>
      <img
        class="positionPic"
        v-if="measureInfo.bp.is_normal == 1"
        src="../images/<EMAIL>"
      />
      <img
        class="positionPic"
        v-if="measureInfo.bp.is_normal == 3"
        src="../images/<EMAIL>"
      />
    </div>

    <!-- 血压无数据 -->
    <div v-if="measureInfo.bp.number == undefined || measureInfo.bp.number < 2" class="bloodPressureBlank">
      <div class="blood">
        <img src="../images/<EMAIL>" />
        <span>血压</span>
      </div>
      <span class="mark">健康报告最少需要2次完整血压数据才能生成</span>
    </div>

    <!-- 血糖测量结果 -->
    <div
      v-if="measureInfo.bg.number != undefined && measureInfo.bg.number >= 2"
      :class="{'bloodSugar': true, 'bloodSugar1': measureInfo.bg.is_normal == 1, 'bloodSugar2': measureInfo.bg.is_normal == 2, 'bloodSugar3': measureInfo.bg.is_normal == 3}"
      @click="sugarReport"
    >
      <div class="blood">
        <img
          v-if="measureInfo.bg.is_normal == 1"
          src="../images/<EMAIL>"
        />
        <img
          v-if="measureInfo.bg.is_normal == 2"
          src="../images/<EMAIL>"
        />
        <img
          v-if="measureInfo.bg.is_normal == 3"
          src="../images/<EMAIL>"
        />
        <span>血糖</span>
      </div>
      <div class="high">
        <span>{{ measureInfo.bg.rate ? Number(measureInfo.bg.rate).toFixed(1) + '%' : 0 }}</span>
        <span>达标比例</span>
      </div>
      <div class="down">
        <span>{{ measureInfo.bg.unusual }}</span>
        <span>异常次数</span>
      </div>
      <img
        class="positionPic"
        v-if="measureInfo.bg.is_normal == 1"
        src="../images/<EMAIL>"
      />
      <img
        class="positionPic"
        v-if="measureInfo.bg.is_normal == 2"
        src="../images/<EMAIL>"
      />
      <img
        class="positionPic"
        v-if="measureInfo.bg.is_normal == 3"
        src="../images/<EMAIL>"
      />
    </div>

    <!-- 血糖无数据 -->
    <div class="bloodSugarBlank" v-if="measureInfo.bg.number == undefined || measureInfo.bg.number < 2">
      <div class="blood">
        <img src="../images/<EMAIL>" />
        <span>血糖</span>
      </div>
      <span class="mark">健康报告最少需要2次完整血糖数据才能生成</span>
    </div>

    <!-- 体重&步数 -->
    <div class="weightSport">
      <div class="weight" @click='weightReport'>
        <div class="weightTitle">
          <img
            v-if="measureInfo.weight.is_normal == 3"
            src="../images/bmi_blue.png"
          />
          <img
            v-if="measureInfo.weight.is_normal == 1"
            src="../images/<EMAIL>"
          />
          <img
            v-if="measureInfo.weight.is_normal == 2"
            src="../images/<EMAIL>"
          />
          <img
            v-if="measureInfo.weight.is_normal == 4"
            src="../images/<EMAIL>"
          />
          <img
            v-if="measureInfo.weight.is_normal == 5"
            src="../images/<EMAIL>"
          />
          <span class="title">体重</span>
          <span 
            class="weightStatus"
            v-if="measureInfo.weight.weight != undefined"
          >{{ measureInfo.weight.normal_text }}</span>
        </div>
        <div class="weights">
          <span>{{ measureInfo.weight.weight == undefined ? '--' : measureInfo.weight.weight }}</span>
          <span>kg</span>
        </div>
      </div>
      <div class="sport" @click="sportReport">
        <div class="sportTitle">
          <img src="../images/<EMAIL>" />
          <span>步数</span>
        </div>
        <div class="sportt">
          <span>平均每日</span>
          <span>{{ measureInfo.step == undefined || measureInfo.step == 0 ? '--' : measureInfo.step }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { reportData } from '@/api/common/report'
export default {
  data: () => {
    return {
      userId: '', // 用户ID
      measureInfo: {
        date: {
          start: '',
          end: ''
        },
        bp: {},
        bg: {},
        weight: {},
        step: ''
      }
    }
  },
  created () {
    this.userId = this.$route.query.user_id
    // 获取患者报告
    this.getReportData()
  },
  methods: {
    /**
     * 获取患者报告
     */
    getReportData() {
      reportData(this.userId).then(res => {
        if (res.code === 200) {
          this.measureInfo = res.data
        } else {
          this.$toast(res.msg)
        }
      }).catch(err => {
        this.$toast(err)
      })
    },
    /**
     * 血压跳转
     */
    bloodReport() {
      // 调起app跳转页
      this.goAppReport(`/common/report/bp`)
    },
    /**
     * 血糖跳转
     */
    sugarReport() {
      // 调起app跳转页
      this.goAppReport(`/common/report/bg`)
    },
    /**
     * 体重跳转
     */
    weightReport() {
      if (this.measureInfo.weight.weight != undefined) {
        // 调起app跳转页
        this.goAppReport(`/common/report/weight`)
      }
    },
    /**
     * 步数跳转
     */
    sportReport() {
      if (this.measureInfo.step != undefined && this.measureInfo.step != 0) {
        // 调起app跳转页
        this.goAppReport(`/common/report/sport`)
      }
    },
    /**
     * 调起app跳转页
     */
    goAppReport(url) {
      const UA = navigator.userAgent
      const isAndroid = UA.indexOf('Android') > -1 || UA.indexOf('Adr') > -1 // android终端
      const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端

      let date = this.measureInfo.date
      url += `?user_id=${ this.userId }&start_date=${ date.start }&end_date=${ date.end }`

      // 原生app方法名称：jumpToReport
      if (isAndroid) {
        console.log('安卓')
        window.android.jumpToReport(url)
      } else if (isIOS) {
        console.log('苹果')
        window.webkit.messageHandlers.jumpToReport.postMessage(url)
      } else {
        this.$toast('跳转失败')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.wrapper {
  overflow: hidden;
  text-align: left;
  background: #fff;

  .report-title {
    font-size: 20px;
    font-weight: 500;
    color: #333333;
    margin-top: 13px;
    margin-left: 16px;
    line-height: 29px;
  }

  .report-time {
    font-size: 18px;
    font-weight: 400;
    color: #666666;
    margin-top: 24px;
    margin-left: 16px;
    margin-bottom: 10px;
    line-height: 25px;
  }
}

.bloodPressure,
.bloodSugar {
  width: 350px;
  height: 85px;
  background: #f3f4f9;
  border-radius: 6px;
  margin: 0 auto;
  margin-bottom: 12px;
  position: relative;
}

.bloodPressure .high,
.bloodPressure .down,
.bloodSugar .high,
.bloodSugar .down {
  width: 68px;
  height: 64px;
  float: left;
  margin-top: 11px;
  margin-right: 10px;
}

.bloodPressure .positionPic,
.bloodSugar .positionPic {
  width: 66px;
  height: 56px;
  position: absolute;
  right: 0;
  bottom: 0;
}

.bloodPressure .high span:nth-child(1),
.bloodPressure .down span:nth-child(1),
.bloodSugar .high span:nth-child(1),
.bloodSugar .down span:nth-child(1) {
  font-size: 32px;
  height: 45px;
  color: #262626;
  text-align: center;
  display: block;
  opacity: 0.8;
}

.bloodPressure .high span:nth-child(2),
.bloodPressure .down span:nth-child(2),
.bloodSugar .high span:nth-child(2),
.bloodSugar .down span:nth-child(2) {
  font-size: 13px;
  color: #999;
  text-align: center;
  display: block;
}

.bloodPressure1,
.bloodSugar1 {
  border-left: 5px solid #95d652;
}

.bloodPressure2,
.bloodSugar2 {
  border-left: 5px solid #f7c94f;
}

.bloodPressure3,
.bloodSugar3 {
  border-left: 5px solid #fa7343;
}

.bloodPressureBlank,
.bloodSugarBlank {
  width: 355px;
  height: 85px;
  margin: 0 auto;
  border-radius: 6px;
  margin-bottom: 12px;
  background: #f3f4f9;
}

.bloodPressureBlank .blood,
.bloodSugarBlank .blood,
.bloodPressure .blood,
.bloodSugar .blood {
  width: 140px;
  margin: 13px 0 13px 14px;
  float: left;
}

.bloodPressureBlank .blood img,
.bloodSugarBlank .blood img,
.bloodPressure .blood img,
.bloodSugar .blood img {
  width: 28px;
  height: 28px;
  float: left;
  margin-right: 9px;
}

.bloodPressureBlank .blood span,
.bloodSugarBlank .blood span {
  font-size: 18px;
  line-height: 28px;
  color: #666;
  float: left;
}

.bloodPressure .blood span,
.bloodSugar .blood span {
  font-size: 18px;
  line-height: 28px;
  color: #666;
}

.bloodPressureBlank .mark,
.bloodSugarBlank .mark {
  width: 176px;
  height: 42px;
  line-height: 21px;
  opacity: 0.8;
  font-size: 15px;
  color: #999;
  float: left;
  margin-top: 24px;
}

.weightSport {
  width: 355px;
  height: 78px;
  margin: 0 auto;
  margin-bottom: 12px;
}
.weightSport .weight {
  width: 172px;
  height: 78px;
  background: #f6f9fd;
  border-radius: 6px;
  margin-right: 11px;
  float: left;
}
.weightSport .weight .weightTitle {
  width: 172px;
  margin: 12px 0 0 9px;
  float: left;
}
.weightSport .weight .weightTitle img {
  width: 24px;
  height: 24px;
  float: left;
  margin-right: 6px;
}
.weightSport .weight .weightTitle .title {
  font-size: 18px;
  line-height: 28px;
  color: #666;
  float: left;
}
.weightSport .weight .weightTitle .weightStatus {
  width: 35px;
  height: 20px;
  text-align: center;
  line-height: 20px;
  padding: 0 1.5px;
  border: 1px solid #ef7c69;
  color: #ef7c69;
  font-size: 12px;
  border-radius: 5px;
  float: left;
  margin: 3px 0 0 48px;
}

.weightSport .weights {
  font-size: 18px;
  line-height: 28px;
  float: left;
  margin-left: 50px;
}
.weightSport .weights span:nth-child(1) {
  opacity: 0.8;
  font-size: 28px;
  color: #333;
  margin-right: 7px;
}
.weightSport .weights span:nth-child(2) {
  font-size: 12px;
  color: #999;
}

.weightSport .sport {
  width: 172px;
  height: 78px;
  background: #f6f9fd;
  border-radius: 3px;
  float: left;
}
.weightSport .sport .sportTitle {
  width: 172px;
  margin: 12px 0 0 9px;
  float: left;
}
.weightSport .sport .sportTitle img {
  width: 24px;
  height: 24px;
  float: left;
  margin-right: 6px;
}
.weightSport .sport .sportTitle span {
  font-size: 18px;
  line-height: 28px;
  color: #666;
  float: left;
}

.weightSport .sportt {
  font-size: 18px;
  line-height: 28px;
  float: left;
  margin-left: 25px;
}
.weightSport .sportt span:nth-child(2) {
  opacity: 0.8;
  font-size: 28px;
  color: #333;
  margin-right: 7px;
  margin-left: 5px;
}
.weightSport .sportt span:nth-child(1) {
  font-size: 12px;
  color: #999;
}
</style>
