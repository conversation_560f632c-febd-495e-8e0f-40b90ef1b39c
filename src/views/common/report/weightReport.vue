<template>
  <div class="content">
    <div class='main'>
      <div class="marks" v-if="showModal">
        <div class="modal">
          <img class="del" @click="del" src="./../images/<EMAIL>" />
          <img class="cont" src="https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/wechat/BMI.png" />
        </div>
      </div>
      <div class="header">
        <span></span><span>体重周期变化</span><img @click="weightHelp" src="./../images/bangzhu.png" />
      </div>
      <div class="status">
        <div class="top">
          <div class="data">
            <div class="num"><span>{{ weightInfo.weight }}</span><span>kg</span><span>最新体重</span></div>
            <div class="num"><span>{{ weightInfo.height }}</span><span>cm</span><span>身高</span></div>
            <div class="num bmi"><span :class="[color(weightInfo.bmi)]">{{toFixed(weightInfo.bmi)}}</span><span>BMI</span></div>
          </div>
          <div class="scale">
            <span ref="position" :class="['positions', color(weightInfo.bmi)]">{{toFixed(weightInfo.bmi)}}</span>
            <div class="allNum">
              <span>消瘦</span><span>正常</span><span>超重</span><span>肥胖</span>
            </div>
            <div class="numbers">
              <span>18.5</span><span>23.9</span><span>28</span>
            </div>
          </div>
          <div class="weightStatus">您目前体型处于<span :class="[color(weightInfo.bmi)]">{{ weightInfo.normal_text}}</span>状态</div>
        </div>
        <span class="bg"></span>
        <div class="bottom" v-if="weightInfo.week_diff != 0 || weightInfo.week_diff_bmi != 0">
          <div class="left">
          </div>
          <div class="right">
            <div class="rightTop">同比上周期</div>
            <div class="weight3" v-if="weightInfo.week_msg != '' && weightInfo.week_diff != 0">
              <span v-if="weightInfo.week_msg != ''">{{weightInfo.week_msg == '' ? '' : weightInfo.week_msg}}</span>
              <span v-if="weightInfo.week_diff != 0">{{weightInfo.week_diff == 0 ? '' : weightInfo.week_diff}}</span>
              <span v-if="weightInfo.week_diff != 0">kg</span>
            </div>
            <div class="bmi3" v-if="weightInfo.week_bmi_msg != '' && weightInfo.week_diff_bmi != 0">
              <span v-if="weightInfo.week_bmi_msg != ''">{{weightInfo.week_bmi_msg == '' ? '' : weightInfo.week_bmi_msg}}</span>
              <span v-if="weightInfo.week_diff_bmi != 0">{{weightInfo.week_diff_bmi == 0 ? '' : toFixed(weightInfo.week_diff_bmi)}}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="mark">
        <span>健康体重范围：{{ weightInfo.health_height}}。</span>
        <span>体质指数（BMI）正常范围：{{ weightInfo.health_weight}}。</span>
      </div>
    </div>
  </div>
</template>

<script>
  import { reportWeight } from '@/api/common/report'
  export default {
    data() {
      return {
        userId: '',
        startDate: '',
        endDate: '',
        showModal: false,
        weightInfo: {
          week_diff: 0,
          week_diff_bmi: 0
        },
        lang: ''
      }
    },
    created() {
      let query = this.$route.query
      this.userId = query.user_id
      this.startDate = query.start_date
      this.endDate = query.end_date

      this.$nextTick(() => {
        this.init()
      })
    },
    methods: {
      init() {
        let that = this
        let datas = {
          user_id: this.userId, // 用户ID
          start_date: this.startDate, // 开始时间
          end_date: this.endDate // 结束时间
        }
        reportWeight(datas).then(res => {
          if (res.code == 200) {
            that.weightInfo = res.data
            if (that.weightInfo.bmi < 18.5) {
              that.lang = that.weightInfo.bmi / 18.5 * 75 - 25
            } else if (that.weightInfo.bmi > 18.5 && that.weightInfo.bmi < 23.9) {
              that.lang = (that.weightInfo.bmi - 18.5) / (23.9 - 18.5) * 92 + 76 - 25
            } else if (that.weightInfo.bmi > 23.9 && that.weightInfo.bmi < 28) {
              that.lang = (that.weightInfo.bmi - 23.9) / (28 - 23.9) * 75 + 169 - 25
            } else if (that.weightInfo.bmi > 28 && that.weightInfo.bmi < 45) {
              that.lang = (that.weightInfo.bmi - 28) / (45 - 28) * 80 + 245 - 25
            } else {
              that.lang = 580
            }
          } else {
            that.$toast(res.msg)
          }
        }).then(() => {
          that.$refs.position.style.marginLeft = that.lang + 'px'
        })
      },
      weightHelp() {
        this.showModal = true
      },
      del() {
        this.showModal = false
      },
      toFixed(value) {
        if (value == '' || value == 0) {
          return ''
        } else {
          if (Math.floor(value) !== value) {
            return Number(value).toFixed(1)
          }
          return value
        }
      },
      color(val) {
        if (val < 18.5) {
          return 'color1'
        } else if (val > 18.5 && val < 23.9) {
          return 'color2'
        } else if (val > 23.9 && val < 28) {
          return 'color3'
        } else if (val > 28 && val < 45) {
          return 'color4'
        } else {
          return 'color4'
        }
      }
    }
  }
</script>

<style lang="scss" scoped>
  .main{
    width: 355px;
    margin: 0 auto;
    background: #fff;
    padding: 0 10px;
  }
  .main .pop{
    width: 310px;
    height: 352px;
  }
  .main .header{
    height: 40px;
    line-height: 40px;
  }
  .main .header span:nth-child(1){
    width: 4px;
    height: 17px;
    background:#398CFF;
    margin: 11px 4px 0 0;
    display: block;
    float: left;
  }
  .main .header span:nth-child(2){
    font-size: 18px;
    color: #262626;
    display: block;
    float: left;
  }
  .main .header img{
    width: 19px;
    height: 19px;
    float: left;
    margin: 11px 0 0 4px;
  }
  .status{
    width: 355px;
    /* min-height: 690px; */
    background: #F6F9FD;
    border-radius: 3px;
  }
  .status .top{
    width: 355px;
    height: 215px;
  }
  .status .top .data{
    width: 355px;
    height: 75px;
    margin-bottom: 7px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .status .top .data .num{
    flex: 1;
  }
  .status .top .data .num span:nth-child(1){
    font-size: 28px;
    color: #666;
  }
  .status .top .data .num span:nth-child(2){
    font-size: 12px;
    color: #2A2A2A;
  }
  .status .top .data .num span:nth-child(3){
    display: block;
    font-size: 13px;
    color: #333;
  }
  .status .top .data .bmi span:nth-child(1){
    font-size: 28px;
    color: #73CCF6;
  }
  .status .top .data .bmi span:nth-child(2){
    display: block;
    font-size: 13px;
    color: #333;
  }
  .scale{
    width: 325px;
    margin: 0 15px;
  }
  .scale .positions{
    display: block;
    width: 50px;
    height: 33px;
    text-align: center;
    line-height: 20px;
    font-size: 19px;
    color: #FCC569;
    margin-left: 180px;
    background: url('https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/wechat/icon%402x.png') no-repeat center bottom;
    background-size:10px 12px;
    margin-left: 20px;
  }
  .scale .allNum{
    width: 325px;
    height: 24px;
    margin-top: 2px;
    background: #fff;
  }
  .scale .allNum span:nth-child(1){
    display: block;
    float: left;
    width: 75px;
    height: 24px;
    line-height: 24px;
    background: #73CCF6;
    color: #fff;
    font-size: 13px;
    margin-right: 1px;
    text-align: center;
  }
  .scale .allNum span:nth-child(2){
    display: block;
    float: left;
    width: 92px;
    height: 24px;
    line-height: 24px;
    background: #92E464;
    color: #fff;
    font-size: 13px;
    margin-right: 1px;
    text-align: center;
  }
  .scale .allNum span:nth-child(3){
    display: block;
    float: left;
    width: 75px;
    height: 24px;
    line-height: 24px;
    background: #FCC569;
    color: #fff;
    font-size: 13px;
    margin-right: 1px;
    text-align: center;
  }
  .scale .allNum span:nth-child(4){
    display: block;
    float: left;
    width: 80px;
    height: 24px;
    line-height: 24px;
    background: #EF7C69;
    color: #fff;
    font-size: 13px;
    text-align: center;
  }
  .scale .numbers{
    width: 325px;
    height: 24px;
    line-height: 24px;
    text-align: left;
  }
  .scale .numbers span:nth-child(1){
    font-size: 12px;
    color: #999;
    margin-left: 65px;
  }
  .scale .numbers span:nth-child(2){
    font-size: 12px;
    color: #999;
    margin-left: 65px;
  }
  .scale .numbers span:nth-child(3){
    font-size: 12px;
    color: #999;
    margin-left: 60px;
  }
  .weightStatus{
    width: 355px;
    height: 40px;
    line-height: 40px;
    font-size: 16px;
    color: #666;
    text-align: center;
  }
  .weightStatus span{
    height: 40px;
    line-height: 40px;
    font-size: 28px;
    color: #FCC569;
    padding: 0 4px;
  }
  .status .bg{
    display: block;
    width: 355px;
    height: 2px;
    background: #fff;
  }
  .status .left{
    width: 355px;
    height: 128px;
    background: url('https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/wechat/71%402x.png') no-repeat center center;
    background-size:40px 75px;
  }
  .status .bottom{
    width: 355px;
    height: 128px;
    background: #F6F9FD;
  }
  .status .bottom .left{
    width: 110px;
    height: 128px;
    float: left;
  }
  .status .bottom .left .ago{
    font-size: 16px;
    color: #666;
    margin-bottom: 5px;
  }
  .status .bottom .left .before{
    width: 110px;
    height: 60px;
    color: #999;
  }
  .status .bottom .left .before .ul{
    width: 18px;
    height: 60px;
    color: #999;
    font-size: 12px;
    float: left;
    margin:0 10px 0 15px;
  }
  .status .bottom .left .before .ul .li{
    width: 18px;
    height: 60px;
    color: #999;
    font-size: 12px;
    border-radius: 3px;
  }
  .status .bottom .left .before .ul .li span{
    width: 18px;
    text-align: center;
    display: block;
    color: #fff;
    font-size: 12px;
    float: left;
  }
  .status .bottom .left .leftTitle span{
    font-size: 12px;
    line-height: 26px;
    color: #999;
  }
  .status .bottom .left .leftTitle span:nth-child(1){
    margin: 0 15px 0 5px;
  }
  .status .bottom .right{
    width: 220px;
    height: 128px;
    float: left;
  }
  .rightTop{
    font-size: 18px;
    color: #4471AC;
    margin-top: 15px;
    text-align: left;
  }
  .status .bottom .right .weight3, .status .bottom .right .bmi3{
    line-height: 28px;
    text-align: left;
  }
  .status .bottom .right .weight3 span:nth-child(1){
    display: inline-block;
    font-size: 16px;
    color: #666;
  }
  .status .bottom .right .weight3 span:nth-child(2){
    display: inline-block;
    font-size: 28px;
    color: #64A5FF;
  }
  .status .bottom .right .bmi3 span:nth-child(1){
    display: inline-block;
    font-size: 16px;
    color: #666;
  }
  .status .bottom .right .bmi3 span:nth-child(2){
    display: inline-block;
    font-size: 28px;
    color: #64A5FF;
  }
  .status .bottom .right .weight3 span:nth-child(3){
    display: inline-block;
    font-size: 12px;
    color: #64A5FF;
  }
  .mark{
    height: 34px;
    line-height: 17px;
    font-size: 12px;
    color: #999;
    margin: 5px 0 15px;
    text-align: left;
  }
  .mark span{
    display: block;
  }
  .bg{
    width: 375px;
    height: 5px;
    background: #F3F4F9;
  }
  .deviceWork{
    width: 331px;
    min-height: 90px;
    background: #F6F9FD;
    border-radius: 3px;
    font-size: 15px;
    color: #666;
    padding: 12px;
    margin-bottom: 17px;
  }
  .deviceWork span{
    display: block;
  }
  .div{
    width: 355px;
  }
  .marks{
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    position: fixed;
    z-index: 9999;
  }
  .marks .modal{
    width: 310px;
    height: 378px;
    border-radius: 3px;
    background:#fff;
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
    margin: auto;
    z-index: 10000;
  }
  .marks .modal .del{
    width: 26px;
    height: 26px;
    position: absolute;
    top: 0;
    right: 0;
  }
  .marks .modal .cont{
    width: 310px;
    height: 352px;
    position: absolute;
    bottom: 0;
    right: 0;
  }
  .color1{
    color: #73CCF6 !important;
  }
  .color2{
    color: #92E464 !important;
  }
  .color3{
    color: #FCC569 !important;
  }
  .color4{
    color: #EF7C69 !important;
  }
</style>
