<template>
  <div class="content">
    <div class='main'>
      <div class="header">
        <span></span><span>步数</span>
        <span style="background: url('https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/wechat/data%402x.png') no-repeat left center; background-size:16px 13px;">数据来自于微信运动</span>
      </div>
      <div class="status">
        <div id="sport" ref="sport"></div>
        <span class="bg"></span>
        <div class="top">
          <div class="data">
            <div class="num"><span>{{measureInfo.total_step}}</span><span>步</span><span>周期总步数</span></div>
            <div class="num"><span>{{measureInfo.current_avg}}</span><span>步</span><span>平均每日步数</span></div>
          </div>
        </div>
        <span class="bg"></span>
        <div class="bottom" >
          <div class="left" style="background: url('https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/wechat/7%E5%A4%A9%E5%91%A8%E6%9C%9F.png') no-repeat center center; background-size:40px 40px;">
          </div>
          <div class="right">
          <div class="rightTop">同比上周期</div>
            <div class="sport3" v-if="measureInfo.before_pre_msg != '' && measureInfo.before_pre != 0">
              {{measureInfo.before_pre_msg}}
              <span v-if="measureInfo.before_pre != 0">{{Number(measureInfo.before_pre).toFixed(1)}}</span>
              <span>%</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import echarts from 'echarts'
  import { reportMotion } from '@/api/common/report'

  export default {
    data() {
      return {
        userId: '',
        startDate: '',
        endDate: '',
        measureInfo: { before_pre: 0 },
        xDate: [],
        datas: []
      }
    },
    created() {
      let query = this.$route.query
      this.userId = query.user_id
      this.startDate = query.start_date
      this.endDate = query.end_date

      this.$nextTick(() => {
        this.init()
      })
    },
    methods: {
      init() {
        let that = this
        let datas = {
          user_id: this.userId, // 用户ID
          start_date: this.startDate, // 开始时间
          end_date: this.endDate // 结束时间
        }
        reportMotion(datas).then(res => {
          if (res.code == 200) {
            that.measureInfo = res.data
            that.xDate = res.data.motion_time
            that.datas = res.data.motion_chat
          } else {
            that.$toast(res.msg)
          }
        }).then(() => {
          that.sportPic(that.$refs.sport, that.xDate, that.datas)
        })
      },
      sportPic(dom, xDate, datas) {
        let myEcharts = echarts.init(dom)
        myEcharts.setOption({
          backgroundColor: '#F6F9FD', // 背景色
          tooltip: {
              trigger: 'none'
          },
          grid: {
              left: '3%',
              right: '3%',
              bottom: '5%',
              top: '15%',
              containLabel: true
          },
          xAxis: {
              type: 'category',
              axisLine: {
                  lineStyle: {
                      color: '#666'
                  }
              },
              axisLabel: {
                  color: '#333'
              },
              boundaryGap: true,
              data: xDate
          },
          yAxis: {
            type: 'value',
            axisLine: {
              lineStyle: {
                  color: '#666'
              }
            },
            axisLabel: {
              color: '#333'
            },
            splitLine: {
              lineStyle: {
                  color: '#eee'
              }
            }
          },
          series: [{
            name: '步数',
            type: 'line',
            symbol: 'triangle',
            symbolSize: 9,
            itemStyle: {
              normal: {
                color: '#FF8E00',
                label: {
                    show: true,
                    color: '#333333',
                    fontSize: '13'
                },
                lineStyle: {
                    color: '#F67710',
                    width: 1
                }
              }
            },
            data: datas
          }]
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  .main{
    width: 355px;
    margin: 0 auto;
    background: #fff;
    padding: 0 10px;
  }
  .main .header{
    height: 40px;
    line-height: 40px;
  }
  .main .header span:nth-child(1){
    width: 4px;
    height: 17px;
    background:#398CFF;
    margin: 11px 4px 0 0;
    display: block;
    float: left;
  }
  .main .header span:nth-child(2){
    font-size: 18px;
    color: #262626;
    display: block;
    float: left;
  }
  .main .header span:nth-child(3){
    width: 145px;
    display: block;
    float: right;
    font-size: 14px;
    color: #999;
    text-align: right;
  }
  .status{
    width: 355px;
    /* height: 680px; */
    margin-bottom: 18px;
    background: #F6F9FD;
    border-radius: 3px;
  }
  .status #sport{
    width: 355px;
    height: 171px;
  }
  .status .top{
    width: 355px;
    height: 80px;
  }
  .status .top .data{
    width: 355px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .status .top .data .num{
    flex: 1;
  }
  .status .top .data .num span:nth-child(1){
    font-size: 28px;
    color: #666;
  }
  .status .top .data .num span:nth-child(2){
    font-size: 12px;
    color: #2A2A2A;
  }
  .status .top .data .num span:nth-child(3){
    display: block;
    font-size: 13px;
    color: #333;
  }
  .weightStatus{
    width: 355px;
    height: 40px;
    line-height: 40px;
    font-size: 16px;
    color: #666;
    text-align: center;
  }
  .weightStatus span{
    height: 40px;
    line-height: 40px;
    font-size: 28px;
    color: #FCC569;
    padding: 0 4px;
  }
  .status .bg{
    display: block;
    width: 355px;
    height: 2px;
    background: #fff;
  }
  .status .bottom{
    width: 355px;
    height: 85px;
    background: #F6F9FD;
  }
  .status .bottom .left{
    width: 70px;
    height: 85px;
    float: left;
  }
  .status .bottom .left .ago{
    font-size: 16px;
    color: #666;
    margin-bottom: 5px;
  }
  .status .bottom .left .before{
    width: 100px;
    height: 60px;
    color: #999;
  }
  .status .bottom .left .before .ul{
    width: 18px;
    height: 60px;
    color: #999;
    font-size: 12px;
    float: left;
    margin:0 10px 0 15px;
  }
  .status .bottom .left .before .ul .li{
    width: 18px;
    height: 60px;
    color: #999;
    font-size: 12px;
    border-radius: 3px;
  }
  .status .bottom .left .before .ul .li span{
    width: 18px;
    text-align: center;
    display: block;
    color: #fff;
    font-size: 12px;
    float: left;
  }
  .status .bottom .left .leftTitle span{
    font-size: 10px;
    line-height: 20px;
    color: #999;
  }
  .status .bottom .left .leftTitle span:nth-child(1){
    margin: 0 15px 0 5px;
  }
  .status .bottom .right{
    width: 190px;
    height: 85px;
    float: left;
  }
  .status .bottom .right .sport3{
    /* margin-top: 20px; */
    line-height: 28px;
    font-size: 16px;
    color: #666;
  }
  .status .bottom .right .sport3 span:nth-child(1){
    display: inline-block;
    font-size: 28px;
    color: #64A5FF;
  }
  .status .bottom .right .sport3 span:nth-child(2){
    display: inline-block;
    font-size: 12px;
    color: #64A5FF;
  }
  .status .bottom .right{
    width: 285px;
    height: 84px;
    float: left;
  }
  .rightTop{
    font-size: 18px;
    color: #4471AC;
    margin-top: 15px;
    text-align: left;
  }
</style>
