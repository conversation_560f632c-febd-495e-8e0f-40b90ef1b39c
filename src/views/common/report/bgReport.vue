<template>
  <div class="content">
    <div class="blood">
      <div class="bloodTitle"><span class="name">血糖</span><span class="mark"
              @click="help = !help">?</span><span class="weekDate">{{ timeSlot }}</span></div>
      <div class="tabPic">
        <div class="tab">
          <ul class="clearfix">
            <li v-for="(item, index) in tabList"
                :key=index
                :class='{active:item.value == tabIndex}'
                @click="changeTab(index)">{{item.name}}
            </li>
          </ul>
        </div>
        <div class="sugarPic"
             ref="sugarPic">
        </div>
      </div>
    </div>
    <div class="checkPic">
      <div class="left"
           ref="checkRef"></div>
      <div class="right">
        <p class="one">你近7日血糖监测<span :class="measureInfo.bg_distribution.conclusion == '不理想' ? 'colorRed' : 'colorNor'">{{measureInfo.bg_distribution.conclusion}}</span></p>
        <p class="two"><span class="two1"></span>正常{{measureInfo.bg_distribution.normal_count}}次<span class="two2"></span>不稳定{{measureInfo.bg_distribution.high_count + measureInfo.bg_distribution.low_count}}次</p>
        <p class="three"
           @click="unusual = !unusual">血糖不稳定常见原因</p>
      </div>
    </div>
    <div class="normalSugar">正常血糖范围:4.4≤空腹血糖&lt;7.0、4.4≤非空腹血糖&lt;10</div>
    <div class="blood">
      <div class="bloodTitle"><span class="name">血糖分布</span><span class="weekDate">{{ timeSlot }}</span>
      </div>
      <div class="sugarTable">
        <div class="tables">
          <div class="tr">
            <div class="th td_center width1">日期</div>
            <div class="th td_center width1">凌晨</div>
            <div class="td width2">
              <div class="tdd2">早餐</div>
              <div class="tdd1 td_center">
                <div class=" thsmall width3">空腹</div>
                <div class=" thsmall  width3 border-none">后</div>
              </div>
            </div>
            <div class="td width2">
              <div class="tdd2">午餐</div>
              <div class="tdd1 td_center">
                <div class=" thsmall width3">前</div>
                <div class=" thsmall  width3 border-none">后</div>
              </div>
            </div>
            <div class="td width2">
              <div class="tdd2">晚餐</div>
              <div class="tdd1 td_center">
                <div class=" thsmall width3">前</div>
                <div class=" thsmall  width3 border-none">后</div>
              </div>
            </div>
            <div class="th td_center width1">睡前</div>
          </div>
          <div v-for="(item, index) in familyGluData"
               :key="index"
               class="table">
            <div class="tr"
                 v-if="index<7">
              <span class="thsmall width1 date">{{item.date}}</span>
              <span class="thsmall width1 data"
                    :class="item['8'] | beforecolor">{{item['8']==0 ? '--' : item['8']}}</span>
              <span class="thsmall width4 data"
                    :class="item['1'] | beforecolor">{{item['1']==0 ? '--' : item['1']}}</span>
              <span class="thsmall width4 data"
                    :class="item['2'] | aftercolor">{{item['2']==0 ? '--' : item['2']}}</span>
              <span class="thsmall width4 data"
                    :class="item['3'] | beforecolor">{{item['3']==0 ? '--' : item['3']}}</span>
              <span class="thsmall width4 data"
                    :class="item['4'] | aftercolor">{{item['4']==0 ? '--' : item['4']}}</span>
              <span class="thsmall width4 data"
                    :class="item['5'] | beforecolor">{{item['5']==0 ? '--' : item['5']}}</span>
              <span class="thsmall width4 data"
                    :class="item['6'] | aftercolor">{{item['6']==0 ? '--' : item['6']}}</span>
              <span class="thsmall width1 data"
                    :class="item['7'] | beforecolor">{{item['7']==0 ? '--' : item['7']}}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="blood clearfix">
      <div class="bloodTitle"><span class="name">近7日血糖分布</span><span class="weekDate">{{ timeSlot }}</span></div>
      <div class="bloodList clearfix">
        <ul>
          <li v-for="(item, index) in measureInfo.bg_distribution.history_data"
              :key=index>
            <div class="listTime">{{item.group_time}}</div>
            <div class="listDetail"
                 v-for="(subItem, index) in item.data"
                 :key="index">
              <p class="time1"><span>{{subItem.time}}</span><span :class="[status(subItem.bg_type), 'active']"></span></p>
              <p class="time2"><span :class="sbpColor(subItem.bg_type)">{{subItem.bg}}</span>mmol/l</p>
              <p class="time3">{{subItem.dining_status}}</p>
            </div>
          </li>
        </ul>
      </div>
    </div>
    <div class="mask"
         v-if="unusual"></div>
    <div class="maskContent"
         v-if="unusual">
      <p class="title">血糖不稳定常见原因</p>
      <div>首先糖尿病防治意识淡薄是一个重要原因，其次没有规律血糖监测，饮食，运动等，最后控糖药物使用不规律并缺乏规范系统的管理。</div>
      <p class="tips">来源：中国成人体质指数分类的推荐意见简介</p>
      <p class="sure"
         @click="sure">确定</p>
    </div>
    <div class="maskHelp"
         v-if="help"></div>
    <div class="maskContentHelp"
         v-if="help">
      <p class="title">一天应该什么时机测量血糖？</p>
      <div>
        <span>在生活作息规律的前提下，我们有6类时间点可以检测血糖周期变化。</span>
        <span>1、清晨空腹情况下测量血糖</span>
        <span>2、三餐前半小时血糖</span>
        <span>3、三餐后2小时血糖</span>
        <span>4、晚上睡前血糖</span>
        <span>5、必要时加测凌晨1~3时的血糖</span>
        <span>6、其他特殊时机血糖</span>
      </div>
      <p class="sure"
         @click="sure">确定</p>
    </div>
  </div>
</template>

<script>
import echarts from 'echarts'
import { reportBg } from '@/api/common/report'

export default {
  data () {
    return {
      userId: '',
      startDate: '',
      endDate: '',
      title: true,
      createList: [[10, 20], [60, 90], [70, 100], [85, 135], [100, 130]],
      checkNumner: 20,
      tabIndex: 0,
      tabList: [{ name: '全部', value: 0 }, { name: '凌晨', value: 1 }, { name: '空腹', value: 2 },
      { name: '早餐后', value: 3 }, { name: '午餐前', value: 4 }, { name: '午餐后', value: 5 },
      { name: '晚餐前', value: 6 }, { name: '晚餐后', value: 7 }, { name: '睡前', value: 8 }],
      amilyGluData: [{}, {}, {}, {}, {}, {}, {}], // 家庭血糖
      date: ['10/1', '10/2', '10/3', '10/4', '10/5', '10/6', '10/7'],
      unusual: false,
      help: false,
      measureInfo: {
        bg_distribution: {},
        top_data: {
          all_data: {},
          one_data: {},
          two_data: {},
          three_data: {},
          four_data: {},
          five_data: {},
          six_data: {},
          seven_data: {},
          eight_data: {}
        }
      },
      anchor: [],
      familyGluData: [],
      timeSlot: ''
    }
  },
  filters: {
    beforecolor (val) {
      if (val > 0 && val < 4.4) {
        return 'colorDown'
      } else if (val >= 4.4 && val <= 7 || val == 0) {
        return ''
      } else {
        return 'colorHigh'
      }
    },
    aftercolor (val) {
      if (val > 0 && val < 4.4) {
        return 'colorDown'
      } else if (val >= 4.4 && val <= 10 || val == 0) {
        return ''
      } else {
        return 'colorHigh'
      }
    }
  },
  created () {
    let query = this.$route.query
    this.userId = query.user_id
    this.startDate = query.start_date
    this.endDate = query.end_date

    this.init()
  },
  methods: {
    init () {
      let that = this
      reportBg({
        user_id: this.userId, // 用户ID
        start_date: this.startDate, // 开始时间
        end_date: this.endDate // 结束时间
      }).then((res) => {
        if (res.code === 200) {
          that.measureInfo = res.data
          let familyGlusList = []
          for (var key in res.data.bg_distribution.seven_data) {
            familyGlusList.push(res.data.bg_distribution.seven_data[key])
          }
          that.familyGluData = familyGlusList
          that.anchor = [
            { name: res.data.start_date, value: [res.data.start_date, 0] },
            { name: res.data.end_date, value: [res.data.end_date, 0] }
          ]
          that.timeSlot = res.data.time_slot || ''
        } else {
          this.$toast(res.msg)
        }
      }).then(() => {
        that.sugarPic(that.$refs.sugarPic, that.measureInfo.top_data.all_data)
        that.checkPic(that.$refs.checkRef, that.measureInfo.bg_distribution.count, that.measureInfo.bg_distribution.normal_count, that.measureInfo.bg_distribution.high_count, that.measureInfo.bg_distribution.low_count)
      }).catch(err => {
        this.$toast(err)
      })
    },
    status (val) {
      if (val == '正常') {
        return 'bgNormal'
      } else if (val == '偏高') {
        return 'bgHigh'
      } else if (val == '偏低') {
        return 'bgDown'
      }
    },
    sbpColor (val) {
      if (val == '正常') {
        return 'colorNormal'
      } else if (val == '偏高') {
        return 'colorHigh'
      } else if (val == '偏低') {
        return 'colorDown'
      }
    },
    changeTab (index) {
      this.tabIndex = index
      if (index == 0) {
        this.sugarPic(this.$refs.sugarPic, this.measureInfo.top_data.all_data)
      } else if (index == 1) {
        this.sugarPic(this.$refs.sugarPic, this.measureInfo.top_data.one_data)
      } else if (index == 2) {
        this.sugarPic(this.$refs.sugarPic, this.measureInfo.top_data.two_data)
      } else if (index == 3) {
        this.sugarPic(this.$refs.sugarPic, this.measureInfo.top_data.three_data)
      } else if (index == 4) {
        this.sugarPic(this.$refs.sugarPic, this.measureInfo.top_data.four_data)
      } else if (index == 5) {
        this.sugarPic(this.$refs.sugarPic, this.measureInfo.top_data.five_data)
      } else if (index == 6) {
        this.sugarPic(this.$refs.sugarPic, this.measureInfo.top_data.six_data)
      } else if (index == 7) {
        this.sugarPic(this.$refs.sugarPic, this.measureInfo.top_data.seven_data)
      } else if (index == 8) {
        this.sugarPic(this.$refs.sugarPic, this.measureInfo.top_data.eight_data)
      }
    },
    sure () {
      this.unusual = false
      this.help = false
    },
    sugarPic (dom, datas) {
      let myEcharts = echarts.init(dom)
      myEcharts.setOption({
        backgroundColor: '#F6F9FD', // 背景色
        title: {
          text: 'mmol/L',
          left: '10px',
          top: '-6px',
          textStyle: {
            color: '#666',
            fontSize: '14'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '5%',
          top: '12%',
          containLabel: true
        },
        xAxis: {
          type: 'time',
          splitNumber: 10,
          splitLine: { show: false },
          axisLabel: {
            textStyle: {
              fontSize: 8
            }
          }
        },
        yAxis: {
          splitLine: { show: false }
        },
        series: [
          {
            data: datas,
            type: 'line',
            clickable: false,
            smooth: this.tabIndex !== 0,
            symbol: this.tabIndex === 0 ? 'none' : 'circle',
            itemStyle: {
              normal: {
                lineStyle: {
                  color: '#61A7FF',
                  width: 1
                },
                label: {
                  show: true,
                  formatter: function (data) {
                    return data.value[1]
                  },
                  fontSize: 14,
                  color: '#666666',
                  fontStyle: 'normal',
                  fontWeight: 'normal'
                }
              }
            }
          },
          {
            name: 'anchor',
            type: 'line',
            showSymbol: false,
            data: this.anchor,
            itemStyle: { normal: { opacity: 0 } }, // 不绘制该线条
            lineStyle: { normal: { opacity: 0 } }
          }
        ]
      })
    },
    checkPic (dom, count, normal, high, low) {
      let myEcharts = echarts.init(dom)
      myEcharts.setOption({
        color: ['#07C160', '#FF9600'],
        title: {
          show: true,
          text: '检查次数',
          left: 'center',
          top: '35%',
          textStyle: {
            color: '#010203',
            fontSize: 12,
            align: 'center'
          },
          subtext: count + '次',
          // 副标题文本样式
          subtextStyle: {
            fontSize: 14,
            color: '#262626'
          }
        },
        series: [
          {
            // name: '血压检测',
            type: 'pie',
            silent: true,
            radius: ['70%', '45%'],
            avoidLabelOverlap: false,
            label: {
              normal: {
                show: false,
                position: 'center'
              }
            },
            data: [
              { value: normal, name: '正常次数' },
              { value: high + low, name: '不稳定次数' }
            ],
            itemStyle: {
              borderWidth: 3,
              borderColor: '#fff',
              emphasis: {
                shadow: 10,
                shadowOffsetX: 0,
                shadowColor: '#rgba(0,0,0,0.5)'
              }
            }
          }
        ]
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import "@/assets/scss/sugarReport.scss";
</style>
