<template>
  <div class="content">
    <div class="blood">
      <div class="bloodTitle"><span>血压</span><span>{{ info.time_slot }}</span></div>
      <div class="bloodPic">
        <blood-pic :data="info.chart"></blood-pic>
      </div>
    </div>
    <div class="checkPic">
      <div class="left"
           ref="checkRef"></div>
      <div class="right">
        <p class="one">你近7日血压监测<span :class="info.avg_msg == '不稳定' ? 'color' : 'colorNor'">{{info.avg_msg}}</span></p>
        <p class="one">平均血压{{info.avg_sbp}}/{{info.avg_dbp}}</p>
        <p class="two"><span class="two1"></span>正常{{info.normal}}次<span class="two2"></span>不稳定{{info.unusual}}次</p>
        <p class="three"
           @click="unusualReason = !unusualReason">血压不稳定常见原因</p>
      </div>
    </div>
    <div class="blood">
      <div class="bloodTitle"><span>近7日血压分布</span><span>{{ info.timeSlot }}</span></div>
      <div class="tabOne"
           v-show="tabIndex == 1">
        <div class="tabLeft">
          <span class="morning">早晨</span>
          <span>收缩压</span>
          <span>舒张压</span>
          <span>心率</span>
          <span class="dinner">晚间</span>
          <span>收缩压</span>
          <span>舒张压</span>
          <span>心率</span>
        </div>
        <div class="tabRight">
          <p class="dateTime">
            <span v-for="(item, index) in info.chart_time"
                  :key=index>{{item}}</span>
          </p>
          <p class="gaoya">
            <span v-for="(item, index) in info.table_list.morning_sbp"
                  :class="sbpColor(item)"
                  :key=index>{{item == 0 ? '--' : item}}</span>
          </p>
          <p class="diya">
            <span v-for="(item, index) in info.table_list.morning_dbp"
                  :class="dbpColor(item)"
                  :key=index>{{item == 0 ? '--' : item}}</span>
          </p>
          <p class="gaoya">
            <span v-for="(item, index) in info.table_list.morning_pulse"
                  :key=index>{{item == 0 ? '--' : item}}</span>
          </p>
          <p class="blank"></p>
          <p class="sbp">
            <span v-for="(item, index) in info.table_list.night_sbp"
                  :class="sbpColor(item)"
                  :key=index>{{item == 0 ? '--' : item}}</span>
          </p>
          <p class="dbp">
            <span v-for="(item, index) in info.table_list.night_dbp"
                  :class="dbpColor(item)"
                  :key=index>{{item == 0 ? '--' : item}}</span>
          </p>
          <p class="sbp">
            <span v-for="(item, index) in info.table_list.night_pulse"
                  :key=index>{{item == 0 ? '--' : item}}</span>
          </p>

        </div>
      </div>
      <div class="tabTwo"
           v-show="tabIndex == 2">
        <div class="bloodChart"
             ref="bloodRef"></div>
      </div>
      <div class="tabChange"
           ref="tabChange">
        <span @click="tab(1)"></span>
        <span @click="tab(2)"></span>
      </div>
    </div>
    <div class="blood clearfix">
      <div class="bloodTitle"><span>血压测量明细</span><span>{{ info.timeSlot }}</span></div>
      <div class="bloodList clearfix">
        <div class="listTitle">
          <p class="time1">时间</p>
          <p class="time2"><span>收缩压/舒张压</span><span class="unit">（mmHg）</span></p>
          <p class="time3"><span>心率</span><span class="unit">（次/分）</span></p>
        </div>
        <ul>
          <li v-for="(item, index) in info.bp_distribution"
              :key=index>
            <div class="listTime">{{item.group_time}}</div>
            <div class="listDetail"
                 v-for="(val, index) in item.data"
                 :key=index>
              <p class="time1"><span>{{val.sec}}</span><span :class="[status(val.normal), 'active']"></span></p>
              <p class="time2"><span :class="sbpColor(val.sbp)">{{val.sbp}}</span>/<span :class="dbpColor(val.dbp)">{{val.dbp}}</span></p>
              <p class="time3">{{val.pulse}}</p>
            </div>
          </li>
        </ul>
      </div>
    </div>
    <div class="mask"
         v-if="unusualReason"></div>
    <div class="maskContent"
         v-if="unusualReason">
      <p class="title">血压不稳定常见原因</p>
      <div>
        <span>1、高钠低钾膳食</span>
        <span>2、超重和肥胖</span>
        <span>3、饮酒</span>
        <span>4、精神紧张</span>
        <span>5、其它危险因素：年龄、高血压家族史、缺乏体力活动等</span>
        <span>6、女性低血压的患病率比男性高</span>
        <span>7、BMI低</span>
        <span>8、自觉健康状况不理想</span>
      </div>
      <p class="sure"
         @click="sure">确定</p>
    </div>
  </div>
</template>

<script>
import echarts from 'echarts'
import bloodPic from '@/components/bloodPic.vue'
import { reportBp } from '@/api/common/report'

export default {
  data () {
    return {
      userId: '',
      startDate: '',
      endDate: '',
      tabIndex: 1,
      unusualReason: false,
      reportId: '',
      info: {
        chart: [],
        avg_msg: '',
        avg_sbp: '',
        avg_dbp: '',
        number: '',
        normal: '',
        unusual: '',
        chart_time: [],
        table_list: {
          morning_sbp: [],
          morning_dbp: [],
          morning_pulse: [],
          night_sbp: [],
          night_dbp: [],
          night_pulse: []
        },
        bp_distribution: [],
        time_slot: ''
      }
    }
  },
  components: { bloodPic },
  created () {
    let query = this.$route.query
    this.userId = query.user_id
    this.startDate = query.start_date
    this.endDate = query.end_date

    this.init()
  },
  methods: {
    init () {
      let that = this
      reportBp({
        user_id: this.userId, // 用户ID
        start_date: this.startDate, // 开始时间
        end_date: this.endDate // 结束时间
      }).then(res => {
        if (res.code == 200) {
          that.info = res.data
        } else {
          this.$toast(res.msg)
        }
      }).then(() => {
        that.checkPic(that.$refs.checkRef, that.info.number, that.info.normal, that.info.unusual)
      }).then(() => {
        that.bloodPic(that.$refs.bloodRef, that.info.chart_time, that.info.table_list.morning_sbp, that.info.table_list.morning_dbp, that.info.table_list.night_sbp, that.info.table_list.night_dbp)
      }).catch(err => {
        this.$toast(err)
      })
    },
    status (num) {
      if (num == 1) {
        return 'bgNormal'
      } else if (num == 2) {
        return 'bgHigh'
      } else if (num == 3) {
        return 'bgDown'
      }
    },
    sbpColor (num) {
      if (num > 0 && num < 90) {
        return 'colorDown'
      } else if (num >= 90 && num <= 135 || num == 0) {
        return ''
      } else {
        return 'colorHigh'
      }
    },
    dbpColor (num) {
      if (num > 0 && num < 60) {
        return 'colorDown'
      } else if (num >= 60 && num <= 85 || num == 0) {
        return ''
      } else {
        return 'colorHigh'
      }
    },
    sure () {
      this.unusualReason = false
    },
    tab (index) {
      this.tabIndex = index
      if (index == 1) {
        this.$refs.tabChange.style.backgroundImage = "url('https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/left.png')"
      } else if (index == 2) {
        this.$refs.tabChange.style.backgroundImage = "url('https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/right.png')"
      }
    },
    // 血压饼状图
    checkPic (dom, numner, normal, unusual) {
      let myEcharts = echarts.init(dom)
      myEcharts.setOption({
        color: ['#07C160', '#FF9600'],
        title: {
          show: true,
          text: '检查次数',
          left: 'center',
          top: '35%',
          textStyle: {
            color: '#010203',
            fontSize: 12,
            align: 'center'
          },
          subtext: numner + '次',
          // 副标题文本样式
          subtextStyle: {
            fontSize: 14,
            color: '#262626'
          }
        },
        series: [
          {
            // name: '血压检测',
            type: 'pie',
            silent: true,
            radius: ['70%', '45%'],
            avoidLabelOverlap: false,
            label: {
              normal: {
                show: false,
                position: 'center'
              }
            },
            data: [
              { value: normal, name: '正常次数' },
              { value: unusual, name: '不稳定次数' }
            ],
            itemStyle: {
              borderWidth: 3,
              borderColor: '#fff',
              emphasis: {
                shadow: 10,
                shadowOffsetX: 0,
                shadowColor: '#rgba(0,0,0,0.5)'
              }
            }
          }
        ]
      })
    },
    // 血压明细图
    bloodPic (dom, datas, m_sbp, m_dbp, s_sbp, s_dbp) {
      let myEcharts = echarts.init(dom)
      myEcharts.setOption({
        title: {
          text: 'mmHg',
          left: '8px',
          textStyle: {
            color: '#666666',
            fontSize: '24px'
          }
        },
        tooltip: {
          trigger: 'none'
        },
        legend: {
          y: 'bottom',
          data: ['早晨高压', '早晨低压', '晚上高压', '晚上低压'],
          textStyle: {
            color: '#666666',
            fontSize: '20px'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '18%',
          top: '12%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          axisLine: {
            lineStyle: {
              color: '#ccc'
            }
          },
          axisLabel: {
            color: '#666'
          },
          boundaryGap: true,
          data: datas
        },
        yAxis: {
          type: 'value',
          axisLine: {
            lineStyle: {
              color: '#ccc'
            }
          },
          axisLabel: {
            color: '#666'
          },
          min: 0,
          max: 200,
          splitLine: {
            lineStyle: {
              color: '#eee'
            }
          }
        },
        series: [
          {
            name: '早晨高压',
            type: 'line',
            symbol: 'circle',
            symbolSize: 9,
            itemStyle: {
              normal: {
                color: '#FF8E00',
                label: {
                  show: true,
                  color: '#333333',
                  fontSize: '14'
                },
                lineStyle: {
                  color: '#F67710',
                  width: 1
                }
              }
            },
            data: m_sbp
          },
          {
            name: '早晨低压',
            type: 'line',
            symbol: 'triangle',
            symbolSize: 9,
            itemStyle: {
              normal: {
                color: '#FF8E00',
                label: {
                  show: true,
                  color: '#333333',
                  fontSize: '14'
                },
                lineStyle: {
                  color: '#F67710',
                  width: 1
                }
              }
            },
            data: m_dbp
          },
          {
            name: '晚上高压',
            type: 'line',
            symbol: 'circle',
            symbolSize: 9,
            itemStyle: {
              normal: {
                color: '#61B1F0',
                label: {
                  show: true,
                  color: '#333333',
                  position: 'bottom',
                  fontSize: '14'
                },
                lineStyle: {
                  color: '#10A2F6',
                  width: 1
                }
              }
            },
            data: s_sbp
          },
          {
            name: '晚上低压',
            type: 'line',
            symbol: 'triangle',
            symbolSize: 9,
            itemStyle: {
              normal: {
                color: '#61B1F0',
                label: {
                  show: true,
                  color: '#333333',
                  position: 'bottom',
                  fontSize: '15'
                },
                lineStyle: {
                  color: '#10A2F6',
                  width: 1
                }
              }
            },
            data: s_dbp
          }
        ]
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import "@/assets/scss/bloodReport.scss";
</style>
