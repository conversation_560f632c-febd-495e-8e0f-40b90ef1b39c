<template>
  <!-- 智慧呼吸的 报告 -->
  <div class="breath_page">
    <!-- 第一页 -->
    <div class="page1">
        <div class="header">
            <div class="flex_div header_up bb">
                <div class="header_hosp">
                    <img :src="header_data.logo" alt="img" class="logo_img">
                    <span>{{header_data.hosp_name}}</span>
                </div>
                <div class="header-code">
                    <svg id="svgcode" class="svgcode"></svg>
                    <div>{{header_data.bar_code}}</div>
                </div>
            </div>
            <div class="flex_div header_down bb">
                <div class="header_info">姓名：{{header_data.name}}</div>
                <div class="header_info">{{header_data.sex == 1?'男':'女'}} | {{header_data.age}}岁</div>
                <div class="header_info">手机号：{{header_data.phone}}</div>
                <div class="header_info">
                    身高：{{header_data.height}}cm | 体重：{{header_data.weight}}kg | BMI：{{header_data.bmi}}kg/㎡
                </div>
            </div>
            <div class="flex_div bb">
                <div class="header_info fl">病程：{{header_data.course_disease}}</div>
                <div class="header_info fl">加入CWMC时间：{{header_data.join_cwmc_date ? header_data.join_cwmc_date.substring(0,10):''}}</div>
                <div class="header_info fl">就诊时间：{{header_data.visit_at ? header_data.visit_at:''}}</div>
                <div class="header_info fl">打印时间：{{header_data.print_date?header_data.print_date.substring(0,16):''}}</div>
            </div>
            <div class="flex_div">
                <span class="med_history_title">简要病史：</span>
                <van-checkbox 
                    v-for="(item,index) in header_data.medical_history" 
                    :key="index" 
                    :name="item.name"
                    shape="square"
                    v-model="item.checked"
                    icon-size="14px"
                >
                {{item.name}}
                </van-checkbox>
            </div>
        </div>
        <div class="content_tip">
            系统生成检查报告为节选，如有需求请向医师展示详细报告。数据来源于肺功能仪等检查设备自动上传和医院HIS导入及人工录入值，可能存在偏差，仅供参考。
        </div>
        <div class="table_container">
            <div class="table_title">院内基础肺功能检查</div>
            <div class="lung_div">
                <div class="table_div">
                    <table>
                        <tr class="bg_grey">
                            <th class="width180 text_left">项目</th>
                            <th>最佳值</th>
                            <th>预计值</th>
                            <th>百分比</th>
                            <th>单位</th>
                        </tr>
                        <tr v-for="(lungFun,index) in base_lung_function.project_target" :key="index" :style="{background: index%2==1?'rgba(0, 0, 0, 0.06)':''}">
                            <td class="text_left">{{lungFun.name}}</td>
                            <td>{{lungFun.perfect_value}}</td>
                            <td>{{lungFun.pre_value}}</td>
                            <td>{{lungFun.percent_value}}</td>
                            <td>{{lungFun.unit}}</td>
                        </tr>
                    </table>
                </div>
                <div class="table_right">
                    <div class="bg_grey table_right_title">质控评级及结论</div>
                    <div class="table_right_level">质控评级：{{base_lung_function.qc_level}}</div>
                    <div>结论：{{base_lung_function.conclusion}}</div>
                </div>
            </div>
            <!-- 家庭随访 -->
            <div class="table_title">家庭随访/治疗近期情况</div>
            <div class="family_visit">
                <div class="visit_item">
                    <div>氧疗时长</div>
                    <div class="visit_data">
                    <span class="font18">{{family_follow_up.oxygen_duration.data}}</span>
                    <!-- <span class="font14">h</span> -->
                    <span class="visit_date">{{family_follow_up.oxygen_duration.date_time}}</span>
                    </div>
                </div>
                <div class="visit_item">
                    <div>雾化治疗</div>
                    <div class="visit_data">
                    <span class="font18">{{family_follow_up.atomization_therapy.data}}</span>
                    <!-- <span class="font14">min</span> -->
                    <span class="visit_date">{{family_follow_up.atomization_therapy.date_time}}</span>
                    </div>
                </div>
                <div class="visit_item">
                    <div>血氧浓度</div>
                    <div class="visit_data">
                    <span class="font18">{{family_follow_up.blood_oxygen.data}}</span>
                    <!-- <span class="font14">%</span> -->
                    <span class="visit_date">{{family_follow_up.blood_oxygen.date_time}}</span>
                    </div>
                </div>
                <div class="visit_item">
                    <div>随访问卷</div>
                    <div class="visit_data">
                    <span class="font18">{{family_follow_up.follow_up_question.data}}</span>
                    <span class="visit_date">{{family_follow_up.follow_up_question.date_time}}</span>
                    </div>
                </div>
            </div>
            <!-- 呼出气 -->
            <div class="table_title">呼出气一氧化氮测定</div>
            <div class="table_huqi">
                <table>
                    <tr class="bg_grey">
                        <th class="text_left">项目</th>
                        <th>检查结果</th>
                        <th>单位</th>
                        <th>参考范围及临床意义</th>
                    </tr>
                    <tr v-for="(item,index) in no_determination" :key="index" :style="{background: index%2==1?'rgba(0, 0, 0, 0.06)':''}">
                        <td class="text_left">{{item.name}}</td>
                        <td>{{item.inspect_res}}</td>
                        <td>{{item.unit}}</td>
                        <td>{{item.reference_range}}</td>
                    </tr>
                </table>
            </div>
            <div class="table_title">
                <span class="title1">BD舒张试验</span>
                <span>气道过敏反应试验</span>
            </div>
            <div class="two_table_div">
                <div class="table1">
                    <table>
                        <tr class="bg_grey">
                            <th class="width180 text_left">项目</th>
                            <th>用药前</th>
                            <th>用药后</th>
                            <th>改善率</th>
                            <th>单位</th>
                        </tr>
                        <tr v-for="(item,index) in bd_diastole" :key="index" :style="{background: index%2==1?'rgba(0, 0, 0, 0.06)':''}">
                            <td class="text_left">{{item.name}}</td>
                            <td>{{item.pre_med}}</td>
                            <td>{{item.after_med}}</td>
                            <td>{{item.change_rate}}</td>
                            <td>{{item.unit}}</td>
                        </tr>
                    </table>
                </div>
                <div class="table2">
                    <table>
                        <tr class="bg_grey">
                            <th class="width200 text_left">项目</th>
                            <th>注释</th>
                            <th>值</th>
                            <th>单位</th>
                        </tr>
                        <tr>
                            <td class="text_left">初始阻力</td>
                            <td>Rrs cont</td>
                            <td>{{airway_allergy.rrs_cont}}</td>
                            <td>cmH20/L/sec</td>
                        </tr>
                        <tr class="bg_grey">
                            <td class="text_left">初始气道传导率</td>
                            <td>Grs</td>
                            <td>{{airway_allergy.grs}}</td>
                            <td>L/sec/cmH20</td>
                        </tr>
                        <tr>
                            <td class="text_left">反应阈值</td>
                            <td>Dmin</td>
                            <td>{{airway_allergy.dmin}}</td>
                            <td>Unit</td>
                        </tr>
                        <tr class="bg_grey">
                            <td class="text_left">阻力上升开始时最小浓度</td>
                            <td>Cmin</td>
                            <td>{{airway_allergy.cmin}}</td>
                            <td>u/g/ml</td>
                        </tr>
                        <tr>
                            <td class="text_left">反应性</td>
                            <td>SGrs</td>
                            <td>{{airway_allergy.sgrs}}</td>
                            <td>L/sec/cmH20/min</td>
                        </tr>
                        <tr class="bg_grey">
                            <td class="text_left">反应性</td>
                            <td>SGrs/Grs cont</td>
                            <td>{{airway_allergy.sgrs_grs_cont}}</td>
                            <td>min</td>
                        </tr>
                        <tr>
                            <td class="text_left">参照率</td>
                            <td>PD</td>
                            <td></td>
                            <td>Unit</td>
                        </tr>
                    </table>
                </div>
            </div>
            <div class="fr page_num">1/2</div>
        </div>
    </div>

    <!-- 第二页 -->
    <div class="page2">
        <div class="flex_div header_down bb">
            <div class="header_info">姓名：{{header_data.name}}</div>
            <div class="header_info">{{header_data.sex == 1?'男':'女'}} | {{header_data.age}}岁</div>
            <div class="header_info">手机号：{{header_data.phone}}</div>
            <div class="header_info">
                身高：{{header_data.height}}cm | 体重：{{header_data.weight}}kg | BMI：{{header_data.bmi}}kg/㎡
            </div>
        </div>
        <div class="flex_div bb">
            <div class="header_info fl">病程：{{header_data.course_disease}}</div>
            <div class="header_info fl">加入CWMC时间：{{header_data.join_cwmc_date ? header_data.join_cwmc_date.substring(0,10):''}}</div>
            <div class="header_info fl">就诊时间：{{header_data.visit_at ? header_data.visit_at:''}}</div>
            <div class="header_info fl">打印时间：{{header_data.print_date?header_data.print_date.substring(0,16):''}}</div>
        </div>
        <div class="table_container">
            <div class="table_title">换气功能检查</div>
            <div class="two_table_div">
                <div class="table1">
                    <table>
                        <tr class="bg_grey">
                            <th class="width160 text_left">项目</th>
                            <th>最佳值</th>
                            <th>预计值</th>
                            <th>百分比</th>
                            <th>单位</th>
                        </tr>
                        <tr v-for="(item,index) in ventilation_function_left" :key="index" :style="{background: index%2==1?'rgba(0, 0, 0, 0.06)':''}">
                            <td class="text_left">{{item.name}}</td>
                            <td>{{item.best}}</td>
                            <td>{{item.pre}}</td>
                            <td>{{item.percent}}</td>
                            <td>{{item.unit}}</td>
                        </tr>
                    </table>
                </div>
                <div class="table2">
                    <table>
                        <tr class="bg_grey">
                            <th class="width160 text_left">项目</th>
                            <th>最佳值</th>
                            <th>预计值</th>
                            <th>百分比</th>
                            <th>单位</th>
                        </tr>
                        <tr v-for="(item,index) in ventilation_function_right" :key="index" :style="{background: index%2==1?'rgba(0, 0, 0, 0.06)':''}">
                            <td class="text_left">{{item.name}}</td>
                            <td>{{item.best}}</td>
                            <td>{{item.pre}}</td>
                            <td>{{item.percent}}</td>
                            <td>{{item.unit}}</td>
                        </tr>
                        <tr class="bg_grey">
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                        </tr>
                    </table>
                </div>
            </div>

            <div class="table_title">气道阻力试验</div>
            <div class="two_table_div">
                <div class="table1">
                    <table>
                        <tr class="bg_grey">
                            <th class="width160 text_left">项目</th>
                            <th>最佳值</th>
                            <th>预计值</th>
                            <th>百分比</th>
                            <th>单位</th>
                        </tr>
                        <tr v-for="(item,index) in airway_resistance_left" :key="index" :style="{background: index%2==1?'rgba(0, 0, 0, 0.06)':''}">
                            <td class="text_left">{{item.name}}</td>
                            <td>{{item.best}}</td>
                            <td>{{item.pre}}</td>
                            <td>{{item.percent}}</td>
                            <td>{{item.unit}}</td>
                        </tr>
                    </table>
                </div>
                <div class="table2">
                    <table>
                        <tr class="bg_grey">
                            <th class="width160 text_left">项目</th>
                            <th>最佳值</th>
                            <th>预计值</th>
                            <th>百分比</th>
                            <th>单位</th>
                        </tr>
                        <tr v-for="(item,index) in airway_resistance_right" :key="index" :style="{background: index%2==1?'rgba(0, 0, 0, 0.06)':''}">
                            <td class="text_left">{{item.name}}</td>
                            <td>{{item.best}}</td>
                            <td>{{item.pre}}</td>
                            <td>{{item.percent}}</td>
                            <td>{{item.unit}}</td>
                        </tr>
                    </table>
                </div>
            </div>

            <div class="table_title">血液、血气检查</div>
            <div class="two_table_div">
                <div class="table1">
                    <table>
                        <tr class="bg_grey">
                            <th class="width160 text_left">项目</th>
                            <th>检查结果</th>
                            <th>单位</th>
                        </tr>
                        <tr>
                            <td class="text_left">嗜酸性粒细胞</td>
                            <td>{{blood_gas.cell}}</td>
                            <td>uL</td>
                        </tr>
                        <tr>
                            <td class="text_left">血PH</td>
                            <td>{{blood_gas.blood_ph}}</td>
                            <td>mmHg</td>
                        </tr>
                    </table>
                </div>
                <div class="table2">
                    <table>
                        <tr class="bg_grey">
                            <th class="width160 text_left">项目</th>
                            <th>检查结果</th>
                            <th>单位</th>
                        </tr>
                        <tr >
                            <td class="text_left">PO₂</td>
                            <td>{{blood_gas.po2}}</td>
                            <td>mmHg</td>
                        </tr>
                        <tr >
                            <td class="text_left">PCO₂</td>
                            <td>{{blood_gas.pco2}}</td>
                            <td>%</td>
                        </tr>
                    </table>
                </div>
            </div>

            <div class="table_title">影像检查</div>
            <div class="flex_table">
                <div class="flex_item br">
                    <div class="item_title bg_grey">胸部CT</div>
                    <div class="item_intro">描述：{{imaging.chest_ct}}</div>
                    <div class="item_intro">印象：{{imaging.chest_ct_impression}}</div>
                </div>
                <div class="flex_item">
                    <div class="item_title bg_grey">心电图</div>
                    <div class="item_intro">描述：{{imaging.elec_dio}}</div>
                </div>
                <div class="flex_item br">
                    <div class="item_title bg_grey">心脏超声</div>
                    <div class="item_intro">描述：{{imaging.ucg}}</div>
                </div>
                <div class="flex_item">
                    <div class="item_title bg_grey">结论</div>
                    <div class="item_intro">描述：{{imaging.conclusion}}</div>
                </div>

            </div>

            <div class="page_footer">
                <img :src="footer_logo" alt="img" class="footer_img">
                <div class="tip_container">
                    <div>扫描二维码，关注智众公众号</div>
                    <div>1. 查看报告解读</div>
                    <div>2. 系统学习权威患教知识</div>
                </div>

            </div>
            <div class="fr page_num">2/2</div>
        </div>

    </div>

    <div class="page-break-inside"></div>
  </div>
</template>
<script>
  import JsBarcode from "jsbarcode";
//   import { getBreathReportData } from "@/api/common/saasBreathReport";
import { reportProduce } from "@/api/saasReport";
  export default {
    data() {
      return {
        footer_logo: require('./wechat_zz_logo.jpeg'),
        // 一分为二
        ventilation_function_right:[],
        ventilation_function_left:[],
        airway_resistance_left:[],
        airway_resistance_right:[],

        "header_data": {
            "join_cwmc_date": "",
            "visit_date": "",
            "sex": 1,
            "weight": "",
            "medical_history": [],
            "hosp_name": "",
            "course_disease": "",
            "phone": "",
            "name": "",
            "bar_code": "",
            "logo": "",
            "age": '',
            "height": "",
            "bmi": "",
            "print_date": ""
        },
        "base_lung_function": {
            "conclusion": "",
            "project_target": [],
            "qc_level": ""
        },
        "family_follow_up": {
            "oxygen_duration": {
                "data": "暂无",
                "date_time": ""
            },
            "atomization_therapy": {
                "data": "暂无",
                "date_time": ""
            },
            "follow_up_question": {
                "data": "暂无",
                "date_time": ""
            },
            "blood_oxygen": {
                "data": "暂无",
                "date_time": ""
            }
        },
        "no_determination": [],
        "bd_diastole": [],
        "airway_allergy": {
            "sgrs": "",
            "dmin": "",
            "cmin": "",
            "sgrs_grs_cont": "",
            "grs": "",
            "rrs_cont": ""
        },
        "airway_resistance": [],
        "ventilation_function": [],
        "blood_gas": {
            "po2": "",
            "pco2": "",
            "blood_ph": "",
            "cell": ""
        },
        "imaging": {
            "elec_dio": "",
            "conclusion": "",
            "ucg": "",
            "chest_ct": "",
            "chest_ct_impression": ""
        }
      }
    },
    created() {
        this.getReportData()
    },
    beforeMount() {
        this.setRem()
        window.onresize = () => {
            this.setRem()
        }
      
    },
    mounted(){
    },
    methods: {
        // 获取报告的数据
        getReportData(){
            let sign = this.$route.query.sign
            let template_id = this.$route.query.template_id
            let param = {sign, template_id}
            reportProduce(param).then((res)=>{
                if(res.code == 0){
                    if(res.data){
                        let data = res.data;
                        this.header_data = data.header_data;
                        this.base_lung_function = data.base_lung_function;
                        this.family_follow_up = data.family_follow_up;
                        this.no_determination = data.no_determination;
                        this.bd_diastole = data.bd_diastole;
                        this.airway_allergy = data.airway_allergy;

                        this.ventilation_function = data.ventilation_function;
                        this.airway_resistance = data.airway_resistance;
                        this.ventilation_function_left = data.ventilation_function.slice(0,4);
                        this.ventilation_function_right = data.ventilation_function.slice(4);
                        this.airway_resistance_left = data.airway_resistance.slice(0,2);
                        this.airway_resistance_right = data.airway_resistance.slice(2);

                        this.blood_gas = data.blood_gas;
                        this.imaging = data.imaging;
                        this.$nextTick(()=>{
                            if(data.header_data.bar_code){
                                this.setBarCode(data.header_data.bar_code)
                            }
                        })
                    }
                }else{
                    this.$toast(res.msg);
                }
            })
        },
        setRem(){
            document.documentElement.style.fontSize = '32px';
            // let scale = document.documentElement.clientWidth / 375 
            // 设置页面根节点字体大小
            // document.documentElement.style.fontSize = (16 * scale) + 'px'
        },
        // 生成BarCode
        setBarCode(barcode) {
            // console.log(barcode);
            // barcode = 'M100000573635'
            JsBarcode("#svgcode", barcode, {
                format: "CODE128", //选择要使用的条形码类型
                // lineColor: "#000",
                // background: "#EBEEF5",
                width: 3,
                height: 90,
                fontSize: 40, //设置文本的大小
                displayValue: false, //不展示，客户端展示数字会有编码问题，直接手写div展示
            });
        },
    }
  }
</script>

<style lang="scss">
.breath_page {
    width: 100%;
    min-width: 1070px;
    // width: 1070px;
    background-color: white;
    margin: 0 auto;
    font-size: 16px;
    .page1{
        height: 1555px;
    }
    .flex_div{
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-direction: row;
        flex-wrap: wrap;
        padding: 0 15px;
        height: 42px;
    }
    .bb{
        border-bottom: 1px solid rgba(161, 164, 171, 1);
    }
    .header {
        border-bottom: 1px solid #a1a4ab;
        // font-size: 1rem;
    }

    .header_up {
        width: 100%;
        height: 115px;
        box-sizing: border-box;
        padding: 20px 5px 20px 13px;

        .header_hosp {
            font-size: 28px;
            font-weight: bold;
        }
        .logo_img{
            width: 74px;
            height: 74px;
            border-radius: 50%;
            margin-right: 10px;
            vertical-align: middle;
        }
        .svgcode {
          width: 150px;
          height: 60px;
        }
        
    }
    .header_down{
        width: 100%;
        line-height: 40px;
        box-sizing: border-box;
    }
    .content_tip{
        font-size: 14px;
        background: rgba(0, 0, 0, 0.06);
        height: 45px;
        line-height: 45px;
        text-align: center;
        color: #666666;
    }
    .header_info {
        font-size: 16px;
        line-height: 40px;
    }
    .van-checkbox__label{
        font-size:14px;
    }
    .med_history_title{
        font-size: 16px;
    }
    .table_container{
        width: 100%;
        height: 1257px;
        box-sizing: border-box;
        padding: 0 15px;
        .table_title{
            font-size: 20px;
            margin: 29px 0 15px;
            text-align:left;
            .title1{
                display: inline-block;
                width: 50%;
            }
        }
        // 院内基础肺功能检查
        .lung_div{
            width: 100%;
            height: auto;
            display: flex;
            border: 1px solid #999;
            .table_div{
                width: 50%;
                table{
                    width: 100%;
                    border-right: 1px solid #999999;
                }
                tr{
                    th{
                        height: 44px;
                        line-height: 44px;
                        font-size: 18px;
                    }
                    td{
                        height: 40px;
                        line-height: 40px;
                        font-size: 16px;
                    }
                }
                .width180{
                    width: 180px;
                    box-sizing: border-box;
                }
                .text_left{
                    text-align: left;
                    padding-left: 15px;
                }
            }
            
        }
        .table_right{
            width: 50%;
            text-align: left;
            div{
                padding: 0 14px;
            }
            .table_right_title{
                height: 44px;
                line-height: 44px;
            }
            .table_right_level{
                margin: 12px 0 9px;
            }
        

        }
        .font18{
            font-size: 18px;
        }
        // 家庭随访
        .family_visit{
            display: flex;
            border: 1px solid #999;
            .visit_item{
                width: 25%;
                height: 90px;
                box-sizing: border-box;
                padding: 15px 20px;
                border-right: 1px solid #999;
                text-align: left;
            }
            .visit_item:nth-child(4){
                border-right: none;
            }
            .visit_data{
                margin-top: 15px;
                box-sizing: border-box;
                .font18{
                    font-size: 18px;
                }
                .font14{
                    display: inline-block;
                    font-size: 14px;
                    margin: 0 12px;
                    color: #666;
                }
                .visit_date{
                    display: block;
                    float: right;
                    color: #666;
                }

            }

        }
        // 呼出气
        .table_huqi{
            width: 100%;
            table{
                width: 100%;
                border: 1px solid #999999;
            }
            tr{
                th{
                    height: 44px;
                    line-height: 44px;
                    font-size: 18px;
                }
                td{
                    height: 40px;
                    line-height: 40px;
                    font-size: 16px;
                }
            }
            .width180{
                width: 180px;
                box-sizing: border-box;
            }
            .text_left{
                text-align: left;
                padding-left: 15px;
            }
        }
        // BD舒张试验
        .two_table_div{
            width: 100%;
            height: auto;
            display: flex;
            .table1,
            .table2{
                width: 50%;
                table{
                    width: 100%;
                    border: 1px solid #999999;
                }
                tr{
                    th{
                        height: 44px;
                        line-height: 44px;
                        font-size: 18px;
                    }
                    td{
                        height: 40px;
                        line-height: 40px;
                        font-size: 16px;
                    }
                }
                .width180{
                    width: 180px;
                    box-sizing: border-box;
                }
                .text_left{
                    text-align: left;
                    padding-left: 15px;
                }
                .width160{
                    width: 160px;
                    box-sizing: border-box;
                }
                .width200{
                    width: 210px;
                    box-sizing: border-box;
                }
            }
            .table1{
                table{
                    border-right: none;
                }
            }
        }
        .flex_table{
            display: flex;
            justify-content: center;
            flex-direction: row;
            flex-wrap: wrap;
            border: 1px solid #999;
            .flex_item{
                width: 50%;
                height: 225px;
                box-sizing: border-box;
                .item_title{
                    height: 44px;
                    line-height: 44px;
                    box-sizing: border-box;
                    padding-left: 16px;
                    font-size: 18px;
                    text-align: left;
                }
                .item_intro{
                    margin-top: 8px;
                    text-align: left;
                    padding-left: 16px;
                    font-size: 16px;
                }
            }
        }
        .page_num{
            margin-top: 30px;
            color: #666;
        }
        
    }
    .page_footer{
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 100px;
        .footer_img{
            width: 90px;
            height: 90px;
        }
        .tip_container{
            text-align: left;
            margin-left: 15px;
            div{
                font-size: 14px;
                line-height: 24px;
            }
        }
    }
    .br{
        border-right: 1px solid #999;
    }
    .bg_grey{
        background: rgba(0, 0, 0, 0.06);
    }
    .fr{
        float: right;
    }

    .page-break-inside {
        page-break-before: always !important;
        page-break-after: always !important;
        page-break-inside: avoid !important;
    }

}
</style>
