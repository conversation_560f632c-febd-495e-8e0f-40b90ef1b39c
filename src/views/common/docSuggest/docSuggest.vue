<template>
  <!-- 嵌入到健康助手的 医生通知 -->
  <div class="docSuggest" ref="docSuggest">
      <div class="row">
        <div class="label">时间</div>
        <div class="content">{{date}}</div>
      </div>
      <div class="row">
        <div class="label">医生</div>
        <div class="content">{{docter}}</div>
      </div>
      <!-- <div class="row">
        <span class="label">患者</span>
        <div class="content">{{patient}}</div>
      </div> -->
      <div class="row">
        <div class="label">主题</div>
        <div class="content">{{theme}}</div>
      </div>
      <div class="bg-grey"></div>
      <div>
        <div class="border">医生建议</div>
        <div class="tips">{{content}}</div>
      </div>
  </div>
</template>
<script>
  import {docNoticeDetail} from '@/api/common/docSuggest.js'

  export default {
    data() {
      return {
        // 日期，医生名称，内容，主题
        date:'',
        docter:'',
        content:'',
        theme:'',
        // 接口所需的 用户id   和noticeid
        uid:'',
        noticeId:''
      }
    },
    created() {
      let query = this.$route.query
      this.uid = query.uid   // 1518023
      
      this.noticeId = query.notice_id // 1035
      
      this.init()
    },
    mounted() {
      // 获取浏览器可视区域高度
      this.clientHeight = `${document.documentElement.clientHeight}`
      this.$refs.docSuggest.style.minHeight = this.clientHeight + 'px'
    },
    methods: {
      /**
       * 初始化
       */
      async init() {
        this.getNoticeDetail()
      },
      
      getNoticeDetail() {
        return docNoticeDetail(this.noticeId,this.uid).then(res => {
          if (res.code === 200) {
            let data = res.data
            this.theme = data.subject
            this.date = data.release_date
            this.docter = data.doctor_name
            this.content = data.content
          } else {
            this.$toast(res.msg)
          }
        }).catch(err => {
          this.$toast(err)
        })
      },
      
    }
  }
</script>

<style lang="scss" scoped>
  .docSuggest {
    width: 100%;
    font-size: 0.5rem;
    .row{
      width: 100%;
      line-height: 20px;
      text-align: left;
      padding: 0.4rem;
      .label{
        display: inline-block;
        width: 15%;
        vertical-align: top;
      }
      .content{
        width: 80%;
        display: inline-block;
        height: auto;
        color: #777777;
      }
    }
    .bg-grey{
      background: #ededee;
      width: 100%;
      height: 15px;
    }
    .border{
      width: 90%;
      margin: 0 auto;
      height: 1.5rem;
      line-height: 1.5rem;
      border-bottom: 1px solid #ededee;
      text-align: left;
    }
    .tips{
      width: 90%;
      margin: 0 auto;
      line-height: 1.5rem;
      text-align: left;
      color: #777;
      text-indent: 2em;
    }
  }
</style>
