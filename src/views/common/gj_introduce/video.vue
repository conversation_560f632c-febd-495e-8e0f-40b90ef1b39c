<template>
  <div class="vedio">
      <video
        controls="controls"
        autoplay
        playsinline
        webkit-playsinline
        x5-video-player-type="h5"
        x5-video-orientation="portraint"
        x5-video-player-fullscreen="true"
        class="liveVideo"
        style="object-fit: contain;"
        :src="src">
      </video>
  </div>
</template>
<script>

  export default {
    data() {
      return {
          src:'https://zz-med-pub.oss-cn-hangzhou.aliyuncs.com/resource/video/79ebff8932bd18d5471ef6398a065454.mp4'
      }
    },
    created() {

    },
    mounted() {
      
    },
    methods: {
      
    }
  }
</script>

<style lang="scss" scoped>
  .vedio {
    width: 100%;
    margin: 0;
    padding: 0;
    margin: 0;
  }
  .liveVideo {
    width: 100%;
    height: 100%;
  }
</style>
