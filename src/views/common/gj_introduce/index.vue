<template>
  <!-- 嵌入到医生工作室app 的 版本介绍 -->
  <div class="gj_introduce" ref="gjIntroduce">
      <img class="gj_img" src="../images/gj_introduce1.jpg" alt="" />
      <img class="gj_img" src="../images/gj_introduce2.jpg" alt="" />
      <img class="gj_img" src="../images/gj_introduce3.jpg" alt="" />
      <img class="gj_img" src="../images/gj_introduce4.jpg" alt="" />
      <img class="gj_img" src="../images/gj_introduce5.jpg" alt="" />
      <img class="gj_img" src="../images/gj_introduce6.jpg" alt="" />
  </div>
</template>
<script>

  export default {
    data() {
      return {
        clientHeight:600
      }
    },
    created() {

    },
    mounted() {
      // 获取浏览器可视区域高度
      this.clientHeight = `${document.documentElement.clientHeight}`
      this.$refs.gjIntroduce.style.minHeight = this.clientHeight + 'px'
    },
    methods: {
      
    }
  }
</script>

<style lang="scss" scoped>
  .gj_introduce {
    width: 100%;
    margin: 0;
    padding: 0;
    .gj_img{
      width: 100%;
      height: auto;
    }
  }
</style>
