<template>
  <div class="wrapper" :class="{'gj':sourceType==2}" ref="wrapper">
    <div class="header">
      <div class="header-top">
        <div class="header-avatar">
          <avatar :avatarProps="avatarData"/>
          <div class="active_icon" v-if="docInfo.is_auth==1">
            <van-icon name="success"/>
            已认证
          </div>
        </div>
        <div class="header-info">
          <p class="header-info-name">{{ DocName }}</p>
          <div class="header-info-status">
            <div class="header-info-status-box">
              <p class="header-info-status-box-num">{{ docInfo.post_num }}</p>
              <p class="header-info-status-box-type">发布</p>
            </div>
            <div class="header-info-status-box">
              <p class="header-info-status-box-num">{{ fansNum }}</p>
              <p class="header-info-status-box-type">粉丝</p>
            </div>
            <div class="header-info-status-box">
              <p class="header-info-status-box-num">{{ docInfo.like_num }}</p>
              <p class="header-info-status-box-type">获赞</p>
            </div>
            <div class="header-info-status-box">
              <p class="header-info-status-box-num">{{ docInfo.patient_num }}</p>
              <p class="header-info-status-box-type">患者</p>
            </div>
          </div>
          <div class="follow-button2" v-if="sourceType==2 && showScheduleButton">
            <div class="button" v-if="isAttend" @click="btnDocFollowDetachStatusChange(2,docId)">取消关注</div>
            <div class="button btn_attention" v-if="!isAttend" @click="btnDocFollowDetachStatusChange(1,docId)">关注</div>
            <div class="button btn_grade ml10" @click="btnSearchGrade">查询排班</div>
          </div>
          <div class="follow-button" v-else>
            <div class="button" v-if="isAttend" @click="btnDocFollowDetachStatusChange(2,docId)">取消关注</div>
            <div class="button btn_attention" v-if="!isAttend" @click="btnDocFollowDetachStatusChange(1,docId)">关注</div>
          </div>
        </div>
      </div>
      <div class="header-intro" @click="goDoctorIntro">
        <div class="header-intro-box">
          <div class="header-intro-box-info">
            <div class="header-intro-box-info-img">
              <img src="../images/<EMAIL>" v-if="sourceType==2">
              <img src="../images/approve.png" v-else>
            </div>
            <div class="header-intro-box-info-text">
              <span>认证：</span>
              <span class="header-intro-box-info-text-span">{{ approve }}</span>
            </div>
          </div>
          <div class="header-intro-box-info">
            <div class="header-intro-box-info-img">
              <img src="../images/<EMAIL>" v-if="sourceType==2">
              <img src="../images/intro.png" v-else>
            </div>
            <div class="header-intro-box-info-text">
              <span>简介：</span>
              <span class="header-intro-box-info-text-span">{{ summary }}</span>
            </div>
          </div>
        </div>
        <div class="header-intro-right" v-if="sourceType!=2">
          <cc-svg-icon icon-class="info" class="icon-class"/>
        </div>
      </div>
    </div>
    <div class="activity">
      <p class="activity-title">全部动态</p>
      <div v-for="activity in activityList"
           class="activity-group"
      >
        <doctor-activity
          :introProps="activity.content"
          :timeProps="activity.release_time"
          :attendNum="activity.attend_num"
          :activityId="activity.id"
          :videosProps="activity.videos"
          :imgSrcProps="activity.images[0]"
        />
      </div>
    </div>
    <div class="footer">
      <span v-if="isLastReport">没有更多动态啦</span>
      <div v-if="!isLastReport && isLoading" class="footer-loading">
        <span>加载更多动态</span>
        <van-loading type="spinner" size="18" class="footer-icon"/>
      </div>
    </div>
  </div>
</template>

<script>
import avatar from '../components/avatar.vue'
import doctorActivity from '../components/doctorActivity.vue'
import {doctorActivityList, doctorInfo, docFollowDetachStatusChange, searchScheduleList} from '@/api/common/publish.js'
import {getClientHeight, getScrollTop, getScrollHeight} from '@/utils/utils.js'

export default {
  data: () => {
    return {
      sourceType: '',
      docId: '', // 医生ID
      pageNo: 1,
      pageSize: 20,
      avatarData: '', // 头像
      DocName: '', // 医生姓名
      approve: '', // 认证
      summary: '', // 简介
      patientNum: '', // 患者数量
      postNum: '', // 发布数量
      introData: '',
      timeData: '',
      praiseData: '',
      imgSrcData: '',
      isClientBottom: false,
      isLastReport: false,
      isLoading: false,
      showScheduleButton: false,
      docInfo: {},
      fansNum: 0,
      isAttend: false,
      activityList: []
    }
  },
  components: {
    'avatar': avatar,
    'doctor-activity': doctorActivity
  },
  watch: {
    isClientBottom(newVal, oldVal) {
      // 滑倒底部并且还有更多报告时，以及不在加载中（防止重复加载）
      if (newVal && !this.isLastReport && !this.isLoading) {
        // 获取医生动态
        this.getDoctorActivityList(++this.pageNo)
      }
    }
  },
  created() {
    let query = this.$route.query
    this.docId = query.doc_id
    this.sourceType = query.source_type

    // 初始化
    this.init()
  },
  mounted() {
    // 判断是否滑到底部
    this.checkScroll()

    // 获取浏览器可视区域高度
    this.clientHeight = `${document.documentElement.clientHeight}`
    this.$refs.wrapper.style.minHeight = this.clientHeight + 'px'
  },
  methods: {
    /**
     * 初始化
     */
    init() {
      let that = this
      // 获取医生信息
      this.getDoctorInfo()
      // 获取医生动态
      this.getDoctorActivityList(this.pageNo)
      if (this.sourceType == 2) {
        searchScheduleList({doc_id: this.docId}).then((res) => {
          if (res.status = 200) {
            that.showScheduleButton = res.data.has_schedule
          }
        })
      }
    },
    btnSearchGrade() {
      this.noDoubleTap(() => {
        const UA = navigator.userAgent
        const isAndroid = UA.indexOf('Android') > -1 || UA.indexOf('Adr') > -1 // android终端
        const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
        // 原生app方法名称：jumpToReport
        if (isAndroid) {
          console.log('安卓')
          window.android.jumpQueryDoctorArrange(this.docId)
        } else if (isIOS) {
          console.log('苹果')
          window.webkit.messageHandlers.jumpQueryDoctorArrange.postMessage(this.docId)
        } else {
          this.$toast('跳转失败')
        }
      })
    },
    btnDocFollowDetachStatusChange(type, id) {
      let that = this
      this.noDoubleTap(() => {
        docFollowDetachStatusChange(type, id).then(res => {
          if (res.status === 0) {
            if (type == 1) {
              that.fansNum = that.fansNum + 1
              that.isAttend = true
            } else {
              that.fansNum = that.fansNum - 1
              that.isAttend = false
            }
          }
        })
      })
    },
    /**
     * 获取医生信息
     */
    getDoctorInfo() {
      let that = this
      doctorInfo(this.docId).then(res => {
        if (res.status === 0) {
          let data = res.data
          that.docInfo = data
          that.isAttend = data.is_attend
          that.avatarData = data.avatar
          that.DocName = data.name
          that.fansNum = data.fans_num
          that.approve = data.approve
          that.summary = data.summary
          that.patientNum = data.patient_num
          that.postNum = data.post_num
        } else {
          this.$toast(res.msg)
        }
      }).catch(err => {
        this.$toast(err)
      })
    },
    /**
     * 获取医生动态
     * @param {Number} pageNo 页码
     */
    getDoctorActivityList(pageNo) {
      this.isLoading = true
      doctorActivityList({
        doc_id: this.docId,
        page_no: pageNo,
        page_size: this.pageSize
      }).then(res => {
        this.isLoading = false
        if (res.status === 0) {
          let data = res.data
          this.pageNo = data.current_page
          this.activityList = this.activityList.concat(data.data)
          if (data.total_page === data.current_page) this.isLastReport = true
        } else {
          this.$toast(res.msg)
        }
      }).catch(err => {
        this.$toast(err)
      })
    },

    /**
     * 前往医生介绍
     */
    goDoctorIntro() {
      if (this.sourceType != 2) {
        this.$router.push({
          path: '/common/publish/doctor/detail',
          query: {
            'doc_id': this.docId,
            'source_type': this.sourceType
          }
        })
      }
    },
    /**
     * 判断是否滑到底部
     */
    checkScroll() {
      let scrollTop = getScrollTop()
      window.onscroll = () => {
        let newSrollTop = getScrollTop() // 当前滚动高度
        let scrollHeight = getScrollHeight() // 文档完整的高度
        let clientHeight = getClientHeight() // 可视范围的高度
        let distance = 2 // 底部间距

        // 如果当前滚动高度大于之前滚动高度 -> 下滑
        if (newSrollTop > scrollTop) {
          // 如果到底部 this.isClientBottom = true；如果不在底部 this.isClientBottom = false
          if (newSrollTop + clientHeight + distance < scrollHeight) {
            // console.log('没到底')
            this.isClientBottom = false
          } else {
            // console.log('到底了')
            this.isClientBottom = true
          }
        } else {
          this.isClientBottom = false
        }

        // 每次滚动后，把当前滚动高度赋值给之前滚动高度
        scrollTop = newSrollTop
      }
    }
  }
}
</script>

<style lang="scss" scoped>

.wrapper {
  width: 375px;
  overflow: hidden;
  background: #f7f7f7;

  .header {
    width: 375px;
    overflow: hidden;
    background: #fff;
    box-sizing: border-box;
    padding: 7px 18px 17px 16px;

    .header-top {
      display: flex;
      align-items: center;
      justify-content: flex-start;

      .header-avatar {
        width: 85px;
        height: 85px;
        position: relative;

        .active_icon {
          position: absolute;
          width: 65px;
          height: 22px;
          margin: 0 auto;
          background: #1789FC;
          border: 1px solid rgba(255, 255, 255, 0.8980392156862745);
          opacity: 1;
          border-radius: 12px;
          font-size: 11px;
          font-weight: 400;
          line-height: 22px;
          color: #FFFFFF;
          bottom: -10px;
          left: 10px;
        }
      }

      .header-info {
        flex: 1;
        text-align: left;
        margin-left: 18px;

        .header-info-name {
          font-size: 20px;
          font-weight: 500;
          color: #000;
          line-height: 28px;
        }

        .header-info-status {
          display: flex;
          margin-top: 6px;
          align-items: center;
          justify-content: flex-start;

          .header-info-status-box {
            width: 25%;

            .header-info-status-box-num {
              font-size: 15px;
              font-weight: 500;
              color: #000000;
              line-height: 16px;
            }

            .header-info-status-box-type {
              font-size: 13px;
              font-weight: 400;
              line-height: 18px;
              color: #ADADAD;
            }
          }
        }

        .follow-button2 {
          display: flex;
          margin-top: 6px;

          .button {
            width: 113px;
            height: 37px;
            text-align: center;
            background: #F4F4F4;
            opacity: 1;
            border-radius: 4px;
            font-size: 15px;
            font-weight: 400;
            line-height: 37px;
            color: #000000;
          }

          .ml10 {
            margin-left: 10px;
          }

          .btn_attention {
            background: #1789FC;
            color: #fff;
          }
        }

        .follow-button {
          margin-top: 6px;

          .button {
            width: 234px;
            height: 37px;
            text-align: center;
            background: #F4F4F4;
            opacity: 1;
            border-radius: 4px;
            font-size: 15px;
            font-weight: 400;
            line-height: 37px;
            color: #000000;
          }

          .btn_attention {
            background: #1789FC;
            color: #fff;
          }

        }
      }
    }

    .header-intro {
      width: 100%;
      display: flex;
      margin-top: 19px;
      align-items: center;
      justify-content: space-between;

      .header-intro-box {
        width: 315px;

        .header-intro-box-info {
          width: 315px;
          display: flex;
          align-items: center;
          justify-content: flex-start;

          .header-intro-box-info-img {
            width: 15px;
            line-height: 0;

            img {
              width: 100%;
            }
          }

          .header-intro-box-info-text {
            width: 292px;
            display: flex;
            font-size: 14px;
            font-weight: 400;
            color: #626262;
            margin-left: 8px;
            line-height: 20px;

            .header-intro-box-info-text-span {
              flex: 1;
              overflow: hidden;
              text-align: left;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
          }
        }
      }

      .header-intro-right {
        width: 26px;
        line-height: 0;

        .header-intro-right-arrow {
          font-size: 26px;
        }
      }
    }
  }

  .activity {
    margin-top: 8px;
    text-align: left;
    background: #fff;

    .activity-title {
      height: 57px;
      font-size: 15px;
      font-weight: 600;
      color: #2C2C2C;
      line-height: 57px;
      padding-left: 18px;
      border-bottom: 1px solid #F5F5F5;
    }

    .activity-group {
      padding: 25px 18px;
      border-bottom: 1px solid #F5F5F5;

      &:last-of-type {
        border-bottom: 0;
      }
    }
  }

  .footer {
    height: 24px;
    line-height: 24px;
    margin-top: 10px;
    font-size: 14px;
    color: #999;

    .footer-loading {
      display: flex;
      align-items: center;
      justify-content: center;

      .footer-icon {
        margin-left: 4px;
      }
    }
  }
}

.gj {
  .header {
    .header-top {
      .header-avatar {
        .active_icon {
          width: 64px;
          height: 22px;
          background: #FF7B34;
          border: 1px solid rgba(255, 255, 255, 0.8980392156862745);
          opacity: 1;
          border-radius: 12px;
        }
      }

      .header-info {

        .header-info-status {


          .header-info-status-box {

            .header-info-status-box-num {
              font-size: 15px;
              font-weight: 500;
              color: #000000;
              line-height: 16px;
            }

            .header-info-status-box-type {
              font-size: 13px;
              font-weight: 400;
              line-height: 18px;
              color: #ADADAD;
            }
          }
        }

        .follow-button {

          .button {
            border-radius: 4px;
            background: #F4F4F4;
          }

          .btn_attention {
            background: linear-gradient(90deg, #FF8100 0%, #FF8309 10%, #FF8611 20%, #FE881A 30%, #FE8B23 40%, #FE8E2C 50%, #FE9034 60%, #FE923D 70%, #FD9546 80%, #FD974E 90%, #FD9A57 100%);
          }

          .btn_grade {
            height: 35px;
            border: 1px solid #FF7C35;
            background: none;
            font-size: 15px;
            color: #FF7B34;
          }

        }

        .follow-button2 {

          .button {
            border-radius: 4px;
            background: #F4F4F4;
          }

          .btn_attention {
            background: linear-gradient(90deg, #FF8100 0%, #FF8309 10%, #FF8611 20%, #FE881A 30%, #FE8B23 40%, #FE8E2C 50%, #FE9034 60%, #FE923D 70%, #FD9546 80%, #FD974E 90%, #FD9A57 100%);
          }

          .btn_grade {
            height: 35px;
            border: 1px solid #FF7C35;
            background: none;
            font-size: 15px;
            color: #FF7B34;
          }

        }
      }
    }

    .header-intro {
      width: 100%;
      display: flex;
      margin-top: 19px;
      align-items: center;
      justify-content: space-between;

      .header-intro-box {
        width: 315px;

        .header-intro-box-info {
          align-items: baseline;

          .header-intro-box-info-img {
            width: 15px;
            line-height: 0;

            img {
              width: 100%;
            }
          }

          .header-intro-box-info-text {
            width: 300px;

            .header-intro-box-info-text-span {
              white-space: normal;

            }
          }
        }
      }

      .header-intro-right {
        width: 26px;
        line-height: 0;

        .header-intro-right-arrow {
          font-size: 26px;
        }
      }
    }
  }

  .activity {
    .activity-title {
      font-size: 18px;
    }

    .activity-group {
      padding: 25px 18px;
      border-bottom: 1px solid #F5F5F5;

      &:last-of-type {
        border-bottom: 0;
      }
    }
  }

  .footer {
    height: 24px;
    line-height: 24px;
    margin-top: 10px;
    font-size: 14px;
    color: #999;

    .footer-loading {
      display: flex;
      align-items: center;
      justify-content: center;

      .footer-icon {
        margin-left: 4px;
      }
    }
  }
}

</style>
