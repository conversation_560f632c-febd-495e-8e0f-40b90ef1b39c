<template>
  <div class="wrpper" :class="{'gj':sourceType==2}" ref="wrapper">
    <van-tabs v-model="tabId" color="#ff7c35"  sticky @change="changeTabs">
      <van-tab title="推荐" title-style="font-size: 15px;">
        <div>
          <div class="van-search" >
            <div class="van-search__content van-search__content--square" @click="searchInputClicked">
              <div class="van-cell van-cell--borderless van-field">
                <div class="van-field__left-icon"><i class="van-icon van-icon-search"><!----></i></div>
                <div class="van-cell__value van-cell__value--alone">
                  <div class="van-field__body"><input type="search" ref="searchInput"
                                                      @focus="changeEndUi"
                                                      v-model="keywords" placeholder="请输入搜索医院或医生"
                                                      class="van-field__control">
                  </div>
                </div>
              </div>
            </div>
            <div v-if="showCancelSearch" role="button" tabindex="0" @click="cancelSearch" class="van-search__action">
              取消
            </div>
          </div>
          <div class="search_box" v-if="searchBoxFlg &&showFlg && keywords!==''" @click="onSearch">
            <div class="left">
              <div>
                <span class="title">搜索</span><span class="keyword">{{keywords}}</span>
              </div>
              <div class="desc">搜索医生姓名、医生所在医院名</div>
            </div>
            <van-icon class="right_icon" color="#D8D8D8" name="arrow" size="24"/>
          </div>

        </div>
        <van-pull-refresh v-if="!showFlg"
                          v-model="isLoading"
                          success-text="刷新成功"
                          @refresh="onRefresh"
        >
          <div v-if="activityList.length" class="content">
            <activity-item
              v-for="(activity, index) in activityList"
              :key="activity.id"
              :index="index"
              :activityId="activity.id"
              :sourceType="sourceType"
              :avatarProps="activity.avatar"
              :isAuth="activity.is_auth"
              :docNameProps="activity.doc_name"
              :hospNameProps="activity.hosp_name"
              :contentProps="activity.content"
              :resourcesProps="activity.resources"
              :releaseTimeProps="activity.release_time"
              :DocIdProps="activity.doc_id"
              :imagesProps="activity.images"
              :videosProps="activity.videos"
              :attendNum="activity.attend_num"
              :attendStatus="activity.attend_status"
              :docAttendStatus="activity.doc_attend_status"
              :links="activity.links"
              @statusChangeEvent="statusChangeFun"
              class="activity-item"
            />
          </div>
          <div v-if="activityList.length" class="footer">
            <span v-if="isLastReport">没有更多动态啦</span>
            <div v-if="!isLastReport && isLoading" class="footer-loading">
              <span>加载更多动态</span>
              <van-loading type="spinner" size="18" class="footer-icon"/>
            </div>
          </div>
          <div v-if="activityList.length==0 && showPage" class="empty">
            <div class="empty-content">
              <img src="../images/empty.png" alt="">
              <p>暂无动态</p>
            </div>
          </div>
        </van-pull-refresh>
      </van-tab>
      <van-tab title="关注">
        <div>
          <div class="van-search">
            <div class="van-search__content van-search__content--square"  @click="searchInputClicked2">
              <div class="van-cell van-cell--borderless van-field">
                <div class="van-field__left-icon"><i class="van-icon van-icon-search"><!----></i></div>
                <div class="van-cell__value van-cell__value--alone">
                  <div class="van-field__body"><input type="search" ref="searchInput2"
                                                      @focus="changeEndUi" v-model="keywords" placeholder="请输入搜索医院或医生"
                                                      class="van-field__control">
                  </div>
                </div>
              </div>
            </div>
            <div v-if="showCancelSearch" role="button" tabindex="0" @click="cancelSearch" class="van-search__action">
              取消
            </div>
          </div>
          <div class="search_box" v-if="searchBoxFlg &&showFlg && keywords!==''" @click="onSearch">
            <div class="left">
              <div>
                <span class="title">搜索</span><span class="keyword">{{keywords}}</span>
              </div>
              <div class="desc">搜索医生姓名、医生所在医院名</div>
            </div>
            <van-icon class="right_icon" color="#D8D8D8" name="arrow" size="24"/>
          </div>
        </div>
        <follow-view v-if="!showFlg" ref="refFollowView"></follow-view>
      </van-tab>
    </van-tabs>
    <doctor-list v-if="showFlg" :tabId="tabId" :keywords="keywords" ref="searchDoctoList"></doctor-list>
  </div>
</template>

<script>
  import activityItem from '../components/activityItem.vue'
  import searchDoctorList from './searchDoctorList.vue'
  import { doctorActivityList } from '@/api/common/publish.js'
  import { getClientHeight, getScrollTop, getScrollHeight } from '@/utils/utils.js'
  import followView from './follow.vue'

  export default {
    data: () => {
      return {
        tabId: 0,
        showPage: false,
        showFlg: false,
        sourceType: '',
        pageNo: 1,
        keywords: '',
        pageSize: 20,
        showCancelSearch: false,
        isClientBottom: false,
        isLastReport: false,
        isLoading: false,
        searchBoxFlg: false,
        activityList: []
      }
    },
    components: {
      'follow-view': followView,
      'activity-item': activityItem,
      'doctor-list': searchDoctorList
    },
    watch: {
      isClientBottom(newVal, oldVal) {
        if (this.showFlg == false) {
          // 滑倒底部并且还有更多报告时，以及不在加载中（防止重复加载）
          if (newVal && !this.isLastReport && !this.isLoading) {
            if (!this.showFlg) {
              if (this.tabId == 0) {
                // 获取医生动态
                this.getDoctorActivityList(++this.pageNo)
              } else {
                this.$nextTick(() => {
                  this.$refs.refFollowView.getDoctorActivityList(++this.$refs.refFollowView.pageNo)
                })
              }
            } else {
              this.$nextTick(() => {
                this.$refs.searchDoctoList.getSearchDocList(++this.$refs.searchDoctoList.pageNo)
              })
            }
          }
        }
      },
      keywords(newVal, oldVal) {
        console.log(newVal, oldVal, 1234)
        if (newVal !== oldVal && newVal !== '') {
          this.searchBoxFlg = true
          console.log(this.searchBoxFlg, 1234)
        }
      }
    },
    created() {
      this.sourceType = this.$route.query.source_type
      this.tabId = Number(this.$store.getters['docDynamic/tabId'])
      if (this.tabId === 1) {
      } else {
        // 获取医生动态
        this.getDoctorActivityList(this.pageNo)
      }
    },
    mounted() {
      // 判断是否滑到底部
      this.checkScroll()
      // 获取浏览器可视区域高度
      this.clientHeight = `${document.documentElement.clientHeight}`
      this.$refs.wrapper.style.minHeight = this.clientHeight + 'px'
      window.addEventListener('pageshow', (e) => {
        if (e.persisted || (window.performance && window.performance.navigation.type == 2)) {
          if (this.tabId == 0) {
            // 获取医生动态
            this.getDoctorActivityList(1, true)
          } else {
            this.$refs.refFollowView.getDoctorActivityList(1, true)
            this.$refs.refFollowView.getAttachDocList(1)
          }
        }
      }, false)
    },
    methods: {
      searchInputClicked() {
        if (this.$refs.searchInput) {
          this.$refs.searchInput.focus()
        }
      },
      searchInputClicked2() {
        if (this.$refs.searchInput2) {
          this.$refs.searchInput2.focus()
        }
      },
      /**
       * 获取医生动态
       * @param {Number} pageNo 页码
       * @param {Boolean} refresh 刷新标志
       */
      getDoctorActivityList(pageNo, refresh = false) {
        this.isLoading = true
        let that = this
        doctorActivityList({
          page_no: pageNo,
          attend: 0,
          page_size: this.pageSize
        }).then(res => {
          that.isLoading = false
          if (res.status === 0) {
            that.showPage = true
            let data = res.data
            that.pageNo = data.current_page
            // 刷新列表，覆盖数列，否则添加数列
            if (refresh) this.activityList = data.data
            else that.activityList = that.activityList.concat(data.data)

            if (data.total_page === data.current_page) that.isLastReport = true
          } else {
            this.$toast(res.msg)
          }
        }).catch(err => {
          this.$toast(err)
        })
      },
      // 关注状态更改，遍历医生动态列表，更改同一个医生的状态
      statusChangeFun (param) {
        this.activityList = this.activityList.map(item => {
          if (item.doc_id == param.id) {
            item.doc_attend_status = param.docAttendStatus
          }
          return item
        })
      },
      /**
       * 判断是否滑到底部
       */
      checkScroll() {
        let scrollTop = getScrollTop()
        window.onscroll = () => {
          let newSrollTop = getScrollTop() // 当前滚动高度
          let scrollHeight = getScrollHeight() // 文档完整的高度
          let clientHeight = getClientHeight() // 可视范围的高度
          let distance = 2 // 底部间距

          // 如果当前滚动高度大于之前滚动高度 -> 下滑
          if (newSrollTop > scrollTop) {
            // 如果到底部 this.isClientBottom = true；如果不在底部 this.isClientBottom = false
            if (newSrollTop + clientHeight + distance < scrollHeight) {
              // console.log('没到底')
              this.isClientBottom = false
            } else {
              // console.log('到底了')
              this.isClientBottom = true
            }
          } else {
            this.isClientBottom = false
          }

          // 每次滚动后，把当前滚动高度赋值给之前滚动高度
          scrollTop = newSrollTop
        }
      },
      changeEndUi() {
        this.showCancelSearch = true
        this.showFlg = true
      },
      cancelSearch() {
        let that = this
        this.showCancelSearch = false
        this.searchBoxFlg = false
        this.keywords = ''
        that.showFlg = false
        setTimeout(function () {
          that.showFlg = false
        }, 500)

        // console.log(this.showCancelSearch, 1234)
        // if (this.tabId === 1) {
        //   this.$nextTick(() => {
        //     this.$refs.refFollowView.getDoctorActivityList(1, true)
        //   })
        // } else {
        //   // 获取医生动态
        //   this.getDoctorActivityList(1, true)
        // }
      },
      onSearch() {
        if (this.keywords !== '') {
          this.showFlg = true
          this.$nextTick(() => {
            this.$refs.searchDoctoList.getSearchDocList(1, true)
            this.searchBoxFlg = false
          })
        } else {
          this.showCancelSearch = false
          this.showFlg = false
        }
      },
      changeTabs() {
        let that = this
        this.$store.dispatch('docDynamic/saveTabId', this.tabId)
        if (that.tabId == 0) {
          // 获取医生动态
          that.$nextTick(() => {
            that.getDoctorActivityList(this.pageNo)
          })
        } else {
          that.$nextTick(() => {
            that.$refs.refFollowView.getDoctorActivityList(1)
            that.$refs.refFollowView.getAttachDocList(1)
          })
        }
        this.showFlg = false
        this.showCancelSearch = false
        this.keywords = ''
      },
      /**
       * 下拉刷新
       */
      onRefresh() {
        // 获取医生动态
        this.getDoctorActivityList(1, true)
      }
    }
  }
</script>

<style lang="scss" scoped>
  .wrpper {
    background: #f7f7f7;

    .content {
      overflow: hidden;

      .activity-item {
        margin-top: 8px;

        &:first-of-type {
          margin-top: 0;
        }
      }
    }

    .footer {
      height: 24px;
      line-height: 24px;
      margin-top: 10px;
      font-size: 14px;
      color: #999;

      .footer-loading {
        display: flex;
        align-items: center;
        justify-content: center;

        .footer-icon {
          margin-left: 4px;
        }
      }
    }

    .empty {
      height: 100vh;
      overflow: hidden;

      .empty-content {
        margin-top: 30vh;

        img {
          width: 84px;
          height: 84px;
        }

        p {
          font-size: 17px;
          color: #AAAAAA;
          font-weight: 400;
          margin-top: 20px;
        }
      }

    }

    .search_box {
      margin: 5px 0;
      width: 100%;
      text-align: left;
      align-items: center;
      display: flex;
      background: #fff;
      padding: 15px 18px;
      .left {
        width: 85%;
        .title {
          font-size: 17px;
          font-weight: 400;
          line-height: 24px;
          color: #000;
        }
        .keyword {
          color: #1789FC;
          font-size: 17px;
          line-height: 24px;
          margin-left: 10px;
        }
        .desc {
          font-size: 14px;
          font-weight: 400;
          line-height: 20px;
          color: #A7A7A7;
        }
      }
    }
  }
</style>
