<template>
  <div class="wrapper">
    <div class="header-wrapper">
      <div class="FollowUserInfo" v-if="attachDocList.length">
        <div class="title">
          关注医生
        </div>
        <div class="userList">
          <div class="user_item" v-for="docInfo in attachDocList" @click="goDocDetail(docInfo.id)">
            <img :src="docInfo.doc_avatar==''?'https://zz-med-pub.oss-cn-hangzhou.aliyuncs.com/shared/images/default/doc_logo.jpg':docInfo.doc_avatar">
            <div class="user_name">{{docInfo.doc_name}}</div>
          </div>
          <div class="user_item" v-if="total_page>1" @click="btnFollowedUser">
            <div class="empty">
              <img src="../images/more.png">
            </div>
            <!--<img src="../images/empty.png">-->
            <div class="user_name_two">全部医生</div>
          </div>
        </div>
      </div>
    </div>
    <van-pull-refresh
      v-model="isLoading"
      success-text="刷新成功"
      @refresh="onRefresh"
    >
      <div v-if="activityList.length" class="content">
        <div class="title">
          医生动态
        </div>
        <activity-item
          v-for="activity in activityList"
          :activityId="activity.id"
          :sourceType="sourceType"
          :isAuth="activity.is_auth"
          :avatarProps="activity.avatar"
          :docNameProps="activity.doc_name"
          :hospNameProps="activity.hosp_name"
          :contentProps="activity.content"
          :resourcesProps="activity.resources"
          :releaseTimeProps="activity.release_time"
          :DocIdProps="activity.doc_id"
          :imagesProps="activity.images"
          :attendNum="activity.attend_num"
          :attendStatus="activity.attend_status"
          :docAttendStatus="activity.doc_attend_status"
          class="activity-item"
        />
      </div>
      <div v-if="activityList.length" class="footer">
        <span v-if="isLastReport">没有更多动态啦</span>
        <div v-if="!isLastReport && isLoading" class="footer-loading">
          <span>加载更多动态</span>
          <van-loading type="spinner" size="18" class="footer-icon"/>
        </div>
      </div>
      <div v-if="activityList.length==0 && showPage" class="empty">
        <div class="empty-content">
          <img src="../images/empty.png" alt="">
          <p>{{attachDocList.length==0?"暂无关注的医生":"暂无动态"}} </p>
        </div>
      </div>
    </van-pull-refresh>
  </div>
</template>

<script>
  import { doctorActivityList, getAttachDocList } from '@/api/common/publish.js'
  import activityItem from '../components/activityItem.vue'
  import avatar from '../components/avatar.vue'

  export default {
    data: () => {
      return {
        userList: [],
        pageNo: 1,
        pageSize: 20,
        total_page: 1,
        activityList: [],
        attachDocList: [],
        showPage: false,
        isLoading: false,
        isLastReport: false
      }
    },
    components: {
      'activity-item': activityItem,
      'avatar': avatar
    },
    created() {
      this.sourceType = this.$route.query.source_type
      // 获取医生动态
      this.getDoctorActivityList(this.pageNo)
      this.getAttachDocList(1)
    },
    mounted() {
    },
    watch: {
      isClientBottom(newVal, oldVal) {
        // 滑倒底部并且还有更多报告时，以及不在加载中（防止重复加载）
        if (newVal && !this.isLastReport && !this.isLoading) {
          // 获取医生动态
          this.getDoctorActivityList(++this.pageNo)
        }
      }
    },
    methods: {
      /**
       * 前往医生详情
       */
      goDocDetail(doc_id) {
        this.$router.push({
          path: '/common/publish/doctor',
          query: {
            'doc_id': doc_id,
            'source_type': this.sourceType
          }
        })
      },
      btnFollowedUser() {
        this.$router.push({
          path: '/common/publish/doctor/followed',
          query: {
            'source_type': this.sourceType
          }
        })
      },
      /**
       * 获取关注医生
       * @param {Number} pageNo 页码
       * @param {Boolean} refresh 刷新标志
       */
      getAttachDocList(pageNo) {
        let that = this
        getAttachDocList({
          attend: 1,
          page_no: pageNo,
          page_size: 3
        }).then(res => {
          if (res.status === 0) {
            let data = res.data
            that.total_page = data.total_page
            that.attachDocList = data.data
          } else {
            this.$toast(res.msg)
          }
        }).catch(err => {
          this.$toast(err)
        })
      },
      onRefresh() {
        this.getDoctorActivityList(1, true)
      },
      /**
       * 获取医生动态
       * @param {Number} pageNo 页码
       * @param {Boolean} refresh 刷新标志
       */
      getDoctorActivityList(pageNo, refresh = false) {
        this.isLoading = true
        let that = this
        doctorActivityList({
          page_no: pageNo,
          attend: 1,
          page_size: this.pageSize
        }).then(res => {
          that.isLoading = false
          if (res.status === 0) {
            that.showPage = true
            let data = res.data
            that.pageNo = data.current_page
            // 刷新列表，覆盖数列，否则添加数列
            if (refresh) {
              that.activityList = data.data
            } else {
              that.activityList = that.activityList.concat(data.data)
            }
            if (data.total_page === data.current_page) that.isLastReport = true
          } else {
            this.$toast(res.msg)
          }
        }).catch(err => {
          this.$toast(err)
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  .wrpper {
    background: #f7f7f7;
    .header-wrapper {
      width: 100%;
      top: 40px;
      .FollowUserInfo {
        padding-top: 10px;
        .title {
          text-align: left;
          margin-left: 20px;
          font-size: 15px;
          font-weight: 500;
          line-height: 21px;
          color: #000000;
        }
        .userList {
          display: flex;
          padding-top: 5px;
          .user_item {
            width: 25%;
            img {
              width: 57px;
              height: 57px;
              border-radius: 50%;
            }
            .empty {
              width: 57px;
              height: 57px;
              display: flex;
              margin: 0 auto;
              justify-content: center;
              align-items: center;
              background: #EAEAEA;
              border-radius: 50%;
              img {
                width: 27px;
                height: 27px;
              }
            }
            .user_name {
              font-size: 13px;
              font-weight: 400;
              line-height: 26px;
              color: #393939;
              opacity: 0.8;
            }
            .user_name_two {
              font-size: 13px;
              font-weight: 400;
              line-height: 26px;
              color: #393939;
              opacity: 0.8;
              margin-top: 7px;
            }

          }
        }

      }
    }

    .content {
      overflow: hidden;
      .title {
        text-align: left;
        margin-left: 20px;
        font-size: 15px;
        font-weight: 500;
        line-height: 21px;
        color: #000000;
      }
      .activity-item {
        margin-top: 8px;

        &:first-of-type {
          margin-top: 0;
        }
      }
    }

    .footer {
      height: 24px;
      line-height: 24px;
      margin-top: 10px;
      font-size: 14px;
      color: #999;

      .footer-loading {
        display: flex;
        align-items: center;
        justify-content: center;

        .footer-icon {
          margin-left: 4px;
        }
      }
    }

    .empty {
      height: 100vh;
      overflow: hidden;

      .empty-content {
        margin-top: 30vh;

        img {
          width: 84px;
          height: 84px;
        }

        p {
          font-size: 17px;
          color: #AAAAAA;
          font-weight: 400;
          margin-top: 20px;
        }
      }

    }
  }
</style>
