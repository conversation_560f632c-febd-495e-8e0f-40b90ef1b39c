<template>
  <div class="wrapper" ref="wrapper">
    <div class="title">{{ subject }}</div>
    <div class="date">发布时间：{{ date }}</div>
    <div class="article">{{ content }}</div>
    <div class="service_package" v-if="doc_package_id">
      <div class="service_name" >
        <img class="icon" src="../images/package_box.png">
        <div class="service_info">
          <div class="service_info_title">{{ doc_service_package.name}}</div>
        </div>
      </div>
      <div class="service_button" @click="btnPackageDetail">查看</div>
    </div>
  </div>
</template>
<script>
  import {noticeDetailNew, noticeChangeStatus} from '@/api/common/publish.js'
  import wx from "weixin-js-sdk";

  export default {
    data() {
      return {
        subject: '',
        content: '',
        date: '',
        noticeId: '',
        userId: '',
        doc_package_id:0,
        doc_service_package:{}
      }
    },
    created() {
      let query = this.$route.query
      this.userId = query.user_id
      this.noticeId = query.notice_id
      // this.noticeId = 1031//测试写死
      // 初始化
      this.init()
    },
    mounted() {
      // 获取浏览器可视区域高度
      this.clientHeight = `${document.documentElement.clientHeight}`
      this.$refs.wrapper.style.minHeight = this.clientHeight + 'px'
    },
    methods: {
      /**
       * 初始化
       */
      async init() {
        // 发布通知-获取通知内容
        let detailRes = await this.getNoticeDetail()
        // 如果获取失败，不往下执行
        if (!detailRes) return
        // 发布通知-更新阅读状态
        this.postNoticeChangeStatus()
      },
      /**
       * 发布通知-获取通知内容
       */
      getNoticeDetail() {
        return noticeDetailNew(this.noticeId).then(res => {
          if (res.status === 0) {
            let data = res.data
            this.subject = data.subject
            this.content = data.content
            this.date = data.release_date
            this.doc_package_id=data.doc_package_id
            this.doc_service_package=data.doc_service_package
            return true
          } else {
            this.$toast(res.msg)
            return false
          }
        }).catch(err => {
          this.$toast(err)
          return false
        })
      },
      btnPackageDetail(){
        let that = this
        this.noDoubleTap(() => {
          wx.miniProgram.navigateTo({
            url: '/pages/servicePackage/packageDetail/packageDetail?source=NOTICE&doc_open_package_id=' +  this.doc_package_id
          })
        })
      },
      /**
       * 发布通知-更新阅读状态
       */
      postNoticeChangeStatus() {
        noticeChangeStatus({
          noticeId: this.noticeId,
          userId: this.userId
        }).then(res => {
          if (res.code !== 200) {
            console.log(res.msg)
          }
        }).catch(err => {
          console.log(err)
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  .wrapper {
    width: 100%;
    overflow: hidden;

    .title {
      min-height: 30px;
      padding: 22px 22px 12px 22px;
      line-height: 30px;
      color: #333;
      font-size: 22px;
      font-weight: 600;
      text-align: left;
    }

    .date {
      height: 21px;
      padding: 0 22px;
      color: #9b9b9b;
      font-size: 14px;
      margin-bottom: 16px;
      text-align: left;
    }

    .article {
      color: #333;
      padding: 0 22px;
      font-size: 18px;
      text-align: left;
      line-height: 34px;
      margin-bottom: 24px;
      word-break: break-word;
    }


    /*service_package start*/
    .service_package{
      background: #FFEFD8;
      border: 1px solid #F9DBB0;
      border-radius: 12px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 5px 15px 0;
      padding: 14px;
      .service_name{
        font-weight: 500;
        font-size: 17px;
        line-height: 24px;
        /* identical to box height */
        color: #825A1D;
        display: flex;
        justify-content: flex-start;
        align-content: center;
        align-items: center;
        .service_info{
          .service_info_title{
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            width: 220px;
            text-align: left;
          }
        }

        .icon{
          font-size: 0;
          width: 24px;
          height: 24px;
          margin-right: 14px;
        }

      }
      .service_button{
        width: 60px;
        background: #F4B24D;
        border-radius: 6px;
        font-weight: 500;
        font-size: 14px;
        line-height: 28px;
        color: #FFFFFF;
        text-align: center;
      }
    }
    /*service_package end*/
  }
</style>
