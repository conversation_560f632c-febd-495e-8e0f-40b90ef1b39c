<template>
  <div class="wrapper">
    <van-pull-refresh
      v-model="isLoading"
      success-text="刷新成功"
      @refresh="onRefresh"
    >
      <div v-if="userList.length" class="content">
        <activity-item
          v-for="activity in activityList"
          :sourceType="sourceType"
          :avatarProps="activity.avatar"
          :docNameProps="activity.doc_name"
          :isAuth="activity.is_auth"
          :hospNameProps="activity.hosp_name"
          :contentProps="activity.content"
          :releaseTimeProps="activity.release_time"
          :DocIdProps="activity.doc_id"
          :imagesProps="activity.images"
          class="activity-item"
        />
      </div>
      <div v-if="userList.length" class="footer">
        <span v-if="isLastReport">没有更多动态啦</span>
        <div v-if="!isLastReport && isLoading" class="footer-loading">
          <span>加载更多动态</span>
          <van-loading type="spinner" size="18" class="footer-icon"/>
        </div>
      </div>
      <div v-if="userList.length==0 && showPage" class="empty">
        <div class="empty-content">
          <img src="../images/empty.png" alt="">
          <p>暂无数据</p>
        </div>
      </div>
    </van-pull-refresh>
  </div>
</template>

<script>
  export default {
    data: () => {
      return {
        userList: [],
        showPage: true,
        isLoading: false,
        isLastReport: false
      }
    },
    created() {
    },
    mounted() {
    },
    methods: {}
  }
</script>

<style lang="scss" scoped>
  .wrpper {
    background: #f7f7f7;
  }
</style>
