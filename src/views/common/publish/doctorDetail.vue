<template>
  <div class="wrapper" ref="wrapper">
    <div class="box">
      <!-- 医生简介 -->
      <div v-if="intro" class="group introduce">
        <h2 class="group-title">医生简介</h2>
        <p class="introduce-info">{{ intro }}</p>
      </div>
      <!-- 擅长病症 -->
      <div v-if="expert" class="group introduce">
        <h2 class="group-title">擅长病症</h2>
        <p class="introduce-info">{{ expert }}</p>
      </div>
      <!-- 门诊排班 -->
      <div class="group schedule">
        <h2 class="group-title">门诊排班</h2>
        <schedule
          ref="scheduleComp"
          :editFlag="false"
          :scheduleProp="scheduleData"
          class="group-schedule"
        />
      </div>
      <!-- 地图跳转 -->
      <div class="map" @click="goMap">
        <div class="map-top">
          <div class="map-top-info">
            <p class="map-top-info-name">{{ hosp }}</p>
            <p class="map-top-info-room">{{ clinicAddr }}</p>
          </div>
          <van-icon name="arrow" class="map-top-arrow"/>
        </div>
        <p class="map-address">{{ addr }}</p>
      </div>
    </div>
  </div>
</template>

<script>
  import wx from 'weixin-js-sdk'
  import schedule from '../components/schedule.vue'
  import {doctorShow} from '@/api/common/publish.js'
  import recipesVue from '../../cookBook/recipes.vue'

  export default {
    data: () => {
      return {
        sourceType: '',
        docId: '', // 医生ID
        hosp: '', // 医院名称
        addr: '', // 医院地址
        clinicAddr: '', // 诊室地址
        intro: '', // 医生简介
        expert: '', // 擅长病症
        latitude: '', // 纬度
        longitude: '', // 经度
        scheduleData: [
          {
            duration_type: 1, // 1上午 2下午
            mon: 0, // 周一 1坐诊 0不坐诊
            tue: 0, // 周二 1坐诊 0不坐诊
            wed: 0, // 周三 1坐诊 0不坐诊
            thu: 0, // 周四 1坐诊 0不坐诊
            fri: 0, // 周五 1坐诊 0不坐诊
            sat: 0, // 周六 1坐诊 0不坐诊
            sun: 0 // 周日 1坐诊 0不坐诊
          },
          {
            duration_type: 2, // 1上午 2下午
            mon: 0, // 周一 1坐诊 0不坐诊
            tue: 0, // 周二 1坐诊 0不坐诊
            wed: 0, // 周三 1坐诊 0不坐诊
            thu: 0, // 周四 1坐诊 0不坐诊
            fri: 0, // 周五 1坐诊 0不坐诊
            sat: 0, // 周六 1坐诊 0不坐诊
            sun: 0 // 周日 1坐诊 0不坐诊
          }
        ]
      }
    },
    components: {
      schedule
    },
    created() {
      let query = this.$route.query
      this.docId = query.doc_id
      this.sourceType = query.source_type

      // 获取医生简历、排班
      this.getDoctorShow()
    },
    mounted() {
      // 获取浏览器可视区域高度
      this.clientHeight = `${document.documentElement.clientHeight}`
      this.$refs.wrapper.style.minHeight = this.clientHeight + 'px'
    },
    methods: {
      /**
       * 获取医生简历、排班
       */
      getDoctorShow() {
        doctorShow(this.docId).then(res => {
          if (res.status === 0) {
            let data = res.data
            let hosp = data.hosp
            let resume = data.resume || {}

            this.hosp = hosp.name
            this.addr = hosp.full_addr
            this.longitude = hosp.longitude
            this.latitude = hosp.latitude

            this.intro = resume.intro
            this.expert = resume.expert
            this.clinicAddr = resume.clinic_addr

            if (data.clinic_time && data.clinic_time.length > 0) this.scheduleData = data.clinic_time
          } else {
            this.$toast(res.msg)
          }
        }).catch(err => {
          this.$toast(err)
        })
      },
      /**
       * 地图跳转
       */
      goMap() {
        // 小程序 this.sourceType === 1 跳转小程序地图组件页
        if (this.sourceType === '1') {
          wx.miniProgram.navigateTo({
            url: `/pages/main/map/map?latitude=${ this.latitude }&longitude=${ this.longitude }&title=${ this.hosp }`
          })
        } else if (this.sourceType === '3') {
          wx.miniProgram.navigateTo({
            url: `/pages/home/<USER>/map/map?latitude=${ this.latitude }&longitude=${ this.longitude }&title=${ this.hosp }`
          })
        } else {
          window.location.href =
            `http://api.map.baidu.com/marker?location=${ this.latitude },${ this.longitude }&title=${ this.hosp }&content=${ this.addr }&output=html`
        }
      }
    }
  }
</script>

<style lang="scss" scoped>
  .wrapper {
    padding: 30px 18px 0;
    background: #fff;
    .box {
      background: #fff;
      .group {
        text-align: left;
        margin-top: 50px;

        &:first-of-type {
          margin-top: 0;
        }

        .group-title {
          font-size: 15px;
          font-weight: 400;
          color: #919191;
          line-height: 21px;
        }
      }

      .introduce {

        .introduce-info {
          font-size: 17px;
          font-weight: 400;
          margin-top: 10px;
          color: #3B3B3B;
          line-height: 29px;
        }
      }

      .group-schedule {
        margin-top: 18px;
      }

      .map {
        padding: 12px 0;
        text-align: left;
        margin-top: 20px;
        border: 1px solid #EFEFEF {
          left: 0px;
          right: 0px;
        }
        .map-top {
          display: flex;
          align-items: center;
          justify-content: space-between;

          .map-top-info {

            .map-top-info-name {
              font-size: 15px;
              font-weight: 400;
              color: #000000;
              line-height: 21px;
            }

            .map-top-info-room {
              font-size: 13px;
              color: #A3A3A3;
              font-weight: 400;
              line-height: 18px;
            }
          }

          .map-top-arrow {
            font-size: 18px;
            color: #A3A3A3;
          }
        }

        .map-address {
          font-size: 13px;
          color: #A3A3A3;
          font-weight: 400;
          line-height: 18px;
        }
      }
    }
  }
</style>
