<!--
 * @Descripttion:
 * @version:
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-08-03 09:52:33
 * @LastEditors: liuqiannan
 * @LastEditTime: 2021-08-03 15:36:06
-->
<template>
  <div class="wrpper" ref="wrapper">
    <div v-if="hasData">
      <activity-item
        :activityId="activityId"
        :DocIdProps="activityList.doc_id"
        :avatarProps="activityList.doc_info.photo"
        :isAuth="activityList.doc_info.is_auth"
        :docNameProps="activityList.doc_info.name"
        :hospNameProps="activityList.doc_info.hospName"
        :contentProps="activityList.content"
        :releaseTimeProps="activityList.updated_at"
        :attendNum="activityList.attend_num"
        :attendStatus="activityList.attend_status"
        :sourceType="sourceType"
        :imagesProps="activityList.images"
        :videosProps="activityList.videos"
        :article="activityList.article"
        :wechat_mini_share_code="activityList.wechat_mini_share_code"
        :servicePackageName="activityList.doc_service_package.package_name"
      />

    </div>

    <div v-else class="no-data-wrapper">
      <div class="center">
        <img src="../images/no-dynamic-img.png" class="no-data-img"/>
      </div>
      <div class="center">动态不见了，去看看别的动态吧～</div>
    </div>
  </div>
</template>

<script>
import activityItem from '../components/shareActivityItem.vue'
import {shareActivityDetail, getWechatJsSign} from '@/api/common/publish.js'
import wx from 'weixin-js-sdk'

export default {
  components: {
    activityItem
  },
  data() {
    return {
      sourceType: '',
      activityId: '',
      url: '',
      activityList: {
        doc_info: {}
      },
      hasData: true
    }
  },
  created() {
    let that = this


    let query = this.$route.query
    this.activityId = query.activity_id
    this.sourceType = query.source_type
    this.url = `${process.env.VUE_APP_BASE_URL}common/publish/activity/shareActivityDetail?activity_id=${this.activityId}&source_type=2`

    // 获取动态详情
    getWechatJsSign(this.url).then(res => {
      if (res.status === 0) {
        wx.config({
          debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
          appId: res.data.appId, // 必填，公众号的唯一标识
          timestamp: res.data.timestamp, // 必填，生成签名的时间戳
          nonceStr: res.data.nonceStr, // 必填，生成签名的随机串
          signature: res.data.signature, // 必填，签名
          jsApiList: [
            "updateAppMessageShareData"
          ],
        });
        that.getActivityDetail()
      }
    })
  },
  mounted() {
    // 获取浏览器可视区域高度
    this.clientHeight = `${document.documentElement.clientHeight}`
    this.$refs.wrapper.style.minHeight = this.clientHeight + 'px'
  },
  methods: {
    // 获取动态详情
    getActivityDetail() {
      let that = this
      shareActivityDetail(this.activityId, 'QR_CODE_DS').then(res => {
        if (res.code === 200) {

          this.hasData = true;
          this.activityList = res.data
          let imgUrl='https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/wechat/zzmedSugarController/2.2.0/zz_logo2.png'
          if (res.data.thumb_images.length > 0) {
            imgUrl = res.data.thumb_images[0]
            if (imgUrl.indexOf('http') == -1) {
              imgUrl = 'https:' + imgUrl
            }
          }

          console.log(imgUrl, 12121)
          document.title = res.data.text
          wx.ready(function () {   //需在用户可能点击分享按钮前就先调用
            wx.updateAppMessageShareData({
              title: res.data.text, // 分享标题
              desc: '来自[医生工作室]' + res.data.doc_info.name + '的文章动态', // 分享描述
              link: that.url, // 分享链接，该链接域名或路径必须与当前页面对应的公众号 JS 安全域名一致
              imgUrl: imgUrl, // 分享图标
              success: function () {
                // 设置成功
                console.log(1111)
              }
            })

          });


        } else {
          this.hasData = false;
          this.$toast(res.msg)
        }
      }, error => {
        this.hasData = false;
      }).catch(err => {
        this.hasData = false;
        this.$toast(err)
      })
    }
  }
}
</script>

<style>
.no-data-wrapper {
  height: 100%;
  /* background: #f7f7f7; */
}

.no-data-img {
  width: 224px;
  height: 125px;
  margin: 50px auto 20px;
}

.center {
  text-align: center;
  font-size: 17px;
}
</style>
