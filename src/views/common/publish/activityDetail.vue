<!--
 * @Descripttion:
 * @version:
 * @Author: l<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-09 16:13:01
 * @LastEditors: liuqiannan
 * @LastEditTime: 2021-08-03 13:47:14
-->
<template>
  <div class="wrpper" ref="wrapper">
    <div v-if="hasData">
      <activity-item
        :activityId="activityId"
        :DocIdProps="activityList.doc_id"
        :avatarProps="activityList.avatar"
        :isAuth="activityList.is_auth"
        :docNameProps="activityList.doc_name"
        :hospNameProps="activityList.hosp_name"
        :contentProps="activityList.content"
        :resourcesProps="activityList.resources"
        :releaseTimeProps="activityList.release_time"
        :attendNum="activityList.attend_num"
        :attendStatus="activityList.attend_status"
        :sourceType="sourceType"
        :imagesProps="activityList.images"
        :videosProps="activityList.videos"
        :docAttendStatus="activityList.doc_attend_status"
      />
    </div>
    <div v-else class="no-data-wrapper">
      <div class="center">
          <img src="../images/no-dynamic-img.png" class="no-data-img" />
      </div>
      <div class="center">动态不见了，去看看别的动态吧～</div>
    </div>
  </div>

</template>

<script>
  import activityItem from '../components/activityItem.vue'
  import {activityDetail} from '@/api/common/publish.js'

  export default {
    data: () => {
      return {
        sourceType:'',
        activityId: '',
        activityList: {
          id: '',
          avatar: '', // 头像
          doc_name: '', // 医生姓名
          hosp_name: '', // 医院名称
          content: '', // 动态内容
          release_time: '', // 发布时长
          images: [] // 发布图片
        },
        hasData:false //有数据时显示
      }
    },
    components: {
      'activity-item': activityItem
    },
    created() {
      let query = this.$route.query
      this.activityId = query.activity_id
      this.sourceType = query.source_type
      // 获取动态详情
      this.getActivityDetail()
    },
    mounted() {
      // 获取浏览器可视区域高度
      this.clientHeight = `${document.documentElement.clientHeight}`
      this.$refs.wrapper.style.minHeight = this.clientHeight + 'px'
    },
    methods: {
      /**
       * 获取动态详情
       */
      getActivityDetail() {
        activityDetail(this.activityId).then(res => {
          console.log(res,'12341234')
          if (res.status === 0) {
            this.activityList = res.data;
            this.hasData = true;
          } else {
            this.hasData = false;
            this.$toast(res.msg)
          }
        },error=>{
          this.hasData = false;
        }).catch(err => {
          this.hasData = false;
          this.$toast(err)
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  .wrpper {
    background: #f7f7f7;
  }
  .no-data-wrapper{
    height:100%;
    background:#f7f7f7;
  }
  .no-data-img {
    width: 224px;
    height: 125px;
    margin: 50px auto 20px;
  }
  .center {
      text-align: center;
      font-size: 17px;
  }
</style>
