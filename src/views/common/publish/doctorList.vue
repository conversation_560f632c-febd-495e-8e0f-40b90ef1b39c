<template>
  <div class="wrapper" ref="wrapper">
    <div v-if="doctorList.length" class="content">
      <div class="item-wrapper">
        <div class="item-top" v-for="docInfo in doctorList">
          <div @click="goDocDetail(docInfo.id)" class="item-avatar">
            <avatar :avatarProps="docInfo.doc_avatar"/>
          </div>
          <div @click="goDocDetail(docInfo.id)" class="item-info">
            <div class="item-name">{{docInfo.doc_name}}
              <van-icon v-if="docInfo.is_auth" name="checked" color="#1789FC"/>
            </div>
            <div class="item-status">
              <span class="item-status-site">{{docInfo.hosp_name}}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="doctorList.length" class="footer">
      <span v-if="isLastReport">没有更多动态啦</span>
      <div v-if="!isLastReport" class="footer-loading">
        <span>加载更多动态</span>
        <van-loading type="spinner" size="18" class="footer-icon"/>
      </div>
    </div>
    <div v-if="doctorList.length==0 && showPage" class="empty">
      <div class="empty-content">
        <img src="../images/empty.png" alt="">
        <p>暂无数据</p>
      </div>
    </div>
  </div>
</template>

<script>
  import avatar from '../components/avatar.vue'
  import { getClientHeight, getScrollTop, getScrollHeight } from '@/utils/utils.js'
  import { getAttachDocList } from '@/api/common/publish.js'

  export default {
    data: () => {
      return {
        pageNo: 1,
        pageSize: 8,
        isClientBottom: false,
        isLastReport: false,
        avatarProps: 'https://zz-med-test-pub.oss-cn-hangzhou.aliyuncs.com/pro/doc/202006/default/TTyjksi4hSrsJpsiIh4t6Ed7oRXShzHGjPhRJ6XW.jpeg',
        doctorList: []
      }
    },
    components: {
      'avatar': avatar
    },
    watch: {
      isClientBottom(newVal, oldVal) {
        // console.log(1341324)
        // 滑倒底部并且还有更多报告时，以及不在加载中（防止重复加载）
        if (newVal && !this.isLastReport) {
          // 获取医生动态
          this.getAttachDocList(++this.pageNo)
        }
      }
    },
    created() {
      this.sourceType = this.$route.query.source_type
      // 获取医生动态
      this.getAttachDocList(this.pageNo)
    },
    mounted() {
      // 判断是否滑到底部
      this.checkScroll()
      // 获取浏览器可视区域高度
      this.clientHeight = `${document.documentElement.clientHeight}`
      this.$refs.wrapper.style.minHeight = this.clientHeight + 'px'
    },
    methods: {
      /**
       * 前往医生详情
       */
      goDocDetail(doc_id) {
        this.$router.push({
          path: '/common/publish/doctor',
          query: {
            'doc_id': doc_id,
            'source_type': this.sourceType
          }
        })
      },
      /**
       * 获取医生动态
       * @param {Number} pageNo 页码
       * @param {Boolean} refresh 刷新标志
       */
      getAttachDocList(pageNo, refresh = false) {
        let that = this
        getAttachDocList({
          page_no: pageNo,
          attend: 1,
          page_size: this.pageSize
        }).then(res => {
          if (res.status === 0) {
            that.showPage = true
            let data = res.data
            that.pageNo = data.current_page
            // 刷新列表，覆盖数列，否则添加数列

            if (refresh) this.doctorList = data.data
            else that.doctorList = that.doctorList.concat(data.data)

            if (data.total_page === data.current_page) that.isLastReport = true
          } else {
            this.$toast(res.msg)
          }
        }).catch(err => {
          this.$toast(err)
        })
      },
      /**
       * 判断是否滑到底部
       */
      checkScroll() {
        let scrollTop = getScrollTop()
        window.onscroll = () => {
          let newSrollTop = getScrollTop() // 当前滚动高度
          let scrollHeight = getScrollHeight() // 文档完整的高度
          let clientHeight = getClientHeight() // 可视范围的高度
          let distance = 2 // 底部间距

          // 如果当前滚动高度大于之前滚动高度 -> 下滑
          if (newSrollTop > scrollTop) {
            // 如果到底部 this.isClientBottom = true；如果不在底部 this.isClientBottom = false
            if (newSrollTop + clientHeight + distance < scrollHeight) {
               // console.log('没到底')
              this.isClientBottom = false
            } else {
               // console.log('到底了')
              this.isClientBottom = true
            }
          } else {
            this.isClientBottom = false
          }

          // 每次滚动后，把当前滚动高度赋值给之前滚动高度
          scrollTop = newSrollTop
        }
      }
    }
  }
</script>

<style lang="scss" scoped>
  .wrapper {
    width: 375px;
    overflow: hidden;
    background: #f7f7f7;
    .content {
      background: #fff;
      .item-top {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        padding: 20px 18px;
        border-bottom: 1px solid #F5F5F5;;
        .item-avatar {
          width: 54px;
          height: 54px;
          img {
            width: 100%;
          }
        }

        .item-info {
          text-align: left;
          margin-left: 8px;
          margin-top: 3px;
          width: 270px;
          .item-name {
            font-size: 17px;
            font-weight: 500;
            color: #253457;
            line-height: 26px;
            img {
              width: 17px;
              height: 17px;
              margin-left: 5px;
              margin-top: 4px;
            }
          }

          .item-status {
            font-size: 13px;
            font-weight: 400;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            color: #AAAAAA;
            margin-top: 4px;
            .item-status-time {
              margin-left: 10px;
            }
          }
        }
      }
    }

    .footer {
      height: 24px;
      line-height: 24px;
      margin-top: 10px;
      font-size: 14px;
      color: #999;

      .footer-loading {
        display: flex;
        align-items: center;
        justify-content: center;

        .footer-icon {
          margin-left: 4px;
        }
      }
    }

    .empty {
      height: 100vh;
      overflow: hidden;

      .empty-content {
        margin-top: 30vh;

        img {
          width: 84px;
          height: 84px;
        }

        p {
          font-size: 17px;
          color: #AAAAAA;
          font-weight: 400;
          margin-top: 20px;
        }
      }

    }
  }
</style>
