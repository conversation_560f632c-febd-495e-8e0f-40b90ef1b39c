<template>
  <div class="container">
    <div class="header">
      <div class="avatar">
        <img :src="patient_avatar" alt="avatar">
      </div>
      <div class="info">
        <div class="first">
          <span class="name">{{ patient_name }}</span>
          <!-- <img :src="require(`../../assets/images/medical-record/${patient_name_visit_level_img}.png`)" :alt="patient_name_visit_level_img" class="visit-level" v-if="patient_name_visit_level_img"> -->
          <span class="visit" v-if="userinfo.visit_level">V<span class="n">{{ userinfo.visit_level }}</span></span>
          <span class="diabetes" v-if="patient_diabetes">{{ diabetes_type }} {{ patient_diabetes }}</span>
        </div>
        <div class="second">
          <span class="gender">{{ patient_gender }}</span>
          <span class="age">{{ patient_age }}</span>
          <span class="bmi">{{ patient_bmi }}</span>
        </div>
      </div>
    </div>
    <div class="content">
      <!-- 异常提醒 -->
      <van-collapse v-model="active_attention" class="title-collapse-item-patient attention">
        <van-collapse-item name="abnormal">
          <template #title>
            <div>
              <img class="icon" src="../../assets/images/medical-record/attention-01.png" alt="icon">
              <span class="title">异常提醒</span>
              <span class="tips">（仅针对部分重要指标做提醒）</span>
            </div>
          </template>
          <!-- <ul v-if="abnormal_list.length > 0 || abnormal_ass['eye'].list.length > 0 || abnormal_ass['imt'].list.length > 0">
            <li class="attention-item-1" v-for="(item, index) in abnormal_list" :key="index">
              <div class="lf">
                <div class="name">{{ item.name }}</div>
                <div class="value">
                  <span class="val">{{ item.answer_name || '-' }}</span>
                  <img src="../../assets/images/medical-record/arrow-up.png" alt="img" class="icon" v-if="getArrowType(item) === 'arrow-up'">
                  <img src="../../assets/images/medical-record/arrow-down.png" alt="img" class="icon" v-else>
                </div>
              </div>
              <div class="rt">
                <div class="date">{{ item.visit_at || '-' }}</div>
                <div class="reference">{{ item.unit || '-' }}</div>
              </div>
            </li>
            
            <li class="attention-item-2" v-if="abnormal_ass['eye'].list.length > 0">
              <div class="lf">
                <div class="name">{{ abnormal_ass['eye'].name }}</div>
              </div>
              <div class="list">
                <div v-for="(item, index) in abnormal_ass['eye'].list" :key="index">
                  <div>
                    <span class="val">{{ item.title }}</span>
                    <span class="date">{{ item.visit_at }}</span>
                  </div>
                  <div class="val">{{ item.value }}</div>
                </div>
              </div>
            </li>

            <li class="attention-item-2" v-if="abnormal_ass['imt'].list.length > 0">
              <div class="lf">
                <div class="name">{{ abnormal_ass['imt'].name }}</div>
              </div>
              <div class="list">
                <div v-for="(item, index) in abnormal_ass['imt'].list" :key="index">
                  <div>
                    <span class="val">{{ item.title }}</span>
                    <span class="date">{{ item.visit_at }}</span>
                  </div>
                  <div class="val">{{ item.value }}</div>
                </div>
              </div>
            </li>
          </ul>

          <div class="none" v-else>暂无数据</div> -->    

          <ul v-if="abnormal_vital_lab_ass.length > 0">
            <li class="attention-item-1" v-for="(item, index) in abnormal_vital_lab_ass" :key="index">
              <div class="lf">
                <div class="name">{{ item.name }}</div>
                <div class="value" v-if="item.indicator_group_code !== 'bp'">
                  <img alt="img" class="icon danger" v-if="item.icon.warn" :src="require(`../../assets/images/medical-record/danger-${item.icon.warn}.png`)">
                  <!-- <img alt="img" class="icon danger" v-if="item.icon.warn === 'high'" src="../../assets/images/medical-record/danger-high.png">
                  <img alt="img" class="icon danger" v-if="item.icon.warn === 'low'" src="../../assets/images/medical-record/danger-low.png"> -->
                  <span :class="`val ${item.icon.cls} ${item.icon.arrow}`">{{ item.indicator_value || '-' }}</span>
                  <img alt="img" class="icon arrow" v-if="item.icon.arrow" :src="require(`../../assets/images/medical-record/arrow-${item.icon.arrow}.png`)">
                  <!-- <img alt="img" class="icon arrow" v-if="item.icon.arrow === 'up'" src="../../assets/images/medical-record/arrow-up.png">
                  <img alt="img" class="icon arrow" v-if="item.icon.arrow === 'down'" src="../../assets/images/medical-record/arrow-down.png"> -->
                </div>
                <div class="value" v-else>
                  <span class="sbp" v-if="item.bp.sbp">
                    <img alt="img" class="icon danger" v-if="item.bp.sbp.icon.warn" :src="require(`../../assets/images/medical-record/danger-${item.bp.sbp.icon.warn}.png`)">
                    <!-- <img alt="img" class="icon danger" v-if="item.bp.sbp.icon.warn === 'high'" src="../../assets/images/medical-record/danger-high.png">
                    <img alt="img" class="icon danger" v-if="item.bp.sbp.icon.warn === 'low'" src="../../assets/images/medical-record/danger-low.png"> -->
                    <span :class="`val ${item.bp.sbp.icon.arrow}`">{{ item.bp.sbp.indicator_value || '-' }}</span>
                    <img alt="img" class="icon arrow" v-if="item.bp.sbp.icon.arrow" :src="require(`../../assets/images/medical-record/arrow-${item.bp.sbp.icon.arrow}.png`)">
                    <!-- <img alt="img" class="icon arrow" v-if="item.bp.sbp.icon.arrow === 'up'" src="../../assets/images/medical-record/arrow-up.png">
                    <img alt="img" class="icon arrow" v-if="item.bp.sbp.icon.arrow === 'down'" src="../../assets/images/medical-record/arrow-down.png"> -->
                  </span>

                  <i class="gap" v-if="item.bp.sbp && item.bp.dbp">/</i>

                  <span class="dbp" v-if="item.bp.dbp">
                    <img alt="img" class="icon danger" v-if="item.bp.dbp.icon.warn" :src="require(`../../assets/images/medical-record/danger-${item.bp.dbp.icon.warn}.png`)">
                    <!-- <img alt="img" class="icon danger" v-if="item.bp.dbp.icon.warn === 'high'" src="../../assets/images/medical-record/danger-high.png">
                    <img alt="img" class="icon danger" v-if="item.bp.dbp.icon.warn === 'low'" src="../../assets/images/medical-record/danger-low.png"> -->
                    <span :class="`val ${item.bp.dbp.icon.arrow}`">{{ item.bp.dbp.indicator_value || '-' }}</span>
                    <img alt="img" class="icon arrow" v-if="item.bp.dbp.icon.arrow" :src="require(`../../assets/images/medical-record/arrow-${item.bp.dbp.icon.arrow}.png`)">
                    <!-- <img alt="img" class="icon arrow" v-if="item.bp.dbp.icon.arrow === 'up'" src="../../assets/images/medical-record/arrow-up.png">
                    <img alt="img" class="icon arrow" v-if="item.bp.dbp.icon.arrow === 'down'" src="../../assets/images/medical-record/arrow-down.png"> -->
                  </span>
                </div>
              </div>
              <div class="rt">
                <div class="date">{{ item.visit_at || '-' }}</div>
                <div class="reference">{{ item.indicator_unit || '-' }}</div>
              </div>
            </li>
          </ul>
          <div class="none" v-else>暂无数据</div>

        </van-collapse-item>
      </van-collapse>

      <!-- 现病史 -->
      <van-collapse v-model="active_histories" class="title-collapse-item-patient histories">
        <van-collapse-item name="histories">
          <template #title>
            <div class="item-header">
              <span>
                <img class="icon" src="../../assets/images/medical-record/disease-02.png" alt="icon">
                <span class="title">现病史</span>
              </span>
              <span class="tips">上次到院：{{ histories.symptom_time || '暂无' }}</span>
            </div>
          </template>
          <div class="histories-content">
            <ul>
              <li class="symptom">
                <div class="title">症状</div>
                <div :class="`value ${histories.symptom ? '' : 'none'}`">{{ histories.symptom || '暂无' }}</div>
              </li>
              <li class="drug">
                <div class="title">用药</div>
                <div :class="`value ${(histories.medications_without_drug_name && histories.medications_without_drug_name.length > 0) ? '' : 'none'}`">{{ (histories.medications_without_drug_name && histories.medications_without_drug_name.length > 0) ? histories.medications_without_drug_name : '暂无' }}</div>
              </li>
            </ul>
          </div>
        </van-collapse-item>
      </van-collapse>

      <!-- 既往史 -->
      <van-collapse v-model="active_past" class="title-collapse-item-patient histories past">
        <van-collapse-item name="past">
          <template #title>
            <div class="item-header">
              <span>
                <img class="icon" src="../../assets/images/medical-record/history-03.png" alt="icon">
                <span class="title">既往史</span>
              </span>
              <span class="tips">{{ histories.past_time ? `${histories.past_time}更新` : '暂无更新' }}</span>
            </div>
          </template>
          <div class="histories-content">
            <ul>
              <li>
                <div class="title">既往史</div>
                <div :class="`value ${histories.past ? '' : 'none'}`">{{ histories.past || '暂无' }}</div>
              </li>
            </ul>
          </div>
        </van-collapse-item>
      </van-collapse>

      <!-- 生命体征 -->
      <van-collapse v-model="active_signs" class="title-collapse-item-patient signs">
        <van-collapse-item name="signs">
          <template #title>
            <div class="item-header">
              <span>
                <img class="icon" src="../../assets/images/medical-record/diagnosis-04.png" alt="icon">
                <span class="title">生命体征</span>
              </span>
              <span class="tips">{{ signs.update_at ? `${signs.update_at}更新` : '暂无更新' }}</span>
            </div>
          </template>
          <div class="signs-content">
            <ul>
              <li class="bg">
                <div class="title">院内毛糖</div>
                <div class="value">{{ signs.dining_status || '' }}&nbsp;{{ signs.bg || '--' }}<span class="unit">mmol/L</span></div>
                <div class="value bg-measure-at">{{ signs.bg_measure_at ? `${signs.bg_measure_at.substr(0, 10)}更新` : '' }}</div>
              </li>
              <li class="three">
                <div>
                  <p class="title">身高</p>
                  <p class="value">{{ signs.height || '--' }}<span class="unit">cm</span></p>
                </div>
                <div>
                  <p class="title">体重</p>
                  <p class="value">{{ signs.weight || '--' }}<span class="unit">kg</span></p>
                </div>
                <div>
                  <p class="title">BMI</p>
                  <p class="value">{{ signs.bmi || '--' }}<span class="unit">kg/m²</span></p>
                </div>
              </li>
              <li class="three">
                <div>
                  <p class="title">血压</p>
                  <p class="value">{{ signs.bp || '--' }}<span class="unit">mmHg</span></p>
                </div>
                <div>
                  <p class="title">心率</p>
                  <p class="value">{{ signs.pulse || '--' }}<span class="unit">bpm</span></p>
                </div>
                <div>
                  <p class="title">内脏脂肪</p>
                  <p class="value">{{ signs.vat || '--' }}<span class="unit">cm²</span></p>
                </div>
              </li>
              <li class="four">
                <div>
                  <p class="title">头围</p>
                  <p class="value">{{ signs.head_circumference || '--' }}<span class="unit">cm</span></p>
                </div>
                <div>
                  <p class="title">颈围</p>
                  <p class="value">{{ signs.neck_circumference || '--' }}<span class="unit">cm</span></p>
                </div>
                <div>
                  <p class="title">腰围</p>
                  <p class="value">{{ signs.waistline || '--' }}<span class="unit">cm</span></p>
                </div>
                <div>
                  <p class="title">臀围</p>
                  <p class="value">{{ signs.hips || '--' }}<span class="unit">cm</span></p>
                </div>
              </li>
            </ul>
          </div>
        </van-collapse-item>
      </van-collapse>

      <!-- 核心指标 -->
      <van-collapse v-model="active_indicators" class="title-collapse-item-patient indicators">
        <van-collapse-item name="indicators">
          <template #title>
            <div class="item-header">
              <span>
                <img class="icon" src="../../assets/images/medical-record/star-05.png" alt="icon">
                <span class="title">核心指标</span>
              </span>
              <span class="tips">{{ indicators.visit_at ? `${indicators.visit_at}更新` : '暂无更新' }}</span>
            </div>
          </template>
          <div class="indicators-content">
            <div class="indicators-item">
              <div class="sub-title">
                <div class="lf">
                  <span class="tip"></span>
                  <span class="text">实验室检查</span>
                </div>
                <div class="rt">
                  <span class="btn" @click="getIndicatorsCharts(1)">数据趋势</span>
                </div>
              </div>
              <ul>
                <li class="attention-item-1" v-for="(item, index) in indicators.lab" :key="index">
                  <div class="lf">
                    <div class="name">{{ item.name }}</div>
                    <div class="value">
                      <span class="val">{{ item.current_value || '--' }}</span>
                      <span class="unit">{{ (item.current_value && item.unit) ? item.unit : '' }}</span>
                      <img src="../../assets/images/medical-record/arrow-up.png" alt="img" class="icon" v-if="item.arrow === 'up'">
                      <img src="../../assets/images/medical-record/arrow-down.png" alt="img" class="icon" v-if="item.arrow === 'down'">
                    </div>
                  </div>
                  <div class="rt">
                    <div class="date">{{ item.current_visit || '--' }}</div>
                    <div :class="`reference ${item.reference ? 'show' : 'hide'}`">{{ item.reference ? `参考值 ${item.reference}` : '--' }}</div>
                  </div>
                </li>
              </ul>
            </div>

            <div class="indicators-item">
              <div class="sub-title">
                <div class="lf">
                  <span class="tip"></span>
                  <span class="text">辅助检查</span>
                </div>
                <div class="rt">
                  <span class="btn" @click="getIndicatorsCharts(2)">数据趋势</span>
                </div>
              </div>
              <ul>
                <li class="attention-item-1" v-for="(item, index) in indicators.ass" :key="index">
                  <div class="lf">
                    <div class="name">{{ item.name }}</div>
                    <div class="value">
                      <span class="val">{{ item.current_value || '--' }}</span>
                      <span class="unit">{{ (item.current_value && item.unit) ? item.unit : '' }}</span>
                      <img src="../../assets/images/medical-record/arrow-up.png" alt="img" class="icon" v-if="item.arrow === 'up'">
                      <img src="../../assets/images/medical-record/arrow-down.png" alt="img" class="icon" v-if="item.arrow === 'down'">
                    </div>
                  </div>
                  <div class="rt">
                    <div class="date">{{ item.current_visit || '--' }}</div>
                    <div :class="`reference ${item.reference ? 'show' : 'hide'}`">{{ item.reference ? `参考值 ${item.reference}` : '--' }}</div>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </van-collapse-item>
      </van-collapse>

      <!-- 实验室检查记录 -->
      <van-collapse v-model="active_lab" class="title-collapse-item-patient record">
        <van-collapse-item name="lab">
          <template #title>
            <div class="item-header">
              <span>
                <img class="icon" src="../../assets/images/medical-record/check-06.png" alt="icon">
                <span class="title">实验室检查记录</span>
              </span>
              <span class="tips">{{ laboratory.visit_at ? `${laboratory.visit_at}更新` : '暂无更新' }}</span>
            </div>
          </template>
          <div class="record-content">
            <ul v-if="laboratory.lab_list.length > 0">
              <li v-for="(item, index) in laboratory.lab_list" :key="index" @click="getRecordDetail(item)">
                <span class="text">{{ item.report_name }}</span>
                <van-icon name="arrow" />
              </li>
            </ul>
            <div class="none" v-else>暂无数据</div>
          </div>
        </van-collapse-item>
      </van-collapse>

      <!-- 辅助检查记录 -->
      <van-collapse v-model="active_ass" class="title-collapse-item-patient record">
        <van-collapse-item name="ass">
          <template #title>
            <div class="item-header">
              <span>
                <img class="icon" src="../../assets/images/medical-record/medical-care-07.png" alt="icon">
                <span class="title">辅助检查记录</span>
              </span>
              <span class="tips">{{ laboratory.aux_at ? `${laboratory.aux_at}更新` : '暂无更新' }}</span>
            </div>
          </template>
          <div class="record-content">
            <ul v-if="laboratory.ass_list.length > 0">
              <li v-for="(item, index) in laboratory.ass_list" :key="index" @click="getRecordDetail(item)">
                <span class="text">{{ item.report_name }}</span>
                <van-icon name="arrow" />
              </li>
            </ul>
            <div class="none" v-else>暂无数据</div>
          </div>
        </van-collapse-item>
      </van-collapse>
    </div>
  </div>
</template>

<script>
import man_avatar from '../../assets/images/medical-record/man.png';
import woman_avatar from '../../assets/images/medical-record/woman.png';
import {
  getMedicalUserInfo,
  getMedicalAbnormalInfo,
  getMedicalHistories,
  getMedicalsigns,
  getMedicalIndicators,
  getMedicalLaboratory,
} from '@/api/common/report.js';

export default {
  data() {
    return {
      user_id: null,
      hosp_id: null,
      dept_id: null,
      userinfo: {
        photo: '',
        visit_level: '',
        name: '',
        age: '',
        sex: '',
        bmi: '',
        diabetes: {},
      },
      active_attention: ['abnormal'],
      active_histories: ['histories'],
      active_past: ['past'],
      active_signs: ['signs'],
      active_indicators: ['indicators'],
      active_lab: ['lab'],
      active_ass: ['ass'],
      data: {},
      abnormal: {},
      abnormal_list: [],
      abnormal_ass: {
        eye: {
          list: []
        },
        imt: {
          list: []
        }
      },
      abnormal_vital_lab_ass: [],
      histories: {},
      signs: {},
      indicators: {
        visit_at: '',
        lab: [],
        ass: [],
      },
      laboratory: {
        lab_list: [],
        ass_list: [],
      },
    };
  },
  computed: {
    patient_avatar() {
      let avatar = 'https://zz-med-test-pub.oss-cn-hangzhou.aliyuncs.com/base/avatar/user/default.png';
      if (this.userinfo && this.userinfo.photo) {
        avatar = this.userinfo.photo;
      } else if (this.userinfo.sex === 1) {
        avatar = man_avatar;
      } else if (this.userinfo.sex === 2) {
        avatar = woman_avatar;
      }
      return avatar;
    },
    patient_name() {
      let name = '';
      if (this.userinfo && this.userinfo.name) {
        name = this.userinfo.name;
      }
      return name;
    },
    patient_diabetes() {
      let diabetes = '';
      if (this.userinfo && this.userinfo.diabetes) {
        const { has_diabetes, diabetes_age } = this.userinfo.diabetes;
        if (has_diabetes) {
          diabetes = diabetes_age;
        }
      }
      return diabetes;
    },
    diabetes_type() {
      let type = '糖尿病';
      if (this.userinfo && this.userinfo.diabetes) {
        const { has_diabetes, diabetes_type } = this.userinfo.diabetes;
        if (has_diabetes) {
          if (diabetes_type === 1) {
            type = '1型糖尿病';
          } else if (diabetes_type === 2) {
            type = '2型糖尿病';
          } else if (diabetes_type === 3) {
            type = '妊娠期糖尿病';
          } else if (diabetes_type === 4) {
            type = '其他类型糖尿病';
          }
        }
      }
      return type;
    },
    patient_name_visit_level_img() {
      if (this.userinfo.visit_level) {
        return `V-23-${this.userinfo.visit_level}`;
      }
      return '';
    },
    patient_gender() {
      let sex = '未知';
      if (this.userinfo) {
        sex = this.userinfo.sex;
        if (sex === 0) {
          sex = '男';
        } else if (sex === 1) {
          sex = '女';
        }
      }
      return `${sex} |`;
    },
    patient_age() {
      let age = '--';
      if (this.userinfo && this.userinfo.age) {
        age = this.userinfo.age;
        age = `${age}岁`;
      }
      return `${age} |`;
    },
    patient_bmi() {
      let bmi = 'BMI: --';
      if (this.userinfo && this.userinfo.bmi) {
        bmi = this.userinfo.bmi * 1;
        bmi = bmi.toFixed(1);
        bmi = `BMI: ${bmi}`;
      }
      return bmi;
    },
  },
  created() {
    this.user_id = this.$route.query.user_id;
    this.hosp_id = this.$route.query.hosp_id;
    this.dept_id = this.$route.query.dept_id;

    localStorage.setItem('authorization', this.$route.query.token);

    this.getBaseInfo();
  },
  methods: {
    // 获取基本信息
    async getBaseInfo() {
      const res = await getMedicalUserInfo({
        user_id: this.user_id,
        hosp_id: this.hosp_id,
      });

      if (res.code === 200) {
        let { bmi, user, diabetes, record } = res.data || {};
        bmi = bmi ? bmi.toFixed(1) : '';
        const visit_level = record ? record.visit_level : '';

        this.userinfo = user;
        this.userinfo.bmi = bmi;
        this.userinfo.diabetes = diabetes;
        this.userinfo.visit_level = visit_level;
        document.title = user.barcode
        this.init();
      } else {
        this.$toast(res.msg);
      }
    },
    init() {
      this.getAbnormalInfo();
      this.getMedicalHistoriesData();
      this.getMedicalsignsData();
      this.getMedicalIndicatorsData();
      this.getMedicalLaboratoryData();
    },
    // 获取现病史数据
    async getMedicalHistoriesData() {
      const res = await getMedicalHistories({
        user_id: this.user_id,
        visit_level: this.userinfo.visit_level,
      });
      if (res.code === 200) {
        this.histories = res.data;
      } else {
        this.$toast(res.msg);
      }
    },
    // 获取生命体征数据
    async getMedicalsignsData() {
      const res = await getMedicalsigns({
        user_id: this.user_id,
        visit_level: this.userinfo.visit_level,
      });
      if (res.code === 200) {
        this.signs = res.data;
      } else {
        this.$toast(res.msg);
      }
    },
    // 获取核心指标数据
    async getMedicalIndicatorsData() {
      const res = await getMedicalIndicators({
        user_id: this.user_id,
        visit_level: this.userinfo.visit_level,
      });
      if (res.code === 200) {
        this.indicators = res.data;
        this.formateIndicatorsData(res.data);
      } else {
        this.$toast(res.msg);
      }
    },
    formateIndicatorsData(data) {
      this.indicators.visit_at = data.visit_at;
      const indicators = data.indicators;

      const initial_lab_list = [
        { name: '空腹血糖', id: '875001' },
        { name: '餐后2h血糖', id: '875004' },
        { name: '空腹胰岛素', id: '875006' },
        { name: '餐后2h胰岛素', id: '875009' },
        { name: '空腹C肽', id: '875011' },
        { name: '餐后2hC肽', id: '875014' },
        { name: '糖化血红蛋白', id: '876' },
        { name: '尿蛋白比肌酐', id: '901013' },
        { name: '低密度脂蛋白', id: '878042' },
      ];

      const initial_ass_list = [
        { name: '左侧 BA PWV', id: '929003' },
        { name: '右侧 BA PWV', id: '929004' },
        { name: '内脏脂肪', id: 'vat' },
        { name: '神经传导', id: '904033' },
        { name: '颈动脉超声', id: '925017' },
        { name: '眼底检查', id: '948' },
      ];

      const list_lab = [];
      initial_lab_list.forEach(item => {
        const id = item.id;
        const o = indicators[id] || item;
        o.name = item.name;
        if (o.reminder === 2) {
          o.arrow = 'up';
        } else if (o.reminder === 1) {
          o.arrow = 'down';
        }
        list_lab.push(o);
      })

      const list_ass = [];
      initial_ass_list.forEach(item => {
        const id = item.id;
        const o = indicators[id] || item;
        o.name = item.name;
        if (id === '929003') {
          o.reference = '≤1400';
          if (o.current_value > 1400) {
            o.arrow = 'up';
          }
        } else if (id === '929004') {
          o.reference = '≤1400';
          if (o.current_value > 1400) {
            o.arrow = 'up';
          }
        } else if (id === 'vat') {
          o.unit = 'cm²';
          o.reference = '≤100';
          if (o.current_value > 100) {
            o.arrow = 'up';
          }
        }
        list_ass.push(o);
      })

      this.indicators.lab = list_lab;
      this.indicators.ass = list_ass;
    },
    // 数据图表
    getIndicatorsCharts(type) {
      const base_url = process.env.VUE_APP_BASE_DOC_API;
      const { user_id, hosp_id, token } = this.$route.query;
      const url = `${base_url}api/medical/core_check?user_id=${user_id}&type=${type}&hosp_id=${hosp_id}&token=${token}`;
      location.href = url;
    },
    // 获取检查记录数据
    async getMedicalLaboratoryData() {
      const res = await getMedicalLaboratory({
        user_id: this.user_id,
        visit_level: this.userinfo.visit_level,
      });
      if (res.code === 200) {
        this.laboratory = res.data;
        this.laboratory.lab_list = [];
        this.laboratory.ass_list = [];
        this.formateLaboratoryData(res.data.examineResults);
      } else {
        this.$toast(res.msg);
      }
    },
    formateLaboratoryData(list) {
      if (list.length > 0) {
        const lab_list = [];
        const ass_list = [];
        list.forEach(item => {
          if (item.classify === 1) { // 实验室检查
            if (item.report_name.includes('肾功能')) {
              item.report_name = item.report_name.replace('肾功能', '尿蛋白');
            }
            lab_list.push(item);
          } else if (item.classify === 2) { // 辅助检查
            ass_list.push(item);
          }
        })
        this.laboratory.lab_list = lab_list;
        this.laboratory.ass_list = ass_list;
      }
    },
    getRecordDetail(item) {
      // 根据php之前跳转逻辑，这里对跳转path做特殊处理
      let path = 'labInfo'
      if ([6, 7, 8, 9, 30].includes(item.type)) {
        path = 'auxInfo'
      }

      const base_url = process.env.VUE_APP_BASE_DOC_API;
      const { user_id, hosp_id, token } = this.$route.query;
      const url = `${base_url}api/medical/${path}?user_id=${user_id}&type=${item.type}&hosp_id=${hosp_id}&token=${token}`;
      location.href = url;
    },
    // 获取异常提醒数据
    async getAbnormalInfo() {
      const res = await getMedicalAbnormalInfo({
        hosp_id: this.hosp_id,
        dept_id: this.dept_id,
        user_id: this.user_id,
      })
      if (res.code === 200) {
        this.abnormal = res.data;
        // this.formateAbnormalInfo(res.data);

        const data = this.formateAbnormalInfo(res.data);
        this.formatAbnormalInfoV2(data);
      } else {
        this.$toast(res.msg);
      }
    },
    // 格式化异常数据
    formateAbnormalInfo(data) {
      /*
      const { abnormal_info, abnormal_key_text } = abnormal;

      const keys1 = [
        'bp',
        'bmi',
        'subquestion_id_876',
        'subquestion_id_875001',
        'subquestion_id_878040',
        'subquestion_id_878038',
        'subquestion_id_878042',
        'epi_egfr',
        'uacr',
        'subquestion_id_878030',
        'subquestion_id_878031',
        'subquestion_id_878032',
        'subquestion_id_878033',
      ];
      const keys2 = [
        'yandi_ai_945',
        // 'subquestion_id_945',
        // 'yandi_ai_946',
        // 'subquestion_id_946',
        'subquestion_id_925004',
        // 'subquestion_id_925008',
        // 'subquestion_id_925012',
        // 'subquestion_id_925016',
      ];

      const keys_unit_reference = [
        'bp',
        'bmi',
        'subquestion_id_876',
        'subquestion_id_875001',
        'subquestion_id_878040',
        'subquestion_id_878038',
        'subquestion_id_878042',
        'epi_egfr',
        'uacr',
      ];

      const abnormal_list = [];
      const abnormal_ass = {};

      for (let key in abnormal_info) {
        if (keys1.includes(key)) {
          const o = abnormal_info[key][0];
          if (o) {
            o.name = abnormal_key_text[key];
            if (key === 'bp') {
              let { sbp, dbp } = abnormal_info[key][0] || { sbp: '', dbp: '' };
              if (sbp || dbp) {
                o.bp = `${sbp}/${dbp}`;
              } else {
                o.bp = '';
              }
              o.answer_name = o[key];
              o.unit = 'mmHg';
            } else if (key === 'bmi') {
              let v = o[key] ? o[key] * 1 : o[key]
              if (v) {
                o[key] = v.toFixed(1);
              }
              o.answer_name = o[key];
              o.unit = 'kg/m²';
            }

            if (key === 'epi_egfr') {
              o.answer_name = o.epi_egfr;
              o.unit = o.epi_egfr_unit;
            }
            if (key === 'uacr') {
              o.answer_name = o.val_901013;
              o.unit = o.unit_901013;
            }

            abnormal_list.push(o);
          }
        } else if (keys2.includes(key)) {
          const o = abnormal_info[key][0] || {};
          const k = key === 'yandi_ai_945' ? 'eye' : 'imt';
          const res = this.formateAssInfo(abnormal_info, o, k);
          abnormal_ass[k] = res;
        }
      }
      this.abnormal_list = abnormal_list;
      this.abnormal_ass = abnormal_ass;
      */

      const { abnormal_info, abnormal_group_code_text } = data
      const _abnormal_info = []
      const bp = {}
      let main = {}

      /*
      abnormal_info.forEach(item => {
        item.icon = this.setAbnormalIcon(item)
        if (item.indicator_group_code !== 'bp')  {
          _abnormal_info.push(item)
        } else if (item.indicator_group_code === 'bp') {
          if (item.indicator_code !== '') {
            bp[item.indicator_code] = item
          } else {
            main = item
          }
        }
      })
      */

      // 以下4项指标特殊对待，只记录低危和高危
      const glu_codes = ['bg', 'glu1', 'glu3', 'glu4']

      for (let i = 0; i < abnormal_info.length; i++) {
        const item = abnormal_info[i]
        item.icon = this.setAbnormalIcon(item)
        if (glu_codes.includes(item.indicator_group_code) && item.res_deflection < 3) {
          continue
        }
        if (item.indicator_group_code !== 'bp')  {
          _abnormal_info.push(item)
        } else if (item.indicator_group_code === 'bp' && item.indicator_code === '') {
          /*
          if (item.indicator_code !== '') {
            bp[item.indicator_code] = item
          } else {
            main = item
          }
          */
          main = item
          const group_indicator_list = item.group_indicator_list
          group_indicator_list.forEach(bp_item => {
            bp[bp_item.indicator_code] = bp_item
            bp[bp_item.indicator_code].icon = this.setAbnormalIcon(bp_item)
          })
        }
      }

      main.bp = bp
      main.indicator_unit = bp.dbp ? bp.dbp.indicator_unit : ''
      _abnormal_info.push(main)
      return {
        abnormal_info: _abnormal_info,
        abnormal_group_code_text
      }
    },
    formatAbnormalInfoV2(data) {
      const { abnormal_info, abnormal_group_code_text } = data
      const vital_keys = ['bg', 'bp', 'bmi', 'fat', 'af_flg', 'pulse', 'waist_circumference', 'waist_to_hip_ratio']
      const lab_keys = ['a1c', 'chol', 'egfrepi', 'glu1', 'glu3', 'glu4', 'lpa', 'tgb', 'uacr', 'uhdl', 'uldl', '']
      const ass_keys = ['diameter_change', 'labi', 'labi_pwv', 'rabi', 'rabi_pwv', 'left_eye_rechecked', 'right_eye_rechecked']
      const abnormal_vital = []
      const abnormal_lab = []
      const abnormal_ass = []

      abnormal_info.forEach(item => {
        const indicator_group_code = item.indicator_group_code
        item.name = abnormal_group_code_text[indicator_group_code]
        //item.icon = this.setAbnormalIcon(item)
        if (vital_keys.includes(indicator_group_code)) {
          abnormal_vital.push(item)
        } else if (lab_keys.includes(indicator_group_code)) {
          abnormal_lab.push(item)
        } else if (ass_keys.includes(indicator_group_code)) {
          abnormal_ass.push(item)
        }
      })

      const abnormal_vital_lab_ass = [...abnormal_vital, ...abnormal_lab, ...abnormal_ass]

      this.abnormal_vital = abnormal_vital
      this.abnormal_lab = abnormal_lab
      this.abnormal_ass = abnormal_ass
      this.abnormal_vital_lab_ass = abnormal_vital_lab_ass
    },
    setAbnormalIcon(item) {
      const { res_deflection, indicator_code } = item
      let cls = ''
      let arrow = ''
      let warn = ''

      if (res_deflection === 2 || res_deflection === 4) {
        arrow = 'up'
      }
      if (res_deflection === 1 || res_deflection === 3) {
        arrow = 'down'
      }
      if (res_deflection === 3) {
        warn = 'low'
      }
      if (res_deflection === 4) {
        warn = 'high'
      }

      const arr = ['af_flg', 'left_eye_rechecked', 'right_eye_rechecked']
      if (arr.includes(indicator_code)) {
        cls = 'text-red'
      }

      return { cls, arrow, warn }
    },
    formateAssInfo(abnormal_info, obj, key) {
      if (key === 'eye') {
        obj.name = '眼底检查';

        let left_ai = abnormal_info['yandi_ai_945'][0]
          ? abnormal_info['yandi_ai_945'][0]['left_eye_dignosis']
          : '';
        if (left_ai) {
          left_ai = `${left_ai}（AI阅片）`;
        }
        let left_qn = abnormal_info['subquestion_id_945'][0]
          ? abnormal_info['subquestion_id_945'][0]['answer_name']
          : '';
        if (left_qn) {
          left_qn = `${left_qn}（人工阅片）`;
        }
        const left_ai_visit_at = abnormal_info['yandi_ai_945'][0]
          ? abnormal_info['yandi_ai_945'][0]['visit_at']
          : '';
        const left_qn_visit_at = abnormal_info['subquestion_id_945'][0]
          ? abnormal_info['subquestion_id_945'][0]['visit_at']
          : '';

        let right_ai = abnormal_info['yandi_ai_946'][0]
          ? abnormal_info['yandi_ai_946'][0]['right_eye_dignosis']
          : '';
        if (right_ai) {
          right_ai = `${right_ai}（AI阅片）`;
        }
        let right_qn = abnormal_info['subquestion_id_946'][0]
          ? abnormal_info['subquestion_id_946'][0]['answer_name']
          : '';
        if (right_qn) {
          right_qn = `${right_qn}（人工阅片）`;
        }
        const right_ai_visit_at = abnormal_info['yandi_ai_946'][0]
          ? abnormal_info['yandi_ai_946'][0]['visit_at']
          : '';
        const right_qn_visit_at = abnormal_info['subquestion_id_946'][0]
          ? abnormal_info['subquestion_id_946'][0]['visit_at']
          : '';

        let left_value = left_ai + left_qn;
        let right_value = right_ai + right_qn;

        const left_visit_at = left_ai_visit_at || left_qn_visit_at;
        const right_visit_at = right_ai_visit_at || right_qn_visit_at;

        const list = [];
        if (left_value && left_visit_at) {
          list.push({ title: '左眼', value: left_value, visit_at: left_visit_at });
        }
        if (right_value && right_visit_at) {
          list.push({ title: '右眼', value: right_value, visit_at: right_visit_at });
        }

        obj.list = list;
      } else if (key === 'imt') {
        obj.name = '颈动脉超声';

        let left_imt_z_value = abnormal_info['subquestion_id_925012'][0]
          ? abnormal_info['subquestion_id_925012'][0]['answer_name']
          : '';
        let left_imt_n_value = abnormal_info['subquestion_id_925016'][0]
          ? abnormal_info['subquestion_id_925016'][0]['answer_name']
          : '';
        let right_imt_z_value = abnormal_info['subquestion_id_925004'][0]
          ? abnormal_info['subquestion_id_925004'][0]['answer_name']
          : '';
        let right_imt_n_value = abnormal_info['subquestion_id_925008'][0]
          ? abnormal_info['subquestion_id_925008'][0]['answer_name']
          : '';

        const left_imt_z_visit_at = abnormal_info['subquestion_id_925012'][0]
          ? abnormal_info['subquestion_id_925012'][0]['visit_at']
          : '';
        const left_imt_n_visit_at = abnormal_info['subquestion_id_925016'][0]
          ? abnormal_info['subquestion_id_925016'][0]['visit_at']
          : '';
        const right_imt_z_visit_at = abnormal_info['subquestion_id_925004'][0]
          ? abnormal_info['subquestion_id_925004'][0]['visit_at']
          : '';
        const right_imt_n_visit_at = abnormal_info['subquestion_id_925008'][0]
          ? abnormal_info['subquestion_id_925008'][0]['visit_at']
          : '';

        const list = [];
        if (left_imt_z_value && left_imt_z_visit_at) {
          list.push({ title: '左颈总斑块', value: left_imt_z_value, visit_at: left_imt_z_visit_at });
        }
        if (left_imt_n_value && left_imt_n_visit_at) {
          list.push({ title: '左颈内斑块', value: left_imt_n_value, visit_at: left_imt_n_visit_at });
        }
        if (right_imt_z_value && right_imt_z_visit_at) {
          list.push({
            title: '右颈总斑块',
            value: right_imt_z_value,
            visit_at: right_imt_z_visit_at,
          });
        }
        if (right_imt_n_value && right_imt_n_visit_at) {
          list.push({
            title: '右颈内斑块',
            value: right_imt_n_value,
            visit_at: right_imt_n_visit_at,
          });
        }

        obj.list = list;
      }
      return obj;
    },
    getArrowType(item) {
      if (item.name === 'eGFR-EPI') {
        const v = item.epi_egfr;
        if (v > 120) {
          return 'arrow-up';
        }
        return 'arrow-down';
      }
      return 'arrow-up';
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  text-align: initial;
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 21px 15px;
    .avatar {
      width: 60px;
      margin-right: 15px;
      img {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        vertical-align: top;
      }
    }
    .info {
      width: 100%;
      span {
        font-size: 14px;
        color: #5a6266;
      }
      .first {
        display: flex;
        align-items: center;
        span {
          display: inline-block;
        }
        .visit {
          margin: 0 6px;
          font-size: 15px;
          font-weight: bold;
          color: #f7830d;
          .n {
            color: #f7830d;
            font-size: 12px;
          }
        }
        .name {
          max-width: 120px;
          padding: 2px 0;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          font-size: 20px;
          color: #333;
          // font-weight: bold;
        }
        .visit-level {
          width: 15px;
          height: 15px;
          vertical-align: top;
          margin: 0 15px 0 6px;
        }
      }
      .second {
        display: flex;
        align-items: center;
        margin-top: 11px;
        span {
          display: inline-block;
        }
        .age,
        .bmi {
          margin-left: 6px;
        }
      }
    }
  }
  .content {
    ul {
      li {
        &.attention-item-1 {
          display: flex;
          justify-content: space-between;
          align-items: center;
          border-bottom: 1px solid #ebedf0;
          // height: 82px;
          min-height: 72px;
          padding: 5px 0;
          > div {
            // height: 60px;
            min-height: 60px;
            display: flex;
            flex-direction: column;
            justify-content: center;
          }
          .lf {
            color: #0a0a0a;
            .name {
              // font-weight: bold;
              font-size: 17px;
            }
            .value {
              display: flex;
              align-items: center;
              .sbp, .dbp {
                display: flex;
                align-items: center;
              }
              .gap {
                margin: 0 3px 0 0;
              }
              .val {
                // font-weight: bold;
                font-size: 16px;
                &.up, &.text-red {
                  color: #F53F3F;
                }
                &.down {
                  color: #3388FF;
                }
              }
              .unit {
                margin-top: 3px;
                margin-left: 3px;
                font-size: 12px;
                color: #878F99;
              }
              .icon {
                width: 20px;
                height: 20px;
                &.danger {
                  width: 18px;
                  height: 18px;
                  margin-right: 3px;
                }
                &.arrow {
                  width: 20px;
                  height: 20px;
                }
              }
            }
          }
          .rt {
            min-width: 90px;
            text-align: right;
            .date {
              font-size: 14px;
              color: #5a6266;
            }
            .reference {
              font-size: 14px;
              color: #878f99;
            }
          }
        }
        &.attention-item-2 {
          &:last-of-type {
            border: none;
          }
          padding: 15px 0;
          border-bottom: 1px solid #ebedf0;
          .lf {
            .name {
              // font-weight: bold;
              font-size: 17px;
              color: #0a0a0a;
            }
          }
          .list {
            > div {
              margin-top: 9px;
              .val {
                font-size: 15px;
                color: #0A0A0A;
              }
              > div {
                display: flex;
                justify-content: space-between;
                .date {
                  font-size: 14px;
                  color: #5A6266;
                }
                &.val {
                  margin-top: 2px;
                }
              }
            }
          }
        }
      }
    }
    .histories-content {
      ul li {
        display: flex;
        flex-direction: column;
        justify-content: center;
        min-height: 82px;
        padding: 15px 0;
        box-sizing: border-box;
        &.symptom {
          border-bottom: 1px solid #ebedf0;
        }
        .title {
          font-size: 17px;
          color: #0A0A0A;
        }
        .value {
          font-size: 15px;
          color: #0A0A0A;
          &.none {
            color: #878F99;
          }
        }
      }
    }
    .signs-content {
      padding: 9px 0;
      li {
        display: flex;
        align-items: center;
        padding: 12px 0;
        .title {
          font-size: 17px;
          color: #5A6266;
        }
        .value {
          font-size: 16px;
          color: #0A0A0A;
        }
        .unit {
          font-size: 14px;
          color: #0A0A0A;
        }
        &.bg {
          .title {
            width: 30%;
            text-align: right;
          }
          .value {
            width: 45%;
            text-align: right;
            font-size: 15px;
          }
          .bg-measure-at {
            width: 45%;
            text-align: right;
            font-size: 15px;
          }
        }
        &.three {
          justify-content: space-between;
          > div {
            width: 33.33%;
            text-align: center;
          }
        }
        &.four {
          justify-content: space-between;
          > div {
            width: 25%;
            text-align: center;
          }
        }
      }
    }
    .indicators-content {
      .indicators-item {
        &:last-of-type ul li:last-of-type {
          border: none;
        }
        .sub-title {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 15px 0;
          border-bottom: 1px solid #ebedf0;
          .lf {
            display: flex;
            align-items: center;
          }
          .tip {
            display: inline-block;
            width: 2px;
            height: 16px;
            margin-right: 6px;
            border-radius: 1px;
            background-color: #F7830D;
          }
          .text {
            height: 24px;
            line-height: 24px;
            font-size: 17px;
            color: #0A0A0A;
          }
          .btn {
            display: inline-block;
            text-align: center;
            width: 62px;
            height: 23px;
            line-height: 25px;
            border-radius: 6px;
            border: 1px solid #3388FF;
            font-size: 12px;
            color: #3388FF;
          }
        }
        ul li.attention-item-1 {
          // padding: 13px 0;
          padding: 5px 0;
          height: initial;
          > div {
            height: initial;
          }
          .lf .value .val {
            text-align: justify;
          }
          .rt {
            align-self: flex-start;
            .reference {
              margin-top: 6px;
            }
            .reference.hide {
              color: transparent;
            }
          }
        }
      }
    }
    .record-content {
      padding: 6px 0;
      ul li {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 9px 0;
        // border-bottom: 1px solid #ebedf0;
        &:last-of-type {
          border: none;
        }
        .text {
          font-size: 15px;
          color: #0A0A0A;
        }
        .van-icon-arrow {
          margin-top: 0;
        }
      }
      .none {
        padding: 9px 0;
        font-size: 15px;
      }
    }
  }
}
</style>
<style lang="scss">
.title-collapse-item-patient {
  &.van-hairline--top-bottom::after {
    display: none;
  }
  .van-collapse-item__title {
    align-items: center;
    border-radius: 0;
    background-color: #f8f8f8;
    .van-cell__title {
      height: 36px;
      display: flex;
      align-items: center;
      .icon {
        width: 20px;
        height: 20px;
        vertical-align: top;
      }
      .title {
        font-size: 18px;
        // font-weight: bold;
        color: #0a0a0a;
        margin-left: 6px;
      }
      .tips {
        font-size: 14px;
        color: #5a6266;
      }
    }
  }
  .van-icon-arrow {
    // margin-top: 6px;
    line-height: inherit;
  }
  .van-collapse-item__content {
    padding-top: 0;
    padding-bottom: 0;
  }
  &.attention {
    .van-collapse-item__title {
      background-color: #ffece8;
    }
    .none {
      padding: 15px 0;
      font-size: 15px;
    }
    .attention-item-1 .rt .reference {
      margin-top: 6px;
    }
  }
  &.histories, &.past, &.signs, &.indicators, &.record {
    .item-header {
      display: flex;
      justify-content: space-between;
      width: 100%;
    }
  }
}
</style>
