<template>
  <div class="main" ref="height">
    <p ref="p">登录码在您的短信中查看</p>
    <input ref="input" type="text" placeholder="请输入登录码" v-model="val" />
    <div @click="login">确认登录</div>
  </div>
</template>

<script>
  import md5 from 'js-md5'
  export default {
    data() {
      return {
        val: ''
      }
    },
    created() {
      this.$nextTick(() => {
        this.init()
      })
    },
    methods: {
      init() {
        // 判断安卓或苹果
          var userAgent = navigator.userAgent
          var isIOS = !!userAgent.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)
          console.log(screen.height)
          if (isIOS) {
            if (screen.height == 812 && screen.width == 375) {
              this.$refs.p.style.paddingTop = '52%'
              this.$refs.input.style.marginTop = '120px'
            }
          }
        this.$refs.height.style.height = window.innerHeight + 'px'
      },
      login() {
        if (this.val == '') {
          this.$toast('请输入登录码')
          return false
        }
        location.href = 'https://api.zz-med.com/v2/question?wx_user_id=' + this.val  + '&sign=' + md5(this.val + '' + this.val)
      }
    }
  }
</script>

<style lang="scss" scoped>
  .main{
    width: 100%;
    height: 100%;
    // display: flex;
    // justify-content:center;
    // align-items:center;
    background: url('https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/question-bg%402x.png') no-repeat;
    background-size: 100% 100%;
    p{
      width: 100%;
      line-height: 24px;
      text-align: center;
      font-size:16px;
      font-weight:400;
      padding-top: 43%;
      color: #fff;
    }
    input{
      width: 248px;
      height: 43px;
      line-height: 43px;
      border:2px solid #FF9420;
      border-radius:22px;
      margin: 0 auto;
      margin-top: 110px;
      font-size:18px;
      padding-left: 10px;
    }
    div{
      width: 258px;
      height: 50px;
      line-height: 50px;
      background: url('https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/question-anniu.png') no-repeat;
      background-size: cover;
      border-radius:22px;
      margin: 0 auto;
      margin-top: 80px;
      font-size:19px;
      font-weight:500;
      color:rgba(57,25,22,1);
    }
  }
</style>
