<template>
  <div class="main">
    <div class="qrcode" id="qrcode" ref="qrcode"></div>
  </div>
</template>

<script>
  import QRCode from "qrcodejs2";
    export default {
      data() {
        return {
          content: ''     //这是二维码内容
        }
      },
      created() {
        this.content = this.$route.query.url
        this.$nextTick(() => {

          this.qrcode();

        });
      },
      methods: {
        qrcode () {
          let qrcode = new QRCode("qrcode", {

            width: 350, // 二维码宽度，单位像素

            height: 350, // 二维码高度，单位像素

            text: this.content // 生成二维码的链接

          });
        }
      }
    }
</script>

<style scoped>
  .main{
    width: 100%;
    height: 100%;
  }
  .qrcode{
    width: 350px;
    height: 350px;
    margin: 12.5px;
  }
</style>
