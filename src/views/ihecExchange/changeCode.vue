<template>
  <div class="content_wrapper">
    <div class="header_wrapper">
      <img src="./images/word1.png" class="title" alt="">
    </div>
    <div class="main_wrapper">
      <div class="title">
        请输入兑换码
      </div>
      <div class="sub_title">
        请输入血压计附赠服务卡上的兑换码
      </div>
      <input type="text" class="input" placeholder="请输入兑换码" v-model="redeemCode">
      <div class="line"></div>
      <div class="btn" @click="getExchange">立即兑换</div>
    </div>
    <div class="footer_wrapper">
      <div class="title">
        用户权益
      </div>
      <div class="clause">
        <div class="name">
          尊敬的客户，您好!
        </div>
        <div class="top">
          在兑换本次血压服务包权益前，请您认真阅读以下几条兑换细则：
        </div>
      </div>
      <div class="illustrate">
        <div class="info">
          <div class="infoTitle">
            <img src="./images/headerImg.png" alt="">
            适用人群
          </div>
          <div class="text">该项服务仅适用于高血压人群，兑换成功后，兑换码即失效，请使用人本人兑换，亲属请勿代为兑换；</div>

        </div>
        <div class="info">
          <div class="infoTitle">
            <img src="./images/duiHuan.png" alt="">
            兑换渠道
          </div>
          <div class="text">权益兑换码为官方统一赠送礼品，获得权益兑换卡片的用户可在ihec智慧血压公众号内验证使用，兑换血压服务包；</div>

        </div>
        <div class="info">
          <div class="infoTitle">
            <img src="./images/fuWu.png" alt="">
            服务内容
          </div>
          <div class="text">本次服务包含欧姆龙控压营，健康助手小程序，健康报告，及一次专家报告解读四项血压管理服务，您的血压问题，我们有办法解决~</div>
        </div>
      </div>
      <div class="tip_img">
        <img src="./images/<EMAIL>">
        <img src="./images/<EMAIL>">
      </div>
    </div>
  </div>
</template>
<script>
  import { getRedeemCode, getUserOpenid } from '@/api/ihecExchange'
  import { Toast } from 'vant'
  import wx from 'weixin-js-sdk'

  export default {
    data() {
      return {
        cell: '',
        code: '',
        openid: '',
        redeemCode: ''
      }
    },
    created() {
      let that = this
      this.code = this.$route.query.code
      getUserOpenid({ code: this.code }).then(function (res) {
        if (res.status == 0) {
          that.openid = res.data.openid
        }
      })
    },
    methods: {
      getExchange() {
        const that = this
        if (that.redeemCode == '' || !that.redeemCode) {
          Toast('请输入兑换码！')
          return false
        }
        if (that.openid == '' || !that.openid) {
          Toast('openid为空！')
          return false
        }
        this.noDoubleTap(() => {
          getRedeemCode({
            'redeem_code': that.redeemCode,
            'openid': that.openid
          }).then(res => {
            if (res.status == 0) {
              Toast('兑换成功')
              setTimeout(() => {
                this.$router.push({
                  name: 'ihec.new.exchage.success',
                  query: {}
                })
              }, 1000)
            }
          })
          setTimeout(() => {
            that.$btnDisabled = true
          }, 1000)
        }, false)
      }
    }
  }
</script>

<style lang="scss" scoped>
  .content_wrapper {
    width: 100%;
    overflow: hidden;
    background: url("./images/indexBg.png");
    background-size: cover;
    .header_wrapper {
      padding-top: 23px;
      text-align: center;
      width: 100%;
      .title {
        font-size: 21px;
        line-height: 30px;
        color: #575757;
        letter-spacing: 0;
        margin-bottom: 12px;
        width: 226px;
        height: 28px;
      }
    }
    .main_wrapper {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 357px;
      height: 185px;
      background: #fff;
      margin: 0 auto;
      border: 3px solid #3AD4F8;
      box-shadow: 0 0 3px 0 rgba(0, 0, 0, 0.30);
      border-radius: 15px;
      padding-top: 20px;
      .title {
        font-size: 21px;
        line-height: 30px;
        color: #575757;
        letter-spacing: 0;
        margin-bottom: 10px;
      }
      .sub_title {
        font-size: 16px;
        line-height: 23px;
        color: #A8A8A8;
        letter-spacing: 0;
      }
      .input {
        height: 35px;
        line-height: 35px;
        text-indent: 10px;
        width: 270px;
        font-size: 20px;
        color: #575757;
      }
      .getCodeBox {
        display: flex;
        align-items: center;
        .input {
          height: 35px;
          line-height: 35px;
          text-indent: 10px;
          width: 170px;
          font-size: 20px;
          color: #575757;
        }
        button {
          height: 30px;
          width: 100px;
          background: linear-gradient(90deg, #46BBFF 0%, #007BFF 100%);
          font-size: 12px;
          border: none;
          color: #ffffff;
          border-radius: 6px;
        }
      }
      .line {
        height: 1px;
        width: 270px;
        background: linear-gradient(90deg, #46BBFF 0%, #007BFF 100%);
        margin-bottom: 16px;
      }
      .btn {
        margin: 0 auto;
        background: linear-gradient(360deg, #f76e1e 0%, #fbd760 100%);
        border-radius: 6px;
        color: #fff;
        width: 307px;
        height: 50px;
        font-size: 17px;
        line-height: 50px;
      }
    }
    .footer_wrapper {
      width: 357px;
      background: #fff;
      margin: 0 auto;
      margin-top: 24px;
      margin-bottom: 12px;
      border: 3px solid #3AD4F8;
      box-shadow: 0 0 3px 0 rgba(0, 0, 0, 0.30);
      border-radius: 15px;
      position: relative;
      .title {
        font-size: 20px;
        color: #fff;
        width: 234px;
        height: 38px;
        line-height: 38px;
        background: url("./images/titleBg.png");
        background-size: cover;
        position: absolute;
        top: -7px;
        left: 62px;
      }
      .clause {
        width: 335px;
        margin: 46px 7px 10px 14px;
        .name {
          text-align: left;
          font-size: 17px;
          color: #404040;
          font-weight: 500;
        }
        .top {
          font-size: 15px;
          color: #404040;
          text-align: left;
          line-height: 24px;
          padding-top: 10px;
        }
      }
      .illustrate {
        width: 328px;
        margin: 0 auto;
        .info {
          margin: 21px 0;
          display: flex;
          flex-direction: column;
          .infoTitle {
            display: flex;
            align-items: center;
            font-size: 17px;
            color: #0F8DFD;
            font-weight: bold;
            img {
              width: 23px;
              height: 23px;
              margin-right: 5px;
            }
          }
          .text {
            margin-top: 8px;
            font-size: 15px;
            color: #919191;
            text-align: left;
            line-height: 22px;
          }
        }
      }
      .tip_img {
        padding: 15px 0;
        display: flex;
        align-items: center;
        justify-content: center;
        img:first-child {
          width: 45px;
          height: 92px;
        }
        img:nth-child(2) {
          width: 238px;
          margin-left: 10px;
          height: 75px;
        }
      }
    }
  }
</style>
