<template>
  <div class="content">
    <img src="./images/trueIcon.png" alt="" class="successIcon">
    <div class="successWord">兑换成功</div>
    <div class="p1">尊敬的用户,恭喜您兑换成功！</div>
    <div class="p2">请扫描下方二维码添加客服小海</div>
    <div class="p2">开启您的控压之旅吧</div>
    <img src="https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/wechat/sugarControllerVIP/V1.0.0/xiaohai_qrcode.png" alt="" class="codeImg">
  </div>
</template>
<script>

  export default {
    data() {
      return {}
    },
    mounted() {

    },
    methods: {}
  }
</script>

<style lang="scss" scoped>
  .content{
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .successIcon{
      width: 158px;
      height: 127px;
      margin-top: 26px;
      margin-bottom: 9px;
    }
    .successWord{
      color: #333333;
      font-size: 20px;
      font-weight: 500;
      text-align: center;
    }
    .p1{
      width: 292px;
      line-height: 30px;
      color: #404040;
      text-align: left;
      font-size: 20px;
      margin-top: 20px;
      text-align: center;
      padding-bottom: 10px;
    }
    .codeImg{
      width: 200px;
      height: 200px;
      padding-top: 20px;
    }
    .p2{
      margin-top: 10px;
      font-size: 18px;
      color: #666666;
    }
  }
</style>
