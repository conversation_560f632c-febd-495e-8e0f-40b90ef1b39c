<template>
  <div class="content_wrapper">
    <div class="header_wrapper">
      <img src="./images/word1.png" class="title" alt="">
    </div>
    <div class="main_wrapper">
      <div class="title">
        请输入手机号
      </div>
      <input type="text" class="input" placeholder="请输入手机号" @click="clickInput" v-model="cell">
      <div class="line"></div>
      <div class="getCodeBox">
        <input type="text" class="input" placeholder="请输入验证码" @click="clickInput" v-model="code">
        <button v-show="show" @click="getCode()">获取验证码</button>
        <button v-show="!show">{{count}}s后重新获取</button>
      </div>
      <div class="line"></div>
      <div class="radioBox">
        <input type="checkbox" v-model="agreeBtn">
        <div><a href="https://patient-h5.zz-med-test.com/static/h5/iHECUserProtocol2.html">同意《14天血压服务知情同意书》</a></div>
      </div>
      <div class="btn" @click="getExchange">立即兑换</div>
    </div>
    <div class="footer_wrapper">
      <div class="title">
        用户权益
      </div>
      <div class="clause">
        <div class="name">
          尊敬的客户，您好!
        </div>
        <div class="top">
          在兑换本次血压服务包权益前，请您认真阅读以下几条兑换细则：
        </div>
      </div>
      <div class="illustrate">
        <div class="info">
          <div class="infoTitle">
            <img src="./images/headerImg.png" alt="">
            适用人群
          </div>
          <div class="text">该项服务仅适用于高血压人群，兑换成功后，兑换码即失效，请使用人本人兑换，亲属请勿代为兑换；</div>

        </div>
        <div class="info">
          <div class="infoTitle">
            <img src="./images/duiHuan.png" alt="">
            兑换渠道
          </div>
          <div class="text">权益兑换码为官方统一赠送礼品，获得权益兑换卡片的用户可在ihec智慧血压公众号内验证使用，兑换血压服务包；</div>

        </div>
        <div class="info">
          <div class="infoTitle">
            <img src="./images/fuWu.png" alt="">
            服务内容
          </div>
          <div class="text">本次服务包含欧姆龙控压营，健康助手小程序，健康报告，及一次专家报告解读四项血压管理服务，您的血压问题，我们有办法解决~</div>
        </div>
      </div>
      <div class="tip_img">
        <img src="./images/<EMAIL>">
        <img src="./images/<EMAIL>">
      </div>
    </div>
  </div>
</template>
<script>
  import {getSendCode, saveQrcodeUser, getQrcodeYouUrl} from '@/api/ihecExchange'
  import {Toast} from 'vant'
  import wx from 'weixin-js-sdk'

  export default {
    data() {
      return {
        cell: '',
        code: '',
        channel_qrcode_id: '',
        openid: '',
        agreeBtn: '',
        show: true,
        count: '',
        url: '',
        timer: null
      }
    },
    mounted() {
    },
    created() {
      this.channel_qrcode_id = this.$route.query.channel_qrcode_id
      this.openid = this.$route.query.openid
      this.code = this.$route.query.code
      this.init()
    },
    methods: {
      init() {
        let that = this
        getQrcodeYouUrl({channel_qrcode_id: this.channel_qrcode_id}).then(function (res) {
          if (res.status == 0) {
            that.url = res.data.youzan_url
          }

        })
      },
      clickInput(e) {
        e.stopPropagation()
        e.target.focus()
      },
      getCode() {
        const that = this
        if (that.cell == '' || !that.cell) {
          Toast('请输入手机号码！')
          return false
        }
        if (!(/^1[34578]\d{9}$/.test(that.cell))) {
          Toast('手机号码格式错误！')
          return false
        }
        const TIME_COUNT = 60
        if (!this.timer) {
          this.count = TIME_COUNT
          this.show = false
          this.timer = setInterval(() => {
            if (this.count > 0 && this.count <= TIME_COUNT) {
              this.count--
            } else {
              this.show = true
              clearInterval(this.timer)
              this.timer = null
            }
          }, 1000)
        }
        getSendCode({
          'cell': that.cell
        }).then(res => {
          if (res.status == 0) {
            Toast('发送成功！')
          }
        })
      },
      getExchange() {
        const that = this
        if (that.cell == '' || !that.cell) {
          Toast('请输入手机号码！')
          return false
        }
        if (!(/^1[34578]\d{9}$/.test(that.cell))) {
          Toast('手机号码格式错误！')
          return false
        }
        if (that.code == '' || !that.code) {
          Toast('请输入验证码！')
          return false
        }
        if (this.agreeBtn != true) {
          Toast('请勾选知情同意书！')
          return false
        }
        this.noDoubleTap(() => {
          saveQrcodeUser({
            'openid': that.openid,
            'channel_qrcode_id': that.channel_qrcode_id,
            'cell': that.cell,
            'verify_code': that.code
          }).then(res => {
            if (res.status == 0) {
              Toast('兑换成功')
              setTimeout(() => {
                window.location.href = that.url
              }, 1000)
            }
          })
          setTimeout(() => {
            that.$btnDisabled = true
          }, 1000)
        }, false)
      }
    }
  }
</script>

<style lang="scss" scoped>
  .content_wrapper {
    width: 100%;
    overflow: hidden;
    background: url("./images/indexBg.png");
    background-size: cover;
    .header_wrapper {
      padding-top: 23px;
      text-align: center;
      width: 100%;
      .title {
        font-size: 21px;
        line-height: 30px;
        color: #575757;
        letter-spacing: 0;
        margin-bottom: 12px;
        width: 226px;
        height: 28px;
      }
    }
    .main_wrapper {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 357px;
      height: 255px;
      background: #fff;
      margin: 0 auto;
      border: 3px solid #3AD4F8;
      box-shadow: 0 0 3px 0 rgba(0, 0, 0, 0.30);
      border-radius: 15px;
      padding-top: 20px;
      .title {
        font-size: 21px;
        line-height: 30px;
        color: #575757;
        letter-spacing: 0;
        margin-bottom: 12px;
      }
      .input {
        height: 35px;
        line-height: 35px;
        text-indent: 10px;
        width: 270px;
        font-size: 20px;
        color: #575757;
      }
      .getCodeBox {
        display: flex;
        align-items: center;
        .input {
          height: 35px;
          line-height: 35px;
          text-indent: 10px;
          width: 160px;
          font-size: 20px;
          color: #575757;
        }
        button {
          height: 30px;
          width: 110px;
          background: linear-gradient(90deg, #46BBFF 0%, #007BFF 100%);
          font-size: 12px;
          border: none;
          color: #ffffff;
          border-radius: 6px;
        }
      }
      .line {
        height: 1px;
        width: 270px;
        background: linear-gradient(90deg, #46BBFF 0%, #007BFF 100%);
        margin-bottom: 16px;
      }
      .radioBox {
        display: flex;
        align-items: center;
        margin-bottom: 25px;
        input {
          width: 20px;
          height: 20px;
          background: #007BFF;
        }
        input[type="checkbox"] {
          width: 20px;
          height: 20px;
          display: inline-block;
          text-align: center;
          vertical-align: middle;
          line-height: 18px;
          margin-right: 10px;
          position: relative;
        }

        input[type="checkbox"]::before {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          background: #fff;
          width: 100%;
          height: 100%;
          border: 1px solid #d9d9d9;
          border-radius: 4px;
        }

        input[type="checkbox"]:checked::before {
          content: "\2713";
          background-color: #1890FF;
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          border: 1px solid #7D7D7D;
          border-radius: 4px;
          color: #fff;
          font-size: 20px;
          font-weight: bold;
        }
        div {
          display: flex;
          align-items: center;
          a {
            font-size: 15px;
            color: #1890FF;
            margin-left: 5px;
          }
        }
      }
      .btn {
        margin: 0 auto;
        background: linear-gradient(360deg, #f76e1e 0%, #fbd760 100%);
        border-radius: 6px;
        color: #fff;
        width: 307px;
        height: 50px;
        font-size: 17px;
        line-height: 50px;
      }
    }
    .footer_wrapper {
      width: 357px;
      background: #fff;
      margin: 0 auto;
      margin-top: 24px;
      margin-bottom: 12px;
      border: 3px solid #3AD4F8;
      box-shadow: 0 0 3px 0 rgba(0, 0, 0, 0.30);
      border-radius: 15px;
      position: relative;
      .title {
        font-size: 20px;
        color: #fff;
        width: 234px;
        height: 38px;
        line-height: 38px;
        background: url("./images/titleBg.png");
        background-size: cover;
        position: absolute;
        top: -7px;
        left: 62px;
      }
      .clause {
        width: 335px;
        margin: 46px 7px 10px 14px;
        .name {
          text-align: left;
          font-size: 17px;
          color: #404040;
          font-weight: 500;
        }
        .top {
          font-size: 15px;
          color: #404040;
          text-align: left;
          line-height: 24px;
          padding-top: 10px;
        }
      }
      .illustrate {
        width: 328px;
        margin: 0 auto;
        .info {
          margin: 21px 0;
          display: flex;
          flex-direction: column;
          .infoTitle {
            display: flex;
            align-items: center;
            font-size: 17px;
            color: #0F8DFD;
            font-weight: bold;
            img {
              width: 23px;
              height: 23px;
              margin-right: 5px;
            }
          }
          .text {
            margin-top: 8px;
            font-size: 15px;
            color: #919191;
            text-align: left;
            line-height: 22px;
          }
        }
      }
      .tip_img {
        padding: 15px 0;
        display: flex;
        align-items: center;
        justify-content: center;
        img:first-child {
          width: 45px;
          height: 92px;
        }
        img:nth-child(2) {
          width: 238px;
          margin-left: 10px;
          height: 75px;
        }
      }
    }
  }
</style>
