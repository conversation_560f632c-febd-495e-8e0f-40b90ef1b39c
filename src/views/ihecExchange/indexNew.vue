<template>
  <div class="content_wrapper">
    <div class="header_wrapper"></div>
    <div class="main_wrapper">
      <div class="title">
        请输入手机号
      </div>
      <div class="inputBox">
        <img src="./images/shouji.png" alt="">
        <div class="line"></div>
        <input type="text" class="input" placeholder="请输入手机号" @click="clickInput" v-model="cell">
      </div>
      <div class="getCodeBox">
        <img src="./images/yanzhengma.png" alt="">
        <div class="line"></div>
        <input type="text" class="input1" placeholder="请输入验证码" @click="clickInput" v-model="code">
        <div class="line1"></div>
        <button class="button1" v-show="show" @click="getCode()">获取验证码</button>
        <button class="button2" v-show="!show">{{count}}s后重新获取</button>
      </div>
      <div class="radioBox">
        {{tips}}
      </div>
      <div class="btn" @click="getExchange">进入商城</div>
    </div>
  </div>
</template>
<script>
  import {getSendCode, saveQrcodeUser, getQrcodeYouUrl} from '@/api/ihecExchange'
  import {Toast} from 'vant'
  import wx from 'weixin-js-sdk'

  export default {
    data() {
      return {
        cell: '',
        code: '',
        channel_qrcode_id: '',
        openid: '',
        agreeBtn: '',
        show: true,
        count: '',
        url: '',
        timer: null,
        tips: ''
      }
    },
    mounted() {
    },
    created() {
      this.channel_qrcode_id = this.$route.query.channel_qrcode_id
      this.openid = this.$route.query.openid
      this.init()
    },
    methods: {
      init() {
        let that = this
        getQrcodeYouUrl({channel_qrcode_id: this.channel_qrcode_id}).then(function (res) {
          if (res.status == 0) {
            that.url = res.data.youzan_url
          }

        })
      },
      clickInput(e) {
        e.stopPropagation()
        e.target.focus()
      },
      getCode() {
        const that = this
        if (that.cell == '' || !that.cell) {
          this.tips = '请输入正确的手机号'
          return false
        }
        if (!(/^1[34578]\d{9}$/.test(that.cell))) {
          this.tips = '请输入正确的手机号'
          return false
        }
        const TIME_COUNT = 60
        if (!this.timer) {
          this.count = TIME_COUNT
          this.show = false
          this.timer = setInterval(() => {
            if (this.count > 0 && this.count <= TIME_COUNT) {
              this.count--
            } else {
              this.show = true
              clearInterval(this.timer)
              this.timer = null
            }
          }, 1000)
        }
        getSendCode({
          'cell': that.cell
        }).then(res => {
          if (res.status == 0) {
            this.tips = '发送成功！'
            // Toast('发送成功！')
          }
        })
      },
      getExchange() {
        const that = this
        if (that.cell == '' || !that.cell) {
          this.tips = '请输入正确的手机号'
          // Toast('请输入手机号码！')
          return false
        }
        if (!(/^1[34578]\d{9}$/.test(that.cell))) {
          this.tips = '请输入正确的手机号'
          // Toast('手机号码格式错误！')
          return false
        }
        if (that.code == '' || !that.code) {
          this.tips = '请输入验证码！'
          // Toast('请输入验证码！')
          return false
        }
        // if (this.agreeBtn != true) {
        //   Toast('请勾选知情同意书！')
        //   return false
        // }
        this.noDoubleTap(() => {
          saveQrcodeUser({
            'openid': that.openid,
            'channel_qrcode_id': that.channel_qrcode_id,
            'cell': that.cell,
            'verify_code': that.code
          }).then(res => {
            if (res.status == 0) {
              this.tips = '兑换成功'
              // Toast('兑换成功')
              setTimeout(() => {
                window.location.href = that.url
              }, 1000)
            }
          })
          setTimeout(() => {
            that.$btnDisabled = true
          }, 1000)
        }, false)
      }
    }
  }
</script>

<style lang="scss" scoped>
  .content_wrapper {
    width: 100%;
    .header_wrapper {
      width: 100%;
      height: 218px;
      background: url("images/beijing.png");
      background-size: cover;
    }
    .main_wrapper {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 345px;
      height: 322px;
      background: #fff;
      margin: 0 auto;
      box-shadow: 0px 4px 10px 0px rgba(37, 52, 224, 0.15);
      border-radius: 6px;
      padding-top: 20px;
      margin-top: -90px;
      .title {
        font-size: 20px;
        line-height: 28px;
        color: #474747;
        letter-spacing: 0;
        margin-bottom: 20px;
      }
      .line {
        width: 1px;
        height: 16px;
        background: #d8d8d8;
        margin-right: 10px;
      }
      .line1 {
        width: 1px;
        height: 16px;
        background: #8A97A5;
        margin-right: 10px;
      }
      .inputBox {
        height: 50px;
        line-height: 50px;
        width: 300px;
        font-size: 20px;
        color: #333333;
        border: 1px #DDDDDD solid;
        border-radius: 25px;
        margin-bottom: 16px;
        display: flex;
        align-items: center;
        img {
          width: 22px;
          height: 22px;
          margin: 0 10px 0 23px;
        }
        .input {
          height: 50px;
          width: 233px;
          font-size: 20px;
          color: #333333;
          border-radius: 0 25px 25px 0;
        }
      }
      .getCodeBox {
        display: flex;
        align-items: center;
        height: 50px;
        line-height: 50px;
        width: 300px;
        font-size: 20px;
        color: #333333;
        border: 1px #DDDDDD solid;
        border-radius: 25px;
        img {
          width: 22px;
          height: 22px;
          margin: 0 10px 0 23px;
        }
        .input1 {
          height: 50px;
          line-height: 50px;
          width: 140px;
          font-size: 20px;
          color: #333333;
        }
        .button1 {
          width: 60px;
          height: 50px;
          font-size: 12px;
          border: none;
          background: #ffffff;
          margin: 0;
          padding: 0;
          color: #8A97A5;
          border-radius: 0 25px 25px 0;
        }
        .button2 {
          width: 90px;
          height: 50px;
          font-size: 12px;
          border: none;
          background: #ffffff;
          margin: 0;
          padding: 0;
          color: #8A97A5;
          border-radius: 0 25px 25px 0;
        }
      }
      .radioBox {
        text-align: center;
        margin-bottom: 31px;
        margin-top: 12px;
        color: #FF5A58;
        font-size: 16px;
        line-height: 22px;
        height: 22px;
        width: 100%;
      }
      .btn {
        margin: 0 auto;
        box-shadow: 0px 2px 10px 0px rgba(87, 143, 255, 0.6);
        background: #4D7DEF;
        border-radius: 25px;
        color: #fff;
        width: 300px;
        height: 50px;
        font-size: 18px;
        line-height: 50px;
        font-weight: 600;
      }
    }
  }
</style>
