<!--
 * @Descripttion: MMC-数据中心
 * @version:
 * @Author: guxiang
 * @Date: 2022-01-18 15:57:52
 * @LastEditors: guxiang
 * @LastEditTime: 2022-01-18 15:57:52
-->
<template>
  <div class="wrapper">
    <div class="centerNumber">
      <p class="title">
        <img src="@/assets/images/operationReport/person.png" alt="" />
        <span>中心患者数量</span>
      </p>
      <van-divider />
      <div class="infoFlex">
        <div>
          <p>我的质控患者（人）</p>
          <span>{{ mmcData.user_data.quality_count }}</span>
        </div>
        <div>
          <!-- <p>患者总数（人）</p> -->
          <p>{{ mmcData.user_data.mmc_patient_total.name }}</p>
          <span>{{ mmcData.user_data.mmc_patient_total.num }}</span>
        </div>
        <div>
          <p>{{ mmcData.user_data.month_incr.name }}</p>
          <span>{{ mmcData.user_data.month_incr.num }}</span>
        </div>
        <div>
          <p>{{ mmcData.user_data.mmc_24_warning.name }}</p>
          <span>{{ mmcData.user_data.mmc_24_warning.num }}</span>
        </div>
      </div>
    </div>
    <div class="tableCard">
      <p class="title">
        <span>
          <img src="@/assets/images/dataCenter/followUp.png" alt="" />
          <span>管理患者的随访率</span>
        </span>
        <span class="extend" @click="change('visit')">
          <span>{{ visitExtend ? "收起" : "展开" }}</span>
          <van-icon v-if="visitExtend" name="arrow-up" />
          <van-icon v-if="!visitExtend" name="arrow-down" />
        </span>
      </p>
      <div v-if="visitExtend" class="table-outer">
        <table class="table">
          <thead>
            <tr>
              <th>访视阶段</th>
              <th>中心随访率</th>
              <th>我的随访率</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, index) in mmcData.visit_data" :key="index">
              <td class="td">{{ item.visit_level }}</td>
              <td class="td">{{ item.percent }}</td>
              <td class="td">{{ item.my && item.my.percent }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <div class="tableCard">
      <p class="title">
        <span>
          <img src="@/assets/images/dataCenter/saccharify.png" alt="" />
          <span>管理患者的糖化达标率</span>
        </span>
        <span class="extend" @click="change('bg')">
          <span>{{ bgExtend ? "收起" : "展开" }}</span>
          <van-icon v-if="bgExtend" name="arrow-up" />
          <van-icon v-if="!bgExtend" name="arrow-down" />
        </span>
      </p>
      <div v-if="bgExtend" class="table-outer">
        <table class="table">
          <thead>
            <tr>
              <th>访视阶段</th>
              <th>中心糖化达标率</th>
              <th>我的糖化达标率</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, index) in mmcData.bg" :key="index">
              <td class="td">{{ item.level }}</td>
              <td class="td">{{ item.rate }}</td>
              <td class="td">{{ item.my.rate }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <div class="tableCard">
      <p class="title">
        <span>
          <img src="@/assets/images/dataCenter/bp.png" alt="" />
          <span>管理患者的血压达标率</span>
        </span>
        <span class="extend" @click="change('bp')">
          <span>{{ bpExtend ? "收起" : "展开" }}</span>
          <van-icon v-if="bpExtend" name="arrow-up" />
          <van-icon v-if="!bpExtend" name="arrow-down" />
        </span>
      </p>
      <div v-if="bpExtend" class="table-outer">
        <table class="table">
          <thead>
            <tr>
              <th>访视阶段</th>
              <th>中心血压达标率</th>
              <th>我的血压达标率</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, index) in mmcData.bp" :key="index">
              <td class="td">{{ item.level }}</td>
              <td class="td">{{ item.rate }}</td>
              <td class="td">{{ item.my.rate }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <div class="tableCard">
      <p class="title">
        <span>
          <img src="@/assets/images/dataCenter/ldl.png" alt="" />
          <span>管理患者的血脂达标率</span>
        </span>
        <span class="extend" @click="change('ldl')">
          <span>{{ ldlExtend ? "收起" : "展开" }}</span>
          <van-icon v-if="ldlExtend" name="arrow-up" />
          <van-icon v-if="!ldlExtend" name="arrow-down" />
        </span>
      </p>
      <div v-if="ldlExtend" class="table-outer">
        <table class="table">
          <thead>
            <tr>
              <th>访视阶段</th>
              <th>中心血脂达标率</th>
              <th>我的血脂达标率</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, index) in mmcData.ldl" :key="index">
              <td class="td">{{ item.level }}</td>
              <td class="td">{{ item.rate }}</td>
              <td class="td">{{ item.my.rate }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <div class="tableCard">
      <p class="title">
        <span>
          <img src="@/assets/images/dataCenter/compare.png" alt="" />
          <span>管理患者的综合达标率</span>
        </span>
        <span class="extend" @click="change('compare')">
          <span>{{ compareExtend ? "收起" : "展开" }}</span>
          <van-icon v-if="compareExtend" name="arrow-up" />
          <van-icon v-if="!compareExtend" name="arrow-down" />
        </span>
      </p>
      <div v-if="compareExtend" class="table-outer">
        <table class="table">
          <thead>
            <tr>
              <th>访视阶段</th>
              <th>中心综合达标率</th>
              <th>我的综合达标率</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, index) in mmcData.compare" :key="index">
              <td class="td">{{ item.level }}</td>
              <td class="td">{{ item.rate }}</td>
              <td class="td">{{ item.my.rate }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script>
import { getmmcDataCenter } from "@/api/dataCenter.js";
export default {
  data() {
    return {
      visitExtend: false,
      bgExtend: false,
      bpExtend: false,
      ldlExtend: false,
      compareExtend: false,
      mmcData: {
        user_data: {
          mmc_patient_total: {
            name: "",
            num: 0,
          },
          month_incr: {
            name: "",
            num: 0,
          },
          mmc_24_warning: {
            name: "",
            num: 0,
          },
        },
        visit_data: {},
        bg: {},
        bp: {},
        ldl: {},
        compare: {},
      },
    };
  },
  created() {
    this.getData();
  },
  methods: {
    async getData() {
      let res = await getmmcDataCenter();
      if (res.code == 200) {
        this.mmcData = res.data;
      } else {
        this.$toast(res.msg);
      }
    },
    change(e) {
      this[e + "Extend"] = !this[e + "Extend"];
    },
  },
};
</script>

<style lang="scss" scoped>
.wrapper {
  background: #f5f5f5;
  padding: 16px 10px;
  .centerNumber {
    background: #ffffff;
    border-radius: 4px;
    .title {
      height: 30px;
      font-size: 16px;
      display: flex;
      align-items: center;
      padding-top: 20px;
      padding-left: 10px;
      img {
        height: 20px;
      }
      span {
        margin-left: 8px;
        font-weight: 500;
        color: #333333;
      }
    }
    .infoFlex {
      display: flex;
      flex-wrap: wrap;
      div {
        width: 50%;
        &:nth-child(3),
        &:nth-child(4) {
          margin-top: 30px;
          margin-bottom: 20px;
        }
        p {
          font-size: 13px;
          font-weight: 400;
          color: #999999;
        }
        span {
          font-size: 18px;
          font-weight: 400;
          color: #000000;
        }
      }
    }
  }
  .tableCard {
    margin-top: 13px;
    background: #ffffff;
    border-radius: 4px;
    .title {
      height: 30px;
      font-size: 16px;
      display: flex;
      //   align-items: center;
      justify-content: space-between;
      padding: 0 10px;
      padding-top: 20px;
      padding-bottom: 16px;
      span {
        display: flex;
        align-items: center;
        white-space:nowrap;
        img {
          height: 20px;
        }
        span {
          margin-left: 8px;
          font-weight: 500;
          color: #333333;
        }
      }
      .extend {
        font-size: 14px;
        font-weight: 400;
        color: #999999;
        span {
          color: #999999;
        }
      }
    }
    .table-outer {
      padding: 0 10px;
      padding-bottom: 16px;
    }
    .table {
      width: 100%;
      min-height: 48px;
      margin: 0 auto;
      thead {
        border: 1px solid #e6e6e6;
        border-right: 0;
        border-bottom: 0;
        tr {
          display: flex;
          height: 48px !important;
          line-height: 48px !important;
          background: #f7f7f7;
          font-size: 13px;
          font-weight: 400;
          th {
            flex: 1;
            display: block;
            color: #111;
            border-right: 1px solid #e6e6e6;
          }
          .th {
            flex: 1.5;
          }
        }
      }
      tbody {
        border-right: 1px solid #e6e6e6;
        border-bottom: 1px solid #e6e6e6;
      }
      tr {
        display: flex;
        height: 36px !important;
        line-height: 36px !important;
        td {
          flex: 1;
          color: #474747;
          font-size: 13px;
          font-weight: 400;
          vertical-align: middle;
          border: 1px solid #e6e6e6;
        }
        .td {
          border-right: 0;
          border-bottom: 0;
        }
        .tds {
          flex: 1.5;
        }
      }

      .table-val {
        color: #333;
        font-size: 17px;
        font-weight: normal;
      }
    }
  }
}
</style>
