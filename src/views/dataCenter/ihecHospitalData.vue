<!--
 * @Descripttion: MMC-数据中心
 * @version: 
 * @Author: guxiang
 * @Date: 2022-01-18 15:57:52
 * @LastEditors: guxiang
 * @LastEditTime: 2022-01-18 15:57:52
-->
<template>
  <div class="wrapper">
    <van-list
      v-model="loading"
      :finished="finished"
      finished-text="没有更多了"
      @load="onLoad"
    >
      <div v-for="(item, index) in showList" :key="index">
        <div class="time">{{ item.measure_at }}</div>
        <div
          class="hospitalData borderBottom"
          v-for="e in item.data"
          :key="e.id"
        >
          <div v-if="e.bp_status == 1" class="status normal">
            {{ e.bp_status_text }}
          </div>
          <div v-if="e.bp_status == 2" class="status low">
            {{ e.bp_status_text }}
          </div>
          <div v-if="e.bp_status == 3 || e.bp_status == 4" class="status high">
            {{ e.bp_status_text }}
          </div>
          <div>
            <div class="topFont">{{ e.sbp }}/{{ e.dbp }}</div>
            <div class="bottomFont">mmHg</div>
          </div>
          <div>
            <div class="topFont">{{ e.pulse }}</div>
            <div class="bottomFont">bpm</div>
          </div>
          <div class="normalFont">{{ e.measure_time }}</div>
          <div class="normalFont">{{ e.upload_way }}</div>
        </div>
      </div>
    </van-list>
  </div>
</template>

<script>
import { getihecHospitalData } from "@/api/dataCenter.js";
export default {
  data() {
    return {
      list: [],
      showList: [],
      page_no: 1,
      loading: false,
      finished: false,
    };
  },
  created() {},
  methods: {
    onLoad() {
      this.getData();
    },
    async getData() {
      let res = await getihecHospitalData({
        user_id: this.$route.query.patient_id,
        page_no: this.page_no,
        page_size: 10,
      });
      if (res.code == 200) {
        for (let i = 0; i < res.data.bp_list.length; i++) {
          this.list.push(res.data.bp_list[i]);
        }
        // 获得的数据转为二维数组
        var map = {};
        this.showList = [];
        for (let a = 0; a < this.list.length; a++) {
          var ai = this.list[a];
          if (!map[ai.measure_at]) {
            this.showList.push({
              measure_at: ai.measure_at,
              data: [ai],
            });
            map[ai.measure_at] = ai;
          } else {
            for (let j = 0; j < this.showList.length; j++) {
              var dj = this.showList[j];
              if (dj.measure_at == ai.measure_at) {
                dj.data.push(ai);
                break;
              }
            }
          }
        }
        // 页数加一
        this.page_no += 1;
        // 加载状态结束
        this.loading = false;
        // 数据全部加载完成
        if (res.data.current_page == res.data.last_page) {
          this.finished = true;
        }
      } else {
        this.$toast(res.msg);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.wrapper {
  // background: #f5f5f5;
  //   padding: 16px 10px;
  min-height: 300px;
  .time {
    background: #f5f5f5;
    text-align: left;
    padding: 8px 0;
    padding-left: 15px;
    font-size: 16px;
    font-weight: 600;
    color: #333333;
  }
  .hospitalData {
    background: #ffffff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 15px;
    .status {
      border-radius: 10px;
      font-size: 12px;
      line-height: 13px;
      font-weight: 400;
      padding: 2px 18px;
    }
    .normal {
      border: 1px solid #07c160;
      color: #07c160;
    }
    .high {
      border: 1px solid #e30000;
      color: #e30000;
    }
    .low {
      border: 1px solid #7b62ff;
      color: #7b62ff;
    }
    .topFont {
      font-size: 18px;
      font-weight: 400;
      color: #333333;
    }
    .bottomFont {
      font-size: 14px;
      font-weight: 400;
      color: #999999;
    }
    .normalFont {
      font-size: 16px;
      font-weight: 400;
      color: #666660;
    }
  }
  .borderBottom {
    border-bottom: 1px solid #e8e8e8;
  }
}
</style>