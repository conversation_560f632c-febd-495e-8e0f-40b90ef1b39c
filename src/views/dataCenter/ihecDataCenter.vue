<!--
 * @Descripttion: MMC-数据中心
 * @version: 
 * @Author: guxiang
 * @Date: 2022-01-18 15:57:52
 * @LastEditors: guxiang
 * @LastEditTime: 2022-01-18 15:57:52
-->
<template>
  <div class="wrapper">
    <div class="centerNumber">
      <p class="title">
        <img src="@/assets/images/operationReport/person.png" alt="" />
        <span>患者数量</span>
      </p>
      <van-divider />
      <div class="infoFlex">
        <div>
          <p>患者总数</p>
          <span>{{ ihecData.ihec_patient_total }}</span>
        </div>
        <div>
          <p>{{ name }}</p>
          <span>{{ ihecData.ihec_month_incr }}</span>
        </div>
        <div>
          <p>24小时预警</p>
          <span>{{ ihecData.ihec_24_warning }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getihecDataCenter } from "@/api/dataCenter.js";
export default {
  data() {
    return {
      ihecData: {
        ihec_month_incr: 0,
        ihec_patient_total: 0,
        ihec_24_warning: 0,
      },
      name: "",
    };
  },
  created() {
    this.getData();
  },
  methods: {
    async getData() {
      let res = await getihecDataCenter({
        panel_key: this.$route.query.panel_key,
        workroom_id: this.$route.query.workroom_id,
      });
      if (res.code == 200) {
        for (let i = 0; i < res.data.length; i++) {
          this.ihecData[res.data[i].key] = res.data[i].num;
          if (res.data[i].key == "ihec_month_incr") {
            this.name = res.data[i].name;
          }
        }
      } else {
        this.$toast(res.msg);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.wrapper {
  background: #f5f5f5;
  padding: 16px 10px;
  .centerNumber {
    background: #ffffff;
    border-radius: 4px;
    .title {
      height: 30px;
      font-size: 16px;
      display: flex;
      align-items: center;
      padding-top: 20px;
      padding-left: 10px;
      img {
        height: 20px;
      }
      span {
        margin-left: 8px;
        font-weight: 500;
        color: #333333;
      }
    }
    .infoFlex {
      display: flex;
      flex-wrap: wrap;
      padding-top: 10px;
      padding-bottom: 20px;
      div {
        width: 33.33%;
        p {
          font-size: 13px;
          font-weight: 400;
          color: #999999;
        }
        span {
          font-size: 18px;
          font-weight: 400;
          color: #000000;
        }
      }
    }
  }
}
</style>