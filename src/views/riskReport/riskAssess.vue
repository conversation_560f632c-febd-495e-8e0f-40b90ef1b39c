<template>
  <div class="wrapper">
    <div @click="goMetabolicIndex" class="group">
      <div class="group-sup">
        <p class="group-title">瑞宁<sup class="title-sup">TM</sup>代谢指数</p>
        <img src="./images/ningguangyuanshi.png" class="group-img1">
      </div>
      <div class="group-sub">
        <p class="group-intro">瑞宁代谢指数是基于全国大规模流行病学研究数据，根据您的血压、血糖、肥胖、血脂等指标计算所得，代表了您未来3年里患心脑血管疾病的风险。</p>
        <van-button round color="#E97B29" class="group-btn">开始评估<van-icon name="arrow" class="group-arrow" /></van-button>
      </div>
    </div>
    <div @click="goIschemicCardiovascular" class="group">
      <div class="group-sup">
        <div>
          <p class="group-title">国人缺血性</p>
          <p class="group-title">心血管病(ICVD)</p>
          <p class="group-tips">10年发病风险评估</p>
        </div>
        <img src="./images/renwu.png" class="group-img2">
      </div>
      <div class="group-sub">
        <p class="group-intro">心血管病严重危害国人健康，超过75%为动脉粥样硬化疾病所致。动脉粥样硬化的发生发展过程缓慢，因此，对特定人群进行危险评估，识别心血管疾病的高危人群，并进行干预，以降低心血管病危险，预防心血管事件。本评估适用于35～39岁年龄段人群。</p>
        <van-button round color="#4D7CEF" class="group-btn">开始评估<van-icon name="arrow" class="group-arrow" /></van-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data: () => {
    return {
      userId: ''
    }
  },
  created() {
    this.userId = this.$route.query.userId || ''
  },
  methods: {
    /**
     * 前往瑞宁代谢指数
     */
    goMetabolicIndex() {
      // window.location.href = `https://patient-h5.zz-med-test.com/dxzsHome?userId=${this.userId}`
      this.$router.push({
        path: '/dxzsHome',
        query: {
          userId: this.userId
        }
      })
    },
    /**
     * 前往缺血性心血管病
     */
    goIschemicCardiovascular() {
      // window.location.href = `https://patient-h5.zz-med-test.com/xxg`
      this.$router.push({
        path: '/xxg',
        query: {
          userId: this.userId
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.wrapper {
  padding: 0 15px;
  overflow: hidden;
  min-height: 100vh;
  background: #F5F6FA;

  .group {
    padding: 20px;
    margin: 15px 0;
    background: #FFF;
    border-radius: 9px;
    box-shadow: 0px 2px 8px 0px rgba(153, 153, 153, 0.2);

    &:nth-of-type(2) {

      .group-sub {
        background-color: #E8F1FF;
      }
    }

    .group-sup {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .group-title {
        color: #333;
        font-size: 28px;
        text-align: left;
        font-weight: 500;
        line-height: 40px;

        .title-sup {
          color: #333;
          margin: 0 2px;
          font-size: 14px;
          font-weight: 400;
          line-height: 20px;
          vertical-align: super;
        }
      }

      .group-img1 {
        width: 53px;
        margin-right: 20px;
      }

      .group-img2 {
        width: 86px;
      }

      .group-tips {
        font-size: 22px;
        color: #4E84FE;
        text-align: left;
        font-weight: 500;
        line-height: 30px;
      }
    }

    .group-sub {
      padding: 16px;
      border-radius: 9px;
      background-color: #F8DFCF;

      .group-intro {
        color: #333;
        font-size: 16px;
        text-align: left;
        font-weight: 400;
        line-height: 22px;
      }

      .group-btn {
        width: 232px;
        height: 38px;
        margin-top: 14px;
        line-height: 36px;
        border-radius: 19px;

        .group-arrow {
          vertical-align: middle;
        }
      }
    }
  }
}
</style>
