<template>
  <div class="content">
    <div class="title">
      {{title}}
    </div>
    <div class="watchTime">
      发布时间：{{updated_at}}
    </div>
    <div class="article" v-html="content">
      <div>{{content}}</div>
    </div>
  </div>
</template>
<script>
  import {getDetail} from '@/api/noticeDetail'

  export default {
    data() {
      return {
        title: '',
        content: '',
        updated_at: '',
        id: ''
      }
    },
    created() {
      this.init()
    },
    methods: {
      async init() {
        this.id = this.$route.query.id
        await getDetail({'id':this.id}).then(res => {
          this.title = res.data.title
          this.content = res.data.content
          this.updated_at = res.data.updated_at
        })
      }
    }
  }
</script>

<style scoped>
  .content {
    width: 375px;
    height: auto;
    padding-bottom: 25px;
  }

  .title {
    width: 331px;
    min-height: 30px;
    padding: 22px 22px 12px 22px;
    line-height: 30px;
    color: #333;
    font-size: 22px;
    font-weight: 600;
    text-align: left;
  }

  .watchTime {
    width: 331px;
    height: 21px;
    padding: 0 22px;
    color: #9B9B9B;
    font-size: 14px;
    margin-bottom: 16px;
    text-align: left;
  }

  .article {
    width: 331px;
    max-width: 331px!important;
    min-height: 18px;
    padding: 0 22px;
    height: auto;
    word-break: break-word;
    text-align: left;
    font-size: 18px;
    overflow: hidden;
  }

  .article >>> img {
    width: 100%;
    height: 100%;
    max-height: 100%;
    display: block!important;
    max-width: 100%!important;
  }

  .article >>> p, .article >>> div {
    width: 100%;
    font-size: 18px;
    color: #333333;
    text-align: left;
    line-height: 34px;
    margin-bottom: 24px;
    display: block!important;
    max-width: 100%!important;
  }

  .article >>> video {
    margin-bottom: 24px;
    width: 100%;
    display: block!important;
    max-width: 100%!important;
  }

  .article >>> iframe {
    width: 100%;
    height: 146px;
    display: block!important;
    max-width: 100%!important;
  }

  .article >>> section {
    width: 100%;
    max-width: 100%!important;
  }
</style>
