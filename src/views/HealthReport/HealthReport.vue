<template>
  <div :class="['wrapper', {'wrapper-no': hasData === 0}]">
    <div class="no-data" v-if="hasData === 0">
      <img src="./images/noHealthReport.png" alt="noData"/>
    </div>
    <div v-if="hasData === 1" class="header-backgroud">
      <img src="./images/bg-header.png" alt="bg-header">
    </div>
    <div v-if="hasData === 1" class="content">
      <!-- 报告概况 -->
      <div :class="['group', 'overview', {'overview-abnormal': familyStaticAbnormal+hospStaticCount > 0}]">
        <div @click="goRouter('/guanjia/health/report/family/bg')" class="overview-item">
          <div class="overview-box">
            <p class="overview-title">近7天血糖</p>
            <p class="overview-txt">
              达标<span :class="['overview-num',{'overview-num-grey':familyStaticNormalPercent<100}]">{{familyStaticNormalPercent}}</span>%
            </p>
            <p class="overview-txt">
              异常<span
              :class="['overview-num',{'overview-num-red':familyStaticAbnormal>0}]">{{familyStaticAbnormal}}</span>次
            </p>
          </div>
        </div>
        <div @click="goRouter('/guanjia/health/report/anomaly')" class="overview-item">
          <div class="overview-box">
            <p class="overview-title">指标共{{hospIndexCount}}项</p>
            <!-- 异常 -->
            <p class="overview-txt overview-txt-abnormal" v-if="hospStaticCount > 0">
              异常<span class="overview-num overview-num-red">{{hospStaticCount}}</span>项
            </p>
            <!-- 正常 -->
            <div v-if="hospStaticCount == 0 && hospIndexCount>0">
              <p class="overview-txt overview-txt-normal">您各项指标都</p>
              <p class="overview-txt overview-txt-normal">
                <span class="overview-num">正常</span>
                <img src="./images/normal.png" alt="high"/>
              </p>
            </div>
          </div>
        </div>
      </div>
      <!-- 代谢指数评估 -->
      <div class="group">
        <div class="group-top" @click="goRouter('/guanjia/health/report/metabolism')">
          <p class="group-top-title">代谢指数评估</p>
          <div class="group-top-right">
            <span>了解更多</span>
            <img src="./images/next2.png" alt="go"/>
          </div>
        </div>
        <div v-if="metaboliseScore!=='-'">
          <div class="metabolism-middle">
            <div class="metabolism-echarts-wrapper">
              <div class="metabolism-echarts-box" ref="metabolismEchart"></div>
              <div class="metabolism-echarts-title">
                <p class="echarts-title-main">
                  <span>{{metaboliseScore}}</span>分
                </p>
                <p class="echarts-title-sub">代谢指数</p>
              </div>
            </div>
            <p class="metabolism-middle-p">{{metaboliseText}}</p>
            <div class="metabolism-echarts-wrapper">
              <div class="metabolism-echarts-box" ref="cardiovascularEchart"></div>
              <div class="metabolism-echarts-title">
                <p class="echarts-title-main">
                  <span>{{metabolisePercent}}</span>
                </p>
                <p class="echarts-title-sub2">三年发生心血管</p>
                <p class="echarts-title-sub2">疾病的概率</p>
              </div>
            </div>
          </div>
          <div class="metabolism-bottom">
            <img src="./images/attention.png" alt="metabolism"/>
            <span>建议您加强家庭血糖监测，并及时就诊。</span>
          </div>
        </div>
        <!-- 无数据状态 -->
        <div v-else class="metabolism-no">
          <p>暂无代谢指数</p>
          <p>您可以去附近的mmc中心进行评估</p>
        </div>
      </div>
      <!-- 家庭测量数据 -->
      <div class="group">
        <div class="group-up">
          <div class="group-up-left">
            <img src="./images/family.png" alt="family" class="group-up-icon"/>
            <span class="group-up-title">家庭测量数据</span>
          </div>
        </div>
        <div class="report-general">
          <!-- 血糖 -->
          <div
            @click="goRouter('/guanjia/health/report/family/bg')"
            class="report-item report-item-bg"
          >
            <div class="report-item-top">
              <div class="report-item-name">
                <span class="report-item-txt">血糖</span>
                <img
                  v-if="familyMeasure.bg.empty === 0"
                  src="./images/arrow_right2.png"
                  alt="go"
                  class="report-item-go"/>
              </div>
              <div v-if="familyMeasure.bg.empty === 1" class="report-item-value">
                <div class="report-item-num">
                  <span class="report-item-val">{{familyMeasure.bg.bg}}</span>
                  <img v-if="familyMeasure.bg.normal === 3" src="./images/low.png" alt="low"/>
                  <img v-if="familyMeasure.bg.normal === 2" src="./images/high.png" alt="high"/>
                </div>
                <div
                  v-if="familyMeasure.bg.normal_ext != null"
                  class="report-item-remind"
                  :class="familyMeasure.bg.normalClass"
                >{{familyMeasure.bg.normal_ext}}
                </div>
                <img src="./images/arrow_right2.png" alt="go" class="report-item-go"/>
              </div>
              <div class="report-item-refer">
                <span>参考值</span>
                <span class="report-item-range">
                  {{familyMeasure.bg.reference}}
                  mmol/L
                </span>
              </div>
            </div>
            <div v-if="familyMeasure.bg.empty === 1" class="report-item-date">
              <span>{{familyMeasure.bg.measure_at}}</span>
            </div>
          </div>
          <!-- 血压 -->
          <div
            @click="goRouter('/guanjia/health/report/family/bp')"
            class="report-item report-item-bp"
          >
            <div class="report-item-top">
              <div class="report-item-name">
                <span class="report-item-txt">血压</span>
                <img
                  v-if="familyMeasure.bp.empty === 0"
                  src="./images/arrow_right2.png"
                  alt="go"
                  class="report-item-go"/>
              </div>
              <div v-if="familyMeasure.bp.empty === 1" class="report-item-value">
                <div class="report-item-num">
                  <span class="report-item-val">{{familyMeasure.bp.sbp + '/' + familyMeasure.bp.dbp}}</span>
                  <img v-if="familyMeasure.bp.normal === 3" src="./images/low.png" alt="low"/>
                  <img v-if="familyMeasure.bp.normal === 2" src="./images/high.png" alt="high"/>
                </div>
                <img src="./images/arrow_right2.png" alt="go" class="report-item-go"/>
              </div>
              <div v-if="familyMeasure.bp.empty === 1" class="report-item-refer">
                <span>参考值</span>
                <span class="report-item-range">
                  {{familyMeasure.bp.reference}}
                  mmHg
                </span>
              </div>
              <div v-if="familyMeasure.bp.empty === 0" class="report-item-no">
                <span>你在近30天未记录该项数据，建议定期测量</span>
              </div>
            </div>
            <div v-if="familyMeasure.bp.empty === 1" class="report-item-date">
              <span>{{familyMeasure.bp.measure_at}}</span>
            </div>
          </div>
          <!-- 运动 -->
          <div
            @click="goRouter('/guanjia/health/report/family/motion')"
            class="report-item report-item-motion"
          >
            <div class="report-item-top">
              <div class="report-item-name">
                <span class="report-item-txt">运动</span>
                <img
                  v-if="familyMeasure.step.empty === 0"
                  src="./images/arrow_right2.png"
                  alt="go"
                  class="report-item-go"/>
              </div>
              <div v-if="familyMeasure.step.empty === 1" class="report-item-value">
                <div class="report-item-num">
                  <span class="report-item-val">{{familyMeasure.step.num}}</span>
                  <span class="report-step-unit">步</span>
                </div>
                <img src="./images/arrow_right2.png" alt="go" class="report-item-go"/>
              </div>
              <div v-if="familyMeasure.step.empty === 0" class="report-item-no">
                <span>你在近30天未记录该项数据，建议定期测量</span>
              </div>
            </div>
            <div v-if="familyMeasure.step.empty === 1" class="report-item-date">
              <span>{{familyMeasure.step.measure_at}}</span>
            </div>
          </div>
          <!-- BMI -->
          <div
            @click="goRouter('/guanjia/health/report/family/bmi')"
            class="report-item report-item-bmi"
          >
            <div class="report-item-top">
              <div class="report-item-name">
                <span class="report-item-txt">BMI</span>
                <img
                  v-if="familyMeasure.bmi.empty === 0"
                  src="./images/arrow_right2.png"
                  alt="go"
                  class="report-item-go"/>
              </div>
              <div v-if="familyMeasure.bmi.empty === 1" class="report-item-value">
                <div class="report-item-num">
                  <span class="report-item-val">{{familyMeasure.bmi.bmi}}</span>
                  <img v-if="familyMeasure.bmi.normal === 3" src="./images/low.png" alt="low"/>
                  <img v-if="familyMeasure.bmi.normal === 2" src="./images/high.png" alt="high"/>
                </div>
                <div
                  v-if="familyMeasure.bmi.normal_ext != null"
                  class="report-item-remind"
                  :class="familyMeasure.bmi.normalClass"
                >{{familyMeasure.bmi.normal_ext}}
                </div>
                <img src="./images/arrow_right2.png" alt="go" class="report-item-go"/>
              </div>
              <div v-if="familyMeasure.bmi.empty === 1" class="report-item-refer">
                <span>参考值</span>
                <span class="report-item-range">
                  {{familyMeasure.bmi.reference}}
                  kg/m²
                </span>
              </div>
              <div v-if="familyMeasure.bmi.empty === 0" class="report-item-no">
                <span>你在近30天未记录该项数据，建议定期测量</span>
              </div>
            </div>
            <div v-if="familyMeasure.bmi.empty === 1" class="report-item-date">
              <span>{{familyMeasure.bmi.measure_at}}</span>
            </div>
          </div>
        </div>
      </div>
      <!-- 医院报告数据 -->
      <div class="group">
        <div class="group-up">
          <div class="group-up-left">
            <img src="./images/hosp.png" alt="hosp" class="group-up-icon"/>
            <span class="group-up-title">最新医院就诊指标</span>
          </div>
          <span @click="goRouter('/guanjia/health/report/his')" class="group-up-btn">历史报告</span>
        </div>
        <div class="report-general">
          <!-- 糖化血红蛋白 -->
          <div
            @click="goRouter('/guanjia/health/report/hosp/hemoglobin')"
            class="report-item report-item-hemoglobin"
          >
            <div class="report-item-top">
              <div class="report-item-name">
                <span class="report-item-txt">糖化血红蛋白</span>
                <img
                  v-if="hospMeasure.s_876.empty === 0"
                  src="./images/arrow_right2.png"
                  alt="go"
                  class="report-item-go"/>
              </div>
              <div v-if="hospMeasure.s_876.empty === 1" class="report-item-value">
                <div class="report-item-num">
                  <span class="report-item-val">{{hospMeasure.s_876.val}}</span>
                  <img v-if="hospMeasure.s_876.normal === 1" src="./images/low.png" alt="low"/>
                  <img v-if="hospMeasure.s_876.normal === 2" src="./images/high.png" alt="high"/>
                </div>
                <img src="./images/arrow_right2.png" alt="go" class="report-item-go"/>
              </div>
              <div v-if="hospMeasure.s_876.empty === 1" class="report-item-refer">
                <span v-if="hospMeasure.s_876.reference !== ''">参考值</span>
                <span class="report-item-range">
                  {{hospMeasure.s_876.reference}}
                  {{hospMeasure.s_876.unit}}
                </span>
              </div>
              <div v-if="hospMeasure.s_876.empty === 0" class="report-item-no">
                <p>你未做过该检测</p>
                <p>建议每年至少检查一次</p>
              </div>
            </div>
            <div v-if="hospMeasure.s_876.empty === 1" class="report-item-date">
              <span>{{hospMeasure.s_876.date}}</span>
            </div>
          </div>
          <!-- 院内血压 -->
          <div
            @click="goRouter('/guanjia/health/report/hosp/bp')"
            class="report-item report-item-bp"
          >
            <div class="report-item-top">
              <div class="report-item-name">
                <span class="report-item-txt">院内血压</span>
                <img
                  v-if="hospMeasure.hospBp.empty === 0"
                  src="./images/arrow_right2.png"
                  alt="go"
                  class="report-item-go"/>
              </div>
              <div class="report-item-value" v-if="hospMeasure.hospBp.empty === 1">
                <div class="report-item-num">
                  <span class="report-item-val">{{hospMeasure.hospBp.val}}</span>
                  <img v-if="hospMeasure.hospBp.normal === 1" src="./images/low.png" alt="low"/>
                  <img v-if="hospMeasure.hospBp.normal === 2" src="./images/high.png" alt="high"/>
                </div>
                <img src="./images/arrow_right2.png" alt="go" class="report-item-go"/>
              </div>
              <div v-if="hospMeasure.hospBp.empty === 1" class="report-item-refer">
                <span v-if="hospMeasure.hospBp.reference !== ''">参考值</span>
                <span class="report-item-range">
                  {{hospMeasure.hospBp.reference}}
                  {{hospMeasure.hospBp.unit}}
                </span>
              </div>
              <div v-if="hospMeasure.hospBp.empty === 0" class="report-item-no">
                <p>你未做过该检测</p>
                <p>建议每年至少检查一次</p>
              </div>
            </div>
            <div v-if="hospMeasure.hospBp.empty === 1" class="report-item-date">
              <span>{{hospMeasure.hospBp.date}}</span>
            </div>
          </div>
          <!-- 尿白蛋白比肌酐 -->
          <div
            @click="goRouter('/guanjia/health/report/hosp/creatinine')"
            class="report-item report-item-creatinine"
          >
            <div class="report-item-top">
              <div class="report-item-name">
                <span class="report-item-txt">尿白蛋白比肌酐</span>
                <img
                  v-if="hospMeasure.s_901013.empty === 0"
                  src="./images/arrow_right2.png"
                  alt="go"
                  class="report-item-go"/>
              </div>
              <div v-if="hospMeasure.s_901013.empty === 1" class="report-item-value">
                <div class="report-item-num">
                  <span class="report-item-val">{{hospMeasure.s_901013.val}}</span>
                  <img v-if="hospMeasure.s_901013.normal === 1" src="./images/low.png" alt="low"/>
                  <img v-if="hospMeasure.s_901013.normal === 2" src="./images/high.png" alt="high"/>
                </div>
                <img src="./images/arrow_right2.png" alt="go" class="report-item-go"/>
              </div>
              <div v-if="hospMeasure.s_901013.empty === 1" class="report-item-refer">
                <span v-if="hospMeasure.s_901013.reference !== ''">参考值</span>
                <span class="report-item-range">
                  {{hospMeasure.s_901013.reference}}
                  {{hospMeasure.s_901013.unit}}
                </span>
              </div>
              <div v-if="hospMeasure.s_901013.empty === 0" class="report-item-no">
                <p>你未做过该检测</p>
                <p>建议每年至少检查一次</p>
              </div>
            </div>
            <div v-if="hospMeasure.s_901013.empty === 1" class="report-item-date">
              <span>{{hospMeasure.s_901013.date}}</span>
            </div>
          </div>
          <!-- BMI -->
          <div
            @click="goRouter('/guanjia/health/report/hosp/bmi')"
            class="report-item report-item-bmi"
          >
            <div class="report-item-top">
              <div class="report-item-name">
                <span class="report-item-txt">BMI</span>
                <img
                  v-if="hospMeasure.bmi.empty === 0"
                  src="./images/arrow_right2.png"
                  alt="go"
                  class="report-item-go"/>
              </div>
              <div v-if="hospMeasure.bmi.empty === 1" class="report-item-value">
                <div class="report-item-num">
                  <span class="report-item-val">{{hospMeasure.bmi.val}}</span>
                  <img v-if="hospMeasure.bmi.normal === 3" src="./images/low.png" alt="low"/>
                  <img v-if="hospMeasure.bmi.normal === 2" src="./images/high.png" alt="high"/>
                </div>
                <div
                  v-if="hospMeasure.bmi.normalExt != null"
                  class="report-item-remind"
                  :class="hospMeasure.bmi.normalClass"
                >{{hospMeasure.bmi.normalExt}}
                </div>
                <img src="./images/arrow_right2.png" alt="go" class="report-item-go"/>
              </div>
              <div v-if="hospMeasure.bmi.empty === 1" class="report-item-refer">
                <span v-if="hospMeasure.bmi.reference !== ''">参考值</span>
                <span class="report-item-range">
                  {{hospMeasure.bmi.reference}}
                  {{hospMeasure.bmi.unit}}
                </span>
              </div>
              <div v-if="hospMeasure.bmi.empty === 0" class="report-item-no">
                <p>你未做过该检测</p>
                <p>建议每年至少检查一次</p>
              </div>
            </div>
            <div v-if="hospMeasure.bmi.empty === 1" class="report-item-date">
              <span>{{hospMeasure.bmi.date}}</span>
            </div>
          </div>
          <!-- 血脂 -->
          <div
            @click="goRouter('/guanjia/health/report/hosp/lipid')"
            class="report-item report-item-lipid"
          >
            <div class="report-item-name">
              <span class="report-item-txt">血脂</span>
              <img src="./images/arrow_right2.png" alt="go" class="report-item-go"/>
            </div>
            <div v-if="Number(hospMeasure.blood_lipid.empty)===1">
              <p class="report-item-anomaly">{{hospMeasure.blood_lipid.conclusion}}</p>
              <div class="report-item-date">
                <span>{{hospMeasure.blood_lipid.date}}</span>
              </div>
            </div>
            <!-- 无数据状态 -->
            <div v-else class="report-item-tips">
              <p>你未做过该检测</p>
              <p>建议每年至少检查一次</p>
            </div>
          </div>
          <!-- 动脉硬化检测 -->
          <div
            @click="goRouter('/guanjia/health/report/hosp/arteriosclerosis')"
            class="report-item report-item-arteriosclerosis"
          >
            <div class="report-item-name">
              <span class="report-item-txt">动脉硬化检测</span>
              <img src="./images/arrow_right2.png" alt="go" class="report-item-go"/>
            </div>
            <div class="report-item-tips" v-if="Number(hospMeasure.pwv.empty)===1">
              <p>查看结论</p>
              <div class="report-item-date-no-tab">
                <span>{{hospMeasure.pwv.date}}</span>
              </div>
            </div>
            <!-- 无数据状态 -->
            <div v-else class="report-item-tips">
              <p>你未做过该检测</p>
              <p>建议每年至少检查一次</p>
            </div>
          </div>
          <!-- 内脏脂肪面积 -->
          <div
            @click="goRouter('/guanjia/health/report/hosp/vat')"
            class="report-item report-item-vat"
          >
            <div class="report-item-top">
              <div class="report-item-name">
                <span class="report-item-txt">内脏脂肪面积</span>
                <img
                  v-if="hospMeasure.vat.empty === 0"
                  src="./images/arrow_right2.png"
                  alt="go"
                  class="report-item-go"/>
              </div>
              <div v-if="hospMeasure.vat.empty === 1" class="report-item-value">
                <div class="report-item-num">
                  <span class="report-item-val">{{hospMeasure.vat.val}}</span>
                  <img v-if="hospMeasure.vat.normal === 1" src="./images/low.png" alt="low"/>
                  <img v-if="hospMeasure.vat.normal === 2" src="./images/high.png" alt="high"/>
                </div>
                <img src="./images/arrow_right2.png" alt="go" class="report-item-go"/>
              </div>
              <div v-if="hospMeasure.vat.empty === 1" class="report-item-refer">
                <span v-if="hospMeasure.vat.reference!==''">参考值</span>
                <span class="report-item-range">
                  {{hospMeasure.vat.reference}}
                  {{hospMeasure.vat.unit}}
                </span>
              </div>
              <div v-if="hospMeasure.vat.empty === 0" class="report-item-no">
                <p>你未做过该检测</p>
                <p>建议每年至少检查一次</p>
              </div>
            </div>
            <div v-if="hospMeasure.vat.empty === 1" class="report-item-date">
              <span>{{hospMeasure.vat.date}}</span>
            </div>
          </div>
          <!-- 眼底筛查 -->
          <div
            @click="goRouter('/guanjia/health/report/hosp/sugarnet')"
            class="report-item report-item-sugarnet"
          >
            <div class="report-item-top">
              <div class="report-item-name">
                <span class="report-item-txt">眼底筛查</span>
                <img src="./images/arrow_right2.png" alt="go" class="report-item-go"/>
              </div>
              <div v-if="hospMeasure.s_945.empty === 1 || hospMeasure.s_946.empty === 1" class="report-item-eye">
                <div class="item-eye-info eye-info-left">
                  <p class="eye-info-top">左眼</p>
                  <p class="eye-info-down">{{hospMeasure.s_945.val}}</p>
                </div>
                <i class="item-eye-line"></i>
                <div class="item-eye-info eye-info-right">
                  <p class="eye-info-top">右眼</p>
                  <p class="eye-info-down">{{hospMeasure.s_946.val}}</p>
                </div>
              </div>
              <div v-if="hospMeasure.s_945.empty === 0 && hospMeasure.s_946.empty === 0" class="report-item-no">
                <p>你未做过该检测</p>
                <p>建议每年至少检查一次</p>
              </div>
            </div>
            <div v-if="hospMeasure.s_945.empty === 1" class="report-item-date">
              <span>{{hospMeasure.s_945.date}}</span>
            </div>
          </div>
          <!-- 周围神经病变检测 -->
          <div
            @click="goRouter('/guanjia/health/report/hosp/neuropathy')"
            class="report-item report-item-neuropathy"
          >
            <div class="report-item-name">
              <span class="report-item-txt">周围神经病变检测</span>
              <img src="./images/arrow_right2.png" alt="go" class="report-item-go"/>
            </div>
            <div v-if="Number(hospMeasure.ner_sur.empty)===1">
              <div class="report-item-tips">
                <p>查看结论</p>
                <div class="report-item-date-no-tab">
                  <span>{{hospMeasure.ner_sur.date}}</span>
                </div>
              </div>
            </div>
            <div v-else class="report-item-tips">
              <p>你未做过该检测</p>
              <p>建议每年至少检查一次</p>
            </div>
          </div>
          <!-- 肝肾、甲状腺功能 -->
          <div
            @click="goRouter('/guanjia/health/report/hosp/liver')"
            class="report-item report-item-liver"
          >
            <div class="report-item-name">
              <span class="report-item-txt">肝肾、甲状腺功能</span>
              <img src="./images/arrow_right2.png" alt="go" class="report-item-go"/>
            </div>
            <div v-if="Number(hospMeasure.liver_kidney.empty)===1">
              <div>
                <p class="report-item-anomaly">{{hospMeasure.liver_kidney.conclusion}}</p>
              </div>
              <div class="report-item-date">
                <span>{{hospMeasure.liver_kidney.date}}</span>
              </div>
            </div>
            <!-- 无数据状态 -->
            <div v-else class="report-item-tips">
              <p>你未做过该检测</p>
              <p>建议每年至少检查一次</p>
            </div>
          </div>
          <!-- 颈动脉超声 -->
          <div
            @click="goRouter('/guanjia/health/report/hosp/carotid')"
            class="report-item report-item-carotid"
          >
            <div class="report-item-name">
              <span class="report-item-txt">颈动脉超声</span>
              <img src="./images/arrow_right2.png" alt="go" class="report-item-go"/>
            </div>
            <div v-if="Number(hospMeasure.carotid.empty)===1" class="report-item-tips">
              <p>查看结论</p>
              <div class="report-item-date-no-tab">
                <span>{{hospMeasure.carotid.date}}</span>
              </div>
            </div>
            <!-- 无数据状态 -->
            <div v-else class="report-item-tips">
              <p>你未做过该检测</p>
              <p>建议每年至少检查一次</p>
            </div>
          </div>
          <!-- 心超、心电图 -->
          <div
            @click="goRouter('/guanjia/health/report/hosp/heart')"
            class="report-item report-item-heart"
          >
            <div class="report-item-name">
              <span class="report-item-txt">心超、心电图</span>
              <img src="./images/arrow_right2.png" alt="go" class="report-item-go"/>
            </div>
            <div v-if="Number(hospMeasure.heart.empty)===1" class="report-item-tips">
              <p>查看结论</p>
              <div class="report-item-date-no-tab">
                <span>{{hospMeasure.heart.date}}</span>
              </div>
            </div>
            <!-- 无数据状态 -->
            <div v-else class="report-item-tips">
              <p>你未做过该检测</p>
              <p>建议每年至少检查一次</p>
            </div>
          </div>
          <!-- 实验室检测图表 -->
          <!-- <div
            @click="healthCoreUrl(1)"
            class="report-item report-item-lab"
          >
            <div class="report-item-name">
              <span class="report-item-txt">实验室检测图表</span>
              <img src="./images/arrow_right2.png" alt="go" class="report-item-go"/>
            </div>
          </div> -->
          <!-- 辅助检查图表 -->
          <!-- <div
            @click="healthCoreUrl(2)"
            class="report-item report-item-assist"
          >
            <div class="report-item-name">
              <span class="report-item-txt">辅助检查图表</span>
              <img src="./images/arrow_right2.png" alt="go" class="report-item-go"/>
            </div>
          </div> -->
        </div>
      </div>
    </div>
    <footer v-if="hasData === 1">
      <p class="footer-tips">备注：报告内容仅供参考，不作为诊疗依据。目前只显示部分数据。</p>
    </footer>
  </div>
</template>

<script>
  import echarts from 'echarts'
  import { getVisitReport, getHealthCoreUrl } from '@/api/HealthReport'

  export default {
    data() {
      return {
        userId: 0,
        hasData: '',
        visit_at: '',
        hosp_id: '',
        reportDate: '',
        createDate: '',
        hospStaticCount: 0,
        hospIndexCount: 0,
        familyStaticNormal: '',
        familyStaticAbnormal: '',
        familyStaticNormalPercent: '',
        familyStaticAbnormalPercent: '',
        hospStatic: [],
        metaboliseScore: '',
        metabolisePercent: '',
        metaboliseText: '',
        familyMeasure: {
          bg: {},
          bmi: {},
          bp: {},
          step: {},
          time_line: ''
        },
        hospMeasure: {
          bmi: {},
          hospBp: {},
          s_876: {},
          s_945: {},
          s_946: {},
          s_901013: {},
          vat: {},
          blood_lipid: {},
          liver_kidney: {},
          pwv: {},
          ner_sur: {},
          carotid: {},
          heart: {}
        },
        hospPicture: '',
        isShowMoreItem: false
      }
    },
    computed: {
      // 家庭血糖异常 进度条
      progressRatioSite() {
        let style = {}
        if (this.familyStaticAbnormalPercent !== '') {
          style.width = this.familyStaticAbnormalPercent
        }
        return style
      },
      // 代谢指数评估 echarts数据
      metabolismData() {
        let baseScore = 100
        let metaboliseScore = Number(this.metaboliseScore)
        let metaboliseOtherScore = baseScore - metaboliseScore
        let metabolisePercent = this.metabolisePercent
        let metaboliseVal = Number(metabolisePercent.split('%')[0])
        let metaboliseOtherVal = baseScore - metaboliseVal
        return [
          {
            ref: this.$refs.metabolismEchart,
            data: [
              {
                value: metaboliseScore,
                colorStops: ['#FBAF25', '#F67710']
              },
              {
                value: metaboliseOtherScore,
                colorStops: ['#E8E8E8', '#E8E8E8']
              }
            ]
          },
          {
            ref: this.$refs.cardiovascularEchart,
            data: [
              {
                value: metaboliseVal,
                colorStops: ['#FFD4CD', '#EC667F']
              },
              {
                value: metaboliseOtherVal,
                colorStops: ['#98F1C9', '#5FDF98']
              }
            ]
          }
        ]
      }
    },
    methods: {
      /**
       * 初始化
       */
      async init() {
        let param = {}
        // 获取报告数据
        await getVisitReport(param).then(res => {
          if (res.status === 0) {
            // 处理报告数据
            this.handleReportData(res.data)
          } else {
            this.$toast(res.msg)
          }
        })
      },
      /**
       * 处理报告数据
       * @param {Object} data 报告数据
       */
      handleReportData(data) {
        if (data.has_data === 1) {
          let that = this
          this.hasData = 1
          this.reportDate = data.reportDate || ''
          this.createDate = data.createDate || ''
          this.hosp_id = data.hosp_id || this.$route.query.hosp_id || ''
          // 血糖异常统计
          let deviantStatic = data.deviantStatic || {}
          let familyStatic = deviantStatic.familyStatic || {}

          this.familyStaticNormal =
            familyStatic.normal != null ? familyStatic.normal : ''
          this.familyStaticAbnormal =
            familyStatic.abnormal != null ? familyStatic.abnormal : ''
          this.familyStaticNormalPercent =
            familyStatic.normal_percent != null ? familyStatic.normal_percent : ''

          this.familyStaticAbnormalPercent = familyStatic.abnormal_percent || ''
          this.hospStatic = deviantStatic.hospStatic || []

          this.hospStaticCount = deviantStatic.hospStaticCount || 0
          this.hospIndexCount = deviantStatic.hospIndexCount || 0

          // 代谢指数评估
          let metabolise = data.metabolise || {}
          this.metaboliseScore = metabolise.score || ''
          this.metabolisePercent = metabolise.percent || ''
          this.metaboliseText = metabolise.text || ''
          // 家庭测量数据
          let familyMeasure = data.familyMeasure || {}
          Object.keys(familyMeasure).forEach(val => {
            if (familyMeasure[val] && familyMeasure[val].normal != null) {
              if (val === 'bmi') {
                familyMeasure[val].normalClass = that.bmiClass(
                  familyMeasure[val].normal
                )
              } else {
                familyMeasure[val].normalClass = that.familyJudgeClass(
                  familyMeasure[val].normal
                )
              }
            }
          })
          this.familyMeasure.bg = familyMeasure.bg || {}
          this.familyMeasure.bmi = familyMeasure.bmi || {}
          this.familyMeasure.bp = familyMeasure.bp || {}
          this.familyMeasure.step = familyMeasure.step || {}
          // 医院报告数据
          let hospMeasure = data.hospMeasure || {}
          Object.keys(hospMeasure).forEach(val => {
            if (hospMeasure[val] && hospMeasure[val].normal != null) {
              if (val === 'bmi') {
                hospMeasure[val].normalExt = that.bmiExt(hospMeasure[val].normal)
                hospMeasure[val].normalClass = that.bmiClass(
                  hospMeasure[val].normal
                )
              } else if (val === 'vat') {
                hospMeasure[val].normalExt = that.vatExt(hospMeasure[val].normal)
                hospMeasure[val].normalClass = that.vatClass(
                  hospMeasure[val].normal
                )
              } else {
                hospMeasure[val].normalExt = that.judgeExt(
                  hospMeasure[val].normal
                )
                hospMeasure[val].normalClass = that.hospJudgeClass(
                  hospMeasure[val].normal
                )
              }
            }
          })
          this.hospMeasure.bmi = hospMeasure.bmi || {}
          this.hospMeasure.hospBp = hospMeasure.hospBp || {}
          this.hospMeasure.s_876 = hospMeasure.s_876 || {}
          this.hospMeasure.s_945 = hospMeasure.s_945 || {}
          this.hospMeasure.s_946 = hospMeasure.s_946 || {}
          this.hospMeasure.s_901013 = hospMeasure.s_901013 || {}
          this.hospMeasure.vat = hospMeasure.vat || {}

          this.hospMeasure.blood_lipid = hospMeasure.blood_lipid || {}
          this.hospMeasure.liver_kidney = hospMeasure.liver_kidney || {}
          this.hospMeasure.pwv = hospMeasure.pwv || {}
          this.hospMeasure.ner_sur = hospMeasure.ner_sur || {}
          this.hospMeasure.carotid = hospMeasure.carotid || {}
          this.hospMeasure.heart = hospMeasure.heart || {}

          this.userId = data.user_id

          // echarts初始化
          if (this.metaboliseScore !== '-') {
            this.$nextTick(() => {
              this.metabolismData.forEach(metabolism => {
                that.initEchart(metabolism.ref, metabolism.data)
              })
            })
          }
        } else {
          this.hasData = 0
        }
      },
      /**
       * 家庭类型判断类名(1:正常,2:偏高,3:偏低,4:极高)
       * @param {number} type 类型
       * @return {string} className 类名
       */
      familyJudgeClass(type) {
        let className = 'item-remind-'
        switch (type) {
          case 1:
            className = ''
            break
          case 2:
            className += 'up'
            break
          case 3:
            className += 'down'
            break
          case 4:
            className += 'highest'
            break
          default:
            className = ''
        }
        return className
      },
      /**
       * 医院类型判断类名(0:正常,1:偏低,2:偏高)
       * @param {number} type 类型
       * @return {string} className 类名
       */
      hospJudgeClass(type) {
        let className = 'item-remind-'
        switch (type) {
          case 0:
            className = ''
            break
          case 1:
            className += 'down'
            break
          case 2:
            className += 'up'
            break
          default:
            className = ''
        }
        return className
      },
      /**
       * BMI类型判断类名(1:正常,2:偏胖,3:偏瘦,4:肥胖)
       * @param {number} type 类型
       * @return {string} className 类名
       */
      bmiClass(type) {
        let className = 'item-remind-bmi-'
        switch (type) {
          case 1:
            className += 'normal'
            break
          case 2:
            className += 'up'
            break
          case 3:
            className += 'down'
            break
          case 4:
            className += 'highest'
            break
          default:
            className = ''
        }
        return className
      },
      /**
       * 内脏脂肪类型判断类名(0:正常,2:偏高)
       * @param {number} type 类型
       * @return {string} className 类名
       */
      vatClass(type) {
        let className = 'item-remind-'
        switch (type) {
          case 2:
            className += 'up'
            break
          default:
            className = ''
        }
        return className
      },
      /**
       * 类型判断状态(0:正常,1:偏低,2:偏高)
       * @param {number} type 类型
       * @return {string} ext 状态
       */
      judgeExt(type) {
        let ext
        switch (type) {
          case 0:
            ext = '正常'
            break
          case 1:
            ext = '偏低'
            break
          case 2:
            ext = '偏高'
            break
          default:
            ext = '-'
        }
        return ext
      },
      /**
       * BMI类型判断状态(1:正常,2:偏胖,3:偏瘦,4:肥胖)
       * @param {number} type 类型
       * @return {string} ext 状态
       */
      bmiExt(type) {
        let ext
        switch (type) {
          case 1:
            ext = '正常'
            break
          case 2:
            ext = '偏胖'
            break
          case 3:
            ext = '偏瘦'
            break
          case 4:
            ext = '肥胖'
            break
          default:
            ext = '-'
        }
        return ext
      },
      /**
       * 内脏脂肪类型判断状态(0:正常,2:偏高)
       * @param {number} type 类型
       * @return {string} ext 状态
       */
      vatExt(type) {
        let ext
        switch (type) {
          case 0:
            ext = '正常'
            break
          case 2:
            ext = '偏高'
            break
          default:
            ext = '-'
        }
        return ext
      },

      /**
       * echarts初始化
       * @param {Object} ref DOM元素
       * @param {Object} data 图表数据
       */
      initEchart(ref, data) {
        let myEchart = echarts.init(ref)
        myEchart.setOption({
          series: [
            {
              type: 'pie', // 图标类型
              center: ['50%', '50%'], // 饼图的中心（圆心）坐标，数组的第一项是横坐标，第二项是纵坐标
              radius: ['70%', '100%'], // 饼图的半径，数组第一项是内半径，第二项是外半径
              hoverAnimation: false, // 是否开启 放大动画效果
              label: {
                // 饼图图形上的文本标签
                normal: {
                  show: false
                }
              },
              startAngle: 90, // 起始角度
              data: [
                {
                  value: data[0].value,
                  itemStyle: {
                    color: {
                      type: 'linear',
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      colorStops: [
                        {
                          offset: 0,
                          color: data[0].colorStops[0] // 0% 处的颜色
                        },
                        {
                          offset: 1,
                          color: data[0].colorStops[1] // 100% 处的颜色
                        }
                      ],
                      global: false // 缺省为 false
                    }
                  }
                },
                {
                  value: data[1].value,
                  itemStyle: {
                    normal: {
                      // normal：图形在默认状态下的样式；emphasis：图形在高亮状态下的样式
                      color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [
                          {
                            offset: 0,
                            color: data[1].colorStops[0] // 0% 处的颜色
                          },
                          {
                            offset: 1,
                            color: data[1].colorStops[1] // 100% 处的颜色
                          }
                        ],
                        global: false // 缺省为 false
                      }
                    }
                  }
                }
              ]
            }
          ]
        })
      },
      /**
       * 路由跳转
       * @param {string} path 路由地址
       */
      goRouter(path) {
        let query = { rebackType: 2 }
        this.$router.push({ path, query })
      },
      healthCoreUrl(type) {
        window.open(getHealthCoreUrl(this.userId, type))
      }
    },
    created() {
      // this.$nextTick(() => {
      this.init()
      // })
    }
  }
</script>

<style lang='scss' scoped>
  @import '@/assets/scss/HealthReport/HealthReport.scss'
</style>
