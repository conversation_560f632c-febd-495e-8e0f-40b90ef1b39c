<template>
  <div class="wrapper">
    <div class="content">
      <div class="group" v-if="hasData === 0">
        <div class="carotid-nodata-img">
          <img src="../images/nodata.png" alt="nodata" />
          <p>30天没有新的测量记录</p>
          <p>记录了才会有报告哦</p>
        </div>
      </div>
      <div class="attention" v-if="reminder">
        <img src="../images/<EMAIL>" alt="attention" />
        <span>{{reminder}}</span>
      </div>
      <div class="group" v-if="pieChartProps">
        <p class="group-title">近30天血糖概述</p>
        <div class="familybp-wrapper">
          <div class="bp-pie-echart">
            <div class="bp-pie-echart-left">
              <pie-chart :pieChartProps="pieChartProps" class="bp-pie-echart"></pie-chart>
              <div class="bp-pie-echart-txt">
                <p class="bp-pie-echart-txt-up">血糖测量</p>
                <p class="bp-pie-echart-txt-down">
                  <span>{{overviewTotal}}</span>次
                </p>
              </div>
            </div>
            <div class="bp-pie-echart-right">
              <div class="bp-right-group">
                <i class="bp-group-sign bp-group-sign2"></i>
                <span class="bp-group-tips">偏高</span>
                <span
                  class="bp-group-ratio"
                  v-if="overview.up.percent != null"
                >{{overview.up.percent}}%</span>
                <span class="bp-group-num" v-if="overview.up.num != null">
                  <b>{{overview.up.num}}</b>次
                </span>
              </div>
              <div class="bp-right-group">
                <i class="bp-group-sign bp-group-sign3"></i>
                <span class="bp-group-tips">正常</span>
                <span
                  class="bp-group-ratio"
                  v-if="overview.normal.percent!= null"
                >{{overview.normal.percent}}%</span>
                <span class="bp-group-num" v-if="overview.normal.num != null">
                  <b>{{overview.normal.num}}</b>次
                </span>
              </div>
              <div class="bp-right-group">
                <i class="bp-group-sign bp-group-sign4"></i>
                <span class="bp-group-tips">偏低</span>
                <span
                  class="bp-group-ratio"
                  v-if="overview.down.percent != null"
                >{{overview.down.percent}}%</span>
                <span class="bp-group-num" v-if="overview.down.num != null">
                  <b>{{overview.down.num}}</b>次
                </span>
              </div>
            </div>
          </div>
          <div class="bg-box">
            <div>
              <p class="bg-top">{{overviewMax}}</p>
              <p class="bg-down">最高值</p>
            </div>
            <i></i>
            <div>
              <p class="bg-top">{{overviewMin}}</p>
              <p class="bg-down">最低值</p>
            </div>
          </div>
        </div>
        <div class="resultTips">参考《中国II型糖尿病防治指南2017年版》得出标准结论，实际控制指标以医生建议为准。</div>
      </div>
      <div class="group" v-if="barChartProps">
        <p class="group-title">近30天血糖异常原因记录次数</p>
        <bar-chart :barChartProps="barChartProps" class="bar-echart"></bar-chart>
      </div>
      <div class="group familybg-table-wrapper" v-if="records.length > 0">
        <div class="group-top">
          <p class="group-title">
            血糖记录
            <span class="group-title-unit">mmol/L</span>
          </p>
          <div class="curves-period">
            <!--<div-->
              <!--class="curves-btn"-->
              <!--:class="{'curves-btn-click': curvesData.leftClick && isLeftClick}"-->
              <!--@click="changeDate(0, curvesData.leftClick)"-->
            <!--&gt;-->
              <!--<span>&lsaquo;</span>-->
            <!--</div>-->
            <img v-if="curvesData.leftClick === 0" src="../images/left1.png" alt="" class="directionIcon">
            <img v-if="curvesData.leftClick !== 0" src="../images/left2.png" alt="" @click="changeDate(0, curvesData.leftClick)" class="directionIcon">
            <span
              class="curves-date"
            >{{curvesData.date[0] + "-" + curvesData.date[curvesData.date.length - 1]}}</span>
            <!--<div-->
              <!--class="curves-btn"-->
              <!--:class="{'curves-btn-click': curvesData.rightClick && isRightClick}"-->
              <!--@click="changeDate(1, curvesData.rightClick)"-->
            <!--&gt;-->
              <!--<span>&rsaquo;</span>-->
            <!--</div>-->
            <img v-if="curvesData.rightClick === 0" src="../images/right1.png" alt="" class="directionIcon">
            <img v-if="curvesData.rightClick !== 0" src="../images/right2.png" alt="" @click="changeDate(1, curvesData.rightClick)" class="directionIcon">
          </div>
        </div>
        <div class="familybg-table-content">
          <table class="familybg-table">
            <tr>
              <td rowspan="2">日期</td>
              <td rowspan="2">凌晨</td>
              <td colspan="2">早餐</td>
              <td colspan="2">午餐</td>
              <td colspan="2">晚餐</td>
              <td rowspan="2">睡前</td>
            </tr>
            <tr>
              <td>空腹</td>
              <td>后</td>
              <td>前</td>
              <td>后</td>
              <td>前</td>
              <td>后</td>
            </tr>
            <tr v-for="(item, index) in records" :key="index">
              <td>{{item.date}}</td>
              <td v-for="(td, ind) in item.value" :key="ind">
                <span :class="recordColor(td.normal)">{{td.bg}}</span>
              </td>
            </tr>
          </table>
        </div>
      </div>
      <div class="group" v-if="lineChartProps">
        <div class="group-top">
          <div class="group-title">
            <span>血糖曲线</span>
            <span class="group-title-unit">mmHg</span>
          </div>
          <div class="curves-period">
            <!--<div-->
              <!--class="curves-btn"-->
              <!--:class="{'curves-btn-click': curvesData.leftClick && isLeftClick}"-->
              <!--@click="changeDate(0, curvesData.leftClick)"-->
            <!--&gt;-->
              <!--<span>&lsaquo;</span>-->
            <!--</div>-->
            <img v-if="curvesData.leftClick === 0" src="../images/left1.png" alt="" class="directionIcon">
            <img v-if="curvesData.leftClick !== 0" src="../images/left2.png" alt="" @click="changeDate(0, curvesData.leftClick)" class="directionIcon">
            <span
              class="curves-date"
            >{{curvesData.date[0] + "-" + curvesData.date[curvesData.date.length - 1]}}</span>
            <!--<div-->
              <!--class="curves-btn"-->
              <!--:class="{'curves-btn-click': curvesData.rightClick && isRightClick}"-->
              <!--@click="changeDate(1, curvesData.rightClick)"-->
            <!--&gt;-->
              <!--<span>&rsaquo;</span>-->
            <!--</div>-->
            <img v-if="curvesData.rightClick === 0" src="../images/right1.png" alt="" class="directionIcon">
            <img v-if="curvesData.rightClick !== 0" src="../images/right2.png" alt="" @click="changeDate(1, curvesData.rightClick)" class="directionIcon">
          </div>
        </div>
        <div class="curves-tabs">
          <div class="curves-tabs-box">
            <div
              class="curves-item"
              v-for="tab in curvesTabs"
              :key="tab.index"
              @click="curvesActive = tab.index"
              :class="{'curves-item-active': curvesActive === tab.index, 'curves-item1': tab.name.length === 2, 'curves-item2': tab.name.length === 3}"
            >
              <span>{{tab.name}}</span>
              <img src="../images/tag-percent.png" alt="index" />
            </div>
          </div>
        </div>
        <line-chart
          class="line-echart"
          :lineChartProps="lineChartProps"
          :key="curvesActive + curvesData.currentPeriod.join()"
        ></line-chart>
      </div>
      <div class="group" v-if="recommendData">
        <p class="group-title">为您推荐</p>
        <report-recommend v-for="(item, index) in recommendData" :key="index" :data="item"></report-recommend>
      </div>
    </div>
    <footer>
      <footer-comp :footerProps="footerProps"></footer-comp>
    </footer>
  </div>
</template>

<script>
import ReportRecommend from '../components/ReportRecommend'
import FooterComp from '../components/FooterComp'
import LineChart from '../components/LineChart'
import PieChart from '../components/PieChart'
import BarChart from '../components/BarChart'

import { getExamData, getPeriodData } from '@/api/HealthReport'

export default {
  data() {
    return {
      checkItem: 'bg',
      hasData: '',
      visit_at: '',
      hosp_id: '',
      reminder: 0,
      overview: {
        up: {},
        down: {},
        normal: {}
      },
      overviewTotal: '',
      overviewMax: '',
      overviewMin: '',
      curvesData: {},
      curvesTabs: [
        {
          index: 8,
          name: '凌晨'
        },
        {
          index: 1,
          name: '空腹'
        },
        {
          index: 2,
          name: '早餐后'
        },
        {
          index: 3,
          name: '午餐前'
        },
        {
          index: 4,
          name: '午餐后'
        },
        {
          index: 5,
          name: '晚餐前'
        },
        {
          index: 6,
          name: '晚餐后'
        },
        {
          index: 7,
          name: '睡前'
        }
      ],
      curvesActive: 1,
      period: [],
      isLeftClick: 1,
      isRightClick: 1,
      records: [],
      recordsData: [],
      pieChartProps: 0,
      barChartProps: 0,
      lineChartProps: 0,
      recommendData: 0
    }
  },
  components: {
    'pie-chart': PieChart,
    'bar-chart': BarChart,
    'line-chart': LineChart,
    'footer-comp': FooterComp,
    'report-recommend': ReportRecommend
  },
  computed: {
    /**
     * 页脚跳转路由数据
     */
    footerProps() {
      return {
        hasNext: 1,
        btnTxt: '血压',
        visit_at: this.visit_at || '',
        hosp_id: this.hosp_id || '',
        path: '/guanjia/health/report/family/bp'
      }
    }
  },
  watch: {
    /**
     * 监听血糖曲线状态，更改折线图数据
     */
    curvesActive(newVal) {
      this.lineChartProps = {
        legendData: ['血糖'],
        value: [{ val: this.curvesData.bg[newVal] || [], name: '血糖' }],
        visit_at: this.curvesData.date || []
      }
    }
  },
  methods: {
    /**
     * 初始化
     */
    init() {
      let param = { checkItem: this.checkItem }
      this.visit_at = this.$route.query.visit_at || ''
      this.hosp_id = this.$route.query.hosp_id || ''
      if (this.visit_at !== '' && this.hosp_id !== '') {
        param.visit_at = this.visit_at
        param.hosp_id = this.hosp_id
      }
      // 获取检查报告数据
      getExamData(param).then(res => {
        if (res.status === 0) {
          // 处理检查报告数据
          this.handleData(res.data)
        } else {
          this.$toast(res.msg)
        }
      })
    },
    /**
     * 处理检查报告数据
     * @param {Object} data 检查报告数据
     */
    handleData(data) {
      if (data.hasData === 1) {
        this.hasData = 1
        this.reminder = data.reminder || ''
        this.period = data.period || []

        let curvesData = data.curves || {}
        this.curvesData.bg = curvesData.bg || {}
        this.curvesData.date = curvesData.date || []
        this.curvesData.leftClick = curvesData.leftClick || 0
        this.curvesData.rightClick = curvesData.rightClick || 0
        this.curvesData.currentPeriod = curvesData.currentPeriod || []

        let statics = data.statics || {}
        let number = statics.number || {}
        let percent = statics.percent || {}
        this.overviewTotal = statics.total != null ? statics.total : ''
        this.overviewMax = statics.high != null ? statics.high : ''
        this.overviewMin = statics.down != null ? statics.down : ''
        this.overview.up = { num: number.up, percent: percent.up }
        this.overview.down = { num: number.down, percent: percent.down }
        this.overview.normal = { num: number.normal, percent: percent.normal }
        let deviantList = statics.deviantList || {
          喝酒: 0,
          情绪: 0,
          抽烟: 0,
          用药: 0,
          睡眠: 0,
          胰岛素: 0,
          运动: 0,
          饮食: 0,
          其他: 0
        }

        let recordsData = Object.values(data.eightData || [])
        recordsData.forEach((record, i) => {
          let val = Object.values(record.value)
          val.unshift(val.pop())
          record.value = val
        })
        this.recordsData = recordsData
        let records = []
        this.curvesData.date.forEach(date => {
          this.recordsData.forEach(val => {
            if (val.date === date) records.push(val)
          })
        })
        this.records = records

        this.pieChartProps = [
          {
            value: number.normal != null ? number.normal : 0,
            color: '#5FDF98'
          },
          {
            value: number.up != null ? number.up : 0,
            color: '#EF7C69'
          },
          {
            value: number.down != null ? number.down : 0,
            color: '#73CCF6'
          }
        ]
        this.lineChartProps = {
          legendData: ['血糖'],
          value: [{ val: this.curvesData.bg[1] || [], name: '血糖' }],
          visit_at: this.curvesData.date || []
        }
        this.barChartProps = {
          xAxisData: Object.keys(deviantList),
          series: [Object.values(deviantList)]
        }

        this.recommendData = data.recommend || []
      } else {
        this.hasData = 0
      }
    },
    /**
     * 判断血糖记录颜色
     * @param {Number} normal 血糖水平状态（1：正常；2：过高；3：过低）
     * @return {String} c 类名
     */
    recordColor(normal) {
      let c = 'record-'
      switch (normal) {
        case 2:
          c += 'up'
          break
        case 3:
          c += 'down'
          break
        default:
          c = ''
          break
      }
      return c
    },
    /**
     * 改变曲线图日期
     * @param {Number} flag 更改日期方向（0：左，1：右）
     * @param {Number} hasPeriod 是否有日期（0：无，1：有）
     */
    changeDate(flag, hasPeriod) {
      let isClick = ''
      if (flag) {
        isClick = 'isRightClick'
      } else {
        isClick = 'isLeftClick'
      }
      if (hasPeriod && this[isClick]) {
        this[isClick] = 0
        getPeriodData({
          checkItem: this.checkItem,
          params: {
            visit_period: this.period.join(),
            current_date: this.curvesData.currentPeriod[flag],
            flag: flag
          }
        }).then(res => {
          if (res.status === 0) {
            let data = res.data || {}
            this.curvesData.bg = data.bg || {}
            this.curvesData.date = data.date || []
            this.curvesData.leftClick = data.leftClick || 0
            this.curvesData.rightClick = data.rightClick || 0
            this.curvesData.currentPeriod = data.currentPeriod || []
            this.lineChartProps = {
              legendData: ['血糖'],
              value: [{ val: data.bg[this.curvesActive] || [], name: '血糖' }],
              visit_at: data.date || []
            }
            let records = []
            this.curvesData.date.forEach(date => {
              this.recordsData.forEach(val => {
                if (val.date === date) records.push(val)
              })
            })
            this.records = records
          }
          this[isClick] = 1
        })
      }
    }
  },
  created() {
    this.$nextTick(() => {
      this.init()
    })
  }
}
</script>

<style lang="scss" scoped>
@import "@/assets/scss/HealthReport/HealthReportExam.scss";
</style>
