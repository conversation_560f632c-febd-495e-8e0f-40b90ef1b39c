<template>
  <div class="wrapper">
    <div class="content">
      <div v-if="hasData === 0" class="group">
        <div class="carotid-nodata-img">
          <img src="../images/nodata.png" alt="nodata" />
          <p>30天没有新的测量记录</p>
          <p>记录了才会有报告哦</p>
        </div>
      </div>
      <div class="attention" v-if="reminder">
        <img src="../images/<EMAIL>" alt="attention" />
        <span>{{reminder}}</span>
      </div>
      <div class="group" v-if="pieChartProps">
        <p class="group-title">近30天血压概述</p>
        <div class="familybp-wrapper">
          <div class="bp-pie-echart">
            <div class="bp-pie-echart-left">
              <pie-chart :pieChartProps="pieChartProps" class="bp-pie-echart"></pie-chart>
              <div class="bp-pie-echart-txt">
                <p class="bp-pie-echart-txt-up">血压测量</p>
                <p class="bp-pie-echart-txt-down">
                  <span>{{overviewTotal}}</span>次
                </p>
              </div>
            </div>
            <div class="bp-pie-echart-right">
              <div class="bp-right-group">
                <i class="bp-group-sign bp-group-sign1"></i>
                <span class="bp-group-tips">极高</span>
                <span
                  class="bp-group-ratio"
                  v-if="overview.max.percent != null"
                >{{overview.max.percent}}%</span>
                <span class="bp-group-num" v-if="overview.max.num != null">
                  <b>{{overview.max.num}}</b>次
                </span>
              </div>
              <div class="bp-right-group">
                <i class="bp-group-sign bp-group-sign2"></i>
                <span class="bp-group-tips">偏高</span>
                <span
                  class="bp-group-ratio"
                  v-if="overview.medium.percent != null"
                >{{overview.medium.percent}}%</span>
                <span class="bp-group-num" v-if="overview.medium.num != null">
                  <b>{{overview.medium.num}}</b>次
                </span>
              </div>
              <div class="bp-right-group">
                <i class="bp-group-sign bp-group-sign3"></i>
                <span class="bp-group-tips">正常</span>
                <span
                  class="bp-group-ratio"
                  v-if="overview.normal.percent != null"
                >{{overview.normal.percent}}%</span>
                <span class="bp-group-num" v-if="overview.normal.num != null">
                  <b>{{overview.normal.num}}</b>次
                </span>
              </div>
              <div class="bp-right-group">
                <i class="bp-group-sign bp-group-sign4"></i>
                <span class="bp-group-tips">偏低</span>
                <span
                  class="bp-group-ratio"
                  v-if="overview.min.percent != null"
                >{{overview.min.percent}}%</span>
                <span class="bp-group-num" v-if="overview.min.num != null">
                  <b>{{overview.min.num}}</b>次
                </span>
              </div>
            </div>
          </div>
          <div class="bp-box">
            <div>
              <p class="bp-box-title">收缩压</p>
              <div class="bp-content">
                <div>
                  <p class="bp-top">{{overviewSbpMax}}</p>
                  <p class="bp-down">最高值</p>
                </div>
                <div>
                  <p class="bp-top">{{overviewSbpMin}}</p>
                  <p class="bp-down">最低值</p>
                </div>
              </div>
            </div>
            <div>
              <p class="bp-box-title bp-box-title2">舒张压</p>
              <div class="bp-content">
                <div>
                  <p class="bp-top">{{overviewDbpMax}}</p>
                  <p class="bp-down">最高值</p>
                </div>
                <div>
                  <p class="bp-top">{{overviewDbpMin}}</p>
                  <p class="bp-down">最低值</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="resultTips">参考《2019家庭血压监测指南》得出标准结论，实际控制指标以医生建议为准。</div>
      </div>
      <div class="group" v-if="barChartProps">
        <p class="group-title">近30天血压异常原因记录次数</p>
        <bar-chart :barChartProps="barChartProps" class="bar-echart"></bar-chart>
      </div>
      <div class="group" v-if="lineChartProps">
        <div class="group-top">
          <div class="group-title">
            <span>血压曲线</span>
            <span class="group-title-unit">mmol/L</span>
          </div>
          <div class="curves-period">
            <img v-if="curvesData.leftClick === 0" src="../images/left1.png" alt="" class="directionIcon">
            <img v-if="curvesData.leftClick !== 0" src="../images/left2.png" alt="" @click="changeDate(0, curvesData.leftClick)" class="directionIcon">
            <!--<div-->
              <!--class="curves-btn"-->
              <!--:class="{'curves-btn-click': curvesData.leftClick && isLeftClick}"-->
              <!--@click="changeDate(0, curvesData.leftClick)"-->
            <!--&gt;-->
              <!--<span>&lsaquo;</span>-->
            <!--</div>-->
            <span
              class="curves-date"
            >{{curvesData.date[0] + "-" + curvesData.date[curvesData.date.length - 1]}}</span>
            <!--<div-->
            <!--class="curves-btn"-->
            <!--:class="{'curves-btn-click': curvesData.rightClick && isRightClick}"-->
            <!--@click="changeDate(1, curvesData.rightClick)"-->
          <!--&gt;-->
            <!--<span>&rsaquo;</span>-->
          <!--</div>-->
            <img v-if="curvesData.rightClick === 0" src="../images/right1.png" alt="" class="directionIcon">
            <img v-if="curvesData.rightClick !== 0" src="../images/right2.png" alt="" @click="changeDate(1, curvesData.rightClick)" class="directionIcon">
          </div>
        </div>
        <line-chart
          class="line-echart"
          :lineChartProps="lineChartProps"
          :key="curvesData.currentPeriod.join()"
        ></line-chart>
      </div>
      <div class="group" v-if="recommendData">
        <p class="group-title">为您推荐</p>
        <report-recommend v-for="(item, index) in recommendData" :key="index" :data="item"></report-recommend>
      </div>
    </div>
    <footer>
      <footer-comp :footerProps="footerProps"></footer-comp>
    </footer>
  </div>
</template>

<script>
import ReportRecommend from '../components/ReportRecommend'
import FooterComp from '../components/FooterComp'
import LineChart from '../components/LineChart'
import PieChart from '../components/PieChart'
import BarChart from '../components/BarChart'

import { getExamData, getPeriodData } from '@/api/HealthReport'

export default {
  data() {
    return {
      checkItem: 'bp',
      hasData: '',
      visit_at: '',
      hosp_id: '',
      reminder: 0,
      curvesData: {
        date: [],
        currentPeriod: [],
        leftClick: 0,
        rightClick: 0
      },
      period: [],
      isLeftClick: 1,
      isRightClick: 1,
      overview: {
        max: {},
        min: {},
        medium: {},
        normal: {}
      },
      overviewTotal: '',
      overviewSbpMax: '',
      overviewSbpMin: '',
      overviewDbpMax: '',
      overviewDbpMin: '',
      pieChartProps: 0,
      barChartProps: 0,
      lineChartProps: 0,
      recommendData: 0
    }
  },
  components: {
    'pie-chart': PieChart,
    'bar-chart': BarChart,
    'line-chart': LineChart,
    'footer-comp': FooterComp,
    'report-recommend': ReportRecommend
  },
  computed: {
    /**
     * 页脚跳转路由数据
     */
    footerProps() {
      return {
        hasNext: 1,
        btnTxt: '运动',
        visit_at: this.visit_at || '',
        hosp_id: this.hosp_id || '',
        path: '/guanjia/health/report/family/motion'
      }
    }
  },
  methods: {
    /**
     * 初始化
     */
    init() {
      let param = { checkItem: this.checkItem }
      this.visit_at = this.$route.query.visit_at || ''
      this.hosp_id = this.$route.query.hosp_id || ''
      if (this.visit_at !== ' && this.hosp_id !== ') {
        param.visit_at = this.visit_at
        param.hosp_id = this.hosp_id
      }

      // 获取检查报告数据
      getExamData(param).then(res => {
        if (res.status === 0) {
          // 处理检查报告数据
          this.handleData(res.data)
        } else {
          this.$toast(res.msg)
        }
      })
    },
    /**
     * 处理检查报告数据
     * @param {Object} data 检查报告数据
     */
    handleData(data) {
      if (data.hasData === 1) {
        this.hasData = 1
        this.reminder = data.reminder || ''

        let number = data.number || {}
        let percent = data.percent || {}
        let upDown = data.upDown || {}
        let deviantList = data.deviantList || {
          喝酒: 0,
          情绪: 0,
          抽烟: 0,
          用药: 0,
          睡眠: 0,
          胰岛素: 0,
          运动: 0,
          饮食: 0,
          其他: 0
        }

        this.curvesData.date = data.date || []
        this.curvesData.leftClick = data.leftClick || 0
        this.curvesData.rightClick = data.rightClick || 0
        this.curvesData.currentPeriod = data.currentPeriod || []
        this.period = data.period || []
        // 概述
        this.overviewTotal = data.total || ''
        this.overviewSbpMax = upDown.sbp_high != null ? upDown.sbp_high : ''
        this.overviewSbpMin = upDown.sbp_down != null ? upDown.sbp_down : ''
        this.overviewDbpMax = upDown.dbp_high != null ? upDown.dbp_high : ''
        this.overviewDbpMin = upDown.dbp_down != null ? upDown.dbp_down : ''
        this.overview.max = { num: number.max, percent: percent.max }
        this.overview.min = { num: number.min, percent: percent.min }
        this.overview.medium = { num: number.medium, percent: percent.medium }
        this.overview.normal = { num: number.normal, percent: percent.normal }

        // 图表
        this.pieChartProps = [
          {
            value: number.normal != null ? number.normal : '',
            color: '#5FDF98'
          },
          {
            value: number.medium != null ? number.medium : '',
            color: '#EF7C69'
          },
          {
            value: number.min != null ? number.min : '',
            color: '#73CCF6'
          },
          {
            value: number.max != null ? number.max : '',
            color: '#C44353'
          }
        ]
        this.lineChartProps = {
          legendData: ['收缩压', '舒张压'],
          value: [
            {
              val: data.sbp || [],
              name: '收缩压',
              color: '#F67710'
            },
            {
              val: data.dbp || [],
              name: '舒张压',
              color: '#61B1F0'
            }
          ],
          visit_at: data.date || []
        }
        this.barChartProps = {
          xAxisData: Object.keys(deviantList),
          series: [Object.values(deviantList)]
        }

        this.recommendData = data.recommend || []
      } else {
        this.hasData = 0
      }
    },
    /**
     * 改变曲线图日期
     * @param {Number} flag 更改日期方向（0：左，1：右）
     * @param {Number} hasPeriod 是否有日期（0：无，1：有）
     */
    changeDate(flag, hasPeriod) {
      let isClick = ''
      if (flag) {
        isClick = 'isRightClick'
      } else {
        isClick = 'isLeftClick'
      }
      if (hasPeriod && this[isClick]) {
        this[isClick] = 0

        getPeriodData({
          checkItem: this.checkItem,
          params: {
            visit_period: this.period.join(),
            current_date: this.curvesData.currentPeriod[flag],
            flag: flag
          }
        }).then(res => {
          console.log(res)
          if (res.status === 0) {
            let data = res.data || {}
            this.curvesData.date = data.date || []
            this.curvesData.leftClick = data.leftClick || 0
            this.curvesData.rightClick = data.rightClick || 0
            this.curvesData.currentPeriod = data.currentPeriod || []
            this.lineChartProps = {
              legendData: ['收缩压', '舒张压'],
              value: [
                {
                  val: data.sbp || [],
                  name: '收缩压',
                  color: '#F67710'
                },
                {
                  val: data.dbp || [],
                  name: '舒张压',
                  color: '#61B1F0'
                }
              ],
              visit_at: data.date || []
            }
          }
          this[isClick] = 1
        })
      }
    }
  },
  created() {
    this.$nextTick(() => {
      this.init()
    })
  }
}
</script>

<style lang='scss' scoped>
@import '@/assets/scss/HealthReport/HealthReportExam.scss';
</style>
