<template>
  <div class="wrapper">
    <div class="content">
      <div class="group" v-if="hasData === 0">
        <div class="carotid-nodata-img">
          <img src="../images/nodata.png" alt="nodata" />
          <p>30天没有新的测量记录</p>
          <p>记录了才会有报告哦</p>
        </div>
      </div>
      <div class="attention" v-if="reminder">
        <img src="../images/<EMAIL>" alt="attention" />
        <span>{{reminder}}</span>
      </div>
      <div class="group" v-if="lineChartProps">
        <div class="group-top">
          <div class="group-title">
            <span>步数（30天）</span>
          </div>
          <div class="curves-period">
            <div
              class="curves-btn"
              :class="{'curves-btn-click': curvesData.leftClick && isLeftClick}"
              @click="changeDate(0, curvesData.leftClick)"
            >
              <span>&lsaquo;</span>
            </div>
            <span
              class="curves-date"
            >{{curvesData.date[0] + "-" + curvesData.date[curvesData.date.length - 1]}}</span>
            <div
              class="curves-btn"
              :class="{'curves-btn-click': curvesData.rightClick && isRightClick}"
              @click="changeDate(1, curvesData.rightClick)"
            >
              <span>&rsaquo;</span>
            </div>
          </div>
        </div>
        <line-chart
          class="line-echart"
          :lineChartProps="lineChartProps"
          :key="curvesData.currentPeriod.join()"
        ></line-chart>
      </div>
      <div class="group" v-if="recommendData">
        <p class="group-title">为您推荐</p>
        <report-recommend v-for="(item, index) in recommendData" :key="index" :data="item"></report-recommend>
      </div>
    </div>
    <footer>
      <footer-comp :footerProps="footerProps"></footer-comp>
    </footer>
  </div>
</template>

<script>
import ReportRecommend from '../components/ReportRecommend'
import FooterComp from '../components/FooterComp'
import LineChart from '../components/LineChart'

import { getExamData, getPeriodData } from '@/api/HealthReport'

export default {
  data() {
    return {
      checkItem: 'motion',
      hasData: '',
      visit_at: '',
      hosp_id: '',
      reminder: 0,
      curvesData: {
        date: [],
        currentPeriod: [],
        leftClick: 0,
        rightClick: 0
      },
      period: [],
      isLeftClick: 1,
      isRightClick: 1,
      lineChartProps: 0,
      recommendData: 0
    }
  },
  components: {
    'line-chart': LineChart,
    'footer-comp': FooterComp,
    'report-recommend': ReportRecommend
  },
  computed: {
    /**
     * 页脚跳转路由数据
     */
    footerProps() {
      return {
        hasNext: 1,
        btnTxt: 'BMI',
        visit_at: this.visit_at || '',
        hosp_id: this.hosp_id || '',
        path: '/guanjia/health/report/family/bmi'
      }
    }
  },
  methods: {
    /**
     * 初始化
     */
    init() {
      let param = { checkItem: this.checkItem }
      this.visit_at = this.$route.query.visit_at || ''
      this.hosp_id = this.$route.query.hosp_id || ''
      if (this.visit_at !== '' && this.hosp_id !== '') {
        param.visit_at = this.visit_at
        param.hosp_id = this.hosp_id
      }

      // 获取检查报告数据
      getExamData(param).then(res => {
        if (res.status === 0) {
          // 处理检查报告数据
          this.handleData(res.data)
        } else {
          this.$toast(res.msg)
        }
      })
    },
    /**
     * 处理家庭测量数据
     * @param {Object} data 检查报告数据
     */
    handleData(data) {
      if (data.hasData === 1) {
        this.hasData = 1
        this.reminder = data.reminder || ''
        this.curvesData.date = data.date || []
        this.curvesData.leftClick = data.leftClick || 0
        this.curvesData.rightClick = data.rightClick || 0
        this.curvesData.currentPeriod = data.currentPeriod || []
        this.period = data.period || []
        this.lineChartProps = {
          top: '9%',
          height: '90%',
          value: [{ val: data.motions || [] }],
          visit_at: data.date || []
        }
        this.recommendData = data.recommend || []
      } else {
        this.hasData = 0
      }
    },
    /**
     * 改变曲线图日期
     * @param {Number} flag 更改日期方向（0：左，1：右）
     * @param {Number} hasPeriod 是否有日期（0：无，1：有）
     */
    changeDate(flag, hasPeriod) {
      let isClick = ''
      if (flag) {
        isClick = 'isRightClick'
      } else {
        isClick = 'isLeftClick'
      }
      if (hasPeriod && this[isClick]) {
        this[isClick] = 0

        getPeriodData({
          checkItem: this.checkItem,
          params: {
            visit_period: this.period.join(),
            current_date: this.curvesData.currentPeriod[flag],
            flag: flag
          }
        }).then(res => {
          console.log(res)
          if (res.status === 0) {
            let data = res.data || {}
            this.curvesData.date = data.date || []
            this.curvesData.leftClick = data.leftClick || 0
            this.curvesData.rightClick = data.rightClick || 0
            this.curvesData.currentPeriod = data.currentPeriod || []
            this.lineChartProps = {
              top: '9%',
              height: '90%',
              legendData: ['BMI'],
              value: [
                {
                  val: data.motions || [],
                  name: 'BMI',
                  color: '#F67710'
                }
              ],
              visit_at: data.date || []
            }
          }
          this[isClick] = 1
        })
      }
    }
  },
  created() {
    this.$nextTick(() => {
      this.init()
    })
  }
}
</script>

<style lang='scss' scoped>
@import '@/assets/scss/HealthReport/HealthReportExam.scss';
</style>
