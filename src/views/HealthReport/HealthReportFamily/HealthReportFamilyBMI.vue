<template>
  <div class="wrapper">
    <div class="content">
      <div class="group" v-if="hasData === 0">
        <div class="carotid-nodata-img">
          <img src="../images/nodata.png" alt="nodata" />
          <p>30天没有新的测量记录</p>
          <p>记录了才会有报告哦</p>
        </div>
      </div>
      <div class="attention" v-if="reminder">
        <img src="../images/<EMAIL>" alt="attention" />
        <span>{{reminder}}</span>
      </div>
      <div class="group" v-if="bmiVal">
        <p class="group-title">家庭BMI（30天）</p>
        <div class="bmi-wrapper">
          <div class="bmi-group">
            <p class="bmi-group-val">{{weightVal}}</p>
            <p class="bmi-group-name">体重</p>
          </div>
          <div class="bmi-group">
            <p class="bmi-group-val">{{heightVal}}</p>
            <p class="bmi-group-name">身高</p>
          </div>
          <div class="bmi-group">
            <p class="bmi-group-val" :style="bmiStyle">{{bmiVal}}</p>
            <p class="bmi-group-name">BMI</p>
          </div>
        </div>
        <bmi-index :bmiVal="bmiVal"></bmi-index>
      </div>
      <div class="group" v-if="lineChartProps">
        <div class="group-top">
          <div class="group-title">
            <span>BMI曲线</span>
            <span class="group-title-unit">kg/m²</span>
          </div>
          <div class="curves-period">
            <div
              class="curves-btn"
              :class="{'curves-btn-click': curvesData.leftClick && isLeftClick}"
              @click="changeDate(0, curvesData.leftClick)"
            >
              <span>&lsaquo;</span>
            </div>
            <span
              class="curves-date"
            >{{curvesData.date[0] + "-" + curvesData.date[curvesData.date.length - 1]}}</span>
            <div
              class="curves-btn"
              :class="{'curves-btn-click': curvesData.rightClick && isRightClick}"
              @click="changeDate(1, curvesData.rightClick)"
            >
              <span>&rsaquo;</span>
            </div>
          </div>
        </div>
        <line-chart
          class="line-echart"
          :lineChartProps="lineChartProps"
          :key="curvesData.currentPeriod.join()"
        ></line-chart>
      </div>
      <div class="group" v-if="recommendData">
        <p class="group-title">为您推荐</p>
        <report-recommend v-for="(item, index) in recommendData" :key="index" :data="item"></report-recommend>
      </div>
    </div>
    <footer>
      <footer-comp :footerProps="footerProps"></footer-comp>
    </footer>
  </div>
</template>

<script>
import ReportRecommend from '../components/ReportRecommend'
import FooterComp from '../components/FooterComp'
import LineChart from '../components/LineChart'
import BMIIndex from '../components/BMIIndex'

import { getExamData, getPeriodData } from '@/api/HealthReport'

export default {
  data() {
    return {
      checkItem: 'bmi',
      hasData: '',
      visit_at: '',
      hosp_id: '',
      reminder: 0,
      curvesData: {
        date: [],
        currentPeriod: [],
        leftClick: 0,
        rightClick: 0
      },
      period: [],
      isLeftClick: 1,
      isRightClick: 1,
      bmiVal: 0,
      heightVal: 0,
      weightVal: 0,
      lineChartProps: 0,
      recommendData: 0
    }
  },
  components: {
    'bmi-index': BMIIndex,
    'line-chart': LineChart,
    'footer-comp': FooterComp,
    'report-recommend': ReportRecommend
  },
  computed: {
    // BMI数值颜色计算
    bmiStyle() {
      let style = {}
      let bmiVal = this.bmiVal
      if (bmiVal > 0 && bmiVal <= 18.5) style.color = '#73CCF6'
      if (bmiVal > 18.5 && bmiVal <= 24) style.color = '#92E464'
      if (bmiVal > 24 && bmiVal <= 28) style.color = '#FCC569'
      if (bmiVal > 28) style.color = '#EF7C69'
      return style
    },
    /**
     * 页脚跳转路由数据
     */
    footerProps() {
      return {
        hasNext: 1,
        btnTxt: '糖化血红蛋白',
        visit_at: this.visit_at || '',
        hosp_id: this.hosp_id || '',
        path: '/guanjia/health/report/hosp/hemoglobin'
      }
    }
  },
  methods: {
    /**
     * 初始化
     */
    init() {
      let param = { checkItem: this.checkItem }
      this.visit_at = this.$route.query.visit_at || ''
      this.hosp_id = this.$route.query.hosp_id || ''
      if (this.visit_at !== '' && this.hosp_id !== '') {
        param.visit_at = this.visit_at
        param.hosp_id = this.hosp_id
      }

      // 获取家庭测量数据
      getExamData(param).then(res => {
        if (res.status === 0) {
          // 处理家庭测量数据
          this.handleData(res.data)
        } else {
          this.$toast(res.msg)
        }
      })
    },
    /**
     * 处理家庭测量数据
     * @param {Object} data 检查报告数据
     */
    handleData(data) {
      if (data.hasData === 1) {
        this.hasData = 1
        this.reminder = data.reminder || ''
        this.curvesData.date = data.date || []
        this.curvesData.leftClick = data.leftClick || 0
        this.curvesData.rightClick = data.rightClick || 0
        this.curvesData.currentPeriod = data.currentPeriod || []
        this.period = data.period || []
        let lastData = data.lastData || {}
        this.bmiVal = Number(lastData.bmi != null ? lastData.bmi : 0)
        this.heightVal = lastData.height != null ? lastData.height : 0
        this.weightVal = lastData.weight != null ? lastData.weight : 0
        this.lineChartProps = {
          legendData: ['BMI'],
          value: [
            {
              val: data.bmi || [],
              name: 'BMI'
            }
          ],
          visit_at: data.date || []
        }

        this.recommendData = data.recommend || []
      } else {
        this.hasData = 0
      }
    },
    /**
     * 改变曲线图日期
     * @param {Number} flag 更改日期方向（0：左，1：右）
     * @param {Number} hasPeriod 是否有日期（0：无，1：有）
     */
    changeDate(flag, hasPeriod) {
      let isClick = ''
      if (flag) {
        isClick = 'isRightClick'
      } else {
        isClick = 'isLeftClick'
      }
      if (hasPeriod && this[isClick]) {
        this[isClick] = 0

        getPeriodData({
          checkItem: this.checkItem,
          params: {
            visit_period: this.period.join(),
            current_date: this.curvesData.currentPeriod[flag],
            flag: flag
          }
        }).then(res => {
          console.log(res)
          if (res.status === 0) {
            let data = res.data || {}
            this.curvesData.date = data.date || []
            this.curvesData.leftClick = data.leftClick || 0
            this.curvesData.rightClick = data.rightClick || 0
            this.curvesData.currentPeriod = data.currentPeriod || []
            this.lineChartProps = {
              legendData: ['BMI'],
              value: [
                {
                  val: data.bmi || [],
                  name: 'BMI',
                  color: '#F67710'
                }
              ],
              visit_at: data.date || []
            }
          }
          this[isClick] = 1
        })
      }
    }
  },
  created() {
    this.$nextTick(() => {
      this.init()
    })
  }
}
</script>

<style lang='scss' scoped>
@import '@/assets/scss/HealthReport/HealthReportExam.scss';
</style>
