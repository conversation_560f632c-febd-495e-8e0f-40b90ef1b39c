<template>
  <div ref="wrapper" class="wrapper">
    <div class="report">
      <div class="report-top">
        <div class="report-info">
          <img src="./images/<EMAIL>" alt="hos"/>
          <span>{{hospName}}</span>
        </div>
        <div class="report-info">
          <img src="./images/<EMAIL>" alt="clock"/>
          <span class="report-time">{{time}}</span>
        </div>
      </div>
      <div class="report-general">
        <!-- 糖化血红蛋白 -->
        <div v-if="Number(hospMeasure.s_876.empty)===1"
             @click="goRouter('/guanjia/health/report/hemoglobin/his')"
             class="report-item report-item-hemoglobin"
        >
          <div class="report-item-name">
            <span class="report-item-txt">糖化血红蛋白</span>
          </div>
          <div class="report-item-value">
            <div class="report-item-num">
              <span class="report-item-val">{{hospMeasure.s_876.val}}</span>
              <img v-if="Number(hospMeasure.s_876.normal) === 1" src="./images/low.png" alt="low"/>
              <img v-if="Number(hospMeasure.s_876.normal) === 2" src="./images/high.png" alt="high"/>
            </div>
            <img src="./images/arrow_right2.png" alt="go" class="report-item-go"/>
          </div>
          <div class="report-item-refer">
            <span  v-if="hospMeasure.s_876.reference!==''">参考值</span>
            <span class="report-item-range">
                {{hospMeasure.s_876.reference}}
                {{hospMeasure.s_876.unit}}
              </span>
          </div>
        </div>
        <!-- 院内血压 -->
        <div v-if="Number(hospMeasure.hospBp.empty)===1"
             @click="goRouter('/guanjia/health/report/bp/his')"
             class="report-item report-item-bp"
        >
          <div class="report-item-name">
            <span class="report-item-txt">院内血压</span>
          </div>
          <div class="report-item-value">
            <div class="report-item-num">
              <span class="report-item-val">{{hospMeasure.hospBp.val}}</span>
              <img v-if="hospMeasure.hospBp.normal === 1" src="./images/low.png" alt="low"/>
              <img v-if="hospMeasure.hospBp.normal === 2" src="./images/high.png" alt="high"/>
            </div>
            <img src="./images/arrow_right2.png" alt="go" class="report-item-go"/>
          </div>
          <div class="report-item-refer" >
            <span v-if="hospMeasure.hospBp.reference!==''">参考值</span>
            <span class="report-item-range">
                {{hospMeasure.hospBp.reference}}
                {{hospMeasure.hospBp.unit}}
              </span>
          </div>
        </div>
        <!-- 尿白蛋白比肌酐 -->
        <div v-if="Number(hospMeasure.s_901013.empty)===1"
             @click="goRouter('/guanjia/health/report/creatinine/his')"
             class="report-item report-item-creatinine"
        >
          <div class="report-item-name">
            <span class="report-item-txt">尿白蛋白比肌酐</span>
          </div>
          <div class="report-item-value">
            <div class="report-item-num">
              <span class="report-item-val">{{hospMeasure.s_901013.val}}</span>
              <img v-if="hospMeasure.s_901013.normal === 1" src="./images/low.png" alt="low"/>
              <img v-if="hospMeasure.s_901013.normal === 2" src="./images/high.png" alt="high"/>
            </div>
            <img src="./images/arrow_right2.png" alt="go" class="report-item-go"/>
          </div>
          <div class="report-item-refer" >
            <span v-if="hospMeasure.s_901013.reference!==''">参考值</span>
            <span class="report-item-range">
                {{hospMeasure.s_901013.reference}}
                {{hospMeasure.s_901013.unit}}
              </span>
          </div>
        </div>
        <!-- BMI -->
        <div v-if="Number(hospMeasure.bmi.empty)===1"
             @click="goRouter('/guanjia/health/report/bmi/his')"
             class="report-item report-item-bmi"
        >
          <div class="report-item-name">
            <span class="report-item-txt">BMI</span>
          </div>
          <div class="report-item-value">
            <div class="report-item-num">
              <span class="report-item-val">{{hospMeasure.bmi.val}}</span>
              <img v-if="hospMeasure.bmi.normal === 3" src="./images/low.png" alt="low"/>
              <img v-if="hospMeasure.bmi.normal === 2" src="./images/high.png" alt="high"/>
            </div>
            <div
              v-if="hospMeasure.bmi.normalExt != null"
              class="report-item-remind"
              :class="hospMeasure.bmi.normalClass"
            >{{hospMeasure.bmi.normalExt}}
            </div>
            <img src="./images/arrow_right2.png" alt="go" class="report-item-go"/>
          </div>
          <div class="report-item-refer" >
            <span v-if="hospMeasure.bmi.reference!==''">参考值</span>
            <span class="report-item-range">
                {{hospMeasure.bmi.reference}}
                {{hospMeasure.bmi.unit}}
              </span>
          </div>
        </div>
        <!-- 血脂 -->
        <div v-if="Number(hospMeasure.blood_lipid.empty)===1"
             @click="goRouter('/guanjia/health/report/lipid/his')"
             class="report-item report-item-lipid"
        >
          <div class="report-item-name">
            <span class="report-item-txt">血脂</span>
            <img src="./images/arrow_right2.png" alt="go" class="report-item-go"/>
          </div>
          <div class="report-item-tips">
            <p>{{hospMeasure.blood_lipid.conclusion}}</p>
          </div>
        </div>
        <!-- 动脉硬化检测 -->
        <div v-if="Number(hospMeasure.pwv.empty)===1"
          @click="goRouter('/guanjia/health/report/arteriosclerosis/his')"
          class="report-item report-item-arteriosclerosis"
        >
          <div class="report-item-name">
            <span class="report-item-txt">动脉硬化检测</span>
            <img src="./images/arrow_right2.png" alt="go" class="report-item-go"/>
          </div>
          <div class="report-item-tips" >
            <p>查看结论</p>
            <!--<div class="report-item-date-no-tab">-->
              <!--<span>{{hospMeasure.pwv.date}}</span>-->
            <!--</div>-->
          </div>
        </div>
        <!-- 周围神经病变检测 -->
        <div v-if="Number(hospMeasure.ner_sur.empty)===1"
             @click="goRouter('/guanjia/health/report/neuropathy/his')"
             class="report-item report-item-neuropathy"
        >
          <div class="report-item-name">
            <span class="report-item-txt">周围神经病变检测</span>
            <img src="./images/arrow_right2.png" alt="go" class="report-item-go"/>
          </div>
          <div>
            <div class="report-item-tips">
              <p>查看结论</p>
              <!--              <div class="report-item-date-no-tab">-->
              <!--                <span>{{hospMeasure.ner_sur.date}}</span>-->
              <!--              </div>-->
            </div>
          </div>
        </div>
        <!-- 内脏脂肪面积 -->
        <div v-if="Number(hospMeasure.vat.empty)===1"
             @click="goRouter('/guanjia/health/report/vat/his')"
             class="report-item report-item-vat"
        >
          <div class="report-item-name">
            <span class="report-item-txt">内脏脂肪面积</span>
          </div>
          <div class="report-item-value">
            <div class="report-item-num">
              <span class="report-item-val">{{hospMeasure.vat.val}}</span>
              <img v-if="hospMeasure.vat.normal === 1" src="./images/low.png" alt="low"/>
              <img v-if="hospMeasure.vat.normal === 2" src="./images/high.png" alt="high"/>
            </div>
            <img src="./images/arrow_right2.png" alt="go" class="report-item-go"/>
          </div>
          <div class="report-item-refer">
            <span  v-if="hospMeasure.vat.reference!==''">参考值</span>
            <span class="report-item-range">
                {{hospMeasure.vat.reference}}
                {{hospMeasure.vat.unit}}
              </span>
          </div>
        </div>
        <!-- 眼底筛查 -->
        <div v-if="Number(hospMeasure.s_945.empty)===1"
             @click="goRouter('/guanjia/health/report/sugarnet/his')"
             class="report-item report-item-sugarnet"
        >
          <div class="report-item-name">
            <span class="report-item-txt">眼底筛查</span>
            <img src="./images/arrow_right2.png" alt="go" class="report-item-go"/>
          </div>
          <div class="report-item-eye">
            <div class="item-eye-info eye-info-left">
              <p class="eye-info-top">左眼</p>
              <p>{{hospMeasure.s_945.val}}</p>
            </div>
            <i class="item-eye-line"></i>
            <div class="item-eye-info eye-info-right">
              <p class="eye-info-top">右眼</p>
              <p>{{hospMeasure.s_946.val}}</p>
            </div>
          </div>
        </div>
        <!-- 肝肾、甲状腺功能 -->
        <div v-if="Number(hospMeasure.liver_kidney.empty)===1"
             @click="goRouter('/guanjia/health/report/liver/his')"
             class="report-item report-item-liver"
        >
          <div class="report-item-name">
            <span class="report-item-txt">肝肾、甲状腺功能</span>
            <img src="./images/arrow_right2.png" alt="go" class="report-item-go"/>
          </div>
          <div>
            <div class="report-item-tips">
              <p>{{hospMeasure.liver_kidney.conclusion}}</p>
            </div>
            <!--            <div class="report-item-date">-->
            <!--              <span>{{hospMeasure.liver_kidney.date}}</span>-->
            <!--            </div>-->
          </div>
        </div>
        <!-- 颈动脉超声 -->
        <div v-if="Number(hospMeasure.carotid.empty)===1"
             @click="goRouter('/guanjia/health/report/carotid/his')"
             class="report-item report-item-carotid"
        >
          <div class="report-item-name">
            <span class="report-item-txt">颈动脉超声</span>
            <img src="./images/arrow_right2.png" alt="go" class="report-item-go"/>
          </div>
          <div class="report-item-tips">
            <p>查看结论</p>
            <!--            <div class="report-item-date-no-tab">-->
            <!--              <span>{{hospMeasure.carotid.date}}</span>-->
            <!--            </div>-->
          </div>
        </div>
        <!-- 心超、心电图 -->
        <div v-if="Number(hospMeasure.heart.empty)===1"
             @click="goRouter('/guanjia/health/report/heart/his')"
             class="report-item report-item-heart"
        >
          <div class="report-item-name">
            <span class="report-item-txt">心超、心电图</span>
            <img src="./images/arrow_right2.png" alt="go" class="report-item-go"/>
          </div>
          <div class="report-item-tips">
            <p>查看结论</p>
            <!--            <div class="report-item-date-no-tab">-->
            <!--              <span>{{hospMeasure.heart.date}}</span>-->
            <!--            </div>-->
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import { getVisitReport } from '@/api/HealthReport'

  export default {
    data() {
      return {
        clientHeight: 0,
        familyMeasure: {
          bg: {},
          bmi: {},
          bp: {},
          step: {},
          time_line: ''
        },
        hospMeasure: {
          bmi: {},
          hospBp: {},
          s_876: {},
          s_945: {},
          s_946: {},
          s_901013: {},
          vat: {},
          blood_lipid: {},
          liver_kidney: {},
          pwv: {},
          ner_sur: {},
          carotid: {},
          heart: {}
        },
        hospName: '',
        time: ''
      }
    },
    created() {
      this.record_id = this.$route.query.id || 0
      // 初始化
      this.init(this.record_id)
    },
    beforeMount() {
      // 获取页面可视高度
      this.clientHeight = `${document.documentElement.clientHeight}`
      // 浏览器重置时获取页面可视高度
      window.onresize = () => {
        this.clientHeight = `${document.documentElement.clientHeight}`
      }
    },
    watch: {
      clientHeight() {
        this.$refs.wrapper.style.minHeight = this.clientHeight + 'px'
      }
    },
    methods: {
      /**
       * 初始化
       */
      init(recordId = 0) {
        let param = {}
        if (recordId !== 0) {
          param.record_id = recordId
        }

        // 获取报告数据
        getVisitReport(param).then(res => {
          if (res.status === 0) {
            // 处理报告数据
            this.handleReportData(res.data)
          } else {
            this.$toast(res.msg)
          }
        })
      },
      /**
       * BMI类型判断类名(1:正常,2:偏胖,3:偏瘦,4:肥胖)
       * @param {number} type 类型
       * @return {string} className 类名
       */
      bmiClass(type) {
        let className = 'item-remind-bmi-'
        switch (type) {
          case 1:
            className += 'normal'
            break
          case 2:
            className += 'up'
            break
          case 3:
            className += 'down'
            break
          case 4:
            className += 'highest'
            break
          default:
            className = ''
        }
        return className
      },
      /**
       * 家庭类型判断类名(1:正常,2:偏高,3:偏低,4:极高)
       * @param {number} type 类型
       * @return {string} className 类名
       */
      familyJudgeClass(type) {
        let className = 'item-remind-'
        switch (type) {
          case 1:
            className = ''
            break
          case 2:
            className += 'up'
            break
          case 3:
            className += 'down'
            break
          case 4:
            className += 'highest'
            break
          default:
            className = ''
        }
        return className
      },
      /**
       * BMI类型判断状态(1:正常,2:偏胖,3:偏瘦,4:肥胖)
       * @param {number} type 类型
       * @return {string} ext 状态
       */
      bmiExt(type) {
        let ext
        switch (type) {
          case 1:
            ext = '正常'
            break
          case 2:
            ext = '偏胖'
            break
          case 3:
            ext = '偏瘦'
            break
          case 4:
            ext = '肥胖'
            break
          default:
            ext = '-'
        }
        return ext
      },
      /**
       * 内脏脂肪类型判断状态(0:正常,2:偏高)
       * @param {number} type 类型
       * @return {string} ext 状态
       */
      vatExt(type) {
        let ext
        switch (type) {
          case 0:
            ext = '正常'
            break
          case 2:
            ext = '偏高'
            break
          default:
            ext = '-'
        }
        return ext
      },
      /**
       * 内脏脂肪类型判断类名(0:正常,2:偏高)
       * @param {number} type 类型
       * @return {string} className 类名
       */
      vatClass(type) {
        let className = 'item-remind-'
        switch (type) {
          case 2:
            className += 'up'
            break
          default:
            className = ''
        }
        return className
      },
      /**
       * 医院类型判断类名(0:正常,1:偏低,2:偏高)
       * @param {number} type 类型
       * @return {string} className 类名
       */
      hospJudgeClass(type) {
        let className = 'item-remind-'
        switch (type) {
          case 0:
            className = ''
            break
          case 1:
            className += 'down'
            break
          case 2:
            className += 'up'
            break
          default:
            className = ''
        }
        return className
      },
      /**
       * 类型判断状态(0:正常,1:偏低,2:偏高)
       * @param {number} type 类型
       * @return {string} ext 状态
       */
      judgeExt(type) {
        let ext
        switch (type) {
          case 0:
            ext = '正常'
            break
          case 1:
            ext = '偏低'
            break
          case 2:
            ext = '偏高'
            break
          default:
            ext = '-'
        }
        return ext
      },
      /**
       * 处理报告数据
       * @param {Object} data 报告数据
       */
      handleReportData(data) {
        if (data.has_data === 1) {
          let that = this
          this.hasData = 1
          this.hospName = data.hospName
          this.time = data.visitAt

          // 家庭测量数据
          let familyMeasure = data.familyMeasure || {}
          Object.keys(familyMeasure).forEach(val => {
            if (familyMeasure[val] && familyMeasure[val].normal != null) {
              if (val === 'bmi') {
                familyMeasure[val].normalClass = that.bmiClass(
                  familyMeasure[val].normal
                )
              } else {
                familyMeasure[val].normalClass = that.familyJudgeClass(
                  familyMeasure[val].normal
                )
              }
            }
          })
          this.familyMeasure.bg = familyMeasure.bg || {}
          this.familyMeasure.bmi = familyMeasure.bmi || {}
          this.familyMeasure.bp = familyMeasure.bp || {}
          this.familyMeasure.step = familyMeasure.step || {}
          // 医院报告数据
          let hospMeasure = data.hospMeasure || {}
          Object.keys(hospMeasure).forEach(val => {
            if (hospMeasure[val] && hospMeasure[val].normal != null) {
              if (val === 'bmi') {
                hospMeasure[val].normalExt = that.bmiExt(hospMeasure[val].normal)
                hospMeasure[val].normalClass = that.bmiClass(
                  hospMeasure[val].normal
                )
              } else if (val === 'vat') {
                hospMeasure[val].normalExt = that.vatExt(hospMeasure[val].normal)
                hospMeasure[val].normalClass = that.vatClass(
                  hospMeasure[val].normal
                )
              } else {
                hospMeasure[val].normalExt = that.judgeExt(
                  hospMeasure[val].normal
                )
                hospMeasure[val].normalClass = that.hospJudgeClass(
                  hospMeasure[val].normal
                )
              }
            }
          })
          this.hospMeasure.bmi = hospMeasure.bmi || {}
          this.hospMeasure.hospBp = hospMeasure.hospBp || {}
          this.hospMeasure.s_876 = hospMeasure.s_876 || {}
          this.hospMeasure.s_945 = hospMeasure.s_945 || {}
          this.hospMeasure.s_946 = hospMeasure.s_946 || {}
          this.hospMeasure.s_901013 = hospMeasure.s_901013 || {}
          this.hospMeasure.vat = hospMeasure.vat || {}

          this.hospMeasure.blood_lipid = hospMeasure.blood_lipid || {}
          this.hospMeasure.liver_kidney = hospMeasure.liver_kidney || {}
          this.hospMeasure.pwv = hospMeasure.pwv || {}
          this.hospMeasure.ner_sur = hospMeasure.ner_sur || {}
          this.hospMeasure.carotid = hospMeasure.carotid || {}
          this.hospMeasure.heart = hospMeasure.heart || {}
        } else {
          this.hasData = 0
        }
      },
      /**
       * 路由跳转
       * @param {string} path 路由地址
       */
      goRouter(path) {
        let query = { rebackType: 2, view_type: 1 }
        if (this.record_id !== '' || this.record_id !== 0) {
          query.record_id = this.record_id
        }
        this.$router.push({ path, query })
      }
    }
  }
</script>

<style lang="scss" scoped>

  .report-item-remind {
    width: 50px;
    height: 20px;
    color: #CCC;
    font-size: 17px;
    line-height: 20px;
    text-align: center;
    border-radius: 11px;
    border: 1px solid #CCC;
  }

  .item-remind-bmi-normal {
    color: #92E464;
    border: 1px solid #92E464;
  }

  .item-remind-bmi-up {
    color: #FCC569;
    border: 1px solid #FCC569;
  }

  .item-remind-bmi-down {
    color: #73CCF6;
    border: 1px solid #73CCF6;
  }

  .item-remind-bmi-highest {
    color: #EF7C69;
    border: 1px solid #EF7C69;
  }
  .wrapper {
    width: 100%;
    overflow: hidden;
    background-color: #F5F6FA;
    min-height: -webkit-fill-available;

    .report {
      margin: 15px;
      overflow: hidden;
      border-radius: 9px;
      background-color: #fff;

      .report-top {
        height: 40px;
        display: flex;
        padding: 10px;
        flex-direction: column;
        align-items: flex-start;
        justify-content: space-between;
        border-bottom: 1px solid #F2F3F4;

        .report-info {
          display: flex;
          align-items: center;

          img {
            width: 15px;
          }

          span {
            font-size: 16px;
            font-weight: 400;
            margin-left: 4px;
            color: #262626;
          }

          .report-time {
            color: #999;
          }
        }
      }

      .report-general {
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-start;

        .report-item-lipid {
          background-image: url("~@/assets/images/report-icon1.png");
        }

        .report-item-bp {
          background-image: url("~@/assets/images/report-icon2.png");
        }

        .report-item-bmi {
          background-image: url("~@/assets/images/report-icon4.png");
        }

        .report-item-hemoglobin {
          background-image: url("~@/assets/images/report-icon5.png");
        }

        .report-item-creatinine {
          background-image: url("~@/assets/images/report-icon7.png");
        }

        .report-item-arteriosclerosis {
          background-image: url("~@/assets/images/report-icon8.png");
        }

        .report-item-vat {
          background-image: url("~@/assets/images/report-icon9.png");
        }

        .report-item-sugarnet {
          background-image: url("~@/assets/images/report-icon10.png");
        }

        .report-item-neuropathy {
          background-image: url("~@/assets/images/report-icon11.png");
        }

        .report-item-liver {
          background-image: url("~@/assets/images/report-icon12.png");
        }

        .report-item-carotid {
          background-image: url("~@/assets/images/report-icon13.png");
        }

        .report-item-heart {
          background-image: url("~@/assets/images/report-icon14.png");
        }

        .report-item {
          width: 50%;
          padding: 10px 0;
          text-align: left;
          background-size: 50px;
          box-sizing: border-box;
          background-repeat: no-repeat;
          background-position: 90% 90%;
          border-bottom: 1px solid #F2F3F4;

          &:nth-of-type(odd) {
            border-right: 1px solid #F2F3F4;
          }

          .report-item-name {
            color: #333;
            display: flex;
            padding: 0 10px;
            align-items: center;
            justify-content: space-between;

            .report-item-txt {
              font-size: 16px;
              font-weight: 600;
              line-height: 20px;
            }

            .report-item-go {
              width: 8px;
              height: 15px;
            }
          }

          .report-item-value {
            display: flex;
            padding: 0 10px;
            margin-top: 14px;
            align-items: center;
            justify-content: space-between;

            .report-item-num {
              display: flex;
              align-items: center;
              justify-content: flex-start;

              span {
                height: 18px;
                color: #333;
                font-size: 26px;
                font-weight: 600;
                line-height: 18px;
              }

              img {
                width: 20px;
                height: 20px;
              }
            }

            .report-item-go {
              width: 8px;
              height: 15px;
            }
          }

          .report-item-refer {
            color: #999;
            padding: 0 10px;
            font-size: 14px;
            margin-top: 14px;
            font-weight: 400;
            line-height: 18px;

            .report-item-range {
              margin-left: 2px;
            }
          }

          .report-item-eye {
            display: flex;
            margin-top: 8px;
            align-items: flex-start;
            justify-content: flex-start;

            .item-eye-info {
              width: 50%;
              box-sizing: border-box;

              p {
                color: #333;
                padding: 0 2px;
                font-size: 14px;
                margin-top: 3px;
                font-weight: 400;
                line-height: 18px;
                text-align: center;
              }

              .eye-info-top {
                font-size: 15px;
                margin: 0 0 10px;
                color: #F67710;
                line-height: 19px;
                position: relative;
                z-index: 0;

                &::after {
                  content: "";
                  width: 22px;
                  height: 22px;
                  border-radius: 50%;
                  background-color: #FFECDE;
                  position: absolute;
                  top: 50%;
                  left: 50%;
                  transform: translate(-50%, -50%);
                  z-index: -1;
                }
              }
            }

            .item-eye-line {
              width: 1px;
              height: 40px;
              align-self: center;
              background-color: #E8E8E8;
            }
          }

          .report-item-include {
            color: #333;
            margin-top: 3px;
            font-size: 14px;
            padding: 0 10px;
            font-weight: 400;
            line-height: 18px;
          }

          .report-item-tips {
            margin-top: 8px;
            padding: 0 10px;

            p {
              font-size: 15px;
              font-weight: 400;
              color: #7AA0FF;
              line-height: 21px;
            }
          }
        }
      }
    }
  }
</style>
