<template>
  <div class="wrapper">
    <div class="content" v-if="list">
      <div
        class="group"
        v-for="item in list"
        :key="item.id"
        @click="goReport(item.date, item.hosp_id)"
      >
        <div class="group-circle-wrapper">
          <i class="group-circle-line"></i>
          <i class="group-circle-out">
            <i class="group-circle-inner"></i>
          </i>
        </div>
        <div class="group-report-wrapper">
          <h2 class="group-report-name">{{item.hosp_name}}</h2>
          <p class="group-report-time">
            就诊日期：
            <span>{{item.date}}</span>
          </p>
        </div>
      </div>
    </div>
    <footer v-else>数据已加载完毕</footer>
  </div>
</template>

<script>
import { getReportList } from '@/api/HealthReport'

export default {
  data() {
    return {
      list: 0
    }
  },
  methods: {
    /**
     * 初始化
     */
    init() {
      // 获取报告列表数据
      getReportList().then(res => {
        if (res.status === 0) {
          this.list = res.data
        } else {
          this.$toast(res.msg)
        }
      })
    },
    /**
     * 跳转健康报告首页
     * @param {String} date 报告日期
     * @param {String} hospId 医院ID
     */
    goReport(date = '', hospId = '') {
      let query = {}
      if (date) query.visit_at = date
      if (hospId) query.hosp_id = hospId
      this.$router.push({
        path: '/guanjia/health/report',
        query
      })
    }
  },
  created() {
    this.init()
  }
}
</script>

<style lang="scss" scoped>
@import "@/assets/scss/HealthReport/HealthReportList.scss";
</style>
