<template>
  <div class="wrapper">
    <div class="content">
      <div class="attention">
        <img src="../images/<EMAIL>" alt="attention"/>
        <span>为了数据准确性，建议每年至少检查一次</span>
      </div>
      <div class="group" v-if="hasData === 0">
        <div class="carotid-nodata-img">
          <img src="../images/nodata.png" alt="nodata"/>
          <p>暂无数据，建议完善检查</p>
        </div>
      </div>
      <div class="exam-wrapper" v-for="(exam, index) in examData" :key="index" >
        <div class="point" v-if="exam.date!==''">
          <div>
            <img src="../images/<EMAIL>" alt="clock"/>
            <span>{{exam.date}}</span>
          </div>
          <div>
            <img src="../images/<EMAIL>" alt="hos"/>
            <span>{{exam.hosp}}</span>
          </div>
        </div>
        <div class="exam-content" v-if="exam.date!==''">
          <exam-item v-for="(item, index) in exam.examItems" :key="index" :examData="item"></exam-item>
        </div>
      </div>
      <div class="group introduce-wrapper">
        <introduce-img :src="introduceSrc"></introduce-img>
      </div>
      <div v-if="hisReportList.length > 0" class="group">
        <p class="group-title">历史报告</p>
        <his-report :list="hisReportList" path="liver"></his-report>
      </div>
      <div class="group" v-if="recommendData">
        <p class="group-title">为您推荐</p>
        <report-recommend v-for="(item, index) in recommendData" :key="index" :data="item"></report-recommend>
      </div>
    </div>
    <footer>
      <footer-comp :footerProps="footerProps"></footer-comp>
    </footer>
  </div>
</template>

<script>
  import HisReport from '../components/HisReportList'
  import ReportExamItem from '../components/ReportExamItem'
  import ReportRecommend from '../components/ReportRecommend'
  import introduceImg from '../components/introduceImg'
  import FooterComp from '../components/FooterComp'

  import { getHeartData } from '@/api/HealthReport'

  export default {
    data() {
      return {
        checkItem: 'liver',
        hasData: '',
        visit_at: '',
        hosp_id: '',
        examData: [],
        introduceSrc: require('../images/ganshen.png'),
        recommendData: 0,
        hisReportList: []
      }
    },
    components: {
      'footer-comp': FooterComp,
      'exam-item': ReportExamItem,
      'introduce-img': introduceImg,
      'report-recommend': ReportRecommend,
      'his-report': HisReport
    },
    computed: {
      /**
       * 页脚跳转路由数据
       */
      footerProps() {
        return {
          hasNext: 1,
          btnTxt: '颈动脉超声',
          visit_at: this.visit_at || '',
          hosp_id: this.hosp_id || '',
          path: '/guanjia/health/report/hosp/carotid'
        }
      }
    },
    created() {
      this.record_id = this.$route.query.id || 0
      this.$nextTick(() => {
        this.init()
      })
    },
    methods: {
      /**
       * 初始化
       */
      init(recordId = 0) {
        let param = { checkItem: this.checkItem }
        if (recordId !== 0) {
          param.record_id = recordId
        }

        // 获取检查报告数据
        getHeartData(param).then(res => {
          if (res.status === 0) {
            // 处理检查报告数据
            this.handleData(res.data)
          } else {
            this.$toast(res.msg)
          }
        })
      },
      /**
       * 处理检查报告数据
       * @param {Object} data 检查报告数据
       */
      handleData(data) {
        if (data.has_data === 1) {
          this.hasData = 1
          let liverObj = {}
          let kidneyObj = {}
          let thyroidObj = {}

          liverObj.date = data.liver_date || ''
          liverObj.hosp = data.liver_name || ''
          kidneyObj.date = data.kidney_date || ''
          kidneyObj.hosp = data.kidney_name || ''
          thyroidObj.date = data.thyroid_date || ''
          thyroidObj.hosp = data.thyroid_name || ''

          let data878030 = data['878030'] || {}
          let data878031 = data['878031'] || {}
          let data878033 = data['878033'] || {}
          let data878034 = data['878034'] || {}
          let data878035 = data['878035'] || {}
          let data878036 = data['878036'] || {}
          let data878037 = data['878037'] || {}
          let data878038 = data['878038'] || {}
          let data883003 = data['883003'] || {}
          let data883004 = data['883004'] || {}
          let data883005 = data['883005'] || {}

          liverObj.examItems = [
            {
              title: '丙氨酸氨基转移酶',
              reference: data878030.reference,
              subtitle: data878030.subquestion_name,
              data: [
                {
                  val: data878030.answer_name,
                  unit: data878030.unit,
                  updown: data878030.updown
                }
              ]
            },
            {
              title: '天门冬氨酸氨基转移酶',
              reference: data878031.reference,
              subtitle: data878031.subquestion_name,
              data: [
                {
                  val: data878031.answer_name,
                  unit: data878031.unit,
                  updown: data878031.updown
                }
              ]
            },
            {
              title: 'r-谷氨酰转移酶',
              reference: data878033.reference,
              subtitle: data878033.subquestion_name,
              data: [
                {
                  val: data878033.answer_name,
                  unit: data878033.unit,
                  updown: data878033.updown
                }
              ]
            },
            {
              title: '总胆红素',
              reference: data878034.reference,
              subtitle: data878034.subquestion_name,
              data: [
                {
                  val: data878034.answer_name,
                  unit: data878034.unit,
                  updown: data878034.updown
                }
              ]
            },
            {
              title: '直接胆红素',
              reference: data878035.reference,
              subtitle: data878035.subquestion_name,
              data: [
                {
                  val: data878035.answer_name,
                  unit: data878035.unit,
                  updown: data878035.updown
                }
              ]
            }
          ]
          this.examData.push(liverObj)

          kidneyObj.examItems = [
            {
              title: '尿素',
              reference: data878036.reference,
              subtitle: data878036.subquestion_name,
              data: [
                {
                  val: data878036.answer_name,
                  unit: data878036.unit,
                  updown: data878036.updown
                }
              ]
            },
            {
              title: '肌酐',
              reference: data878037.reference,
              subtitle: data878037.subquestion_name,
              data: [
                {
                  val: data878037.answer_name,
                  unit: data878037.unit,
                  updown: data878037.updown
                }
              ]
            },
            {
              title: '尿酸',
              reference: data878038.reference,
              subtitle: data878038.subquestion_name,
              data: [
                {
                  val: data878038.answer_name,
                  unit: data878038.unit,
                  updown: data878038.updown
                }
              ]
            }
          ]
          this.examData.push(kidneyObj)

          thyroidObj.examItems = [
            {
              title: '促甲状腺素',
              reference: data883005.reference,
              subtitle: data883005.subquestion_name,
              data: [
                {
                  val: data883005.answer_name,
                  unit: data883005.unit,
                  updown: data883005.updown
                }
              ]
            },
            {
              title: '游离三碘甲状腺原氨酸',
              reference: data883003.reference,
              subtitle: data883003.subquestion_name,
              data: [
                {
                  val: data883003.answer_name,
                  unit: data883003.unit,
                  updown: data883003.updown
                }
              ]
            },
            {
              title: '游离甲状腺素',
              reference: data883004.reference,
              subtitle: data883004.subquestion_name,
              data: [
                {
                  val: data883004.answer_name,
                  unit: data883004.unit,
                  updown: data883004.updown
                }
              ]
            }
          ]
          this.examData.push(thyroidObj)

          // 获取历史报告
          this.hisReportList = data.history

          this.recommendData = data.recommend || []
        } else {
          this.hasData = 0
        }
      },
      changeRecord(recordId) {
        this.init(recordId)
      }
    }
  }
</script>

<style lang='scss' scoped>
  @import '@/assets/scss/HealthReport/HealthReportExam.scss'
</style>
