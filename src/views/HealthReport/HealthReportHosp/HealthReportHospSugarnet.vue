<template>
  <div class="wrapper">
    <div class="content">
      <div class="attention">
        <img src="../images/<EMAIL>" alt="attention"/>
        <span>为了数据准确性，建议每年至少检查一次</span>
      </div>
      <div class="group" v-if="hasData === 0">
        <div class="carotid-nodata-img">
          <img src="../images/nodata.png" alt="nodata"/>
          <p>暂无数据，建议完善检查</p>
        </div>
      </div>
      <div class="exam-wrapper" v-if="hasData === 1">
        <div class="point">
          <div>
            <img src="../images/<EMAIL>" alt="clock"/>
            <span>{{date}}</span>
          </div>
          <div>
            <img src="../images/<EMAIL>" alt="hos"/>
            <span>{{hosp}}</span>
          </div>
        </div>
        <div class="sugarnet">
          <div class="sugarnet-item">
            <div class="sugarnet-title">
              <span>左眼</span>
              <i></i>
            </div>
            <div class="sugarnet-val" v-if="LAnswer !== '' || RAnswer !== ''">{{LAnswer}}</div>
            <div class="sugarnet-pics">
              <div v-if="leftPic.length > 0">
                <div class="sugarnet-pic" v-for="(leftP, index) in leftPic" :key="index">
                  <img :src="leftP" alt="leftPic" @click="showBigImg($event)"/>
                </div>
              </div>
              <div v-else class="sugarnet-pic-no">
                <img src="../images/noEyes.png" alt="暂无图片"/>
                <span>暂无图片</span>
              </div>
            </div>
          </div>
          <i class="sugarnet-line"></i>
          <div class="sugarnet-item">
            <div class="sugarnet-title">
              <span>右眼</span>
              <i></i>
            </div>
            <div class="sugarnet-val" v-if="LAnswer !== '' || RAnswer !== ''">{{RAnswer}}</div>
            <div class="sugarnet-pics">
              <div v-if="rightPic.length > 0">
                <div class="sugarnet-pic" v-for="(rightP, index) in rightPic" :key="index">
                  <img :src="rightP" alt="rightPic" @click="showBigImg($event)"/>
                </div>
              </div>
              <div v-else class="sugarnet-pic-no">
                <img src="../images/noEyes.png" alt="暂无图片"/>
                <span>暂无图片</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="group introduce-wrapper">
        <introduce-img :src="introduceSrc"></introduce-img>
      </div>
      <div v-if="hisReportList.length > 0" class="group">
        <p class="group-title">历史报告</p>
        <his-report :list="hisReportList" path="sugarnet"  @refresh="changeRecord"></his-report>
      </div>
      <div class="group" v-if="recommendData">
        <p class="group-title">为您推荐</p>
        <report-recommend v-for="(item, index) in recommendData" :key="index" :data="item"></report-recommend>
      </div>
    </div>
    <footer>
      <footer-comp :footerProps="footerProps"></footer-comp>
    </footer>
    <big-img v-if="isShowBigImg" @hideBigImg="hideBigImg" :bigImg="bigImg"></big-img>
  </div>
</template>

<script>
  import HisReport from '../components/HisReportList.vue'
  import ReportRecommend from '../components/ReportRecommend'
  import introduceImg from '../components/introduceImg'
  import BigImg from '../components/BigImg'
  import FooterComp from '../components/FooterComp'

  import { getHeartData } from '@/api/HealthReport'

  export default {
    data() {
      return {
        checkItem: 'sugarnet',
        hasData: '',
        visit_at: '',
        hosp_id: '',
        date: '',
        hosp: '',
        leftPic: [],
        rightPic: [],
        LAnswer: '',
        RAnswer: '',
        isShowBigImg: false,
        bigImg: { src: '' },
        introduceSrc: require('../images/tangwang.png'),
        recommendData: 0,
        hisReportList: []
      }
    },
    components: {
      'big-img': BigImg,
      'footer-comp': FooterComp,
      'introduce-img': introduceImg,
      'report-recommend': ReportRecommend,
      'his-report': HisReport
    },
    computed: {
      /**
       * 页脚跳转路由数据
       */
      footerProps() {
        return {
          hasNext: 1,
          btnTxt: '周围神经病变检测',
          visit_at: this.visit_at || '',
          hosp_id: this.hosp_id || '',
          path: '/guanjia/health/report/hosp/neuropathy'
        }
      }
    },
    created() {
      this.record_id = this.$route.query.id || 0
      this.$nextTick(() => {
        this.init()
      })
    },
    methods: {
      /**
       * 初始化
       */
      init(recordId = 0) {
        let param = { checkItem: this.checkItem }
        if (recordId !== 0) {
          param.record_id = recordId
        }

        // 获取检查报告数据
        getHeartData(param).then(res => {
          if (res.status === 0) {
            // 处理检查报告数据
            this.handleData(res.data)
          } else {
            this.$toast(res.msg)
          }
        })
      },
      /**
       * 处理检查报告数据
       * @param {Object} data 检查报告数据
       */
      handleData(data) {
        if (data.has_data === 1) {
          this.hasData = 1
          this.date = data.visit_at || ''
          this.hosp = data.hosp_name || ''
          let dataLeft = data['left_eye'] || {}
          let dataRight = data['right_eye'] || {}
          this.LAnswer = dataLeft.answer_name || ''
          this.RAnswer = dataRight.answer_name || ''
          this.leftPic = data.left_pic || []
          this.rightPic = data.right_pic || []
          this.recommendData = data.recommend || []

          // 获取历史报告
          this.hisReportList = data.history
        } else {
          this.hasData = 0
        }
      },
      changeRecord(recordId) {
        this.init(recordId)
      },
      /**
       * 显示大图
       * @param {Object} e 事件对象
       */
      showBigImg(e) {
        this.bigImg = { src: e.currentTarget.src, center: true }
        this.isShowBigImg = true
        // 禁止body滚动
        let docBody = document.body
        docBody.addEventListener('touchmove', this.bodyScroll, false)
        document.body.style.overflow = 'hidden'
        // chrome滚动警告
        document.body.style.touchAction = 'none'
      },
      /**
       * 隐藏大图
       */
      hideBigImg() {
        this.isShowBigImg = false
        // 放开body滚动
        let docBody = document.body
        docBody.addEventListener('touchmove', this.bodyScroll, false)
        document.body.style.overflow = ''
        // chrome滚动警告
        document.body.style.touchAction = ''
      },
      /**
       * 阻止body滚动默认事件
       */
      bodyScroll(event) {
        event.preventDefault()
      }
    }
  }
</script>

<style lang='scss' scoped>
  @import '@/assets/scss/HealthReport/HealthReportExam.scss'
</style>
