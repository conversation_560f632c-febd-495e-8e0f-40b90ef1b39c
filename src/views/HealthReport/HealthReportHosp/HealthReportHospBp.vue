<template>
  <div class="wrapper">
    <div class="content">
      <div class="attention">
        <img src="../images/<EMAIL>" alt="attention"/>
        <span>为了数据准确性，建议每年至少检查一次</span>
      </div>
      <div class="group" v-if="hasData === 0">
        <div class="carotid-nodata-img">
          <img src="../images/nodata.png" alt="nodata"/>
          <p>暂无数据，建议完善检查</p>
        </div>
      </div>
      <div class="exam-wrapper" v-for="(exam, index) in examData" :key="index">
        <div class="point">
          <div>
            <img src="../images/<EMAIL>" alt="clock"/>
            <span>{{exam.date}}</span>
          </div>
          <div>
            <img src="../images/<EMAIL>" alt="hos"/>
            <span>{{exam.hosp}}</span>
          </div>
        </div>
        <div class="exam-content">
          <exam-item v-for="(item, index) in exam.examItems" :key="index" :examData="item"></exam-item>
        </div>
      </div>
      <div v-if="hisReportList.length > 0" class="group">
        <p class="group-title">历史报告</p>
        <his-report :list="hisReportList" path="bp"></his-report>
      </div>
      <div class="group" v-if="recommendData">
        <p class="group-title">为您推荐</p>
        <report-recommend v-for="(item, index) in recommendData" :key="index" :data="item"></report-recommend>
      </div>
    </div>
    <footer>
      <footer-comp :footerProps="footerProps"></footer-comp>
    </footer>
  </div>
</template>

<script>
  import HisReport from '../components/HisReportList.vue'
  import ReportExamItem from '../components/ReportExamItem'
  import ReportRecommend from '../components/ReportRecommend'
  import FooterComp from '../components/FooterComp'

  import { getHeartData } from '@/api/HealthReport'

  export default {
    data() {
      return {
        checkItem: 'hospbp',
        hasData: '',
        visit_at: '',
        hosp_id: '',
        examData: [],
        recommendData: 0,
        hisReportList: []
      }
    },
    components: {
      'footer-comp': FooterComp,
      'exam-item': ReportExamItem,
      'report-recommend': ReportRecommend,
      'his-report': HisReport
    },
    computed: {
      /**
       * 页脚跳转路由数据
       */
      footerProps() {
        return {
          hasNext: 1,
          btnTxt: '尿白蛋白比肌酐',
          visit_at: this.visit_at || '',
          hosp_id: this.hosp_id || '',
          path: '/guanjia/health/report/hosp/creatinine'
        }
      }
    },
    created() {
      this.$nextTick(() => {
        this.init()
      })
    },
    methods: {
      /**
       * 初始化
       */
      init(recordId = 0) {
        let param = { checkItem: this.checkItem }
        if (recordId !== 0) {
          param.record_id = recordId
        }

        // 获取检查报告数据
        getHeartData(param).then(res => {
          if (res.status === 0) {
            // 处理检查报告数据
            this.handleData(res.data)
          } else {
            this.$toast(res.msg)
          }
        })
      },
      /**
       * 处理检查报告数据
       * @param {Object} data 检查报告数据
       */
      handleData(data) {
        if (data.has_data === 1) {
          this.hasData = 1
          let bpArr = data.bpArr || {}
          let bpObj = {}
          bpObj.date = bpArr.visit_at || ''
          bpObj.hosp = bpArr.hosp_name || ''
          bpObj.examItems = [
            {
              title: '血压',
              data: [
                {
                  val: bpArr.sbp + '/' + bpArr.dbp,
                  unit: 'mmHg'
                }
              ]
            },
            {
              title: '心率',
              data: [
                {
                  val: bpArr.pulse,
                  unit: '次/分'
                }
              ]
            }
          ]
          this.examData.push(bpObj)

          // 获取历史报告
          this.hisReportList = data.history

          this.recommendData = data.recommend || []
        } else {
          this.hasData = 0
        }
      }
    }
  }
</script>

<style lang='scss' scoped>
  @import '@/assets/scss/HealthReport/HealthReportExam.scss'
</style>
