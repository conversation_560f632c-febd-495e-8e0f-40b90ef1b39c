<template>
  <div class="wrapper">
    <div class="content">
      <div class="attention">
        <img src="../images/<EMAIL>" alt="attention" />
        <span>为了数据准确性，建议每年至少检查一次</span>
      </div>
      <div class="group" v-if="hasData === 0">
        <div class="carotid-nodata-img">
          <img src="../images/nodata.png" alt="nodata" />
          <p>暂无数据，建议完善检查</p>
        </div>
      </div>
      <div class="exam-wrapper" v-for="(exam, index) in examData" :key="index">
        <div class="point">
          <div>
            <img src="../images/<EMAIL>" alt="clock" />
            <span>{{exam.date}}</span>
          </div>
          <div>
            <img src="../images/<EMAIL>" alt="hos" />
            <span>{{exam.hosp}}</span>
          </div>
        </div>
        <div class="exam-content">
          <exam-item v-for="(item, index) in exam.examItems" :key="index" :examData="item"></exam-item>
        </div>
      </div>
      <div class="group" v-if="bmiVal">
        <bmi-index :bmiVal="bmiVal"></bmi-index>
      </div>
      <div class="group" v-if="lineChartProps">
        <line-chart class="line-echart" :lineChartProps="lineChartProps"></line-chart>
      </div>
      <div v-if="hisReportList.length > 0" class="group">
        <p class="group-title">历史报告</p>
        <his-report :list="hisReportList" path="bmi"></his-report>
      </div>
      <div class="group" v-if="recommendData">
        <p class="group-title">为您推荐</p>
        <report-recommend v-for="(item, index) in recommendData" :key="index" :data="item"></report-recommend>
      </div>
    </div>
    <footer>
      <footer-comp :footerProps="footerProps"></footer-comp>
    </footer>
  </div>
</template>

<script>
import HisReport from '../components/HisReportList'
import ReportExamItem from '../components/ReportExamItem'
import ReportRecommend from '../components/ReportRecommend'
import LineChart from '../components/LineChart'
import BMIIndex from '../components/BMIIndex'
import FooterComp from '../components/FooterComp'

import { getHeartData } from '@/api/HealthReport'

export default {
  data() {
    return {
      checkItem: 'bmihosp',
      hasData: '',
      visit_at: '',
      hosp_id: '',
      bmiVal: 0,
      examData: [],
      isLeftClick: 1,
      isRightClick: 1,
      lineChartProps: 0,
      recommendData: 0,
      hisReportList: []
    }
  },
  components: {
    'bmi-index': BMIIndex,
    'line-chart': LineChart,
    'footer-comp': FooterComp,
    'exam-item': ReportExamItem,
    'report-recommend': ReportRecommend,
    'his-report': HisReport
  },
  computed: {
    /**
     * 页脚跳转路由数据
     */
    footerProps() {
      return {
        hasNext: 1,
        btnTxt: '血脂',
        visit_at: this.visit_at || '',
        hosp_id: this.hosp_id || '',
        path: '/guanjia/health/report/hosp/lipid'
      }
    }
  },
  created() {
    this.$nextTick(() => {
      this.init()
    })
  },
  methods: {
    /**
     * 初始化
     */
    init(recordId = 0) {
      let param = { checkItem: this.checkItem }
      if (recordId !== 0) {
        param.record_id = recordId
      }

      // 获取检查报告数据
      getHeartData(param).then(res => {
        if (res.status === 0) {
          // 处理检查报告数据
          this.handleData(res.data)
        } else {
          this.$toast(res.msg)
        }
      })
    },
    /**
     * 处理检查报告数据
     * @param {Object} data 检查报告数据
     */
    handleData(data) {
      if (data.has_data === 1) {
        this.hasData = 1
        let currentData = data.current || {}
        if (currentData.bmi == null) currentData.bmi = 0
        let examObj = {}
        examObj.date = currentData.visit_at || ''
        examObj.hosp = currentData.hosp_name || ''
        examObj.examItems = [
          {
            title: 'BMI',
            data: [
              {
                val: currentData.bmi,
                unit: 'kg/m²',
                BMI: currentData.normal
              }
            ]
          }
        ]
        this.examData.push(examObj)

        let diagram = data.diagram || {}
        let diagramValue = []
        let visitAt = (diagram.visit_at || []).map((val, index) => {
          let formatDate = this.formatDate(val)
          diagramValue.push([formatDate, diagram.value[index]])
          return formatDate
        })
        let base = data.base || {}
        let baseVisitAt = base.visit_at ? this.formatDate(base.visit_at) : ''
        // 去重并排序日期
        visitAt = Array.from(
          new Set([baseVisitAt, ...visitAt])
        ).sort()

        this.lineChartProps = {
          xRotate: visitAt.length > 5 ? 45 : 0,
          legendData: ['基线', 'BMI'],
          value: [
            {
              val: diagramValue,
              name: 'BMI'
            },
            {
              val: [[baseVisitAt, base.value || '']],
              name: '基线',
              symbol: 'triangle',
              color: '#009DD9'
            }
          ],
          visit_at: visitAt
        }
        this.bmiVal = Number(currentData.bmi)
        // 获取历史报告
        this.hisReportList = data.history
        this.recommendData = data.recommend || []
      } else {
        this.hasData = 0
      }
    },
    /**
     * 格式化日期
     * @param {String} date 日期
     * @return {String} 格式化日期
     */
    formatDate(date) {
      date = new Date(date)
      let y = String(date.getFullYear()).slice(2)
      let m = date.getMonth() + 1
      m = m < 10 ? '0' + m : m
      let d = date.getDate()
      d = d < 10 ? '0' + d : d
      return y + '/' + m + '/' + d
    }
  }
}
</script>

<style lang='scss' scoped>
@import '@/assets/scss/HealthReport/HealthReportExam.scss'
</style>
