<template>
  <div class="wrapper">
    <div class="content">
      <div class="attention">
        <img src="../images/<EMAIL>" alt="attention" />
        <span>为了数据准确性，建议每年至少检查一次</span>
      </div>
      <div class="group" v-if="hasData === 0">
        <div class="carotid-nodata-img">
          <img src="../images/nodata.png" alt="nodata" />
          <p>暂无数据，建议完善检查</p>
        </div>
      </div>
      <div class="exam-wrapper" v-if="hasData === 1">
        <div class="point">
          <div>
            <img src="../images/<EMAIL>" alt="clock" />
            <span>{{date}}</span>
          </div>
          <div>
            <img src="../images/<EMAIL>" alt="hos" />
            <span>{{hosp}}</span>
          </div>
        </div>
        <div class="arteriosclerosis">
          <div class="arteriosclerosis-exam">
            <div class="arteriosclerosis-exam-item">
              <div class="arteriosclerosis-exam-title">
                <span>左侧</span>
                <i></i>
              </div>
              <div class="arteriosclerosis-exam-val">
                <span>ABI</span>
                <b>{{labi}}</b>
              </div>
              <div class="arteriosclerosis-exam-val">
                <span>baPWV</span>
                <b>{{lbapwv}}</b>
              </div>
            </div>
            <div class="arteriosclerosis-exam-item">
              <div class="arteriosclerosis-exam-title">
                <span>右侧</span>
                <i></i>
              </div>
              <div class="arteriosclerosis-exam-val">
                <span>ABI</span>
                <b>{{rabi}}</b>
              </div>
              <div class="arteriosclerosis-exam-val">
                <span>baPWV</span>
                <b>{{rbapwv}}</b>
              </div>
            </div>
          </div>
          <div class="arteriosclerosis-conclusion">
            <p>动脉硬化检测结论：</p>
            <p>{{conclusion_left}}</p>
            <p>{{conclusion_right}}</p>
          </div>
        </div>
      </div>
      <div class="group introduce-wrapper">
        <introduce-img :src="introduceSrc"></introduce-img>
      </div>
      <div v-if="hisReportList.length > 0" class="group">
        <p class="group-title">历史报告</p>
        <his-report :list="hisReportList" path="arteriosclerosis" @refresh="changeRecord"></his-report>
      </div>
      <div class="group" v-if="recommendData">
        <p class="group-title">为您推荐</p>
        <report-recommend v-for="(item, index) in recommendData" :key="index" :data="item"></report-recommend>
      </div>
    </div>
    <footer>
      <footer-comp :footerProps="footerProps"></footer-comp>
    </footer>
  </div>
</template>

<script>
import HisReport from '../components/HisReportList'
import ReportRecommend from '../components/ReportRecommend'
import introduceImg from '../components/introduceImg'
import FooterComp from '../components/FooterComp'

import { getHeartData } from '@/api/HealthReport'

export default {
  data() {
    return {
      checkItem: 'arteriosclerosis',
      hasData: '',
      visit_at: '',
      hosp_id: '',
      date: '',
      hosp: '',
      conclusion_left: '',
      conclusion_right: '',
      labi: '',
      rabi: '',
      lbapwv: '',
      rbapwv: '',
      introduceSrc: require('../images/dongmaiyinghua.png'),
      recommendData: 0,
      hisReportList: []
    }
  },
  components: {
    'footer-comp': FooterComp,
    'introduce-img': introduceImg,
    'report-recommend': ReportRecommend,
    'his-report': HisReport
  },
  computed: {
    /**
     * 页脚跳转路由数据
     */
    footerProps() {
      return {
        hasNext: 1,
        btnTxt: '内脏脂肪面积',
        visit_at: this.visit_at || '',
        hosp_id: this.hosp_id || '',
        path: '/guanjia/health/report/hosp/vat'
      }
    }
  },
  created() {
    this.$nextTick(() => {
      this.init()
    })
  },
  methods: {
    /**
     * 初始化
     */
    init(recordId = 0) {
      let param = { checkItem: this.checkItem }
      if (recordId !== 0) {
        param.record_id = recordId
      }

      // 获取检查报告数据
      getHeartData(param).then(res => {
        if (res.status === 0) {
          // 处理检查报告数据
          this.handleData(res.data)
        } else {
          this.$toast(res.msg)
        }
      })
    },
    /**
     * 处理检查报告数据
     * @param {Object} data 检查报告数据
     */
    handleData(data) {
      if (data.has_data === 1) {
        this.hasData = 1
        let pwvData = data.pwv || {}
        this.date = pwvData.visit_at || ''
        this.hosp = pwvData.hosp_name || ''
        this.conclusion_left = pwvData.pwvabi_conclusion_left || ''
        this.conclusion_right = pwvData.pwvabi_conclusion_right || ''
        this.labi = pwvData.labi || ''
        this.rabi = pwvData.rabi || ''
        this.lbapwv = pwvData.lbapwv || ''
        this.rbapwv = pwvData.rbapwv || ''
        // 获取历史报告
        this.hisReportList = data.history
        this.recommendData = data.recommend || []
      } else {
        this.hasData = 0
      }
    }
  }
}
</script>

<style lang='scss' scoped>
@import '@/assets/scss/HealthReport/HealthReportExam.scss'
</style>
