<template>
  <div class="wrapper">
    <div class="content">
      <div class="attention">
        <img src="../images/<EMAIL>" alt="attention" />
        <span>为了数据准确性，建议每年至少检查一次</span>
      </div>
      <div class="group" v-if="hasData === 0">
        <div class="carotid-nodata-img">
          <img src="../images/nodata.png" alt="nodata" />
          <p>暂无数据，建议完善检查</p>
        </div>
      </div>
      <div class="exam-wrapper" v-for="(exam, index) in examData" :key="index">
        <div class="point">
          <div>
            <img src="../images/<EMAIL>" alt="clock" />
            <span>{{exam.date}}</span>
          </div>
          <div>
            <img src="../images/<EMAIL>" alt="hos" />
            <span>{{exam.hosp}}</span>
          </div>
        </div>
        <div class="exam-content">
          <exam-item v-for="(item, index) in exam.examItems" :key="index" :examData="item"></exam-item>
        </div>
      </div>
      <div class="group" v-if="lineChartProps">
        <line-chart class="line-echart" :lineChartProps="lineChartProps"></line-chart>
      </div>
      <div v-if="hisReportList.length > 0" class="group">
        <p class="group-title">历史报告</p>
        <his-report :list="hisReportList" path="lipid" ></his-report>
      </div>
      <div class="group" v-if="recommendData">
        <p class="group-title">为您推荐</p>
        <report-recommend v-for="(item, index) in recommendData" :key="index" :data="item"></report-recommend>
      </div>
    </div>
    <footer>
      <footer-comp :footerProps="footerProps"></footer-comp>
    </footer>
  </div>
</template>

<script>
import HisReport from '../components/HisReportList'
import LineChart from '../components/LineChart'
import ReportExamItem from '../components/ReportExamItem'
import ReportRecommend from '../components/ReportRecommend'
import FooterComp from '../components/FooterComp'

import { getHeartData } from '@/api/HealthReport'

export default {
  data() {
    return {
      checkItem: 'lipid',
      hasData: '',
      visit_at: '',
      hosp_id: '',
      examData: [],
      isLeftClick: 1,
      isRightClick: 1,
      lineChartProps: 0,
      recommendData: 0,
      hisReportList: []
    }
  },
  components: {
    'line-chart': LineChart,
    'footer-comp': FooterComp,
    'exam-item': ReportExamItem,
    'report-recommend': ReportRecommend,
    'his-report': HisReport
  },
  computed: {
    /**
     * 页脚跳转路由数据
     */
    footerProps() {
      return {
        hasNext: 1,
        btnTxt: '动脉硬化检测',
        visit_at: this.visit_at || '',
        hosp_id: this.hosp_id || '',
        path: '/guanjia/health/report/hosp/arteriosclerosis'
      }
    }
  },
  created() {
    this.record_id = this.$route.query.id || 0
    this.$nextTick(() => {
      this.init()
    })
  },
  methods: {
    /**
     * 初始化
     */
    init(recordId = 0) {
      let param = { checkItem: this.checkItem }
      if (recordId !== 0) {
        param.record_id = recordId
      }
      // 获取检查报告数据
      getHeartData(param).then(res => {
        if (res.status === 0) {
          // 处理检查报告数据
          this.handleData(res.data)
        } else {
          this.$toast(res.msg)
        }
      })
    },
    /**
     * 处理检查报告数据
     * @param {Object} data 检查报告数据
     */
    handleData(data) {
      if (data.has_data === 1) {
        this.hasData = 1
        let examObj = {}
        examObj.date = data.visit_date || ''
        examObj.hosp = data.hosp_name || ''

        let data878039 = data['878039'] || {}
        let data878040 = data['878040'] || {}
        let data878042 = data['878042'] || {}

        examObj.examItems = [
          {
            title: '甘油三酯',
            reference: data878039.reference,
            data: [
              {
                val: data878039.answer_name,
                unit: data878039.unit,
                updown: data878039.updown
              }
            ]
          },
          {
            title: '总胆固醇',
            reference: data878040.reference,
            data: [
              {
                val: data878040.answer_name,
                unit: data878040.unit,
                updown: data878040.updown
              }
            ]
          },
          {
            title: '低密度脂蛋白胆固醇',
            reference: data878042.reference,
            data: [
              {
                val: data878042.answer_name,
                unit: data878042.unit,
                updown: data878042.updown
              }
            ]
          }
        ]
        this.examData.push(examObj)

        let base = data.base || {}
        let base878039 = base['878039'] || {}
        let base878040 = base['878040'] || {}
        let base878042 = base['878042'] || {}
        let base878039Val = [
          base878039.visit_at ? this.formatDate(base878039.visit_at) : '',
          base878039.value || ''
        ]
        let base878040Val = [
          base878040.visit_at ? this.formatDate(base878040.visit_at) : '',
          base878040.value || ''
        ]
        let base878042Val = [
          base878042.visit_at ? this.formatDate(base878042.visit_at) : '',
          base878042.value || ''
        ]

        let listLab878039 = data.list_lab['878039'] || {}
        let listLab878040 = data.list_lab['878040'] || {}
        let listLab878042 = data.list_lab['878042'] || {}

        let val878039 = []
        let val878040 = []
        let val878042 = []
        let visitAt878039 = (listLab878039.visit_at || []).map(
          (val, index) => {
            let formatDate = this.formatDate(val)
            val878039.push([formatDate, listLab878039.value[index]])
            return formatDate
          }
        )
        let visitAt878040 = (listLab878040.visit_at || []).map(
          (val, index) => {
            let formatDate = this.formatDate(val)
            val878040.push([formatDate, listLab878040.value[index]])
            return formatDate
          }
        )
        let visitAt878042 = (listLab878042.visit_at || []).map(
          (val, index) => {
            let formatDate = this.formatDate(val)
            val878042.push([formatDate, listLab878042.value[index]])
            return formatDate
          }
        )
        // 去重并排序日期
        let visitAt = Array.from(
          new Set([
            base878039Val[0],
            base878040Val[0],
            base878042Val[0],
            ...visitAt878039,
            ...visitAt878040,
            ...visitAt878042])
        ).sort()

        this.lineChartProps = {
          top: '31%',
          height: '68%',
          xRotate: visitAt.length > 5 ? 45 : 0,
          legendData: ['甘油三酯', '甘油三酯基线', '总胆固醇', '总胆固醇基线', '低密度脂蛋白胆固醇', '低密度脂蛋白胆固醇基线'],
          value: [
            {
              val: val878039,
              name: '甘油三酯',
              symbol: 'diamond',
              color: '#c23531'
            },
            {
              val: [base878039Val],
              name: '甘油三酯基线',
              symbol: 'triangle',
              color: '#c23531'
            },
            {
              val: val878040,
              name: '总胆固醇',
              symbol: 'circle',
              color: '#2f4554'
            },
            {
              val: [base878040Val],
              name: '总胆固醇基线',
              symbol: 'triangle',
              color: '#2f4554'
            },
            {
              val: val878042,
              name: '低密度脂蛋白胆固醇',
              symbol: 'rect',
              color: '#61a0a8'
            },
            {
              val: [base878042Val],
              name: '低密度脂蛋白胆固醇基线',
              symbol: 'triangle',
              color: '#61a0a8'
            }
          ],
          visit_at: visitAt
        }
        // 获取历史报告
        this.hisReportList = data.history
        this.recommendData = data.recommend || []
      } else {
        this.hasData = 0
      }
    },
    /**
     * 格式化日期
     * @param {String} date 日期
     * @return {String} 格式化日期
     */
    formatDate(date) {
      date = new Date(date)
      let y = String(date.getFullYear()).slice(2)
      let m = date.getMonth() + 1
      m = m < 10 ? '0' + m : m
      let d = date.getDate()
      d = d < 10 ? '0' + d : d
      return y + '/' + m + '/' + d
    },
    changeRecord(recordId) {
      this.init(recordId)
    }
  }
}
</script>

<style lang='scss' scoped>
@import '@/assets/scss/HealthReport/HealthReportExam.scss'
</style>
