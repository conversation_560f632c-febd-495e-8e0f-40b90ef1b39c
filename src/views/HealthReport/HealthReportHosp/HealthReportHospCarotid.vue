<template>
  <div class="wrapper">
    <div class="content">
      <div class="attention">
        <img src="../images/<EMAIL>" alt="attention"/>
        <span>为了数据准确性，建议每年至少检查一次</span>
      </div>
      <div v-if="hasData === 0" class="group">
        <div class="carotid-nodata-img">
          <img src="../images/nodata.png" alt="nodata"/>
          <p>暂无数据，建议完善检查</p>
        </div>
        <div class="carotid-nodata-tips">
          <div>
            <img src="../images/<EMAIL>" alt="attention"/>
            <p>颈动脉超声检查是诊断、评估颈动脉壁病变的有效手段之一，能够反应糖尿病患者大血管病变、动脉粥样硬化、颈动脉狭窄的情况，对预防缺血性脑卒中有重要意义。</p>
          </div>
        </div>
      </div>
      <div v-if="hasData === 1" class="exam-wrapper">
        <div class="point">
          <div>
            <img src="../images/<EMAIL>" alt="clock"/>
            <span>{{date}}</span>
          </div>
          <div>
            <img src="../images/<EMAIL>" alt="hos"/>
            <span>{{hosp}}</span>
          </div>
        </div>
        <div class="neuropathy">
          <img src="../images/assignment.png" alt="answer"/>
          <span class="neuropathy-title">结论:</span>
          <span class="neuropathy-answer">{{answer}}</span>
        </div>
      </div>
      <div class="group introduce-wrapper">
        <introduce-img :src="introduceSrc"></introduce-img>
      </div>
      <div v-if="hisReportList.length > 0" class="group">
        <p class="group-title">历史报告</p>
        <his-report :list="hisReportList" path="carotid"></his-report>
      </div>
      <div class="group" v-if="recommendData">
        <p class="group-title">为您推荐</p>
        <report-recommend v-for="(item, index) in recommendData" :key="index" :data="item"></report-recommend>
      </div>
    </div>
    <footer>
      <footer-comp :footerProps="footerProps"></footer-comp>
    </footer>
  </div>
</template>

<script>
  import HisReport from '../components/HisReportList'
  import ReportRecommend from '../components/ReportRecommend'
  import introduceImg from '../components/introduceImg'
  import FooterComp from '../components/FooterComp'

  import { getHeartData } from '@/api/HealthReport'

  export default {
    data() {
      return {
        checkItem: 'carotid',
        hasData: '',
        visit_at: '',
        hosp_id: '',
        examData: [],
        date: '',
        hosp: '',
        answer: '',
        introduceSrc: 'https://zz-med-pub.oss-cn-hangzhou.aliyuncs.com/guanJia/images/jindongmai.png',
        recommendData: 0,
        hisReportList: []
      }
    },
    components: {
      'footer-comp': FooterComp,
      'introduce-img': introduceImg,
      'report-recommend': ReportRecommend,
      'his-report': HisReport
    },
    computed: {
      /**
       * 页脚跳转路由数据
       */
      footerProps() {
        return {
          hasNext: 1,
          btnTxt: '心超、心电图',
          visit_at: this.visit_at || '',
          hosp_id: this.hosp_id || '',
          path: '/guanjia/health/report/hosp/heart'
        }
      }
    },
    created() {
      this.$nextTick(() => {
        this.init()
      })
    },
    methods: {
      /**
       * 初始化
       */
      init(recordId = 0) {
        let param = { checkItem: this.checkItem }
        if (recordId !== 0) {
          param.record_id = recordId
        }

        // 获取检查报告数据
        getHeartData(param).then(res => {
          if (res.status === 0) {
            // 处理检查报告数据
            this.handleData(res.data)
          } else {
            this.$toast(res.msg)
          }
        })
      },
      /**
       * 处理检查报告数据
       * @param {Object} data 检查报告数据
       */
      handleData(data) {
        if (data.has_data === 1) {
          this.hasData = 1
          let dataCarotid = data['carotid'] || {}
          this.date = dataCarotid.visit_at || ''
          this.hosp = dataCarotid.hosp_name || ''
          this.answer = dataCarotid.answer_name || ''
          // 获取历史报告
          this.hisReportList = data.history
          this.recommendData = data.recommend || []
        } else {
          this.hasData = 0
        }
      },
      changeRecord(recordId) {
        this.init(recordId)
      }
    }
  }
</script>

<style lang='scss' scoped>
  @import '@/assets/scss/HealthReport/HealthReportExam.scss'
</style>
