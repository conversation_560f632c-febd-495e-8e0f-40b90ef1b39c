<template>
  <div ref="wrapper" class="wrapper">
    <!--没有异常指标-->
    <div class="noDataBox" v-if="noException === true">
      <img src="./images/good.png" alt="" class="good">
      <p>您很棒，没有指标异常</p>
      <div @click="back">查看健康报告</div>
    </div>
    <!--没有历史数据-->
    <div class="noDataBox" v-if="noData === true">
      <img src="./images/empty.png" alt="" class="empty">
      <p>没有您的历史报告数据</p>
      <div @click="back">查看健康报告</div>
    </div>
    <div class="tips-wrapper" v-if="noException === false">
      <div class="tips">
        <img src="./images/<EMAIL>" alt="hos"/>
        <span>在所有指标最新状态中</span>
      </div>
      <div class="tips">
        <img src="./images/baojing.png" alt="hos"/>
        <span class="tips-num">共{{num}}项异常</span>
      </div>
    </div>
    <div class="exam-wrapper" v-if="noException === false">
      <exam-item v-for="(item, index) in examData" :key="index" :examData="item"></exam-item>
    </div>
  </div>
</template>

<script>
  import ReportExamItem from './components/ReportExamItem'
  import { getVisitReport } from '@/api/HealthReport'

  export default {
    data() {
      return {
        clientHeight: 0,
        examData: [],
        hospIndexCount: 0,
        num: '',
        noException: '',
        noData: false
      }
    },
    components: { 'exam-item': ReportExamItem },
    created() {
      // 初始化
      this.init()
    },
    beforeMount() {
      // 获取页面可视高度
      this.clientHeight = `${document.documentElement.clientHeight}`
      // 浏览器重置时获取页面可视高度
      window.onresize = () => {
        this.clientHeight = `${document.documentElement.clientHeight}`
      }
    },
    watch: {
      clientHeight() {
        this.$refs.wrapper.style.minHeight = this.clientHeight + 'px'
      }
    },
    methods: {
      /**
       * 初始化
       */
      async init() {
        let param = {}
        // 获取报告数据
        await getVisitReport(param).then(res => {
          if (res.status === 0) {
            let deviantStatic = res.data.deviantStatic || {}
            let hospStatic = deviantStatic.hospStatic || {}
            this.num = deviantStatic.hospStaticCount
            this.hospIndexCount = deviantStatic.hospIndexCount

            if (this.num === 0) {
              this.noException = true
            } else {
              this.noException = false
            }
            let specialList = hospStatic.special || []
            let noSpecialList = hospStatic.no_special || []
            // let obj = {}
            specialList.forEach((item) => {
              this.examData.push(
                {
                  title: item[0].name,
                  reference: item[0].reference,
                  data: [
                    {
                      val: item[1].val,
                      time: '120分钟',
                      unit: item[1].unit,
                      updown: item[1].normal
                    },
                    {
                      val: item[0].val,
                      time: '0分钟',
                      unit: '',
                      updown: item[0].normal
                    }

                  ]
                }
              )
            })

            noSpecialList.forEach((item) => {
              this.examData.push(
                {
                  title: item.name,
                  reference: item.reference,
                  data: [
                    {
                      val: item.val,
                      unit: item.unit,
                      updown: item.normal
                    }
                  ]
                }
              )
            })
            if (this.hospIndexCount === 0) {
              this.noData = true
              this.noException = ''
            }
          } else {
            this.$toast(res.msg)
          }
        })
      },
      /**
       * 初始化
       */
      init2() {
        // TODO 异常统计数据接口联调

        this.num = '4'// 共4项异常
        this.examData = [
          {
            title: '糖化血红蛋白',
            reference: '',
            data: [
              {
                val: '1',
                unit: '%',
                updown: 0
              }
            ]
          },
          {
            title: '葡萄糖',
            reference: '3.80-6.20',
            data: [
              {
                val: '1',
                time: '120分钟',
                unit: 'mmol/L',
                updown: 1
              },
              {
                val: null,
                time: null,
                updown: null
              }
            ]
          }
        ]
      },
      // 返回上一页
      back() {
        window.history.go(-1)
      }
    }
  }
</script>

<style lang="scss" scoped>
  .wrapper {
    width: 100%;
    overflow: hidden;
    background-color: #F5F6FA;
    min-height: -webkit-fill-available;
    .noDataBox {
      .good {
        width: 183px;
        height: 166px;
        margin: 0 auto;
        margin-top: 110px;
      }
      .empty {
        width: 214px;
        height: 206px;
        margin: 0 auto;
        margin-top: 75px;
      }
      p {
        color: #666;
        font-size: 20px;
        margin: 14px 0 37px 0;
        line-height: 28px;
      }
      div {
        width: 160px;
        height: 50px;
        line-height: 50px;
        text-align: center;
        border-radius: 25px;
        border: 1px #F3955F solid;
        margin: 0 auto;
        font-size: 18px;
        color: #F3955F;
        cursor: pointer;
      }
    }
    .tips-wrapper {
      height: 35px;
      display: flex;
      margin: 0 15px;
      align-items: center;
      justify-content: space-between;

      .tips {
        display: flex;
        align-items: center;

        img {
          width: 15px;
          margin-right: 4px;
          margin-top: -2px;
        }

        span {
          color: #999;
          font-size: 13px;
        }

        .tips-num {
          color: #666;
        }
      }
    }

    .exam-wrapper {
      margin: 0 15px;
      overflow: hidden;
      border-radius: 9px;
      background-color: #fff;
    }
  }
</style>
