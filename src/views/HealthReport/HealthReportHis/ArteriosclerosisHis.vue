<template>
  <div ref="wrapper" class="wrapper">
    <div class="content">
      <div class="group" v-if="hasData === 0">
        <div class="carotid-nodata-img">
          <img src="../images/nodata.png" alt="nodata" />
          <p>暂无数据，建议完善检查</p>
        </div>
      </div>
      <div class="exam-wrapper" v-if="hasData === 1">
        <div class="point">
          <div>
            <img src="../images/<EMAIL>" alt="clock" />
            <span>{{date}}</span>
          </div>
          <div>
            <img src="../images/<EMAIL>" alt="hos" />
            <span>{{hosp}}</span>
          </div>
        </div>
        <div class="arteriosclerosis">
          <div class="arteriosclerosis-exam">
            <div class="arteriosclerosis-exam-item">
              <div class="arteriosclerosis-exam-title">
                <span>左侧</span>
                <i></i>
              </div>
              <div class="arteriosclerosis-exam-val">
                <span>ABI</span>
                <b>{{labi}}</b>
              </div>
              <div class="arteriosclerosis-exam-val">
                <span>baPWV</span>
                <b>{{lbapwv}}</b>
              </div>
            </div>
            <div class="arteriosclerosis-exam-item">
              <div class="arteriosclerosis-exam-title">
                <span>右侧</span>
                <i></i>
              </div>
              <div class="arteriosclerosis-exam-val">
                <span>ABI</span>
                <b>{{rabi}}</b>
              </div>
              <div class="arteriosclerosis-exam-val">
                <span>baPWV</span>
                <b>{{rbapwv}}</b>
              </div>
            </div>
          </div>
          <div class="arteriosclerosis-conclusion">
            <p>动脉硬化检测结论：</p>
            <p>{{conclusion_left}}</p>
            <p>{{conclusion_right}}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getHeartData } from '@/api/HealthReport'

export default {
  data() {
    return {
      checkItem: 'arteriosclerosis',
      hasData: '',
      visit_at: '',
      hosp_id: '',
      date: '',
      hosp: '',
      conclusion_left: '',
      conclusion_right: '',
      labi: '',
      rabi: '',
      lbapwv: '',
      rbapwv: '',
      clientHeight: 0
    }
  },
  created() {
    // 初始化
    this.record_id = this.$route.query.record_id || 0
    this.init(this.record_id)
  },
  beforeMount() {
    // 获取页面可视高度
    this.clientHeight = `${document.documentElement.clientHeight}`
    // 浏览器重置时获取页面可视高度
    window.onresize = () => {
      this.clientHeight = `${document.documentElement.clientHeight}`
    }
  },
  watch: {
    clientHeight() {
      this.$refs.wrapper.style.minHeight = this.clientHeight + 'px'
    }
  },
  methods: {
    /**
     * 初始化
     */
    init(recordId = 0) {
      let param = { checkItem: this.checkItem }
      if (recordId !== 0) {
        param.record_id = recordId
      }

      // 获取检查报告数据
      getHeartData(param).then(res => {
        if (res.status === 0) {
          // 处理检查报告数据
          this.handleData(res.data)
        } else {
          this.$toast(res.msg)
        }
      })
    },
    /**
     * 处理检查报告数据
     * @param {Object} data 检查报告数据
     */
    handleData(data) {
      if (data.has_data === 1) {
        this.hasData = 1
        let pwvData = data.pwv || {}
        this.date = pwvData.visit_at || ''
        this.hosp = pwvData.hosp_name || ''
        this.conclusion_left = pwvData.pwvabi_conclusion_left || ''
        this.conclusion_right = pwvData.pwvabi_conclusion_right || ''
        this.labi = pwvData.labi || ''
        this.rabi = pwvData.rabi || ''
        this.lbapwv = pwvData.lbapwv || ''
        this.rbapwv = pwvData.rbapwv || ''

        this.recommendData = data.recommend || []
      } else {
        this.hasData = 0
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/assets/scss/HealthReport/HealthReportExam.scss'
</style>
