<template>
  <div ref="wrapper" class="wrapper">
    <div class="content">
      <div class="group" v-if="hasData === 0">
        <div class="carotid-nodata-img">
          <img src="../images/nodata.png" alt="nodata" />
          <p>暂无数据，建议完善检查</p>
        </div>
      </div>
      <div class="exam-wrapper" v-for="(exam, index) in examData" :key="index">
        <div class="point">
          <div>
            <img src="../images/<EMAIL>" alt="clock" />
            <span>{{exam.date}}</span>
          </div>
          <div>
            <img src="../images/<EMAIL>" alt="hos" />
            <span>{{exam.hosp}}</span>
          </div>
        </div>
        <div class="exam-content">
          <exam-item v-for="(item, index) in exam.examItems" :key="index" :examData="item"></exam-item>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ReportExamItem from '../components/ReportExamItem'
import { getHeartData } from '@/api/HealthReport'

export default {
  data() {
    return {
      checkItem: 'hospbp',
      hasData: '',
      visit_at: '',
      hosp_id: '',
      examData: [],
      clientHeight: 0
    }
  },
  components: { 'exam-item': ReportExamItem },
  created() {
    // 初始化
    this.record_id = this.$route.query.record_id || 0
    this.init(this.record_id)
  },
  beforeMount() {
    // 获取页面可视高度
    this.clientHeight = `${document.documentElement.clientHeight}`
    // 浏览器重置时获取页面可视高度
    window.onresize = () => {
      this.clientHeight = `${document.documentElement.clientHeight}`
    }
  },
  watch: {
    clientHeight() {
      this.$refs.wrapper.style.minHeight = this.clientHeight + 'px'
    }
  },
  methods: {
    /**
     * 初始化
     */
    init(recordId = 0) {
      let param = { checkItem: this.checkItem }
      if (recordId !== 0) {
        param.record_id = recordId
      }

      // 获取检查报告数据
      getHeartData(param).then(res => {
        if (res.status === 0) {
          // 处理检查报告数据
          this.handleData(res.data)
        } else {
          this.$toast(res.msg)
        }
      })
    },
    /**
     * 处理检查报告数据
     * @param {Object} data 检查报告数据
     */
    handleData(data) {
      if (data.has_data === 1) {
        this.hasData = 1
        let bpArr = data.bpArr || {}
        let bpObj = {}
        bpObj.date = bpArr.visit_at || ''
        bpObj.hosp = bpArr.hosp_name || ''
        bpObj.examItems = [
          {
            title: '血压',
            data: [
              {
                val: bpArr.sbp + '/' + bpArr.dbp,
                unit: 'mmHg'
              }
            ]
          },
          {
            title: '心率',
            data: [
              {
                val: bpArr.pulse,
                unit: '次/分'
              }
            ]
          }
        ]
        this.examData.push(bpObj)

        this.recommendData = data.recommend || []
      } else {
        this.hasData = 0
      }
    }
  }
}
</script>

<style lang='scss' scoped>
@import '@/assets/scss/HealthReport/HealthReportExam.scss'
</style>
