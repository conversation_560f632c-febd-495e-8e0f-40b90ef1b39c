<template>
  <div ref="wrapper" class="wrapper">
    <div class="content">
      <div class="group" v-if="hasData === 0">
        <div class="carotid-nodata-img">
          <img src="../images/nodata.png" alt="nodata"/>
          <p>暂无数据，建议完善检查</p>
        </div>
      </div>
      <div class="exam-wrapper" v-for="(exam, index) in examData" :key="index">
        <div class="point" v-if="exam.date!==''">
          <div>
            <img src="../images/<EMAIL>" alt="clock"/>
            <span>{{exam.date==''?visit_at:exam.date}}</span>
          </div>
          <div>
            <img src="../images/<EMAIL>" alt="hos"/>
            <span>{{exam.hosp==''?hosp_name:exam.hosp}}</span>
          </div>
        </div>
        <div class="exam-content" v-if="exam.date!==''">
          <exam-item v-for="(item, index) in exam.examItems" :key="index" :examData="item"></exam-item>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import ReportExamItem from '../components/ReportExamItem'
  import { getHeartData } from '@/api/HealthReport'

  export default {
    data() {
      return {
        checkItem: 'heart',
        hasData: '',
        visit_at: '',
        hosp_name: '',
        hosp_id: '',
        examData: [],
        clientHeight: 0
      }
    },
    components: { 'exam-item': ReportExamItem },
    created() {
      // 初始化
      this.record_id = this.$route.query.record_id || 0
      this.init(this.record_id)
    },
    beforeMount() {
      // 获取页面可视高度
      this.clientHeight = `${document.documentElement.clientHeight}`
      // 浏览器重置时获取页面可视高度
      window.onresize = () => {
        this.clientHeight = `${document.documentElement.clientHeight}`
      }
    },
    watch: {
      clientHeight() {
        this.$refs.wrapper.style.minHeight = this.clientHeight + 'px'
      }
    },
    methods: {
      /**
       * 初始化
       */
      init(recordId = 0) {
        let param = { checkItem: this.checkItem }
        if (recordId !== 0) {
          param.record_id = recordId
        }

        // 获取检查报告数据
        getHeartData(param).then(res => {
          if (res.status === 0) {
            // 处理检查报告数据
            this.handleData(res.data)
          } else {
            this.$toast(res.msg)
          }
        })
      },
      /**
       * 处理检查报告数据
       * @param {Object} data 检查报告数据
       */
      handleData(data) {
        if (data.has_data === 1) {
          this.hasData = 1
          let superObj = {}
          let electObj = {}

          let data954 = data['954'] || {}
          let data930001 = data['930001'] || {}
          let data930003 = data['930003'] || {}
          let data930004 = data['930004'] || {}
          let data930005 = data['930005'] || {}
          let data930006 = data['930006'] || {}
          let data930007 = data['930007'] || {}
          let data930008 = data['930008'] || {}
          let data952001 = data['952001'] || {}

          superObj.date = data.heart_chao_visit_at
          superObj.hosp = data.heart_chao_hosp_name
          superObj.examItems = [
            {
              title: '左房内径',
              reference: data930001.reference,
              data: [
                {
                  val: data930001.answer_name,
                  unit: data930001.unit,
                  updown: data930001.updown
                }
              ]
            },
            {
              title: '左室后壁厚度',
              reference: data930004.reference,
              data: [
                {
                  val: data930004.answer_name,
                  unit: data930004.unit,
                  updown: data930004.updown
                }
              ]
            },
            {
              title: 'LVEF',
              reference: data930005.reference,
              data: [
                {
                  val: data930005.answer_name,
                  unit: data930005.unit,
                  updown: data930005.updown
                }
              ]
            },
            {
              title: '左室舒张末期内径',
              reference: data930003.reference,
              data: [
                {
                  val: data930003.answer_name,
                  unit: data930003.unit,
                  updown: data930003.updown
                }
              ]
            },
            {
              title: '左室收缩末期内径',
              reference: data930006.reference,
              data: [
                {
                  val: data930006.answer_name,
                  unit: data930006.unit,
                  updown: data930006.updown
                }
              ]
            },
            {
              title: '主动脉根部内径',
              reference: data930007.reference,
              data: [
                {
                  val: data930007.answer_name,
                  unit: data930007.unit,
                  updown: data930007.updown
                }
              ]
            },
            {
              title: '心超结论：',
              conclusion: data930008.answer_name
            }
          ]

          this.examData.push(superObj)
          if (data.ecg_visit_at !== undefined) {
            electObj.date = data.ecg_visit_at
            electObj.hosp = data.ecg_hosp_name
            electObj.examItems = [
              {
                title: '心律',
                data: [
                  {
                    val: data['952']
                  }
                ]
              },
              {
                title: '心率',
                reference: data952001.reference,
                data: [
                  {
                    val: data952001.answer_name,
                    unit: data952001.unit,
                    updown: data952001.updown
                  }
                ]
              },
              {
                title: '心电图结论：',
                conclusion: data954.answer_name
              }
            ]
            this.examData.push(electObj)
          }
          this.visit_at = data.kidney_date
          this.hosp_name = data.kidney_name
          this.recommendData = data.recommend || []
        } else {
          this.hasData = 0
        }
      }
    }
  }
</script>

<style lang="scss" scoped>
  @import '@/assets/scss/HealthReport/HealthReportExam.scss'
</style>
