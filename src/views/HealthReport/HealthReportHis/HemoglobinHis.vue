<template>
  <div ref="wrapper" class="wrapper">
    <div class="content">
      <div v-if="hasData === 0" class="group">
        <div class="carotid-nodata-img">
          <img src="../images/nodata.png" alt="nodata"/>
          <p>暂无数据，建议完善检查</p>
        </div>
      </div>
      <div
        v-for="(exam, index) in examData"
        :key="index"
        class="exam-wrapper"
      >
        <div class="point">
          <div>
            <img src="../images/<EMAIL>" alt="clock"/>
            <span>{{exam.date}}</span>
          </div>
          <div>
            <img src="../images/<EMAIL>" alt="hos"/>
            <span>{{exam.hosp}}</span>
          </div>
        </div>
        <div class="exam-content">
          <exam-item v-for="(item, index) in exam.examItems" :key="index" :examData="item"></exam-item>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import ReportExamItem from '../components/ReportExamItem'
  import { getHeartData } from '@/api/HealthReport'

  export default {
    data() {
      return {
        checkItem: 'hemoglobin',
        hasData: '',
        visit_at: '',
        hosp_id: '',
        examData: [],
        clientHeight: 0
      }
    },
    components: { 'exam-item': ReportExamItem },
    created() {
      // 初始化
      this.record_id = this.$route.query.record_id || 0
      this.init(this.record_id)
    },
    beforeMount() {
      // 获取页面可视高度
      this.clientHeight = `${document.documentElement.clientHeight}`
      // 浏览器重置时获取页面可视高度
      window.onresize = () => {
        this.clientHeight = `${document.documentElement.clientHeight}`
      }
    },
    watch: {
      clientHeight() {
        this.$refs.wrapper.style.minHeight = this.clientHeight + 'px'
      }
    },
    methods: {
      /**
       * 初始化
       */
      init(recordId = 0) {
        let param = { checkItem: this.checkItem }
        if (recordId !== 0) {
          param.record_id = recordId
        }

        // 获取检查报告数据
        getHeartData(param).then(res => {
          if (res.status === 0) {
            // 处理检查报告数据
            this.handleData(res.data)
          } else {
            this.$toast(res.msg)
          }
        })
      },
      /**
       * 处理检查报告数据
       * @param {Object} data 检查报告数据
       */
      handleData(data) {
        if (data.has_data === 1) {
          this.hasData = 1
          let examObj = {}
          examObj.date = data.visit_at || ''
          examObj.hosp = data.hosp_name || ''

          let data876 = data['876'] || {}
          let data875001 = data['875001'] || {}
          let data875004 = data['875004'] || {}
          let data875006 = data['875006'] || {}
          let data875009 = data['875009'] || {}
          let data875011 = data['875011'] || {}
          let data875014 = data['875014'] || {}

          examObj.examItems = [
            {
              title: '糖化血红蛋白',
              reference: data876.reference,
              data: [
                {
                  val: data876.answer_name,
                  unit: data876.unit,
                  updown: data876.updown
                }
              ]
            },
            {
              title: '葡萄糖',
              reference: data875001.reference,
              data: [
                {
                  val: data875004.answer_name,
                  time: '120分钟',
                  unit: data875004.unit === undefined ? data875001.unit : data875004.unit,
                  updown: data875004.updown
                },
                {
                  val: data875001.answer_name,
                  time: '0分钟',
                  updown: data875001.updown
                }
              ]
            },
            {
              title: '胰岛素',
              reference: data875006.reference,
              data: [
                {
                  val: data875009.answer_name,
                  time: '120分钟',
                  unit: data875009.unit === undefined ? data875006.unit : data875009.unit,
                  updown: data875009.updown
                },
                {
                  val: data875006.answer_name,
                  time: '0分钟',
                  updown: data875006.updown
                }
              ]
            },
            {
              title: 'C肽',
              reference: data875011.reference,
              data: [
                {
                  val: data875014.answer_name,
                  time: '120分钟',
                  unit: data875014.unit === undefined ? data875011.unit : data875014.unit,
                  updown: data875014.updown
                },
                {
                  val: data875011.answer_name,
                  time: '0分钟',
                  updown: data875011.updown
                }
              ]
            }
          ]
          this.examData.push(examObj)

          let listLab = data.list_lab || {}
          let listLabValue = []
          let visitAt = (listLab.visit_at || []).map((val, index) => {
            let formatDate = this.formatDate(val)
            listLabValue.push([formatDate, listLab.value[index]])
            return formatDate
          })
          let basicLab = data.basic_lab || {}
          let basicVisitAt = basicLab.visit_at
            ? this.formatDate(basicLab.visit_at)
            : ''
          // 去重并排序日期
          visitAt = Array.from(
            new Set([basicVisitAt, ...visitAt])
          ).sort()

          this.lineChartProps = {
            xRotate: visitAt.length > 5 ? 45 : 0,
            legendData: ['基线', '糖化血红蛋白'],
            value: [
              {
                val: listLabValue,
                name: '糖化血红蛋白',
                color: '#F67710'
              },
              {
                val: [[basicVisitAt, basicLab.value || '']],
                name: '基线',
                symbol: 'triangle',
                color: '#009DD9'
              }
            ],
            visit_at: visitAt
          }
          this.recommendData = data.recommend || []
        } else {
          this.hasData = 0
        }
      },
      /**
       * 格式化日期
       * @param {String} date 日期
       * @return {String} 格式化日期
       */
      formatDate(date) {
        date = new Date(date)
        let y = String(date.getFullYear()).slice(2)
        let m = date.getMonth() + 1
        m = m < 10 ? '0' + m : m
        let d = date.getDate()
        d = d < 10 ? '0' + d : d
        return y + '/' + m + '/' + d
      }
    }
  }
</script>

<style lang='scss' scoped>
  @import '@/assets/scss/HealthReport/HealthReportExam.scss'
</style>
