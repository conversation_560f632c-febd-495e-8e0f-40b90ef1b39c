<template>
  <div ref="wrapper" class="wrapper">
    <div class="content">
      <div class="group" v-if="hasData === 0">
        <div class="carotid-nodata-img">
          <img src="../images/nodata.png" alt="nodata" />
          <p>暂无数据，建议完善检查</p>
        </div>
      </div>
      <div class="exam-wrapper" v-if="hasData === 1">
        <div class="point">
          <div>
            <img src="../images/<EMAIL>" alt="clock" />
            <span>{{date}}</span>
          </div>
          <div>
            <img src="../images/<EMAIL>" alt="hos" />
            <span>{{hosp}}</span>
          </div>
        </div>
        <div class="sugarnet">
          <div class="sugarnet-item">
            <div class="sugarnet-title">
              <span>左眼</span>
              <i></i>
            </div>
            <div class="sugarnet-val" v-if="LAnswer !== '' || RAnswer !== ''">{{LAnswer}}</div>
            <div class="sugarnet-pics">
              <div v-if="leftPic.length > 0">
                <div class="sugarnet-pic" v-for="(leftP, index) in leftPic" :key="index">
                  <img :src="leftP" alt="leftPic" @click="showBigImg($event)" />
                </div>
              </div>
              <div v-else class="sugarnet-pic-no">
                <img src="../images/noEyes.png" alt="暂无图片" />
                <span>暂无图片</span>
              </div>
            </div>
          </div>
          <i class="sugarnet-line"></i>
          <div class="sugarnet-item">
            <div class="sugarnet-title">
              <span>右眼</span>
              <i></i>
            </div>
            <div class="sugarnet-val" v-if="LAnswer !== '' || RAnswer !== ''">{{RAnswer}}</div>
            <div class="sugarnet-pics">
              <div v-if="rightPic.length > 0">
                <div class="sugarnet-pic" v-for="(rightP, index) in rightPic" :key="index">
                  <img :src="rightP" alt="rightPic" @click="showBigImg($event)" />
                </div>
              </div>
              <div v-else class="sugarnet-pic-no">
                <img src="../images/noEyes.png" alt="暂无图片" />
                <span>暂无图片</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <big-img v-if="isShowBigImg" @hideBigImg="hideBigImg" :bigImg="bigImg"></big-img>
  </div>
</template>

<script>
import BigImg from '../components/BigImg'
import { getHeartData } from '@/api/HealthReport'

export default {
  data() {
    return {
      checkItem: 'sugarnet',
      hasData: '',
      visit_at: '',
      hosp_id: '',
      date: '',
      hosp: '',
      leftPic: [],
      rightPic: [],
      LAnswer: '',
      RAnswer: '',
      isShowBigImg: false,
      bigImg: { src: '' },
      clientHeight: 0
    }
  },
  components: { 'big-img': BigImg },
  created() {
    // 初始化
    this.record_id = this.$route.query.record_id || 0
    this.init(this.record_id)
  },
  beforeMount() {
    // 获取页面可视高度
    this.clientHeight = `${document.documentElement.clientHeight}`
    // 浏览器重置时获取页面可视高度
    window.onresize = () => {
      this.clientHeight = `${document.documentElement.clientHeight}`
    }
  },
  watch: {
    clientHeight() {
      this.$refs.wrapper.style.minHeight = this.clientHeight + 'px'
    }
  },
  methods: {
    /**
     * 初始化
     */
    init(recordId = 0) {
      let param = { checkItem: this.checkItem }
      if (recordId !== 0) {
        param.record_id = recordId
      }

      // 获取检查报告数据
      getHeartData(param).then(res => {
        if (res.status === 0) {
          // 处理检查报告数据
          this.handleData(res.data)
        } else {
          this.$toast(res.msg)
        }
      })
    },
    /**
     * 处理检查报告数据
     * @param {Object} data 检查报告数据
     */
    handleData(data) {
      if (data.has_data === 1) {
        this.hasData = 1
        this.date = data.visit_at || ''
        this.hosp = data.hosp_name || ''
        let dataLeft = data['left_eye'] || {}
        let dataRight = data['right_eye'] || {}
        this.LAnswer = dataLeft.answer_name || ''
        this.RAnswer = dataRight.answer_name || ''
        this.leftPic = data.left_pic || []
        this.rightPic = data.right_pic || []
        this.recommendData = data.recommend || []
      } else {
        this.hasData = 0
      }
    },
    /**
     * 显示大图
     * @param {Object} e 事件对象
     */
    showBigImg(e) {
      this.bigImg = { src: e.currentTarget.src, center: true }
      this.isShowBigImg = true
      // 禁止body滚动
      let docBody = document.body
      docBody.addEventListener('touchmove', this.bodyScroll, false)
      document.body.style.overflow = 'hidden'
      // chrome滚动警告
      document.body.style.touchAction = 'none'
    },
    /**
     * 隐藏大图
     */
    hideBigImg() {
      this.isShowBigImg = false
      // 放开body滚动
      let docBody = document.body
      docBody.addEventListener('touchmove', this.bodyScroll, false)
      document.body.style.overflow = ''
      // chrome滚动警告
      document.body.style.touchAction = ''
    },
    /**
     * 阻止body滚动默认事件
     */
    bodyScroll(event) {
      event.preventDefault()
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/assets/scss/HealthReport/HealthReportExam.scss'
</style>
