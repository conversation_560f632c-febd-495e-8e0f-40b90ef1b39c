<template>
  <div ref="wrapper" class="wrapper">
    <div class="content">
      <div v-if="hasData === 0" class="group">
        <div class="carotid-nodata-img">
          <img src="../images/nodata.png" alt="nodata" />
          <p>暂无数据，建议完善检查</p>
        </div>
        <div class="carotid-nodata-tips">
          <div>
            <img src="../images/<EMAIL>" alt="attention" />
            <p>颈动脉超声检查是诊断、评估颈动脉壁病变的有效手段之一，能够反应糖尿病患者大血管病变、动脉粥样硬化、颈动脉狭窄的情况，对预防缺血性脑卒中有重要意义。</p>
          </div>
        </div>
      </div>
      <div v-if="hasData === 1" class="exam-wrapper">
        <div class="point">
          <div>
            <img src="../images/<EMAIL>" alt="clock" />
            <span>{{date}}</span>
          </div>
          <div>
            <img src="../images/<EMAIL>" alt="hos" />
            <span>{{hosp}}</span>
          </div>
        </div>
        <div class="neuropathy">
          <img src="../images/assignment.png" alt="answer" />
          <span class="neuropathy-title">结论:</span>
          <span class="neuropathy-answer">{{answer}}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getHeartData } from '@/api/HealthReport'

export default {
  data() {
    return {
      checkItem: 'carotid',
      hasData: '',
      visit_at: '',
      hosp_id: '',
      examData: [],
      date: '',
      hosp: '',
      answer: '',
      clientHeight: 0
    }
  },
  created() {
    // 初始化
    this.record_id = this.$route.query.record_id || 0
    this.init(this.record_id)
  },
  beforeMount() {
    // 获取页面可视高度
    this.clientHeight = `${document.documentElement.clientHeight}`
    // 浏览器重置时获取页面可视高度
    window.onresize = () => {
      this.clientHeight = `${document.documentElement.clientHeight}`
    }
  },
  watch: {
    clientHeight() {
      this.$refs.wrapper.style.minHeight = this.clientHeight + 'px'
    }
  },
  methods: {
    /**
     * 初始化
     */
    init(recordId = 0) {
      let param = { checkItem: this.checkItem }
      if (recordId !== 0) {
        param.record_id = recordId
      }

      // 获取检查报告数据
      getHeartData(param).then(res => {
        if (res.status === 0) {
          // 处理检查报告数据
          this.handleData(res.data)
        } else {
          this.$toast(res.msg)
        }
      })
    },
    /**
     * 处理检查报告数据
     * @param {Object} data 检查报告数据
     */
    handleData(data) {
      if (data.has_data === 1) {
        this.hasData = 1
        let dataCarotid = data['carotid'] || {}
        this.date = dataCarotid.visit_at || ''
        this.hosp = dataCarotid.hosp_name || ''
        this.answer = dataCarotid.answer_name || ''

        this.recommendData = data.recommend || []
      } else {
        this.hasData = 0
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/assets/scss/HealthReport/HealthReportExam.scss'
</style>
