<template>
  <div ref="wrapper" class="wrapper">
    <div v-if="noData == true" class="noDataBox">
      <img src="./images/empty.png" alt="" class="empty">
      <p>没有您的历史报告数据</p>
    </div>
    <div class="his" v-if="noData == false">
      <div
        v-for="item in hisData"
        :key="item.id"
        @click="goReport(item)"
        class="group"
      >
        <div class="group-up">
          <div class="group-left">
            <div class="group-info">
              <img src="./images/<EMAIL>" alt="hos"/>
              <span>{{item.hosp_name}}</span>
            </div>
            <div class="group-info">
              <img src="./images/<EMAIL>" alt="clock"/>
              <span class="group-time">{{item.visit_at}}</span>
            </div>
          </div>
          <div
            v-if="item.type === 1"
            class="group-right"
          >
            <img src="./images/jiance.png" alt="attention"/>
            <span>检查</span>
          </div>
          <div
            v-else
            class="group-right group-see"
          >
            <img src="./images/jiuzhen.png" alt="attention"/>
            <span>就诊</span>
          </div>
        </div>
        <div class="group-down">
          <img src="./images/<EMAIL>" alt="attention"/>
          <span v-if="item.has_data === 1">检查项：{{item.listmsg}}</span>
          <span v-else>该次就诊未进行检查</span>
        </div>
      </div>
    </div>
    <transition name="fade">
      <div v-show="showTips" class="tips-modal">
        <div class="tips-wrapper">
          <h1 class="tips-title">提示</h1>
          <div class="tips-content">
            <span>该次就诊未进行检查</span>
          </div>
          <div @click="closeTips" class="tips-footer">
            <span>确定</span>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
  import { getVisitHistory } from '@/api/HealthReport'

  export default {
    data() {
      return {
        clientHeight: 0,
        hisData: [],
        showTips: false,
        noData: false
      }
    },
    created() {
      // 初始化
      this.init()
    },
    beforeMount() {
      // 获取页面可视高度
      this.clientHeight = `${document.documentElement.clientHeight}`
      // 浏览器重置时获取页面可视高度
      window.onresize = () => {
        this.clientHeight = `${document.documentElement.clientHeight}`
      }
    },
    watch: {
      clientHeight() {
        this.$refs.wrapper.style.minHeight = this.clientHeight + 'px'
      }
    },
    methods: {
      /**
       * 初始化
       */
      init() {
        let that = this
        getVisitHistory().then(function (res) {
          if (res.status === 0) {
            that.hisData = res.data
            if (that.hisData.length === 0) {
              that.noData = true
            }
          } else {
            this.$toast(res.msg)
          }
        })
      },
      /**
       * 前往报告详情页
       * @param {Object} item 报告数据
       */
      goReport(item) {
        if (item.has_data === 0) {
          this.showTips = true
          return false
        }

        this.$router.push({
          path: '/guanjia/health/report/detail',
          query: { id: item.id }
        })
      },
      /**
       * 关闭提示
       */
      closeTips() {
        this.showTips = false
      }
    }
  }
</script>

<style lang="scss" scoped>
  .wrapper {
    width: 100%;
    overflow: hidden;
    background-color: #F5F6FA;
    min-height: -webkit-fill-available;
    .noDataBox{
      width: 100%;
      height: 100%;
      .empty{
        width: 282px;
        height: 274px;
        margin: 0 auto;
        margin-top: 110px;
        margin-bottom: 20px;
      }
      p{
        color: #999;
        font-size: 15px;
        line-height: 21px;
      }
    }
    .his {
      margin: 15px;
      overflow: hidden;
      border-radius: 9px;
      background-color: #fff;

      .group {
        padding: 15px;
        border-bottom: 1px solid #F2F3F4;

        &:last-of-type {
          border-bottom: 0;
        }

        .group-up {
          display: flex;
          align-items: center;
          justify-content: space-between;

          .group-left {

            .group-info {
              display: flex;
              margin-top: 5px;
              align-items: flex-start;
              justify-content: flex-start;

              img {
                width: 15px;
                margin-top: 2px;
              }

              span {
                width: 200px;
                height: 20px;
                font-size: 16px;
                font-weight: 400;
                text-align: left;
                color: #262626;
                margin-left: 4px;
                overflow: hidden;
                line-height: 20px;
                white-space: nowrap;
                text-overflow: ellipsis;
              }

              .group-time {
                color: #999;
              }
            }
          }

          .group-right {
            width: 75px;
            height: 30px;
            color: #FFB44C;
            font-size: 15px;
            font-weight: 500;
            border-radius: 18px;
            border: 1px solid #FFB44C;
            display: flex;
            align-items: center;
            justify-content: center;

            img {
              width: 15px;
            }

            span {
              margin-left: 2px;
              line-height: 30px;
            }
          }

          .group-see {
            color: #7AC3FF;
            border: 1px solid #7AC3FF;
          }
        }

        .group-down {
          color: #666;
          font-size: 14px;
          margin-top: 5px;
          font-weight: 400;
          display: flex;
          align-items: flex-start;
          justify-content: flex-start;

          img {
            width: 15px;
            margin-top: 2px;
          }

          span {
            text-align: left;
            margin-left: 4px;
            line-height: 20px;
          }
        }
      }
    }

    .tips-modal {
      background: rgba(0, 0, 0, 0.5);
      position: fixed;
      overflow: auto;
      z-index: 999;
      bottom: 0;
      right: 0;
      left: 0;
      top: 0;

      .tips-wrapper {
        width: 330px;
        transform: translate(-50%, -50%);
        position: fixed;
        left: 50%;
        top: 50%;
        background: #FFF;
        border-radius: 10px;
        border: 1px solid #e8e8e8;

        .tips-title {
          height: 60px;
          color: #333;
          font-size: 20px;
          font-weight: 400;
          line-height: 60px;
        }

        .tips-content {
          height: 22px;
          font-size: 16px;
          font-weight: 400;
          color: #262626;
          line-height: 22px;
          text-align: center;
          padding-bottom: 35px;
          border-bottom: 1px solid #E8E8E8;
        }

        .tips-footer {
          height: 40px;
          color: #333;
          font-size: 17px;
          font-weight: 400;
          line-height: 40px;
        }
      }
    }
  }
</style>
