<template>
  <div class="wrapper">
    <div class="header-backgroud">
      <img src="./images/bg-header.png" alt="bg-header">
    </div>
    <div class="content">
      <div class="header" v-if="metaboliseScore!=='-' && metaboliseScore!=='' ">
        <h1>{{metaboliseScore}}</h1>
        <p>代谢指数得分</p>
      </div>
      <div class="group" v-if="metaboliseScore!=='-' && metaboliseScore!==''">
        <p class="group-title">代谢指数与心血管</p>
        <div class="metabolism-score">
          <div class="metabolism-echarts-wrapper">
            <div class="metabolism-echarts-box" ref="metabolismEchart"></div>
            <div class="metabolism-echarts-title">
              <p class="echarts-title-main">
                <span>{{metaboliseScore}}</span>分
              </p>
              <p class="echarts-title-sub">代谢指数</p>
            </div>
          </div>
          <div>
            <p class="metabolism-score-p">{{metaboliseText}}</p>
          </div>
          <div class="metabolism-echarts-wrapper">
            <div class="metabolism-echarts-box" ref="cardiovascularEchart"></div>
            <div class="metabolism-echarts-title">
              <p class="echarts-title-main">
                <span>{{metabolisePercent}}</span>
              </p>
              <p class="echarts-title-sub2">三年发生心血管</p>
              <p class="echarts-title-sub2">疾病的概率</p>
            </div>
          </div>
        </div>
        <p class="group-title">代谢指数在人群中的水平</p>
        <div class="metabolism-level">
          <div class="level-group">
            <div class="level-box">
              <div class="level-line level-total" :style="totalStyle">
                <span>全国</span>
                <span>{{totalScore}}</span>
              </div>
            </div>
            <div class="level-box">
              <div class="level-line level-mine" :style="myStyle">
                <span>我</span>
                <span>{{metaboliseScore}}</span>
              </div>
            </div>
            <div class="level-txt">
              <p>高于全国平均水平</p>
              <p>{{totalPercent}}</p>
            </div>
          </div>
          <div class="level-group">
            <div class="level-box">
              <div class="level-line level-area" :style="areaStyle">
                <span>{{area}}</span>
                <span>{{cityScore}}</span>
              </div>
            </div>
            <div class="level-box">
              <div class="level-line level-mine" :style="myStyle">
                <span>我</span>
                <span>{{metaboliseScore}}</span>
              </div>
            </div>
            <div class="level-txt">
              <p>高于本地区平均水平</p>
              <p>{{cityPercent}}</p>
            </div>
          </div>
        </div>
      </div>
      <div class="introduce-wrapper">
        <introduce-img :src="introduceSrc" ref="introduceImg"></introduce-img>
      </div>
      <div class="group" v-if="recommendData && metaboliseScore!=='-' && metaboliseScore!==''">
        <p class="group-title">为您推荐</p>
        <report-recommend v-for="(item, index) in recommendData" :key="index" :data="item"></report-recommend>
      </div>
    </div>
    <footer>
      <footer-comp :footerProps="footerProps"></footer-comp>
    </footer>
  </div>
</template>

<script>
  import echarts from 'echarts'

  import { getMetaboliseScore } from '@/api/HealthReport'

  import ReportRecommend from './components/ReportRecommend'
  import introduceImg from './components/introduceImg'
  import FooterComp from './components/FooterComp'

  export default {
    data() {
      return {
        introduceSrc: require('./images/metabolism.png'),
        visit_at: '',
        hosp_id: '',
        totalScore: '',
        totalPercent: '',
        area: '上海',
        cityScore: '',
        cityPercent: '',
        metaboliseScore: '',
        metabolisePercent: '',
        metaboliseText: '',
        recommendData: 0
      }
    },
    components: {
      'footer-comp': FooterComp,
      'introduce-img': introduceImg,
      'report-recommend': ReportRecommend
    },
    computed: {
      // 代谢指数评估 echarts数据
      metabolismData() {
        let baseScore = 100
        let metaboliseScore = Number(this.metaboliseScore)
        let metaboliseOtherScore = baseScore - metaboliseScore
        let metabolisePercent = this.metabolisePercent
        let metaboliseVal = Number(metabolisePercent.split('%')[0])
        let metaboliseOtherVal = baseScore - metaboliseVal
        return [
          {
            ref: this.$refs.metabolismEchart,
            data: [
              {
                value: metaboliseScore,
                colorStops: ['#FBAF25', '#F67710']
              },
              {
                value: metaboliseOtherScore,
                colorStops: ['#E8E8E8', '#E8E8E8']
              }
            ]
          },
          {
            ref: this.$refs.cardiovascularEchart,
            data: [
              {
                value: metaboliseVal,
                colorStops: ['#FFD4CD', '#EC667F']
              },
              {
                value: metaboliseOtherVal,
                colorStops: ['#98F1C9', '#5FDF98']
              }
            ]
          }
        ]
      },
      totalStyle() {
        let style = {}
        let totalScore = this.totalScore
        if (totalScore !== '') {
          style.width = totalScore + '%'
        }
        return style
      },
      areaStyle() {
        let style = {}
        let cityScore = this.cityScore
        if (cityScore !== '') {
          style.width = cityScore + '%'
        }
        return style
      },
      myStyle() {
        let style = {}
        let metaboliseScore = this.metaboliseScore
        if (metaboliseScore !== '') {
          style.width = metaboliseScore + '%'
        }
        return style
      },
      /**
       * 页脚跳转路由数据
       */
      footerProps() {
        return {
          hasNext: 1,
          btnTxt: '家庭血糖',
          visit_at: this.visit_at || '',
          hosp_id: this.hosp_id || '',
          path: '/guanjia/health/report/family/bg'
        }
      }
    },
    methods: {
      /**
       * 初始化
       */
      async init() {
        let param = {}
        this.visit_at = this.$route.query.visit_at || ''
        this.hosp_id = this.$route.query.hosp_id || ''
        if (this.visit_at !== '' && this.hosp_id !== '') {
          param.visit_at = this.visit_at
          param.hosp_id = this.hosp_id
        }

        // 获取代谢指数 数据
        await getMetaboliseScore(param).then(res => {
          if (res.status === 0) {
            // 处理数据
            this.handleData(res.data)
          } else {
            this.$toast(res.msg)
          }
        })
        if (this.metaboliseScore !== '-') {
          // echarts初始化
          this.metabolismData.forEach(metabolism => {
            this.initEchart(metabolism.ref, metabolism.data)
          })
        }
      },
      /**
       * 处理数据
       * @param {Object} data 代谢指数数据
       */
      handleData(data) {
        let relate = data.relate || {}
        this.metaboliseScore = relate.score || ''

        if (this.metaboliseScore === '-' || this.metaboliseScore === '') {
          this.$refs.introduceImg.isShowMoreImg = true
        }

        this.metabolisePercent = relate.percent || ''
        this.metaboliseText = relate.text || ''
        let scale = data.scale || {}
        this.totalScore = scale.total != null ? scale.total : ''
        this.totalPercent = scale.total_percent || ''
        this.area = scale.area || ''
        this.cityScore = scale.city != null ? scale.city : ''
        this.cityPercent = scale.city_percent || ''
        this.recommendData = data.recommend || []
      },
      /**
       * echarts初始化
       * @param {Object} ref DOM元素
       * @param {Object} data 图表数据
       */
      initEchart(ref, data) {
        let myEchart = echarts.init(ref)
        myEchart.setOption({
          series: [
            {
              type: 'pie', // 图标类型
              center: ['50%', '50%'], // 饼图的中心（圆心）坐标，数组的第一项是横坐标，第二项是纵坐标
              radius: ['70%', '100%'], // 饼图的半径，数组第一项是内半径，第二项是外半径
              hoverAnimation: false, // 是否开启 放大动画效果
              label: {
                // 饼图图形上的文本标签
                normal: {
                  show: false
                }
              },
              startAngle: 90, // 起始角度
              data: [
                {
                  value: data[0].value,
                  itemStyle: {
                    color: {
                      type: 'linear',
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      colorStops: [
                        {
                          offset: 0,
                          color: data[0].colorStops[0] // 0% 处的颜色
                        },
                        {
                          offset: 1,
                          color: data[0].colorStops[1] // 100% 处的颜色
                        }
                      ],
                      global: false // 缺省为 false
                    }
                  }
                },
                {
                  value: data[1].value,
                  itemStyle: {
                    normal: {
                      // normal：图形在默认状态下的样式；emphasis：图形在高亮状态下的样式
                      color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [
                          {
                            offset: 0,
                            color: data[1].colorStops[0] // 0% 处的颜色
                          },
                          {
                            offset: 1,
                            color: data[1].colorStops[1] // 100% 处的颜色
                          }
                        ],
                        global: false // 缺省为 false
                      }
                    }
                  }
                }
              ]
            }
          ]
        })
      }
    },
    created() {
      this.$nextTick(() => {
        this.init()
      })
    }
  }
</script>

<style lang='scss' scoped>
  @import '@/assets/scss/HealthReport/HealthReportMetabolism.scss';
</style>
