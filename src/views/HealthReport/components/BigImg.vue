<template>
  <!-- 过渡动画 -->
  <transition name="fade">
    <div class="big-img-component-wrapper" @click="hideBigImg">
      <img :src="bigImg.src" />
    </div>
  </transition>
</template>

<script>
export default {
  props: { bigImg: { type: Object, required: true } },
  methods: {
    hideBigImg() {
      // 发送事件
      this.$emit('hideBigImg')
    }
  }
}
</script>

<style scoped>
/* 动画 */
.fade-enter-active,
.fade-leave-active {
  transition: all 0.4s ease;
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
}

/* 大图 */
.big-img-component-wrapper {
  background: rgba(0, 0, 0, 0.7);
  position: fixed;
  overflow: auto;
  z-index: 999;
  bottom: 0;
  right: 0;
  left: 0;
  top: 0;
}

.big-img-component-wrapper img {
  transform: translate(-50%, -50%);
  max-width: 100%;
  position: fixed;
  left: 50%;
  top: 50%;
}
</style>
