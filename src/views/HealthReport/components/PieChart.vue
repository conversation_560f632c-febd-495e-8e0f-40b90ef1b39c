<template>
  <div class="pie-chart-component" ref="chartBox"></div>
</template>

<script>
import echarts from 'echarts'

export default {
  data() {
    return {}
  },
  props: {
    pieChartProps: Array
  },
  computed: {
    pieEchartsData() {
      let arr = []
      this.pieChartProps.forEach(elem => {
        arr.push({
          value: elem.value,
          itemStyle: {
            color: elem.color
          }
        })
      })
      return arr
    }
  },
  methods: {
    /**
     * echarts初始化
     */
    initPieEcharts() {
      let that = this
      let myEchart = echarts.init(this.$refs.chartBox)
      myEchart.setOption({
        series: [
          {
            type: 'pie', // 图标类型
            center: ['50%', '50%'], // 饼图的中心（圆心）坐标，数组的第一项是横坐标，第二项是纵坐标
            radius: ['70%', '100%'], // 饼图的半径，数组第一项是内半径，第二项是外半径
            hoverAnimation: false, // 是否开启 放大动画效果
            label: {
              // 饼图图形上的文本标签
              normal: {
                show: false
              }
            },
            startAngle: 90, // 起始角度
            data: that.pieEchartsData
          }
        ]
      })
    }
  },
  created() {
    this.$nextTick(() => {
      this.initPieEcharts()
    })
  }
}
</script>

<style lang="scss" scoped>
.pie-chart-component {
  width: 100%;
  height: 100%;
}
</style>
