<template>
  <div class="exam-group">
    <div class="exam-group-left">
      <p v-if="examData.title && examData.title !== ''" class="exam-group-title">{{examData.title}}</p>
      <p
        v-if="examData.conclusion && examData.conclusion !== ''"
        class="exam-group-conclusion"
      >{{examData.conclusion}}</p>
      <div
        v-if="(examData.subtitle && examData.subtitle !== '') || (examData.reference && examData.reference !== '')"
        class="exam-group-subtitle"
      >
        <span
          v-if="examData.subtitle && examData.subtitle !== ''"
          class="exam-group-span1"
        >{{examData.subtitle}}</span>
        <span
          v-if="examData.reference && examData.reference !== ''"
          class="exam-group-span2"
        >参考值 {{examData.reference}}</span>
      </div>
    </div>
    <div v-if="examData.data" class="exam-group-right">
      <div class="exam-group-right-item" v-for="(item, index) in examData.data" :key="index">
        <div
          v-if="item.val && item.val != ''"
          class="group-val-box"
          :class="{'group-val-min': item.val.length > 5}"
        >
          <p v-if="item.time" class="exam-group-time">{{item.time}}</p>
          <p class="exam-group-val">{{item.val}}</p>
        </div>
        <img v-if="item.updown === 1" src="../images/low.png" alt="low" class="exam-group-arrow"/>
        <img v-if="item.updown === 2" src="../images/high.png" alt="high" class="exam-group-arrow"/>
        <span v-if="item.unit && item.unit != ''" class="exam-group-unit">{{item.unit}}</span>
        <span
          v-if="item.BMI && item.BMI != ''"
          class="exam-group-BMI"
          :class="BMIClass(item.BMI)"
        >{{BMIExt(item.BMI)}}</span>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    data() {
      return {}
    },
    props: { examData: Object },
    computed: {},
    methods: {
      // 1：'正常', 2：'偏胖', 3：'偏瘦', 4：'肥胖',
      BMIClass(bmi) {
        let name = 'exam-group-BMI'
        switch (bmi) {
          case 1:
            name += 1
            break
          case 2:
            name += 2
            break
          case 3:
            name += 3
            break
          case 4:
            name += 4
            break
          default:
            name = ''
        }
        return name
      },
      BMIExt(bmi) {
        let name
        switch (bmi) {
          case 1:
            name = '正常'
            break
          case 2:
            name = '偏胖'
            break
          case 3:
            name = '偏瘦'
            break
          case 4:
            name = '肥胖'
            break
          default:
            name = ''
        }
        return name
      }
    }
  }
</script>

<style lang="scss" scoped>
  .exam-group {
    width: 100%;
    display: flex;
    padding: 10px 15px;
    align-items: center;
    box-sizing: border-box;
    justify-content: space-between;
    border-bottom: 1px solid #e8e8e8;

    &:last-of-type {
      border-bottom: 0;
    }

    .exam-group-left {
      display: flex;
      flex-direction: column;
      justify-content: center;

      .exam-group-conclusion,
      .exam-group-title {
        height: 18px;
        color: #333;
        font-size: 16px;
        text-align: left;
        line-height: 18px;
      }

      .exam-group-conclusion {
        height: auto;
        margin-top: 5px;
        line-height: 24px;
      }

      .exam-group-subtitle {
        display: flex;
        margin-top: 5px;

        span {
          padding: 0 5px;
          font-size: 10px;
          color: #f28b05;
          line-height: 18px;
          margin-right: 2px;
          border-radius: 16px;
          box-sizing: border-box;
          border: 1px solid #ffce93;
        }

        .exam-group-span1 {
          display: flex;
          align-items: center;
          background-color: #ffecde;
        }
      }
    }

    .exam-group-right {
      width: 48%;
      display: flex;
      flex-direction: row-reverse;
      justify-content: space-between;

      .exam-group-right-item {
        display: flex;
        align-items: flex-end;
        justify-content: center;

        &:last-of-type {
          justify-content: flex-end;
        }

        .group-val-box {
          display: flex;
          color: #f28b05;
          margin-right: 3px;
          flex-direction: column;

          .exam-group-time {
            font-size: 11px;
            margin-bottom: 4px;
          }

          .exam-group-val {
            font-size: 26px;
            color: #333;
          }
        }

        .group-val-min {
          .exam-group-val {
            font-size: 16px;
          }
        }

        .exam-group-arrow {
          width: 22px;
          margin-right: 3px;
        }

        .exam-group-unit {
          font-size: 13px;
          color: #151515;
        }

        .exam-group-BMI {
          width: 50px;
          height: 20px;
          font-size: 15px;
          color: #92e464;
          margin-left: 6px;
          line-height: 20px;
          border-radius: 11px;
          border: 1px solid #92e464;
        }

        .exam-group-BMI1 {
          color: #92e464;
          border: 1px solid #92e464;
        }

        .exam-group-BMI2 {
          color: #fcc569;
          border: 1px solid #fcc569;
        }

        .exam-group-BMI3 {
          color: #73ccf6;
          border: 1px solid #73ccf6;
        }

        .exam-group-BMI4 {
          color: #ef7c69;
          border: 1px solid #ef7c69;
        }
      }
    }
  }
</style>
