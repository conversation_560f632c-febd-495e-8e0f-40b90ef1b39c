<template>
  <div class="bmi-component-wrapper">
    <div class="bmi-content">
      <div class="bmi-box bmi-box1">
        <div class="bmi-top" v-if="bmiVal > 0 && bmiVal <= 18.5" :style="bmiLeft">
          <span>{{bmiVal}}</span>
          <img src="../images/bmi-tag.png" alt="index" />
        </div>
        <div class="bmi-middle">偏瘦</div>
        <div class="bmi-dowm">18.5</div>
      </div>
      <div class="bmi-box bmi-box2">
        <div class="bmi-top" v-if="bmiVal > 18.5 && bmiVal <= 24" :style="bmiLeft">
          <span>{{bmiVal}}</span>
          <img src="../images/bmi-tag.png" alt="index" />
        </div>
        <div class="bmi-middle">正常</div>
        <div class="bmi-dowm">24</div>
      </div>
      <div class="bmi-box bmi-box3">
        <div class="bmi-top" v-if="bmiVal > 24 && bmiVal <= 28" :style="bmiLeft">
          <span>{{bmiVal}}</span>
          <img src="../images/bmi-tag.png" alt="index" />
        </div>
        <div class="bmi-middle">偏胖</div>
        <div class="bmi-dowm">28</div>
      </div>
      <div class="bmi-box bmi-box4">
        <div class="bmi-top" v-if="bmiVal > 28" :style="bmiLeft">
          <span>{{bmiVal}}</span>
          <img src="../images/bmi-tag.png" alt="index" />
        </div>
        <div class="bmi-middle">肥胖</div>
      </div>
    </div>
    <p class="bmi-tips">注：BMI为肥胖指数，用来判断身体肥胖程度，单位是kg/m²</p>
  </div>
</template>

<script>
export default {
  data() {
    return {}
  },
  props: {
    bmiVal: Number
  },
  computed: {
    bmiLeft() {
      let up
      let down
      let bmiVal = this.bmiVal
      if (bmiVal > 0 && bmiVal <= 18.5) {
        up = bmiVal
        down = 18.5
      } else if (bmiVal > 18.5 && bmiVal <= 24) {
        up = bmiVal - 18.5
        down = 24 - 18.5
      } else if (bmiVal > 24 && bmiVal <= 28) {
        up = bmiVal - 24
        down = 28 - 24
      } else if (bmiVal > 28) {
        up = bmiVal - 28
        down = 35 - 28
        if (up > down) up = down
      } else {
        up = 0
        down = 1
      }

      return { left: (up / down) * 100 + '%' }
    }
  },
  methods: {}
}
</script>

<style lang="scss" scoped>
.bmi-component-wrapper {
  background: #fff;

  .bmi-content {
    display: flex;
    margin-bottom: 10px;
    justify-content: space-between;

    .bmi-box {
      width: 25%;
      position: relative;
      box-sizing: border-box;

      .bmi-top {
        display: flex;
        position: absolute;
        align-items: center;
        flex-direction: column;
        transform: translateX(-50%);

        span {
          margin-bottom: 2px;
          font-size: 19px;
        }

        img {
          width: 10px;
        }
      }

      .bmi-middle {
        height: 24px;
        color: #fff;
        font-size: 13px;
        margin-top: 35px;
        line-height: 24px;
        text-align: center;
        margin-bottom: 5px;
        border-width: 0 1px;
        border-style: solid;
        border-color: #fff;
      }

      .bmi-dowm {
        color: #999;
        font-size: 12px;
        text-align: right;
        margin-right: -6px;
      }
    }

    .bmi-box1 {
      .bmi-top {
        span {
          color: #73ccf6;
        }
      }

      .bmi-middle {
        background-color: #73ccf6;
      }
    }

    .bmi-box2 {
      .bmi-top {
        span {
          color: #92e464;
        }
      }

      .bmi-middle {
        background-color: #92e464;
      }
    }

    .bmi-box3 {
      .bmi-top {
        span {
          color: #fcc569;
        }
      }

      .bmi-middle {
        background-color: #fcc569;
      }
    }

    .bmi-box4 {
      .bmi-top {
        span {
          color: #ef7c69;
        }
      }

      .bmi-middle {
        background-color: #ef7c69;
      }
    }
  }

  .bmi-tips {
    color: #999;
    font-size: 13px;
    text-align: left;
    line-height: 17px;
  }
}
</style>
