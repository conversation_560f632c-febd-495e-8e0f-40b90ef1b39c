<template>
  <div class="his-wrapper">
    <div :class="['his-content', {'his-content-expand': isExpand}]">
      <div
        v-for="item in list"
        :key="item.record_id"
        class="his-group"
      >
        <div class="his-info">
          <div class="his-info-box">
            <img src="../images/<EMAIL>" alt="hosp"/>
            <span>{{item.hosp_name}}</span>
          </div>
          <div class="his-info-box">
            <img src="../images/<EMAIL>" alt="clock"/>
            <span class="his-info-time">{{item.visit_at}}</span>
          </div>
        </div>
        <div class="his-go" @click="goHisRepoer(item.record_id)">
          <span>同项检查</span>
          <img src="../images/arrow_right.png" alt="go"/>
        </div>
      </div>
    </div>
    <div
      v-if="list.length > 2"
      @click="isExpand = !isExpand"
      class="his-tips"
    >
      <div
        v-if="isExpand"
        class="his-tips-box"
      >
        <span>收起</span>
        <img src="../images/up.png" alt="up"/>
      </div>
      <div
        v-else
        class="his-tips-box"
      >
        <span>还有{{list.length - 2}}项检查报告，点击展开</span>
        <img src="../images/down.png" alt="down"/>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    data() {
      return {
        isExpand: false
      }
    },
    props: {
      list: {
        type: Array,
        default: () => {
          return []
        }
      },
      path: {
        type: String,
        required: true
      }
    },
    methods: {
      goHisRepoer(id) {
        if (this.path == null) {
          return false
        }
        this.$router.push({
          path: `/guanjia/health/report/${this.path}/his`,
          query: { 'record_id': id }
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  .his-wrapper {
    width: 100%;

    .his-content {
      width: 100%;
      overflow: hidden;
      max-height: 162px;

      .his-group {
        width: 100%;
        height: 80px;
        border-bottom: 1px solid #F2F3F4;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .his-info {
          height: 70%;
          display: flex;
          flex-direction: column;
          justify-content: space-evenly;

          .his-info-box {
            display: flex;
            align-items: center;

            img {
              width: 15px;
              height: 15px;
            }

            span {
              height: 24px;
              font-size: 16px;
              color: #262626;
              margin-left: 5px;
              font-weight: 400;
              line-height: 24px;
              max-width: 85%;
              overflow: hidden;
            }

            .his-info-time {
              color: #999;
            }
          }
        }

        .his-go {
          width: 110px;
          height: 35px;
          font-size: 15px;
          color: #F3955F;
          font-weight: 500;
          line-height: 35px;
          border-radius: 18px;
          border: 1px solid #F3955F;
          display: flex;
          align-items: center;
          justify-content: center;

          img {
            width: 9px;
            height: 13px;
            margin-left: 6px;
          }
        }
      }
    }

    .his-content-expand {
      max-height: none;
    }

    .his-tips {
      width: 100%;
      padding-top: 15px;

      .his-tips-box {
        display: flex;
        align-items: center;
        justify-content: center;
        color: #999;
        font-size: 14px;
        font-weight: 400;
      }
    }
  }
</style>
