<template>
  <div class="introduce-component-wrapper" :style="ShowMoreImg">
    <div class="introduce-img" :style="ShowMoreImg">
      <img :src="src" alt />
    </div>
    <div v-if="!isShowMoreImg" class="introduce-more-img" @click="isShowMoreImg = !isShowMoreImg">
      <div class="data-img-more-down">
        <img src="../images/arrow_down.png" alt />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      isShowMoreImg: false
    }
  },
  props: {
    src: {
      required: true
    }
  },
  computed: {
    ShowMoreImg() {
      let style = {}
      if (this.isShowMoreImg) style.maxHeight = 'none'
      return style
    }
  },
  methods: {}
}
</script>

<style lang="scss" scoped>
.introduce-component-wrapper {
  overflow: hidden;
  max-height: 120px;
  position: relative;

  .introduce-img {
    max-height: 120px;

    img {
      width: 100%;
    }
  }

  .introduce-more-img {
    width: 100%;
    height: 125px;
    position: absolute;
    top: 0;
    background-image: linear-gradient(
      -180deg,
      rgba(255, 255, 255, 0) 62%,
      #ffffff 100%
    );

    .data-img-more-down {
      width: 18px;
      position: absolute;
      bottom: 14%;
      left: 50%;
      animation: iMove 0.8s infinite;

      img {
        width: 100%;
      }
    }

    @keyframes iMove {
      0% {
        transform: translate(-50%, 0);
      }

      20% {
        transform: translate(-50%, 50%);
      }

      40% {
        transform: translate(-50%, 40%);
      }

      60% {
        transform: translate(-50%, 50%);
      }

      100% {
        transform: translate(-50%, 0);
      }
    }
  }
}
</style>
