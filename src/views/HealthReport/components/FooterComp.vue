<template>
  <div class="footer-component">
    <button
      v-if="footerProps.hasNext"
      class="next-btn"
      @click="goNext"
    >查看下一项指标“{{footerProps.btnTxt}}”</button>
    <p class="remark">备注：报告内容仅供参考，不作为诊疗依据。</p>
  </div>
</template>

<script>
export default {
  data() {
    return {}
  },
  props: {
    footerProps: Object
  },
  methods: {
    /**
     * 路由跳转
     */
    goNext() {
      let query = { rebackType: 2 }
      let visitAt = this.footerProps.visit_at
      let hospId = this.footerProps.hosp_id
      if (visitAt && visitAt !== '') query.visit_at = visitAt
      if (hospId && hospId !== '') query.hosp_id = hospId
      this.$router.push({
        path: this.footerProps.path,
        query
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.footer-component {
  width: 100%;

  .next-btn {
    border: 0;
    width: 100%;
    height: 46px;
    color: #fff;
    font-size: 16px;
    line-height: 46px;
    text-align: center;
    margin-bottom: 10px;
    background-image: linear-gradient(90deg, #ffa555 0%, #f67710 100%);
  }

  .remark {
    color: #999;
    font-size: 12px;
    text-align: left;
    line-height: 17px;
  }
}
</style>
