<template>
  <div class="recommend-component-group" @click="goRecommend">
    <div class="recommend-group-left">
      <div class="recommend-group-title">{{data.title}}</div>
      <div class="recommend-group-sub">
        <div class="recommend-group-eye">
          <img src="../images/<EMAIL>" alt="eye" />
          <span>{{data.click}}</span>
        </div>
        <div class="recommend-group-time">{{data.create_at}}</div>
      </div>
    </div>
    <div class="recommend-group-right">
      <img :src="data.picture" alt="recommend" />
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {}
  },
  props: {
    data: Object
  },
  methods: {
    /**
     * 跳转推荐文章
     */
    goRecommend() {
      // this.$router.push({
      //   path: this.data.url
      // });
      window.location.href = this.data.url
    }
  }
}
</script>

<style lang="scss" scoped>
.recommend-component-group {
  display: flex;
  margin-top: 15px;
  align-items: center;
  justify-content: space-between;

  .recommend-group-left {
    display: flex;
    text-align: left;
    flex-direction: column;
    justify-content: center;

    .recommend-group-title {
      width: 200px;
      font-size: 16px;
      overflow: hidden;
      color: #262626;
      line-height: 22px;
      margin-bottom: 6px;
    }

    .recommend-group-sub {
      height: 15px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .recommend-group-eye {
        display: flex;
        align-items: center;
        justify-content: space-between;

        img {
          width: 17px;
        }

        span {
          color: #999;
          font-size: 12px;
          margin-left: 10px;
        }
      }

      .recommend-group-time {
        color: #999;
        font-size: 12px;
      }
    }
  }

  .recommend-group-right {
    width: 95px;
    height: 65px;
    margin-left: 10px;

    img {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
