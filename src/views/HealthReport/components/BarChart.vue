<template>
  <div class="bar-chart-component" ref="chartBox"></div>
</template>

<script>
import echarts from 'echarts'

export default {
  data() {
    return {}
  },
  props: {
    barChartProps: Object
  },
  computed: {
    barChartdata() {
      let arr = []
      this.barChartProps.series.forEach(elem => {
        arr.push({
          type: 'bar',
          data: elem,
          barWidth: '60%' // 柱图宽度
        })
      })

      return {
        xAxisData: this.barChartProps.xAxisData,
        series: arr
      }
    }
  },
  methods: {
    /**
     * echarts初始化
     */
    initBarEchart() {
      let that = this
      let myEchart = echarts.init(this.$refs.chartBox)
      myEchart.setOption({
        xAxis: [
          {
            type: 'category',
            // boundaryGap: false,
            axisLabel: {
              interval: 0,
              rotate: 45
            },
            axisTick: {
              alignWithLabel: true
            },
            data: that.barChartdata.xAxisData
          }
        ],
        yAxis: [
          {
            type: 'value'
          }
        ],
        grid: {
          top: that.barChartProps.top || '5%', // 组件离容器上侧的距离
          left: that.barChartProps.left || '2%', // 组件离容器左侧的距离
          width: that.barChartProps.width || '95%', // 组件的宽度
          height: that.barChartProps.height || '95%', // 组件的高度
          containLabel: true // grid 区域包含坐标轴的刻度标签
        },
        series: that.barChartdata.series
      })
    }
  },
  created() {
    this.$nextTick(() => {
      this.initBarEchart()
    })
  }
}
</script>

<style lang="scss" scoped>
.bar-chart-component {
  width: 100%;
  height: 100%;
}
</style>
