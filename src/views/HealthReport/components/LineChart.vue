<template>
  <div class="line-chart-component" ref="chartBox"></div>
</template>

<script>
import echarts from 'echarts'

export default {
  data() {
    return {}
  },
  props: {
    lineChartProps: Object
  },
  computed: {
    echartOption() {
      let seriesData = []
      this.lineChartProps.value.forEach(elem => {
        seriesData.push({
          name: elem.name || '',
          type: 'line',
          data: elem.val,
          symbol: elem.symbol || 'emptyCircle',
          symbolSize: elem.symbolSize || 7,
          showAllSymbol: true,
          color: elem.color || '#F67710',
          label: { show: true, color: '#000', fontSize: 12 }
        })
      })
      let xAxisData = this.lineChartProps.visit_at
      return {
        seriesData,
        xAxisData
      }
    }
  },
  methods: {
    /**
     * echarts初始化
     */
    initEchart() {
      let that = this
      let myEchart = echarts.init(that.$refs.chartBox)
      myEchart.setOption({
        tooltip: {
          show: true,
          trigger: 'axis'
        },
        backgroundColor: '#fff',
        legend: {
          top: 'top',
          left: 'left',
          data: that.lineChartProps.legendData || []
        },
        xAxis: [
          {
            type: 'category',
            // boundaryGap: false,
            axisLabel: {
              interval: 0,
              rotate: that.lineChartProps.xRotate || 0
            },
            axisTick: {
              alignWithLabel: true
            },
            data: that.echartOption.xAxisData
          }
        ],
        yAxis: [
          {
            type: 'value'
          }
        ],
        grid: {
          top: that.lineChartProps.top || '18%', // 组件离容器上侧的距离
          left: that.lineChartProps.left || '2%', // 组件离容器左侧的距离
          width: that.lineChartProps.width || '95%', // 组件的宽度
          height: that.lineChartProps.height || '82%', // 组件的高度
          containLabel: true // grid 区域包含坐标轴的刻度标签
        },
        series: that.echartOption.seriesData
      })
    }
  },
  created() {
    this.$nextTick(() => {
      this.initEchart()
    })
  }
}
</script>

<style lang="scss" scoped>
.line-chart-component {
  width: 100%;
  height: 100%;
}
</style>
