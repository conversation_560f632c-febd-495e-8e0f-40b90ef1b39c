.wrapper {
  width: 100%;
  position: relative;;
  .header {
    font-size: 24px;
    color: #ffb44c;
    line-height: 32px;
    font-weight: bold;
    text-align: center;
    padding: 26px 10px 12px;
    background-color: rgba(255,255,255,0.9);
  }

  .content {
    padding: 20px;

    .group {
      margin-bottom: 40px;
      position: relative;
      .title {
        font-size: 16px;
        text-align: left;
        font-weight: bold;
        line-height: 24px;
        padding: 0 0 8px 0;
        position: relative;
        word-wrap: break-word;

        .title-star {
          position: absolute;
          top: 0;
          left: -12px;
          color: red;
          margin: 2px 0 0 2px;
        }

        .title-tips {
          color: #999;
          font-weight: normal;
        }
      }

      .ok {

        .ok-txt {
          height: 35px;
          display: flex;
          padding: 0 10px;
          align-items: center;
          justify-content: space-between;
          border-bottom: 1px solid #dbdbdb;

          span {
            font-size: 14px;
            font-weight: normal;
          }
        }

        .ok-star {
          display: flex;
          padding: 0 30px;
          align-items: center;
          justify-content: space-between;

          .star {
            width: 22px;
            height: 22px;
            display: flex;
            margin: 10px 0;
            position: relative;

            img {
              width: 100%;
              height: 100%;
            }

            .tips {
              padding: 4px;
              color: #333;
              font-size: 12px;
              width: max-content;
              position: absolute;
              text-align: center;
              border-radius: 4px;
              border: 1px solid #3296fa;
              background-color: rgb(255,255,255);
              transform: translateX(-50%);
              top: -34px;
              left: 50%;

              &::after,
              &::before {
                content: '';
                width: 0;
                height: 0;
                position: absolute;
                border-top: 9px solid #3296fa;
                border-left: 8px solid transparent;
                border-right: 8px solid transparent;
                transform: translateX(-50%);
                bottom: -8px;
                left: 50%;
              }

              &::after {
                border-top-color: #fff;
                bottom: -7px;
              }
            }
          }
        }
      }

      .agree,
      .help {
        border: 1px solid #e0e0e0;
        border-bottom: none;
        border-radius: 4px;

        .van-radio,
        .van-checkbox {
          height: 50px;
          padding: 0 12px;
          border-bottom: 1px solid #e0e0e0;

          .agree-val,
          .help-val {
            color: #333;
            font-size: 16px;
            line-height: 28px;
          }
        }
      }

      .van-cell {
        border-radius: 4px;
        border: 1px solid #e0e0e0;
      }

      .other{
        width: 60%;
        position: absolute;
        right: 10%;
        bottom: 0.5%;
      }
    }
  }

  .footer {
    padding: 0 20px;
    margin-bottom: 40px;

    .submit {
      font-size: 24px;
      color: #fff;
      line-height: 42px;
      text-align: center;
      border-radius: 4px;
      background: #F3955F;
      border: none;
      width: 100%;
    }
  }

  .model{
    position: fixed;
    height: 100%;
    width: 100%;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    margin: auto;
    background: rgba(0,0,0,0.4);
    img{
      width: 251px;
      height: 320px;
      position: fixed;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      margin: auto;
    }
  }
}
