<template>
    <div class="wrapper">
        <h1 class="header">满意度调查</h1>
        <div class="content">
            <div class="group">
                <h2 class="title">
                    <i class="title-star">*</i>
                    1. 您对控糖营满意吗？
                </h2>
                <radio-group v-model="Satisfied" class="agree">
                    <radio
                            v-for="(item, index) in SatisfiedOptions"
                            checked-color="#ffb44c"
                            :name="item.name"
                            :key="index"
                    >
                        <span class="agree-val">{{ item.value }}</span>
                    </radio>
                </radio-group>
            </div>
            <div class="group">
                <h2 class="title">
                    <i class="title-star">*</i>
                    2. 您是否愿意将控糖营推荐给朋友？
                </h2>
                <radio-group v-model="enjoyed" class="agree">
                    <radio
                            v-for="(item, index) in enjoyedOptions"
                            checked-color="#ffb44c"
                            :name="item.name"
                            :key="index"
                    >
                        <span class="agree-val">{{ item.value }}</span>
                    </radio>
                </radio-group>
            </div>
            <div class="group">
                <h2 class="title">
                    <i class="title-star">*</i>
                    3. 你觉得哪部分对您帮助最大？
                    <span class="title-tips">【多选题】</span>
                </h2>
                <checkbox-group v-model="help" class="help">
                    <checkbox
                            v-for="(item, index) in helpOptions"
                            checked-color="#ffb44c"
                            :name="item.name"
                            shape="square"
                            :key="index"
                    >
                        <span class="help-val">{{ item.value }}</span>
                    </checkbox>
                </checkbox-group>
                <field v-if="help == 9" v-model="other_msg" type="input" class="other"/>
            </div>
            <div class="group">
                <h2 class="title">
                    <i class="title-star">*</i>
                    4. 在您心中，21天控糖营值多少钱？
                </h2>
                <radio-group v-model="money" class="agree">
                    <radio
                            v-for="(item, index) in moneyOptions"
                            checked-color="#ffb44c"
                            :name="item.name"
                            :key="index"
                    >
                        <span class="agree-val">{{ item.value }}</span>
                    </radio>
                </radio-group>
            </div>
            <div class="group">
                <h2 class="title">
                    5. 您对我们控糖营的建议
                </h2>
                <field v-model="advice" type="textarea" autosize rows="3"/>
            </div>
        </div>
        <div class="footer">
            <button class="submit" @click="submit" :disabled="isDisable">提交</button>
        </div>

        <div class="model" v-if="show==true">
            <img
                    src="https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/wechat/sugarController/%E9%97%AE%E5%8D%B7%E6%8F%90%E4%BA%A4%E6%88%90%E5%8A%9F.png"
                    alt="">
        </div>
    </div>
</template>

<script>
    import 'vant/lib/field/style'
    import 'vant/lib/checkbox/style'
    import 'vant/lib/checkbox-group/style'
    import 'vant/lib/radio/style'
    import 'vant/lib/radio-group/style'
    import Field from 'vant/lib/field'
    import Checkbox from 'vant/lib/checkbox'
    import CheckboxGroup from 'vant/lib/checkbox-group'
    import Radio from 'vant/lib/radio'
    import RadioGroup from 'vant/lib/radio-group'

    import { getReportSatisfied, getUserInfo } from '@/api/survey'

    export default {
        components: {
            'checkbox-group': CheckboxGroup,
            'checkbox': Checkbox,
            'radio-group': RadioGroup,
            'radio': Radio,
            'field': Field
        },
        data() {
            return {
                Satisfied: '',
                SatisfiedOptions: [
                    {
                        value: '很满意',
                        name: 1
                    },
                    {
                        value: '满意',
                        name: 2
                    },
                    {
                        value: '一般',
                        name: 3
                    },
                    {
                        value: '不满意',
                        name: 4
                    },
                    {
                        value: '非常不满意',
                        name: 5
                    }
                ],
                enjoyed: '',
                enjoyedOptions: [
                    {
                        value: '非常愿意',
                        name: 1
                    },
                    {
                        value: '愿意',
                        name: 2
                    },
                    {
                        value: '一般般',
                        name: 3
                    },
                    {
                        value: '不愿意',
                        name: 4
                    },
                    {
                        value: '非常不愿意',
                        name: 5
                    }
                ],
                help: [],
                helpOptions: [
                    {
                        value: '通过食谱了解到如何饮食',
                        name: 1
                    },
                    {
                        value: '通过每日运动来更好的锻炼',
                        name: 2
                    },
                    {
                        value: '通过每日文章让自己掌握控糖知识',
                        name: 3
                    },
                    {
                        value: '可以随时记录血糖和血压',
                        name: 4
                    },
                    {
                        value: '在微信群和其他患者交流',
                        name: 5
                    },
                    {
                        value: '专家在直播间讲课与解答',
                        name: 6
                    },
                    {
                        value: '护士在微信群里面解答',
                        name: 7
                    },
                    {
                        value: '没什么帮助',
                        name: 8
                    },
                    {
                        value: '其他',
                        name: 9
                    }
                ],
                other: '',
                money: '',
                moneyOptions: [
                    {
                        value: '免费',
                        name: 1
                    },
                    {
                        value: '0-100元',
                        name: 2
                    },
                    {
                        value: '101-500元',
                        name: 3
                    },
                    {
                        value: '501-1000元',
                        name: 4
                    },
                    {
                        value: '1001-2000元',
                        name: 5
                    },
                    {
                        value: '2000元以上',
                        name: 6
                    }
                ],
                advice: '',
                show: false,
                isDisable: false,
                token: ''
            }
        },
        created() {
            this.getUser()
        },
        methods: {
            /**
             * 提交
             */
            submit() {
                if (this.Satisfied === '') {
                    this.$toast('请回答第一题')
                    return false
                }
                if (this.enjoyed === '') {
                    this.$toast('请回答第二题')
                    return false
                }
                if (this.help.length === 0) {
                    this.$toast('请回答第三题')
                    return false
                }

                if (this.money === '') {
                    this.$toast('请回答第四题')
                    return false
                }
                if (this.advice === '') {
                    this.$toast('请回答第五题')
                    return false
                }
                console.log(this.Satisfied, this.enjoyed, this.help, this.money, this.advice, '这是五题的答案')
                this.isDisable = true
                setTimeout(() => {
                    this.isDisable = false
                }, 1000)
                this.token = this.$route.query.accessToken
                let that = this
                let otherMsg = ''
                if (this.help.indexOf('9') !== -1) {
                    otherMsg = this.other_msg
                }

                getReportSatisfied({
                    'type': 1,
                    'score': this.Satisfied,
                    'is_to_friend': this.enjoyed,
                    'pay_money': this.money,
                    'help': this.help,
                    'other_msg': otherMsg,
                    'advice': this.advice,
                    'token': this.token
                }).then(function (res) {
                    if (res.status === 0) {
                        // this.$toast('提交成功')
                        that.show = true
                    } else {
                        this.$toast(res.msg)
                    }
                })
            },
            getUser() {
                let that = this
                getUserInfo({ 'token': this.token }).then(function (res) {
                    if (res.status === 0) {
                        that.days = res.data.camp_type
                    } else {
                        this.$toast(res.msg)
                    }
                })
            }
        }
    }
</script>

<style lang="scss" scoped>
    @import "newSurvey.scss";
</style>
