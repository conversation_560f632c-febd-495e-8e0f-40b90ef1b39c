<template>
    <div class="content">
       <div class="title">
         测试页面
         <span @click="jump"> 跳转</span>
         <a href="https://segmentfault.com/a/1190000016717727"> a跳转 </a>
        </div>
        <iframe width="100%" height="500px" src="https://api.kuaidi100.com/tools/map/innerzt" frameborder="0"></iframe>
        456789789
    </div>
</template>

<script>
    export default {
        data() {
          return {}
        },
        created() {
          this.init()
        },
        methods: {
          init() {
            console.log(window.wx, 1234)

            // window.wx.miniProgram.navigateBack({delta: 1})
            window.wx.miniProgram.postMessage({ data: '获取成功' })
          },
          jump() {
            console.log('111111')
            window.wx.onMenuShareAppMessage()
             // window.wx.miniProgram.redirectTo({ url: '/pages/home/<USER>/home/<USER>' })
              // window.wx.miniProgram.postMessage({ data: 'foo' })
              // window.wx.miniProgram.postMessage({ data: {foo: 'bar'} })
              // window.wx.miniProgram.getEnv(function(res) { console.log(res.miniprogram) })
          }
        }
    }
</script>

<style lang="scss" scoped>

</style>
