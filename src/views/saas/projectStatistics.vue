<!--
 * @Descripttion: projectStatistics
 * @version: 
 * @Author: chenyang
 * @Date: 2024-01-08 09:25:55
 * @LastEditors: chenyang
 * @LastEditTime: 2024-01-08 09:25:55
-->
<template>
    <div class="wrapper">
        <div class="content">
            <!-- <van-nav-bar title="" left-text="" right-text="" left-arrow @click-left="onClickLeft" /> -->
            <div class="selectDays">
                <div style="color: rgb(75,134,247);" @click="handleLastDay"
                    :class="statisticsdate == agoDate ? 'nextDay-disable' : ''"><van-icon name="arrow-left" />上一天</div>
                <div style="font-size: 20px;">{{ statisticsdate }}</div>
                <div style="color: rgb(75,134,247);" @click="handleNextDay"
                    :class="statisticsdate == nowDate ? 'nextDay-disable' : ''">下一天<van-icon name="arrow" /></div>
            </div>
            <van-list class="vanListBox">
                <div class="listcase" v-for="item in statisticslist" :key="item" :title="item">
                    <van-icon name="info-o" @click="toastClick(item.label)" size="16px" class="tipsIcon"
                        v-if="item.label == '已成功记录动作患者数' || item.label == '初诊报告打印数' || item.label == '血脂数据填写数' || item.label == 'UACR数据填写数'" />
                    <h3 style="line-height: 34px;font-size: 24px;margin-bottom: 10px;font-style: normal;font-weight: 500;">
                        {{ item.value ? item.value : '--' }}
                    </h3>
                    <p style="font-size: 12px;">{{ item.label }}</p>
                </div>
            </van-list>
        </div>
        <div class="tipsBox" v-show="showTips">
            <p>{{ tooltip }}</p>
            <van-button class="cancelBtn" icon="cross" color="rgba(0, 0, 0, 0)" size="18"
                @click="toastCancel()"></van-button>
        </div>
    </div>
</template>

<script>
import { getProjectStatisticsListApi } from '@/api/saas.js'
import moment from 'moment'
export default {
    data() {
        return {
            statisticslist: [],
            statisticsdate: moment().format('YYYY/MM/DD'),
            loading: false,
            finished: false,
            tooltip: null,
            showTips: false,
            nowDate: moment().format('YYYY/MM/DD'),
            agoDate: moment(new Date().setDate(new Date().getDate() - 30)).format('YYYY/MM/DD')
        }
    },

    created() {
        this.userAgreementFun()
    },
    methods: {
        async userAgreementFun() {
            const res = await getProjectStatisticsListApi({
                date: moment(this.statisticsdate).format('YYYY-MM-DD'),
            })
            this.statisticslist = res.data
            this.showTips = false
            this.tooltip = null
        },
        handleLastDay() {
            this.statisticsdate = moment(this.statisticsdate).subtract(1, 'day').format('YYYY/MM/DD')
            this.userAgreementFun()
        },
        handleNextDay() {
            this.statisticsdate = moment(this.statisticsdate).add(1, 'day').format('YYYY/MM/DD')
            this.userAgreementFun()
        },
        toastClick(msg) {
            let tips = null
            if (msg == '已成功记录动作患者数') {
                tips = '当患者完成入组项目+添加企微好友+完成知情同意签署后，才会记录动作成功'
            } else if (msg == '初诊报告打印数') {
                tips = '加入项目当天，打印初诊报告的患者人数'
            } else if (msg == '血脂数据填写数') {
                tips = '血脂数据填写-使用商米或电脑端填写血脂数据的患者人数，相同患者同一天内仅计一次'
            } else if (msg == 'UACR数据填写数') {
                tips = 'UACR数据填写-使用商米或电脑端填写UACR数据的患者人数，相同患者同一天内仅计一次'
            } else {
                tips = null
            }
            this.tooltip = tips
            this.showTips = true
        },
        toastCancel() {
            this.showTips = false
            this.tooltip = null
        }

        // onLoad() {
        //     // 异步更新数据
        //     // setTimeout 仅做示例，真实场景中一般为 ajax 请求
        //     setTimeout(() => {
        //         for (let i = 0; i < 10; i++) {
        //             this.list.push(this.list.length + 1);
        //         }

        //         // 加载状态结束
        //         this.loading = false;

        //         // 数据全部加载完成
        //         if (this.list.length >= 40) {
        //             this.finished = true;
        //         }
        //     }, 1000);
        // },
    }
}
</script>

<style lang="scss" scoped>
.wrapper {
    .selectDays {
        display: flex;
        justify-content: space-between;
        padding: 13px 14px 12px;
    }

    .vanListBox {
        display: flex;
        flex-wrap: wrap;

        .listcase {
            display: flex;
            flex-direction: column;
            // padding: 20px 0 30px;
            justify-content: center;
            align-items: center;
            width: 161px;
            height: 116px;
            margin: 0 11px 15px;
            flex-shrink: 0;
            position: relative;

            .tipsIcon {
                position: absolute;
                right: 19px;
                top: 14px;
            }
        }
    }

    .tipsBox {
        display: flex;
        width: 351px;
        height: 35px;
        padding: 5px 12px;
        align-items: center;
        gap: 12px;
        // align-items: center;
        border-radius: 8px;
        background: rgba(0, 0, 0, 0.75);
        box-shadow: 0px 2px 5px 0px rgba(51, 51, 51, 0.20);
        position: fixed;
        left: 0;
        bottom: 4px;

        p {
            color: #FFF;
            font-family: PingFang SC;
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
            padding-right: 12px;
            text-align: left;
        }

        .cancelBtn {
            position: absolute;
            right: 12px;
            bottom: 2px;
        }
    }

    .nextDay-disable {
        pointer-events: none !important;
        color: #ccc !important;
    }

}
</style>
