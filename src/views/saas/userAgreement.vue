<!--
 * @Descripttion: 
 * @version: 
 * @Author: l<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-09 16:25:55
 * @LastEditors: liuqiannan
 * @LastEditTime: 2021-06-15 13:26:32
-->
<template>
  <div class="wrapper">
      <div class="content" v-html="content">
      </div>
  </div>
</template>

<script>
import { userAgreementApi } from '@/api/saas.js'
export default {
    data() {
        return {
            content: ''
        }
    },
    created () {
        this.userAgreementFun()
    },
    methods: {
        async userAgreementFun () {
            const res = await userAgreementApi({
                project_id: this.$route.query.project_id,
                department_id: this.$route.query.department_id
            }) 
            this.content = res.data
        }
    }
}
</script>

<style lang="scss" scoped>
.wrapper {
    padding: 20px 10px;
    height: 100vh;
    overflow: auto;
    background: white;
    .title {
        font-size: 20px;
        margin-bottom: 40px;
    }
    .content {
        text-align: justify;
        font-size: 16px;
    }
}
</style>