<template>
  <div class="main">
    <img :src="imgUrl" alt="">
  </div>
</template>

<script>
  import { getImg } from '@/api/imgPage'

    export default {
        data () {
          return {
            token: '',
            code: '',
            imgUrl: ''
          }
        },
        created () {
          let that = this
          this.code = this.$route.query.code
          getImg({
            'union_code': this.code
          }).then(function (res) {
            if (res.status === 0) {
              that.imgUrl = res.data.code_url

              // that.imgUrl = 'https://gss0.bdstatic.com/7051cy89RcgCncy6lo7D0j9wexYrbOWh7c50/CZZ/czzzm540-280.png'
              // console.log(1, that.imgUrl)
            } else {
              this.$toast(res.msg)
            }
          })
        },
      method: {

      }
    }
</script>

<style lang="scss" scoped>
.main{
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  img{
    width: 100%;
    height: 100%;
  }
}
</style>
