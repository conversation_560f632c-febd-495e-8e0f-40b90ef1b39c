<template>
    <div class="main">
      <div v-if="type == 1">
        <div class="tip" v-if="is_safety">
            <span> <van-icon class="passed" name="checked"/>
              此二维码已通过安全验证，可以放心扫码</span>
          <van-icon class="btnClose" @click="closePopup" name="cross"/>
        </div>
        <van-image :src="imgUrl" alt=""/>
      </div>
      <!--这是新增的-->
      <div v-if="type == 2">
        <div class="tip" v-if="is_safety">
            <span> <van-icon class="passed" name="checked"/>
              此二维码已通过安全验证，可以放心扫码</span>
          <van-icon class="btnClose" @click="closePopup" name="cross"/>
        </div>
        <div class="headBox">
          <img src="./headerImg.png">
          <div>{{name}}</div>
        </div>
        <img :src="imgUrl" alt="" class="img">
      </div>
    </div>
</template>
<script>
    import { getActiveImg } from '@/api/imgPage'

    export default {
        data() {
            return {
                uuid: '',
                is_safety: 0,
                imgUrl: '',
                type: '',
                name: ''
            }
        },
        created() {
            let that = this
            var ua = window.navigator.userAgent.toLowerCase()
            if (ua.match(/MicroMessenger/i) == 'micromessenger') {
                this.uuid = this.$route.query.uuid
                getActiveImg({
                    'uuid': this.uuid
                }).then(function (res) {
                    if (res.status === 0) {
                        that.imgUrl = res.data.wechat_qrcode_url
                        that.is_safety = res.data.is_safety
                        document.title = res.data.name
                        that.name = res.data.name
                        that.type = res.data.wechat_qrcode_build_action
                    } else {
                        this.$toast(res.msg)
                    }
                })
            } else {
                this.$toast('请微信中打开此扫码!')
                return false
            }
        },
        methods: {
            closePopup() {
                this.is_safety = 0
            }
        }
    }
</script>

<style lang="scss" scoped>
    .main {
        width: 100%;
        height: 100%;
        img {
            padding-top: 5px;
            width: 100%;
            height: 100%;
        }
        .tip {
            font-size: 16px;
            line-height: 24px;
            color: #3EC77C;
            background-color: #3F3F3F;
            padding-left: 5px;
            text-align: left;
            .passed {
                line-height: 24px;
                margin-right: 5px;
            }
            .btnClose {
                line-height: 24px;
                margin-right: 10px;
                color: #fff;
                float: right;
            }
        }
      .headBox{
        display: flex;
        align-items: center;
        padding: 20px 10px;
        img{
          width: 75px;
          height: 75px;
          margin-right: 10px;
        }
        div{
          font-size: 20px;
          color: #333;
          width: 270px;
          text-align: left;
          line-height: 24px;
        }
      }
      .img{
        width: 350px;
        height: 350px;
      }

    }
</style>
