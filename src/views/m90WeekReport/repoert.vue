<template>
  <div class="main" ref="main">
    <div class="top" v-if="flagOne">
      <div class="info">
        <div class="name">
          <img :src="avatar == '' ? 'https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/inherts%402x.png' : avatar" /><span>{{nick_name}}</span>
        </div>
        <div class="reward">
          <div class="date">{{dateChange(lastStarted_at)}}-{{dateChange(lastExpired_at)}}</div>
          <img v-if="reward == 1" src="https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/oneStar.png">
          <img v-if="reward == 2" src="https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/twoStar%402x.png">
          <img v-if="reward == 3" src="https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/threeStar%402x.png">
          <div class="rewardText">
            <span v-if="reward == 1">深厚潜力暂未开启</span><span v-if="reward == 1">而自律已离我不远</span>
            <span v-if="reward == 2">控糖毅力已被发掘</span><span v-if="reward == 2">距离完美只差一步</span>
            <span v-if="reward == 3">任督二脉完全被打开</span><span v-if="reward == 3">自律的光环照耀全场</span>
          </div>
        </div>
      </div>
      <div class="target">
        <p>达成了<span>{{details.complete_motion_num}}</span>天运动目标</p>
        <p>测量了<span>{{details.complete_bg_num}}</span>次血糖</p>
        <p>测量了<span>{{details.complete_bp_num}}</span>次血压</p>
        <p>测量了<span>{{details.complete_weight_num}}</span>次体重</p>
        <p>查看了<span>{{details.complete_recipe_view_num}}</span>天食谱</p>
        <p>阅读了<span>{{details.complete_article_num}}</span>篇文章</p>
        <p>答对了<span>{{details.complete_question_num}}</span>道文章答题</p>
      </div>
    </div>
    <div v-if="flagTwo">
      <div class="lastweek"><span></span>以下是根据您上周的表现调整的方案<span></span></div>
      <div class="thisWeek">本周的方案<span>{{dateChange(started_at)}}-{{dateChange(expired_at)}}</span></div>
      <div class="details" v-for="(item, index) in project" :key=index>
        <p class="p1">{{item.plan_name}}</p>
        <p class="p2">{{item.description}}</p>
      </div>
      <!-- <div class="details">
        <p class="p1">血糖测量</p>
        <p class="p2"></p>
      </div>
      <div class="details">
        <p class="p1">血压测量</p>
        <p class="p2"></p>
      </div> -->
      <div class="goBackHome" @click="goBackHome">回到首页</div>
    </div>
  </div>
</template>
<script>
  import { Toast } from 'vant'
  import wx from 'weixin-js-sdk'
  import { getReportDetails, getReportProject } from '@/api/m90Report'

  export default {
    data() {
      return {
        id: '',
        source_type: '5',
        flagOne: false,
        flagTwo: false,
        reward: 1, // 1星 <40%  2星 40-80%  3星  >80%
        lastStarted_at: '',
        lastExpired_at: '',
        started_at: '',
        expired_at: '',
        details: {},
        nick_name: '',
        avatar: '',
        project: []
      }
    },
    components: {},
    created() {
      this.id = this.$route.query.id
      this.init()
    },
    methods: {
      // 初始化
      init() {
        let that = this
        getReportDetails({ id: this.id }).then(res => {
          console.log('详情', res)
          if (res.status == 0) {
            that.details = res.data.detail
            that.nick_name = res.data.nick_name
            that.avatar = res.data.avatar
            that.lastStarted_at = res.data.started_at
            that.lastExpired_at = res.data.expired_at
            that.reward = this.changeReward(res.data.detail.finishing_rate)
          } else {
            this.$toast(res.msg)
          }
          that.flagOne = true
        }).then(() => {
          this.$refs.main.style.minHeight = window.innerHeight + 'px'
        })
        getReportProject({started_at: this.started_at, expired_at: this.expired_at, id: this.id}).then(res => {
          console.log('方案', res)
          if (res.status == 0) {
            that.project = res.data.detail
            that.started_at = res.data.started_at
            that.expired_at = res.data.expired_at
          } else {
            that.$toast(res.msg)
          }
          that.flagTwo = true
        })

      },
      dateChange(num) {
        var date = num.replace(/-/g, '')
        var year = date.slice(0,4)
        var month = date.slice(4,6)
        var day = date.slice(6,8)
        return Number(month) + '月' + Number(day) + '日'
      },
      changeReward(num) {
        if(num<40) {
          return 1
        } else if(num >= 40 && num < 80) {
          return 2
        } else if(num >= 80){
          return 3
        }
      },
      goBackHome() {
        wx.miniProgram.redirectTo({
          url: '/pages/home/<USER>/newHome/newHome'
        })
      }
    }
  }
</script>

<style lang='scss' scoped>
  @import '@/assets/scss/m90WeekReport/report.scss'
</style>
