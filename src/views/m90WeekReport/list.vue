<template>
  <div class="wrapper" ref="main">
    <div class="content" v-if="list.length > 0">
      <div class="group" v-for="item in list" :key="item.id" @click="goReport(item.started_at, item.expired_at, item.id)">
        <div class="left">
          <p>{{dateChange(item.started_at)}}-{{dateChange(item.expired_at)}}</p>
          <span>健康管理周报</span>
        </div>
        <div class="right">
          <span v-if="item.is_read == 0 ">未读</span>
          <img src="https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/rightReport%402x.png" />
        </div>
      </div>
    </div>
    <div class="noData" v-if="list.length == 0">
      <img src="https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/noDatas%402x.png" />
      <p>暂无周报</p>
      <span>控糖7天后会生成周报</span>
    </div>
  </div>
</template>

<script>
import { getReportList, getReportDetails, getReportProject } from '@/api/m90Report'
import {getClientHeight, getScrollTop, getScrollHeight} from '@/utils/utils.js'

export default {
  data() {
    return {
      pageNo: 1,
      pageSize: 10,
      isClientBottom: false,
      isLastReport: false,
      isLoading: false,
      sourceType: '5',
      list: []
    }
  },
  watch: {
    isClientBottom(newVal, oldVal) {
      // 滑倒底部并且还有更多报告时，以及不在加载中（防止重复加载）
      if (newVal && !this.isLastReport && !this.isLoading) {
        // 获取医生动态
        this.init(++this.pageNo)
      }
    }
  },
  created() {
    this.init(1)
  },
  mounted() {
    // 判断是否滑到底部
    this.checkScroll()
    // 获取浏览器可视区域高度
    this.clientHeight = `${document.documentElement.clientHeight}`
    this.$refs.main.style.minHeight = this.clientHeight + 'px'
  },
  methods: {
    /**
     * 初始化
     */
    init(pageNo) {
      // 获取报告列表数据
      let that = this
      that.isLoading = true
      getReportList({page: pageNo, page_size: this.pageSize}).then(res => {
        if (res.status === 0) {
          that.pageNo = res.data.current_page
          that.list = that.list.concat(res.data.list)
          if (res.data.total_page === res.data.current_page) that.isLastReport = true
        } else {
          this.$toast(res.msg)
        }
        this.isLoading = false
      })
    },
    /**
     * 跳转健康报告首页
     * @param {String} date 报告日期
     * @param {String} hospId 医院ID
     */
    goReport(started_at, expired_at, id) {
      let query = {started_at: started_at, expired_at: expired_at, id: id}
      this.$router.push({
        path: '/m90/week/report',
        query
      })
    },
    dateChange(num) {
      var date = num.replace(/-/g, '')
      var year = date.slice(0,4)
      var month = date.slice(4,6)
      var day = date.slice(6,8)
      return Number(month) + '月' + Number(day) + '日'
    },
    /**
     * 判断是否滑到底部
     */
    checkScroll() {
      let scrollTop = getScrollTop()
      window.onscroll = () => {
        let newSrollTop = getScrollTop() // 当前滚动高度
        let scrollHeight = getScrollHeight() // 文档完整的高度
        let clientHeight = getClientHeight() // 可视范围的高度
        let distance = 2 // 底部间距

        // 如果当前滚动高度大于之前滚动高度 -> 下滑
        if (newSrollTop > scrollTop) {
          // 如果到底部 this.isClientBottom = true；如果不在底部 this.isClientBottom = false
          if (newSrollTop + clientHeight + distance < scrollHeight) {
            // console.log('没到底')
            this.isClientBottom = false
          } else {
            // console.log('到底了')
            this.isClientBottom = true
          }
        } else {
          this.isClientBottom = false
        }
        // 每次滚动后，把当前滚动高度赋值给之前滚动高度
        scrollTop = newSrollTop
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "@/assets/scss/m90WeekReport/list.scss";
</style>
