<template>
  <div class="main" ref="main" v-if="flag">
    <div class="top">
      <p></p>
      <div class="result">
        <img v-if="info.complete_task_scale >= 70 && info.complete_task_num != 0" src="./../../assets/images/<EMAIL>" />
        <img v-if="info.complete_task_scale < 70 && info.complete_task_num != 0" src="./../../assets/images/<EMAIL>" />
        <img v-if="info.complete_task_num == 0" src="./../../assets/images/<EMAIL>" />
        <div class="times1" v-if="info.complete_task_num != 0">{{info.start_to_end}}</div>
        <div class="times2" v-if="info.complete_task_num == 0">{{info.start_to_end}}</div>
      </div>
      <div class="ranking">排名 {{info.rank == 0 ? '未上榜' : 'No.' + info.rank}}</div>
    </div>
    <div class="stage">
      <div class="target">
        <img src="./../../assets/images/<EMAIL>" />
        <div class="right">
          <p>{{info.stage == 0 ? info.stage_name : info.stage_name + ' | ' + info.at_week_name }}</p>
          <span v-if="info.complete_task_scale >= 70 && info.complete_task_num != 0">您共完成了<i>{{info.complete_task_num}}</i>个健康任务完成率达到<i>{{info.complete_task_scale}}</i>%，表现超过<i>{{info.more_than_scale}}</i>%的用户</span>
          <span v-if="info.complete_task_scale < 70 && info.complete_task_num != 0">您共完成了<i>{{info.complete_task_num}}</i>个健康任务完成率比较低哦，健康表现只领先了<i>{{info.more_than_scale}}</i>%的用户</span>
          <span v-if="info.complete_task_num == 0">您似乎忘了做本周的健康任务，您的健康大家都在关心，行动起来吧。</span>
        </div>
      </div>
      <div class="managePlan">
        <h2 v-if="info.complete_task_num != 0 && info.complete_task_scale >= 70" class="managePlan-title managePlan-title-h">根据您的近况，任务方案有变化</h2>
        <h2 v-else class="managePlan-title managePlan-title-n">继续保持当前健康任务方案</h2>
        <div v-if="info.complete_task_num != 0 && info.complete_task_scale >= 70" class="managePlan-content">
          <div class="details clearfix" v-if="info.next_motion_target_trend != 0">
            <p>运动目标</p>
            <span v-if="info.next_motion_target_trend == 1">下降了<i>{{info.next_motion_target}}</i><img src="./../../assets/images/<EMAIL>" />步</span>
            <span v-if="info.next_motion_target_trend == 2">提升到<i>{{info.next_motion_target}}</i><img src="./../../assets/images/<EMAIL>" />步</span>
          </div>
          <div class="details clearfix" v-if="info.next_bg_plan != ''">
            <p>血糖测量方案</p>
            <span>{{info.next_bg_plan}}</span>
          </div>
          <div class="details clearfix" v-if="info.next_bp_plan != ''">
            <p>血压测量方案</p>
            <span>{{info.next_bp_plan}}</span>
          </div>
        </div>
      </div>
    </div>
    <img @click="goBackHome" v-if="info.complete_task_num == 0" class="imgNoData" src="./../../assets/images/healthBtn.png" />
    <div class="title" v-if="weekReport">家庭周报</div>
    <div v-if="weekReport" :class="['reportStatus', allBg(descStatus)]" @click="jumpReportDetails">
      <p class="report-title">{{ reportDescTitle }}</p>
      <p class="report-content">{{ reportDescContent }}</p>
      <img src="./../../assets/images/<EMAIL>" />
    </div>
    <div class="title" v-if="info.complete_task_num != 0">健康任务完成情况</div>
    <div class="healthJob" v-if="info.complete_task_num != 0">
      <div class="money">
        <p>
          <span>{{ info.at_week_name}}您一共获得<i>{{info.points}}</i>健康金</span><img src="./../../assets/images/<EMAIL>" />
        </p>
        <span>通过上传最近一次医院就诊报告，可额</span>
        <span>外获得<i>80</i>健康金</span>
      </div>
      <img @click="uploadHospReport" v-if="info.hospital_report == ''" class="imgUpload" src="./../../assets/images/upload.png" />
      <img v-if="info.hospital_report != ''" class="imgUpload" src="./../../assets/images/uploadBtn.png" />
      <div class="bg"></div>
      <p class="p" v-if="info.motion_complete_days != 0"><img src="./../../assets/images/<EMAIL>" /><span>达成{{info.motion_complete_days}}天运动目标</span></p>
      <p class="p" v-if="info.diet_complete_days != 0"><img src="./../../assets/images/<EMAIL>" /><span>阅读{{info.diet_complete_days}}天食谱</span></p>
      <p class="p" v-if="info.measure_complete_status == 1"><img src="./../../assets/images/celiang.png" /><span>完成本周测量任务</span></p>
      <p class="p" v-if="info.read_complete_num != 0"><img src="./../../assets/images/<EMAIL>" /><span>学习{{info.read_complete_num}}篇文章</span></p>
      <p class="p" v-if="info.qa_complete_num != 0"><img src="./../../assets/images/<EMAIL>" /><span>完成{{info.qa_complete_num}}道文章答题</span></p>
    </div>
  </div>
</template>

<script>
  import { Toast } from 'vant'
  import wx from 'weixin-js-sdk'
  import { getWeekDetail } from '@/api/weekReport'

  export default {
    data() {
      return {
        info: {
          at_week_name: '',
          complete_task_num: '',
          complete_task_scale: '',
          diet_complete_days: '',
          hospital_report: '',
          hospital_report_upload_at: '',
          more_than_num: '',
          more_than_scale: '',
          motion_complete_days: '',
          next_bg_plan: '',
          next_bp_plan: '',
          next_motion_target: '',
          next_need_bg_0: '',
          next_need_bg_1: '',
          next_need_bp: '',
          points: '',
          qa_complete_days: '',
          read_complete_days: '',
          stage_name: '',
          start_to_end: '',
          task_total_num: '',
          id: ''
        },
        pressureStatus: 1, // 血压状态（2种）：1理想，2不稳定，0代表无数据
        bloodSugarStatus: 1, // 血糖状态（4种）：1理想，2欠佳，3不理想，4不理想低血糖，0代表无数据
        weekId: '', // 跳转往期报告的id
        reportDescTitle: '', // 跳转往期报告的title
        reportDescContent: '', // 跳转往期报告的content
        weekReport: false,
        source_type: '',
        flag: false,
        id: ''
      }
    },
    components: {},
    created() {
      this.id = this.$route.query.id
      this.source_type = this.$route.query.source_type
      this.init()
    },
    computed: {
      /**
       * 血压和血糖整体状态
       * @return {Number} 状态（3种）：1 normal、2 high、3 down
       */
      descStatus() {
        let pressureStatus = this.pressureStatus
        let bloodSugarStatus = this.bloodSugarStatus
        let status = ''
        // 血压或血糖有无数据情况下
        if (pressureStatus === 0 || bloodSugarStatus === 0) {
          if (pressureStatus !== 0) {
            if (pressureStatus === 1) {
              status = 1
            } else if (pressureStatus === 2) {
              status = 3
            }
          }
          if (bloodSugarStatus !== 0) {
            if (bloodSugarStatus === 1) {
              status = 1
            } else if (bloodSugarStatus === 3 || bloodSugarStatus === 4) {
              status = 2
            } else if (bloodSugarStatus === 2) {
              status = 3
            }
          }
        } else {
          // 当血压和血糖都为 1 理想时，返回 1 normal；
          // 当血糖为 3 不理想、4 不理想低血糖时，返回 2 high；
          // 其他状态时返回 3 down
          if (pressureStatus === 1 && bloodSugarStatus === 1) {
            status = 1
          } else if (bloodSugarStatus === 3 || bloodSugarStatus === 4) {
            status = 2
          } else {
            status = 3
          }
        }
        return status
      }
    },
    methods: {
      // 初始化
      init() {
        let that = this
        getWeekDetail({ id: this.id }).then(res => {
          // console.log(res)
          if (res.status == 0) {
            that.info = res.data
            if (res.data.guanjia_home_report_desc == '') {
              that.weekReport = false
            } else {
              that.weekReport = true
              that.pressureStatus = res.data.guanjia_home_report_desc.bp_status
              that.bloodSugarStatus = res.data.guanjia_home_report_desc.bg_status
              that.weekId = res.data.guanjia_home_report_desc.id
              that.reportDescTitle = res.data.guanjia_home_report_desc.title
              that.reportDescContent = res.data.guanjia_home_report_desc.content
              console.log(that.reportDescTitle, 'that.reportDescTitle')
              console.log(that.reportDescContent, 'that.reportDescContent')
            }
            that.flag = true
          }
        }).then(() => {
          this.$refs.main.style.minHeight = window.innerHeight + 'px'
        })
      },
      allBg(num) {
        if (num === 1) {
          return 'normal'
        } else if (num === 2) {
          return 'high'
        } else if (num === 3) {
          return 'down'
        }
      },
      jumpReportDetails() {
        this.$router.push({
          name: 'Report.Details',
          query: { 'source_type': this.source_type, id: this.weekId }
        })
      },
      uploadHospReport() {
        const UA = navigator.userAgent
        const isAndroid = UA.indexOf('Android') > -1 || UA.indexOf('Adr') > -1 // android终端
        const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
        let param = JSON.stringify({
          id: this.id
        })
        // 原生app方法名称：jumpUploadReport
        if (isAndroid) {
          console.log('安卓')
          window.android.jumpUploadReport(param)
        } else if (isIOS) {
          console.log('苹果')
          window.webkit.messageHandlers.jumpUploadReport.postMessage(param)
        } else {
          Toast('不支持您的设备上传')
        }
      },
      goBackHome() {
        const UA = navigator.userAgent
        const isAndroid = UA.indexOf('Android') > -1 || UA.indexOf('Adr') > -1 // android终端
        const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
        // 原生app方法名称：jumpAppHome
        if (isAndroid) {
          console.log('安卓')
          window.android.jumpAppPopHealthmanager()
        } else if (isIOS) {
          console.log('苹果')
          window.webkit.messageHandlers.jumpAppPopHealthmanager.postMessage('')
        } else {
          Toast('跳转失败')
        }
      }
    }
  }
</script>

<style lang='scss' scoped>
  @import '@/assets/scss/weekReport/weekReport.scss'
</style>
