<template>
  <div class='VMCHealthReport'>
    <div class="content">
      <div class="headerBlock">
        <div class="titleLine">
          <div class="leftImg">
            <img class="img" :src="headerInfo.icon" alt="">
          </div>
          <div class="rightTitle">
            <div class="title">
              <span>{{headerInfo.hospital}}</span>
              <span>{{headerInfo.title}}</span>
            </div>
            <div class="dept">
              <span>{{headerInfo.department}}</span>
              <span>评估时间：{{headerInfo.latest_assessment_time || '--'}}</span>
            </div>
          </div>
        </div>
        <div class="splitLine"></div>
        <div class="personInfo">
          <div class="infoItem">
            <span class="label">姓名：</span>
            <span class="val" v-if="headerInfo && headerInfo.patient_info">{{headerInfo.patient_info.name}}</span>
          </div>
          <div class="infoItem">
            <span class="label">年龄：</span>
            <span class="val" v-if="headerInfo && headerInfo.patient_info">{{headerInfo.patient_info.age}}</span>
          </div>
          <div class="infoItem">
            <span class="label">性别：</span>
            <span class="val" v-if="headerInfo && headerInfo.patient_info">{{headerInfo.patient_info.sex}}</span>
          </div>
          <div class="infoItem">
            <span class="label">身高：</span>
            <span class="val">{{headerInfo.height ? `${headerInfo.height}cm` : '--'}}</span>
          </div>
          <div class="infoItem">
            <span class="label">体重：</span>
            <span class="val">{{headerInfo.weight ? `${headerInfo.weight}kg` : '--'}}</span>
          </div>
          <div class="infoItem">
            <span class="label">条码号：</span>
            <span class="val" v-if="headerInfo && headerInfo.patient_info">{{headerInfo.patient_info.barcode}}</span>
          </div>
        </div>
      </div>
      <div class="inner">
        <div class="titleLine">血管衰老评估分级</div>
        <div class="innerContent flexBetween" style="max-height: 90px;">
          <div class="rate">
            <span class="rateVal" :style="`color: ${curRateObj.bgColor}`">{{curRateObj.rateVal}}</span>
            <span class="fromTips">* 评估标准来源于《血管衰老临床评估与干预中国专家共识2023》</span>
          </div>
          <div class="chart">
            <div class="item" v-for="(item,index) in rateArr" :key="item.rateVal">
              <div class="circleTag" :style="`background:${item.bgColor};visibility: ${item.checked ? 'visible' : 'hidden'}`">
                <div class="arrowFlag" :style="`border-top: 10px solid ${item.bgColor}`"></div>
                <div>{{ item.rateVal }}</div>
              </div>
              <div :style="`background:${item.bgColor}`" :class="['itemBlock',index == 0 ? 'firstBorderRadius' : '',index == 4 ? 'lastBorderRadius' : '']"></div>
              <div>{{item.rateVal}}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="innerOuter">
        <div class="leftInner">
          <div class="inner">
            <div class="titleLine">血管衰老评估分级</div>
            <div class="innerContent">
              <div class="topDocIntro">
                <img class="docImg" src="./img/doc_inrto.png" alt="">
                <div class="intro" v-if="adviseInfo.assessment_text">{{ adviseInfo.assessment_text.row2 }}</div>
              </div>
              <div class="intro1" v-if="adviseInfo.assessment_text">{{ adviseInfo.assessment_text.row1 }}</div>
            </div>
          </div>
          <div class="inner" v-if="adviseInfo && adviseInfo.suggest_prescription_text">
            <div class="titleLine">健康建议</div>
            <div class="innerContent">
              <div class="msgItem">
                <img class="icon" src="./img/eatIcon.png" alt="">
                <div class="rightMsg">
                  <span class="label">饮食建议：</span>
                  <span class="val">{{adviseInfo.suggest_prescription_text.eat}}</span>
                </div>
              </div>
              <div class="msgItem">
                <img class="icon" src="./img/sportIcon.png" alt="">
                <div class="rightMsg">
                  <span class="label">运动建议：</span>
                  <span class="val">{{adviseInfo.suggest_prescription_text.sport}}</span>
                </div>
              </div>
              <div class="msgItem">
                <img class="icon" src="./img/monitorIcon.png" alt="">
                <div class="rightMsg">
                  <span class="label">监测评估建议：</span>
                  <span class="val">{{adviseInfo.suggest_prescription_text.monitor}}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="rightInner">
          <div class="inner" v-if="assessmentInfo.length > 0">
            <div class="titleLine">评估异常项目</div>
            <div class="flexBetween">
              <div class="abnormalItem" :class="{'rightBorder bottomBorder': index == 0, 'bottomBorder': index == 1, 'rightBorder': index == 2}" v-for="(item,index) of assessmentInfo" :key="item.rf_category_id">
                <span class="abnormalTitle">{{item.rf_category_name}}</span>
                <img v-if="item.showLabelItemsNum < 6" :class="[item.rf_category_id == 11 && item.showLabelItemsNum != 0 ? 'warnIcon' : 'docIcon']" :src="item.showLabelItemsNum == 0 ? item.normalImg : item.abnormalImg" alt="">
                <div class="abnormalLine">
                  <div v-if="item.showLabels.length > 0">
                    <div v-for="innerItem in item.showLabels" :key="innerItem.rf_id">
                      <div class="labelName">
                        <div class="redCircle"></div>
                        {{ innerItem.rf_index_name }}：
                      </div>
                      <div class="valItem" v-for="lItem in innerItem.rf_params" :key="lItem.rf_param_id">{{ lItem.rf_param_display_name }}: {{ lItem.rf_param_display_value }}</div>
                    </div>
                  </div>
                  <span v-else class="noWarnText">恭喜您，未发现明显异常！</span>
                </div>
              </div>
            </div>
          </div>
          <div class="inner" v-else>
            <div class="titleLine">评估异常项目</div>
            <div class="innerContent colCenter">
              <img class="noWarnDocIcon" src="./img/noWarnDocIcon.png" alt="">
              <div class="noWarnDocTips">手动进行评估，请与医生进行沟通</div>
            </div>
          </div>
          <div class="docAppQrcode">
            <img class="qrcode" src="https://ares.zz-med.com/doctorapp/patientAppDownloadQrCode.png" alt="">
            <span>扫码下载 APP</span>
            <span>进行每日的家庭监测指标记录</span>
            <span>可以实时将结果反馈到医生查看哦</span>
          </div>
        </div>
      </div>
      <!-- <div class="blankBlock"></div> -->
      <div class="bottomBlock" style="margin-top: 35px;">
        <span>本报告内容仅供参考使用，评估判断标准来源于《血管衰老临床评估与干预中国专家共识2023》。</span>
        <span v-if="assessmentInfo.length > 0">第1页 / 共3页</span>
      </div>
      <!-- 第二页 -->
      <template  v-if="assessmentInfo.length > 0">
        <div class="headerBlock">
          <div class="titleLine">
            <div class="leftImg">
              <img class="img" :src="headerInfo.icon" alt="">
            </div>
            <div class="rightTitle">
              <div class="title">
                <span>{{headerInfo.hospital}}</span>
                <span>{{headerInfo.title}}</span>
              </div>
              <div class="dept">
                <span>{{headerInfo.department}}</span>
                <span>评估时间：{{headerInfo.latest_assessment_time || '--'}}</span>
              </div>
            </div>
          </div>
          <div class="splitLine"></div>
          <div class="personInfo">
            <div class="infoItem">
              <span class="label">姓名：</span>
              <span class="val" v-if="headerInfo && headerInfo.patient_info">{{headerInfo.patient_info.name}}</span>
            </div>
            <div class="infoItem">
              <span class="label">年龄：</span>
              <span class="val" v-if="headerInfo && headerInfo.patient_info">{{headerInfo.patient_info.age}}</span>
            </div>
            <div class="infoItem">
              <span class="label">性别：</span>
              <span class="val" v-if="headerInfo && headerInfo.patient_info">{{headerInfo.patient_info.sex}}</span>
            </div>
            <div class="infoItem">
              <span class="label">身高：</span>
              <span class="val">{{headerInfo.height ? `${headerInfo.height}cm` : '--'}}</span>
            </div>
            <div class="infoItem">
              <span class="label">体重：</span>
              <span class="val">{{headerInfo.weight ? `${headerInfo.weight}kg` : '--'}}</span>
            </div>
            <div class="infoItem">
              <span class="label">条码号：</span>
              <span class="val" v-if="headerInfo && headerInfo.patient_info">{{headerInfo.patient_info.barcode}}</span>
            </div>
          </div>
        </div>
        <div class="tableInner">
          <div class="titleLine">血管衰老评估结果</div>
          <div class="tableBlock">
            <div class="thead">
              <div class="col cell">分类</div>
              <div class="col cell">评估项</div>
              <div class="col cell">指标项</div>
              <div class="col cell">结果</div>
            </div>
            <div class="tbody">
              <div v-if="index < 3" v-for="(item,index) in assessmentInfo" :key="item.rf_category_id" class="categoryLine">
                <div class="category">{{item.rf_category_name}}</div>
                <div class="itemsOuter">
                  <div v-for="inner in item.items" :key="inner.rf_id" class="itemsLine">
                    <div class="itemsCol" style="display: flex;flex-direction: column;">
                      <div>{{ inner.rf_index_name }}</div>
                      <!-- 超重 -->
                      <div v-if="inner.rf_id == 89" class="normalTips">
                        BMI≥24
                      </div>
                      <!-- 中心性肥胖 -->
                      <div v-if="inner.rf_id == 90" class="normalTips">
                        男性：腰围≥85cm<br>女性：腰围≥80cm
                      </div>
                      <!-- FMD降低 -->
                      <div v-if="inner.rf_id == 96" class="normalTips">
                        FMD＜5%
                      </div>
                      <!-- PWV增快 -->
                      <div v-if="inner.rf_id == 97" class="normalTips">
                        右侧BA PWV≥1400<br>左侧BA PWV≥1400
                      </div>
                      <!-- IMT增厚 -->
                      <div v-if="inner.rf_id == 98" class="normalTips">
                        0~49岁：IMT≥0.7mm<br>50~59岁：IMT≥0.8mm<br>≥60岁：IMT≥0.9mm
                      </div>
                      <!-- 管腔扩大 -->
                      <div v-if="inner.rf_id == 99" class="normalTips">
                        左颈总动脉直径＞7.5mm<br>右颈总动脉直径＞7.5mm<br>升主动脉直径＞34mm
                      </div>
                      <img class="tableWarnIcon" v-if="inner.is_matched" src="./img/warnIcon.png" alt="">
                    </div>
                    <div class="paramOuter">
                      <div v-for="sItem in inner.rf_params" :key="sItem.rf_param_id" class="paramCol">
                        <div class="paramName cell">{{ sItem.rf_param_display_name || '--' }}</div>
                        <div class="paramVal cell">
                          {{ sItem.rf_param_display_value || '--' }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- <table class="table">
            <thead>
              <td>分类</td>
              <td>条件</td>
              <td>答案名称</td>
              <td>数值</td>
            </thead>
            <tbody>
              <tr>
                <td class="tableLabel" rowspan="7">危险因素</td>
                <td>超重</td>
                <td>BMI</td>
                <td>数值</td>
              </tr>
              <tr>
                <td>中心性肥胖</td>
                <td>腰围</td>
                <td>数值</td>
              </tr>
              <tr>
                <td>血脂异常</td>
                <td>是否存在血脂异常</td>
                <td>数值</td>
              </tr>
              <tr>
                <td>高血压</td>
                <td>是否患有高血压</td>
                <td>数值</td>
              </tr>
              <tr>
                <td>糖尿病</td>
                <td>是否有明确诊断的糖尿病</td>
                <td>数值</td>
              </tr>
              <tr>
                <td>吸烟</td>
                <td>请问您是否吸烟</td>
                <td>数值</td>
              </tr>
              <tr>
                <td>饮酒</td>
                <td>请问您是否饮酒</td>
                <td>数值</td>
              </tr>
              <tr>
                <td class="tableLabel" rowspan="3">血管受损表现</td>
                <td>FMD 降低</td>
                <td>血管扩张率（FMD）</td>
                <td>数值</td>
              </tr>
              <tr>
                <td>PWV 增加</td>
                <td>右侧BA PWV</td>
                <td>数值</td>
              </tr>
              <tr>
                <td>PWV 增快</td>
                <td>左侧BA PWV</td>
                <td>数值</td>
              </tr>
              <tr>
                <td class="tableLabel" rowspan="8">血管结构改变</td>
                <td rowspan="4">IMT 增厚</td>
                <td>右颈内 IMT</td>
                <td>数值</td>
              </tr>
              <tr>
                <td>右颈总 IMT</td>
                <td>数值</td>
              </tr>
              <tr>
                <td>左颈内 IMT</td>
                <td>数值</td>
              </tr>
              <tr>
                <td>左颈总 IMT</td>
                <td>数值</td>
              </tr>
              <tr>
                <td rowspan="3">管腔扩大</td>
                <td>左颈总动脉直径</td>
                <td>数值</td>
              </tr>
              <tr>
                <td>右颈总动脉直径</td>
                <td>数值</td>
              </tr>
              <tr>
                <td>升主动脉径</td>
                <td>数值</td>
              </tr>
              <tr>
                <td>动脉粥样（硬化）斑块</td>
                <td>左颈总斑块</td>
                <td>数值</td>
              </tr>
            </tbody>
          </table> -->
        </div>
        <div class="bottomBlock" style="margin-top: 20px;">
          <span>本报告内容仅供参考使用，评估判断标准来源于《血管衰老临床评估与干预中国专家共识2023》。</span>
          <span>第2页 / 共3页</span>
        </div>
      </template>
      <!-- 第三页 -->
       <template  v-if="assessmentInfo.length > 0">
        <div class="headerBlock">
          <div class="titleLine">
            <div class="leftImg">
              <img class="img" :src="headerInfo.icon" alt="">
            </div>
            <div class="rightTitle">
              <div class="title">
                <span>{{headerInfo.hospital}}</span>
                <span>{{headerInfo.title}}</span>
              </div>
              <div class="dept">
                <span>{{headerInfo.department}}</span>
                <span>评估时间：{{headerInfo.latest_assessment_time || '--'}}</span>
              </div>
            </div>
          </div>
          <div class="splitLine"></div>
          <div class="personInfo">
            <div class="infoItem">
              <span class="label">姓名：</span>
              <span class="val" v-if="headerInfo && headerInfo.patient_info">{{headerInfo.patient_info.name}}</span>
            </div>
            <div class="infoItem">
              <span class="label">年龄：</span>
              <span class="val" v-if="headerInfo && headerInfo.patient_info">{{headerInfo.patient_info.age}}</span>
            </div>
            <div class="infoItem">
              <span class="label">性别：</span>
              <span class="val" v-if="headerInfo && headerInfo.patient_info">{{headerInfo.patient_info.sex}}</span>
            </div>
            <div class="infoItem">
              <span class="label">身高：</span>
              <span class="val">{{headerInfo.height ? `${headerInfo.height}cm` : '--'}}</span>
            </div>
            <div class="infoItem">
              <span class="label">体重：</span>
              <span class="val">{{headerInfo.weight ? `${headerInfo.weight}kg` : '--'}}</span>
            </div>
            <div class="infoItem">
              <span class="label">条码号：</span>
              <span class="val" v-if="headerInfo && headerInfo.patient_info">{{headerInfo.patient_info.barcode}}</span>
            </div>
          </div>
        </div>
        <div class="tableInner">
          <div class="titleLine">血管衰老评估结果</div>
          <div class="tableBlock">
            <div class="thead">
              <div class="col cell">分类</div>
              <div class="col cell">评估项</div>
              <div class="col cell">指标项</div>
              <div class="col cell">结果</div>
            </div>
            <div class="tbody">
              <div v-if="index > 2" v-for="(item,index) in assessmentInfo" :key="item.rf_category_id" class="categoryLine">
                <div class="category">{{item.rf_category_name}}</div>
                <div class="itemsOuter">
                  <div v-for="inner in item.items" :key="inner.rf_id" class="itemsLine">
                    <div class="itemsCol">
                      {{ inner.rf_index_name }}
                      <img class="tableWarnIcon" v-if="inner.is_matched" src="./img/warnIcon.png" alt="">
                    </div>
                    <div class="paramOuter">
                      <div v-for="sItem in inner.rf_params" :key="sItem.rf_param_id" class="paramCol">
                        <div class="paramName cell">{{ sItem.rf_param_display_name || '--' }}</div>
                        <div class="paramVal cell">
                          {{ sItem.rf_param_display_value || '--' }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- <table class="table">
            <thead>
              <td>分类</td>
              <td>条件</td>
              <td>答案名称</td>
              <td>数值</td>
            </thead>
            <tbody>
              <tr>
                <td>血管结构改变</td>
                <td>冠心病</td>
                <td>是否患有冠心病</td>
                <td>数值</td>
              </tr>
              <tr>
                <td rowspan="6">血管衰老相关疾病</td>
                <td>脑卒中</td>
                <td>是否患有脑梗死病史</td>
                <td>数值</td>
              </tr>
              <tr>
                <td>脑卒中</td>
                <td>是否患有脑出血病史</td>
                <td>数值</td>
              </tr>
              <tr>
                <td>脑小血管病</td>
                <td>是否有脑小血管病</td>
                <td>数值</td>
              </tr>
              <tr>
                <td>间歇性跛行</td>
                <td>是否有间歇性跛行</td>
                <td>数值</td>
              </tr>
              <tr>
                <td>主动脉夹层</td>
                <td>是否有主动脉夹层</td>
                <td>数值</td>
              </tr>
              <tr>
                <td>主动脉瘤</td>
                <td>是否有主动脉瘤</td>
                <td>数值</td>
              </tr>
            </tbody>
          </table> -->
        </div>
        <div class="blankBlock"></div>
        <div class="bottomBlock" style="margin-top: 20px;">
          <span>本报告内容仅供参考使用，评估判断标准来源于《血管衰老临床评估与干预中国专家共识2023》。</span>
          <span>第3页 / 共3页</span>
        </div>
       </template>
    </div>
  </div>
</template>

<script>
import { reportProduce,getVMCDetail } from "@/api/saasReport";
export default {
  data() {
    return {
      rateArr: [
        {
          bgColor: '#42FA4B',
          rateVal: '',
          checked: false
        },
        {
          bgColor: '#3EBE01',
          rateVal: '',
          checked: false
        },
        {
          bgColor: '#E47E07',
          rateVal: '',
          checked: false
        },
        {
          bgColor: '#F63004',
          rateVal: '',
          checked: false
        },
        {
          bgColor: '#B51B0F',
          rateVal: '',
          checked: false
        },
      ],
      curRateObj: {},
      headerInfo: {},
      assessmentRate: {},
      assessmentInfo: {},
      adviseInfo: {}
    }
  },
  methods: {
    // 测试用这个 结构一样
    // getData(){
    //   // https://zmsc.zz-med-test.com/api/inside/v1/report/vmc/detail?patient_id=1619174290752536588&hosp_id=11633&dept_id=12066&project_id=120032
    //   let obj = {
    //     patient_id: '1619174290752536588',
    //     hosp_id: 11633,
    //     dept_id: 12066,
    //     project_id: 270032,
    //   }
    //   getVMCDetail(obj).then(res=>{
    //     if(res.code == 0){
    //       let { report_header,assessment,assessment_risk_factor_list,text_content } = res.data
    //       this.headerInfo = report_header
    //       this.assessmentRate = assessment
    //       // 等级数据处理
    //       this.rateArr.forEach((i,index)=>{
    //         assessment.ret_list.forEach((j,jindex)=>{
    //           if(index == jindex){
    //             i.rateVal = j
    //           }
    //           if(assessment.ret == j){
    //             this.rateArr[jindex].checked = true
    //           }
    //         })
    //       })
    //       this.curRateObj = this.rateArr.find(item=>{
    //         return item.checked
    //       })
    //       // 评估异常项目 数据处理
    //       this.assessmentInfo = assessment_risk_factor_list.map(item=>{
    //         item.showLabels = item.items.filter(inner=>{
    //           return inner.is_matched
    //         })
    //         item.showLabelItemsNum = item.showLabels.length
    //         item.showLabels.forEach(i=>{
    //           item.showLabelItemsNum += i.rf_params.length
    //         })
    //         item.normalImg = require('./img/doc.png')
    //         if(item.rf_category_id == 11){
    //           item.abnormalImg = require('./img/warnIcon.png')
    //         }
    //         if(item.rf_category_id == 12){
    //           item.abnormalImg = require('./img/vascularFunction.png')
    //         }
    //         if(item.rf_category_id == 13){
    //           item.abnormalImg = require('./img/vascularStructure.png')
    //         }
    //         if(item.rf_category_id == 14){
    //           item.abnormalImg = require('./img/heart.png')
    //         }
    //         return item
    //       })
    //       this.adviseInfo = text_content
    //     }
    //   })
    // },
    init(){
      let obj = {
        template_id: this.$route.query.template_id,
        sign: this.$route.query.sign,
      }
      reportProduce(obj).then(res=>{
        if(res.code == 0){
          let { report_header,assessment,assessment_risk_factor_list,text_content } = res.data
          this.headerInfo = report_header
          this.assessmentRate = assessment
          // 等级数据处理
          this.rateArr.forEach((i,index)=>{
            assessment.ret_list.forEach((j,jindex)=>{
              if(index == jindex){
                i.rateVal = j
              }
              if(assessment.ret == j){
                this.rateArr[jindex].checked = true
              }
            })
          })
          this.curRateObj = this.rateArr.find(item=>{
            return item.checked
          })
          // 评估异常项目 数据处理
          this.assessmentInfo = assessment_risk_factor_list.map(item=>{
            item.showLabels = item.items.filter(inner=>{
              return inner.is_matched
            })
            item.showLabelItemsNum = item.showLabels.length
            item.showLabels.forEach(i=>{
              item.showLabelItemsNum += i.rf_params.length
            })
            item.normalImg = require('./img/doc.png')
            if(item.rf_category_id == 11){
              item.abnormalImg = require('./img/warnIcon.png')
            }
            if(item.rf_category_id == 12){
              item.abnormalImg = require('./img/vascularFunction.png')
            }
            if(item.rf_category_id == 13){
              item.abnormalImg = require('./img/vascularStructure.png')
            }
            if(item.rf_category_id == 14){
              item.abnormalImg = require('./img/heart.png')
            }
            return item
          })
          this.adviseInfo = text_content
        }
      })
    }
  },
  created() {

  },
  mounted() {
    this.init()
  }
}
</script>

<style lang='scss'>
.VMCHealthReport {
  //google chrome explore
  -webkit-print-color-adjust: exact;
  //firefox  explore
  -moz-print-color-adjust: exact;
  color-adjust: exact;
  background: #ffffff;
  font-size: 14px;
  color: #000000;
  display: flex;
  justify-content: center;

  .content {
    width: 355px;
    // padding: 8px;
  }

  .headerBlock {
    width: 100%;
    padding-bottom: 4px;
    .titleLine {
      display: flex;
      align-items: center;
      height: 28px;
      .leftImg {
        width: 25px;
        height: 25px;
        .img{
          width: 100%;
          height: 100%;
          border-radius: 50%;
        }
      }

      .rightTitle {
        margin-left: 4px;
        flex: 1;

        .title,
        .dept {
          display: flex;
          justify-content: space-between;
          line-height: normal;
        }

        .title {
          font-weight: 600;
          font-size: 10px;
        }

        .dept {
          font-size: 8px;
          margin-top: 2px;
        }
      }
    }

    .splitLine {
      width: 100%;
      height: 1px;
      background: #3AA9DD;
      margin: 6px 0;
    }

    .personInfo {
      display: flex;
      justify-content: space-between;

      .infoItem {
        font-size: 6px;

        .label {
          font-weight: 500;
        }

        .val {}
      }
    }
  }
  .inner{
    width: 100%;
    border: 0.5px solid #3AA9DD;
    margin-top: 8px;
    .titleLine{
      box-sizing: border-box;
      width: 100%;
      height: 18px;
      line-height: 18px;
      background: #3AA9DD;
      text-align: left;
      padding-left: 5px;
      font-size: 7px;
      font-weight: 500;
    }
    .innerContent{
      box-sizing: border-box;
      padding: 8px;
      .rate{
        display: flex;
        flex: 1;
        flex-direction: column;
        text-align: left;
        .rateVal{
          font-size: 15px;
          font-weight: 500;
          line-height: normal;
        }
        .fromTips{
          font-size: 5px;
          margin-top: 4px;
        }
      }
      .chart{
        display: flex;
        align-items: center;
        gap: 2px;
        flex: 1;
        .firstBorderRadius{
          border-radius: 20px 0 0 20px;
        }
        .lastBorderRadius{
          border-radius: 0 20px 20px 0;
        }
        .item{
          display: flex;
          flex-direction: column;
          align-items: center;
          font-size: 5px;
          
          .itemBlock{
            width: 26px;
            height: 4px;
            background: #42FA4B;
            margin: 2px 0
          }
          .circleTag{
            width: 15px;
            height: 15px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            // background: green;
            position: relative;
            top: -2px;
            .arrowFlag{
              width: 0;
              height: 0;
              border-bottom: 5px solid transparent;
              border-left: 5px solid transparent;
              border-right: 5px solid transparent;
              // border-top: 5px solid green;
              position: absolute;
              bottom: -7px;
            }
          }
        }
      }
      .topDocIntro{
        font-size: 7px;
        width: 100%;
        position: relative;
        display: flex;
        .docImg{
          width: 60px;
          height: 50px;
        }
        .intro{
          width: calc(100% - 80px);
          text-align: left;
          position: absolute;
          right: 0;
          bottom: 0;
          line-height: 10px;
        }
      }
      .intro1{
        font-size: 7px;
        line-height: 10px;
        text-align: left;
        margin-top: 10px;
      }
      .msgItem:first-of-type{
        margin-top: 0;
      }
      .msgItem{
        display: flex;
        align-items: flex-start;
        margin-top: 5px;
        .icon{
          width: 7px;
          height: 7px;
        }
        .rightMsg{
          display: flex;
          flex-direction: column;
          margin-left: 4px;
          text-align: left;
          font-size: 7px;
        }
        .label{
          font-weight: 500;
        }
        .val{
          line-height: 10px;
          margin-top: 2px;
        }
      }
    }
  }
  .abnormalItem{
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 50%;
    font-size: 7px;
    padding: 6px;
    box-sizing: border-box;
    .abnormalTitle{
      font-weight: 500;
    }
    .warnIcon{
      width: 24px;
      height: 24px;
      margin-top: 5px;
    }
    .docIcon{
      width: 43px;
      height: 42px;
      margin-top: 5px;
    }
    .abnormalLine{
      margin-top: 5px;
      line-height: normal;
      font-size: 6px;
      .labelName{
        display: flex;
        align-items: center;
        margin-top: 2px;
        text-align: left;
        .redCircle{
          width: 2px;
          height: 2px;
          border-radius: 50%;
          background: red;
          margin-right: 2px;
        }
      }
      .valItem{
        text-align: left;
        color: #333333;
      }
      .noWarnText{
        font-size: 5px;
      }
    }
  }
  .rightBorder{
    border-right: 0.5px solid #3AA9DD;
  }
  .bottomBorder{
    border-bottom: 0.5px solid #3AA9DD;
  }
  .innerOuter{
    display: flex;
    gap: 8px;
    .leftInner,.rightInner{
      flex: 1;
    }
  }
  .blankBlock{
    width: 100%;
    height: 290px;
  }
  .bottomBlock{
    font-size: 4px;
    color: #666666;
    display: flex;
    justify-content: space-between;
    margin-top: 6px;
  }
  .tableInner{
    width: 100%;
    // border: 0.5px solid #3AA9DD;
    margin-top: 8px;
    .titleLine{
      box-sizing: border-box;
      width: 100%;
      height: 18px;
      line-height: 18px;
      background: #3AA9DD;
      text-align: center;
      font-size: 7px;
      font-weight: 500;
    }
  }
  .flexBetween{
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
  }
  .abnormalInnerMargin{
    // margin: -6px -7px;
  }
  .noWarnDocIcon{
    width: 75px;
    height: 60px;
  }
  .noWarnDocTips{
    font-size: 7px;
    font-weight: 500;
    margin-top: 6px;
  }
  .tableBlock{
    width: 100%;
    // padding: 8px;
    box-sizing: border-box;
    line-height: normal;
    font-size: 6px;
    display: flex;
    flex-direction: column;
    border: 0.5px solid #999999;
    .categoryLine:last-of-type{
      border-bottom: none;
    }
    .categoryLine{
      display: flex;
      align-items: center;
      border-bottom: 0.5px solid #999999;
    }
    .col,.category{
      // width: 25%;
      flex: 1;
    }
    .thead{
      width: 100%;
      display: flex;
      border-bottom: 0.5px solid #999999;
      .col{
        border-right: 0.5px solid #999999;
        font-weight: 500 !important;
      }
      .col:last-of-type{
        border-right: none;
      }
    }
    .cell{
      // padding: 4.5px;
      height: 18px;
      line-height: 18px;
      box-sizing: border-box;
    }
    .itemsOuter{
      // width: 75%;
      flex: 3;
      border-left: 0.5px solid #999999;
      .itemsLine:last-of-type{
        border-bottom: none;
      }
      .itemsLine{
        display: flex;
        // padding: 10px 0;
        border-bottom: 0.5px solid #999999;
        .itemsCol{
          // flex: 1;
          width: 33.3%;
          display: flex;
          align-items: center;
          justify-content: center;
          border-right: 0.5px solid #999999;
          position: relative;
          .tableWarnIcon{
            width: 8px;
            height: 8px;
            position: absolute;
            right: 5px;
            top: 50%;
            transform: translateY(-50%);
          }
        }
        .paramOuter{
          // flex: 2;
          width: 66.7%;
        }
        .paramCol:last-of-type{
          border-bottom: none !important;
        }
        .paramCol{
          display: flex;
          width: 100%;
          border-bottom: 0.5px solid #999999;
          .paramName{
            width: 50%;
            border-right: 0.5px solid #999999;
          }
          .paramVal{
            width: 50%;
          }
        }
      }
    }
  }
  .table{
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;
    
    td {
      vertical-align: middle;
      border: 0.5px solid #999999;
      padding: 7px;
      line-height: normal;
      font-size: 6px;
    }
    thead td{
      font-weight: 500;
    }
    tbody{
      color: #000000;
    }
    .tableLabel{
      font-weight: 500;
    }
  }
  .docAppQrcode{
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: 5px;
    line-height: 8px;
    margin-top: 10px;
    .qrcode{
      width: 35px;
      height: 35px;
    }
  }
  .colCenter{
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    padding: 50px 0 !important;
  }
  .normalTips{
    color: #979797;
    font-size: 5px;
  }
}
</style>