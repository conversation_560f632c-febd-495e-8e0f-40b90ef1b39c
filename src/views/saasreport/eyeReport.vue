<!--
 * @Descripttion: 眼底报告
 * @version:
 * @Author: guxiang
 * @Date:
 * @LastEditors:
 * @LastEditTime: 2021-12-29 15:08:34
-->
<template>
  <div id="printMe" class="content">
    <div class="header">
      <div class="title">
        {{ detail.hosp_name }}
      </div>
      <div class="sub_title">
        眼底图像筛查
      </div>
      <div class="date">日期：{{ detail.build_time ? detail.build_time.split(' ')[0] : '' }}</div>
    </div>
    <div class="user_content">
      <div class="row">
        <div class="cell">
          姓名：{{ detail.user_name }}
        </div>
        <div class="cell">
          性别：{{ detail.sex }}
        </div>
        <div class="cell">
          年龄：{{ detail.age }}
        </div>
        <div class="cell">
          编号：{{ detail.barcode }}
        </div>
      </div>
      <div class="row">
        <div class="cell">
          科室：{{ detail.dept_name }}
        </div>
      </div>
    </div>
    <div class="report_info">
      <div class="eye_info">
        <div>右眼图片</div>
        <img v-if="detail.right_img.length>0" :src="detail.right_img[0]" alt="">
      </div>
      <div class="eye_info">
        <div>左眼图片</div>
        <img v-if="detail.left_img.length>0" :src="detail.left_img[0]" alt="">
      </div>
      <div class="eye_info pt10">
        <img v-if="detail.right_img.length>1" :src="detail.right_img[1]" alt="">
      </div>
      <div class="eye_info pt10">
        <img v-if="detail.left_img.length>1" :src="detail.left_img[1]" alt="">
      </div>
    </div>
    <div class="report_command">
      <div class="title">印象：</div>
      <section class="command" v-html="detail.answer_name_948"></section>
      <div class="doc_name">操作人：</div>
    </div>
  </div>

</template>
<script>
import {getEyeReport} from "@/api/saasReport";

export default {
  data() {
    return {
      detail: {
        right_img: [],
        left_img: [],
      }
    }
  },
  created() {
    this.init()
  },
  methods: {
    async init() {

      // {
      //   "encrypt_hosp": 11633,
      //   "encrypt_params": "WfcHDdiNE4doKDKyzvCpGhA3dae71zwbZcRO9RtAReaDAosIgIHwc1N1NHCP2MJeiCy-xK7Lb0TIl3Pp15SNrDVggQ7i1rjuNTe8ejNtBwigXb3l7-WDDuXC0KBweKpAypCPWOSr_oHGONV5w8EiPn7uKhlqXJxDNNEf3OL6-kc"
      // }
      let res = await getEyeReport({
        "encrypt_hosp": this.$route.query.encrypt_hosp,
        "encrypt_params": this.$route.query.encrypt_params
      });
      if (res.code == 1) {
        let detail = res.data.detail
       // detail.answer_name_948 = detail.answer_name_948.replace('/\n/g', '<br>')
        this.detail = detail
        console.log(this.detail.answer_name_948)
      } else {
        this.$toast(res.message);
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.content {
  background: #ffffff;
  font-size: 14px;
  color: #000000;
  font-weight: normal;

  .header {
    display: flex;
    justify-content: center;
    flex-direction: column;

    .title {
      font-size: 13px;
    }

    .sub_title {
      padding-top: 5px;
      font-size: 11px;
    }

    .date {
      font-size: 8px;
      text-align: right;
      margin-right: 8px;
    }
  }

  .user_content {
    margin: 5px 0;
    padding: 5px;
    border-bottom: 1px solid #9B9B9B;
    border-top: 1px solid #9B9B9B;
    display: flex;
    flex-direction: column;
    font-size: 8px;

    .row {
      width: 100%;
      display: flex;
      flex-direction: row;
      white-space: nowrap;

      .cell {
        text-align: left;
        font-weight: 400;
        width: 25%;
      }
    }

    .row:not(:first-child) {
      padding-top: 5px;
    }

  }

  .report_info {
    padding-top: 10px;
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    font-size: 9px;

    .eye_info {
      width: 50%;

      img {
        padding-top: 5px;
        width: 100px;
      }
    }
  }

  .pt10 {
    padding-top: 10px;
  }

  .report_command {
    display: flex;
    flex-direction: column;
    font-size: 9px;
    padding-top: 28px;

    .title {
      text-align: left;
    }

    .command {
      white-space: pre-wrap;
      padding-top: 5px;
      padding-left: 15px;
      font-size: 8px;
      text-align: left;
      min-height: 80px;
      line-height: 16px;
    }

    .doc_name {
      padding-top: 5px;
      text-align: right;
      margin-right: 50px;
    }
  }
}

@media print {
  .command {
    page-break-inside: avoid;
    word-wrap: break-word;
  }
}

@page {
  size: auto; /* auto is the initial value */
  margin: 15mm 10mm; /* this affects the margin in the printer settings */
  padding: 10mm;
}

html {
  background-color: #ffffff;
  margin: 0px; /* this affects the margin on the html before sending to printer */
}

body {
  border: solid 0px blue;
  margin: 10mm 15mm 10mm 15mm; /* margin you want for the content */
}
</style>
