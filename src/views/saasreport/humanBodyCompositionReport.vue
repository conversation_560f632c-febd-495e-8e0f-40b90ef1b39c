<template>
  <div class='humanBodyCompositionReport'>
    <div class="content">
      <div class="headerBlock">
        <div class="titleLine">
          人体成分分析报告
        </div>
        <div class="splitLine"></div>
        <div class="personInfoLine">
          <div class="leftRate">
            <span class="rateLabel">身体打分</span>
            <span class="rateVal">100</span>
          </div>
          <div class="personInfo">
            <div class="line1">
              <div class="infoItem">
                <span class="label">姓名：</span>
                <span class="val">张三</span>
              </div>
              <div class="infoItem">
                <span class="label">性别：</span>
                <span class="val">女</span>
              </div>
              <div class="infoItem">
                <span class="label">日期：</span>
                <span class="val">2025-06-24 10:55:31</span>
              </div>
            </div>
            <div class="line2">
              <div class="infoItem">
                <span class="label">年龄：</span>
                <span class="val">20</span>
              </div>
              <div class="infoItem">
                <span class="label">身高：</span>
                <span class="val">176</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="innerOuter">
        <div class="leftInner">
          
        </div>
        <div class="rightInner">

        </div>
      </div>
      <div class="bottomBlock" style="margin-top: 35px;">
        <span>本报告内容仅供参考使用，评估判断标准来源于《血管衰老临床评估与干预中国专家共识2023》。</span>
      </div>
    </div>
  </div>
</template>

<script>
import {  } from "@/api/saasReport";
export default {
  data() {
    return {
      
    }
  },
  methods: {
    
  },
  created() {

  },
  mounted() {
    
  }
}
</script>

<style lang='scss'>
.humanBodyCompositionReport {
  //google chrome explore
  -webkit-print-color-adjust: exact;
  //firefox  explore
  -moz-print-color-adjust: exact;
  color-adjust: exact;
  background: #ffffff;
  font-size: 14px;
  color: #000000;
  display: flex;
  justify-content: center;

  .content {
    width: 355px;
    // padding: 8px;
  }

  .headerBlock {
    width: 100%;
    margin-top: 20px;
    .titleLine {
      font-weight: 600;
      font-size: 16px;
      height: 28px;
      line-height: 28px;
    }

    .splitLine {
      width: 100%;
      height: 1px;
      background: #3AA9DD;
      margin: 6px 0;
    }

    .personInfoLine {
      display: flex;
      .leftRate{
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        min-width: 40px;
        .rateLabel{
          font-size: 8px;
        }
        .rateVal{
          font-size: 16px;
          margin-top: 5px;
        }
      }
      .personInfo{
        display: flex;
        flex-wrap: wrap;
        font-size: 8px;
        margin-left: 20px;
        .line1,.line2{
          display: flex;
        }
        .infoItem {
          display: flex;
          align-items: center;
          margin-left: 10px;
          min-width: 50px;
          .label{

          }
          .val{
            margin-left: 5px;
          }
        }
      }
    }
  }
  .innerOuter{
    display: flex;
    gap: 8px;
    .leftInner,.rightInner{
      flex: 1;
    }
  }
  .bottomBlock{
    font-size: 4px;
    color: #666666;
    display: flex;
    justify-content: space-between;
    margin-top: 6px;
  }
}
</style>