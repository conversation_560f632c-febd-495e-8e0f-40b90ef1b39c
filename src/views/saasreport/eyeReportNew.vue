<template>
  <div class="box" ref="contenterPdf">
    <div id="printMe" class="content" ref="content" style="width: 840px;">
      <div class="print" data-html2canvas-ignore="true" v-if="isShow">
        <van-button class="downloadBtn" type="info" v-if="this.$route.query == 'mmc'"
          @click="debouncedClick">下载</van-button>
        <van-button class="downloadBtn" type="info" v-else @click="downloadPdf()">下载</van-button>
        <van-button class="printBtn" type="info" @click="printPage()">打印</van-button>
      </div>
      <div class="header">
        <div class="title">
          {{ detail.hosp_name }}
        </div>
        <div class="sub_title">
          眼底图像筛查
        </div>
        <div class="date">日期：{{ detail.build_time }}</div>
      </div>
      <div class="user_content">
        <div class="row">
          <div class="cell">
            姓名：{{ detail.user_name }}
          </div>
          <div class="cell">
            性别：{{ detail.sex }}
          </div>
          <div class="cell">
            年龄：{{ detail.age }}
          </div>
          <div class="cell">
            编号：{{ detail.barcode }}
          </div>
        </div>
        <div class="row">
          <div class="cell">
            科室：{{ detail.dept_name }}
          </div>
          <div class="cell">
            门诊号：{{ detail.magcardid }}
          </div>
          <div class="cell">
            住院号：{{ detail.admissionno }}
          </div>
          <div class="cell">
            医保号：{{ detail.sbkid }}
          </div>
        </div>
      </div>
      <div class="report_info">
        <div class="eye_info">
          <div>右眼图片</div>
          <img v-if="detail.right_img.length > 0" :src="detail.right_img[0]" alt="">
        </div>
        <div class="eye_info">
          <div>左眼图片</div>
          <img v-if="detail.left_img.length > 0" :src="detail.left_img[0]" alt="">
        </div>
        <div class="eye_info pt10">
          <img v-if="detail.right_img.length > 1" :src="detail.right_img[1]" alt="">
        </div>
        <div class="eye_info pt10">
          <img v-if="detail.left_img.length > 1" :src="detail.left_img[1]" alt="">
        </div>
      </div>
      <div class="eye_impression">
        <div class="eye_right">
          <div class="title">右眼印象：</div>
          <div class="detail">{{ detail.result.right_eye_dignosis }}</div>
        </div>
        <div class="eye_left">
          <div class="title">左眼印象：</div>
          <div class="detail">{{ detail.result.left_eye_dignosis }}</div>
        </div>
      </div>
      <div class="report_command">
        <div class="title">印象：</div>
        <span class="command">
          {{ detail.impression }}
        </span>
      </div>
      <div class="report_operator">
        <div class="doc_name">操作人：</div>
      </div>
      <div class="disclaimer">
        <span>免责声明：因拍摄或者白内障等原因，导致图片模糊或部分区域不可见造成的影响与本系统无关。以上评估结果均以上传图像为依据，仅供临床专科医生参考。本报告不涉及任何形式的诊断和治疗内容，亦不包含任何形式的用药建议。</span>
      </div>
    </div>
  </div>


</template>
<script>
import { getEyeReport, getSaasEyeReport } from "@/api/saasReport";
import moment from 'moment'
import { downloadPDF } from "@/utils/htmlToPdf.js"

export default {
  data() {
    return {
      detail: {
        right_img: [],
        left_img: [],
        result: {
          right_eye_dignosis: null,
          left_eye_dignosis: null
        }
      },
      isShow: false
    }
  },
  created() {
    this.switchProject()
  },
  methods: {
    switchProject() {
      if (this.$route.query.type == 'mmc') {
        this.initMmc()
        this.debouncedClick = this.$debounce(this.downloadPdf, 1000);
      } else if (this.$route.query.type == 'saas') {
        //
        this.initSaas()
      }
    },
    // mmc项目  眼底报告请求
    async initMmc() {
      let res = await getEyeReport({
        "encrypt_hosp": this.$route.query.encrypt_hosp,
        "encrypt_params": this.$route.query.encrypt_params
      });
      if (res.code == 1) {
        let detail = res.data.detail
        detail.build_time = moment(detail.build_time).format('YYYY-MM-DD')
        let impressionList = []
        if(detail.result.recom_transfer){
          impressionList.push(detail.result.recom_transfer)
        }
        if(detail.result.recom_reexam){
          impressionList.push(detail.result.recom_reexam)
        }
        if(detail.result.comment){
          impressionList.push(detail.result.comment)
        }
        detail.impression = impressionList.toString()
        this.detail = detail
        this.isShow = this.$route.query.btn != 0 ? true : false
      } else {
        this.$toast(res.message);
      }
    },
    // saas项目  眼底报告请求
    async initSaas() {
      let res = await getSaasEyeReport({
        "token": this.$route.query.token,
      });
      if (res.status == 200) {
        let data = res.data
        this.detail.build_time = data.check_date
        this.detail.hosp_name = data.hosp_name
        this.detail.user_name = data.name
        this.detail.sex = data.sex
        this.detail.age = data.age
        this.detail.barcode = data.barcode
        this.detail.dept_name = data.dept_name
        this.detail.magcardid = data.outpatient_num
        this.detail.admissionno = data.admission_num
        this.detail.sbkid = data.social_security_card
        this.detail.right_img = data.right_img ? data.right_img : []
        this.detail.left_img = data.left_img ? data.left_img : []
        this.detail.result = {
          right_eye_dignosis: data.right_eye_diagnosis,
          left_eye_dignosis: data.left_eye_diagnosis,
          recom_transfer: data.recom_transfer,
          recom_reexam: data.recom_reexam
        }
        let impressionList = []
        if(this.detail.result.recom_transfer){
          impressionList.push(this.detail.result.recom_transfer)
        }
        if(this.detail.result.recom_reexam){
          impressionList.push(this.detail.result.recom_reexam)
        }
        this.detail.impression = impressionList.toString()
        this.isShow = true
      } else {
        this.$toast(res.msg);
      }
    },
    // 下载
    downloadPdf() {
      if (this.$route.query.type == 'mmc') {
        let url = this.detail.download_pdf;
        let link = document.createElement('a');
        link.href = url;
        link.download = '眼底报告';
        link.style.display = 'none';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } else if (this.$route.query.type == 'saas') {
        downloadPDF(this.$refs.content)
      }
    },
    // 打印
    printPage() {
      window.print();
      return false;
    }
  }
};
</script>
<style lang="scss" scoped>
.box {
  display: flex;
  justify-content: center;
  text-align: center;
  
  .content {
    position: relative;
    background: #ffffff;
    font-size: 12px;
    color: #000000;
    font-weight: normal;
    width: 531px;
    min-height: 510px;
    padding-top: 10px;
    height: 90vh;

    .header {
      display: flex;
      justify-content: center;
      flex-direction: column;

      .title {
        font-size: 13px;
      }

      .sub_title {
        padding-top: 5px;
        font-size: 11px;
      }

      .date {
        font-size: 8px;
        text-align: right;
        margin-right: 8px;
      }
    }

    .user_content {
      margin: 5px 0;
      padding: 5px;
      border-bottom: 1px solid #9B9B9B;
      border-top: 1px solid #9B9B9B;
      display: flex;
      flex-direction: column;
      font-size: 8px;

      .row {
        width: 100%;
        display: flex;
        flex-direction: row;
        white-space: nowrap;

        .cell {
          text-align: left;
          font-weight: 400;
          width: 25%;
          font-size: 6px;
        }
      }

      .row:not(:first-child) {
        padding-top: 5px;
      }
    }

    .report_info {
      padding-top: 10px;
      display: flex;
      justify-content: center;
      flex-wrap: wrap;
      font-size: 9px;

      .eye_info {
        width: 50%;

        img {
          padding-top: 5px;
          width: 100px;
        }
      }
    }

    .pt10 {
      padding-top: 10px;
    }

    .eye_impression {
      display: flex;
      font-size: 8px;
      padding-top: 28px;

      .eye_right {
        width: 50%;
        text-align: left;
        display: flex;

        .title {
          font-size: 8px;
          width: 50px;
        }

        .detail {
          font-size: 8px;
          width: 87%;
        }
      }

      .eye_left {
        width: 50%;
        text-align: left;
        display: flex;
        padding-left: 20px;

        .title {
          font-size: 8px;
          width: 50px;
        }

        .detail {
          font-size: 8px;
          width: 87%;
        }
      }
    }

    .report_command {
      display: flex;
      font-size: 8px;
      padding-top: 20px;

      .title {
        text-align: left;
        min-width: 10%;
        max-width: 30%;
      }

      .command {
        font-size: 8px;
        text-align: left;
        min-height: 20px;
      }
    }

    .report_operator {
      font-size: 8px;
      position: relative;

      .doc_name {
        text-align: right;
        position: absolute;
        top: 30px;
        right: 50px;
      }
    }
  }
}

.disclaimer {
  font-size: 9px;
  padding-top: 20px;
  text-align: left;
  position: absolute;
  bottom: 2px;
  color: #9B9B9B;
  font-size: 6px;
}

.print {
  // width: 200px;
  position: absolute;
  top: 10px;
  right: 38px;
  display: flex;
  justify-content: center;
  align-items: center;

  .downloadBtn {
    margin: 4px;
    height: 12px;
    font-size: 6px;
    border-radius: 4px;
  }

  .printBtn {
    margin: 4px;
    height: 12px;
    font-size: 6px;
    border-radius: 4px;
  }
}

@media print {
  .print {
    display: none;
  }

  .command {
    page-break-inside: avoid;
    word-wrap: break-word;
  }
}

@page {
  size: auto;
  /* auto is the initial value */
  margin: 15mm 10mm;
  /* this affects the margin in the printer settings */
  padding: 10mm;
}

html {
  background-color: #ffffff;
  margin: 0px;
  /* this affects the margin on the html before sending to printer */
}

body {
  border: solid 0px blue;
  margin: 10mm 15mm 10mm 15mm;
  /* margin you want for the content */
}
</style>
