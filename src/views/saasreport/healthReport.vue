<!--
 * @Descripttion: 健康体验整合报告
 * @version:
 * @Author: guxiang
 * @Date: 2021-12-22 09:28:34
 * @LastEditors: guxiang
 * @LastEditTime: 2021-12-29 15:08:34
-->
<template>
  <div id="printMe" class="content">
    <div v-if="status">
      <div
        class="flexContent break"
        v-if="list.length == 0 && materialImg == ''"
      >
        <div class="pageHeader">
          <div class="logo-title" style="border-bottom: 0.5px solid #a1a4ab">
            <div class="logo"></div>
            <div>健康体验整合报告</div>
            <svg id="svgcode" class="svgcode"></svg>
          </div>
          <div class="person-info">
            <div>姓名：{{ personInfo.patient.name }}</div>
            <div>
              {{ personInfo.patient.sex_name }}
              <!-- {{ personInfo.patient.sex == 1 ? "男" : "女" }} -->
              <span> | </span>
              <span>{{ personInfo.patient.age }}</span>
            </div>
            <div>手机号：{{ personInfo.patient.mobile }}</div>
            <div>体验门店：{{ personInfo.patient.department }}</div>
            <!-- <div>报告时间：{{ personInfo.report_time }}</div> -->
          </div>
        </div>
        <!-- <div class="noTest">
          未查询到您今日（检查日期）的任何检查结果，快去进行检查吧
        </div> -->
        <div class="noTest">未查询到您今日的任何检查结果，快去进行检查吧</div>
      </div>
      <div v-for="(item, index) in list" :key="index">
        <!-- 体质指数测量 -->
        <div
          :class="index % 2 == 0 ? 'flexContent' : 'flexContent break'"
          v-if="item == 'height_weight'"
        >
          <div class="pageHeader">
            <div class="logo-title" style="border-bottom: 0.5px solid #a1a4ab">
              <div class="logo"></div>
              <div>健康体验整合报告</div>
              <svg id="svgcode" class="svgcode"></svg>
            </div>
            <div class="person-info">
              <div>姓名：{{ personInfo.patient.name }}</div>
              <div>
                {{ personInfo.patient.sex_name }}
                <!-- {{ personInfo.patient.sex == 1 ? "男" : "女" }} -->
                <span> | </span>
                <span>{{ personInfo.patient.age }}</span>
              </div>
              <div>手机号：{{ personInfo.patient.mobile }}</div>
              <div>体验门店：{{ personInfo.patient.department }}</div>
              <!-- <div>报告时间：{{ personInfo.report_time }}</div> -->
            </div>
          </div>
          <div class="bodyIndex">
            <img class="backgroundImg" src="./img/bodyheight_weight.png" alt="" />
            <div class="postionText">
              <img
                v-if="[1, 3].includes(personInfo.height_weight.abnormal_info_bmi.deflection)"
                class="bodyClass"
                src="./img/lowBody.png"
                alt=""
              />
              <img
                v-if="[0].includes(personInfo.height_weight.abnormal_info_bmi.deflection)"
                class="bodyClass"
                src="./img/normalBody.png"
                alt=""
              />
              <img
                v-if="[2].includes(personInfo.height_weight.abnormal_info_bmi.deflection)"
                class="bodyClass"
                src="./img/overloadBody.png"
                alt=""
              />
              <img
                v-if="[4].includes(personInfo.height_weight.abnormal_info_bmi.deflection)"
                class="bodyClass"
                src="./img/fatBody.png"
                alt=""
              />
              <div class="height">{{ personInfo.height_weight.height }}</div>
              <div class="weight">{{ personInfo.height_weight.weight }}</div>
              <div class="BMI">{{ personInfo.height_weight.bmi }}</div>
              <img
                v-if="[1, 2, 3, 4].includes(personInfo.height_weight.abnormal_info_bmi.deflection)"
                class="error"
                src="./img/error.png"
                alt=""
              />
<!--              <div :class="'bodyCircle ' + bodyPostion"></div>-->
              <div :class="`height_weight_label height_weight_label_abnormal${personInfo.height_weight.abnormal_info_bmi.indicator_value.indexOf(personInfo.height_weight.abnormal_info_bmi.status_display)}`">
                <div class="height_weight_label_item lowBMI" v-if="personInfo.height_weight.abnormal_info_bmi.indicator_label">
                  {{`BMI<${personInfo.height_weight.abnormal_info_bmi.indicator_label[0]}`}}
                </div>
                <div class="height_weight_label_item normalBMI" v-if="personInfo.height_weight.abnormal_info_bmi.indicator_label">
                  {{`${personInfo.height_weight.abnormal_info_bmi.indicator_label[0]}≤BMI<${personInfo.height_weight.abnormal_info_bmi.indicator_label[1]}`}}
                </div>
                <div class="height_weight_label_item overloadBMI" v-if="personInfo.height_weight.abnormal_info_bmi.indicator_label">
                  {{`${personInfo.height_weight.abnormal_info_bmi.indicator_label[1]}≤BMI<${personInfo.height_weight.abnormal_info_bmi.indicator_label[2]}`}}
                </div>
                <div class="height_weight_label_item fatBMI" v-if="personInfo.height_weight.abnormal_info_bmi.indicator_label">
                  {{`BMI≥${personInfo.height_weight.abnormal_info_bmi.indicator_label[2]}`}}
                </div>
                <img src="./img/bodyheight_weight_act.png" alt="" class="bodyheight_weight_act" v-if="personInfo.height_weight.abnormal_info_bmi.deflection !== undefined">
              </div>
              <div :class="`height_weight_indicator`" >
                <div :class="`height_weight_indicator_item height_weight_indicator_item${index} height_weight_indicator_item_act${personInfo.height_weight.abnormal_info_bmi.status_display === item ? 0 : ''}`" v-for="(item, index) in personInfo.height_weight.abnormal_info_bmi.indicator_value" :key="index">
                  <div class="height_weight_indicator_item_value">{{item}}</div>
                  <div v-if="personInfo.height_weight.abnormal_info_bmi.indicator_label[index] !== undefined" class="height_weight_indicator_item_label">{{personInfo.height_weight.abnormal_info_bmi.indicator_label[index]}}</div>
                  <div class="height_weight_indicator_item_abnormal"></div>
                </div>
              </div>
            </div>
          </div>
          <div v-if="index % 2 != 0" class="pageFooter">
            <div>
              <span>{{ Math.ceil((index + 1) / 2) }}</span>
              <span class="printTime"
                >报告时间：{{ personInfo.report_time.slice(0, 16) }}</span
              >
            </div>
            <div>
              本报告内容仅供参考使用，不能代替或作为医院或专业机构的诊疗诊断结果或意见。
            </div>
          </div>
        </div>
        <!-- 高血压 -->
        <div
          :class="index % 2 == 0 ? 'flexContent' : 'flexContent break'"
          v-if="item == 'bp'"
        >
          <div v-if="index % 2 == 0" class="pageHeader">
            <div class="logo-title" style="border-bottom: 0.5px solid #a1a4ab">
              <div class="logo"></div>
              <div>健康体验整合报告</div>
              <svg id="svgcode" class="svgcode"></svg>
            </div>
            <div class="person-info">
              <div>姓名：{{ personInfo.patient.name }}</div>
              <div>
                {{ personInfo.patient.sex_name }}
                <!-- {{ personInfo.patient.sex == 1 ? "男" : "女" }} -->
                <span> | </span>
                <span>{{ personInfo.patient.age }}</span>
              </div>
              <div>手机号：{{ personInfo.patient.mobile }}</div>
              <div>体验门店：{{ personInfo.patient.department }}</div>
              <!-- <div>报告时间：{{ personInfo.report_time }}</div> -->
            </div>
          </div>
          <div class="bodyIndex">
            <img
              v-if="personInfo.bp.abnormal_info_sbp.indicator_value.length === 3"
              class="backgroundImg"
              src="./img/bloodPressure3.png"
              alt=""
            />
            <img
              v-else
              class="backgroundImg"
              src="./img/bloodPressure5.png"
              alt=""
            />
            <div class="postionText">
              <div class="systolic">{{ personInfo.bp.sbp }}</div>
              <div class="stretch">{{ personInfo.bp.dbp }}</div>
              <div class="heartRate">{{ personInfo.bp.pulse }}</div>
              <img
                v-if="[1, 2, 3, 4].includes(personInfo.bp.abnormal_info_sbp.deflection)"
                class="systolicWarn"
                src="./img/error.png"
                alt=""
              />
              <img
                v-if="[1, 2, 3, 4].includes(personInfo.bp.abnormal_info_dbp.deflection)"
                class="stretchWarn"
                src="./img/error.png"
                alt=""
              />
<!--              <div :class="'systolicCircle ' + systolicPostion"></div>-->
<!--              <div :class="'stretchCircle ' + stretchPostion"></div>-->

              <div :class="`sbp_indicator sbp_indicator_length${personInfo.bp.abnormal_info_sbp.indicator_value.length}`" >
                <div :class="`sbp_indicator_item sbp_indicator_item${index} sbp_indicator_item_act${personInfo.bp.abnormal_info_sbp.status_display === item ? 0 : ''}`" v-for="(item, index) in personInfo.bp.abnormal_info_sbp.indicator_value" :key="index">
                  <div class="sbp_indicator_item_value">{{item}}</div>
                  <div v-if="personInfo.bp.abnormal_info_sbp.indicator_label[index] !== undefined" class="sbp_indicator_item_label">{{personInfo.bp.abnormal_info_sbp.indicator_label[index]}}</div>
                  <div class="sbp_indicator_item_abnormal"></div>
                </div>
              </div>

              <div :class="`dbp_indicator dbp_indicator_length${personInfo.bp.abnormal_info_dbp.indicator_value.length}`" >
                <div :class="`dbp_indicator_item dbp_indicator_item${index} dbp_indicator_item_act${personInfo.bp.abnormal_info_dbp.status_display === item ? 0 : ''}`" v-for="(item, index) in personInfo.bp.abnormal_info_dbp.indicator_value" :key="index">
                  <div class="dbp_indicator_item_value">{{item}}</div>
                  <div v-if="personInfo.bp.abnormal_info_dbp.indicator_label[index] !== undefined" class="dbp_indicator_item_label">{{personInfo.bp.abnormal_info_dbp.indicator_label[index]}}</div>
                  <div class="dbp_indicator_item_abnormal"></div>
                </div>
              </div>
            </div>
          </div>
          <div v-if="index % 2 != 0" class="pageFooter">
            <div>
              <span>{{ Math.ceil((index + 1) / 2) }}</span>
              <span class="printTime"
                >报告时间：{{ personInfo.report_time.slice(0, 16) }}</span
              >
            </div>
            <div>
              本报告内容仅供参考使用，不能代替或作为医院或专业机构的诊疗诊断结果或意见。
            </div>
          </div>
        </div>
        <!-- 糖尿病视网膜疾病筛查 -->
        <div
          :class="index % 2 == 0 ? 'flexContent' : 'flexContent break'"
          v-if="item == 'eye_examine'"
        >
          <div v-if="index % 2 == 0" class="pageHeader">
            <div class="logo-title" style="border-bottom: 0.5px solid #a1a4ab">
              <div class="logo"></div>
              <div>健康体验整合报告</div>
              <svg id="svgcode" class="svgcode"></svg>
            </div>
            <div class="person-info">
              <div>姓名：{{ personInfo.patient.name }}</div>
              <div>
                {{ personInfo.patient.sex_name }}
                <!-- {{ personInfo.patient.sex == 1 ? "男" : "女" }} -->
                <span> | </span>
                <span>{{ personInfo.patient.age }}</span>
              </div>
              <div>手机号：{{ personInfo.patient.mobile }}</div>
              <div>体验门店：{{ personInfo.patient.department }}</div>
              <!-- <div>报告时间：{{ personInfo.report_time }}</div> -->
            </div>
          </div>
          <div class="bodyIndex">
            <img class="backgroundImg" src="./img/eyeIssue.png" alt="" />
            <div class="postionText">
              <img
                class="lefteye"
                :src="personInfo.eye_examine.left_eye_picture"
                alt=""
              />
              <img
                class="righteye"
                :src="personInfo.eye_examine.right_eye_picture"
                alt=""
              />
              <img class="lefteyeText" src="./img/lefteye.png" alt="" />
              <img class="righteyeText" src="./img/righteye.png" alt="" />
              <div
                v-if="
                  personInfo.eye_examine.left_eye_dignosis &&
                  personInfo.eye_examine.left_eye_picture != ''
                "
              >
                <div
                  class="lefteyeDes"
                  v-if="
                    personInfo.eye_examine.left_eye_dignosis == '图片不可读'
                  "
                >
                  图片不可读(可能因小瞳孔或某些原因导致照片不能满足识别需要的清晰度)
                </div>
                <div
                  class="lefteyeDes"
                  v-else-if="
                    personInfo.eye_examine.left_eye_dignosis ==
                    '未见明显糖尿病视网膜改变'
                  "
                >
                  <label>结论：{{ personInfo.eye_examine.left_eye_dignosis }}</label><strong></strong>
                  <span
                    ></span
                  >
                </div>
                <div class="lefteyeDes" v-else>
                  <label>结论：{{ personInfo.eye_examine.left_eye_dignosis }}</label><strong></strong>
                  <span
                    ></span
                  >
                </div>
              </div>

              <div
                v-if="
                  personInfo.eye_examine.right_eye_dignosis &&
                  personInfo.eye_examine.right_eye_picture != ''
                "
              >
                <div
                  class="righteyeDes"
                  v-if="
                    personInfo.eye_examine.right_eye_dignosis == '图片不可读'
                  "
                >
                  图片不可读(可能因小瞳孔或某些原因导致照片不能满足识别需要的清晰度)
                </div>
                <div
                  class="righteyeDes"
                  v-else-if="
                    personInfo.eye_examine.right_eye_dignosis ==
                    '未见明显糖尿病视网膜改变'
                  "
                >
                  <label>结论：{{ personInfo.eye_examine.right_eye_dignosis }}</label><strong></strong>
                  <span
                    ></span
                  >
                </div>
                <div class="righteyeDes" v-else>
                  <label>结论：{{ personInfo.eye_examine.right_eye_dignosis }}</label><strong></strong>
                  <span
                    ></span
                  >
                </div>
              </div>
            </div>
          </div>
          <div v-if="index % 2 != 0" class="pageFooter">
            <div>
              <span>{{ Math.ceil((index + 1) / 2) }}</span>
              <span class="printTime"
                >报告时间：{{ personInfo.report_time.slice(0, 16) }}</span
              >
            </div>
            <div>
              本报告内容仅供参考使用，不能代替或作为医院或专业机构的诊疗诊断结果或意见。
            </div>
          </div>
        </div>
        <!-- 慢阻肺筛查 -->
        <div
          :class="index % 2 == 0 ? 'flexContent' : 'flexContent break'"
          v-if="item == 'chest'"
        >
          <div v-if="index % 2 == 0" class="pageHeader">
            <div class="logo-title" style="border-bottom: 0.5px solid #a1a4ab">
              <div class="logo"></div>
              <div>健康体验整合报告</div>
              <svg id="svgcode" class="svgcode"></svg>
            </div>
            <div class="person-info">
              <div>姓名：{{ personInfo.patient.name }}</div>
              <div>
                {{ personInfo.patient.sex_name }}
                <!-- {{ personInfo.patient.sex == 1 ? "男" : "女" }} -->
                <span> | </span>
                <span>{{ personInfo.patient.age }}</span>
              </div>
              <div>手机号：{{ personInfo.patient.mobile }}</div>
              <div>体验门店：{{ personInfo.patient.department }}</div>
              <!-- <div>报告时间：{{ personInfo.report_time }}</div> -->
            </div>
          </div>
          <div class="bodyIndex">
            <img class="backgroundImg" src="./img/breathIssue.png" alt="" />
            <div class="postionText">
              <table class="table">
                <!-- <tr>
                <td>项目</td>
                <td>单位</td>
                <td>实际值</td>
                <td>预期值</td>
                <td>
                  占比<span class="proportion">（实测占预期值的比例）</span>
                </td>
                <td>正常范围</td>
              </tr> -->
                <tr>
                  <td>用力肺活量 <span>FVC</span></td>
                  <td>L</td>
                  <td>{{ personInfo.chest.fvc }}</td>
                  <td>{{ personInfo.chest.fvc_pred }}</td>
                  <td class="bolder">
                    {{ personInfo.chest.fvc_perc_pred }}
                    <img
                      v-if="
                        personInfo.chest.fvc_perc_pred < 80 &&
                        personInfo.chest.fvc_perc_pred
                      "
                      class="tableWarn"
                      src="./img/error.png"
                      alt=""
                    />
                  </td>
                  <td style="font-family: LucidaGrande">≥80%</td>
                </tr>
                <tr>
                  <td>一秒用力呼气容积 <span>FEV1</span></td>
                  <td>L</td>
                  <td>{{ personInfo.chest.fev_one_dot_zero }}</td>
                  <td>{{ personInfo.chest.fev_one_dot_zero_pred }}</td>
                  <td>{{ personInfo.chest.fev_one_dot_zero_perc_pred }}</td>
                  <td>-</td>
                </tr>
                <tr>
                  <td>一秒率 <span>FEV1/FVC</span></td>
                  <td>%</td>
                  <td class="bolder">
                    {{ personInfo.chest.fev_one_dot_zero_fvc
                    }}<img
                      v-if="
                        personInfo.chest.fev_one_dot_zero_fvc < 70 &&
                        personInfo.chest.fev_one_dot_zero_fvc
                      "
                      class="tableWarn"
                      src="./img/error.png"
                      alt=""
                    />
                  </td>
                  <td>{{ personInfo.chest.fev_one_dot_zero_fvc_pred }}</td>
                  <td>
                    {{ personInfo.chest.fev_one_dot_zero_fvc_perc_pred }}
                  </td>
                  <td style="font-family: LucidaGrande">≥70%</td>
                </tr>
              </table>
              <div class="charts">
                <div class="top">
                  <div class="leftCharts">
                    <div
                      v-if="
                        personInfo.chest.fvc_perc_pred < 80 &&
                        personInfo.chest.fev_one_dot_zero_fvc >= 70
                      "
                      class="postionCircle"
                      :style="{
                        bottom: `calc(${
                        ((personInfo.chest.fev_one_dot_zero_fvc - 70) / 30 < 1
                          ? (personInfo.chest.fev_one_dot_zero_fvc - 70) / 30
                          : 1) * 100
                        }% - 3px)`,
                        left:`calc(${(personInfo.chest.fvc_perc_pred / 80) * 100}% - 3px)`
                      }"
                    ></div>
                  </div>
                  <div class="rightCharts">
                    <div
                      v-if="
                        personInfo.chest.fvc_perc_pred >= 80 &&
                        personInfo.chest.fev_one_dot_zero_fvc >= 70
                      "
                      class="postionCircle"
                      :style="{
                        bottom: `calc(${
                        ((personInfo.chest.fev_one_dot_zero_fvc - 70) / 30 < 1
                          ? (personInfo.chest.fev_one_dot_zero_fvc - 70) / 30
                          : 1) * 100
                        }% - 3px)`,
                        left:`calc(${
                        ((personInfo.chest.fvc_perc_pred - 80) / 20 < 1
                          ? (personInfo.chest.fvc_perc_pred - 80) / 20
                          : 1) * 100
                        }% - 3px)`
                      }"
                    ></div>
                  </div>
                </div>
                <div class="bottom">
                  <div class="leftCharts leftCharts1">
                    <div
                      v-if="
                        personInfo.chest.fvc_perc_pred < 80 &&
                        personInfo.chest.fev_one_dot_zero_fvc < 70
                      "
                      class="postionCircle"
                      :style="{
                        bottom: `calc(${(personInfo.chest.fev_one_dot_zero_fvc / 70) * 100}% - 3px)`,
                        left:`calc(${(personInfo.chest.fvc_perc_pred / 80) * 100}% - 3px)`
                      }"
                    ></div>
                  </div>
                  <div class="rightCharts">
                    <div
                      v-if="
                        personInfo.chest.fvc_perc_pred >= 80 &&
                        personInfo.chest.fev_one_dot_zero_fvc < 70
                      "
                      class="postionCircle"
                      :style="{
                        bottom: `calc(${(personInfo.chest.fev_one_dot_zero_fvc / 70) * 100}% - 3px)`,
                        left:`calc(${
                        ((personInfo.chest.fvc_perc_pred - 80) / 20 < 1
                          ? (personInfo.chest.fvc_perc_pred - 80) / 20
                          : 1) * 100
                        }% - 3px)`
                      }"
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div v-if="index % 2 != 0" class="pageFooter">
            <div>
              <span>{{ Math.ceil((index + 1) / 2) }}</span>
              <span class="printTime"
                >报告时间：{{ personInfo.report_time.slice(0, 16) }}</span
              >
            </div>
            <div>
              本报告内容仅供参考使用，不能代替或作为医院或专业机构的诊疗诊断结果或意见。
            </div>
          </div>
        </div>
      </div>
      <div v-if="list.length % 2 != 0" class="flexContent break lastFooter">
        <div class="pageFooter">
          <div>
            <span>{{ Math.ceil(list.length / 2) }}</span>
            <span class="printTime"
              >报告时间：{{ personInfo.report_time.slice(0, 16) }}</span
            >
          </div>
          <div>
            本报告内容仅供参考使用，不能代替或作为医院或专业机构的诊疗诊断结果或意见。
          </div>
        </div>
      </div>
      <div class="flexContent break" v-if="materialImg != ''">
        <div class="pageHeader">
          <div class="logo-title" style="border-bottom: 0.5px solid #a1a4ab">
            <div class="logo"></div>
            <div>健康体验整合报告</div>
            <svg id="svgcode" class="svgcode"></svg>
          </div>
          <div class="person-info">
            <div>姓名：{{ personInfo.patient.name }}</div>
            <div>
              {{ personInfo.patient.sex_name }}
              <!-- {{ personInfo.patient.sex == 1 ? "男" : "女" }} -->
              <span> | </span>
              <span>{{ personInfo.patient.age }}</span>
            </div>
            <div>手机号：{{ personInfo.patient.mobile }}</div>
            <div>体验门店：{{ personInfo.patient.department }}</div>
          </div>
        </div>
        <div class="bodyIndex">
          <img class="materialImg" :src="materialImg" alt="" />
        </div>
        <div class="pageFooter">
          <div>
            <span>{{ Math.ceil(list.length / 2) + 1 }}</span>
            <span class="printTime"
              >报告时间：{{ personInfo.report_time.slice(0, 16) }}</span
            >
          </div>
          <div>
            本报告内容仅供参考使用，不能代替或作为医院或专业机构的诊疗诊断结果或意见。
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import JsBarcode from "jsbarcode";
import { reportProduce } from "@/api/saasReport";
export default {
  data() {
    return {
      list: [],
      status: false,
      personInfo: {
        height_weight: {}, //体质指数
        bp: {}, //高血压
        eye_examine: {}, //视网膜疾病
        chest: {}, //肺功能筛查
        patient: {}, //个人信息
        as: {
          material_url: [],
        }, //PWV
      },
      // personAge: null, //计算的个人年龄
      materialImg: "", //PWV图片地址
      printObj: {
        id: "printMe",
        // popTitle: "打印66",
        ignoreClass: "noprint",
        endCallback: (e) => {
          console.log(e);
        },
      },
    };
  },
  computed: {
  },
  created() {
    this.getReportProduce();
  },
  methods: {
    async getReportProduce() {
      // let res = await reportProduce({
      //   template_id: 1,
      //   sign: "5157898f5cad7745ba0dd68be7d0efbebb73c7163aff439357be3abd3471b714599df0007c3cc290cb3e0b4d50d366549df85078b3871bf14042221719dc6ee90f09aed8dd3695e9f55bcf603763339c18eb2bde00471f79b5deea91798ff42a5331d51f73704f5d0a888d9ea839a30fe13cefb822668ed452eac24cdcf69f56b6c3989a1157ea3fca34d8a5008e0993c7cbc75721db2d95a1fd08d26d472ab8cd6bfbe2d73a858abe796db0fcb884ac9912fb83f723f0a7b2ed75eefd0c059cb233211757cd9351eaebeab6f314afe1985eba202a19bfe80bf6239bf9b5d2db",
      // });
      let res = await reportProduce({
        template_id: this.$route.query.template_id,
        sign: this.$route.query.sign,
      });

      // let res = {
      //   "code": 0,
      //   "msg": "处理成功",
      //   "data": {
      //     "height_weight": {
      //       "abnormal_info": [
      //         {
      //           "status_display": "超重",
      //           "deflection": 2,
      //           "code": "bmi",
      //           "indicator_label": [
      //             "18.5",
      //             "24",
      //             "28"
      //           ],
      //           "abnormal_code": "bmi",
      //           "abnormal_status": 1,
      //           "status_display_group": "",
      //           "normal_range": "18.5<=n<24",
      //           "indicator_value": [
      //             "过低",
      //             "正常",
      //             "超重",
      //             "肥胖"
      //           ]
      //         }
      //       ],
      //       "check_date": "2024-05-27 13:29:47",
      //       "weight": "180.0",
      //       "id": 840024,
      //       "height": "180.0",
      //       "bmi": "55.56"
      //     },
      //     "chest": {
      //       "fev_one_dot_zero_fvc": "71",
      //       "svc": "",
      //       "fvc": "",
      //       "fev_one_dot_zero_pred": "",
      //       "check_date": "2024-05-27 13:31:08",
      //       "fev_one_dot_zero_perc_pred": "",
      //       "fev_one_dot_zero_fvc_perc_pred": "",
      //       "fev_one_dot_zero_fvc_pred": "",
      //       "id": 71349349,
      //       "fvc_pred": "",
      //       "fvc_perc_pred": "79",
      //       "fev_one_dot_zero": ""
      //     },
      //     "as": [],
      //     "report_time": "2024-06-25 16:38:28",
      //     "patient": {
      //       "birthday": "2024-04-16",
      //       "sex_name": "男",
      //       "patient_id": "1732953614713880584",
      //       "sex": 1,
      //       "name": "萝卜web22测试",
      //       "mobile": "15755056913",
      //       "hospital": "测试医院删中心（松江）",
      //       "department": "全科医疗科",
      //       "barcode": "930001356083",
      //       "age": "0岁"
      //     },
      //     "eye_examine": {
      //       "right_eye_dignosis": "",
      //       "check_date": "2024-05-27 13:30:43",
      //       "left_eye_dignosis": "",
      //       "id": 720021,
      //       "right_eye_picture": "https://saas-test-stg-pri.oss-cn-hangzhou.aliyuncs.com/test/live/20240527/fundus/fundus66541a886e1013.76372026/3f5460b06503c9a1ebde09888f5f1182.jpg?Expires=**********&OSSAccessKeyId=LTAI5tNCAp48gGeuzhz44bGQ&Signature=p%2BeOR08p3Ac90IRfifUdRgd6Pec%3D",
      //       "left_eye_picture": ""
      //     },
      //     "bp": {
      //       "sbp": 110,
      //       "dbp": 90,
      //       "abnormal_info": [
      //         {
      //           "status_display": "高血压1级",
      //           "deflection": 2,
      //           "code": "dbp",
      //           "indicator_label": [
      //             "60",
      //             "90",
      //             "100",
      //             "110"
      //           ],
      //           "abnormal_code": "dbp",
      //           "abnormal_status": 1,
      //           "status_display_group": "高血压1级",
      //           "normal_range": "60≤n＜90",
      //           "indicator_value": [
      //             "低血压",
      //             "正常",
      //             "高血压1级",
      //             "高血压2级",
      //             "高血压3级"
      //           ]
      //         },
      //         {
      //           "status_display": "正常",
      //           "deflection": 0,
      //           "code": "sbp",
      //           "indicator_label": [
      //             "90",
      //             "140",
      //             "160",
      //             "180"
      //           ],
      //           "abnormal_code": "sbp",
      //           "normal_range": "90≤n＜140",
      //           "indicator_value": [
      //             "低血压",
      //             "正常",
      //             "高血压1级",
      //             "高血压2级",
      //             "高血压3级"
      //           ]
      //         },
      //         {
      //           "status_display": "正常",
      //           "deflection": 0,
      //           "code": "pulse",
      //           "indicator_label": [
      //             "60",
      //             "100"
      //           ],
      //           "abnormal_code": "pulse",
      //           "normal_range": "60≤n≤100",
      //           "indicator_value": [
      //             "心动过缓",
      //             "正常",
      //             "心动过速"
      //           ]
      //         }
      //       ],
      //       "check_date": "2024-05-27 13:30:17",
      //       "pulse": 100,
      //       "id": 187009625
      //     }
      //   }
      // }

      // deflection  指标偏向：0 正常，1 偏低，2 偏高，3 低危，4 高危

      if (res.code == 0) {
        this.status = true;
        this.personInfo = res.data;
        // 初始化list并按顺序加入判断数组
        this.list = [];
        if (!Array.isArray(res.data.height_weight)) {
          this.personInfo.height_weight.abnormal_info_bmi = {}
          if (this.personInfo.height_weight.abnormal_info && this.personInfo.height_weight.abnormal_info.length) {
            this.personInfo.height_weight.abnormal_info.forEach(v => {
              this.personInfo.height_weight[`abnormal_info_${v.code}`] = JSON.parse(JSON.stringify(v))
            })
          }
          this.list.push("height_weight");
        }
        if (!Array.isArray(res.data.bp)) {
          this.personInfo.bp.abnormal_info_sbp = {}
          this.personInfo.bp.abnormal_info_dbp = {}
          if (this.personInfo.bp.abnormal_info && this.personInfo.bp.abnormal_info.length) {
            this.personInfo.bp.abnormal_info.forEach(v => {
              this.personInfo.bp[`abnormal_info_${v.code}`] = JSON.parse(JSON.stringify(v))
            })
          }
          this.list.push("bp");
        }
        if (!Array.isArray(res.data.eye_examine)) {
          this.list.push("eye_examine");
        }
        if (!Array.isArray(res.data.chest)) {
          this.list.push("chest");
        }
        this.$nextTick(() => {
          this.setBarCode(this.personInfo.patient.barcode);
        });
        // 极端情况不获取条形码避免barcode没用到报错
        // if (this.list.length != 0) {
        //   this.$nextTick(() => {
        //     this.setBarCode(this.personInfo.patient.barcode);
        //   });
        // }
        // 获取生日
        // this.personAge = this.getAge(res.data.patient.birthday.split("-"));
        // PWV图片获取
        if (res.data.as.material_url && res.data.as.material_url.length != 0) {
          res.data.as.material_url.forEach((item) => {
            if (item.file_name.includes("_02.")) {
              this.materialImg = item.report_url;
            }
          });
        } else {
          this.materialImg = "";
        }
      } else {
        this.$toast(res.msg);
      }
    },
    // 根据生日获取年龄
    getAge(birthday) {
      // 新建日期对象
      let date = new Date();
      // 今天日期，数组，同 birthday
      let today = [date.getFullYear(), date.getMonth() + 1, date.getDate()];
      // 分别计算年月日差值
      let age = today.map((value, index) => {
        return value - birthday[index];
      });
      // 当天数为负数时，月减 1，天数加上月总天数
      if (age[2] < 0) {
        // 简单获取上个月总天数的方法，不会错
        let lastMonth = new Date(today[0], today[1], 0);
        age[1]--;
        age[2] += lastMonth.getDate();
      }
      // 当月数为负数时，年减 1，月数加上 12
      if (age[1] < 0) {
        age[0]--;
        age[1] += 12;
      }
      return age[0];
    },
    // 生成BarCode
    setBarCode(barcode) {
      // console.log(barcode);
      JsBarcode("#svgcode", barcode, {
        format: "CODE128", //选择要使用的条形码类型
        // lineColor: "#000",
        // background: "#EBEEF5",
        // width: 1,
        // height: 30,
        fontSize: 20, //设置文本的大小
        displayValue: true,
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.content {
  background: #ffffff;
  font-size: 14px;
  color: #000000;
  .flexContent {
    z-index: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    .pageHeader {
      width: 355px;
      .logo-title {
        height: 35px;
        line-height: 35px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .logo {
          width: 50px;
        }
        .svgcode {
          width: 50px;
          // height: 30px;
        }
      }
      .person-info {
        display: flex;
        justify-content: space-between;
        font-size: 6px;
        height: 16px;
        line-height: 16px;
      }
    }
    .pageFooter {
      width: 355px;
      display: flex;
      justify-content: space-between;
      font-size: 5px;
      color: #666666;
      .printTime {
        margin-left: 5px;
      }
    }
    .noTest {
      margin-top: 20px;
    }
    .bodyIndex {
      position: relative;
      .backgroundImg {
        width: 355px;
        // height: 220px;
      }
      .materialImg {
        // width: 343px;
        width: 311px;
        // -webkit-transform: rotate(180deg);
        // -moz-transform: rotate(180deg);
        // -o-transform: rotate(180deg);
        // -ms-transform: rotate(180deg);
        // transform: rotate(180deg);
      }
      .postionText {
        z-index: 10;
        font-family: D-DINExp-Bold, D-DINExp;
        font-size: 14px;
        // 体质指数测量定位
        .bodyClass {
          position: absolute;
          width: 35px;
          top: 15.5%;
          left: 4.1%;
        }
        .height {
          position: absolute;
          font-weight: bold;
          top: 12%;
          right: 62%;
        }
        .weight {
          position: absolute;
          font-weight: bold;
          top: 25%;
          right: 62%;
        }
        .BMI {
          position: absolute;
          font-weight: bold;
          top: 37%;
          right: 62%;
        }
        .error {
          position: absolute;
          width: 6px;
          top: 38%;
          right: 59.5%;
        }
        .bodyCircle {
          position: absolute;
          background-color: #6f2a27;
          width: 2.5px;
          height: 2.5px;
          border-radius: 50%;
          border: 0.5px solid #ffffff;
        }
        .lowBodyPostion {
          bottom: 36.5%;
          left: 9%;
        }
        .normalBodyPostion {
          bottom: 36.5%;
          left: 23%;
        }
        .overBodyPostion {
          bottom: 36.5%;
          left: 38%;
        }
        .fatBodyPostion {
          bottom: 36.5%;
          left: 52%;
        }
        .height_weight_label{
          width: 200px;
          height: 14px;
          position: absolute;
          bottom: 7.5%;
          left: 6px;
          // background: #ccc;
          .height_weight_label_item{
            position: absolute;
            bottom: 0;
            left: -20px;
            color: #fff;
            font-size: 12px;
            width: 80px;
            height: 14px;
            line-height: 14px;
            padding: 0.3% 0 0 11px;
            transform: scale(0.5);
            background: #bbb;
            border-radius: 30px;
            z-index: 11;
            &::before{
              content: '';
              display: block;
              width: 8px;
              height: 8px;
              background: #fff;
              border-radius: 50px;
              position: absolute;
              left: 3px;
              top: 3px;
              z-index: 12;
            }
          }
          .normalBMI {
            left: 28.5px !important;
          }
          .overloadBMI {
            width: 81.5px;
            left: 76.5px !important;
          }
          .fatBMI {
            width: 85.5px;
            left: 125px !important;
          }
          .bodyheight_weight_act{
            width: 9px;
            height: 9px;
            position: absolute;
            bottom: 3px;
            z-index: -1;
          }
        }

        .height_weight_label_abnormal0{
          .lowBMI {
            background: #ca2200 !important;
          }
          .bodyheight_weight_act{
            left: 2px;
            z-index: 13;
          }
        }

        .height_weight_label_abnormal1{
          .normalBMI {
            background: #ca2200 !important;
          }
          .bodyheight_weight_act{
            left: 51px !important;
            z-index: 13;
          }
        }

        .height_weight_label_abnormal2{
          .overloadBMI {
            background: #ca2200 !important;
          }
          .bodyheight_weight_act{
            left: 99.5px !important;
            z-index: 13;
          }
        }

        .height_weight_label_abnormal3{
          .fatBMI {
            background: #ca2200 !important;
          }
          .bodyheight_weight_act{
            left: 148.2px;
            z-index: 13;
          }
        }

        .height_weight_indicator{
          width: 198px;
          height: 18px;
          position: absolute;
          bottom: 36%;
          left: 6px;
          z-index: 1;
          font-size: 12px;
          color: #fff;
          display: flex;
          justify-content: space-between;
          align-items: center;
          .height_weight_indicator_item{
            width: 50px;
            // transform: scale(0.55);
            position: relative;
            display: flex;
            justify-content: center;
            flex-direction: column;
            align-items: center;

            .height_weight_indicator_item_value{
              white-space: nowrap;
              font-size: 7.5px;
              margin-top: 2px;
            }
            .height_weight_indicator_item_label{
              position: absolute;
              right: -8px;
              top: 3px;
              background: #fff;
              color: #000;
              font-weight: 500;
              font-size: 6px;
              border-radius: 30px;
              padding: 0 3px;
              // width: 6px;
              height: 6px;
              line-height: 6px;
            }
            .height_weight_indicator_item_abnormal{
              // margin-top: 5px;
              width: 7px;
              height: 7px;
              border: 1px solid rgba(0,0,0,0);
              background: rgba(0,0,0,0);
              border-radius: 50px;
              transform: scale(0.5);
            }
          }
          .height_weight_indicator_item0{
            .height_weight_indicator_item_label{
              right: -12px;
            }
          }
        }

        .height_weight_indicator_item_act0{
          .height_weight_indicator_item_abnormal{
            border: 1px solid #fff !important;
            background: #6f2a27 !important;
          }
        }

        // 高血压定位
        .sbp_indicator_length5{
          width: 210px;
          height: 18px;
          position: absolute;
          bottom: 43.5%;
          left: 6px;
          z-index: 1;
          font-size: 12px;
          color: #fff;
          display: flex;
          justify-content: flex-start;
          align-items: center;
          // background: #ccc;

          .sbp_indicator_item{
            width: 50px;
            // transform: scale(0.55);
            position: relative;
            display: flex;
            justify-content: center;
            flex-direction: column;
            align-items: center;

            .sbp_indicator_item_value{
              font-size: 6px;
              margin-top: 2px;
            }
            .sbp_indicator_item_label{
              position: absolute;
              right: -7px;
              top: 2px;
              background: #fff;
              color: #000;
              font-weight: 500;
              font-size: 6px;
              border-radius: 30px;
              padding: 0 1.5px;
              // width: 6px;
              height: 6px;
              line-height: 6px;
              // transform: scale(0.5);
            }
            .sbp_indicator_item_abnormal{
              // margin-top: 4px;
              width: 7px;
              height: 7px;
              border: 1px solid rgba(0,0,0,0);
              background: rgba(0,0,0,0);
              border-radius: 50px;
              transform: scale(0.5);
            }
          }

          .sbp_indicator_item0{
            width: 40px;
            .sbp_indicator_item_label{
              right: -3px;
            }
          }

          .sbp_indicator_item1{
            width: 28px;
            .sbp_indicator_item_label{
              right: -10px;
            }
          }

          .sbp_indicator_item2{
            width: 50px;
            .sbp_indicator_item_label{
              right: -8px;
            }
          }

          .sbp_indicator_item3{
            width: 50px;
            .sbp_indicator_item_label{
              right: -6px;
            }
          }

          .sbp_indicator_item4{
            flex: 1;
          }
        }

        .sbp_indicator_length3{
          width: 176px;
          height: 18px;
          position: absolute;
          bottom: 43.5%;
          left: 6px;
          z-index: 1;
          font-size: 12px;
          color: #fff;
          display: flex;
          justify-content: flex-start;
          align-items: center;
          // background: #ccc;

          .sbp_indicator_item{
            width: 55px;
            // transform: scale(0.55);
            position: relative;
            display: flex;
            justify-content: center;
            flex-direction: column;
            align-items: center;

            .sbp_indicator_item_value{
              font-size: 6px;
              margin-top: 2px;
            }
            .sbp_indicator_item_label{
              position: absolute;
              right: -8px;
              top: 2px;
              background: #fff;
              color: #000;
              font-weight: 500;
              font-size: 6px;
              border-radius: 30px;
              padding: 0 1.5px;
              // width: 6px;
              height: 6px;
              line-height: 6px;
              // transform: scale(0.5);
            }
            .sbp_indicator_item_abnormal{
              // margin-top: 4px;
              width: 7px;
              height: 7px;
              border: 1px solid rgba(0,0,0,0);
              background: rgba(0,0,0,0);
              border-radius: 50px;
              transform: scale(0.5);
            }
          }
        }


        .sbp_indicator_item_act0{
          .sbp_indicator_item_abnormal{
            border: 1px solid #fff !important;
            background: #6f2a27 !important;
          }
        }

        .dbp_indicator_length5{
          width: 210px;
          height: 18px;
          position: absolute;
          bottom: 27.5%;
          left: 6px;
          z-index: 1;
          font-size: 12px;
          color: #fff;
          display: flex;
          justify-content: flex-start;
          align-items: center;
          .dbp_indicator_item{
            width: 50px;
            // transform: scale(0.55);
            position: relative;
            display: flex;
            justify-content: center;
            flex-direction: column;
            align-items: center;

            .dbp_indicator_item_value{
              font-size: 6px;
              margin-top: 2px;
            }
            .dbp_indicator_item_label{
              position: absolute;
              right: -7px;
              top: 2px;
              background: #fff;
              color: #000;
              font-weight: 500;
              font-size: 6px;
              border-radius: 30px;
              padding: 0 1.5px;
              // width: 6px;
              height: 6px;
              line-height: 6px;
            }
            .dbp_indicator_item_abnormal{
              width: 7px;
              height: 7px;
              border: 1px solid rgba(0,0,0,0);
              background: rgba(0,0,0,0);
              border-radius: 50px;
              transform: scale(0.5);
            }
          }
          .dbp_indicator_item0{
            width: 40px;
            .dbp_indicator_item_label{
              right: -3px;
            }
          }
          .dbp_indicator_item1{
            width: 28px;
            .dbp_indicator_item_label{
              right: -8px;
            }
          }
          .dbp_indicator_item2{
            width: 50px;
            .dbp_indicator_item_label{
              right: -7.5px;
            }
          }
          .dbp_indicator_item3{
            width: 50px;
            .dbp_indicator_item_label{
              right: -5px;
            }
          }
          .dbp_indicator_item4{
            flex: 1;
          }
        }
        .dbp_indicator_length3{
          width: 176px;
          height: 18px;
          position: absolute;
          bottom: 27.5%;
          left: 6px;
          z-index: 1;
          font-size: 12px;
          color: #fff;
          display: flex;
          justify-content: flex-start;
          align-items: center;
          .dbp_indicator_item{
            width: 55px;
            // transform: scale(0.55);
            position: relative;
            display: flex;
            justify-content: center;
            flex-direction: column;
            align-items: center;

            .dbp_indicator_item_value{
              font-size: 6px;
              margin-top: 2px;
            }
            .dbp_indicator_item_label{
              position: absolute;
              right: -8px;
              top: 2px;
              background: #fff;
              color: #000;
              font-weight: 500;
              font-size: 6px;
              border-radius: 30px;
              padding: 0 1.5px;
              // width: 6px;
              height: 6px;
              line-height: 6px;
            }
            .dbp_indicator_item_abnormal{
              width: 7px;
              height: 7px;
              border: 1px solid rgba(0,0,0,0);
              background: rgba(0,0,0,0);
              border-radius: 50px;
              transform: scale(0.5);
            }
          }
        }


        .dbp_indicator_item_act0{
          .dbp_indicator_item_abnormal{
            border: 1px solid #fff !important;
            background: #6f2a27 !important;
          }
        }

        .systolic {
          position: absolute;
          font-weight: bold;
          top: 11%;
          right: 79%;
        }
        .stretch {
          position: absolute;
          font-weight: bold;
          top: 22%;
          right: 79%;
        }
        .heartRate {
          position: absolute;
          font-weight: bold;
          top: 33.5%;
          right: 79%;
        }
        .systolicWarn {
          position: absolute;
          width: 6px;
          top: 12%;
          right: 77%;
        }
        .stretchWarn {
          position: absolute;
          width: 6px;
          top: 23%;
          right: 77%;
        }
        .systolicCircle {
          position: absolute;
          background-color: #6f2a27;
          width: 3px;
          height: 3px;
          border-radius: 50%;
          border: 0.5px solid #ffffff;
        }
        .lowStolicPostion {
          bottom: 44.5%;
          left: 8.8%;
        }
        .normalStolicPostion {
          bottom: 44.5%;
          left: 21.5%;
        }
        .overStolicPostion {
          bottom: 44.5%;
          left: 33%;
        }
        .highStolicPostion {
          bottom: 44.5%;
          left: 44.2%;
        }
        .stretchCircle {
          position: absolute;
          background-color: #6f2a27;
          width: 3px;
          height: 3px;
          border-radius: 50%;
          border: 0.5px solid #ffffff;
        }
        .lowStretchPostion {
          bottom: 29%;
          left: 8.8%;
        }
        .normalStretchPostion {
          bottom: 29%;
          left: 21.5%;
        }
        .overStretchPostion {
          bottom: 29%;
          left: 33%;
        }
        .highStretchPostion {
          bottom: 29%;
          left: 44.2%;
        }
        // 糖尿病视网膜定位
        .lefteye {
          position: absolute;
          left: 3%;
          top: 10%;
          height: 97.5px;
        }
        .righteye {
          position: absolute;
          left: 52%;
          top: 10%;
          height: 97.5px;
        }
        .lefteyeText {
          position: absolute;
          left: 3%;
          top: 10%;
          width: 25px;
        }
        .righteyeText {
          position: absolute;
          left: 52%;
          top: 10%;
          width: 25px;
        }
        .lefteyeDes {
          text-align: left;
          position: absolute;
          width: 140px;
          top: 54%;
          left: 3%;
          font-size: 8px;
          strong {
            font-weight: bolder;
          }
          span {
            font-size: 6px;
          }
        }
        .righteyeDes {
          text-align: left;
          position: absolute;
          width: 140px;
          top: 54%;
          left: 52%;
          font-size: 8px;
          strong {
            font-weight: bolder;
          }
          span {
            font-size: 6px;
          }
        }
        // 肺功能筛查
        .table {
          position: absolute;
          top: 14.1%;
          // width: 343px;
          margin-left: 10.5px;
          height: 75px;
          font-size: 9px;
          border: 0.5px solid #dbf1f4;
          tr {
            border: none;
            td {
              border: 0.5px solid #dbf1f4;
              border-top: none;
              border-bottom: none;
              vertical-align: middle;
              text-align: center;
              span {
                color: #666666;
              }
            }
            td:nth-child(1) {
              width: 108px;
            }
            td:nth-child(2) {
              width: 30px;
              color: #999999;
            }
            td:nth-child(3) {
              width: 45px;
            }
            td:nth-child(4) {
              width: 38px;
            }
            td:nth-child(5) {
              width: 67px;
            }
            td:nth-child(6) {
              width: 41.5px;
            }
          }
          .bolder {
            font-weight: bolder;
            .tableWarn {
              position: absolute;
              width: 6px;
              margin-left: 2px;
              margin-top: 0.5px;
            }
          }
        }
        .charts {
          position: absolute;
          bottom: 6.4%;
          left: 6.9%;
          width: 131.5px;
          // height: 99px;
          .top {
            display: flex;
            .leftCharts {
              position: relative;
              width: 60.5px;
              // height: 55.5px;
              height: 53px;
              // background: #ccc;
            }
            .rightCharts {
              position: relative;
              // width: 68.5px;
              width: 65.5px;
              // height: 55.5px;
              height: 53px;
              margin-left: 1px;
              // background: #f0f0f0;
            }
          }
          .bottom {
            display: flex;
            .leftCharts {
              position: relative;
              width: 60.5px;
              height: 52px;
              // background: #333;
              margin: 1px 1px 0 0;
            }
            .rightCharts {
              position: relative;
              // width: 68.5px;
              width: 65.5px;
              height: 52px;
              margin: 1px 0 0 0;
              // background: #666;
            }
          }
          .postionCircle {
            position: absolute;
            background-color: #6f2a27;
            width: 3px;
            height: 3px;
            border-radius: 50%;
            border: 0.5px solid #ffffff;
          }
        }
      }
    }
  }
  .break {
    page-break-after: always;
  }
  .lastFooter {
    // height: 241.5px;
    margin-top: 234.5px;
  }
}
@page {
  size: auto; /* auto is the initial value */
  margin: 3mm; /* this affects the margin in the printer settings */
}

html {
  background-color: #ffffff;
  margin: 0px; /* this affects the margin on the html before sending to printer */
}

body {
  border: solid 0px blue;
  margin: 10mm 15mm 10mm 15mm; /* margin you want for the content */
}
</style>
