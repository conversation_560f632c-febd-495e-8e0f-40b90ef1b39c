<template>
    <div class="content">
       <div class="title">
         <img class="img" src="./images/1hang.png" />
         <span class="word">家庭消毒液的正确用法</span>
         <div class="btn">
           <p >
             <a id="iPhone" href="https://itunes.apple.com/us/app/mmc管家/id1198430389?l=zh&amp;ls=1&amp;mt=8"></a>
             <a id="Android" href="http://download.zz-med.com/Mmc_Mobile_Android_Application/new/MMCUser_Android_Application.apk"></a>
           </p>
           <img src="./images/go.gif">
           <!-- <p>
             <a id="iPhones" href="ZhiZhongApp://"><img class="img" src="./images/dakai.png" /></a>
             <a id="Androids" href="guanjia://jp.app/openwith"><img class="img" src="./images/dakai.png" /></a>
           </p> -->
         </div>
        </div>
    </div>
</template>

<script>

    export default {
        data() {
            return {

            }
        },
        created() {
          this.$nextTick(() => {
            this.init()
          })
        },
        methods: {
          init() {
            // 判断安卓或苹果
            var userAgent = navigator.userAgent
            var isAndroid = userAgent.indexOf('Android') > -1 || userAgent.indexOf('Linux') > -1
            var isIOS = !!userAgent.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)
            if (isAndroid) {
              document.getElementById('iPhone').style.display = 'none'
              // document.getElementById('iPhones').style.display = 'none'
            }
            if (isIOS) {
              document.getElementById('Android').style.display = 'none'
              // document.getElementById('Androids').style.display = 'none'
            }
          }
        }
    }
</script>

<style lang="scss" scoped>
  .content{
    width: 100%;
    height: 100%;
    display: flex;
    // justify-content:center;
    // align-items:center;
    background:rgba(252,214,183,1);
    .title{
      position: relative;
      .img{
          width: 100%;
          height: 100%;
      }
      .word{
        display: block;
        position: absolute;
        width: 100%;
        top: 110px;
        font-size:30px;
        font-weight:500;
        color:rgba(221,59,12,1);
        line-height:47px;
      }
      .btn{
        width:327px;
        height: 101px;
        background: url('./images/anniu.png') no-repeat;
        background-size: cover;
        position: absolute;
        left: 25px;
        top: 176px;
        p{
            width: 100%;
            height: 100%;
            a{
                float: left;
                width: 100%;
                height: 100%;
            }
        }
        img{
            width: 79px;
            height: 49px;
            position: absolute;
            right: 0;
            bottom: -12px;
        }
      }
    }
  }
</style>
