.content{
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    align-items: center;
    background:rgba(252,214,183,1);
    .title{
        width: 300px;
        height: 39px;
        margin-top: 10px;
        margin-bottom: 8px;
        img{
            width: 100%;
            height: 100%;
        }
    }
    .title1{
        width: 331px;
        height: 64px;
        line-height: 64px;
        font-size:22px;
        font-weight:600;
        color:rgba(255,255,255,1);
        background: url('./images/second-title1.png') no-repeat;
        background-size: cover;
    }
    .title2{
        width: 307px;
        height: 64px;
        line-height: 64px;
        font-size:22px;
        font-weight:600;
        color:rgba(255,255,255,1);
        background: url('./images/second-title2.png') no-repeat;
        background-size: cover;
    }
    .direct{
        width: 345px;
        height: 396px;
        border-radius:5px;
        background: url(./images/export.png) no-repeat;
        background-size: cover;
        margin-top: 7px;
        position: relative;
        .time{
            width:310px;
            height:32px;
            font-size:22px;
            font-weight:600;
            color:#74491c;
            line-height:32px;
            margin: 38px 0 0 20px;
            text-align: left;
            i{
                color:#D0021B;
                font-style: normal;
                font-weight:600;
            }
        }
        p{
            width:310px;
            height:32px;
            font-size:22px;
            font-weight:600;
            color:#74491c;
            line-height:32px;
            margin: 0 0 0 22px;
            text-align: left;
            i{
                color:#D0021B; 
                font-style: normal;
                font-weight:600;
            }
        }
        span{
            width:220px;
            height:34px;
            display: block;
            background:linear-gradient(180deg,rgba(255,254,132,1) 0%,rgba(255,176,52,1) 100%);
            box-shadow:0px 1px 8px 0px rgba(174,103,8,0.15);
            border-radius:17px;
            font-size:16px;
            font-weight:600;
            color:rgba(232,78,38,1);
            line-height:34px;
            position: absolute;
            left: 70px;
            bottom: 16px;
            a{
                display: block;
                font-size:16px;
                font-weight:600;
                color:rgba(232,78,38,1);
            }
        }
    }
    .tab{
        width: 345px;
        height: 197px;
        border-radius:5px;
        background: url(./images/card2.png) no-repeat;
        background-size: cover;
        margin-top: 15px;
        margin-bottom: 10px;
        position: relative;
        p{
            width: 250px;
            height:28px;
            font-size:20px;
            font-weight:600;
            text-align: left;
            color:rgba(213,70,20,1);
            line-height:28px;
            margin: 46px 0 0 20px;
        }
        div{
            width:199px;
            height:48px;
            font-size:17px;
            font-weight:600;
            color:rgba(116,73,28,1);
            line-height:24px;
            text-align: left;
            margin-left: 20px;
            margin-top: 3px;
        }
        span{
            display: block;
            width: 220px;
            height: 34px; 
            background:linear-gradient(180deg,rgba(255,254,132,1) 0%,rgba(255,176,52,1) 100%);
            box-shadow:0px 1px 8px 0px rgba(174,103,8,0.15);
            border-radius:17px;
            margin: 29px 0 0 66px;
            font-size:16px;
            font-weight:600;
            color:rgba(232,78,38,1);
            line-height:34px;
            text-align: center;
        }
    }
    .edit{
        width:365px;
        height:230px;
        background: url(./images/end.png) no-repeat;
        background-size: cover;
        border-radius:5px;
        margin-bottom: 15px;
        .top{
            margin-top: 40px;
            p{
                width:100%;
                text-align: center;
                font-size:16px;
                font-weight:400;
                color:rgba(116,73,28,1);
                line-height:21px;
                i{
                    font-size:20px;
                    font-weight: 600;
                    font-style: normal;
                }
            }
            .first{
                margin-top: 20px;
                margin-bottom: 16px;
            }
            .prevRoster{
                width:194px;
                height:28px;
                line-height:28px;
                font-size:16px;
                font-weight:600;
                color:rgba(232,78,38,1);
                margin-top: 6px;
                margin-left: 85px;
                box-shadow:0px 1px 8px 0px rgba(174,103,8,0.15);
                border-radius:14px;
                border:1px solid rgba(232,78,38,1);
                a{
                    color:rgba(232,78,38,1);
                }
            }
        }
        .center{
            .left{
                width: 210px;
                float: left;
                font-size:24px;
                font-weight:600;
                margin:72px 0 0 17px;
                div{
                    width: 230px;
                    height:22px;
                    font-size:20px;
                    font-weight:600;
                    color:rgba(116,73,28,1);
                    line-height:22px;
                    background: url(./images/finish.png) no-repeat 16px center;
                    background-size: 25px 22px;
                    float: left;
                }
                p{
                    width: 200px;
                    font-size:17px;
                    font-weight:400;
                    color:rgba(102,102,102,1);
                    line-height:37px;
                    float: left;
                    margin-left:20px;
                    margin-top: 7px;
                    text-align: left;
                }
            }
            .jionImg{
                width: 200px;
                height: 67px;
                font-size:24px;
                float: left;
                font-weight:600;
                margin:72px 0 0 17px;
            }
            .comeOn{
                width: 118px;
                height: 106px;
                float: right;
                margin-top: 26px;
                margin-right: 10px;
            }
        }
        .bottom{
            margin-top: 50px;
            p{
                height: 43px;
                span{
                    width: 150px;
                    height: 43px;
                    float: left;
                    background:linear-gradient(180deg,rgba(255,254,132,1) 0%,rgba(255,176,52,1) 100%);
                    box-shadow:0px 1px 6px 0px rgba(174,103,8,0.15);
                    border-radius:22px;
                    font-size:19px;
                    font-weight:600;
                    color:rgba(232,78,38,1);
                    line-height:43px;
                    margin-left: 112px;
                    a{
                        color:rgba(232,78,38,1);
                        font-weight:600;
                    }
                }
                span:nth-child(1){
                    margin-right: 15px;
                }
            }
        }
    }
    .edits{
        width:358px;
        height: 529px;
        background: url('./images/red-bag3.png') no-repeat;
        background-size: cover;
        border-radius:5px;
        margin-bottom: 15px;
        position: relative;
        .topAgo{
            p{
                width:100%;
                text-align: center;
                font-size:16px;
                font-weight:400;
                color:rgba(116,73,28,1);
                line-height:21px;
                i{
                    font-size:20px;
                    font-weight: 600;
                    font-style: normal;
                }
            }
            .first{
                margin-top: 20px;
                margin-bottom: 16px;
            }
            .prevRoster{
                width:194px;
                height:28px;
                line-height:28px;
                font-size:16px;
                font-weight:600;
                color:rgba(232,78,38,1);
                margin-top: 6px;
                margin-left: 85px;
                box-shadow:0px 1px 8px 0px rgba(174,103,8,0.15);
                border-radius:14px;
                border:1px solid rgba(232,78,38,1);
                a{
                    color:rgba(232,78,38,1);
                }
            }
        }
        .top{
            width: 345px;
            background:rgba(255,255,255,1);
            border-radius:5px;
            margin: 0 auto;
            margin-top: 15px;
            padding-bottom: 15px;
            .result{
                width: 100%;
                height: 241px;
                background: url('./images/gongxi-bg.png') no-repeat #fff;
                background-size: 100% 241px;
                position: relative;
                .all{
                    width: 100%;
                    height:25px;
                    font-size:18px;
                    font-weight:400;
                    color:rgba(255,255,255,1);
                    line-height:25px;
                    position: absolute;
                    top: 115px;
                    i{
                        font-size:22px;
                        font-weight:bold;
                        font-style: normal;
                    }
                }
                .alls{
                    display: block;
                    width: 100%;
                    height:26px;
                    font-size:17px;
                    font-weight:400;
                    color:rgba(255,255,255,1);
                    line-height:24px;
                    position: absolute;
                    top: 146px;
                    text-align: center;
                }
            }
            .writeAddress{
                width: 230px;
                padding-left: 30px;
                height: 45px;
                line-height: 45px;
                margin: 0 auto;
                margin-top:10px;
                background: #F97344;
                box-shadow:0px 5px 8px 0px rgba(249,115,68,0.5);
                border-radius:23px;
                font-size:18px;
                font-weight:600;
                color:rgba(253,247,233,1);
                position: relative;
                img{
                    position: absolute;
                    width: 114px;
                    height: 89px;
                    left: -40px;
                    top: -36px;
                }
            }
            .checkAddress{
                width: 260px;
                height: 45px;
                line-height: 45px;
                margin: 0 auto;
                margin-top:10px;
                background:#F97344;
                box-shadow:0px 5px 8px 0px rgba(249,115,68,0.5);
                border-radius:23px;
                font-size:18px;
                font-weight:600;
                color:rgba(253,247,233,1);
            }
            .waiting{
                height: 26px;
                line-height: 26px;
                font-size:16px;
                text-align: center;
                font-weight:400;
                color:rgba(116,73,28,1);
            }
        }
        .bottom{
            position: absolute;
            bottom: 20px;
            left: 112px;
            p{
                height: 43px;
                span{
                    width: 150px;
                    height: 43px;
                    float: left;
                    background:linear-gradient(180deg,rgba(255,254,132,1) 0%,rgba(255,176,52,1) 100%);
                    box-shadow:0px 1px 6px 0px rgba(174,103,8,0.15);
                    border-radius:22px;
                    font-size:19px;
                    font-weight:600;
                    color:rgba(232,78,38,1);
                    line-height:43px;
                    a{
                        color:rgba(232,78,38,1);
                        font-weight:600;
                    }
                }
            }
        }
    }
    
    .tab1{
        width:345px;
        height:112px;
        box-shadow:0px 2px 10px 0px rgba(174,103,8,0.4);
        border-radius:5px;
        background: url(./images/kongtangying.png) no-repeat 221px center rgba(253,237,222,1);
        background-size: 112px 95px;
        margin-top: 14px;
        margin-bottom: 13px;
        position: relative;
        p{
            width: 112px;
            height: 28px;
            font-weight:500;
            color:rgba(253,237,222,1);
            line-height:28px;
            font-size:18px;
            text-shadow:0px 2px 10px rgba(174,103,8,0.4);
            background: url(./images/tab1.png) no-repeat left top;
            background-size: 100% 100%;
        }
        div{
            width: 206px;
            margin-left: 10px;
            margin-top: 10px;
            line-height: 28px;
            font-size:20px;
            font-weight:600;
            color:rgba(213,70,20,1);
            text-shadow:0px 2px 10px rgba(174,103,8,0.4);
            text-align: left;
        }
        span{
            display: block;
            width: 206px;
            height: 36px;
            margin-left: 10px;
            font-size:18px;
            font-weight:400;
            color:rgba(116,73,28,1);
            line-height:28px;
            text-shadow:0px 2px 10px rgba(174,103,8,0.4);
            text-align: left;
        }
    }
}
.mask{
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    z-index: 99;
    background:rgba(0,0,0,1);
    opacity:0.6;
    
}
.zhibo{
    width: 243px;
    height: 214px;
    position: fixed;
    z-index: 100;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    .bg{
        width: 243px;
        height: 140px;
        margin-bottom: 40px;
    }
    .bg1{
        width: 34px;
        height: 34px;
    }
}
.winningList{
    width:265px;
    height:349px;
    background:rgba(255,255,255,1);
    border-radius:5px;
    position: fixed;
    z-index: 100;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    .listTitle{
        width: 175px;
        height: 39px;
        line-height: 32px;
        text-align: center;
        position: absolute;
        left: 45px;
        top: -10px;
        font-size:18px;
        font-weight:500;
        color:rgba(255,255,255,1);
        background: url('./images/zhongjiang-bg.png') no-repeat;
        background-size: cover;
    }
    .list{
        width:250px;
        height:297px;
        overflow: auto;
        margin: 0 auto;
        margin-top: 30px;
        border: 1px solid pink;
        p{
            font-size:16px;
            font-weight:500;
            color:rgba(51,51,51,1);
            line-height:30px;
        }
    }
    .bg1{
        width: 34px;
        height: 34px;
        margin-top: 50px;
    }
}
.writes{
    width:265px;
    height:410px;
    padding-top: 26px;
    background:rgba(255,255,255,1);
    border-radius:5px;
    position: fixed;
    z-index: 100;
    left: 15%;
    top: 15%;
    .writeTitle{
        width: 51px;
        height: 51px;
        position: absolute;
        left: 107px;
        top: -25px;
        font-size:18px;
        font-weight:500;
        color:rgba(255,255,255,1);
        background: url('./images/message.png') no-repeat;
        background-size: cover;
    }
    .question{
        p{
            width: 100%;
            height: 30px;
            line-height:30px;
            font-size:18px;
            font-weight:500;
            color:rgba(51,51,51,1);
            background: url('./images/1.png') no-repeat 25px center;
            background-size: 26px 26px;
            img{
                width: 26px;
                height: 26px;
            }
        }
        .p2{
            background: url('./images/2.png') no-repeat 25px center;
            background-size: 26px 26px;
        }
        .p3{
            background: url('./images/3.png') no-repeat 25px center;
            background-size: 26px 26px;
        }
        .van-cell{
            width:215px;
            height:30px;
            padding: 2px 5px;
            margin: 0 auto;
            margin-bottom: 10px;
            margin-top: 7px;
            border-radius:5px;
            border:1px solid rgba(204,204,204,1);
        }
    }
    .sureSubmit{
        width:215px;
        height:32px;
        line-height: 32px;
        font-size:16px;
        font-weight:600;
        margin: 0 auto;
        margin-top: 20px;
        color:rgba(253,247,233,1);
        background:rgba(251,143,54,1);
        border-radius:5px;
    }
    .relation{
        width:245px;
        height:60px;
        padding: 8px 10px;
        margin-top: 20px;
        background:rgba(253,237,222,1);
        border-radius:5px;
        p{
            width:243px;
            font-size:14px;
            text-align: left;
            font-weight:400;
            color:rgba(116,73,28,1);
            line-height:20px;
            i{
                width:60px;
                height:18px;
                line-height: 18px;
                text-align: center;
                display: inline-block;
                border-radius:8px;
                font-size:12px;
                font-style: normal;
                font-weight:400;
                color:rgba(66,133,244,1);
                border:1px solid rgba(66,133,244,1);
                margin-left: 3px;
            }
        }
    }
    .bg1{
        width: 34px;
        height: 34px;
        margin-top: 20px;
    }
}
.submitSuccess{
    width:265px;
    height:330px;
    padding-top: 30px;
    background:rgba(255,255,255,1);
    border-radius:5px;
    position: fixed;
    z-index: 100;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    .writeTitle{
        width: 51px;
        height: 51px;
        position: absolute;
        left: 107px;
        top: -25px;
        font-size:18px;
        font-weight:500;
        color:rgba(255,255,255,1);
        background: url('./images/message.png') no-repeat;
        background-size: cover;
    }
    .question{
        img{
            width: 76px;
            height: 76px;
            margin: 25px auto;
        }
        p{
            width: 160px;
            height:30px;
            font-size:20px;
            font-weight:500;
            color:rgba(51,51,51,1);
            line-height:30px;
            margin: 0 auto;
            margin-bottom: 12px;
        }
        span{
            width: 160px;
            display: block;
            height:60px;
            margin: 0 auto;
            font-size:18px;
            font-weight:400;
            color:rgba(51,51,51,1);
            line-height:30px;
            margin-bottom: 20px;
        }
    }
    
    .relation{
        width:245px;
        height:60px;
        padding: 8px 10px;
        margin-top: 20px;
        background:rgba(253,237,222,1);
        border-radius:5px;
        p{
            width:243px;
            font-size:14px;
            text-align: left;
            font-weight:400;
            color:rgba(116,73,28,1);
            line-height:20px;
            i{
                width:60px;
                text-align: center;
                height:18px;
                line-height: 18px;
                display: inline-block;
                border-radius:8px;
                font-size:12px;
                font-style: normal;
                font-weight:400;
                color:rgba(66,133,244,1);
                border:1px solid rgba(66,133,244,1);
                margin-left: 3px;
            }
        }
    }
    .bg1{
        width: 34px;
        height: 34px;
        margin-top: 30px;
    }
}
.lookAddress{
    width:265px;
    min-height:290px;
    padding-top: 30px;
    background:rgba(255,255,255,1);
    border-radius:5px;
    position: fixed;
    z-index: 100;
    left: 15%;
    top: 15%;
    .writeTitle{
        width: 51px;
        height: 51px;
        position: absolute;
        left: 107px;
        top: -25px;
        font-size:18px;
        font-weight:500;
        color:rgba(255,255,255,1);
        background: url('./images/message.png') no-repeat;
        background-size: cover;
    }
    .question{
        margin-bottom: 20px;
        p{
            width: 233px;
            padding-left: 32px;
            text-align: left;
            height: 30px;
            line-height:30px;
            font-size:18px;
            font-weight:500;
            color:rgba(51,51,51,1);
        }
        span{
            width: 201px;
            display: block;
            text-align: left;
            line-height:22px;
            font-size:16px;
            font-weight:400;
            margin: 0 auto;
            color:rgba(51,51,51,1);
        }
    }
    .relation{
        width:245px;
        height:60px;
        float: left;
        padding: 8px 10px;
        background:rgba(253,237,222,1);
        border-radius:5px;
        p{
            width:243px;
            font-size:14px;
            text-align: left;
            font-weight:400;
            color:rgba(116,73,28,1);
            line-height:20px;
            i{
                width:60px;
                height:18px;
                line-height: 18px;
                text-align: center;
                display: inline-block;
                border-radius:8px;
                font-size:12px;
                font-style: normal;
                font-weight:400;
                color:rgba(66,133,244,1);
                border:1px solid rgba(66,133,244,1);
                margin-left: 3px;
            }
        }
    }
    .bg1{
        width: 34px;
        height: 34px;
        position: absolute;
        left: 44%;
        bottom: -60px;
    }
}
