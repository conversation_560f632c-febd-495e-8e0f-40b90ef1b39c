<template>
  <div class="wrapper">
    <div class="content">
      <div class="title"><img src="./images/title.png" /></div>
      <div class="title1">MMC专家来解惑</div>
      <div class="direct">
        <div class="time">在线对话<i>“内分泌专家”</i></div>
        <p>足不出户免费享受<i>“特需门诊”</i></p>
        <span><a href="https://sourl.cn/Fn2rRx">点击观看专家直播></a></span>
      </div>
      <div class="tab" @click="list">
        <p>专业居家控糖知识大汇总</p>
        <div>学习越多口罩获取几率越大哦</div>
        <span>进入学习控糖知识（{{readNum}}/12）</span>
      </div>
      <div class="title2">口罩限额领取</div>
      <!-- 参加活动 -->
      <div class="edit" v-show="drawStatus === 0">
        <div class="top">
          <p class="first">口罩赠送活动全部结束</p>
          <!-- <p>额外增加第四期<i>3月10日</i>发放1000个口罩</p> -->
          <div class="prevRoster"><a href="https://mp.weixin.qq.com/s/LSKLpcyBSrcen7Vh5pt99g">查看全部中奖名单></a></div>
        </div>
        <!-- <div class="center clearfix"> -->
          <!-- <img class="jionImg" src="./images/join.gif" @click="jionIn" /> -->
          <!-- <span class="jionImg">活动已经结束</span>
          <img class="comeOn" src="./images/fighting.png" />
        </div> -->
        <div class="bottom">
          <p>
            <span><a href="https://mp.weixin.qq.com/s/ZEq8cz7Cn3JrLaDJVDqOTQ">查看领取规则</a></span>
            <!-- <span @click="share">微信分享领更多</span> -->
          </p>
          <div></div>
        </div>
      </div>
      <!-- 参加活动 -->
      <div class="edit" v-show="drawStatus === 1 && is_bingo === 0">
        <div class="top">
          <p class="first">口罩赠送活动全部结束</p>
          <!-- <p>额外增加第四期<i>3月10日</i>发放1000个口罩</p> -->
          <div class="prevRoster"><a href="https://mp.weixin.qq.com/s/LSKLpcyBSrcen7Vh5pt99g">查看全部中奖名单></a></div>
        </div>
        <!-- <div class="center clearfix">
          <div class="left"> -->
            <!-- <div>您成功加入活动</div>
            <p>如果您中奖会短信通知</p> -->
            <!-- 活动已经结束
          </div>
          <img class="comeOn" src="./images/fighting.png" />
        </div> -->
        <div class="bottom">
          <p>
            <span><a href="https://mp.weixin.qq.com/s/ZEq8cz7Cn3JrLaDJVDqOTQ">查看领取规则</a></span>
            <!-- <span @click="share">微信分享领更多</span> -->
          </p>
          <div></div>
        </div>
      </div>
      <!-- 中奖填写地址 -->
      <div class="edits" v-show="drawStatus === 1 && is_bingo === 1">
        <div class="topAgo">
          <p class="first">口罩赠送活动全部结束</p>
          <!-- <p>额外增加第四期<i>3月10日</i>发放1000个口罩</p> -->
          <div class="prevRoster"><a href="https://mp.weixin.qq.com/s/LSKLpcyBSrcen7Vh5pt99g">查看全部中奖名单></a></div>
        </div>
        <div class="top">
          <div class="result">
            <p class="all">您一共可以领取<i>{{kouzhaoNum}}</i>个口罩</p>
            <span class="alls" v-show="shareNum != 0">其中有{{shareNum}}个通过邀请好友获得</span>
          </div>
          <div class="waiting" v-show="addressStatus != ''">口罩在路上啦，请注意查收</div>
          <div class="writeAddress" @click="write" v-show="addressStatus == ''">
            <img src="./images/gift-button.png" />
            填写收货地址，领口罩
          </div>
          <div class="checkAddress" @click="lookAddress" v-show="addressStatus != ''">查看收货地址</div>
        </div>
        <div class="bottom">
          <p>
            <span><a href="https://mp.weixin.qq.com/s/ZEq8cz7Cn3JrLaDJVDqOTQ">查看领取规则</a></span>
          </p>
        </div>
      </div>
      <!-- 控糖营暂时隐藏 -->
      <!-- <div class="tab1" @click="sugarKnowledge">
        <p>居家控糖营</p>
        <div>每周新开线上控糖营</div>
        <span>专家互动招满即止</span>
      </div> -->
    </div>
    <!-- 弹窗 -->
    <div class="mask" v-stop v-show = "remark"></div>
    <!-- 直播弹窗 -->
    <!-- <div class="zhibo" v-if="status == 1">
      <img class="bg" src="./images/tanchuang.png" />
      <img class="bg1" src="./images/<EMAIL>" @click="close" />
    </div> -->
    <!-- 控糖知识 -->
    <div class="zhibo" v-if="status == 2">
      <img class="bg" src="./images/tanchuang2.png" />
      <img class="bg1" src="./images/<EMAIL>" @click="close" />
    </div>
    <!-- 查看上期中奖名单 -->
    <div class="winningList" v-if="status == 1">
      <div class="listTitle">第一期中奖用户</div>
      <div class="list" >
        <p v-for="(item, index) in winningList" :key=index>恭喜手机号码：<span>{{item.cell}}</span></p>
      </div>
      <img class="bg1" src="./images/<EMAIL>" @click="close" />
    </div>
    <!-- 填写地址 -->
    <div class="writes" v-if="status == 3">
      <div class="writeTitle"></div>
      <div class="question">
        <p class="p1">填写收货人姓名</p>
        <van-field v-model="userName" placeholder="请输入姓名" />
      </div>
      <div class="question">
        <p class="p2">填写您的收货地址</p>
        <van-cell is-link @click="showPopup">{{chooseAddress == '' ? '请选择省/市/区' : chooseAddress}}</van-cell>
        <van-popup v-model="show" position="bottom">
          <van-area
            :area-list="areaList"
            :columns-placeholder="['请选择', '请选择', '请选择']"
            :value="chooseAddress"
            @confirm="onSave"
            @cancel="onDelete"
            @change="onChangeDetail"
          />
        </van-popup>
        <van-field v-model="userAddress" placeholder="请填写详细地址" />
      </div>
      <div class="question">
        <p class="p3">填写您的收货手机</p>
        <van-field v-model="userCell" type="tel" placeholder="请输入手机号码" />
      </div>
      <div class="sureSubmit" @click="sureSubmit">确定提交</div>
      <div class="relation">
        <p>如有任何配送问题，可以联系电话************,</p>
        <p>或添加微信：<span id="target">MMC8815</span>
          <i id="copyBtn" data-clipboard-action="copy" data-clipboard-target="#target" @click="copyPolicyNo">点击复制</i>
        </p>
      </div>
      <img class="bg1" src="./images/<EMAIL>" @click="close" />
    </div>
    <!-- 提交成功 -->
    <div class="submitSuccess" v-if="status == 4">
      <div class="writeTitle"></div>
      <div class="question">
        <img src="./images/get.png" />
        <p>收货地址提交成功</p>
        <span>我们会尽快寄出，请耐心等待。</span>
      </div>
      <div class="relation">
        <p>如有任何配送问题，可以联系电话************,</p>
        <p>或添加微信：<span id="target">MMC8815</span>
          <i id="copyBtn" data-clipboard-action="copy" data-clipboard-target="#target" @click="copyPolicyNo">点击复制</i>
        </p>
      </div>
      <img class="bg1" src="./images/<EMAIL>" @click="close" />
    </div>
    <!-- 查看地址 -->
    <div class="lookAddress" v-if="status == 5">
      <div class="writeTitle"></div>
      <div class="question">
        <p>收货人姓名</p>
        <span>{{checkName}}</span>
      </div>
      <div class="question">
        <p>您的收货地址</p>
        <span>{{checkAddress}}</span>
      </div>
      <div class="question">
        <p>您的收货手机</p>
        <span>{{checkCell}}</span>
      </div>
      <div class="relation">
        <p>如有任何配送问题，可以联系电话************,</p>
        <p>或添加微信：<span id="target">MMC8815</span>
          <i id="copyBtn" data-clipboard-action="copy" data-clipboard-target="#target" @click="copyPolicyNo">点击复制</i>
        </p>
      </div>
      <img class="bg1" src="./images/<EMAIL>" @click="close" />
    </div>
  </div>
</template>

<script>
  import Vue from 'vue'
  import { Toast } from 'vant'
  import { getUserInfo, submitUserInfo, joinActivity } from '@/api/mmcPopularize'
  import Clipboard from 'clipboard'
  import areaList from './../../../public/static/address.js'

  Vue.use(Toast)
  export default {
    data() {
      return {
        is_bingo: '',
        remark: false,
        phone: '',
        status: '',
        user_id: '',
        kouzhaoNum: '',
        shareNum: '',
        addressStatus: '',
        winningList: [],
        userName: '',
        userCell: '',
        userAddress: '',
        checkName: '',
        checkCell: '',
        checkAddress: '',
        areaList,
        searchResult: [],
        show: false,
        chooseAddress: '',
        readNum: '',
        drawStatus: ''
      }
    },
    created() {
      this.getUser()
    },
    watch: {
      $route(to, from) {
        if (to.name !== from.name) {
          this.getUser()
        }
      }
    },
    directives: {
      stop: {
        // 指令的定义
        bind: function (el) {
          el.addEventListener('touchmove', function (e) {
            e.preventDefault()
          })
        }
      }
    },
    methods: {
      // 弹出地址
      showPopup() {
        this.show = true
      },
      // 取消
      onDelete() {
        this.show = false
      },
      // 确认
      onSave (e) {
      // 确定选择,返回的必定是三元素数组
        console.log('this.onAddrConfirm=>e', e)
        if (e[0].name == '' || e[1].name == '' || e[2].name == '') {
          this.$toast('请选择完整地址')
          return false
        }
        if (e[0].name == e[1].name) {
          this.chooseAddress = e[1].name + e[2].name
        } else {
          this.chooseAddress = e[0].name + e[1].name + e[2].name
        }
        console.log(this.chooseAddress)
        this.show = false
      },
      // 改变地址
      onChangeDetail() {

      },
      // 复制
      copyPolicyNo() {
        let clipboard = new Clipboard('#copyBtn')
        clipboard.on('success', function(e) {
          this.$toast('复制成功', 1500)
          e.clearSelection()
        })
      },
      // // 查看中奖名单
      // lookList() {
      //   this.remark = true
      //   this.status = 1
      // },
      // 控糖知识弹窗
      sugarKnowledge() {
        this.remark = true
        this.status = 2
      },
      // 填写地址
      write() {
        this.remark = true
        this.status = 3
      },
      // 查看收货地址
      lookAddress() {
        this.remark = true
        this.status = 5
      },
      // 提交收货地址
      sureSubmit() {
        if (this.userName == '') {
          this.$toast('请填写姓名')
          return false
        }
        if (this.chooseAddress == '') {
          this.$toast('请选择省市区地址')
          return false
        }
        if (this.userAddress == '') {
          this.$toast('请填写详细收货地址')
          return false
        }
        if (this.userCell == '') {
          this.$toast('请填写手机号码')
          return false
        } else {
          let myreg = /^[1][0-9]{10}$/
          if (!myreg.test(this.userCell)) {
            this.$toast('手机号格式不正确')
            return false
          }
        }
        let datas = {
          receiver: this.userName,
          receiver_address: this.chooseAddress + this.userAddress,
          receiver_cell: this.userCell
        }
        let that = this
        submitUserInfo(datas).then((res) => {
          console.log(res)
          if (res.status == 0) {
            that.status = 4
          } else {
            this.$toast(res.msg)
          }
        })
      },
      // 微信分享
      share() {
        if (this.drawStatus == 0) {
          this.$toast('请先参加活动再分享')
          return false
        }
        let params = {
            title: '好友邀您加入MMC，共享福利关怀',
            description: '加入MMC标准化代谢性疾病管理中心，轻松两步参加福利活动。',
            url: 'https://patient-h5.zz-med.com/mask/wx/share?patientId=' + this.user_id
        }
        if (!window.postMessage) {
          return false
        }
        window.postMessage(JSON.stringify(params))
        window.android.actionFromJsWithParam(JSON.stringify(params))
        let res = window.webkit.messageHandlers['actionFromJsWithParam'].postMessage(JSON.stringify(params))
      },
      // 关闭
      close() {
        if (this.status == 4) {
          this.getUser()
        }
        if (this.status == 3) {
          this.userName = ''
          this.userCell = ''
          this.userAddress = ''
          this.chooseAddress = ''
        }
        this.remark = false
        this.status = ''
      },
      // 跳转文章页面
      list() {
        this.$router.push({
          name: 'Sugar.List',
          query: {}
        })
        // let that = this
        // setTimeout(() => {
        //   that.getUser()
        // }, 2000)
      },
      /**
       * 获取信息
       */
      getUser() {
        let that = this
        getUserInfo({}).then(function (res) {
          // console.log(res)
          if (res.status === 0) {
            that.phone = res.data.cell
            that.is_bingo = res.data.is_bingo
            that.user_id = res.data.user_id
            that.winningList = res.data.lastList
            that.kouzhaoNum = res.data.mask_num
            that.shareNum = res.data.invite_mask_num
            that.addressStatus = res.data.receiver_address
            that.checkName = res.data.receiver
            that.checkCell = res.data.receiver_cell
            that.checkAddress = res.data.receiver_address
            that.readNum = res.data.read_num
            that.drawStatus = res.data.is_join
          } else {
            this.$toast(res.msg)
          }
        })
      },
      jionIn() {
        let that = this
        joinActivity({}).then(res => {
          console.log(res)
          if (res.status === 0) {
            that.getUser()
          } else {
            this.$toast(res.msg)
          }
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  @import "activity.scss";
</style>
