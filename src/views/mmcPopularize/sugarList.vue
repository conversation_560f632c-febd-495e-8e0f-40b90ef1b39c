<template>
    <div class="content">
      <div class="list" v-for="(item, index) in list" :key=index>
        <img :src="item.picture" @click="jionRead(item.id, item.url, index)" />
        <div>
          <p @click="jionRead(item.id, item.url, index)">{{item.title}}</p>
          <span v-show="item.is_read == 1" class="alreadyStudy" @click="jionisRead(item.url)">已学习</span>
          <span v-show="item.is_read == 0" class="goStudy" @click="jionRead(item.id, item.url, index)">去学习</span>
        </div>
      </div>
      <div class="no">没有更多啦</div>
    </div>
</template>

<script>

    import { getList, articleRead } from '@/api/mmcPopularize'

    export default {
        data() {
            return {
              list: []
            }
        },
        created() {
          this.init()
        },
        methods: {
          init() {
            let that = this
            getList({}).then(function (res) {
              console.log(res)
              if (res.status === 0) {
                  that.list = res.data.data
              } else {
                  this.$toast(res.msg)
              }
            })
          },
          jionisRead(url) {
            window.location.href = url
          },
          jionRead(id, url, index) {
            let that = this
            articleRead({ 'article_id': id }).then(function (res) {
              if (res.status === 0) {
                that.init()
                window.location.href = url
              } else {
                  this.$toast(res.msg)
              }
            })
          }
        }
    }
</script>

<style lang="scss" scoped>
  .content{
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    align-items: center;
    background:rgba(252,214,183,1);
    .list{
      width: 327px;
      height: 96px;
      padding: 14px 12px;
      background:rgba(253,237,222,1);
      box-shadow:0px 2px 10px 0px rgba(174,103,8,0.3);
      border-radius:5px;
      margin-top: 15px;
      img{
        width: 133px;
        height: 92px;
        float: left;
      }
      div{
        float: left;
        width: 178px;
        height: 92px;
        margin-left: 10px;
        p{
          width:178px;
          height:69px;
          text-align: left;
          float: left;
          font-size:17px;
          font-weight:400;
          color:rgba(116,73,28,1);
          line-height:23px;
          overflow: hidden;
          text-overflow:ellipsis;
          text-overflow: -o-ellipsis-lastline;
          display: -webkit-box;
          -webkit-line-clamp: 3;
          line-clamp: 3;
          -webkit-box-orient: vertical;
        }
        .goStudy{
          width:82px;
          height:32px;
          float: right;
          line-height: 32px;
          text-align: center;
          display: block;
          background:linear-gradient(360deg,rgba(240,92,65,1) 0%,rgba(255,147,68,1) 100%);
          border-radius:16px;
          font-size:16px;
          font-weight:600;
          color:rgba(255,255,255,1);
        }
        .alreadyStudy{
          width:82px;
          height:32px;
          float: right;
          line-height: 32px;
          text-align: center;
          display: block;
          background:linear-gradient(180deg,rgba(180,236,81,1) 0%,rgba(114,190,84,1) 100%);
          border-radius:16px;
          font-size:16px;
          font-weight:600;
          color:rgba(255,255,255,1);
        }
      }
    }
    .no{
      font-size:12px;
      font-weight:400;
      color:rgba(102,102,102,1);
      line-height:17px;
      margin: 14px 0 28px 0;
    }
  }
</style>
