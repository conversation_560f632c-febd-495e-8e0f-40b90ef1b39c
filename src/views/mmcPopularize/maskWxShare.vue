<template>
  <div class="wrapper">
    <div class="content">
      <div class="top">
        <img class="img" src="./images/shang.png"/>
        <img class="img1" src="./images/<EMAIL>"/>
        <img class="img2" src="./images/<EMAIL>"/>
      </div>
      <div class="question">
        <div class="inhert">
          <div class="one">
            <p>1.填写我的手机号码</p>
            <van-field v-model="cell" type="tel" placeholder="请输入手机号码"/>
          </div>
          <div class="one">
            <p>2.您是否是糖尿病患者？</p>
            <van-radio-group v-model="user_type" class="clearfix">
              <van-radio name="1" checked-color="#F76A38">是</van-radio>
              <van-radio name="2" checked-color="#F76A38">否</van-radio>
            </van-radio-group>
          </div>
          <div class="one" v-show="user_type==1 || user_type==''">
            <p>3.您的糖化血红蛋白是多少？</p>
            <van-radio-group v-model="hba1c_range" class="clearfix">
              <van-radio name="1" checked-color="#F76A38">＜7％</van-radio>
              <van-radio name="2" checked-color="#F76A38">7％~8.0％</van-radio>
              <van-radio name="3" checked-color="#F76A38">8.1％~9.0％</van-radio>
              <van-radio name="4" checked-color="#F76A38">＞9.0％</van-radio>
            </van-radio-group>
          </div>
          <div class="one" v-show="user_type==1 || user_type==''">
            <p>4.您的空腹血糖大致波动范围？</p>
            <van-radio-group v-model="glu0_range" class="clearfix">
              <van-radio name="1" checked-color="#F76A38">＜7.0mmol/L</van-radio>
              <van-radio name="2" checked-color="#F76A38">7~8.5mmol/L</van-radio>
              <van-radio name="3" checked-color="#F76A38">8.5~10mmol/L</van-radio>
              <van-radio name="4" checked-color="#F76A38">＞10mmol/L</van-radio>
            </van-radio-group>
          </div>
          <div class="one" v-show="user_type==2">
            <p>3.以下几种情况是糖尿病易感因素？</p>
            <van-checkbox-group v-model="diabetes_reason" class="clearfix">
              <van-checkbox name="1" icon-size="16px" shape="square" checked-color="#F76A38">A 久坐，运动少</van-checkbox>
              <van-checkbox name="2" icon-size="16px" shape="square" checked-color="#F76A38">B 糖尿病家族史</van-checkbox>
              <van-checkbox name="3" icon-size="16px" shape="square" checked-color="#F76A38" class="checkc">C 肥胖
              </van-checkbox>
              <van-checkbox name="4" icon-size="16px" shape="square" checked-color="#F76A38" class="checkd">D
                有高血压、血脂异常
              </van-checkbox>
            </van-checkbox-group>
          </div>
          <div class="one">
            <p>{{user_type==2 ? '4' : '5'}}.您的年龄？</p>
            <van-radio-group v-model="age_range" class="clearfix">
              <van-radio name="1" checked-color="#F76A38">＜40岁</van-radio>
              <van-radio name="2" checked-color="#F76A38">40～50岁</van-radio>
              <van-radio name="3" checked-color="#F76A38">50～60岁</van-radio>
              <van-radio name="4" checked-color="#F76A38">＞60～70岁</van-radio>
              <van-radio name="5" checked-color="#F76A38">＞60岁</van-radio>
            </van-radio-group>
          </div>
          <div class="one">
            <p class="p">{{user_type==2 ? '5' : '6'}}.控制血糖，除了药物之外，您愿意每个月花多少钱在控糖方面？</p>
            <van-radio-group v-model="drug_budget" class="clearfix">
              <van-radio name="1" checked-color="#F76A38">＜100</van-radio>
              <van-radio name="2" checked-color="#F76A38">100～200</van-radio>
              <van-radio name="3" checked-color="#F76A38">200～500</van-radio>
              <van-radio name="4" checked-color="#F76A38">500～1000</van-radio>
              <van-radio name="5" checked-color="#F76A38">1000以上</van-radio>
            </van-radio-group>
          </div>
          <div class="sub" @click="submit" v-show="!isQuestions">提交问卷</div>
          <div class="subs" @click="submits" v-show="isQuestions">提交问卷</div>
          <div class="blank"></div>
        </div>
      </div>
      <div class="btn">
        <p v-show="!isQuestions" @click="download"></p>
        <p v-show="isQuestions">
          <a id="iPhone" href="https://itunes.apple.com/us/app/mmc管家/id1198430389?l=zh&amp;ls=1&amp;mt=8"></a>
          <a id="Android" href="http://download.zz-med.com/Mmc_Mobile_Android_Application/new/MMCUser_Android_Application.apk"></a>
        </p>
        <img src="./images/go.gif">
      </div>
      <div class="bottom">
        <img src="./images/bottom.png">
      </div>
    </div>
    <!-- 弹层 -->
    <div class="mask" v-stop v-show="remarkFlg"></div>
    <!-- 问卷提交成功的弹窗 -->
    <div class="submitSuccess" v-show="questionRemarkFlg">
      <img class="bg" src="./images/<EMAIL>"/>
      <img class="bg1" src="./images/<EMAIL>" @click="close"/>
    </div>
    <!-- 在微信浏览器的判断 -->
    <div class="wxPrompt" v-show="wxRemarkFlg">
      <img class="bg2" src="./images/Bitmap.png"/>
      <img class="bg3" src="./images/text.png"/>
    </div>
  </div>
</template>

<script>
  import Vue from 'vue'
  import { Toast } from 'vant'
  import { submitQuestion } from '@/api/mmcPopularize'

  Vue.use(Toast)
  export default {
    data() {
      return {
        isQuestions: false,
        patient_id: '',
        cell: '',
        user_type: '', // 糖尿病
        hba1c_range: '', // 糖化血红蛋白范围
        glu0_range: '', // 空腹血糖范围
        age_range: '', // 年龄范围
        drug_budget: '', // 用药预算
        diabetes_reason: [],
        remarkFlg: false,
        wxRemarkFlg: false,
        questionRemarkFlg: false
      }
    },
    created() {
      this.patient_id = this.$route.query.patientId
      this.$nextTick(() => {
        this.init()
      })
    },
    directives: {
      stop: {
        // 指令的定义
        bind: function (el) {
          el.addEventListener('touchmove', function (e) {
            e.preventDefault()
          })
        }
      }
    },
    methods: {
      init() {
        if (this.isWeiXin()) {
          this.remarkFlg = true
          this.wxRemarkFlg = true
        } else {
          // 判断安卓或苹果
          var userAgent = navigator.userAgent
          var isAndroid = userAgent.indexOf('Android') > -1 || userAgent.indexOf('Linux') > -1
          var isIOS = !!userAgent.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)
          if (isAndroid) {
              document.getElementById('iPhone').style.display = 'none'
              // document.getElementById('iPhones').style.display = 'none'
            }
            if (isIOS) {
              document.getElementById('Android').style.display = 'none'
              // document.getElementById('Androids').style.display = 'none'
            }
        }
      },
      download() {
        if (this.isQuestions === false) {
          this.$toast('提交问卷后才能参与福利活动哦')
        }
      },
      isWeiXin() {
        var ua = window.navigator.userAgent.toLowerCase()
        if (ua.match(/MicroMessenger/i) === 'micromessenger') {
          return true
        } else {
          return false
        }
      },
      submits() {
        this.$toast('您已提交过全部题目')
        return false
      },
      submit() {
        if (this.user_type === '') {
          this.$toast('请您把题目填写完整再提交')
          return false
        } else {
          if (this.user_type === '1') {
            if (this.cell === '' || this.user_type === '' || this.hba1c_range === '' || this.glu0_range === '' || this.age_range === '' || this.drug_budget === '') {
              this.$toast('请您把题目填写完整再提交')
              return false
            }
          } else {
            if (this.cell === '' || this.user_type === '' || this.age_range === '' || this.drug_budget === '' || this.diabetes_reason.length === 0) {
              this.$toast('请您把题目填写完整再提交')
              return false
            }
          }
        }
        let myreg = /^[1][0-9]{10}$/
        if (!myreg.test(this.cell)) {
          this.$toast('手机号格式不正确')
          return false
        }
        // 提交问题
        let datas = {
          patient_id: this.patient_id, // 推荐人id
          cell: this.cell,
          user_type: this.user_type, // 糖尿病
          hba1c_range: this.hba1c_range, // 糖化血红蛋白范围
          glu0_range: this.glu0_range, // 空腹血糖范围
          age_range: this.age_range, // 年龄范围
          drug_budget: this.drug_budget, // 用药预算
          diabetes_reason: this.diabetes_reason.join(',') // 字符串
        }
        let that = this
        submitQuestion(datas).then(function (res) {
          that.remarkFlg = true
          that.questionRemarkFlg = true
          that.isQuestions = true
        })
      },
      close() {
        this.remarkFlg = false
        this.questionRemarkFlg = false
      }
    }
  }
</script>

<style lang="scss" scoped>
  .content {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    align-items: center;
    background: rgba(255, 255, 255, 1);
    .top {
      position: relative;
      .img {
        width: 100%;
      }
      .img1 {
        width: 10px;
        height: 38px;
        position: absolute;
        bottom: -12px;
        left: 25px;
      }
      .img2 {
        width: 10px;
        height: 38px;
        position: absolute;
        bottom: -12px;
        right: 25px;
      }
    }
    .question {
      width: 345px;
      height: auto;
      padding: 8px;
      background: rgba(247, 106, 56, 1);
      border-radius: 10px;
      .inhert {
        width: 100%;
        height: 100%;
        background: #fff;
        .one {
          width: 302px;
          margin: 0 auto;
          border-bottom: 1px dashed rgba(147, 149, 148, 1);
          p {
            height: 50px;
            line-height: 50px;
            text-align: left;
            font-size: 17px;
            font-weight: 500;
            color: rgba(54, 54, 54, 1);
          }
          .p {
            margin: 15px 0;
            height: 60px;
            line-height: 30px;
          }
          .van-cell {
            width: 302px;
            height: 44px;
            margin-bottom: 19px;
            background: rgba(255, 255, 255, 1);
            border: 1px solid rgba(104, 104, 104, 1);
            border-radius: 5px;
          }
          .van-radio-group {
            .van-radio {
              width: 46%;
              padding-left: 4%;
              margin-bottom: 20px;
              float: left;
              font-size: 16px;
              .van-radio__label {
                font-weight: 400;
                color: #686868;
              }
            }
          }
          .van-checkbox-group {
            .van-checkbox {
              width: 46%;
              padding-left: 4%;
              margin-bottom: 20px;
              float: left;
              font-size: 14px;
              .van-checkbox__label {
                font-weight: 400;
                color: #686868;
              }
            }
            .checkc {
              width: 30%;
              padding-left: 4%;
            }
            .checkd {
              width: 62%;
              padding-left: 4%;
            }
          }
        }
        .sub {
          width: 237px;
          height: 49px;
          line-height: 49px;
          text-align: center;
          margin: 0 auto;
          margin-top: 12px;
          font-size: 17px;
          font-weight: 400;
          color: rgba(255, 255, 255, 1);
          background: url('./images/<EMAIL>') no-repeat;
          background-size: cover;
        }
        .subs{
          width: 237px;
          height: 49px;
          line-height: 49px;
          text-align: center;
          margin: 0 auto;
          margin-top: 12px;
          font-size: 17px;
          font-weight: 400;
          color: rgba(255, 255, 255, 1);
          background: url('./images/<EMAIL>') no-repeat;
          background-size: cover;
        }
        .blank {
          height: 12px;
          background: #fff;
        }
      }
    }
    .btn{
      width:327px;
      height: 101px;
      margin: 10px 0;
      background: url('./images/fulibutton.png') no-repeat;
      background-size: cover;
      position: relative;
      p{
          width: 100%;
          height: 100%;
          a{
              float: left;
              width: 100%;
              height: 100%;
          }
      }
      img{
          width: 79px;
          height: 49px;
          position: absolute;
          right: 0;
          bottom: -12px;
      }
    }
    .bottom {
      position: relative;
      margin-top: 10px;
      img {
        width: 100%;
      }
    }
  }

  .mask {
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    z-index: 99;
    background: rgba(0, 0, 0, 1);
    opacity: 0.7;

  }

  .submitSuccess {
    width: 243px;
    height: 214px;
    position: fixed;
    z-index: 100;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    .bg {
      width: 243px;
      height: 140px;
      margin-bottom: 40px;
    }
    .bg1 {
      width: 34px;
      height: 34px;
    }
  }

  .wxPrompt {
    width: 100%;
    height: 100%;
    position: fixed;
    z-index: 100;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    .bg2 {
      width: 141px;
      height: 70px;
      position: absolute;
      top: 70px;
      right: 27px;
    }
    .bg3 {
      width: 248px;
      height: 78px;
      position: absolute;
      top: 194px;
      left: 62px;
    }
  }
</style>
