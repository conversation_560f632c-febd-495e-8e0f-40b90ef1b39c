<template>
    <div class="content">
       <div class="title">
         <img class="img" src="./images/beijing.png" />
         <div class="btn">
           <p >
             <a id="iPhone" href="https://itunes.apple.com/us/app/mmc管家/id1198430389?l=zh&amp;ls=1&amp;mt=8"></a>
             <a id="Android" href="http://download.zz-med.com/Mmc_Mobile_Android_Application/new/MMCUser_Android_Application.apk"></a>
           </p>
           <img src="./images/go.gif">
           <!-- <p>
             <a id="iPhones" href="ZhiZhongApp://"><img class="img" src="./images/dakai.png" /></a>
             <a id="Androids" href="guanjia://jp.app/openwith"><img class="img" src="./images/dakai.png" /></a>
           </p> -->
         </div>
        </div>
    </div>
</template>

<script>

    export default {
        data() {
            return {

            }
        },
        created() {
          this.$nextTick(() => {
            this.init()
          })
        },
        methods: {
          init() {
            // 判断安卓或苹果
            var userAgent = navigator.userAgent
            var isAndroid = userAgent.indexOf('Android') > -1 || userAgent.indexOf('Linux') > -1
            var isIOS = !!userAgent.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)
            if (isAndroid) {
              document.getElementById('iPhone').style.display = 'none'
              // document.getElementById('iPhones').style.display = 'none'
            }
            if (isIOS) {
              document.getElementById('Android').style.display = 'none'
              // document.getElementById('Androids').style.display = 'none'
            }
          }
        }
    }
</script>

<style lang="scss" scoped>
  @import "popularize.scss";
</style>
