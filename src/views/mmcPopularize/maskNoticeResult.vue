<template>
    <div class="content" ref="height">
      <div class="title"><img src="./images/title.png" /></div>
      <div class="title1">专家教您居家控糖,有机会领取口罩</div>
      <div class="main">
        <div class="result">
          <p class="title2">您可以在MMC管家APP</p>
          <p class="title3">填写收货地址领取</p>
          <!-- <p class="all">您一共可以领取<i>{{kouzhaoNum}}</i>个口罩</p>
          <span class="alls" v-show="shareNum != 0">其中有{{shareNum}}个通过邀请好友获得</span> -->
          <div class="btn">
           <p >
             <a id="iPhone" href="https://itunes.apple.com/us/app/mmc管家/id1198430389?l=zh&amp;ls=1&amp;mt=8"></a>
             <a id="Android" href="http://download.zz-med.com/Mmc_Mobile_Android_Application/new/MMCUser_Android_Application.apk"></a>
           </p>
           <img src="./images/go.gif">
           <!-- <p>
             <a id="iPhones" href="ZhiZhongApp://"><img class="img" src="./images/dakai.png" /></a>
             <a id="Androids" href="guanjia://jp.app/openwith"><img class="img" src="./images/dakai.png" /></a>
           </p> -->
          </div>
        </div>
      </div>
    </div>
</template>

<script>
    export default {
        data() {
          return {
            kouzhaoNum: 10,
            shareNum: 7
          }
        },
        created() {
          this.$nextTick(() => {
            this.init()
          })
        },
        mounted() {

        },
        methods: {
          init() {
            // 判断安卓或苹果
            var userAgent = navigator.userAgent
            var isAndroid = userAgent.indexOf('Android') > -1 || userAgent.indexOf('Linux') > -1
            var isIOS = !!userAgent.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)
            if (isAndroid) {
              document.getElementById('iPhone').style.display = 'none'
              // document.getElementById('iPhones').style.display = 'none'
            }
            if (isIOS) {
              document.getElementById('Android').style.display = 'none'
              // document.getElementById('Androids').style.display = 'none'
            }
            this.$refs.height.style.height = window.innerHeight + 'px'
          }
        }
    }
</script>

<style lang="scss" scoped>
  .content{
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    background:rgba(252,214,183,1);
    .title{
        width: 300px;
        height: 39px;
        margin-top: 22px;
        margin-bottom: 8px;
        img{
            width: 100%;
            height: 100%;
        }
    }
    .title1{
      width: 312px;
      height: 45px;
      line-height: 45px;
      font-size:17px;
      font-weight:400;
      color:rgba(255,255,255,1);
      background: url(./images/title2-bg.png) no-repeat;
      background-size: cover;
    }
    .main{
      width:311px;
      height:360px;
      padding: 17px 15px;
      margin-top: 12px;
      margin-bottom: 20px;
      background:#FDEDDE;
      box-shadow:0px 2px 10px 0px rgba(174,103,8,0.4);
      border-radius:5px;
      .result{
        width: 100%;
        height: 100%;
        background: url('./images/gongxi-bg.png') no-repeat #fff;
        background-size: 100% 220px;
        position: relative;
        .title2{
          width: 100%;
          height: 32px;
          line-height: 32px;
          text-align: center;
          font-size:22px;
          font-weight:400;
          color:rgba(255,255,255,1);
          position: absolute;
          top: 115px;
        }
        .title3{
          width: 100%;
          height: 32px;
          line-height: 32px;
          text-align: center;
          font-size:22px;
          font-weight:400;
          color:rgba(255,255,255,1);
          position: absolute;
          top: 147px;
        }
        .all{
          width: 100%;
          height:25px;
          font-size:18px;
          font-weight:400;
          color:rgba(255,255,255,1);
          line-height:25px;
          position: absolute;
          top: 115px;
          i{
            font-size:22px;
            font-weight:bold;
            font-style: normal;
          }
        }
        .alls{
          display: block;
          width: 100%;
          height:26px;
          font-size:17px;
          font-weight:400;
          color:rgba(255,255,255,1);
          line-height:24px;
          position: absolute;
          top: 146px;
          text-align: center;
        }
        .btn{
            width: 100%;
            height: 101px;
            background: url('./images/fulibutton.png') no-repeat;
            background-size: cover;
            position: absolute;
            top: 230px;
            p{
                width: 100%;
                height: 100%;
                a{
                    float: left;
                    width: 100%;
                    height: 100%;
                }
            }
            img{
                width: 79px;
                height: 49px;
                position: absolute;
                right: 0;
                bottom: -12px;
            }
        }
      }
    }
  }
</style>
