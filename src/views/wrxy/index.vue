<template>
  <div class="main">
    <div class="content">
      <div class="titleBox">
        <div class="title">iHEC科研项目</div>
        <div class="imgBox">
          <img src="http://wx.qlogo.cn/mmhead/Q3auHgzwzM6pg9KYRIDg3T5nBibDRl1cjDlDNXCBNicx26Rr4oYanVKA/0" alt="">
          <div>iHEC智慧血压</div>
        </div>
      </div>
      <div class="listBox">
        <div class="list" v-for="( item , index) in arrList" :key="index" @click="goDetail(item.clickUrl)">
          <img :src="item.imgUrl" alt="">
          <div>{{item.content}}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>

  export default {
    data: () => {
      return {
        arrList: [
          {
            imgUrl: require('./mmm.png'),
            content: 'MMM项目',
            clickUrl: 'https://mp.weixin.qq.com/s?__biz=MzIwMTIwNzEzNQ==&mid=502417712&idx=1&sn=d09f9cc0f0d0a52744868d7bdbafeaae&scene=19#wechat_redirect'
          },
          {
            imgUrl: require('./dtt.png'),
            content: 'DTT(Dosing-Time Trial)研究',
            clickUrl: 'https://mp.weixin.qq.com/s?__biz=MzIwMTIwNzEzNQ==&mid=502417727&idx=1&sn=e830e07708ae0afe42cf92b3d0fc4e51&scene=19#wechat_redirect'
          },
          {
            imgUrl: require('./reaction-hbp.png'),
            content: '万人家庭血压达标行动(REACTION-HBP）',
            clickUrl: 'https://mp.weixin.qq.com/s?__biz=MzIwMTIwNzEzNQ==&mid=502417742&idx=1&sn=05cd65707f97b28891ebae03b8d655c0&scene=19#wechat_redirect'
          },
          {
            imgUrl: require('./impression.png'),
            content: 'IMPRESSION',
            clickUrl: 'https://mp.weixin.qq.com/s?__biz=MzIwMTIwNzEzNQ==&mid=502415840&idx=1&sn=85096c918d1136d86e272e7c01da2d85&scene=19#wechat_redirect'
          }
        ]
      }
    },
    watch: {

    },
    components: {

    },
    created() {

    },
    methods: {
      goDetail: function (url) {
        window.open(url, '_blank')
      }
    }
  }
</script>

<style lang="scss" scoped>
  .main{
    width: 100%;
    height: 100%;
    .content{
      display: flex;
      flex-direction: column;
      padding: 20px;
      background: #ffffff;
      .titleBox{
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        .title{
          font-size: 24px;
        }
        .imgBox{
          display: flex;
          align-items: center;
          margin: 10px 0;
          img{
            width: 28px;
            height: 28px;
          }
          div{
            font-size: 15px;
            margin-left: 10px;
            color: #576b95;
          }
        }
      }
      .listBox{
        display: flex;
        flex-direction: column;
        .list:last-child{
          border-bottom: 1px #eeeeee solid;
        }
        .list{
          display: flex;
          border-top: 1px #eeeeee solid;
          align-items: center;
          padding: 10px 0;
          cursor: pointer;
          img{
            width: 50px;
            height: 50px;
          }
          div{
            color: #000;
            font-size: 17px;
            text-align: left;
            margin-left: 10px;
            line-height: 25px;
          }
        }
      }
    }
  }
</style>
