<template>
  <div class="content">
    <div class="blood">
      <div class="topTip"
           style="background:url('https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/wechat/feedback.png') 16px center no-repeat #E5F0FF;background-size:22px;">
        参考《2019家庭血压监测指南》得出标准结论，<span>实际控制指标以医生建议为准。</span>
      </div>
    </div>
    <div class="blood">
      <div class="bloodTitle"><span>血压</span><span>{{ info.time_slot }}</span></div>
      <div class="bloodPic">
        <blood-pic :data="info.chart"></blood-pic>
      </div>
    </div>
    <div class="checkPic">
      <div class="left"
           ref="checkRef"></div>
      <div class="right">
        <p class="one">你近7日血压监测<span :class="info.avg_msg == '不稳定' ? 'color' : 'colorNor'">{{info.avg_msg}}</span></p>
        <p class="one">平均血压{{info.avg_sbp}}/{{info.avg_dbp}}</p>
        <p class="two"><span class="two1"></span>正常{{info.normal}}次<span class="two2"></span>不稳定{{info.unusual}}次</p>
        <p class="three"
           @click="unusualReason = !unusualReason">血压不稳定常见原因</p>
      </div>
    </div>
    <div class="page-break-inside"></div>
    <div class="blood">
      <div class="bloodTitle"><span>病情信息</span></div>
      <div class="user_info">
        <div class="content_margin">
          <span class="patient_info_name">{{info.name}}</span>
          <img class="patient_info_icon" v-if="info.sex_format=='男'" src="./../image/<EMAIL>">
          <img class="patient_info_icon" v-if="info.sex_format=='女'" src="./../image/<EMAIL>">
        </div>
        <div class="info_inline_show">
          <img class="patient_info_icon" v-if="info.age!=''" src="./../image/age.png">
          <span  v-if="info.age!=''">{{info.age}}岁</span>
          <img  v-if="info.height!=''" class="patient_info_icon" src="./../image/height.png">
          <span v-if="info.height!=''">{{info.height}} cm</span>
          <img v-if="info.weight!=''" class="patient_info_icon" src="./../image/weight.png">
          <span v-if="info.weight!=''" >{{info.weight}} kg</span>
          <img v-if="info.bmi!=''" class="patient_info_icon" src="./../image/bmi.png">
          <span v-if="info.bmi!=''">{{info.bmi}}</span>
        </div>
        <div class="box">
          <div class="pt5"><label>患病年限：</label>{{info.illness_year_format}}</div>
          <div class="pt5"><label>用药情况：</label>{{info.medication}}</div>
          <div class="pt5"><label>病史情况：</label>{{info.medical_history_format}}</div>
        </div>
      </div>
    </div>
    <div class="blood">
      <div class="bloodTitle"><span>健康建议</span></div>
      <div class="box">
        <div>{{info.health_advice}}</div>
      </div>
    </div>
    <div class="page-break-inside"></div>
    <div class="blood">
      <div class="bloodTitle"><span>近7日血压分布</span><span>{{ info.timeSlot }}</span></div>
      <div class="tabTwo">
        <div class="bloodChart"
             ref="bloodRef"></div>
      </div>
      <div class="tabOne pt5">
        <div class="tabLeft">
          <span class="morning">早晨</span>
          <span>收缩压</span>
          <span>舒张压</span>
          <span>心率</span>
          <span class="dinner">晚间</span>
          <span>收缩压</span>
          <span>舒张压</span>
          <span>心率</span>
        </div>
        <div class="tabRight">
          <p class="dateTime">
            <span v-for="(item, index) in info.chart_time"
                  :key=index>{{item}}</span>
          </p>
          <p class="gaoya">
            <span v-for="(item, index) in info.table_list.morning_sbp"
                  :class="sbpColor(item)"
                  :key=index>{{item == 0 ? '--' : item}}</span>
          </p>
          <p class="diya">
            <span v-for="(item, index) in info.table_list.morning_dbp"
                  :class="dbpColor(item)"
                  :key=index>{{item == 0 ? '--' : item}}</span>
          </p>
          <p class="gaoya">
            <span v-for="(item, index) in info.table_list.morning_pulse"
                  :key=index>{{item == 0 ? '--' : item}}</span>
          </p>
          <p class="blank"></p>
          <p class="sbp">
            <span v-for="(item, index) in info.table_list.night_sbp"
                  :class="sbpColor(item)"
                  :key=index>{{item == 0 ? '--' : item}}</span>
          </p>
          <p class="dbp">
            <span v-for="(item, index) in info.table_list.night_dbp"
                  :class="dbpColor(item)"
                  :key=index>{{item == 0 ? '--' : item}}</span>
          </p>
          <p class="sbp">
            <span v-for="(item, index) in info.table_list.night_pulse"
                  :key=index>{{item == 0 ? '--' : item}}</span>
          </p>
        </div>
      </div>
    </div>
    <div class="page-break-inside"></div>
    <div class="blood clearfix">
      <div class="bloodTitle"><span>血压测量明细</span><span>{{ info.timeSlot }}</span></div>
      <div class="bloodList clearfix">
        <div class="listTitle">
          <p class="time1">时间</p>
          <p class="time2"><span>收缩压/舒张压</span><span class="unit">（mmHg）</span></p>
          <p class="time3"><span>心率</span><span class="unit">（次/分）</span></p>
        </div>
        <ul>
          <li v-for="(item, index) in info.bp_distribution"
              :key=index>
            <div class="listTime">{{item.group_time}}</div>
            <div class="listDetail"
                 v-for="(val, index) in item.data"
                 :key=index>
              <p class="time1"><span>{{val.sec}}</span><span :class="[status(val.normal), 'active']"></span></p>
              <p class="time2"><span :class="sbpColor(val.sbp)">{{val.sbp}}</span>/<span :class="dbpColor(val.dbp)">{{val.dbp}}</span>
              </p>
              <p class="time3">{{val.pulse}}</p>
            </div>
          </li>
        </ul>
      </div>
    </div>
    <div class="mask"
         v-if="unusualReason"></div>
    <div class="maskContent"
         v-if="unusualReason">
      <p class="title">血压不稳定常见原因</p>
      <div>
        <span>1、高钠低钾膳食</span>
        <span>2、超重和肥胖</span>
        <span>3、饮酒</span>
        <span>4、精神紧张</span>
        <span>5、其它危险因素：年龄、高血压家族史、缺乏体力活动等</span>
        <span>6、女性低血压的患病率比男性高</span>
        <span>7、BMI低</span>
        <span>8、自觉健康状况不理想</span>
      </div>
      <p class="sure"
         @click="sure">确定</p>
    </div>
  </div>
</template>

<script>
  import echarts from 'echarts'
  import bloodPic from '@/components/bloodPic.vue'
  import {hospTheoryReportBp} from '@/api/common/report'

  export default {
    data() {
      return {
        userId: '',
        startDate: '',
        endDate: '',
        tabIndex: 1,
        unusualReason: false,
        reportId: '',
        info: {
          age:'',
          height:'',
          weight:'',
          bmi:'',
          chart: [],
          avg_msg: '',
          avg_sbp: '',
          avg_dbp: '',
          number: '',
          normal: '',
          unusual: '',
          chart_time: [],
          table_list: {
            morning_sbp: [],
            morning_dbp: [],
            morning_pulse: [],
            night_sbp: [],
            night_dbp: [],
            night_pulse: []
          },
          "illness_year_format": "2-6年",                //类型：String  必有字段  备注：患病年限
          "medication": "123",                //类型：String  必有字段  备注：用药情况
          "medical_history_format": "高血脂",                //类型：String  必有字段  备注：病史情况
          bp_distribution: [],
          time_slot: ''
        }
      }
    },
    components: {bloodPic},
    created() {
      let query = this.$route.query
      this.cell = query.cell
      this.sign = query.sign
      this.startDate = query.start_date
      this.endDate = query.end_date
      this.init()
    },
    methods: {
      init() {
        let that = this
        hospTheoryReportBp({
          sign: this.sign, // 用户ID
          cell: this.cell, // 用户ID
          start_date: this.startDate, // 开始时间
          end_date: this.endDate // 结束时间
        }).then(res => {
          if (res.status == 0) {
            that.info = res.data
          } else {
            this.$toast(res.msg)
          }
        }).then(() => {
          that.checkPic(that.$refs.checkRef, that.info.number, that.info.normal, that.info.unusual)
        }).then(() => {
          that.bloodPic(that.$refs.bloodRef, that.info.chart_time, that.info.m_sbp, that.info.m_dbp, that.info.s_sbp, that.info.s_dbp)
        }).catch(err => {
          this.$toast(err)
        })
      },
      status(num) {
        if (num == 1) {
          return 'bgNormal'
        } else if (num == 2) {
          return 'bgHigh'
        } else if (num == 3) {
          return 'bgDown'
        }
      },
      sbpColor(num) {
        if (num > 0 && num < 90) {
          return 'colorDown'
        } else if (num >= 90 && num <= 135 || num == 0) {
          return ''
        } else {
          return 'colorHigh'
        }
      },
      dbpColor(num) {
        if (num > 0 && num < 60) {
          return 'colorDown'
        } else if (num >= 60 && num <= 85 || num == 0) {
          return ''
        } else {
          return 'colorHigh'
        }
      },
      sure() {
        this.unusualReason = false
      },

      // 血压饼状图
      checkPic(dom, numner, normal, unusual) {
        let myEcharts = echarts.init(dom)
        myEcharts.setOption({
          color: ['#07C160', '#FF9600'],
          title: {
            show: true,
            text: '检查次数',
            left: 'center',
            top: '42%',
            textStyle: {
              color: '#010203',
              fontSize: 18,
              align: 'center'
            },
            subtext: numner + '次',
            // 副标题文本样式
            subtextStyle: {
              fontSize: 14,
              color: '#262626'
            }
          },
          series: [
            {
              // name: '血压检测',
              type: 'pie',
              silent: true,
              radius: ['70%', '45%'],
              avoidLabelOverlap: false,
              label: {
                normal: {
                  show: false,
                  position: 'center'
                }
              },
              data: [
                {value: normal, name: '正常次数'},
                {value: unusual, name: '不稳定次数'}
              ],
              itemStyle: {
                borderWidth: 3,
                borderColor: '#fff',
                emphasis: {
                  shadow: 10,
                  shadowOffsetX: 0,
                  shadowColor: '#rgba(0,0,0,0.5)'
                }
              }
            }
          ]
        })
      },
      // 血压明细图
      bloodPic(dom, datas, m_sbp, m_dbp, s_sbp, s_dbp) {
        console.log(m_sbp, m_dbp, s_sbp, s_dbp, 'm_sbp, m_dbp, s_sbp, s_dbp')
        let myEcharts = echarts.init(dom)
        myEcharts.setOption({
          title: {
            text: 'mmHg',
            left: '8px',
            textStyle: {
              color: '#666666',
              fontSize: '24px'
            }
          },
          tooltip: {
            trigger: 'none'
          },
          legend: {
            y: 'bottom',
            data: ['早晨高压', '早晨低压', '晚上高压', '晚上低压'],
            textStyle: {
              color: '#666666',
              fontSize: '20px'
            }
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '10%',
            top: '12%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            axisLine: {
              lineStyle: {
                color: '#ccc'
              }
            },
            axisLabel: {
              color: '#666'
            },
            boundaryGap: true,
            data: datas
          },
          yAxis: {
            type: 'value',
            axisLine: {
              lineStyle: {
                color: '#ccc'
              }
            },
            axisLabel: {
              color: '#666'
            },
            min: 0,
            max: 200,
            splitLine: {
              lineStyle: {
                color: '#eee'
              }
            }
          },
          series: [
            {
              name: '早晨高压',
              type: 'line',
              symbol: 'circle',
              symbolSize: 9,
              itemStyle: {
                normal: {
                  color: '#FF8E00',
                  label: {
                    show: true,
                    color: '#333333',
                    fontSize: '14'
                  },
                  lineStyle: {
                    color: '#F67710',
                    width: 1
                  }
                }
              },
              data: m_sbp
            },
            {
              name: '早晨低压',
              type: 'line',
              symbol: 'triangle',
              symbolSize: 9,
              itemStyle: {
                normal: {
                  color: '#FF8E00',
                  label: {
                    show: true,
                    color: '#333333',
                    fontSize: '14'
                  },
                  lineStyle: {
                    color: '#F67710',
                    width: 1
                  }
                }
              },
              data: m_dbp
            },
            {
              name: '晚上高压',
              type: 'line',
              symbol: 'circle',
              symbolSize: 9,
              itemStyle: {
                normal: {
                  color: '#61B1F0',
                  label: {
                    show: true,
                    color: '#333333',
                    position: 'bottom',
                    fontSize: '14'
                  },
                  lineStyle: {
                    color: '#10A2F6',
                    width: 1
                  }
                }
              },
              data: s_sbp
            },
            {
              name: '晚上低压',
              type: 'line',
              symbol: 'triangle',
              symbolSize: 9,
              itemStyle: {
                normal: {
                  color: '#61B1F0',
                  label: {
                    show: true,
                    color: '#333333',
                    position: 'bottom',
                    fontSize: '15'
                  },
                  lineStyle: {
                    color: '#10A2F6',
                    width: 1
                  }
                }
              },
              data: s_dbp
            }
          ]
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  @import "@/assets/scss/bloodReport.scss";

  .content {
    .blood {
      .bloodTitle {
        span {
          border-left: 4px solid #398CFF !important;
        }
      }
    }
  }

  .box {
    text-align: left;
    background: #F6F9FD;
    border-radius: 6px;
    font-size: 14px;
    color: #666;
    padding: 12px 13px;
    margin-bottom: 10px;
  }

  .user_info {
    font-size: 18px;
    text-align: left;
  }

  .patient_info_name {
    font-size: 20px;
    margin-right: 20px;
  }

  .patient_info_icon {
    width: 20px;
    height: 20px;
  }

  .info_inline_show {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    margin: 10px 0;
  }

  .info_inline_show img {
    margin: 0 5px;
    line-height: 15px;
    vertical-align: middle;
  }

  .info_inline_show span {
    margin-right: 10px;
  }

  .pt5 {
    padding-top: 5px;
  }

  .topTip {

    text-align: left;
    line-height: 17px;
    padding: 8px 0 8px 45px;
    background: #E5F0FF;
    font-size: 14px;
    margin-top: 10px;
    color: #666;
  }
  .page-break-inside {
    page-break-before: always !important;
    page-break-after: always !important;
    page-break-inside: avoid !important;
  }

  .bloodChart{
    width: 355px;
    height: 227px !important;
  }
  .tabOne{
    height: 237px !important;
  }
  .tabTwo {
    height: 227px !important;
  }
  .tabLeft{
    height: 227px !important;
  }
  .dinner{
    margin-top: 2px !important;
  }

  .blank{
    height: 32px !important;
  }

  .tabLeft{
    span{
      height: 29px !important;
      line-height: 29px !important;
    }

  }
  .tabRight {
    p {
      span {
        height: 29px !important;
        line-height: 29px !important;
      }
    }
  }
  .listTitle{
    display: inline-flex;
  }
</style>
