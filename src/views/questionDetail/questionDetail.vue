<template>
  <div class="content">
    <div class="title">
      <!--{{title}}-->
      {{ article_info.title }}
    </div>
    <div class="article">
      <!--<div>{{content}}</div>-->
      <div v-html="article_info.content"></div>
    </div>
    <div class="btnBox">
      <div
        class="btn"
        :class="{ btn1: is_helpful === 1 }"
        @click="addArticleHelpful(1)"
      >
        <img v-if="is_helpful === 1" src="./images/zan.png" alt="zan" />
        <img v-if="is_helpful !== 1" src="./images/youbangzhu.png" alt="zan" />

        <span>有帮助</span>
      </div>
      <div
        class="btn"
        :class="{ btn2: is_helpful === 0 }"
        @click="addArticleHelpful(0)"
      >
        <img
          v-if="is_helpful === 0"
          src="./images/meibangzhu_dianjihou.png"
          alt="zan"
        />
        <img v-if="is_helpful !== 0" src="./images/meibangzhu.png" alt="zan" />
        <span>没帮助</span>
      </div>
    </div>
  </div>
</template>
<script>
import { getArticleInfo, addArticleHelpful } from "@/api/survey";

export default {
  data() {
    return {
      title: "",
      content: "",
      article_id: "",
      is_helpful: undefined,
      article_info: {},
    };
  },
  created() {
    this.article_id = this.$route.query.articleId;
    console.log(this.isTourist, 111);
    this.init();
  },
  methods: {
    init() {
      let that = this;
      getArticleInfo({
        article_id: this.article_id,
      }).then(function (res) {
        if (res.status === 0) {
          that.article_info = res.data;
          that.is_helpful = that.article_info.is_helpful;
        } else {
          alert(res.msg);
        }
      });
    },
    addArticleHelpful(isHelpful) {
      let that = this;
      if (that.is_helpful === undefined) {
        that.is_helpful = isHelpful;
        addArticleHelpful({
          question_id: that.article_id,
          is_helpful: isHelpful,
        }).then(function (res) {
          if (res.status === 0) {
            that.is_helpful = isHelpful;
          } else {
            alert(res.msg);
          }
        });
      }
    },
  },
};
</script>

<style scoped>
.content {
  width: 331px;
  padding: 0 22px;
  height: auto;
  padding-bottom: 40px;
}

.title {
  width: 100%;
  min-height: 30px;
  line-height: 30px;
  color: #333;
  font-size: 22px;
  font-weight: 600;
  text-align: left;
  margin: 22px 0 20px 0;
}

.article {
  width: 100%;
  min-height: 18px;
  height: auto;
  word-break: break-word;
  text-align: left;
  font-size: 18px;
}

.article >>> img {
  width: 100%;
  height: 100%;
  max-width: 100%;
  max-height: 100%;
  display: block;
}

.article >>> p,
.article >>> div {
  width: 100%;
  font-size: 18px;
  color: #333333;
  text-align: left;
  line-height: 34px;
  margin-bottom: 10px;
}

.article >>> video {
  margin-bottom: 10px;
  width: 100%;
}

.article >>> iframe {
  width: 100%;
  height: 146px;
  display: block;
}

.btnBox {
  display: flex;
  justify-content: space-around;
  padding-top: 20px;
}

.btn1 {
  background: #ff8e00;
  color: #fff;
  border: none;
}

.btn2 {
  background: #398cff;
  color: #fff;
  border: none;
}

.btn {
  display: flex;
  width: 104px;
  height: 35px;
  align-items: center;
  justify-content: center;
  border-radius: 18px;
  font-size: 18px;
  cursor: pointer;
  border: 1px solid #ccc;
}

.btn img {
  width: 20px;
  height: 20px;
  margin-right: 2px;
}
</style>
