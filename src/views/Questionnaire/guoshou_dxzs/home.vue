<template>
  <div class="main" ref="height">
    <div class="content">
      <img src="../images/guoshou_banner.png" alt="" class="banner">
      <div class="contentBox">
        <div class="contentText">
          代谢性疾病是一种全身疾病，涉及血糖、血脂、血压等多个方面的问题。代谢综合征包含糖尿病、糖耐量异常、肥胖、高脂血症、高血压等五大疾病。这些疾病都会引发包括心梗和脑中风在内的心脑血管疾病。瑞宁<sup>TM</sup>代谢指数是基于全国大规模流行病学研究数据，根据您的血压、血糖、肥胖、血脂等指标计算所得，代表了您未来3年里患心脑血管疾病的风险。分数越低代表代谢指数健康状态越差。
        </div>
        <div class="btn" @click="getAssess()">开始评估</div>
        <!-- <div class="tips">填写问卷0/10</div> -->
      </div>
    </div>
    <div class="modalBox" v-show="show == true">
      <div class="userNotices">
        <div class="noticeTitle">用户使用服务须知</div>
        <div class="noticeContent">本服务来源于MMC瑞宁代谢指数问卷，问卷结果不代表医生的诊断、药剂师的判断及其他专家的意见。我们会对您上传的个人信息保密，在保护用户隐私的责任前提下，有限度的使用您的数据进行编辑、汇总、分析和提供报告服务</div>
        <div class="noticeBtn">
          <div class="disAgree" @click="getDisagree">不同意</div>
          <div class="agree" @click="getAgree">同意</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    data() {
      return {
        userId: '',
        staffcode: '',
        source_type: '', // 1来源是控糖营小程序 2来源是管家app
        type: 2,
        show: false
      }
    },
    created() {
      this.staffcode = this.$route.query.staffcode
      this.source_type = this.$route.query.source_type
      this.type = this.$route.query.type
      this.$nextTick(() => {
        this.$refs.height.style.height = window.innerHeight + 'px'
      })
    },
    methods: {
      getAssess() {
        this.show = true
      },
      getDisagree() {
        this.show = false
      },
      getAgree() {
        let type = this.type
        this.$router.push({
          name: 'guoshou.dxzs',
          query: {'type': type, 'staffcode': this.staffcode, 'source_type': this.source_type}
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  @import "home.scss";
</style>
