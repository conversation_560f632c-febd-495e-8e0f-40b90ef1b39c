.main{
  width: 100%;
  height: 100%;
  position: relative;
  .content{
    width: 100%;
    display: flex;
    justify-content: center;
    flex-direction: column;
    .banner{
      width: 100%;
      height: 280px;
    }
    .contentBox{
      margin: -90px 20px 20px 20px;
      background: #fff;
      border-radius: 4px;
      padding: 20px;
      box-shadow: (0 0 20px rgba(186,131,94,.21));
      .contentText{
        font-size: 13px;
        line-height: 30px;
        color: #333;
        font-weight: 600;
      }
      .btn{
        background: #4D7DEF;
        border-radius: 20px;
        height: 40px;
        line-height: 40px;
        color: #fff;
        font-size: 16px;
        cursor: pointer;
        margin-top: 50px;
      }
      .tips{
        font-size: 14px;
        line-height: 50px;
        color: #4D7DEF;
      }
    }
  }
  .modalBox {
    position: absolute;
    background: rgba(0, 0, 0, .5);
    width: 100%;
    height: 100%;
    top: 0;
    z-index: 2;
    display: flex;
    justify-content: center;
    align-items: center;
    .userNotices{
      position: fixed;
      left: 0;
      bottom: 0;
      right: 0;
      top: 0;
      margin: auto;
      width: 323px;
      height: 357px;
      border-radius: 6px;
      border: 1px #E8E8E8 solid;
      background: #ffffff;
      .noticeTitle{
        color: #000000;
        font-size: 18px;
        line-height: 25px;
        margin-top: 20px;
        margin-bottom: 16px;
      }
      .noticeContent{
        color: #333333;
        font-size: 16px;
        line-height: 26px;
        text-indent: 32px;
        width: 278px;
        margin: 0 auto;
        text-align: left;
      }
      .noticeBtn{
        margin-top: 20px;
        display: flex;
        justify-content: space-between;
        padding: 0 22px;
        font-size: 18px;
        .disAgree{
          width: 128px;
          height: 40px;
          border: 1px #d8d8d8 solid;
          border-radius: 21px;
          color: #999999;
          line-height: 40px;
        }
        .agree{
          width: 128px;
          height: 40px;
          border-radius: 21px;
          color: #ffffff;
          line-height: 40px;
          background: linear-gradient(270deg, #346BFF 0%, rgba(60, 168, 255, 0.99) 100%);
        }
      }
    }
  }
}
