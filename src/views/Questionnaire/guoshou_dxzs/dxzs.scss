.main {
  width: 100%;
  height: 100%;
  position: relative;
  .headBox {
    width: 100%;
    background: #4D7DEF;
    height: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    div {
      width: 25px;
      height: 25px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      border: 1px #fff solid;
      font-size: 16px;
    }
    .active {
      color: #4D7DEF;
      background: #fff;
    }
    span {
      width: 15px;
      height: 1px;
      background: #fff;
    }
  }
  .contentBox {
    .content {
      padding-top: 10px;
      position: relative;
      .title {
        display: flex;
        padding-top: 30px;
        font-size: 16px;
        justify-content: center;
        img {
          width: 14px;
          height: 14px;
        }
        div {
          color: #999999;
          margin: 0 5px;
        }
        span {
          color: #EB6048;
        }
      }
      .inputBox {
        display: flex;
        justify-content: center;
        margin-top: 15px;
        align-items: flex-end;
        .input {
          width: 100px;
          height: 50px;
          text-align: center;
          border-bottom: 1px #e8e8e8 solid;
          font-size: 24px;
          text-align: center;
        }
        input {
          width: 100px;
          height: 50px;
          text-align: center;
          border-bottom: 1px #e8e8e8 solid;
          font-size: 24px;
        }
        div {
          font-size: 18px;
          color: #262626;
        }
      }
      .selectBox {
        display: flex;
        justify-content: center;
        margin: 30px 0;
        div {
          width: 75px;
          height: 75px;
          text-align: center;
          line-height: 75px;
          border-radius: 50%;
          border: 1px #e6e6e6 solid;
          font-size: 18px;
        }
        .active1 {
          background: #4D7DEF;
          color: #fff;
          border: none;
        }
        div:first-child {
          margin-right: 50px;
        }
      }
      .nextBtn {
        width: 220px;
        height: 50px;
        border-radius: 25px;
        background: #4D7DEF;
        text-align: center;
        color: #fff;
        line-height: 50px;
        font-size: 18px;
        margin: 0 auto;
        margin-top: 100px;
        cursor: pointer;
      }
      .btnBox {
        display: flex;
        justify-content: center;
        margin-top: 100px;
        padding-bottom: 20px;
        div {
          width: 146px;
          height: 50px;
          line-height: 50px;
          text-align: center;
          font-size: 18px;
          cursor: pointer;
          border-radius: 25px;
        }
        .prev {
          box-shadow: (0 0 20px rgba(186, 131, 94, .21));
          color: #4D7DEF;
          margin-right: 40px;
        }
        .next {
          width: 146px;
          height: 50px;
          background: #4D7DEF;
          color: #fff;
          border: none;
          border-radius: 25px;
          font-size: 18px;
        }
      }
      .pageTips {
        font-size: 12px;
        color: #999;
        margin: 20px 0;
      }
      .pageTips2 {
        font-size: 12px;
        color: #999;
        margin-bottom: 20px;
      }
    }
  }
  .resultBox {
    width: 100%;
    padding: 0;
    display: flex;
    background: linear-gradient(#171DC5, #5A3BEA);
    flex-direction: column;
    .score {
      font-size: 48px;
      color: #fff;
      position: absolute;
      top: 92px;
      font-weight: 600;
      left: 20px;
    }

    .bannerBox {
      width: 100%;
      height: 332px;
      background: url("../images/guoShouBg.png") no-repeat;
      background-size: cover;
    }
    .box {
      display: flex;
      flex-direction: column;
      margin: 0 auto;
      margin-bottom: 10px;
      background: #FFFFFF;
      border-radius: 6px;
      justify-content: center;
      width: 335px;
      height: 270px;
      .title {
        font-size: 18px;
        font-weight: 600;
        color: #4777E7;
        line-height: 25px;
        .bgColor{
          width: 74px;
          height: 8px;
          background: rgba(203,234,255,.8);
          margin: 0 auto;
          margin-top: -9px;
        }
      }
      .sumData {
        padding: 0 14px;
        .content{
          display: flex;
          justify-content: center;
          align-items: center;
          margin: 20px 0 23px 0;
          .leftBox3{
            border: 3px #ee7800 solid;
            position: relative;
            width: 122px;
            height: 122px;
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            .dataBox{
              display: flex;
              align-items: center;
              color: #ee7800;
              font-size: 18px;
              .data{
                font-size: 36px;
                font-weight: bold;
              }
            }
            .dataName{
              font-size: 14px;
              color: #0e0201;
              margin-top: 16px;
            }
          }
          .leftBox1{
            border: 3px #4fbb00 solid;
            position: relative;
            width: 122px;
            height: 122px;
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            .dataBox{
              display: flex;
              align-items: center;
              color: #4fbb00;
              font-size: 18px;
              .data{
                font-size: 36px;
                font-weight: bold;
              }
            }
            .dataName{
              font-size: 14px;
              color: #0e0201;
              margin-top: 16px;
            }
          }
          .leftBox2{
            border: 3px #e20000 solid;
            position: relative;
            width: 122px;
            height: 122px;
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            .dataBox{
              display: flex;
              align-items: center;
              color: #e20000;
              font-size: 18px;
              .data{
                font-size: 36px;
                font-weight: bold;
              }
            }
            .dataName{
              font-size: 14px;
              color: #0e0201;
              margin-top: 16px;
            }
          }
          .leftBox4{
            border: 3px #0088e7 solid;
            position: relative;
            width: 122px;
            height: 122px;
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            .dataBox{
              display: flex;
              align-items: center;
              color: #0088e7;
              font-size: 18px;
              .data{
                font-size: 36px;
                font-weight: bold;
              }
            }
            .dataName{
              font-size: 14px;
              color: #0e0201;
              margin-top: 16px;
            }
          }
          .midBox1{
            width: 50px;
            height: 50px;
            margin: 0 6px;
            background: url("../images/icon-green.png") no-repeat;
            background-size: cover;
            font-size: 14px;
            text-align: center;
            line-height: 50px;
            color: #4fbb00;
          }
          .midBox2{
            width: 50px;
            height: 50px;
            margin: 0 6px;
            background: url("../images/icon-red.png") no-repeat;
            background-size: cover;
            font-size: 14px;
            text-align: center;
            line-height: 50px;
            color: #e20000;
          }
          .midBox3{
            width: 50px;
            height: 50px;
            margin: 0 6px;
            background: url("../images/icon-chengse.png") no-repeat;
            background-size: cover;
            font-size: 14px;
            text-align: center;
            line-height: 50px;
            color: #e78000;
          }
          .midBox4{
            width: 50px;
            height: 50px;
            margin: 0 6px;
            background: url("../images/icon-blue.png") no-repeat;
            background-size: cover;
            font-size: 14px;
            text-align: center;
            line-height: 50px;
            color: #0088e7;
          }
          .rightBox1{
            border: 3px #4fbb00 solid;
            position: relative;
            width: 122px;
            height: 122px;
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            .dataBox{
              display: flex;
              align-items: center;
              color: #4fbb00;
              font-size: 18px;
              .data{
                font-size: 36px;
                font-weight: bold;
              }
            }
            .dataName{
              font-size: 14px;
              color: #0e0201;
              margin-top: 6px;
              width: 90%;
            }
          }
          .rightBox2{
            border: 3px #e20000 solid;
            position: relative;
            width: 122px;
            height: 122px;
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            .dataBox{
              display: flex;
              align-items: center;
              color: #e20000;
              font-size: 18px;
              .data{
                font-size: 36px;
                font-weight: bold;
              }
            }
            .dataName{
              font-size: 14px;
              color: #0e0201;
              margin-top: 6px;
              width: 90%;
            }
          }
          .rightBox3{
            border: 3px #e78000 solid;
            position: relative;
            width: 122px;
            height: 122px;
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            .dataBox{
              display: flex;
              align-items: center;
              color: #e78000;
              font-size: 18px;
              .data{
                font-size: 36px;
                font-weight: bold;
              }
            }
            .dataName{
              font-size: 14px;
              color: #0e0201;
              margin-top: 6px;
              width: 90%;
            }
          }
          .rightBox4{
            border: 3px #0088e7 solid;
            position: relative;
            width: 122px;
            height: 122px;
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            .dataBox{
              display: flex;
              align-items: center;
              color: #0088e7;
              font-size: 18px;
              .data{
                font-size: 36px;
                font-weight: bold;
              }
            }
            .dataName{
              font-size: 14px;
              color: #0e0201;
              margin-top: 6px;
              width: 90%;
            }
          }
        }

        .share_button {

          display: flex;
          align-items: center;
          justify-content: center;
          width: 275px;
          height: 43px;
          background: linear-gradient(90deg, #FFC248 0%, #FCA736 100%);
          box-shadow: 0px 4px 8px 0px rgba(238, 120, 0, 0.2);
          border-radius: 21px;
          margin: 0 auto;
          margin-top: 10px;
          img {
            width: 20px;
            height: 20px;
            margin-right: 5px;
          }
          font-size: 18px;
          font-weight: 500;
          color: #FFFFFF;
          line-height: 43px;

        }
        .tips {
          padding-top: 12px;
          text-align: center;
          font-size: 16px;
          font-weight: 400;
          color: #888888;
          line-height: 30px;
          text-decoration: underline
        }

      }
      .dataInfo {
        padding: 15px 20px;
        .dataBox {
          display: flex;
          justify-content: space-between;
          font-size: 14px;
          line-height: 25px;
          .dataName {
            font-weight: 500;
            color: #4D7DEF;
          }
          .data {
            font-weight: 500;
            color: #2B344C;
          }
        }
      }
    }
    .chartBox{
      width: 335px;
      height: 234px;
      background: #ffffff;
      border-radius: 6px;
      margin: 0 auto;
      margin-bottom: 10px;
      .title {
        font-size: 18px;
        font-weight: 600;
        color: #4777E7;
        line-height: 25px;
        margin-top: 15px;
        margin-bottom: 18px;
        .bgColor{
          width: 272px;
          height: 8px;
          background: rgba(203,234,255,.8);
          margin: 0 auto;
          margin-top: -9px;
        }
      }
      .chart{
        display: flex;
        align-items: center;
        padding: 0 20px;
        height: 20px;
        margin-bottom: 18px;
        .title{
          color: #31394D;
          font-size: 14px;
          line-height: 20px;
          width: 50px;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          text-align: left;
        }
        .chartNum{
          width: 190px;
          height: 10px;
          background: #E5E9F2;
          border-radius: 5px;
          position: relative;
          margin-right: 10px;
          div{
            width: 190px;
            height: 10px;
            border-radius: 5px;
            position: absolute;
            top: 0;
            left: 0;
          }
          .color1{
            background: #B558F6;
          }
          .color2{
            background: #4072EE;
          }
          .color3{
            background: #29CB97;
          }
        }
        .num{
          font-size: 14px;
          line-height: 20px;
          color: #31394D;
        }
      }
      .tipsBox{
        margin: 0 auto;
        width: 295px;
        height: 40px;
        background: rgba(203, 231, 253, 0.7);
        border-radius: 6px;
        color: #4777E7;
        font-size: 18px;
        display: flex;
        align-items: center;
        justify-content: center;
        img{
          width: 18px;
          height: 18px;
          margin-right: 6px;
        }
      }
    }
    .titleDiv {
      width: 100%;
      background: #ee7800;
      height: 30px;
      line-height: 30px;
      color: #fff;
      font-size: 16px;
    }
    .p2 {
      display: flex;
      padding: 25px 0;
      .leftBox {
        display: flex;
        flex-direction: column;
        padding-left: 20px;
        .rankBox {
          width: 150px;
          height: 25px;
          .rankData {
            height: 25px;
            border-radius: 0 10px 10px 0;
            background: #f963ad;
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #fff;
            line-height: 25px;
            div {
              margin: 0 5px;
            }
          }
        }
        .myBox {
          width: 150px;
          height: 25px;
          margin-top: 5px;
          .myData {
            height: 25px;
            border-radius: 0 10px 10px 0;
            background: #639ef9;
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #fff;
            line-height: 25px;
            min-width: 80px;
            div {
              margin: 0 5px;
            }
          }
        }
        .rankTips {
          font-size: 14px;
          color: #0e0201;
          line-height: 30px;
          text-align: left;
        }
      }
      .rightBox {
        display: flex;
        flex-direction: column;
        padding-left: 20px;
        .rankBox {
          width: 150px;
          height: 25px;
          .rankData {
            height: 25px;
            border-radius: 0 10px 10px 0;
            background: #f963ad;
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #fff;
            line-height: 25px;
            div {
              margin: 0 5px;
            }
          }
        }
        .myBox {
          width: 150px;
          height: 25px;
          margin-top: 5px;
          .myData {
            height: 25px;
            border-radius: 0 10px 10px 0;
            background: #639ef9;
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #fff;
            line-height: 25px;
            min-width: 80px;
            div {
              margin: 0 5px;
            }
          }
        }
        .rankTips {
          font-size: 14px;
          color: #0e0201;
          line-height: 30px;
          text-align: left;
        }
      }
    }

    .assessAgain {
      bottom: 0;
      display: flex;
      align-items: center;
      position: fixed;
      color: #793E0D;
      cursor: pointer;
      margin: 0 auto;
      width: 375px;
      height: 84px;
      background: linear-gradient(90deg, #FEE3B3 0%, #E8C078 100%);
      .wordsBox{
        width: 208px;
        margin: 0 17px 0 20px;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        .words1{
          color: #793E0D;
          font-size: 20px;
          line-height: 28px;
          margin-bottom: 2px;
        }
        .words2{
          color: #793E0D;
          font-size: 16px;
          line-height: 22px;
        }
      }
      .wordsBtn{
        width: 110px;
        height: 36px;
        border-radius: 22px;
        background: linear-gradient(270deg, #793E0D 0%, #C69439 100%);
        color: #ffffff;
        font-size: 16px;
        line-height: 36px;
        text-align: center;
        font-weight: 500;
      }
    }
  }
  .axios.request {
    position: absolute;
    background: rgba(0, 0, 0, .5);
    width: 100%;
    height: 100%;
    top: 0;
    z-index: 2;
    display: flex;
    justify-content: center;
    align-items: center;
    .imgBox {
      background: #fff;
      border-radius: 4px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: 35px;
      .loading1 {
        width: 190px;
        height: 100px;
      }
      .loading2 {
        width: 50px;
        height: 50px;
      }
      div {
        width: 230px;
        margin: 20px 0;
        font-size: 16px;
        line-height: 25px;
      }
    }
  }

  .modalBox {
    position: absolute;
    background: rgba(0, 0, 0, .5);
    width: 100%;
    height: 100%;
    top: 0;
    z-index: 2;
    display: flex;
    justify-content: center;
    align-items: center;
    .imgBox {
      background: #fff;
      border-radius: 4px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: 35px;
      .loading1 {
        width: 190px;
        height: 100px;
      }
      .loading2 {
        width: 50px;
        height: 50px;
      }
      div {
        width: 230px;
        margin: 20px 0;
        font-size: 16px;
        line-height: 25px;
      }
    }
  }
  .modalBoxPreview {
    position: absolute;
    z-index: 2;
    .shadow {
      background: rgba(0, 0, 0, .5);
      width: 100%;
      height: 100%;
      position: fixed;
      top: 0;
      bottom: 0;
      right: 0;
      left: 0;
      margin: auto;
    }
    img {
      position: fixed;
      margin: auto;
      top: 0;
      bottom: 0;
      right: 0;
      left: 0;
      z-index: 99;
      width: 308px;
      height: 548px;
    }

    .tips {
      position: fixed;
      margin: auto;
      font-size: 22px;
      color: #fff;
      bottom: 40px;
      right: 0;
      left: 0;
      z-index: 99;
    }

  }
}
