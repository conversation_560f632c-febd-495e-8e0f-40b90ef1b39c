<template>
  <div class="main" ref="height">
    <div class="headBox" v-show="itemType != 4">
      <div :class="{active : itemType == 1}">1</div>
      <span></span>
      <div :class="{active : itemType == 2}">2</div>
      <span></span>
      <div :class="{active : itemType == 3}">3</div>
    </div>
    <div class="contentBox">
      <div v-show="itemType == 1" class="content">
        <div class="title">
          <img src="../images/icon-question2.png" alt="">
          <div>您的出生年月是？</div>
          <span>*必填</span>
        </div>
        <div class="inputBox">
          <div @click="yearInputClicked" style="display: contents;">
            <van-field class="input" ref="yearInput" input-align="center" type="number" inputmode="decimal"
                       v-model="year" maxlength="4"/>
            <div>年</div>
          </div>
          <div @click="monthInputClicked" style="display: contents;">
            <van-field class="input" ref="monthInput" input-align="center" type="number" inputmode="decimal"
                       maxlength="2" v-model="month"/>
            <div>月</div>
          </div>
        </div>
        <div class="title">
          <img src="../images/icon-question2.png" alt="">
          <div>您的性别是？</div>
          <span>*必填</span>
        </div>
        <div class="selectBox">
          <div :class="{active1 : sexType == 1}" @click="changeSex(1)">男</div>
          <div :class="{active1 : sexType == 2}" @click="changeSex(0)">女</div>
        </div>
        <div class="title">
          <img src="../images/icon-question2.png" alt="">
          <div>您的腰围是？</div>
        </div>
        <div class="inputBox" @click="inputClicked">
          <van-field class="input" input-align="center" ref="waistInput" center type="number" inputmode="decimal"
                     maxlength="5" v-model="waist"/>
          <div>cm</div>
        </div>
        <div class="nextBtn" @click="goNext(1)">下一步</div>
        <div class="pageTips">未填写的非必填项将采用40岁以上人群的平均值进行计算</div>
      </div>
      <div v-show="itemType == 2" class="content">
        <div class="title">
          <img src="../images/icon-question2.png" alt="">
          <div>您的血压是多少？</div>
        </div>
        <div class="inputBox">
          <van-field class="input" placeholder="高压" input-align="center" type="number" inputmode="decimal" v-model="sbp"
                     maxlength="3"/>
          <div>mmHg /</div>
          <van-field class="input" placeholder="低压" input-align="center" type="number" inputmode="decimal" v-model="dbp"
                     maxlength="3"/>
          <div>mmHg</div>
        </div>
        <div class="title">
          <img src="../images/icon-question2.png" alt="">
          <div>您是否在服用降压药？</div>
          <span>*必填</span>
        </div>
        <div class="selectBox">
          <div :class="{active1 : stepDownType == 1}" @click="changeStepDown(1)">是</div>
          <div :class="{active1 : stepDownType == 2}" @click="changeStepDown(0)">否</div>
        </div>
        <div class="title">
          <img src="../images/icon-question2.png" alt="">
          <div>您的空腹血糖是？</div>
        </div>
        <div class="inputBox">
          <van-field class="input" input-align="center" type="number" inputmode="decimal" maxlength="4" v-model="kfxt"/>
          <div>mmol/L</div>
        </div>
        <div class="btnBox">
          <div class="prev" @click="changePage(1)">上一步</div>
          <div class="next" @click="goNext(2)">下一步</div>
        </div>
        <div class="pageTips2">未填写的非必填项将采用40岁以上人群的平均值进行计算</div>
      </div>
      <div v-show="itemType == 3" class="content">
        <div class="title">
          <img src="../images/icon-question2.png" alt="">
          <div>请问您现在抽烟吗？</div>
          <span>*必填</span>
        </div>
        <div class="selectBox">
          <div :class="{active1 : smokeType == 1}" @click="changeSmoke(1)">是</div>
          <div :class="{active1 : smokeType == 2}" @click="changeSmoke(0)">否</div>
        </div>
        <div class="title">
          <img src="../images/icon-question2.png" alt="">
          <div>请问您现在饮酒吗？</div>
          <span>*必填</span>
        </div>
        <div class="selectBox">
          <div :class="{active1 : drinkType == 1}" @click="changeDrink(1)">是</div>
          <div :class="{active1 : drinkType == 2}" @click="changeDrink(0)">否</div>
        </div>
        <div class="title">
          <img src="../images/icon-question2.png" alt="">
          <div>您的甘油三酯是多少？</div>
        </div>
        <div class="inputBox">
          <van-field class="input" input-align="center" type="number" inputmode="decimal" maxlength="7" v-model="gysz"/>
          <div>mmol/L</div>
        </div>
        <div class="title">
          <img src="../images/icon-question2.png" alt="">
          <div>您的PWV（脉搏波传导速度）是多少？</div>
        </div>
        <div class="inputBox">
          <van-field class="input" input-align="center" type="number" inputmode="decimal" maxlength="4" v-model="pwv"/>
          <div>cm/s</div>
        </div>
        <div class="btnBox">
          <div class="prev" @click="changePage(2)">上一步</div>
          <button class="next" @click="submit" :disabled="isDisable">完成</button>
        </div>
        <div class="pageTips2">未填写的非必填项将采用40岁以上人群的平均值进行计算</div>
      </div>
    </div>
    <div v-show="itemType == 4" class="resultBox">

      <div class="bannerBox"></div>
      <div class="box" style="margin-top: -164px;">
        <div class="title">
          评估结果
          <div class="bgColor"></div>
        </div>
        <div class="sumData">
          <div class="content">
            <div v-show="health_level == 1" class="leftBox1">
              <div class="dataBox">
                <div class="data">{{dxzsNum}}</div>
                分
              </div>
              <div class="dataName">代谢指数</div>
            </div>
            <div v-show="health_level == 2" class="leftBox4">
              <div class="dataBox">
                <div class="data">{{dxzsNum}}</div>
                分
              </div>
              <div class="dataName">代谢指数</div>
            </div>
            <div v-show="health_level == 3" class="leftBox3">
              <div class="dataBox">
                <div class="data">{{dxzsNum}}</div>
                分
              </div>
              <div class="dataName">代谢指数</div>
            </div>
            <div v-show="health_level == 4" class="leftBox2">
              <div class="dataBox">
                <div class="data">{{dxzsNum}}</div>
                分
              </div>
              <div class="dataName">代谢指数</div>
            </div>
            <!--良好-->
            <div class="midBox1" v-show="health_level == 1">{{healthDescribe}}</div>
            <!--高危-->
            <div class="midBox2" v-show="health_level == 4">{{healthDescribe}}</div>
            <!--危险-->
            <div class="midBox3" v-show="health_level == 3">{{healthDescribe}}</div>
            <!--一般-->
            <div class="midBox4" v-show="health_level == 2">{{healthDescribe}}</div>
            <div v-show="health_level == 1" class="rightBox1">
              <div class="dataBox">
                <div class="data">{{xxgNum}}</div>
                %
              </div>
              <div class="dataName">3年内发生心血管疾病的概率</div>
            </div>
            <div v-show="health_level == 2" class="rightBox4">
              <div class="dataBox">
                <div class="data">{{xxgNum}}</div>
                %
              </div>
              <div class="dataName">3年内发生心血管疾病的概率</div>
            </div>
            <div v-show="health_level == 3" class="rightBox3">
              <div class="dataBox">
                <div class="data">{{xxgNum}}</div>
                %
              </div>
              <div class="dataName">3年内发生心血管疾病的概率</div>
            </div>
            <div v-show="health_level == 4" class="rightBox2">
              <div class="dataBox">
                <div class="data">{{xxgNum}}</div>
                %
              </div>
              <div class="dataName">3年内发生心血管疾病的概率</div>
            </div>
          </div>
          <div class="share_button" @click="shareImage"><img src="../images/fenxiang.png" alt="">邀请好友评估</div>

          <!--<div class="tips" @click="getHome">重新评估</div>-->
        </div>
      </div>
      <div class="chartBox">
        <div class="title">
          您的疾病发生风险在人群中的水平
          <div class="bgColor"></div>
        </div>
        <div class="chart">
          <div class="title">全国</div>
          <div class="chartNum">
            <div class="color1" :style="{width:nationNum + '%'}"></div>
          </div>
          <div class="num">{{nationNum}}分</div>
        </div>
        <div class="chart">
          <div class="title">{{provinceName}}</div>
          <div class="chartNum">
            <div class="color2" :style="{width:provinceNum + '%'}"></div>
          </div>
          <div class="num">{{provinceNum}}分</div>
        </div>
        <div class="chart">
          <div class="title">我</div>
          <div class="chartNum">
            <div class="color3" :style="{width:dxzsNum + '%'}"></div>
          </div>
          <div class="num">{{dxzsNum}}分</div>
        </div>
        <div class="tipsBox">
          <img src="../images/tishi.png" alt="">
          分数越高，风险越低
        </div>
      </div>
      <div style="height: 100px"></div>
      <div class="assessAgain" @click="goBug">
        <div class="wordsBox">
          <div class="words1">{{dxzsNum>nationNum?'您的心血管状态良好':'您的心血管风险偏高'}}</div>
          <div class="words2">{{dxzsNum>nationNum?'有份适合的产品帮持续保持':'有一份适合的产品推荐'}}</div>
        </div>
        <div class="wordsBtn">立即查看</div>
      </div>
    </div>
    <div class="modalBox" v-show="show == true">
      <div class="imgBox">
        <img src="../images/img-loadding.png" alt="loading1" class="loading1">
        <div>正在为您生成【代谢指数报告】请耐心等待...</div>
        <img src="../images/i-shengchengzhong.png" alt="loading2" class="loading2">
      </div>
    </div>

    <div class="modalBoxPreview" v-if="modalBoxPreview">
      <div class="shadow" @click="closeModalBoxPreview"></div>

      <img
        :src="previewImage"
        alt="loading1" class="loading1">
      <div class="tips">长按图片保存</div>
    </div>

  </div>
</template>

<script>
  import 'vant/lib/field/style'
  import {getSaveData, shareGuoshouImage, getQuestionUserInfo} from '@/api/dxzs'

  export default {
    data() {
      return {
        act_uuid: '',
        modalBoxPreview: false,
        show: false,
        isDisable: false,
        staffcode: '',
        userId: '',
        itemType: 1,
        year: '',
        month: '',
        sexType: '',
        sex: '',
        waist: undefined,
        sbp: undefined,
        dbp: undefined,
        stepDownType: '',
        stepDown: '',
        kfxt: undefined,
        smokeType: '',
        smoke: '',
        drinkType: '',
        drink: '',
        gysz: undefined,
        gysz1: undefined,
        pwv: undefined,
        tYear: '',
        tMonth: '',
        tDay: '',
        dxzsNum: '',
        xxgNum: '',
        provinceName: '',
        provinceNum: '',
        nationNum: '',
        overProvinceNum: '',
        overProvinceNum1: '',
        overNationNum: '',
        overNationNum1: '',
        healthDescribe: '',
        health_level: '',
        type: '',
        previewImage: ''
      }
    },
    created() {
      this.staffcode = this.$route.query.staffcode
      this.source_type = this.$route.query.source_type
      this.type = this.$route.query.type
      if (parseInt(this.type) == 1) {
        this.getUserInfo()
      }
    },
    methods: {
      // 获取用户信息
      async getUserInfo() {
        let data = await getQuestionUserInfo()
        this.year = data.data.birthday.slice(0, 4)
        this.month = data.data.birthday.slice(5, 7)
        if (parseInt(data.data.sex) == 0) {
          this.sex = 0
          this.sexType = 1
        } else if (parseInt(data.data.sex) == 1) {
          this.sex = 1
          this.sexType = 2
        }
        if (parseInt(data.data.sbp) != 0) {
          this.sbp = data.data.sbp
        }
        if (parseInt(data.data.dbp) != 0) {
          this.dbp = data.data.dbp
        }
      },
      // 改变性别
      changeSex(type) {
        if (type == 1) {
          this.sex = 1
          this.sexType = 1
        } else {
          this.sex = 0
          this.sexType = 2
        }
      },
      // 改变是否使用降压药
      changeStepDown(type) {
        if (type == 1) {
          this.stepDown = 1
          this.stepDownType = 1
        } else {
          this.stepDown = 0
          this.stepDownType = 2
        }
      },
      // 改变是否吸烟
      changeSmoke(type) {
        if (type == 1) {
          this.smoke = 1
          this.smokeType = 1
        } else {
          this.smoke = 0
          this.smokeType = 2
        }
      },
      // 改变是否饮酒
      changeDrink(type) {
        if (type == 1) {
          this.drink = 1
          this.drinkType = 1
        } else {
          this.drink = 0
          this.drinkType = 2
        }
      },
      yearInputClicked() {
        if (this.$refs.yearInput) {
          this.$refs.yearInput.focus()
        }
      },
      monthInputClicked() {
        if (this.$refs.monthInput) {
          this.$refs.monthInput.focus()
        }
      },
      inputClicked() {
        if (this.$refs.waistInput) {
          this.$refs.waistInput.focus()
        }
      },
      // 下一页
      goNext(type) {
        if (type === 1) {
          var myDate = new Date()
          var tYear = myDate.getFullYear()
          if (this.year > tYear || parseInt(this.year) < 1900) {
            this.$toast('请输入正确的年份！')
            return false
          }
          if (parseInt(this.month) > 12 || parseInt(this.month) < 1) {
            this.$toast('请输入正确的月数！')
            return false
          }
          if (this.year === '') {
            this.$toast('请输入您的出生年月！')
            return false
          }
          if (this.month === '') {
            this.$toast('请输入您的出生年月！')
            return false
          }
          if (this.sex === '') {
            this.$toast('请选择您的性别！')
            return false
          }
          if (parseInt(this.weight) < 0 || parseInt(this.weight) > 500) {
            this.$toast('请输入正确的腰围数！')
            return false
          }
          this.itemType = 2
        }
        if (type === 2) {
          if (parseInt(this.sbp) < 0 || parseInt(this.sbp) > 400) {
            this.$toast('请输入正确的高压值！')
            return false
          }
          if (parseInt(this.dbp) < 0 || parseInt(this.dbp) > 400) {
            this.$toast('请输入正确的低压值！')
            return false
          }
          if (parseInt(this.kfxt) < 0 || parseInt(this.kfxt) > 100) {
            this.$toast('请输入正确的空腹血糖值！')
            return false
          }
          if (this.stepDown === '') {
            this.$toast('请选择您是否在用降压药！')
            return false
          }
          this.itemType = 3
          // this.$nextTick(() => {
          //   this.$refs.height.style.height = window.innerHeight + 'px'
          // })
        }
      },
      changePage(type) {
        this.itemType = type
      },
      getToFixed(value) {
        value = parseFloat(value).toFixed(2)
        return value
      },
      // 保存
      submit() {
        if (parseInt(this.gysz) < 0 || parseInt(this.gysz) > 9999) {
          this.$toast('请输入正确的甘油三酯值！')
          return false
        }
        if (this.smoke === '') {
          this.$toast('请选择您是否在抽烟！')
          return false
        }
        if (this.drink === '') {
          this.$toast('请选择您是否在饮酒！')
          return false
        }
        this.isDisable = true
        setTimeout(() => {
          this.isDisable = false
        }, 1000)
        this.userId = this.$route.query.userId
        let that = this
        if (this.waist === undefined || this.waist === '' || isNaN(this.waist)) {
          this.waist = undefined
        } else {
          this.waist = this.getToFixed(this.waist)
        }
        if (this.sbp === '') {
          this.sbp = undefined
        }
        if (this.dbp === '') {
          this.dbp = undefined
        }
        if (this.kfxt === undefined || this.kfxt === '' || isNaN(this.kfxt)) {
          this.kfxt = undefined
        } else {
          this.kfxt = this.getToFixed(this.kfxt)
        }
        if (this.pwv === undefined || this.pwv === '' || isNaN(this.pwv)) {
          this.pwv = undefined
        } else {
          this.pwv = this.getToFixed(this.pwv)
        }
        if (this.gysz === undefined || this.gysz === '' || isNaN(this.gysz)) {
          this.gysz = undefined
        } else {
          this.gysz1 = this.getToFixed(this.gysz)
          this.gysz = this.getToFixed(this.gysz) * 88.57
        }
        let data = {
          'user_id': 0,
          'birth_year': this.year,
          'birth_month': Number(this.month),
          'sex': this.sex,
          'waist_circumference': this.waist,
          'has_hypotensor': this.stepDown,
          'high_pressure': this.sbp,
          'low_pressure': this.dbp,
          'FPG': this.kfxt,
          'current_smoker': this.smoke,
          'current_drinker': this.drink,
          'PWV': this.pwv,
          'triglycerides': this.gysz
        }
        getSaveData(data).then(function (res) {
          if (res.status === 0) {
            that.show = true
            setTimeout(function () {
              that.show = false
            }, 1500)
            setTimeout(function () {
              that.itemType = 4
            }, 2000)
            var myDate = new Date()
            that.tYear = myDate.getFullYear()
            that.act_uuid = res.data[0].act_uuid
            that.tMonth = myDate.getMonth() + 1
            that.tDay = myDate.getDate()
            that.dxzsNum = res.data[0].metabolise_index
            that.xxgNum = ((res.data[0].angiocarpy_probability) * 100).toFixed(2)
            that.provinceName = res.data[0].province_name
            that.provinceNum = res.data[0].province_score
            that.nationNum = res.data[0].nation_score
            that.overProvinceNum = ((res.data[0].over_province_percent) * 100).toFixed(2)
            that.overProvinceNum1 = Math.abs(that.overProvinceNum)
            that.overNationNum = ((res.data[0].over_nation_percent) * 100).toFixed(2)
            that.overNationNum1 = Math.abs(that.overNationNum)
            that.healthDescribe = res.data[0].health_describe
            that.health_level = res.data[0].health_level
          }
        })
      },
      closeModalBoxPreview() {
        this.modalBoxPreview = false
      },
      shareImage() {
        let that = this
        this.noDoubleTap(() => {
          if (that.previewImage == '') {
            shareGuoshouImage({'staffcode': that.staffcode, 'uuid': that.act_uuid}).then(res => {
              if (res.status == 0) {
                that.previewImage = res.data.images
                that.modalBoxPreview = true
              }
            })
          } else {
            that.modalBoxPreview = true
          }
        })
      },
      getHome() {
        let userId = this.$route.query.userId
        let type = this.type
        setTimeout(() => {
          this.$router.push({
            name: 'guoshou.dxzsHome',
            query: {'userId': userId, 'staffcode': this.staffcode, 'type': type}
          })
        }, 1000)
      },
      goBug() {

        window.location.href = 'https://healthm.e-chinalife.com/loginWaitShare.html?1=1&signature=false&officialid=gh_c489971b3bca&entranceCode=E10000001017&reffercode=' + this.staffcode
      }
    }
  }
</script>

<style lang="scss" scoped>
  @import "dxzs.scss";
</style>
