.main{
  width: 100%;
  position: relative;
  .headBox{
    width: 100%;
    background: #F6F9FD;
    height: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #398CFF;
    div{
      width: 25px;
      height: 25px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      border: 1px #398CFF solid;
      font-size: 16px;
    }
    .active{
      color: #fff;
      background: #398CFF;
    }
    span{
      width: 15px;
      height: 1px;
      background: #398CFF;
    }
  }
  .contentBox{
    .content{
      margin-top: 10px;
      position: relative;
      .title{
        display: flex;
        margin-top: 30px;
        font-size: 16px;
        justify-content: center;
        img{
          width: 14px;
          height: 14px;
        }
        div{
          color: #999999;
          margin: 0 5px;
        }
        span{
          color: #EB6048;
        }
      }
      .inputBox {
        display: flex;
        justify-content: center;
        margin-top: 15px;
        align-items: flex-end;
        .input {
          width: 100px;
          height: 50px;
          text-align: center;
          border-bottom: 1px #e8e8e8 solid;
          font-size: 24px;
          text-align: center;
        }
        input {
          width: 100px;
          height: 50px;
          text-align: center;
          border-bottom: 1px #e8e8e8 solid;
          font-size: 24px;
        }
        div {
          font-size: 18px;
          color: #262626;
        }
      }
      .selectBox{
        display: flex;
        justify-content: center;
        margin: 30px 0;
        div{
          width: 75px;
          height: 75px;
          text-align: center;
          line-height: 75px;
          border-radius: 50%;
          border: 1px #e6e6e6 solid;
          font-size: 18px;
        }
        .active1{
          background: #398CFF;
          color: #fff;
          border: none;
        }
        div:first-child{
          margin-right: 50px;
        }
      }
      .nextBtn{
        width: 220px;
        height: 50px;
        border-radius: 25px;
        background: #398CFF;
        box-shadow: 0 10px 30px 0 rgba(57,140,255,0.40);
        text-align: center;
        color: #fff;
        line-height: 50px;
        font-size: 18px;
        margin: 0 auto;
        margin-top: 100px;
        cursor: pointer;
      }
      .btnBox{
        display: flex;
        justify-content: center;
        margin-top: 100px;
        margin-bottom: 20px;
        div{
          width: 146px;
          height: 50px;
          line-height: 50px;
          text-align: center;
          font-size: 18px;
          cursor: pointer;
          border-radius: 25px;
        }
        .prev{
          box-shadow: 0 10px 30px 0 rgba(57,140,255,0.40);
          color: #398CFF;
          margin-right: 40px;
        }
        .next{
          background: #398CFF;
          box-shadow: 0 10px 30px 0 rgba(57,140,255,0.40);
          color: #fff;
        }
      }
    }
  }
  .resultBox{
    width: 100%;
    padding: 0;
    display: flex;
    flex-direction: column;
    .bannerBox{
      width: 100%;
      height: 200px;
      background: url("https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/wechat/%E7%B3%96%E5%B0%BF%E7%97%85%E5%8F%91%E7%97%85%E8%AF%84%E4%BC%B0%E7%BB%93%E6%9E%9C%402x.png");
      background-size: cover;
      display: flex;
      flex-direction: column;
      .resultDataBox{
        margin-top: 45px;
        .data1{
          color: #262626;
          font-size: 15px;
          line-height: 22px;
        }
        .data2{
          color: #4FBA00;
          font-size: 26px;
          line-height: 22px;
          margin-top: 8px;
        }
        .data3{
          font-size: 30px;
          color: #398CFF;
          line-height: 28px;
          margin-top: 11px;
          span{
            font-size: 16px;
          }
        }
      }
      .time{
        font-size: 12px;
        color: #FFF;
        margin-top: 33px;
      }
    }
    .p1{
      display: flex;
      flex-direction: column;
      justify-content: center;
      .tipsBox{
        min-height: 120px;
        padding: 0 10px;
        border-bottom: 5px #f3f4f9 solid;
        background: #fff;
        .tipsTop{
          display: flex;
          align-items: center;
          margin-top: 12px;
          margin-bottom: 5px;
          line-height: 26px;
          img{
            width: 20px;
            height: 20px;
          }
          span{
            font-size: 18px;
            color: #398CFF;
            margin-left: 5px;
          }
        }
        .tipsMid{
          color: #262626;
          font-size: 17px;
          line-height: 26px;
          text-align: left;
        }
        .tipsBottom{
          font-size: 12px;
          color: #999;
          margin-top: 5px;
          line-height: 15px;
          text-align: left;
        }
      }
    }
    .dataInfo{
      padding: 0 20px 30px 20px;
      .consultTitle{
        display: flex;
        align-items: center;
        margin-top: 12px;
        margin-bottom: 15px;
        img{
          width: 20px;
          height: 20px;
          margin-right: 5px;
        }
        span{
          font-size: 18px;
          color: #398CFF;
          line-height: 23px;
        }
      }
      .dataBox{
        display: flex;
        justify-content: space-between;
        font-size: 14px;
        line-height: 25px;
        .dataName{
          color: #888888;
        }
        .data{
          color: #0e0201;
        }
      }
    }
    .assessAgain{
      background: #398CFF;
      border-radius: 20px;
      height: 40px;
      line-height: 40px;
      color: #fff;
      font-size: 16px;
      cursor: pointer;
      width: 340px;
      margin: 0 auto;
      margin-bottom: 50px;
    }
  }
}
