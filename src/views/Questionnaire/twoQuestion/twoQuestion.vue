<template>
  <div class="main" ref="height">
    <div class="headBox" v-show="itemType != 3">
      <div :class="{active : itemType == 1}">1</div>
      <span></span>
      <div :class="{active : itemType == 2}">2</div>
    </div>
    <div class="contentBox">
      <div v-show="itemType == 1" class="content">
        <div class="title">
          <img src="../images/icon-questionBlue.png" alt="">
          <div>您的出生年月是？</div>
          <span>*必填</span>
        </div>
        <div class="inputBox">
          <div @click="yearInputClicked" style="display: contents;">
            <van-field class="input" ref="yearInput" input-align="center" type="number" inputmode="decimal"
                       v-model="year" maxlength="4"/>
            <div>年</div>
          </div>
          <div @click="monthInputClicked" style="display: contents;">
            <van-field class="input" ref="monthInput" input-align="center" type="number" inputmode="decimal"
                       maxlength="2" v-model="month"/>
            <div>月</div>
          </div>
        </div>
        <div class="title">
          <img src="../images/icon-questionBlue.png" alt="">
          <div>您的性别是？</div>
          <span>*必填</span>
        </div>
        <div class="selectBox">
          <div :class="{active1 : sexType == 1}" @click="changeSex(1)">男</div>
          <div :class="{active1 : sexType == 2}" @click="changeSex(0)">女</div>
        </div>
        <div class="title">
          <img src="../images/icon-questionBlue.png" alt="">
          <div>您的腰围是？</div>
          <span>*必填</span>
        </div>
        <div class="inputBox">
          <input type="text" oninput="value = value.replace(/[^0-9\.]/g,'')" maxlength="4" v-model="waist">
          <div>cm</div>
        </div>
        <div class="nextBtn" @click="goNext(1)">下一步</div>
      </div>
      <div v-show="itemType == 2" class="content">
        <div class="title">
          <img src="../images/icon-questionBlue.png" alt="">
          <div>您的血压是多少？</div>
          <span>*必填</span>
        </div>
        <div class="inputBox">
          <input type="text" oninput="value = value.replace(/[^0-9]/g,'')" maxlength="4" v-model="sbp">
          <div>mmHg /</div>
          <input type="text" oninput="value = value.replace(/[^0-9]/g,'')" maxlength="4" v-model="dbp">
          <div>mmHg</div>
        </div>
        <div class="title">
          <img src="../images/icon-questionBlue.png" alt="">
          <div>您的体重指数(BMI)是多少？</div>
          <span>*必填</span>
        </div>
        <div class="inputBox">
          <input type="text" oninput="value = value.replace(/[^0-9\.]/g,'')" maxlength="5" v-model="bmi">
        </div>
        <div class="title">
          <img src="../images/icon-questionBlue.png" alt="">
          <div>是否有糖尿病家族病史？</div>
          <span>*必填</span>
        </div>
        <div class="selectBox">
          <div :class="{active1 : isTnbType == 1}" @click="changeIsTnb(1)">是</div>
          <div :class="{active1 : isTnbType == 2}" @click="changeIsTnb(0)">否</div>
        </div>
        <div class="btnBox">
          <div class="prev" @click="changePage(1)">上一步</div>
          <div class="next" @click="goNext(2)">下一步</div>
        </div>
      </div>
    </div>
    <div v-show="itemType == 3" class="resultBox">
      <div class="bannerBox">
        <div class="resultDataBox">
          <div class="data1">糖尿病发病</div>
          <div class="data2">{{report.updown===1?'风险低':'风险高 '}}</div>
          <div class="data3">{{report.score}}<span>分</span></div>
        </div>
        <div class="time">报告时间：{{tYear}}年{{tMonth}}月{{tDay}}日</div>
      </div>
      <div class="p1">
        <div class="tipsBox">
          <div class="tipsTop">
            <img src="../images/<EMAIL>"/>
            <span>健康小贴士</span>
          </div>
          <div class="tipsMid">{{report.conclusion}}</div>
          <div class="tipsBottom">声明：该评估结果不作为任何诊断结果或者依据，仅供个人参考</div>
        </div>
      </div>
      <div class="dataInfo">
        <div class="consultTitle">
          <img src="../images/<EMAIL>"/>
          <span>评估参考数据</span>
        </div>
        <div class="dataBox">
          <div class="dataName">出生年月</div>
          <div class="data">{{year}}年{{month}}月</div>
        </div>
        <div class="dataBox">
          <div class="dataName">性别</div>
          <div class="data">{{sex == 1 ? '男' : '女'}}</div>
        </div>
        <div class="dataBox">
          <div class="dataName">腰围</div>
          <div class="data">{{waist == '' ? '数据缺失' : waist}}cm</div>
        </div>
        <div class="dataBox">
          <div class="dataName">血压</div>
          <div class="data">{{sbp == '' ? '数据缺失' : sbp}}/{{dbp == '' ? '数据缺失' : dbp}}mmHg</div>
        </div>
        <div class="dataBox">
          <div class="dataName">体重指数（BMI）</div>
          <div class="data">{{bmi == '' ? '数据缺失' : bmi}}</div>
        </div>
        <div class="dataBox">
          <div class="dataName">糖尿病家族史</div>
          <div class="data">{{isTnb == 1 ? '是' : '否'}}</div>
        </div>
      </div>
      <div class="assessAgain" @click="getHome">重新评估</div>
    </div>
  </div>
</template>

<script>
  import 'vant/lib/field/style'
  import {questionSecondBlood} from '@/api/dxzs'

  export default {
    data() {
      return {
        itemType: 1,
        year: '',
        month: '',
        sexType: '',
        sex: '',
        waist: '',
        sbp: '',
        dbp: '',
        bmi: '',
        isTnb: '',
        isTnbType: '',
        tYear: '',
        tMonth: '',
        tDay: '',
        report:{}
      }
    },
    created() {
      this.$nextTick(() => {
        // this.$refs.height.style.height = window.innerHeight + 'px'
      })
    },
    methods: {
      // 改变性别
      changeSex(type) {
        if (type == 1) {
          this.sex = 1
          this.sexType = 1
        } else {
          this.sex = 0
          this.sexType = 2
        }
      },
      // 改变是否糖尿病
      changeIsTnb(type) {
        if (type == 1) {
          this.isTnb = 1
          this.isTnbType = 1
        } else {
          this.isTnb = 0
          this.isTnbType = 2
        }
      },
      yearInputClicked() {
        if (this.$refs.yearInput) {
          this.$refs.yearInput.focus()
        }
      },
      monthInputClicked() {
        if (this.$refs.monthInput) {
          this.$refs.monthInput.focus()
        }
      },
      // 下一页
      goNext(type) {
        if (type === 1) {
          let myDate = new Date()
          let tYear = myDate.getFullYear()
          if (this.year > tYear || parseInt(this.year) < 1900) {
            this.$toast('请输入正确的年份！')
            return false
          }
          if (parseInt(this.month) > 12 || parseInt(this.month) < 1) {
            this.$toast('请输入正确的月数！')
            return false
          }
          if (this.year === '') {
            this.$toast('请输入您的出生年月！')
            return false
          }
          if (this.month === '') {
            this.$toast('请输入您的出生年月！')
            return false
          }
          if (this.sex === '') {
            this.$toast('请选择您的性别！')
            return false
          }
          if (this.waist === '') {
            this.$toast('请输入您的腰围！')
            return false
          }
          if (parseInt(this.waist) < 0 || parseInt(this.waist) > 500) {
            this.$toast('请输入正确的腰围数！')
            return false
          }
          this.itemType = 2
        }
        if (type === 2) {
          let myDate = new Date()
          this.tYear = myDate.getFullYear()
          this.tMonth = myDate.getMonth() + 1
          this.tDay = myDate.getDate()
          if (parseInt(this.sbp) < 0 || parseInt(this.sbp) > 400) {
            this.$toast('请输入正确的高压值！')
            return false
          }
          if (parseInt(this.dbp) < 0 || parseInt(this.dbp) > 400) {
            this.$toast('请输入正确的低压值！')
            return false
          }
          if (this.sbp === '') {
            this.$toast('请输入您的高压值！')
            return false
          }
          if (this.dbp === '') {
            this.$toast('请输入您的低压值！')
            return false
          }
          if (this.bmi === '') {
            this.$toast('请输入您的bmi！')
            return false
          }
          if (this.isTnb === '') {
            this.$toast('请选择您是否有糖尿病家族史！')
            return false
          }

          let that = this
          let data = {
            'user_id': 0,
            'year': this.year,
            'bmi':this.bmi,
            'month': Number(this.month),
            'sex': this.sex,
            'waist': this.waist,
            'sbp': this.sbp,
            'dbp': this.dbp,
            'is_history': this.isTnb
          }
          questionSecondBlood(data).then(function (res) {
            if (res.status === 0) {
              that.report=res.data
              that.itemType = 3
            }
          })

        }
      },
      changePage(type) {
        this.itemType = type
      },
      getHome() {
        this.$router.push({
          name: 'twoTnbHome',
          query: {}
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  @import "twoQuestion.scss";
</style>
