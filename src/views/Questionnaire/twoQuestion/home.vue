<template>
  <div class="main">
    <div class="content">
     <div class="contentBox">
        <div class="contentText">
          2型糖尿病是一种常见的慢性病，患病过程是一个循序渐进的过程。中国2型糖尿病风险评估表，在一定程度上能够对糖尿病未患病人群的患病风险进行评估，能够识别出个体的患病风险程度。本工具仅用于糖尿病病风险的初步评估，不能代替临床诊断，具体治疗措施请咨询专业医师。本评估适用于20~74岁年龄段人群。
        </div>
        <div class="btn" @click="getAssess()">开始评估</div>
        <!-- <div class="tips">填写问卷0/10</div> -->
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    data() {
      return {
        userId: '',
        source_type: '', // 1来源是控糖营小程序 2来源是管家app
        type: 2
      }
    },
    created() {
      this.source_type = this.$route.query.source_type
      this.userId = this.$route.query.userId
      this.type = this.$route.query.type
    },
    methods: {
      getAssess() {
        let type = this.type
        this.$router.push({
          name: 'twoTnb',
          query: { 'userId': this.userId, 'type': type, 'source_type': this.source_type }
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  .content{

  }
  .contentBox{
    background-image: url(https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/wechat/zzmedSugarController/v1.3.0/2xing.png);
    width: 350px;
    height: 620px;
    margin: 0 auto;
    margin-top: 20px;
    position: relative;
    background-size: 350px 620px;
  }
  .contentText{
    width: 270px;
    height: 400px;
    border-radius: 10px;
    position: absolute;
    top: 150px;
    left: 25px;
    padding: 50px 13px 0 15px;
    font-size: 16px;
    color: #333;
    line-height: 24px;
    word-break: break-all;
  }
  .btn{
    position: absolute;
    width: 230px;
    height: 40px;
    border-radius: 20px;
    color: #fff;
    background: #4C7DEF;
    text-align: center;
    line-height: 40px;
    font-size: 18px;
    z-index: 2;
    bottom: 70px;
    left: 0;
    right: 0;
    margin: auto;
  }
</style>
