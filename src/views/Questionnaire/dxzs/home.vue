<template>
  <div class="main">
    <div class="content">
      <img src="../images/banner.png" alt="" class="banner">
      <div class="contentBox">
        <div class="contentText">
          代谢性疾病是一种全身疾病，涉及血糖、血脂、血压等多个方面的问题。代谢综合征包含糖尿病、糖耐量异常、肥胖、高脂血症、高血压等五大疾病。这些疾病都会引发包括心梗和脑中风在内的心脑血管疾病。瑞宁<sup>TM</sup>代谢指数是基于全国大规模流行病学研究数据，根据您的血压、血糖、肥胖、血脂等指标计算所得，代表了您未来3年里患心脑血管疾病的风险。分数越低代表代谢指数健康状态越差。
        </div>
        <div class="btn" @click="getAssess()">开始评估</div>
        <!-- <div class="tips">填写问卷0/10</div> -->
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    data() {
      return {
        userId: '',
        source_type: '', // 1来源是控糖营小程序 2来源是管家app
        type: 2
      }
    },
    created() {
      this.source_type = this.$route.query.source_type
      this.userId = this.$route.query.userId
      this.type = this.$route.query.type
    },
    methods: {
      getAssess() {
        let type = this.type
        this.$router.push({
          name: 'dxzs',
          query: { 'userId': this.userId, 'type': type, 'source_type': this.source_type }
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  @import "home.scss";
</style>
