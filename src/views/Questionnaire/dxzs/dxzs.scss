.main{
  width: 100%;
  height: 100%;
  position: relative;
  .headBox{
    width: 100%;
    background: #E97B29;
    height: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    div{
      width: 25px;
      height: 25px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      border: 1px #fff solid;
      font-size: 16px;
    }
    .active{
      color: #E97B29;
      background: #fff;
    }
    span{
      width: 15px;
      height: 1px;
      background: #fff;
    }
  }
  .contentBox{
    .content{
      padding-top: 10px;
      position: relative;
      .title{
        display: flex;
        padding-top: 30px;
        font-size: 16px;
        justify-content: center;
        img{
          width: 14px;
          height: 14px;
        }
        div{
          color: #999999;
          margin: 0 5px;
        }
        span{
          color: #EB6048;
        }
      }
      .inputBox{
        display: flex;
        justify-content: center;
        margin-top: 15px;
        align-items: flex-end;
        input{
          width: 100px;
          height: 50px;
          text-align: center;
          border-bottom: 1px #e8e8e8 solid;
          font-size: 24px;
        }
        div{
          font-size: 18px;
          color: #262626;
        }
      }
      .selectBox{
        display: flex;
        justify-content: center;
        margin: 30px 0;
        div{
          width: 75px;
          height: 75px;
          text-align: center;
          line-height: 75px;
          border-radius: 50%;
          border: 1px #e6e6e6 solid;
          font-size: 18px;
        }
        .active1{
          background: #E97B29;
          color: #fff;
          border: none;
        }
        div:first-child{
          margin-right: 50px;
        }
      }
      .nextBtn{
        width: 220px;
        height: 50px;
        border-radius: 25px;
        background: #E97B29;
        text-align: center;
        color: #fff;
        line-height: 50px;
        font-size: 18px;
        margin: 0 auto;
        margin-top: 100px;
        cursor: pointer;
      }
      .btnBox{
        display: flex;
        justify-content: center;
        margin-top: 100px;
        padding-bottom: 20px;
        div{
          width: 146px;
          height: 50px;
          line-height: 50px;
          text-align: center;
          font-size: 18px;
          cursor: pointer;
          border-radius: 25px;
        }
        .prev{
          box-shadow: (0 0 20px rgba(186,131,94,.21));
          color: #ee7800;
          margin-right: 40px;
        }
        .next{
          width: 146px;
          height: 50px;
          background: #E97B29;
          color: #fff;
          border: none;
          border-radius: 25px;
          font-size: 18px;
        }
      }
      .pageTips{
        font-size: 12px;
        color: #999;
        margin: 20px 0;
      }
      .pageTips2{
        font-size: 12px;
        color: #999;
        margin-bottom: 20px;
      }
    }
  }
  .resultBox{
    width: 100%;
    padding: 0;
    display: flex;
    flex-direction: column;
    .bannerBox{
      width: 100%;
      height: 150px;
      background: url("../images/banner-result.png") no-repeat;
      background-size: cover;
      position: relative;
      .time{
        position: absolute;
        font-size: 11px;
        color: #fff;
        left: 40px;
        bottom: 50px;
      }
    }
    .p1{
      display: flex;
      flex-direction: column;
      justify-content: center;
      .content{
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 30px 0;
        .leftBox3{
          border: 3px #ee7800 solid;
          position: relative;
          width: 130px;
          height: 130px;
          border-radius: 50%;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          .dataBox{
            display: flex;
            align-items: center;
            color: #ee7800;
            font-size: 18px;
            .data{
              font-size: 36px;
              font-weight: bold;
            }
          }
          .dataName{
            font-size: 14px;
            color: #0e0201;
            margin-top: 16px;
          }
        }
        .leftBox1{
          border: 3px #4fbb00 solid;
          position: relative;
          width: 130px;
          height: 130px;
          border-radius: 50%;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          .dataBox{
            display: flex;
            align-items: center;
            color: #4fbb00;
            font-size: 18px;
            .data{
              font-size: 36px;
              font-weight: bold;
            }
          }
          .dataName{
            font-size: 14px;
            color: #0e0201;
            margin-top: 16px;
          }
        }
        .leftBox2{
          border: 3px #e20000 solid;
          position: relative;
          width: 130px;
          height: 130px;
          border-radius: 50%;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          .dataBox{
            display: flex;
            align-items: center;
            color: #e20000;
            font-size: 18px;
            .data{
              font-size: 36px;
              font-weight: bold;
            }
          }
          .dataName{
            font-size: 14px;
            color: #0e0201;
            margin-top: 16px;
          }
        }
        .leftBox4{
          border: 3px #0088e7 solid;
          position: relative;
          width: 130px;
          height: 130px;
          border-radius: 50%;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          .dataBox{
            display: flex;
            align-items: center;
            color: #0088e7;
            font-size: 18px;
            .data{
              font-size: 36px;
              font-weight: bold;
            }
          }
          .dataName{
            font-size: 14px;
            color: #0e0201;
            margin-top: 16px;
          }
        }
        .midBox1{
          width: 50px;
          height: 50px;
          margin: 0 10px;
          background: url("../images/icon-green.png") no-repeat;
          background-size: cover;
          font-size: 14px;
          text-align: center;
          line-height: 50px;
          color: #4fbb00;
        }
        .midBox2{
          width: 50px;
          height: 50px;
          margin: 0 10px;
          background: url("../images/icon-red.png") no-repeat;
          background-size: cover;
          font-size: 14px;
          text-align: center;
          line-height: 50px;
          color: #e20000;
        }
        .midBox3{
          width: 50px;
          height: 50px;
          margin: 0 10px;
          background: url("../images/icon-chengse.png") no-repeat;
          background-size: cover;
          font-size: 14px;
          text-align: center;
          line-height: 50px;
          color: #e78000;
        }
        .midBox4{
          width: 50px;
          height: 50px;
          margin: 0 10px;
          background: url("../images/icon-blue.png") no-repeat;
          background-size: cover;
          font-size: 14px;
          text-align: center;
          line-height: 50px;
          color: #0088e7;
        }
        .rightBox1{
          border: 3px #4fbb00 solid;
          position: relative;
          width: 130px;
          height: 130px;
          border-radius: 50%;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          .dataBox{
            display: flex;
            align-items: center;
            color: #4fbb00;
            font-size: 18px;
            .data{
              font-size: 36px;
              font-weight: bold;
            }
          }
          .dataName{
            font-size: 14px;
            color: #0e0201;
            margin-top: 6px;
            width: 90%;
          }
        }
        .rightBox2{
          border: 3px #e20000 solid;
          position: relative;
          width: 130px;
          height: 130px;
          border-radius: 50%;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          .dataBox{
            display: flex;
            align-items: center;
            color: #e20000;
            font-size: 18px;
            .data{
              font-size: 36px;
              font-weight: bold;
            }
          }
          .dataName{
            font-size: 14px;
            color: #0e0201;
            margin-top: 6px;
            width: 90%;
          }
        }
        .rightBox3{
          border: 3px #e78000 solid;
          position: relative;
          width: 130px;
          height: 130px;
          border-radius: 50%;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          .dataBox{
            display: flex;
            align-items: center;
            color: #e78000;
            font-size: 18px;
            .data{
              font-size: 36px;
              font-weight: bold;
            }
          }
          .dataName{
            font-size: 14px;
            color: #0e0201;
            margin-top: 6px;
            width: 90%;
          }
        }
        .rightBox4{
          border: 3px #0088e7 solid;
          position: relative;
          width: 130px;
          height: 130px;
          border-radius: 50%;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          .dataBox{
            display: flex;
            align-items: center;
            color: #0088e7;
            font-size: 18px;
            .data{
              font-size: 36px;
              font-weight: bold;
            }
          }
          .dataName{
            font-size: 14px;
            color: #0e0201;
            margin-top: 6px;
            width: 90%;
          }
        }
      }
      .tips{
        text-align: center;
        font-size: 12px;
        margin-bottom: 15px;
      }
    }
    .titleDiv{
      width: 100%;
      background: #ee7800;
      height: 30px;
      line-height: 30px;
      color: #fff;
      font-size: 16px;
    }
    .p2{
      display: flex;
      padding: 25px 0;
      .leftBox{
        display: flex;
        flex-direction: column;
        padding-left: 20px;
        .rankBox{
          width: 150px;
          height: 25px;
          .rankData{
            height: 25px;
            border-radius: 0 10px 10px 0;
            background: #f963ad;
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #fff;
            line-height: 25px;
            div{
              margin: 0 5px;
            }
          }
        }
        .myBox{
          width: 150px;
          height: 25px;
          margin-top: 5px;
          .myData{
            height: 25px;
            border-radius: 0 10px 10px 0;
            background: #639ef9;
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #fff;
            line-height: 25px;
            min-width: 80px;
            div{
              margin: 0 5px;
            }
          }
        }
        .rankTips{
          font-size: 14px;
          color: #0e0201;
          line-height: 30px;
          text-align: left;
        }
      }
      .rightBox{
        display: flex;
        flex-direction: column;
        padding-left: 20px;
        .rankBox{
          width: 150px;
          height: 25px;
          .rankData{
            height: 25px;
            border-radius: 0 10px 10px 0;
            background: #f963ad;
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #fff;
            line-height: 25px;
            div{
              margin: 0 5px;
            }
          }
        }
        .myBox{
          width: 150px;
          height: 25px;
          margin-top: 5px;
          .myData{
            height: 25px;
            border-radius: 0 10px 10px 0;
            background: #639ef9;
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #fff;
            line-height: 25px;
            min-width: 80px;
            div{
              margin: 0 5px;
            }
          }
        }
        .rankTips{
          font-size: 14px;
          color: #0e0201;
          line-height: 30px;
          text-align: left;
        }
      }
    }
    .dataInfo{
      padding: 30px 20px;
      .dataBox{
        display: flex;
        justify-content: space-between;
        font-size: 14px;
        line-height: 25px;
        .dataName{
          color: #888888;
        }
        .data{
          color: #0e0201;
        }
      }
    }
    .assessAgain{
      background: #E97B29;
      border-radius: 20px;
      height: 40px;
      line-height: 40px;
      color: #fff;
      font-size: 16px;
      cursor: pointer;
      width: 340px;
      margin: 0 auto;
      margin-bottom: 50px;
    }
  }
  .modalBox{
    position: absolute;
    background: rgba(0,0,0,.5);
    width: 100%;
    height: 100%;
    top: 0;
    z-index: 2;
    display: flex;
    justify-content: center;
    align-items: center;
    .imgBox{
      background: #fff;
      border-radius: 4px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: 35px;
      .loading1{
        width: 190px;
        height: 100px;
      }
      .loading2{
        width: 50px;
        height: 50px;
      }
      div{
        width: 230px;
        margin: 20px 0;
        font-size: 16px;
        line-height: 25px;
      }
    }
  }
}