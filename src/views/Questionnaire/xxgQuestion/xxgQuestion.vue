<template>
  <div class="main" ref="height">
    <div class="headBox" v-show="itemType != 3">
      <div :class="{active : itemType == 1}">1</div>
      <span></span>
      <div :class="{active : itemType == 2}">2</div>
    </div>
    <div class="contentBox">
      <div v-show="itemType == 1" class="content">
        <div class="title">
          <img src="../images/icon-questionBlue.png" alt="">
          <div>您的出生年月是？</div>
          <span>*必填</span>
        </div>
        <div class="inputBox">
          <input type="span" oninput="value = value.replace(/[^0-9]/g,'')" maxlength="4" v-model="year">
          <div>年</div>
          <input type="span" oninput="value = value.replace(/[^0-9]/g,'')" maxlength="2" v-model="month">
          <div>月</div>
        </div>
        <div class="title">
          <img src="../images/icon-questionBlue.png" alt="">
          <div>您的性别是？</div>
          <span>*必填</span>
        </div>
        <div class="selectBox">
          <div :class="{active1 : sex === 0}" @click="sex = 0">男</div>
          <div :class="{active1 : sex == 1}" @click="sex = 1">女</div>
        </div>
        <div class="title">
          <img src="../images/icon-questionBlue.png" alt="">
          <div>您的血压是多少？</div>
          <span>*必填</span>
        </div>
        <div class="inputBox">
          <input type="span" oninput="value = value.replace(/[^0-9]/g,'')" maxlength="4" v-model="sbp">
          <div>mmHg /</div>
          <input type="span" oninput="value = value.replace(/[^0-9]/g,'')" maxlength="4" v-model="dbp">
          <div>mmHg</div>
        </div>
        <div class="title">
          <img src="../images/icon-questionBlue.png" alt="">
          <div>您的身高是多少？</div>
          <span>*必填</span>
        </div>
        <div class="inputBox">
          <input type="span" oninput="value = value.replace(/[^0-9\.]/g,'')" maxlength="5" v-model="height">
          <div>cm</div>
        </div>
        <div class="title">
          <img src="../images/icon-questionBlue.png" alt="">
          <div>您的体重是多少？</div>
          <span>*必填</span>
        </div>
        <div class="inputBox">
          <input type="span" oninput="value = value.replace(/[^0-9\.]/g,'')" maxlength="5" v-model="weight">
          <div>kg</div>
        </div>
        <div class="nextBtn" @click="goNext(1)">下一步</div>
      </div>
      <div v-show="itemType == 2" class="content">
        <div class="title">
          <img src="../images/icon-questionBlue.png" alt="">
          <div>您的总胆固醇是多少？</div>
          <span>*必填</span>
        </div>
        <div class="inputBox">
          <input type="span" oninput="value = value.replace(/[^0-9\.]/g,'')" maxlength="5" v-model="dgc">
          <div>mmol/L</div>
        </div>
        <div class="title">
          <img src="../images/icon-questionBlue.png" alt="">
          <div>您是否吸烟？</div>
          <span>*必填</span>
        </div>
        <div class="selectBox">
          <div :class="{active1 : smoke === 1}" @click="smoke = 1">是</div>
          <div :class="{active1 : smoke === 0}" @click="smoke = 0">否</div>
        </div>
        <div class="title">
          <img src="../images/icon-questionBlue.png" alt="">
          <div>是否有糖尿病家族病史？</div>
          <span>*必填</span>
        </div>
        <div class="selectBox">
          <div :class="{active1 : isTnb === 1}" @click="isTnb = 1">是</div>
          <div :class="{active1 : isTnb === 0}" @click="isTnb = 0">否</div>
        </div>
        <div class="btnBox">
          <div class="prev" @click="changePage(1)">上一步</div>
          <div class="next" @click="goNext(2)">下一步</div>
        </div>
      </div>
    </div>
    <div v-show="itemType == 3" class="resultBox">
      <div class="bannerBox">
        <div class="tips">未来10年</div>
        <div class="resultDataBox">
          <div class="data1">缺血性心血管病</div>
          <div class="data2">{{ backData.updown === 1 ? '低危' : backData.updown === 2 ? '中危' : '高危' }}</div>
          <div class="data3">{{ backData.percent_ten }}<span>%</span></div>
        </div>
        <div class="time">报告时间：{{ backData.report_time }}</div>
      </div>
      <div class="p2">
        <div class="tipsBox">
          <div class="tipsTop">
            <img src="../images/<EMAIL>" />
            <span>未来10年发生缺血性心血管病的危险</span>
          </div>
          <div class="tipsContent">
            <div class="avgBox">
              <div class="background">
                <span class="backgroundContent"></span>
              </div>
              <span class="avgName">平均</span>
            </div>
            <div class="idealBox">
              <div class="background">
                <span class="backgroundContent1"></span>
              </div>
              <span class="idealName">理想</span>
            </div>
            <div class="tipsContentRigth">
              <div>同性别、年龄人群平均水平的
                <span style="color: #398CFF;font-size: 28px;">{{ backData.avg_risk }}</span>
                <span style="color: #398CFF;font-size: 12px;">倍</span>
              </div>
              <div>同性别、年龄人群理想水平的
                <span style="color: #398CFF;font-size: 28px;">{{ backData.low_risk }}</span>
                <span style="color: #398CFF;font-size: 12px;">倍</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="p1">
        <div class="tipsBox">
          <div class="tipsTop">
            <img src="../images/<EMAIL>" />
            <span>健康小贴士</span>
          </div>
          <div class="tipsMid">{{ backData.conclusion }}</div>
          <div class="tipsBottom">声明：该评估结果不作为任何诊断结果或者依据，仅供个人参考</div>
        </div>
      </div>
      <div class="dataInfo">
        <div class="consultTitle">
          <img src="../images/<EMAIL>" />
          <span>评估参考数据</span>
        </div>
        <div class="dataBox">
          <div class="dataName">出生年月</div>
          <div class="data">{{ backData.data.brith }}</div>
        </div>
        <div class="dataBox">
          <div class="dataName">性别</div>
          <div class="data">{{ backData.data.sex }}</div>
        </div>
        <div class="dataBox">
          <div class="dataName">血压</div>
          <div class="data">{{ backData.data.bp }}</div>
        </div>
        <div class="dataBox">
          <div class="dataName">体重指数（BMI）</div>
          <div class="data">{{ backData.data.bmi }}</div>
        </div>
        <div class="dataBox">
          <div class="dataName">总胆固醇</div>
          <div class="data">{{ backData.data.cholesterol }}</div>
        </div>
        <div class="dataBox">
          <div class="dataName">是否吸烟</div>
          <div class="data">{{ backData.data.is_smoke }}</div>
        </div>
        <div class="dataBox">
          <div class="dataName">是否有糖尿病家族史</div>
          <div class="data">{{ backData.data.diabetes }}</div>
        </div>
      </div>
      <div class="assessAgain" @click="getHome">重新评估</div>
    </div>
  </div>
</template>

<script>
  import 'vant/lib/field/style'
  import { questionIcvdquestion } from '@/api/dxzs'

  export default {
    data() {
      return {
        itemType: 1,
        year: '',
        month: '',
        sex: '',
        sbp: '',
        dbp: '',
        height: '',
        weight: '',
        dgc: '',
        smoke: '',
        isTnb: '',
        tYear: '',
        tMonth: '',
        tDay: '',
        backData: {
          avg_risk: '',
          conclusion: '',
          data: {
            bmi: '',
            bp: '',
            brith: '',
            cholesterol: '',
            diabetes: '',
            is_smoke: '',
            sex: ''
          },
          low_risk: '',
          percent_ten: '',
          report_time: '',
          score: '',
          updown: ''
        }
      }
    },
    created() {
      this.userId = this.$route.query.userId || ''
      this.$nextTick(() => {
        // this.$refs.height.style.height = window.innerHeight + 'px'
      })
    },
    methods: {
      // 下一页
      goNext(type) {
        if (type === 1) {
          let myDate = new Date()
          let tYear = myDate.getFullYear()
          if (this.year > tYear || parseInt(this.year) < 1900) {
            this.$toast('请输入正确的年份！')
            return false
          }
          if (parseInt(this.month) > 12 || parseInt(this.month) < 1) {
            this.$toast('请输入正确的月数！')
            return false
          }
          if (this.year === '') {
            this.$toast('请输入您的出生年月！')
            return false
          }
          if (this.month === '') {
            this.$toast('请输入您的出生年月！')
            return false
          }
          if (this.sex === '') {
            this.$toast('请选择您的性别！')
            return false
          }
          if (parseInt(this.sbp) < 0 || parseInt(this.sbp) > 400) {
            this.$toast('请输入正确的高压值！')
            return false
          }
          if (parseInt(this.dbp) < 0 || parseInt(this.dbp) > 400) {
            this.$toast('请输入正确的低压值！')
            return false
          }
          if (this.sbp === '') {
            this.$toast('请输入您的高压值！')
            return false
          }
          if (this.dbp === '') {
            this.$toast('请输入您的低压值！')
            return false
          }
          if (this.weight === '') {
            this.$toast('请输入您的体重！')
            return false
          }
          if (this.height === '') {
            this.$toast('请输入您的身高！')
            return false
          }
          this.itemType = 2
        }
        if (type === 2) {
          let myDate = new Date()
          this.tYear = myDate.getFullYear()
          this.tMonth = myDate.getMonth() + 1
          this.tDay = myDate.getDate()
          if (this.dgc === '') {
            this.$toast('请填写您的总胆固醇是多少！')
            return false
          }
          if (this.smoke === '') {
            this.$toast('请选择您是否吸烟！')
            return false
          }
          if (this.isTnb === '') {
            this.$toast('请选择您是否有糖尿病家族史！')
            return false
          }
          // bmi 计算保留一位小数
          let height = this.height / 100
          let bmi = (this.weight / (height * height)).toFixed(1)
          // 提交问卷
          questionIcvdquestion({
            year: this.year,
            month: this.month,
            bmi: bmi,
            sbp: this.sbp,
            dbp: this.dbp,
            cholesterol: this.dgc, // 胆固醇
            is_smoke: this.smoke,
            diabetes: this.isTnb, // 糖尿病
            sex: this.sex,
            user_id: this.userId
          }).then((res) => {
            if (res.status === 0) {
              this.itemType = 3
              this.backData = res.data
            } else {
              this.$toast(res.msg)
            }
          }).catch((err) => {
            this.$toast(err)
          })
        }
      },
      changePage(type) {
        this.itemType = type
      },
      getHome() {
        this.$router.go(0)
      }
    }
  }
</script>

<style lang="scss" scoped>
  @import "xxgQuestion.scss";
</style>
