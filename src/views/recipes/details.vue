<template>
  <div class="recipesDetails">
    <van-image class="img" :src="details.image" fit="cover" alt="" />
    <div class="main">
      <div class="content">
        <div class="title">{{ details.dish_name }}</div>
        <div class="weight">{{ details.weight }}</div>
        <div class="list">
          <div class="item" v-for="item in energyList" :key="item">
            <div class="label">{{ item.label }}</div>
            <div class="valLine">
              <span class="val">{{ item.val }}</span>
              <span class="unit">{{ item.unit }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="content">
        <div class="title">适应性评价</div>
        <div class="desc">{{ details.food_comment }}</div>
      </div>
      <div class="content">
        <div class="title">食用建议</div>
        <div class="desc">{{ details.dietary_advice }}</div>
      </div>
      <div class="content">
        <div class="title">烹饪建议</div>
        <div class="desc">{{ details.cooking_advice }}</div>
      </div>
      <div class="tips">
        {{ details.tips }}
      </div>
    </div>
  </div>
</template>

<script>
import { getRecipesDetail } from '@/api/recipes'
export default {
  data() {
    return {
      details: {},
      energyList: []
    }
  },
  methods: {
    getRecipesDetailFun() {
      getRecipesDetail({recipe_detail_id: this.$route.query.id}).then(res => {
        if(res.status == 0){
          this.details = res.data
          this.energyList = [
            { label: "热量", val: res.data.calorie || 0, unit: "kcal" },
            { label: "蛋白质", val: res.data.protein || 0, unit: "g" },
            { label: "脂肪", val: res.data.fat || 0, unit: "g" },
            { label: "碳水化合物", val: res.data.carbohydrate || 0, unit: "g" },
            { label: "纤维", val: res.data.fiber || 0, unit: "g" },
            { label: "糖", val: res.data.sugar || 0, unit: "g" },
          ]
        }
      })
    }
  },
  created() {

  },
  mounted() {
    this.getRecipesDetailFun()
  }
}
</script>

<style lang='scss'>
.recipesDetails {
  width: 100%;
  min-height: 100vh;
  background: #F8F9FB;

  .img {
    width: 100%;
    height: 280px;
  }

  .main {
    padding: 0 15px 15px;
    box-sizing: border-box;
    width: 100%;
    height: 100%;
    text-align: left;
  }

  .title {
    color: #333333;
    font-size: 20px;
    font-weight: 500;
    line-height: 100%;
  }

  .weight {
    color: #666666;
    font-size: 16px;
    line-height: 24px;
  }

  .list {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;

    .item {
      width: 30%;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      border-radius: 10px;
      background: #F8F9FB;
      margin-top: 10px;
      padding: 10px;

      .label {
        color: #666666;
        font-size: 15px;
      }

      .valLine {
        color: #333333;
        font-weight: 500;
        line-height: normal;
        margin-top: 2px;

        .val {
          font-size: 22px;
          line-height: 24px;
        }

        .unit {
          font-size: 14px;
        }
      }
    }
  }

  .content {
    width: 100%;
    background: white;
    border-radius: 10px;
    padding: 15px 10px;
    box-sizing: border-box;
    margin-top: 12px;
  }
  .desc{
    color: #666666;
    line-height: 24px;
    font-size: 18px;
    margin-top: 10px;
  }
  .tips {
    color: #BBB8B8;
    font-size: 16px;
    text-align: center;
    line-height: 20px;
    padding: 20px 0;
  }
}
</style>