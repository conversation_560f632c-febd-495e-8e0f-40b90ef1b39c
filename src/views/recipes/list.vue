<template>
  <div class="recipesList">
    <div class="tips">
      {{ tips }}
    </div>
    <div class="content">
      <div class="titleLine">
        <img class="icon" src="@/assets/images/energy.png" alt="">
        <span class="title">总摄入量</span>
      </div>
      <div class="list">
        <div class="item" v-for="item in energyList" :key="item">
          <div class="label">{{ item.label }}</div>
          <div class="valLine">
            <span class="val">{{ item.val }}</span>
            <span class="unit">{{ item.unit }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="content" v-for="(item,index) in foodsList" :key="item">
      <div class="titleLine">
        <img class="icon" :src="getMealIcon(index)" alt="">
        <span class="title">{{ getMealName(index) }}</span>
        <span class="subTitle">{{ item.dish_time_tips }}</span>
      </div>
      <div class="imgList">
        <div class="imgItem" v-for="inner in item.dish" :key="inner"  @click="goDetails(inner)">
          <van-image class="img" :src="inner.image" alt="" fit="cover" />
          <div class="name">{{ inner.dish_name }}</div>
          <div class="weight">{{ inner.weight }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getRecipesList } from '@/api/recipes'
export default {
  data() {
    return {
      tips: '',
      energyList: [],
      foodsList: []
    }
  },
  created() {
    this.getRecipesListFun()
  },
  methods: {
    getRecipesListFun(){
      let recipe_id = this.$route.query.id
      getRecipesList({ recipe_id }).then(res => {
        if(res.status == 0){
          let { tips,total_intake, breakfast, lunch, supper, snack} = res.data
          this.tips = tips
          this.energyList = [
            { label: "热量", val: total_intake.calorie || 0, unit: "kcal" },
            { label: "蛋白质", val: total_intake.protein || 0, unit: "g" },
            { label: "脂肪", val: total_intake.fat || 0, unit: "g" },
            { label: "碳水化合物", val: total_intake.carbohydrate || 0, unit: "g" },
            { label: "纤维", val: total_intake.fiber || 0, unit: "g" },
            { label: "糖", val: total_intake.sugar || 0, unit: "g" },
          ]
          this.foodsList = [
            breakfast,
            lunch,
            snack,
            supper
          ]
          console.log(this.foodsList,'foodsList')
        }
      })
    },
    goDetails(data) {
      this.$router.push({
        path: '/recipesDetails',
        query: {
          id: data.recipe_detail_id
        }
      })
    },
    getMealIcon(index) {
      const icons = [
        require('@/assets/images/breakfast.png'),
        require('@/assets/images/lunch.png'),
        require('@/assets/images/snack.png'),
        require('@/assets/images/supper.png')
      ]
      return icons[index] || icons[3]
    },
    getMealName(index) {
      const names = ['早餐', '午餐', '加餐', '晚餐']
      return names[index] || names[3]
    },
  }
}
</script>

<style lang="scss">
.recipesList {
  font-family: 'pingfang-sc';
  width: 100%;
  min-height: 100vh;
  padding: 15px;
  background: #F8F9FB;
  box-sizing: border-box;
  text-align: left;
  line-height: 20px;

  .tips {
    color: #BBB8B8;
    font-size: 16px;
  }

  .content {
    width: 100%;
    background: white;
    border-radius: 10px;
    padding: 15px 10px;
    box-sizing: border-box;
    margin-top: 12px;

    .titleLine {
      display: flex;
      align-items: center;

      .icon {
        width: 22px;
        height: 22px;
      }

      .title {
        color: #333333;
        font-weight: 500;
        font-size: 19px;
        margin-left: 2px;
      }
      .subTitle{
        color: #999999;
        font-size: 15px;
        margin-left: 2px;
      }
    }
  }

  .list {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    
    .item {
      width: 30%;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      border-radius: 10px;
      background: #F8F9FB;
      margin-top: 10px;
      padding: 10px;
      .label {
        color: #666666;
        font-size: 15px;
      }

      .valLine {
        color: #333333;
        font-weight: 500;
        line-height: normal;
        margin-top: 2px;
        .val {
          font-size: 22px;
          line-height: 24px;
        }

        .unit {
          font-size: 14px;
        }
      }
    }
  }
  .imgList{
    display: flex;
    overflow: auto;
    width: 100%;
    /* 隐藏滚动条 */
    &::-webkit-scrollbar {
        display: none;
    }
    -ms-overflow-style: none;  /* IE和Edge */
    scrollbar-width: none;  /* Firefox */
    .imgItem{
      margin-left: 6px;
      margin-top: 10px;
      .img{
        width: 106px;
        height: 106px;
        border-radius: 8px;
      }
      .name{
        color: #333333;
        font-size: 16px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        width: 106px;  // 添加宽度限制，与图片宽度一致
      }
      .weight{
        color: #666666;
        font-size: 14px;
      }
    }
  }
  .van-image__img{
    border-radius: 8px;
  }
}
</style>