<template>
  <div id="printMe" class="print-div">
    <div class="content">
      <div class="header">
        <!-- <img class="logo" src="" alt="" /> -->
        <div class="title">
          <span class="hospital">{{ personInfo.hospital_info.name }}</span>
          <span class="report">{{ personInfo.project.name }}</span>
          <!-- <span class="chinese">深度健康慢病管理检查报告</span>
          <span class="english">
            Deep Health Chronic Disease Management Inspection Report
          </span> -->
        </div>
      </div>
      <div class="report-info">
        <div class="person-info">
          <div class="short-line">
            <div class="card-style firstcard">
              <div class="card-title">姓&nbsp;&nbsp;&nbsp;&nbsp;名</div>
              <div class="card-info">{{ personInfo.patient.name }}</div>
            </div>
            <div class="card-style secondcard">
              <div class="card-title">年&nbsp;&nbsp;&nbsp;&nbsp;龄</div>
              <div class="card-info">{{ personInfo.patient.age }}</div>
            </div>
            <div class="card-style thirdcard">
              <!-- <div class="card-title">检测机构</div> -->
              <div class="card-title">科室名称</div>
              <div class="card-info">
                {{ personInfo.hospital_info.dept_name }}
              </div>
            </div>
          </div>
          <div class="short-line second-line">
            <div class="card-style firstcard">
              <div class="card-title">手机号</div>
              <div class="card-info">{{ personInfo.patient.mobile }}</div>
            </div>
            <div class="card-style secondcard">
              <div class="card-title">性&nbsp;&nbsp;&nbsp;&nbsp;别</div>
              <div class="card-info">{{ sexShow }}</div>
            </div>
            <div class="card-style thirdcard">
              <div class="card-title">生成时间</div>
              <div class="card-info">
                {{ personInfo.project.generate_time }}
              </div>
            </div>
          </div>
          <div class="long-line">
            <div class="card-title">危险因素</div>
            <div class="card-info">
              <van-checkbox-group
                v-model="checkboxGroup"
                direction="horizontal"
              >
                <van-checkbox name="smoking" shape="square">吸烟</van-checkbox>
                <van-checkbox name="drinking" shape="square">饮酒</van-checkbox>
                <van-checkbox name="sleeplessness" shape="square">
                  失眠
                </van-checkbox>
                <van-checkbox name="cvd" shape="square">
                  心脑血管疾病史
                </van-checkbox>
                <van-checkbox name="exercise_less" shape="square">
                  活动量很小
                </van-checkbox>
              </van-checkbox-group>
            </div>
          </div>
        </div>
<!--        <div class="blood-vessel-age">-->
<!--          <div class="card-title">血管年龄</div>-->
<!--          <div v-if="personInfo.patient.vascular_age" class="card-info has-num">-->
<!--            {{ personInfo.patient.vascular_age }}-->
<!--          </div>-->
<!--          <div v-else class="card-info no-num">-</div>-->
<!--        </div>-->
      </div>
      <div class="attention">
        请注意：本报告内容仅供参考使用，不能代替或作为医院或专业机构的诊疗诊断结果或意见。
      </div>
      <div class="danger-project">
        <div class="title">
          <div class="fontspan"></div>
          <span>高危风险项目</span>
        </div>
        <div class="warning-table">
          <div class="warning">
            <div v-if="allAbnormal > 0">
              <img src="./img/forpc/danger.png" alt="" />
              <div class="danger-point">风险项</div>
              <div class="count">
                <div class="number">{{ allAbnormal }}</div>
                <span class="count-span">项异常</span>
              </div>
            </div>
            <div v-else>
              <img  src="./img/forpc/health.png" />
              <div class="health-point">健康</div>
              <div class="health-span">无异常</div>
            </div>
            <div v-if="personInfo.bg.fbg_value==''"  :class="['box diabetes empty']">血糖</div>
            <div
              v-else
              :class="['box diabetes', bgAbnormal > 0 ? 'abnormal' : 'normal']"
            >
              <span>{{bgAbnormal>0?'血糖异常':'血糖正常'}}</span>
              <img
                v-if="bgAbnormal > 0"
                class="error"
                src="@/views/reportCenter/img/forpc/warning.png"
              />
            </div>
            <div v-if="personInfo.bp.sbp_value=='' && personInfo.bp.dbp_value==''"  :class="['box hypertension empty']">血压</div>
            <div v-else
              :class="[
                'box hypertension',
                bpAbnormal > 0 ? 'abnormal' : 'normal',
              ]"
            >
              <span>{{bpAbnormal>0?'血压异常':'血压正常'}}</span>
              <img
                v-if="bpAbnormal > 0"
                class="error"
                src="@/views/reportCenter/img/forpc/warning.png"
              />
            </div>
            <div v-if="personInfo.bp.pulse_value==''"  :class="['box pulse empty']">脉搏</div>
            <div v-else
              :class="[
                'box pulse',
                pulseAbnormal > 0 ? 'abnormal' : 'normal',
              ]"
            >
              <span>{{pulseAbnormal>0?'脉搏异常':'脉搏正常'}}</span>
              <img
                v-if="pulseAbnormal > 0"
                class="error"
                src="@/views/reportCenter/img/forpc/warning.png"
              />
            </div>
            <div v-if="personInfo.blood_fat.tc_value==''"  :class="['box bloodfat empty']">血脂</div>
            <div v-else
              :class="[
                'box bloodfat',
                bloodFatAbnormal > 0 ? 'abnormal' : 'normal',
              ]"
            >
              <span>{{bloodFatAbnormal>0?'血脂异常':'血脂正常'}}</span>
              <img
                v-if="bloodFatAbnormal > 0"
                class="error"
                src="@/views/reportCenter/img/forpc/warning.png"
              />
            </div>
            <div v-if="personInfo.height_weight.bmi_value==''"  :class="['box fat empty']">BMI</div>
            <div v-else :class="['box fat', BMIAbnormal > 0 ? 'abnormal' : 'normal']">
              <span>{{BMIAbnormal>0?'BMI异常':'BMI正常'}}</span>
              <img
                v-if="BMIAbnormal > 0"
                class="error"
                src="@/views/reportCenter/img/forpc/warning.png"
              />
            </div>
            <div v-if="personInfo.breath.fev1_fvc_value==''"  :class="['box copd empty']">肺功能</div>
            <div v-else
              :class="['box copd', breatheAbnormal > 0 ? 'abnormal' : 'normal']"
            >
              <span style="font-size: 13px">{{breatheAbnormal>0?'肺功能异常':'肺功能正常'}}</span>
              <img
                v-if="breatheAbnormal > 0"
                class="error"
                src="@/views/reportCenter/img/forpc/warning.png"
              />
            </div>
            <div v-if="personInfo.as.rabi_value=='' && personInfo.as.labi_value==''"  :class="['box arteriosclerosis empty']">动脉硬化</div>
            <div v-else
              :class="[
                'box arteriosclerosis',
                asAbnormal > 0 ? 'abnormal' : 'normal',
              ]"
            >
              <span v-if="asAbnormal>0">{{asAbnormal>0?'动脉硬化':'无动脉硬化'}}</span>
              <span v-else style="font-size: 13px">{{asAbnormal>0?'动脉硬化':'无动脉硬化'}}</span>
              <img
                v-if="asAbnormal > 0"
                class="error"
                src="@/views/reportCenter/img/forpc/warning.png"
              />
            </div>
            <div v-if="personInfo.eye_examine.left_eye_dignosis=='' && personInfo.eye_examine.right_eye_dignosis==''"  :class="['box retinopathy empty']">眼底病变</div>
            <div v-else
              :class="[
                'box retinopathy',
                eyeAbnormal > 0 ? 'abnormal' : 'normal',
              ]"
            >
              <span v-if="eyeAbnormal>0">{{eyeAbnormal>0?'眼底病变':'无眼底病变'}}</span>
              <span v-else style="font-size: 13px">{{eyeAbnormal>0?'眼底病变':'无眼底病变'}}</span>
              <img
                v-if="eyeAbnormal > 0"
                class="error"
                src="@/views/reportCenter/img/forpc/warning.png"
              />
            </div>
            <div
              class="smallbox smoking abnormal"
              v-if="personInfo.risk_factor.smoking > 0"
            >
              <span>吸烟</span>
            </div>
            <div
              class="smallbox drinking abnormal"
              v-if="personInfo.risk_factor.drinking > 0"
            >
              <span>饮酒</span>
            </div>
            <div
              class="smallbox sleeplessness abnormal"
              v-if="personInfo.risk_factor.sleeplessness > 0"
            >
              <span>失眠</span>
            </div>
            <div
              class="normalbox cvd abnormal"
              v-if="personInfo.risk_factor.cvd > 0"
            >
              <span>心脑血管疾病史</span>
            </div>
            <div
              class="normalbox exercise_less abnormal"
              v-if="personInfo.risk_factor.exercise_less > 0"
            >
              <span>活动量小</span>
            </div>
          </div>
          <div class="table">
            <tr>
              <th>检查项目</th>
              <th>指标名称</th>
              <th>指标缩写</th>
              <th>测量值</th>
              <th>单位</th>
            </tr>
            <tr :class="fbgAbnormal > 0 ? 'warning-tr' : ''">
              <td class="warning-td"></td>
              <td>空腹血浆血糖</td>
              <td>FBG</td>
              <td v-if="personInfo.bg.fbg_value == ''">
                <div class="none-span">未测量</div>
              </td>
              <td v-else>
                <div class="text-num">{{ personInfo.bg.fbg_value }}</div>
                <img v-if="fbgAbnormal > 0" src="./img/forpc/warning.png" />
              </td>
              <td>mmol/L</td>
            </tr>
            <tr :class="gluAbnormal > 0 ? 'warning-tr' : ''">
              <td class="warning-td">血糖</td>
              <td>随机末梢血糖</td>
              <td>GLU</td>
              <td v-if="personInfo.bg.glu_value == ''">
                <div class="none-span">未测量</div>
              </td>
              <td v-else>
                <div class="text-num">{{ personInfo.bg.glu_value }}</div>
                <img v-if="gluAbnormal > 0" src="./img/forpc/warning.png" />
              </td>
              <td>mmol/L</td>
            </tr>
            <tr :class="hba1cAbnormal > 0 ? 'warning-tr' : ''">
              <td class="warning-td"></td>
              <td>糖化血红蛋白</td>
              <td>HbA1C</td>
              <td v-if="personInfo.bg.hba1c_value == ''">
                <div class="none-span">未测量</div>
              </td>
              <td v-else>
                <div class="text-num">{{ personInfo.bg.hba1c_value }}</div>
                <img v-if="hba1cAbnormal > 0" src="./img/forpc/warning.png" />
              </td>
              <td>%</td>
            </tr>
            <tr :class="sbpAbnormal > 0 ? 'warning-tr' : ''">
              <td class="warning-td">血压</td>
              <td>收缩压</td>
              <td>SBP</td>
              <td v-if="personInfo.bp.sbp_value == ''">
                <div class="none-span">未测量</div>
              </td>
              <td v-else>
                <div class="text-num">{{ personInfo.bp.sbp_value }}</div>
                <img v-if="sbpAbnormal > 0" src="./img/forpc/warning.png" />
              </td>
              <td>mmHg</td>
            </tr>
            <tr :class="dbpAbnormal > 0 ? 'warning-tr' : ''">
              <td class="warning-td"  ></td>
              <td>舒张压</td>
              <td>DBP</td>
              <td v-if="personInfo.bp.dbp_value == ''">
                <div class="none-span">未测量</div>
              </td>
              <td v-else>
                <div class="text-num">{{ personInfo.bp.dbp_value }}</div>
                <img v-if="dbpAbnormal > 0" src="./img/forpc/warning.png" />
              </td>
              <td>mmHg</td>
            </tr>
            <tr :class="pulseAbnormal > 0 ? 'warning-tr' : ''">
              <td class="warning-td">脉搏</td>
              <td>脉搏</td>
              <td>Pulse</td>
              <td v-if="personInfo.bp.pulse_value == ''">
                <div class="none-span">未测量</div>
              </td>
              <td v-else>
                <div class="text-num">{{ personInfo.bp.pulse_value }}</div>
                <img v-if="pulseAbnormal > 0" src="./img/forpc/warning.png" />
              </td>
              <td>cpm</td>
            </tr>
            <tr :class="tcAbnormal > 0 ? 'warning-tr' : ''">
              <td class="warning-td"></td>
              <td>血清总胆固醇</td>
              <td>TC</td>
              <td v-if="personInfo.blood_fat.tc_value == ''">
                <div class="none-span">未测量</div>
              </td>
              <td v-else>
                <div class="text-num">{{ personInfo.blood_fat.tc_value }}</div>
                <img v-if="tcAbnormal > 0" src="./img/forpc/warning.png" />
              </td>
              <td>mmol/L</td>
            </tr>
            <tr :class="tgAbnormal > 0 ? 'warning-tr' : ''">
              <td class="warning-td">血脂</td>
              <td>血清甘油三酯</td>
              <td>TG</td>
              <td v-if="personInfo.blood_fat.tg_value == ''">
                <div class="none-span">未测量</div>
              </td>
              <td v-else>
                <div class="text-num">{{ personInfo.blood_fat.tg_value }}</div>
                <img v-if="tgAbnormal > 0" src="./img/forpc/warning.png" />
              </td>
              <td>mmol/L</td>
            </tr>
            <tr :class="ldl_cAbnormal > 0 ? 'warning-tr' : ''">
              <td class="warning-td"></td>
              <td>血清低密度脂蛋白胆固醇</td>
              <td>LDL-C</td>
              <td v-if="personInfo.blood_fat.ldl_c_value == ''">
                <div class="none-span">未测量</div>
              </td>
              <td v-else>
                <div class="text-num">
                  {{ personInfo.blood_fat.ldl_c_value }}
                </div>
                <img v-if="ldl_cAbnormal > 0" src="./img/forpc/warning.png" />
              </td>
              <td>mmol/L</td>
            </tr>
            <tr :class="hdl_cAbnormal > 0 ? 'warning-tr' : ''">
              <td class="warning-td"></td>
              <td>血清高密度脂蛋白胆固醇</td>
              <td>HDL-C</td>
              <td v-if="personInfo.blood_fat.hdl_c_value == ''">
                <div class="none-span">未测量</div>
              </td>
              <td v-else>
                <div class="text-num">
                  {{ personInfo.blood_fat.hdl_c_value }}
                </div>
                <img v-if="hdl_cAbnormal > 0" src="./img/forpc/warning.png" />
              </td>
              <td>mmol/L</td>
            </tr>
            <tr :class="BMIAbnormal > 0 ? 'warning-tr' : ''">
              <td class="warning-td">体重指数</td>
              <td>体重指数</td>
              <td>BMI</td>
              <td v-if="personInfo.height_weight.bmi_value == ''">
                <div class="none-span">未测量</div>
              </td>
              <td v-else>
                <div class="text-num">
                  {{ personInfo.height_weight.bmi_value }}
                </div>
                <img v-if="BMIAbnormal > 0" src="./img/forpc/warning.png" />
              </td>
              <td>kg/m²</td>
            </tr>
            <tr>
              <td class="warning-td"></td>
              <td>用力肺活量</td>
              <td>FVC</td>
              <td v-if="personInfo.breath.fvc_value == ''">
                <div class="none-span">未测量</div>
              </td>
              <td v-else>
                <div class="text-num">{{ personInfo.breath.fvc_value }}</div>
              </td>
              <td>L</td>
            </tr>
            <tr>
              <td class="warning-td">肺功能</td>
              <td>第一秒用力呼气容积</td>
              <td>FEV1</td>
              <td v-if="personInfo.breath.fev1_value == ''">
                <div class="none-span">未测量</div>
              </td>
              <td v-else>
                <div class="text-num">{{ personInfo.breath.fev1_value }}</div>
              </td>
              <td>L</td>
            </tr>
            <tr :class="breatheAbnormal > 0 ? 'warning-tr' : ''">
              <td class="warning-td"></td>
              <td>一秒率</td>
              <td>FEV1/FVC</td>
              <td v-if="personInfo.breath.fev1_fvc_value == ''">
                <div class="none-span">未测量</div>
              </td>
              <td v-else>
                <div class="text-num">
                  {{ personInfo.breath.fev1_fvc_value }}
                </div>
                <img v-if="breatheAbnormal > 0" src="./img/forpc/warning.png" />
              </td>
              <td>%</td>
            </tr>
            <tr
              :class="rabiAbnormal > 0 || labiAbnormal > 0 ? 'warning-tr' : ''"
            >
              <td class="warning-td">动脉硬化</td>
              <td>踝臂指数</td>
              <td>ABI</td>
              <td
                v-if="
                  personInfo.as.rabi_value == '' &&
                  personInfo.as.labi_value == ''
                "
              >
                <div class="none-span">未测量</div>
              </td>
              <td v-else>
                <div class="right-left">左</div>
                <div class="abi-text">{{ personInfo.as.labi_value }}</div>
                <img v-if="labiAbnormal > 0" src="./img/forpc/warning.png" />
                <div class="right-left">右</div>
                <div class="abi-text">{{ personInfo.as.rabi_value }}</div>
                <img v-if="rabiAbnormal > 0" src="./img/forpc/warning.png" />
              </td>
              <td></td>
            </tr>
            <tr
              :class="rpwvAbnormal > 0 || lpwvAbnormal > 0 ? 'warning-tr' : ''"
            >
              <td class="warning-td">ABI+PWV</td>
              <td>脉搏波传播速度</td>
              <td>PWV</td>
              <td
                v-if="
                  personInfo.as.rpwv_value == '' &&
                  personInfo.as.lpwv_value == ''
                "
              >
                <div class="none-span">未测量</div>
              </td>
              <td v-else>
                <div class="right-left">左</div>
                <div class="abi-text">{{ personInfo.as.lpwv_value }}</div>
                <img v-if="lpwvAbnormal > 0" src="./img/forpc/warning.png" />
                <div class="right-left">右</div>
                <div class="abi-text">{{ personInfo.as.rpwv_value }}</div>
                <img v-if="rpwvAbnormal > 0" src="./img/forpc/warning.png" />
              </td>
              <td>cm/s</td>
            </tr>
            <tr :class="VFAAbnormal > 0 ? 'warning-tr' : ''">
              <td class="warning-td">内脏脂肪</td>
              <td>内脏脂肪面积</td>
              <td>VFA</td>
              <td v-if="personInfo.vf.vat_value == ''">
                <div class="none-span">未测量</div>
              </td>
              <td v-else>
                <div class="text-num">{{ personInfo.vf.vat_value }}</div>
                <img v-if="VFAAbnormal > 0" src="./img/forpc/warning.png" />
              </td>
              <td>cm²</td>
            </tr>
            <tr :class="eyeAbnormal > 0 ? 'warning-tr' : ''">
              <td class="warning-td">眼底照相</td>
              <td
                style="
                  border-right: 0;
                  font-size: 14px;
                  text-align: left;
                  padding-left: 7px;
                "
                colspan="4"
              >
                <span v-if="personInfo.eye_examine.left_eye_dignosis">
                  左眼：{{ personInfo.eye_examine.left_eye_dignosis }}；
                </span>
                <span v-if="personInfo.eye_examine.right_eye_dignosis">
                  右眼：{{ personInfo.eye_examine.right_eye_dignosis }}
                </span>
              </td>
            </tr>
          </div>
        </div>
      </div>
      <div class="check-result break">
        <div class="title">
          <div class="fontspan"></div>
          <span>检查结果</span>
        </div>
        <div class="result-flex-div">
          <div class="check-result-box">
            <!-- 空腹血浆血糖 -->
            <div class="result-box">
              <div class="first">
                <div class="image-and-name">
                  <img src="./img/forpc/bloodSuger.png" alt="" />
                  <span>血糖</span>
                </div>
                <span class="standard">中国2型糖尿病防治指南（2020年版）</span>
              </div>
              <div class="second">
                <div class="name-unit">
                  <div class="name">空腹血浆血糖</div>
                  <div class="abbr-unit">
                    FBG<span class="unit">(mmol/L)</span>
                  </div>
                </div>
                <itemReport
                  v-if="personInfo.bg.abnormal_info_fbg_value"
                  code="fbg_value"
                  :info="personInfo.bg"
                />
              </div>
              <div class="third">
                <div class="third-left-div">名词解释</div>
                <div class="third-right-div">
                  在隔夜空腹（至少8～10小时未进任何热量食物）后，第二天早餐前采的血浆检测出的血糖值
                </div>
              </div>
              <div class="fourth">
                <div class="fourth-left-div">指标意义</div>
                <div class="fourth-right-div">临床诊断糖尿病的金标准</div>
              </div>
            </div>
            <!-- 随机末梢血糖 -->
            <div class="result-box">
              <div class="second">
                <div class="name-unit">
                  <div class="name">随机末梢血糖</div>
                  <div class="abbr-unit">
                    GLU<span class="unit">(mmol/L)</span>
                  </div>
                </div>
                <itemReport
                  v-if="personInfo.bg.abnormal_info_glu_value"
                  code="glu_value"
                  :info="personInfo.bg"
                />
              </div>
              <div class="third">
                <div class="third-left-div">名词解释</div>
                <div class="third-right-div">
                  任意时间采取末梢指血所测量得到的葡萄糖含量值
                </div>
              </div>
              <div class="fourth">
                <div class="fourth-left-div">指标意义</div>
                <div class="fourth-right-div">筛查糖尿病的便捷指标</div>
              </div>
            </div>
            <!-- 糖化血红蛋白 -->
            <div class="result-box">
              <div class="second">
                <div class="name-unit">
                  <div class="name">糖化血红蛋白</div>
                  <div class="abbr-unit">
                    HbA1C<span class="unit">(%)</span>
                  </div>
                </div>
                <itemReport
                  v-if="personInfo.bg.abnormal_info_hba1c_value"
                  code="hba1c_value"
                  :info="personInfo.bg"
                />
              </div>
              <div class="third">
                <div class="third-left-div">名词解释</div>
                <div class="third-right-div">
                  红细胞中的血红蛋白与血清中的糖类（主要指葡萄糖）通过非酶反应相结合的产物，即糖化血红蛋白，临床上计算的是糖化血红蛋白数量占血液中血红蛋白总量的百分比
                </div>
              </div>
              <div class="fourth" style="border-bottom: 0">
                <div class="fourth-left-div">指标意义</div>
                <div class="fourth-right-div">
                  <div>①作为糖尿病的补充诊断标准</div>
                  <div>②临床判断糖尿病患者控制血糖是否达标的金标准</div>
                </div>
              </div>
            </div>
          </div>
          <div class="check-result-box">
            <!-- 血清总胆固醇 -->
            <div class="result-box">
              <div class="first">
                <div class="image-and-name">
                  <img src="./img/forpc/bloodFat.png" alt="" />
                  <span>血脂</span>
                </div>
                <span class="standard"
                  >中国成人血脂异常防治指南（2016 年修订版 ）</span
                >
              </div>
              <div class="second">
                <div class="name-unit">
                  <div class="name">血清总胆固醇</div>
                  <div class="abbr-unit">
                    TC<span class="unit">(mmol/L)</span>
                  </div>
                </div>
                <itemReport
                  v-if="personInfo.blood_fat.abnormal_info_tc_value"
                  code="tc_value"
                  :info="personInfo.blood_fat"
                />
              </div>
              <div class="third">
                <div class="third-left-div">名词解释</div>
                <div class="third-right-div">
                  指血液中各种脂蛋白所含胆固醇之总和
                </div>
              </div>
            </div>
            <!-- 血清甘油三酯 -->
            <div class="result-box">
              <div class="second">
                <div class="name-unit">
                  <div class="name">血清甘油三酯</div>
                  <div class="abbr-unit">
                    TG<span class="unit">(mmol/L)</span>
                  </div>
                </div>
                <itemReport
                  v-if="personInfo.blood_fat.abnormal_info_tg_value"
                  code="tg_value"
                  :info="personInfo.blood_fat"
                />
              </div>
              <div class="third">
                <div class="third-left-div">名词解释</div>
                <div class="third-right-div">
                  指血液中脂质的一种重要成分，是人体储存能量的重要形式
                </div>
              </div>
            </div>
            <!-- 血清低密度脂蛋白胆固醇 -->
            <div class="result-box">
              <div class="second">
                <div class="name-unit">
                  <div class="name">血清低密度脂蛋白胆固醇</div>
                  <div class="abbr-unit">
                    LDL-C<span class="unit">(mmol/L)</span>
                  </div>
                </div>
                <itemReport
                  v-if="personInfo.blood_fat.abnormal_info_ldl_c_value"
                  code="ldl_c_value"
                  :info="personInfo.blood_fat"
                />
              </div>
              <div class="third">
                <div class="third-left-div">名词解释</div>
                <div class="third-right-div">
                  指血液中胆固醇含量最多的脂蛋白
                </div>
              </div>
            </div>
            <!-- 血清高密度脂蛋白胆固醇 -->
            <div class="result-box">
              <div class="second">
                <div class="name-unit">
                  <div class="name">血清高密度脂蛋白胆固醇</div>
                  <div class="abbr-unit">
                    HDL-C<span class="unit">(mmol/L)</span>
                  </div>
                </div>
                <itemReport
                  v-if="personInfo.blood_fat.abnormal_info_hdl_c_value"
                  code="hdl_c_value"
                  :info="personInfo.blood_fat"
                />
              </div>
              <div class="third">
                <div class="third-left-div">名词解释</div>
                <div class="third-right-div">
                  指血液中胆固醇颗粒最小的脂蛋白
                </div>
              </div>
              <div class="fourth" style="border-bottom: 0">
                <div class="fourth-left-div">指标意义</div>
                <div class="fourth-right-div">
                  诊断血脂异常的金标准；评估总体心血管危险
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="header">
        <!-- <img class="logo" src="" alt="" /> -->
        <div class="title">
          <span class="hospital">{{ personInfo.hospital_info.name }}</span>
          <span class="report">{{ personInfo.project.name }}</span>
          <!-- <span class="chinese">深度健康慢病管理检查报告</span>
          <span class="english">
            Deep Health Chronic Disease Management Inspection Report
          </span> -->
        </div>
      </div>
      <div class="check-result second-result">
        <div class="result-flex-div">
          <div class="check-result-box">
            <!-- 收缩压 -->
            <div class="result-box">
              <div class="first">
                <div class="image-and-name">
                  <img src="./img/forpc/bloodPressure.png" alt="" />
                  <span>血压</span>
                </div>
                <span class="standard">中国高血压防治指南 2018 年修订版</span>
              </div>
              <div class="second">
                <div class="name-unit">
                  <div class="name">收缩压</div>
                  <div class="abbr-unit">
                    SBP<span class="unit">(mmHg)</span>
                  </div>
                </div>
                <itemReport
                  v-if="personInfo.bp.abnormal_info_sbp_value"
                  code="sbp_value"
                  :info="personInfo.bp"
                />
              </div>
              <div class="third">
                <div class="third-left-div">名词解释</div>
                <div class="third-right-div">
                  当人的心脏收缩时，动脉内压力最高值，也称作“高压”
                </div>
              </div>
              <div class="fourth">
                <div class="fourth-left-div">指标意义</div>
                <div class="fourth-right-div">临床诊断高血压的金标准</div>
              </div>
            </div>
            <!-- 舒张压 -->
            <div class="result-box">
              <div class="second">
                <div class="name-unit">
                  <div class="name">舒张压</div>
                  <div class="abbr-unit">
                    DBP<span class="unit">(mmHg)</span>
                  </div>
                </div>
                <itemReport
                  v-if="personInfo.bp.abnormal_info_dbp_value"
                  code="dbp_value"
                  :info="personInfo.bp"
                />
              </div>
              <div class="third">
                <div class="third-left-div">名词解释</div>
                <div class="third-right-div">
                  当人的心脏收缩时，动脉内压力最低值，也称作“低压”
                </div>
              </div>
              <div class="fourth">
                <div class="fourth-left-div">指标意义</div>
                <div class="fourth-right-div">临床诊断高血压的金标准</div>
              </div>
            </div>
            <!-- 脉搏 -->
            <div class="result-box">
              <div class="second">
                <div class="name-unit">
                  <div class="name">脉搏</div>
                  <div class="abbr-unit">
                    Pulse<span class="unit">(cpm)</span>
                  </div>
                </div>
                <itemReport
                  v-if="personInfo.bp.abnormal_info_pulse_value"
                  code="pulse_value"
                  :info="personInfo.bp"
                />
              </div>
              <div class="third">
                <div class="third-left-div">名词解释</div>
                <div class="third-right-div">人体表可触摸到的动脉搏动</div>
              </div>
              <div class="fourth" style="border-bottom: 0">
                <div class="fourth-left-div">指标意义</div>
                <div class="fourth-right-div">日常筛查心律失常的便捷指标</div>
              </div>
            </div>
          </div>
          <div class="check-result-box">
            <!-- 用力肺活量 -->
            <div class="result-box">
              <div class="first">
                <div class="image-and-name">
                  <img src="./img/forpc/breathe.png" alt="" />
                  <span>肺功能</span>
                </div>
                <span class="standard">
                  慢性阻塞性肺疾病诊治指南（2021 年修订版）
                </span>
              </div>
              <div class="second">
                <div class="name-unit">
                  <div class="name">用力肺活量</div>
                  <div class="abbr-unit">FVC<span class="unit">(L)</span></div>
                </div>
                <table class="FVC table">
                  <tr>
                    <th>实际值</th>
                    <th>预期值</th>
                    <th>占比</th>
                  </tr>
                  <tr>
                    <td v-if="personInfo.breath.fvc_value">
                      {{ personInfo.breath.fvc_value }}
                    </td>
                    <td class="untested" v-else>未测量</td>
                    <td>{{ personInfo.breath.fvc_pred_value }}</td>
                    <td v-if="personInfo.breath.fvc_perc_pred_value">
                      {{ personInfo.breath.fvc_perc_pred_value }}%
                    </td>
                    <td class="untested" v-else>未测量</td>
                  </tr>
                </table>
              </div>
              <div class="third">
                <div class="third-left-div">名词解释</div>
                <div class="third-right-div">
                  用力最大吸气后，尽力尽快呼气所能呼出的最大气体量
                </div>
              </div>
              <div class="fourth">
                <div class="fourth-left-div">指标意义</div>
                <div class="fourth-right-div">反映肺通气功能</div>
              </div>
            </div>
            <!-- 第一秒用力呼气容积 -->
            <div class="result-box">
              <div class="second">
                <div class="name-unit">
                  <div class="name">第一秒用力呼气容积</div>
                  <div class="abbr-unit">FEV1<span class="unit">(L)</span></div>
                </div>
                <table class="FEV1 table">
                  <tr>
                    <th>实际值</th>
                    <th>预期值</th>
                    <th>占比</th>
                  </tr>
                  <tr>
                    <td v-if="personInfo.breath.fev1_value">
                      {{ personInfo.breath.fev1_value }}
                    </td>
                    <td class="untested" v-else>未测量</td>
                    <td>{{ personInfo.breath.fev1_pred_value }}</td>
                    <td v-if="personInfo.breath.fev1_perc_pred_value">
                      {{ personInfo.breath.fev1_perc_pred_value }}%
                    </td>
                    <td class="untested" v-else>未测量</td>
                  </tr>
                </table>
              </div>
              <div class="third">
                <div class="third-left-div">名词解释</div>
                <div class="third-right-div">
                  用力最大吸气后，尽力尽快呼气所能呼出的最大气体量
                </div>
              </div>
              <div class="fourth">
                <div class="fourth-left-div">指标意义</div>
                <div class="fourth-right-div">反映肺通气功能</div>
              </div>
            </div>
            <!-- 一秒率 -->
            <div class="result-box">
              <div class="second">
                <div class="name-unit">
                  <div class="name">一秒率</div>
                  <div class="abbr-unit">
                    FEV1/FVC<span class="unit">(%)</span>
                  </div>
                </div>
                <itemReport
                  v-if="personInfo.breath.abnormal_info_fev1_fvc_value"
                  code="fev1_fvc_value"
                  :info="personInfo.breath"
                />
              </div>
              <div class="third" style="border-bottom: 0">
                <div class="third-left-div">名词解释</div>
                <div class="third-right-div">
                  <div>
                    最常用的判断有无气流阻塞的呼吸机指标参数；临床上用于诊断慢性阻塞性肺部疾病；
                  </div>
                  <div>鉴别诊断支气管哮喘的重要指标</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="check-result">
        <div class="result-flex-div">
          <div class="check-result-box">
            <!-- 踝臂指数 -->
            <div class="result-box">
              <div class="first">
                <div class="image-and-name">
                  <img src="./img/forpc/ABI.png" alt="" />
                  <span>动脉硬化</span>
                </div>
              </div>
              <div class="second ABI-height">
                <div class="name-unit">
                  <div class="name">踝臂指数</div>
                  <div class="abbr-unit">ABI</div>
                </div>
                <itemReport
                  v-if="personInfo.as.abnormal_info_labi_value || personInfo.as.abnormal_info_rabi_value"
                  code="rabi_value"
                  codetip="右"
                  codebelow="labi_value"
                  codebelowtip="左"
                  :info="personInfo.as"
                />
              </div>
              <div class="third">
                <div class="third-left-div">名词解释</div>
                <div class="third-right-div">
                  踝部胫后动脉或胫前动脉的收缩压÷上臂肱动脉的收缩压
                </div>
              </div>
              <div class="fourth">
                <div class="fourth-left-div">指标意义</div>
                <div class="fourth-right-div">
                  <div>①周围压力传导动脉狭窄性疾病的筛查和诊断</div>
                  <div>②预测及评估心脑血管事件以及死亡风险</div>
                </div>
              </div>
            </div>
            <!-- 脉搏波传播速度 -->
            <div class="result-box">
              <div class="second">
                <div class="name-unit">
                  <div class="name">脉搏波传播速度</div>
                  <div class="abbr-unit">
                    PWV<span class="unit">(cm/s)</span>
                  </div>
                </div>
                <div>
                  <itemReport
                    v-if="personInfo.as.abnormal_info_rpwv_value"
                    code="rpwv_value"
                    codeclassname="abi-num-rpwv"
                    boxclassname="rpwvValue"
                    codetip="右"
                    :info="personInfo.as"
                  />
                  <itemReport
                    v-if="personInfo.as.abnormal_info_lpwv_value"
                    code="lpwv_value"
                    codeclassname="abi-num-bottom-lpwv"
                    boxclassname="lpwvValue"
                    codetip="左"
                    :info="personInfo.as"
                  />
                </div>
              </div>
              <div class="third">
                <div class="third-left-div">名词解释</div>
                <div class="third-right-div">
                  心脏每次搏动时射血产生的沿大动脉壁传播的压力波的传导速度
                </div>
              </div>
              <div class="fourth">
                <div class="fourth-left-div">指标意义</div>
                <div class="fourth-right-div">
                  <div>①检测大动脉的僵硬度；</div>
                  <div>②辅助诊断四肢动脉和胸、腹主动脉狭窄；</div>
                  <div>③预测心血管病的风险；</div>
                  <div>④评估早期血管衰老程度。</div>
                  <div>升高表示</div>
                  <div>①大动脉僵硬；</div>
                  <div>②四肢动脉或胸腹主动脉疑似狭窄；</div>
                  <div>③心血管病发生发展的风险高；</div>
                  <div>④发生心血管事件及死亡的风险高。</div>
                </div>
              </div>
              <div class="fifth">
                <div>
                  2020年《同步四肢血压和臂踝脉搏波速度测量临床应用中国专家共识》
                </div>
                <div>
                  2021年《脉搏波传导速度检测用于早期血管衰老评价的中国专家共识》
                </div>
              </div>
            </div>
          </div>
          <div class="right-box">
            <!-- 体重指数 -->
            <div class="result-box">
              <div class="first">
                <div class="image-and-name">
                  <img src="./img/forpc/BMI.png" alt="" />
                  <span>体重指数</span>
                </div>
                <span class="standard">
                  基于临床的肥胖症多学科诊疗共识（2021年版）
                </span>
              </div>
              <div class="second">
                <div class="name-unit">
                  <div class="name">体重指数</div>
                  <div class="abbr-unit">
                    BMI<span class="unit">(kg/m²)</span>
                  </div>
                </div>
                <itemReport
                  v-if="personInfo.height_weight.abnormal_info_bmi_value"
                  code="bmi_value"
                  :info="personInfo.height_weight"
                />
              </div>
              <div class="third">
                <div class="third-left-div">名词解释</div>
                <div class="third-right-div">体重（Kg）÷身高平方（m²）</div>
              </div>
              <div class="fourth">
                <div class="fourth-left-div">指标意义</div>
                <div class="fourth-right-div">临床诊断超重、肥胖的金标准</div>
              </div>
            </div>
            <!-- 内脏脂肪面积 -->
            <div class="result-box">
              <div class="first">
                <div class="image-and-name">
                  <img src="./img/forpc/VFA.png" alt="" />
                  <span>内脏脂肪</span>
                </div>
                <span class="standard">
                  基于临床的肥胖症多学科诊疗共识（2021年版）
                </span>
              </div>
              <div class="second">
                <div class="name-unit">
                  <div class="name">内脏脂肪面积</div>
                  <div class="abbr-unit">
                    VFA<span class="unit">(cm²)</span>
                  </div>
                </div>
                <itemReport
                  v-if="personInfo.vf.abnormal_info_vat_value"
                  code="vat_value"
                  :info="personInfo.vf"
                />
              </div>
              <div class="third">
                <div class="third-left-div">名词解释</div>
                <div class="third-right-div">
                  包裹在人体内脏器官周围，存在于腹腔内的脂肪面积
                </div>
              </div>
              <div class="fourth">
                <div class="fourth-left-div">指标意义</div>
                <div class="fourth-right-div">腹型肥胖诊断的金标准</div>
              </div>
            </div>
            <!-- 眼底照相 -->
            <div class="result-box" style="margin-bottom: 0">
              <div class="first">
                <div class="image-and-name">
                  <img src="./img/forpc/eye.png" alt="" />
                  <span>眼底照相</span>
                </div>
                <span class="standard two-standard">
                  <div>糖尿病相关眼病防治多学科中国专家共识（2021年版）</div>
                  <div>DR的国际临床分级标准（2019年版）</div>
                </span>
              </div>
              <div class="sixth" style="border-bottom: 0">
                <table class="eyes">
                  <tr>
                    <th></th>
                    <th>左眼</th>
                    <th>右眼</th>
                  </tr>
                  <tr
                    v-if="
                      personInfo.eye_examine.left_eye_picture_one ||
                      personInfo.eye_examine.right_eye_picture_one
                    "
                  >
                    <td>第一组</td>
                    <td>
                      <img
                        class="eyeImg"
                        :src="personInfo.eye_examine.left_eye_picture_one"
                      />
                    </td>
                    <td>
                      <img
                        class="eyeImg"
                        :src="personInfo.eye_examine.right_eye_picture_one"
                      />
                    </td>
                  </tr>
                  <tr
                    v-if="
                      personInfo.eye_examine.left_eye_picture_two ||
                      personInfo.eye_examine.right_eye_picture_two
                    "
                  >
                    <td>第二组</td>
                    <td>
                      <img
                        class="eyeImg"
                        :src="personInfo.eye_examine.left_eye_picture_two"
                      />
                    </td>
                    <td>
                      <img
                        class="eyeImg"
                        :src="personInfo.eye_examine.right_eye_picture_two"
                      />
                    </td>
                  </tr>
                </table>
              </div>
              <div class="dignosis">
                <div>左眼：{{ personInfo.eye_examine.left_eye_dignosis }}</div>
                <div>右眼：{{ personInfo.eye_examine.right_eye_dignosis }}</div>
              </div>
              <!-- <div class="fifth" style="border-bottom: 0">
                <div class="fifth-left-div">结论描述</div>
                <div class="fifth-right-div">
                  有糖尿病视网膜病变，合并黄斑水肿，疑视神经病变，疑白内障
                </div>
              </div> -->
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { reportProduce } from "@/api/saasReport";
import itemReport from "./itemReport.vue";
export default {
  components: {
    itemReport
  },
  data() {
    return {
      personInfo: {
        patient: {},
        hospital_info: {},
        project: {},
        bg: {}, //血糖
        bp: {}, //血压
        blood_fat: {}, //血脂
        height_weight: {}, //体重指数
        breath: {}, //肺功能
        as: {}, //动脉硬化检测
        vf: {}, //内脏脂肪检查
        eye_examine: {}, //眼底照相
        risk_factor: {}, //风险项
      },
      checkboxGroup: [],
      eyesTextArray: ["未见明显糖尿病视网膜改变", "无糖尿病视网膜病变"],
    };
  },
  computed: {
    sexShow() {
      if (this.personInfo.patient.sex == 1) {
        return "男";
      } else if (this.personInfo.patient.sex == 2) {
        return "女";
      } else {
        return "";
      }
    },
    // 异常计算
    // 空腹血浆血糖
    fbgAbnormal() {
      if (this.getAbnormal( 'bg', 'fbg_value') !== 0) {
        return 1;
      } else {
        return 0;
      }
    },
    // 随机末梢血糖
    gluAbnormal() {
      if (this.getAbnormal( 'bg', 'glu_value') !== 0) {
        return 1;
      } else {
        return 0;
      }
    },
    // 糖化血红蛋白
    hba1cAbnormal() {
      if (this.getAbnormal( 'bg', 'hba1c_value') !== 0) {
        return 1;
      } else {
        return 0;
      }
    },
    // 收缩压
    sbpAbnormal() {
      if (this.getAbnormal( 'bp', 'sbp_value') !== 0) {
        return 1;
      } else {
        return 0;
      }
    },
    // 舒张压
    dbpAbnormal() {
      if (this.getAbnormal( 'bp', 'dbp_value') !== 0) {
        return 1;
      } else {
        return 0;
      }
    },
    // 脉搏
    pulseAbnormal() {
      if (this.getAbnormal( 'bp', 'pulse_value') !== 0) {
        return 1;
      } else {
        return 0;
      }
    },
    // 血清总胆固醇
    tcAbnormal() {
      if (this.getAbnormal( 'blood_fat', 'tc_value') !== 0) {
        return 1;
      } else {
        return 0;
      }
    },
    // 血清甘油三酯
    tgAbnormal() {
      if (this.getAbnormal( 'blood_fat', 'tg_value') !== 0) {
        return 1;
      } else {
        return 0;
      }
    },
    // 血清低密度脂蛋白胆固醇
    ldl_cAbnormal() {
      if (this.getAbnormal( 'blood_fat', 'ldl_c_value') !== 0) {
        return 1;
      } else {
        return 0;
      }
    },
    // 血清高密度脂蛋白胆固醇
    hdl_cAbnormal() {
      if (this.getAbnormal( 'blood_fat', 'hdl_c_value') !== 0) {
        return 1;
      } else {
        return 0;
      }
    },
    // 踝臂指数-右
    rabiAbnormal() {
      if (this.getAbnormal( 'as', 'rabi_value') !== 0) {
        return 1;
      } else {
        return 0;
      }
    },
    // 踝臂指数-左
    labiAbnormal() {
      if (this.getAbnormal( 'as', 'labi_value') !== 0) {
        return 1;
      } else {
        return 0;
      }
    },
    // 脉搏波传播速度-右
    rpwvAbnormal() {
      if (this.getAbnormal( 'as', 'rpwv_value') !== 0) {
        return 1;
      } else {
        return 0;
      }
    },
    // 脉搏波传播速度-左
    lpwvAbnormal() {
      if (this.getAbnormal( 'as', 'lpwv_value') !== 0) {
        return 1;
      } else {
        return 0;
      }
    },
    lefteyeAbnormal() {
      if (this.personInfo.eye_examine.left_eye_dignosis &&
        this.getAbnormal( 'eye_examine', 'left_eye_rechecked') !== 0) {
        return 1;
      } else {
        return 0;
      }
    },
    righteyeAbnormal() {
      if (this.personInfo.eye_examine.right_eye_dignosis &&
        this.getAbnormal( 'eye_examine', 'right_eye_rechecked') !== 0) {
        return 1;
      } else {
        return 0;
      }
    },
    // 风险项-血糖
    bgAbnormal() {
      return this.fbgAbnormal + this.gluAbnormal + this.hba1cAbnormal;
    },
    // 风险项-血压
    bpAbnormal() {
      return this.sbpAbnormal + this.dbpAbnormal;
    },
    // 风险项-血脂
    bloodFatAbnormal() {
      return (
        this.tcAbnormal +
        this.tgAbnormal +
        this.ldl_cAbnormal +
        this.hdl_cAbnormal
      );
    },
    // 风险项-BMI
    BMIAbnormal() {
      if (this.getAbnormal( 'height_weight', 'bmi_value') !== 0) {
        return 1;
      } else {
        return 0;
      }
    },
    // 风险项-肺功能-一秒率
    breatheAbnormal() {
      if (this.getAbnormal( 'breath', 'fev1_fvc_value') !== 0) {
        return 1;
      } else {
        return 0;
      }
    },
    // 风险项-动脉硬化
    asAbnormal() {
      return (
        this.rabiAbnormal +
        this.labiAbnormal +
        this.rpwvAbnormal +
        this.lpwvAbnormal
      );
    },
    // 风险项-内脏脂肪面积
    VFAAbnormal() {
      if (this.getAbnormal( 'vf', 'vat_value') !== 0) {
        return 1;
      } else {
        return 0;
      }
    },
    eyeAbnormal() {
      return this.lefteyeAbnormal + this.righteyeAbnormal;
    },
    allAbnormal() {
      return (
        this.bgAbnormal +
        this.bpAbnormal +
        this.pulseAbnormal +
        this.bloodFatAbnormal +
        this.BMIAbnormal +
        this.breatheAbnormal +
        this.asAbnormal +
        this.VFAAbnormal +
        this.eyeAbnormal
      );
    },
  },
  created() {
    this.getReportProduce();
  },
  methods: {
    async getReportProduce() {
      // let res = await reportProduce({
      //   template_id: 5,
      //   sign: "5157898f5cad7745ba0dd68be7d0efbec00ac1f9d167d1a09a8563d9c6b661634e8a44df2211205951526fc22780c3d62036f431d35346547a011f5b6d2bf57c81eb7022280f3ab5ebcc5c3daf6a3da3383434d828a2a989860f4332194a929f5c8b22c79a4f29b25793fa5ed8dd24ee8802bec6aaeb2987ef7515e64f19ac6a4703fbfb36b3e2c5744a7e5e943c339ac627689cd25a3f2d169379fc938f83a758c02b7776f2bf92b94686ca90cbc6332eb259514a8ce049992d33f24982fc3fe2477bd799bf513308aa8eeb0449c271db10e8494827f83000dc3a366c71478b",
      // });
      let res = await reportProduce({
        template_id: this.$route.query.template_id,
        sign: this.$route.query.sign,
      });
      // deflection  指标偏向：0 正常，1 偏低，2 偏高，3 低危，4 高危

      // let res = {
      //   "code": 0,
      //   "msg": "success",
      //   "data": {
      //     "project": {
      //       "name": "三高共管心脑血管病防治健康管理项目",
      //       "generate_time": "2024-09-05 18:07:19"
      //     },
      //     "hospital_info": {
      //       "name": "系统测试医院",
      //       "dept_name": "内分泌内科"
      //     },
      //     "patient": {
      //       "name": "199.None.t",
      //       "age": 41,
      //       "sex": 1,
      //       "mobile": "***********",
      //       "vascular_age": ""
      //     },
      //     "risk_factor": {
      //       "smoking": 0,
      //       "drinking": 0,
      //       "sleeplessness": 0,
      //       "cvd": 0,
      //       "exercise_less": 0
      //     },
      //     "bg": {
      //       "fbg_value": "",
      //       "glu_value": "",
      //       "hba1c_value": "",
      //       "abnormal_info": [
      //         {
      //           "code": "fbg_value",
      //           "abnormal_code": "fasting_bg",
      //           "deflection": 0,
      //           "status_display": "正常",
      //           "indicator_label": [
      //             "2.8",
      //             "3.9",
      //             "6.1",
      //             "7.0"
      //           ],
      //           "indicator_value": [
      //             "低血糖",
      //             "降低",
      //             "正常",
      //             "空腹血糖受损",
      //             "疑似糖尿病"
      //           ],
      //           "normal_range": "3.9≤n＜6.1"
      //         },
      //         {
      //           "code": "glu_value",
      //           "abnormal_code": "GLU1=GLU",
      //           "deflection": 0,
      //           "status_display": "正常",
      //           "indicator_label": [
      //             "2.8",
      //             "3.9",
      //             "6.1",
      //             "7.0"
      //           ],
      //           "indicator_value": [
      //             "低血糖",
      //             "降低",
      //             "正常",
      //             "空腹血糖受损",
      //             "疑似糖尿病"
      //           ],
      //           "normal_range": "3.9≤n＜6.1"
      //         },
      //         {
      //           "code": "hba1c_value",
      //           "abnormal_code": "A1C",
      //           "deflection": 0,
      //           "status_display": "正常",
      //           "indicator_label": [
      //             "6.5"
      //           ],
      //           "indicator_value": [
      //             "正常",
      //             "疑似糖尿病"
      //           ],
      //           "normal_range": "＜6.5"
      //         }
      //       ]
      //     },
      //     "bp": {
      //       "sbp_value": "",
      //       "dbp_value": "",
      //       "pulse_value": "",
      //       "abnormal_info": [
      //         {
      //           "code": "sbp_value",
      //           "abnormal_code": "sbp",
      //           "deflection": 0,
      //           "status_display": "正常",
      //           "indicator_label": [
      //             "90",
      //             "140",
      //             "160",
      //             "180"
      //           ],
      //           "indicator_value": [
      //             "低血压",
      //             "正常",
      //             "高血压1级",
      //             "高血压2级",
      //             "高血压3级"
      //           ],
      //           "normal_range": "90≤n＜140"
      //         },
      //         {
      //           "code": "dbp_value",
      //           "abnormal_code": "dbp",
      //           "deflection": 0,
      //           "status_display": "正常",
      //           "indicator_label": [
      //             "60",
      //             "90",
      //             "100",
      //             "110"
      //           ],
      //           "indicator_value": [
      //             "低血压",
      //             "正常",
      //             "高血压1级",
      //             "高血压2级",
      //             "高血压3级"
      //           ],
      //           "normal_range": "60≤n＜90"
      //         },
      //         {
      //           "code": "pulse_value",
      //           "abnormal_code": "pulse",
      //           "deflection": 0,
      //           "status_display": "正常",
      //           "indicator_label": [
      //             "60",
      //             "100"
      //           ],
      //           "indicator_value": [
      //             "心动过缓",
      //             "正常",
      //             "心动过速"
      //           ],
      //           "normal_range": "60≤n≤100"
      //         }
      //       ]
      //     },
      //     "blood_fat": {
      //       "tc_value": "",
      //       "tg_value": "",
      //       "ldl_c_value": "",
      //       "hdl_c_value": "",
      //       "abnormal_info": [
      //         {
      //           "code": "tc_value",
      //           "abnormal_code": "CHOL",
      //           "deflection": 0,
      //           "status_display": "正常",
      //           "indicator_label": [
      //             "5.2",
      //             "6.2"
      //           ],
      //           "indicator_value": [
      //             "合适水平",
      //             "边缘升高",
      //             "升高"
      //           ],
      //           "normal_range": "＜5.2"
      //         },
      //         {
      //           "code": "tg_value",
      //           "abnormal_code": "TG-B",
      //           "deflection": 0,
      //           "status_display": "正常",
      //           "indicator_label": [
      //             "1.7",
      //             "2.3"
      //           ],
      //           "indicator_value": [
      //             "合适水平",
      //             "边缘升高",
      //             "升高"
      //           ],
      //           "normal_range": "＜1.7"
      //         },
      //         {
      //           "code": "ldl_c_value",
      //           "abnormal_code": "ULDL",
      //           "deflection": 0,
      //           "status_display": "正常",
      //           "indicator_label": [
      //             "2.6",
      //             "3.4",
      //             "4.1"
      //           ],
      //           "indicator_value": [
      //             "理想水平",
      //             "合适水平",
      //             "边缘升高",
      //             "升高"
      //           ],
      //           "normal_range": "＜3.4"
      //         },
      //         {
      //           "code": "hdl_c_value",
      //           "abnormal_code": "UHDL",
      //           "deflection": 0,
      //           "status_display": "正常",
      //           "indicator_label": [
      //             "1.0"
      //           ],
      //           "indicator_value": [
      //             "较低",
      //             "正常"
      //           ],
      //           "normal_range": "≥1.0"
      //         }
      //       ]
      //     },
      //     "height_weight": {
      //       "id": 0,
      //       "bmi_value": "",
      //       "abnormal_info": [
      //         {
      //           "code": "bmi_value",
      //           "abnormal_code": "bmi",
      //           "deflection": 0,
      //           "status_display": "正常",
      //           "indicator_label": [
      //             "18.5",
      //             "24",
      //             "28"
      //           ],
      //           "indicator_value": [
      //             "过低",
      //             "正常",
      //             "超重",
      //             "肥胖"
      //           ],
      //           "normal_range": "18.5≤n＜24"
      //         }
      //       ]
      //     },
      //     "breath": {
      //       "fvc_value": "",
      //       "fvc_pred_value": "",
      //       "fvc_perc_pred_value": "",
      //       "fev1_value": "",
      //       "fev1_pred_value": "",
      //       "fev1_perc_pred_value": "",
      //       "fev1_fvc_value": "",
      //       "abnormal_info": [
      //         {
      //           "code": "fev1_fvc_value",
      //           "abnormal_code": "fev_one_dot_zero_fvc",
      //           "deflection": 0,
      //           "status_display": "正常",
      //           "indicator_label": [
      //             "70"
      //           ],
      //           "indicator_value": [
      //             "慢性阻塞性肺部疾病 ",
      //             "正常"
      //           ],
      //           "normal_range": "≥70"
      //         }
      //       ]
      //     },
      //     "as": {
      //       "labi_value": "",
      //       "lpwv_value": "",
      //       "rabi_value": "",
      //       "rpwv_value": "",
      //       "abnormal_info": [
      //         {
      //           "code": "labi_value",
      //           "abnormal_code": "Labi",
      //           "deflection": 0,
      //           "status_display": "正常",
      //           "indicator_label": [
      //             "0.9",
      //             "1.4"
      //           ],
      //           "indicator_value": [
      //             "疑似动脉硬化",
      //             "正常",
      //             "疑似动脉钙化"
      //           ],
      //           "normal_range": "0.9≤n≤1.4"
      //         },
      //         {
      //           "code": "rabi_value",
      //           "abnormal_code": "Rabi",
      //           "deflection": 0,
      //           "status_display": "正常",
      //           "indicator_label": [
      //             "0.9",
      //             "1.4"
      //           ],
      //           "indicator_value": [
      //             "疑似动脉硬化",
      //             "正常",
      //             "疑似动脉钙化"
      //           ],
      //           "normal_range": "0.9≤n≤1.4"
      //         },
      //         {
      //           "code": "lpwv_value",
      //           "abnormal_code": "LbaPWV",
      //           "deflection": 0,
      //           "status_display": "正常",
      //           "indicator_label": [
      //             "1400",
      //             "1800"
      //           ],
      //           "indicator_value": [
      //             "正常",
      //             "僵硬度增加",
      //             "疑似动脉硬化"
      //           ],
      //           "normal_range": "＜1400"
      //         },
      //         {
      //           "code": "rpwv_value",
      //           "abnormal_code": "RbaPWV",
      //           "deflection": 0,
      //           "status_display": "正常",
      //           "indicator_label": [
      //             "1400",
      //             "1800"
      //           ],
      //           "indicator_value": [
      //             "正常",
      //             "僵硬度增加",
      //             "疑似动脉硬化"
      //           ],
      //           "normal_range": "＜1400"
      //         }
      //       ]
      //     },
      //     "vf": {
      //       "vat_value": "",
      //       "abnormal_info": [
      //         {
      //           "code": "vat_value",
      //           "abnormal_code": "visceral_fat",
      //           "deflection": 0,
      //           "status_display": "正常",
      //           "indicator_label": [
      //             "100"
      //           ],
      //           "indicator_value": [
      //             "正常",
      //             "内脏脂肪型肥胖"
      //           ],
      //           "normal_range": "≤100"
      //         }
      //       ]
      //     },
      //     "eye_examine": {
      //       "left_eye_picture_one": "https://saas-oss-prod-pri.oss-cn-hangzhou.aliyuncs.com/production/live/20240904/fundus/fundus66d812875b9785.98618390/100085339335_20240904_155523_Non_myd_L_009.jpg?OSSAccessKeyId=LTAI5tKUwSWh81v65L4A5eeh&Expires=1733212665&Signature=DwOx9Y+v7Z4fKuM77+JvwQauIE0=",
      //       "right_eye_picture_one": "",
      //       "left_eye_picture_two": "",
      //       "right_eye_picture_two": "",
      //       "left_eye_dignosis": "",
      //       "right_eye_dignosis": "",
      //       "describe": "",
      //       "left_warning": 0,
      //       "right_warning": 0,
      //       "abnormal_info": [
      //         {
      //           "code": "left_eye_rechecked",
      //           "abnormal_code": "left_eye_rechecked",
      //           "deflection": 0,
      //           "status_display": "正常",
      //           "indicator_label": [
      //             "6",
      //             "1",
      //             "3",
      //             "5",
      //             "4",
      //             "2"
      //           ],
      //           "indicator_value": [
      //             "正常",
      //             "正常",
      //             "异常",
      //             "异常",
      //             "异常",
      //             "异常"
      //           ],
      //           "normal_range": "无法判断"
      //         },
      //         {
      //           "code": "right_eye_rechecked",
      //           "abnormal_code": "right_eye_rechecked",
      //           "deflection": '',
      //           "status_display": "",
      //           "indicator_label": [
      //             "6",
      //             "1",
      //             "3",
      //             "5",
      //             "4",
      //             "2"
      //           ],
      //           "indicator_value": [
      //             "正常",
      //             "正常",
      //             "异常",
      //             "异常",
      //             "异常",
      //             "异常"
      //           ],
      //           "normal_range": "无法判断"
      //         }
      //       ]
      //     }
      //   }
      // }


      if (res.code == 0) {
        this.personInfo = res.data;
        Object.keys(this.personInfo).forEach(v => {
          if (['bg', 'blood_fat', 'bp', 'breath', 'as', 'height_weight', 'vf', 'eye_examine'].includes(v)){
            this.personInfo[v].abnormal_info.map(m => {
              this.personInfo[v][`abnormal_info_${m.code}`] = JSON.parse(JSON.stringify(m))
              return m
            })
          }
        })

        if (this.personInfo.risk_factor.cvd > 0) {
          this.checkboxGroup.push("cvd");
        }
        if (this.personInfo.risk_factor.drinking > 0) {
          this.checkboxGroup.push("drinking");
        }
        if (this.personInfo.risk_factor.exercise_less > 0) {
          this.checkboxGroup.push("exercise_less");
        }
        if (this.personInfo.risk_factor.sleeplessness > 0) {
          this.checkboxGroup.push("sleeplessness");
        }
        if (this.personInfo.risk_factor.smoking > 0) {
          this.checkboxGroup.push("smoking");
        }
      }
    },
    getAbnormal (pcode, code) {
      const ncode = `abnormal_info_${code}`
      if (this.personInfo[pcode] && this.personInfo[pcode][ncode] && this.personInfo[pcode][ncode].deflection !== '' && this.personInfo[pcode][ncode].deflection !== null && this.personInfo[pcode][ncode].deflection !== undefined) {
        return ['left_eye_rechecked', 'right_eye_rechecked'].includes(code) ? (this.personInfo[pcode][ncode].abnormal_status || 0) : this.personInfo[pcode][ncode].deflection
      }
      return ''
    }
  },
};
</script>
<style lang="scss" scoped>
.small_font_size{
  font-size: 13px;
}
.print-div {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 auto;
  width: 535px;
  .content {
    // width: 365px;
    width: 535px;
    .header {
      background: #2c9ef7;
      height: 44px;
      display: flex;
      font-weight: 500;
      color: #ffffff;
      .logo {
        width: 20px;
      }
      .title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        .hospital {
          margin-left: 14px;
          font-size: 14px;
        }
        .report {
          margin-right: 10.5px;
          font-size: 11px;
        }
        // flex-direction: column;
        // justify-content: space-evenly;
        // text-align: left;
        // .chinese {
        //   font-size: 14px;
        // }
        // .english {
        //   font-size: 8px;
        // }
      }
    }
    .report-info {
      padding: 7px 5px;
      display: flex;
      justify-content: space-between;
      .person-info {
        font-weight: 400;
        font-size: 7px;
        line-height: 15px;
        color: #000000;
        .short-line {
          display: flex;
          .card-style {
            display: flex;
            .card-title {
              border: 0.5px solid #cccccc;
              border-right: 0px;
              border-radius: 2px 0px 0px 2px;
              background: rgba(44, 158, 247, 0.2);
              width: 37px;
            }
            .card-info {
              border: 0.5px solid #cccccc;
              border-left: 0px;
              border-radius: 0px 2px 2px 0px;
              text-align: left;
              padding-left: 4px;
            }
          }
          .firstcard {
            .card-info {
              width: 53px;
            }
          }
          .secondcard {
            margin: 0 2px;
            .card-info {
              width: 33px;
            }
          }
          .thirdcard {
            .card-info {
              width: 308px;
            }
          }
        }
        .second-line {
          margin: 3px 0;
        }
        .long-line {
          display: flex;
          .card-title {
            border: 0.5px solid #cccccc;
            border-right: 0px;
            border-radius: 2px 0px 0px 2px;
            background: rgba(44, 158, 247, 0.2);
            width: 37px;
          }
          .card-info {
            border: 0.5px solid #cccccc;
            border-left: 0px;
            border-radius: 0px 2px 2px 0px;
            text-align: left;
            padding-left: 4px;
            width: 482px;
            ::v-deep .van-checkbox__icon {
              width: 9px;
              height: 9px;
              .van-icon {
                width: 9px;
                height: 9px;
                font-size: 7px;
              }
            }
            ::v-deep .van-checkbox__label {
              margin-left: 2.5px;
              line-height: 15px;
            }
          }
        }
      }
      .blood-vessel-age {
        width: 75px;
        .card-title {
          font-weight: 500;
          font-size: 8px;
          line-height: 15px;
          color: #000000;
          background: rgba(44, 158, 247, 0.2);
          border: 0.5px solid #2c9ef7;
          border-bottom: 0px;
          border-radius: 2px 2px 0 0;
        }
        .card-info {
          font-weight: 400;
          height: 38.5px;
          line-height: 38px;
          border: 0.5px solid #2c9ef7;
          border-top: 0px;
          border-radius: 0 0 2px 2px;
          color: #999999;
        }
        .has-num {
          font-size: 30px;
        }
        .no-num {
          font-size: 15px;
        }
      }
    }
    .attention {
      font-weight: 400;
      font-size: 7px;
      color: #000000;
      padding: 0 5px;
      text-align: left;
    }

    .danger-project {
      padding: 6.5px 5px;
      .title {
        display: flex;
        align-items: center;
        .fontspan {
          width: 1.5px;
          height: 10px;
          background: #2c9ef7;
          border-radius: 1px;
        }
        span {
          font-weight: 600;
          font-size: 10px;
          color: #2c9ef7;
          margin-left: 3.5px;
        }
      }
      .warning-table {
        width: 100%;
        margin-top: 4.5px;
        display: flex;
        .warning {
          width: 215px;
          border: 0.5px solid #2c9ef7;
          border-right: none;
          display: flex;
          align-items: center;
          position: relative;
          img {
            width: 215px;
          }
          .health-point{
            position: absolute;
            top: 106px;
            left: 70px;
            font-weight: 500;
            font-size: 12px;
            color: #4cb684;
          }
          .health-span{
            font-weight: 500;
            font-size: 12px;
            position: absolute;
            top: 122px;
            left: 63px;
            color: #4cb684;
          }
          .danger-point {
            position: absolute;
            top: 106px;
            left: 63.5px;
            font-weight: 600;
            font-size: 12px;
            color: #ca2200;
          }
          .count {
            font-weight: 400;
            font-size: 8px;
            color: #ca2200;
            .number {
              width: 12.5px;
              font-weight: 600;
              font-size: 12.5px;
              position: absolute;
              top: 122px;
              left: 61px;
            }
            .count-span {
              position: absolute;
              top: 125px;
              left: 76px;
            }
          }
          .box {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            width: 40px;
            height: 40px;
            border-radius: 20px;
            font-size: 8px;
          }
          .smallbox {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            width: 25px;
            height: 25px;
            border-radius: 12.5px;
            font-size: 7px;
          }
          .normalbox {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            width: 35px;
            height: 35px;
            border-radius: 17.5px;
            font-size: 7px;
          }
          .normal {
            color: #2c9ef7;
            background: linear-gradient(180deg, #ffffff 0%, #def3ff 100%);
            box-shadow: 0px 3px 10px rgba(5, 134, 206, 0.3),
              inset 0px -1px 2px #ffffff;
          }
          .empty {
            color: #878F99;
            background: linear-gradient(180deg, #FFFFFF 0%, #DDDDDD 100%);
            box-shadow: 0px 3px 10px rgba(238, 238, 238, 0.3), inset 0px -1px 2px #FFFFFF;
          }
          .abnormal {
            color: #d0021b;
            background: linear-gradient(180deg, #ffffff 0%, #ffdede 100%);
            box-shadow: 0px 3px 10px rgba(206, 5, 5, 0.3),
              inset 0px -1px 2px #ffffff;
            .error {
              margin-top: 2px;
              width: 9px;
            }
          }

          .diabetes {
            position: absolute;
            top: 25px;
            left: 18px;
          }
          .hypertension {
            position: absolute;
            top: 20px;
            left: 75px;
          }
          .pulse {
            position: absolute;
            top: 38px;
            left: 130px;
          }
          .bloodfat {
            position: absolute;
            top: 88px;
            left: 158px;
          }
          .fat {
            position: absolute;
            top: 143px;
            left: 162px;
          }
          .copd {
            position: absolute;
            top: 192px;
            left: 135px;
          }
          .arteriosclerosis {
            position: absolute;
            top: 215px;
            left: 82px;
          }
          .retinopathy {
            position: absolute;
            top: 212px;
            left: 24px;
          }
          .smoking {
            position: absolute;
            top: 64px;
            left: 22.5px;
          }
          .drinking {
            position: absolute;
            top: 149.5px;
            left: 21px;
          }
          .sleeplessness {
            position: absolute;
            top: 145.5px;
            left: 120.5px;
          }
          .cvd {
            position: absolute;
            top: 16px;
            left: 160px;
            span {
              margin: 3px;
              line-height: 9px;
            }
          }
          .exercise_less {
            position: absolute;
            top: 229px;
            left: 156px;
          }
        }
        .table {
          width: 309px;
          border: 0.5px solid #2c9ef7;
          border-collapse: collapse;
          .warning-tr {
            background: rgba(245, 34, 45, 0.1);
          }
          tr {
            // border: none;
            .warning-td {
              background: #ffffff;
            }
            th {
              font-weight: 400;
              font-size: 7.5px;
              height: 17px;
              line-height: 17px;
              color: #000000;
              background: rgba(44, 158, 247, 0.2);
              border-right: 0.5px solid #2c9ef7;
            }
            th:nth-child(5) {
              border-right: 0;
            }
            td {
              border-right: 0.5px solid #2c9ef7;
              // border-top: none;
              // border-bottom: none;
              vertical-align: middle;
              text-align: center;
            }
            td:nth-child(1) {
              width: 49px;
              font-weight: 500;
              font-size: 8px;
              height: 14.5px;
              line-height: 14.5px;
              color: #000000;
            }
            td:nth-child(2) {
              width: 96px;
              font-weight: 400;
              font-size: 7.5px;
              height: 14.5px;
              line-height: 14.5px;
              color: #000000;
            }
            td:nth-child(3) {
              width: 50.5px;
              font-weight: 400;
              font-size: 8px;
              height: 14.5px;
              line-height: 14.5px;
              color: #000000;
            }
            td:nth-child(4) {
              width: 84.5px;
              height: 14.5px;
              // line-height: 14.5px;
              font-size: 8px;
              display: flex;
              align-items: center;
              .text-num {
                text-align: left;
                width: 30px;
                font-weight: 600;
                color: #000000;
                margin-left: 6.5px;
              }
              .none-span {
                font-weight: 400;
                color: #999999;
                margin-left: 6.5px;
              }
              .right-left {
                margin-left: 3.5px;
                font-weight: 400;
                font-size: 6px;
                color: #2c9ef7;
              }
              .abi-text {
                width: 22px;
                font-weight: 600;
                color: #000000;
              }
            }
            td:nth-child(5) {
              width: 27.5px;
              font-weight: 400;
              font-size: 6px;
              height: 14.5px;
              line-height: 14.5px;
              color: #666666;
              border-right: 0;
            }
          }
          tr:nth-child(4),
          tr:nth-child(6),
          tr:nth-child(7),
          tr:nth-child(11),
          tr:nth-child(12),
          tr:nth-child(15),
          tr:nth-child(17),
          tr:nth-child(18) {
            border-bottom: 0.5px solid #2c9ef7;
          }
        }
      }
    }
    .check-result {
      padding: 0 5px;
      .title {
        display: flex;
        align-items: center;
        margin-bottom: 5px;
        .fontspan {
          width: 1.5px;
          height: 10px;
          background: #2c9ef7;
          border-radius: 1px;
        }
        span {
          font-weight: 600;
          font-size: 10px;
          color: #2c9ef7;
          margin-left: 3.5px;
        }
      }
      .result-flex-div {
        display: flex;
        justify-content: space-between;
        margin-bottom: 5px;
        .check-result-box {
          width: 260px;
          // height: 322.5px;
          border: 0.5px solid #cccccc;
          border-radius: 4px;
          .result-box {
            .first {
              display: flex;
              justify-content: space-between;
              align-items: center;
              // margin: 8px 6px 5.5px 7px;
              padding: 8px 6px 2px 7px;
              color: #000000;
              .image-and-name {
                display: flex;
                align-items: center;
                font-weight: 400;
                font-size: 10px;
                // line-height: 28px;
                span {
                  margin-left: 3px;
                }
              }
              .standard {
                font-weight: 400;
                font-size: 6px;
                opacity: 0.7;
                // line-height: 14px;
              }
            }
            .second {
              display: flex;
              justify-content: space-between;
              padding: 14px 8px 7.5px 6.5px;
              border-bottom: 0.5px solid #cccccc;
              .name-unit {
                width: 48px;
                text-align: left;
                .name {
                  font-size: 8px;
                  line-height: 11px;
                  color: #000000;
                }
                .abbr-unit {
                  font-weight: 600;
                  font-size: 7px;
                  line-height: 10px;
                  color: #000000;
                  .unit {
                    margin-left: 3px;
                    font-weight: 400;
                    font-size: 6px;
                    line-height: 7px;
                    color: #000000;
                    opacity: 0.5;
                  }
                }
              }
              .progress {
                width: 186px;
                display: flex;
                justify-content: space-between;
                position: relative;
                height: 9.5px;
                margin-top: 3px;
                font-weight: 400;
                font-size: 6px;
                line-height: 10px;
                color: #000000;
                .unchecked {
                  // color: #666666;
                  color: #333333;
                  background: rgba(44, 158, 247, 0.4);
                }
                .checked {
                  color: #ffffff;
                  background: #2c9ef7;
                }
                .left-radius {
                  border-radius: 100px 0px 0px 100px;
                }
                .right-radius {
                  border-radius: 0px 100px 100px 0px;
                }
                .range-num {
                  background-image: url("./img/forpc/shortRangeNum.png");
                  width: 25px;
                  height: 14.5px;
                  line-height: 14px;
                  font-weight: 500;
                  font-size: 7px;
                  color: #000000;
                }
                .range-num-bottom {
                  background-image: url("./img/forpc/shortRangeNumBottom.png");
                  width: 25px;
                  height: 15px;
                  line-height: 18.5px;
                  font-weight: 500;
                  font-size: 7px;
                  color: #000000;
                }
                .abi-num {
                  background-image: url("./img/forpc/abi-num.png");
                  width: 35px;
                  height: 14.5px;
                  line-height: 14px;
                  font-weight: 500;
                  font-size: 7px;
                  color: #000000;
                }
                .abi-num-bottom {
                  background-image: url("./img/forpc/abi-num-bottom.png");
                  width: 35px;
                  height: 15px;
                  line-height: 18.5px;
                  font-weight: 500;
                  font-size: 7px;
                  color: #000000;
                }
                .indicator_item{
                  flex: 1;
                  text-align: center;
                  position: relative;
                  .indicator_item_label{
                    width: 30px;
                    text-align: center;
                    color: #000;
                    position: absolute;
                    bottom: -10px;
                    right: -15px;
                    z-index: 1;
                  }
                  .range-num{
                    position: absolute;
                    top: -15px;
                    left: calc(50% - 12.5px);
                    z-index: 11;
                  }
                }
              }
              .table {
                font-weight: 400;
                font-size: 7px;
                line-height: 10px;
                // text-align: left;
                color: #333333;
                .untested {
                  font-weight: 400;
                  font-size: 7px;
                  color: #999999;
                }
                th,
                td {
                  width: 60px;
                }
                td {
                  color: #000000;
                }
              }


              .ABI {
                .one {
                  position: relative;
                  width: 40px;
                  margin-right: 1px;
                }
                .two {
                  position: relative;
                  width: 40px;
                  margin-right: 1px;
                }
                .three {
                  position: relative;
                  width: 40px;
                  margin-right: 1px;
                }
                .four {
                  position: relative;
                  width: 40px;
                  margin-right: 1px;
                }
                .five {
                  position: relative;
                  width: 25px;
                }
                .firstsplit {
                  position: absolute;
                  top: 10px;
                  left: 36.5px;
                }
                .secondsplit {
                  position: absolute;
                  top: 10px;
                  left: 77.5px;
                }
                .thirdsplit {
                  position: absolute;
                  top: 10px;
                  left: 119px;
                }
                .fourthsplit {
                  position: absolute;
                  top: 10px;
                  left: 159px;
                }
                .rabi-one {
                  position: absolute;
                  top: -15px;
                  left: 7.5px;
                }
                .rabi-two {
                  position: absolute;
                  top: -15px;
                  left: 7.5px;
                }
                .rabi-three {
                  position: absolute;
                  top: -15px;
                  left: 7.5px;
                }
                .rabi-four {
                  position: absolute;
                  top: -15px;
                  left: 7.5px;
                }
                .rabi-five {
                  position: absolute;
                  top: -15px;
                }
                .labi-one {
                  position: absolute;
                  top: 18px;
                  left: 7.5px;
                }
                .labi-two {
                  position: absolute;
                  top: 18px;
                  left: 7.5px;
                }
                .labi-three {
                  position: absolute;
                  top: 18px;
                  left: 7.5px;
                }
                .labi-four {
                  position: absolute;
                  top: 18px;
                  left: 7.5px;
                }
                .labi-five {
                  position: absolute;
                  top: 18px;
                }
              }
              .PWV {
                .one {
                  position: relative;
                  width: 50px;
                  margin-right: 1px;
                }
                .two {
                  position: relative;
                  width: 87px;
                  margin-right: 1px;
                }
                .three {
                  position: relative;
                  width: 50px;
                }
                .firstsplit {
                  position: absolute;
                  top: 10px;
                  left: 44px;
                }
                .secondsplit {
                  position: absolute;
                  top: 10px;
                  left: 132px;
                }
                .rpwv-one {
                  position: absolute;
                  top: -15px;
                  left: 7.5px;
                }
                .rpwv-two {
                  position: absolute;
                  top: -15px;
                  left: 26px;
                }
                .rpwv-three {
                  position: absolute;
                  top: -15px;
                  left: 7.5px;
                }
                .lpwv-one {
                  position: absolute;
                  top: 18px;
                  left: 7.5px;
                }
                .lpwv-two {
                  position: absolute;
                  top: 18px;
                  left: 26px;
                }
                .lpwv-three {
                  position: absolute;
                  top: 18px;
                  left: 7.5px;
                }
              }
            }
            .third {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 5px;
              border-bottom: 0.5px solid #cccccc;
              .third-left-div {
                width: 48.5px;
                text-align: right;
                font-weight: 400;
                font-size: 8px;
                line-height: 11px;
                color: #000000;
              }
              .third-right-div {
                width: 190px;
                text-align: left;
                font-weight: 400;
                font-size: 7px;
                line-height: 10px;
                color: #333333;
              }
            }
            .fourth {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 5px;
              border-bottom: 0.5px solid #cccccc;
              .fourth-left-div {
                width: 48.5px;
                text-align: right;
                font-weight: 400;
                font-size: 8px;
                line-height: 11px;
                color: #000000;
              }
              .fourth-right-div {
                width: 190px;
                text-align: left;
                font-weight: 400;
                font-size: 7px;
                line-height: 10px;
                color: #333333;
              }
            }
            .fifth {
              // display: flex;
              // justify-content: space-between;
              // align-items: center;
              font-weight: 400;
              font-size: 6px;
              line-height: 8.5px;
              text-align: right;
              color: #000000;
              opacity: 0.7;
              padding: 5px;
            }
            .ABI-height {
              height: 40px;
            }
          }
        }
        .right-box {
          width: 260px;
          .result-box {
            border: 0.5px solid #cccccc;
            border-radius: 4px;
            margin-bottom: 2px;
            .first {
              display: flex;
              justify-content: space-between;
              align-items: center;
              // margin: 8px 6px 5.5px 7px;
              padding: 8px 6px 2px 7px;
              color: #000000;
              .image-and-name {
                display: flex;
                align-items: center;
                font-weight: 400;
                font-size: 10px;
                // line-height: 28px;
                span {
                  margin-left: 3px;
                }
              }
              .standard {
                font-weight: 400;
                font-size: 6px;
                opacity: 0.7;
                // line-height: 14px;
                line-height: 8.5px;
              }
              .two-standard {
                text-align: right;
              }
            }
            .second {
              display: flex;
              justify-content: space-between;
              padding: 14px 8px 7.5px 6.5px;
              border-bottom: 0.5px solid #cccccc;
              .name-unit {
                width: 48px;
                text-align: left;
                .name {
                  font-size: 8px;
                  line-height: 11px;
                  color: #000000;
                }
                .abbr-unit {
                  font-weight: 600;
                  font-size: 7px;
                  line-height: 10px;
                  color: #000000;
                  .unit {
                    margin-left: 3px;
                    font-weight: 400;
                    font-size: 6px;
                    line-height: 7px;
                    color: #000000;
                    opacity: 0.5;
                  }
                }
              }
              .progress {
                display: flex;
                position: relative;
                height: 9.5px;
                margin-top: 3px;
                font-weight: 400;
                font-size: 6px;
                line-height: 10px;
                color: #000000;
                .unchecked {
                  // color: #666666;
                  color: #333333;
                  background: rgba(44, 158, 247, 0.4);
                }
                .checked {
                  color: #ffffff;
                  background: #2c9ef7;
                }
                .left-radius {
                  border-radius: 100px 0px 0px 100px;
                }
                .right-radius {
                  border-radius: 0px 100px 100px 0px;
                }
                .range-num {
                  background-image: url("./img/forpc/shortRangeNum.png");
                  width: 25px;
                  height: 14.5px;
                  line-height: 14px;
                  font-weight: 500;
                  font-size: 7px;
                  color: #000000;
                }
              }
              .BMI {
                .one {
                  position: relative;
                  width: 25px;
                  margin-right: 1px;
                }
                .two {
                  position: relative;
                  width: 68px;
                  margin-right: 1px;
                }
                .three {
                  position: relative;
                  width: 68px;
                  margin-right: 1px;
                }
                .four {
                  position: relative;
                  width: 25px;
                }
                .firstsplit {
                  position: absolute;
                  top: 10px;
                  left: 21px;
                }
                .secondsplit {
                  position: absolute;
                  top: 10px;
                  left: 90.5px;
                }
                .thirdsplit {
                  position: absolute;
                  top: 10px;
                  left: 160px;
                }
                .bmi-one {
                  position: absolute;
                  top: -15px;
                }
                .bmi-two {
                  position: absolute;
                  top: -15px;
                  left: 21.5px;
                }
                .bmi-three {
                  position: absolute;
                  top: -15px;
                  left: 21.5px;
                }
                .bmi-four {
                  position: absolute;
                  top: -15px;
                }
              }
              .VFA {
                  .one {
                    position: relative;
                    width: 94px;
                    margin-right: 1px;
                  }
                  .two {
                    position: relative;
                    width: 94px;
                  }
                  .firstsplit {
                    position: absolute;
                    top: 10px;
                    left: 90.5px;
                  }
                  .vfa-one {
                    position: absolute;
                    top: -15px;
                    left: 34.5px;
                  }
                  .vfa-two {
                    position: absolute;
                    top: -15px;
                    left: 34.5px;
                  }

              }
            }
            .dignosis {
              font-weight: 400;
              font-size: 8px;
              color: #000000;
              padding: 5.5px 10px 8px 10px;
              display: flex;
              text-align: left;
              div:nth-child(1) {
                width: 112px;
              }
              div:nth-child(2) {
                padding-left: 10px;
              }
            }
            .third {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 5px;
              border-bottom: 0.5px solid #cccccc;
              .third-left-div {
                width: 48.5px;
                text-align: right;
                font-weight: 400;
                font-size: 8px;
                line-height: 11px;
                color: #000000;
              }
              .third-right-div {
                width: 190px;
                text-align: left;
                font-weight: 400;
                font-size: 7px;
                line-height: 10px;
                color: #333333;
              }
            }
            .fourth {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 5px;
              // border-bottom: 0.5px solid #cccccc;
              .fourth-left-div {
                width: 48.5px;
                text-align: right;
                font-weight: 400;
                font-size: 8px;
                line-height: 11px;
                color: #000000;
              }
              .fourth-right-div {
                width: 190px;
                text-align: left;
                font-weight: 400;
                font-size: 7px;
                line-height: 10px;
                color: #333333;
              }
            }
            // .fifth {
            //   display: flex;
            //   justify-content: space-between;
            //   align-items: center;
            //   padding: 5px;
            //   // border-bottom: 0.5px solid #cccccc;
            //   .fifth-left-div {
            //     // width: 48.5px;
            //     text-align: right;
            //     font-weight: 400;
            //     font-size: 8px;
            //     line-height: 11px;
            //     color: #000000;
            //   }
            //   .fifth-right-div {
            //     width: 200px;
            //     text-align: left;
            //     font-weight: 400;
            //     font-size: 7px;
            //     line-height: 10px;
            //     color: #333333;
            //   }
            // }
            .sixth {
              display: flex;
              justify-content: space-between;
              padding: 2px 8px 0 6.5px;
              border-bottom: 0.5px solid #cccccc;
              .eyes {
                font-weight: 400;
                font-size: 8px;
                color: #000000;
                .eyeImg {
                  width: 50px;
                }
                tr {
                  th:nth-child(3) {
                    padding-left: 20px;
                  }
                  td:nth-child(1) {
                    padding-top: 5px;
                    vertical-align: top;
                  }
                  td:nth-child(2) {
                    padding-top: 5px;
                    width: 100px;
                  }
                  td:nth-child(3) {
                    padding-top: 5px;
                    width: 100px;
                    .eyeImg {
                      margin-left: 20px;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
    .second-result {
      margin-top: 9.5px;
      margin-bottom: 5px;
    }
    // .break {
    //   page-break-after: always;
    // }
  }
}
@page {
  size: auto; /* auto is the initial value */
  margin: 7mm; /* this affects the margin in the printer settings */
}

html {
  background-color: #ffffff;
  margin: 0px; /* this affects the margin on the html before sending to printer */
}

body {
  border: solid 0px blue;
  margin: 10mm 15mm 10mm 15mm; /* margin you want for the content */
}
</style>
