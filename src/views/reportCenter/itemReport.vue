<template>
  <div class="progress" :class="boxclassname">
    <div
      v-for="(item, index) in info[abnormalKey].indicator_value"
      :key="index"
      class="indicator_item unchecked"
      :class="{
        'checked': item === info[abnormalKey].status_display || (codebelow && item === info[abnormalKeyBelow].status_display),
        'ml1': index !== 0,
        'left-radius': index === 0,
        'right-radius': index === (info[abnormalKey].indicator_value.length - 1),
        'all-radius': info[abnormalKey].indicator_value.length === 1,
       }"
    >
      <div class="indicator_item_value">{{item}}</div>
      <div :class="codeclassname" v-if="item === info[abnormalKey].status_display && info[code] !== ''">
        <span class="tip" v-if="codetip">{{codetip}}</span>{{info[code]}}
      </div>
      <div :class="codebelowclassname" v-if="codebelow && item === info[abnormalKeyBelow].status_display && info[codebelow] !== ''">
        <span class="tip" v-if="codebelowtip">{{codebelowtip}}</span>{{info[codebelow]}}
      </div>
      <div
        class="indicator_item_label"
        v-if="info[abnormalKey].indicator_label[index] !== undefined && index !== (info[abnormalKey].indicator_value.length - 1)"
      >{{info[abnormalKey].indicator_label[index]}}</div>
    </div>
  </div>
</template>

<script>
export default {
  name: "itemReport",
  props: {
    info: {
      type: Object,
      default: () => {
        return {}
      }
    },
    code: { // 当前显示（上方）
      type: String,
      default: ''
    },
    codeclassname: { // 当前显示（上方）class
      type: String,
      default: 'range-num'
    },
    codetip: { // 当前显示（上方）
      type: String,
      default: ''
    },
    codebelow: { // 当前显示（下方）
      type: String,
      default: ''
    },
    codebelowclassname: { // 当前显示（下方）class
      type: String,
      default: 'range-num-bottom'
    },
    codebelowtip: { // 当前显示（下方）
      type: String,
      default: ''
    },
    boxclassname: { // class
      type: String,
      default: ''
    },
  },
  computed: {
    abnormalKey () {
      return `abnormal_info_${this.code}`
    },
    abnormalKeyBelow () {
      return `abnormal_info_${this.codebelow}`
    }
  }
}
</script>

<style lang="scss"  scoped>
.progress {
  width: 186px;
  display: flex;
  justify-content: space-between;
  position: relative;
  height: 9.5px;
  margin-top: 3px;
  font-weight: 400;
  font-size: 6px;
  line-height: 10px;
  color: #000000;
  .ml1{
    margin-left: 1px;
  }
  .unchecked {
  // color: #666666;
    color: #333333;
    background: rgba(44, 158, 247, 0.4);
  }
  .checked {
    color: #ffffff;
    background: #2c9ef7;
  }
  .left-radius {
    border-radius: 100px 0px 0px 100px;
  }
  .right-radius {
    border-radius: 0px 100px 100px 0px;
  }
  .all-radius{
    border-radius: 100px;
    .abi-num-bottom{
      bottom: -15px !important;
    }
  }

  .indicator_item{
    flex: 1;
    text-align: center;
    position: relative;
    .indicator_item_value{
      white-space: nowrap;
      padding: 0 2px;
    }
    .indicator_item_label{
      width: 35px;
      text-align: center;
      color: #000;
      position: absolute;
      bottom: -10px;
      right: -17.5px;
      z-index: 1;
    }
    .tip{
      color: #000;
      font-weight: 500;
    }
    .range-num{
      background-image: url("./img/forpc/shortRangeNum.png");
      width: 25px;
      height: 14.5px;
      line-height: 14px;
      font-weight: 500;
      font-size: 7px;
      color: #000000;
      position: absolute;
      top: -15px;
      left: calc(50% - 12.5px);
      z-index: 11;
    }
    .range-num-bottom{
      background-image: url("./img/forpc/shortRangeNumBottom.png");
      width: 25px;
      height: 15px;
      line-height: 18.5px;
      font-weight: 500;
      font-size: 7px;
      color: #000000;
      position: absolute;
      bottom: -23px;
      left: calc(50% - 12.5px);
      z-index: 11;
    }
    .abi-num {
      background-image: url("./img/forpc/abi-num.png");
      width: 35px;
      height: 14.5px;
      line-height: 14px;
      font-weight: 500;
      font-size: 7px;
      color: #000000;
      position: absolute;
      top: -15px;
      left: calc(50% - 12.5px);
      z-index: 11;
    }
    .abi-num-bottom {
      background-image: url("./img/forpc/abi-num-bottom.png");
      width: 35px;
      height: 15px;
      line-height: 18.5px;
      font-weight: 500;
      font-size: 7px;
      color: #000000;
      position: absolute;
      bottom: -23px;
      left: calc(50% - 12.5px);
      z-index: 11;
    }
    .abi-num-rpwv {
      background: url("./img/forpc/abi-num.png") no-repeat 0 0;
      background-size: 100% 12px;
      width: 32px;
      height: 12px;
      line-height: 11px;
      font-weight: 500;
      font-size: 7px;
      color: #000000;
      position: absolute;
      top: -12px;
      left: calc(50% - 12.5px);
      z-index: 11;
    }
    .abi-num-bottom-lpwv{
      background: url("./img/forpc/abi-num-bottom.png") no-repeat 0 0;
      background-size: 100% 12px;
      width: 32px;
      height: 12px;
      line-height: 14px;
      font-weight: 500;
      font-size: 7px;
      color: #000000;
      position: absolute;
      bottom: -12.5px;
      left: calc(50% - 12.5px);
      z-index: 11;
    }
  }
}
.rpwvValue{
  margin-top: -1px !important;
  // margin-bottom: 20px;
  .indicator_item_label{
    bottom: -8.5px !important;
  }
}
.lpwvValue{
  margin-top: 8px !important;
  // margin-bottom: 20px;
  .indicator_item_label{
    bottom: -8.5px !important;
  }
}
</style>
