<template>
  <div class="progress">
    <div
      v-for="(item, index) in info[abnormalKey].indicator_value"
      :key="index"
      class="indicator_item unchecked"
      :class="{
        'checked': item === info[abnormalKey].status_display || (codebelow && item === info[abnormalKeyBelow].status_display),
        'ml1': index !== 0,
        'left-radius': index === 0,
        'right-radius': index === (info[abnormalKey].indicator_value.length - 1),
        'all-radius': info[abnormalKey].indicator_value.length === 1,
       }"
    >
      <div class="indicator_item_value">{{item}}</div>
      <div :class="codeclassname" v-if="item === info[abnormalKey].status_display">
        <span class="tip" v-if="codetip">{{codetip}}</span>{{info[code]}}
      </div>
      <div :class="codebelowclassname" v-if="codebelow && item === info[abnormalKeyBelow].status_display">
        <span class="tip" v-if="codebelowtip">{{codebelowtip}}</span>{{info[codebelow]}}
      </div>
      <div
        class="indicator_item_label"
        v-if="info[abnormalKey].indicator_label[index] !== undefined && index !== (info[abnormalKey].indicator_value.length - 1)"
      >{{info[abnormalKey].indicator_label[index]}}</div>
    </div>
  </div>
</template>

<script>
export default {
  name: "itemReport",
  props: {
    info: {
      type: Object,
      default: () => {
        return {}
      }
    },
    code: { // 当前显示（上方）
      type: String,
      default: ''
    },
    codeclassname: { // 当前显示（上方）class
      type: String,
      default: 'range-num'
    },
    codetip: { // 当前显示（上方）
      type: String,
      default: ''
    },
    codebelow: { // 当前显示（下方）
      type: String,
      default: ''
    },
    codebelowclassname: { // 当前显示（下方）class
      type: String,
      default: 'range-num-bottom'
    },
    codebelowtip: { // 当前显示（下方）
      type: String,
      default: ''
    },
  },
  computed: {
    abnormalKey () {
      return `abnormal_info_${this.code}`
    },
    abnormalKeyBelow () {
      return `abnormal_info_${this.codebelow}`
    }
  }
}
</script>

<style lang="scss"  scoped>
.progress {
  width: 100%;
  display: flex;
  justify-content: space-between;
  position: relative;
  height: 20px;
  line-height: 20px;
  margin-top: 48px;
  margin-bottom: 35px;
  font-weight: 400;
  font-size: 12px;
  color: #000000;

  .ml1{
    margin-left: 1px;
  }
  .unchecked {
    // color: #666666;
    color: #333333;
    background: rgba(44, 158, 247, 0.4);
  }
  .checked {
    color: #ffffff;
    background: #2c9ef7;
  }
  .left-radius {
    border-radius: 100px 0px 0px 100px;
  }
  .right-radius {
    border-radius: 0px 100px 100px 0px;
  }

  .all-radius{
    border-radius: 100px;
  }

  .indicator_item{
    flex: 1;
    text-align: center;
    position: relative;
    .indicator_item_value{
      white-space: nowrap;
      padding: 0 2px;
    }
    .indicator_item_label{
      width: 35px;
      text-align: center;
      color: #000;
      position: absolute;
      bottom: -20px;
      right: -17.5px;
      z-index: 1;
      opacity: 0.7;
    }
    .tip{
      color: #000;
      font-weight: 500;
    }
    .range-num{
      background-image: url("./img/forphone/rangeNum.png");
      background-size: 100% 100%;
      width: 50px;
      height: 31px;
      line-height: 28px;
      font-weight: 500;
      font-size: 14px;
      color: #000000;
      position: absolute;
      top: -33px;
      left: calc(50% - 25px);
      z-index: 11;
    }
  }

}
</style>
