<template>
  <div class="content">
    <div class="header">
      <img class="avatar" src="./img/forphone/avatar.png" alt="" />
      <div class="person-info">
        <div class="name">{{ personInfo.patient.name }}</div>
        <div class="icon-span">
          <img src="./img/forphone/building.png" alt="" />
          <span>{{
            personInfo.hospital_info.name + personInfo.hospital_info.dept_name
          }}</span>
        </div>
        <div class="icon-span">
          <img src="./img/forphone/time.png" alt="" />
          <span>{{ personInfo.project.generate_time }}</span>
        </div>
      </div>
    </div>
    <div class="danger-project">
<!--      <div class="blood-vessel-age" v-if="personInfo.patient.vascular_age">-->
<!--        你的血管年龄为-->
<!--        <span class="num"> {{ personInfo.patient.vascular_age }} </span>岁-->
<!--      </div>-->
      <!-- <div class="text">综合评估，你有高血压风险</div> -->
      <div class="img-position">
        <span v-if="allAbnormal > 0">
          <img class="danger" src="./img/forphone/danger.png" />
          <div class="danger-point">风险项</div>
          <div class="count">
            <div class="number">{{ allAbnormal }}</div>
            <span class="count-span">项异常</span>
          </div>
        </span>
        <span v-else>
          <img class="danger" src="./img/forphone/health.png" />
          <div class="health-point">健康</div>
          <div class="health-span">无异常</div>
        </span>
        <div v-if="personInfo.bg.fbg_value==''"  :class="['box diabetes empty']">血糖</div>
        <div v-else :class="['box diabetes', bgAbnormal > 0 ? 'abnormal' : 'normal']">
          <span>{{bgAbnormal>0?'血糖异常':'血糖正常'}}</span>
          <img
            v-if="bgAbnormal > 0"
            class="warning"
            src="@/views/reportCenter/img/forphone/warning.png"
          />
        </div>
        <div v-if="personInfo.bp.sbp_value=='' && personInfo.bp.dbp_value==''"  :class="['box hypertension empty']">血压</div>
        <div v-else
          :class="['box hypertension', bpAbnormal > 0 ? 'abnormal' : 'normal']"
        >
          <span>{{bpAbnormal>0?'血压异常':'血压正常'}}</span>
          <img
            v-if="bpAbnormal > 0"
            class="warning"
            src="@/views/reportCenter/img/forphone/warning.png"
          />
        </div>
        <div v-if="personInfo.bp.pulse_value==''"  :class="['box pulse empty']">脉搏</div>
        <div v-else
          :class="[
                'box pulse',
                pulseAbnormal > 0 ? 'abnormal' : 'normal',
              ]"
        >
          <span> {{pulseAbnormal>0?'脉搏异常':'脉搏正常'}}</span>
          <img
            v-if="pulseAbnormal > 0"
            class="error"
            src="@/views/reportCenter/img/forpc/warning.png"
          />
        </div>
        <div v-if="personInfo.blood_fat.tc_value==''"  :class="['box bloodfat empty']">血脂</div>
        <div v-else
          :class="[
            'box bloodfat',
            bloodFatAbnormal > 0 ? 'abnormal' : 'normal',
          ]"
        >
          <span> {{bloodFatAbnormal>0?'血脂异常':'血脂正常'}}</span>
          <img
            v-if="bloodFatAbnormal > 0"
            class="warning"
            src="@/views/reportCenter/img/forphone/warning.png"
          />
        </div>
        <div v-if="personInfo.height_weight.bmi_value==''"  :class="['box fat empty']">BMI</div>
        <div v-else :class="['box fat', BMIAbnormal > 0 ? 'abnormal' : 'normal']">
          <span>{{BMIAbnormal>0?'BMI异常':'BMI正常'}}</span>
          <img
            v-if="BMIAbnormal > 0"
            class="warning"
            src="@/views/reportCenter/img/forphone/warning.png"
          />
        </div>
        <div v-if="personInfo.breath.fev1_fvc_value==''"  :class="['box copd empty']">肺功能</div>
        <div v-else :class="['box copd', breatheAbnormal > 0 ? 'abnormal' : 'normal']">
          <span style="font-size:13px" >{{breatheAbnormal>0?'肺功能异常':'肺功能正常'}}</span>
          <img
            v-if="breatheAbnormal > 0"
            class="warning"
            src="@/views/reportCenter/img/forphone/warning.png"
          />
        </div>
        <div v-if="personInfo.as.rabi_value=='' && personInfo.as.labi_value==''"  :class="['box arteriosclerosis empty']">动脉硬化</div>
        <div v-else
          :class="[
            'box arteriosclerosis',
            asAbnormal > 0 ? 'abnormal' : 'normal',
          ]"
        >
          <span :class="asAbnormal>0?'':'small_font_size'">{{asAbnormal>0?'动脉硬化':'无动脉硬化'}} </span>
          <img
            v-if="asAbnormal > 0"
            class="warning"
            src="@/views/reportCenter/img/forphone/warning.png"
          />
        </div>
        <div v-if="personInfo.eye_examine.left_eye_dignosis=='' && personInfo.eye_examine.right_eye_dignosis==''"  :class="['box retinopathy empty']">眼底病变</div>
        <div v-else
          :class="['box retinopathy', eyeAbnormal > 0 ? 'abnormal' : 'normal']"
        >
          <span :class="eyeAbnormal>0?'':'small_font_size'">{{eyeAbnormal>0?'眼底病变':'无眼底病变'}}</span>
          <img
            v-if="eyeAbnormal > 0"
            class="warning"
            src="@/views/reportCenter/img/forphone/warning.png"
          />
        </div>
        <div
          class="smallbox smoking abnormal"
          v-if="personInfo.risk_factor.smoking > 0"
        >
          <span>吸烟</span>
        </div>
        <div
          class="smallbox drinking abnormal"
          v-if="personInfo.risk_factor.drinking > 0"
        >
          <span>饮酒</span>
        </div>
        <div
          class="smallbox sleeplessness abnormal"
          v-if="personInfo.risk_factor.sleeplessness > 0"
        >
          <span>失眠</span>
        </div>
        <div
          class="normalbox cvd abnormal"
          v-if="personInfo.risk_factor.cvd > 0"
        >
          <span>心脑血管疾病史</span>
        </div>
        <div
          class="normalbox exercise_less abnormal"
          v-if="personInfo.risk_factor.exercise_less > 0"
        >
          <span>活动量小</span>
        </div>
      </div>
    </div>
    <!-- <van-tabs v-model="active" sticky>
      <van-tab title="异常指标解读"> -->
        <div class="padding-div">
          <div v-if="allAbnormal > 0" class="abnormal-index">
            <div class="abnormal-index-word">
              你有
              <span class="abnormal-index-number">{{ allAbnormal }}</span>
              个异常指标
            </div>
            <div class="abnormal-index-percent">
              你共测了{{ countNum }}个指标，其中{{
                ((allAbnormal / countNum) * 100).toFixed(0)
              }}%异常
            </div>
          </div>
          <div v-else class="abnormal-index">
            <div class="abnormal-index-percent">
              您共测了{{ countNum }}个指标，未检测到异常
            </div>
          </div>
          <!-- 空腹血浆血糖 -->
          <div class="abnormal-index-tab" v-if="fbgAbnormal > 0">
            <div class="title">
              <div class="name">空腹血浆血糖</div>
              <div class="state blue">
                {{personInfo.bg.abnormal_info_fbg_value.status_display}}
              </div>
            </div>
            <div class="index">
              <span class="num">{{ personInfo.bg.fbg_value }}</span>
              <span class="unit">mmol/L</span>
            </div>
            <div class="introduce">
              在隔夜空腹（至少8～10小时未进任何热量食物）后，第二天早餐前采的血浆检测出的血糖值。是临床诊断糖尿病的金标准。
            </div>
            <itemReportPhone
              v-if="personInfo.bg.abnormal_info_fbg_value"
              code="fbg_value"
              :info="personInfo.bg"
            />
          </div>
          <!-- 随机末梢血糖 -->
          <div class="abnormal-index-tab" v-if="gluAbnormal > 0">
            <div class="title">
              <div class="name">随机末梢血糖</div>
              <div class="state blue">
                {{personInfo.bg.abnormal_info_glu_value.status_display}}
              </div>
            </div>
            <div class="index">
              <span class="num">{{ personInfo.bg.glu_value }}</span>
              <span class="unit">mmol/L</span>
            </div>
            <div class="introduce">
              随机末梢血糖是指任意时间采取末梢指血所测量得到的葡萄糖含量值。它是筛查糖尿病的便捷指标。
            </div>
            <itemReportPhone
              v-if="personInfo.bg.abnormal_info_glu_value"
              code="glu_value"
              :info="personInfo.bg"
            />
          </div>
          <!-- 糖化血红蛋白 -->
          <div class="abnormal-index-tab" v-if="hba1cAbnormal > 0">
            <div class="title">
              <div class="name">糖化血红蛋白</div>
              <div class="state blue">
                {{personInfo.bg.abnormal_info_hba1c_value.status_display}}
              </div>
            </div>
            <div class="index">
              <span class="num">{{ personInfo.bg.hba1c_value }}</span>
              <span class="unit">%</span>
            </div>
            <div class="introduce">
              红细胞中的血红蛋白与血清中的糖类（主要指葡萄糖）通过非酶反应相结合的产物，即糖化血红蛋白，临床上计算的是糖化血红蛋白数量占血液中血红蛋白总量的百分比。指标意义：①作为糖尿病的补充诊断标准；②临床判断糖尿病患者控制血糖是否达标的金标准。
            </div>
            <itemReportPhone
              v-if="personInfo.bg.abnormal_info_hba1c_value"
              code="hba1c_value"
              :info="personInfo.bg"
            />
          </div>
          <!-- 血清总胆固醇 -->
          <div class="abnormal-index-tab" v-if="tcAbnormal > 0">
            <div class="title">
              <div class="name">血清总胆固醇</div>
              <div class="state blue">
                {{personInfo.blood_fat.abnormal_info_tc_value.status_display}}
              </div>
            </div>
            <div class="index">
              <span class="num">{{ personInfo.blood_fat.tc_value }}</span>
              <span class="unit">mmol/L</span>
            </div>
            <div class="introduce">
              指血液中各种脂蛋白所含胆固醇之总和。血清总胆固醇是诊断血脂异常的金标准；评估总体心血管危险。
            </div>
            <itemReportPhone
              v-if="personInfo.blood_fat.abnormal_info_tc_value"
              code="tc_value"
              :info="personInfo.blood_fat"
            />
          </div>
          <!-- 血清甘油三酯 -->
          <div class="abnormal-index-tab" v-if="tgAbnormal > 0">
            <div class="title">
              <div class="name">血清甘油三酯</div>
              <div class="state blue">
                {{personInfo.blood_fat.abnormal_info_tg_value.status_display}}
              </div>
            </div>
            <div class="index">
              <span class="num">{{ personInfo.blood_fat.tg_value }}</span>
              <span class="unit">mmol/L</span>
            </div>
            <div class="introduce">
              指血液中脂质的一种重要成分，是人体储存能量的重要形式。它是诊断血脂异常的金标准；评估总体心血管危险。
            </div>
            <itemReportPhone
              v-if="personInfo.blood_fat.abnormal_info_tg_value"
              code="tg_value"
              :info="personInfo.blood_fat"
            />
          </div>
          <!-- 血清低密度脂蛋白胆固醇 -->
          <div class="abnormal-index-tab" v-if="ldl_cAbnormal > 0">
            <div class="title">
              <div class="name">血清低密度脂蛋白胆固醇</div>
              <div class="state blue">
                {{personInfo.blood_fat.abnormal_info_ldl_c_value.status_display}}
              </div>
            </div>
            <div class="index">
              <span class="num">{{ personInfo.blood_fat.ldl_c_value }}</span>
              <span class="unit">mmol/L</span>
            </div>
            <div class="introduce">
              指血液中胆固醇含量最多的脂蛋白。血清低密度脂蛋白胆固醇是诊断血脂异常的金标准；评估总体心血管危险。
            </div>
            <itemReportPhone
              v-if="personInfo.blood_fat.abnormal_info_ldl_c_value"
              code="ldl_c_value"
              :info="personInfo.blood_fat"
            />
          </div>
          <!-- 血清高密度脂蛋白胆固醇 -->
          <div class="abnormal-index-tab" v-if="hdl_cAbnormal > 0">
            <div class="title">
              <div class="name">血清高密度脂蛋白胆固醇</div>
              <div class="state blue">
                {{personInfo.blood_fat.abnormal_info_hdl_c_value.status_display}}
              </div>
            </div>
            <div class="index">
              <span class="num">{{ personInfo.blood_fat.hdl_c_value }}</span>
              <span class="unit">mmol/L</span>
            </div>
            <div class="introduce">
              指血液中胆固醇颗粒最小的脂蛋白。血清高密度脂蛋白胆固醇是诊断血脂异常的金标准；评估总体心血管危险。
            </div>
            <itemReportPhone
              v-if="personInfo.blood_fat.abnormal_info_hdl_c_value"
              code="hdl_c_value"
              :info="personInfo.blood_fat"
            />
          </div>
          <!-- 收缩压 -->
          <div class="abnormal-index-tab" v-if="sbpAbnormal > 0">
            <div class="title">
              <div class="name">收缩压</div>
              <div class="state blue">
                {{personInfo.bp.abnormal_info_sbp_value.status_display}}
              </div>
            </div>
            <div class="index">
              <span class="num">{{ personInfo.bp.sbp_value }}</span>
              <span class="unit">mmHg</span>
            </div>
            <div class="introduce">
              收缩压是当人的心脏收缩时，动脉内压力最高值，也称作“高压”。是临床诊断高血压的金标准。
            </div>
            <itemReportPhone
              v-if="personInfo.bp.abnormal_info_sbp_value"
              code="sbp_value"
              :info="personInfo.bp"
            />
          </div>
          <!-- 舒张压 -->
          <div class="abnormal-index-tab" v-if="dbpAbnormal > 0">
            <div class="title">
              <div class="name">舒张压</div>
              <div class="state blue">
                {{personInfo.bp.abnormal_info_dbp_value.status_display}}
              </div>
            </div>
            <div class="index">
              <span class="num">{{ personInfo.bp.dbp_value }}</span>
              <span class="unit">mmHg</span>
            </div>
            <div class="introduce">
              当人的心脏收缩时，动脉内压力最低值，也称作“低压”。舒张压是临床诊断高血压的金标准。
            </div>
            <itemReportPhone
              v-if="personInfo.bp.abnormal_info_dbp_value"
              code="dbp_value"
              :info="personInfo.bp"
            />
          </div>
          <!-- 脉搏 -->
          <div class="abnormal-index-tab" v-if="pulseAbnormal > 0">
            <div class="title">
              <div class="name">脉搏</div>
              <div class="state blue">
                {{personInfo.bp.abnormal_info_pulse_value.status_display}}
              </div>
            </div>
            <div class="index">
              <span class="num">{{ personInfo.bp.pulse_value }}</span>
              <span class="unit">cpm</span>
            </div>
            <div class="introduce">
              人体表可触摸到的动脉搏动。脉搏是日常筛查心律失常的便捷指标。
            </div>
            <itemReportPhone
              v-if="personInfo.bp.abnormal_info_pulse_value"
              code="pulse_value"
              :info="personInfo.bp"
            />
          </div>
          <!-- 体重指数 -->
          <div class="abnormal-index-tab" v-if="BMIAbnormal > 0">
            <div class="title">
              <div class="name">体重指数</div>
              <div class="state blue">
                {{personInfo.height_weight.abnormal_info_bmi_value.status_display}}
              </div>
            </div>
            <div class="index">
              <span class="num">{{ personInfo.height_weight.bmi_value }}</span>
              <span class="unit">kg/m²</span>
            </div>
            <div class="introduce">
              体重（Kg）÷身高平方（m²）。它是临床诊断超重、肥胖的金标准。
            </div>
            <itemReportPhone
              v-if="personInfo.height_weight.abnormal_info_bmi_value"
              code="bmi_value"
              :info="personInfo.height_weight"
            />
          </div>
          <!-- 内脏脂肪面积 -->
          <div class="abnormal-index-tab" v-if="VFAAbnormal > 0">
            <div class="title">
              <div class="name">内脏脂肪面积</div>
              <div class="state blue">
                {{personInfo.vf.abnormal_info_vat_value.status_display}}
              </div>
            </div>
            <div class="index">
              <span class="num">{{ personInfo.vf.vat_value }}</span>
              <span class="unit">cm²</span>
            </div>
            <div class="introduce">
              包裹在人体内脏器官周围，存在于腹腔内的脂肪面积。内脏脂肪面积是腹型肥胖诊断的金标准。
            </div>
            <itemReportPhone
              v-if="personInfo.vf.abnormal_info_vat_value"
              code="vat_value"
              :info="personInfo.vf"
            />
          </div>
          <!-- 用力肺活量 -->
          <!-- <div class="abnormal-index-tab">
            <div class="title">
              <div class="name">用力肺活量</div>
              <div class="state">中度</div>
            </div>
            <table class="table">
              <tr>
                <th>实际值</th>
                <th>预期值</th>
                <th>占比</th>
                <th>结论</th>
              </tr>
              <tr>
                <td>1.50</td>
                <td>3.00</td>
                <td>50%</td>
                <td>中度</td>
              </tr>
            </table>
            <div class="introduce">
              用力最大吸气后，尽力尽快呼气所能呼出的最大气体量。它反映了肺通气功能。
            </div>
          </div> -->
          <!-- 第一秒用力呼气容积 -->
          <!-- <div class="abnormal-index-tab">
            <div class="title">
              <div class="name">第一秒用力呼气容积</div>
              <div class="state">中度</div>
            </div>
            <table class="table">
              <tr>
                <th>实际值</th>
                <th>预期值</th>
                <th>占比</th>
                <th>结论</th>
              </tr>
              <tr>
                <td>1.50</td>
                <td>3.00</td>
                <td>50%</td>
                <td>中度</td>
              </tr>
            </table>
            <div class="introduce">
              最大吸气至肺总量位后开始呼气，第一秒钟呼出的气体量。它反映了肺通气功能。
            </div>
          </div> -->
          <!-- 一秒率 -->
          <div class="abnormal-index-tab" v-if="breatheAbnormal > 0">
            <div class="title">
              <div class="name">一秒率</div>
              <div class="state blue">
                {{personInfo.breath.abnormal_info_fev1_fvc_value.status_display}}
              </div>
            </div>
            <div class="index">
              <span class="num">{{ personInfo.breath.fev1_fvc_value }}</span>
              <span class="unit">%</span>
            </div>
            <div class="introduce">
              最常用的判断有无气流阻塞的呼吸机指标参数；临床上用于诊断慢性阻塞性肺部疾病；鉴别诊断支气管哮喘的重要指标。
            </div>
            <itemReportPhone
              v-if="personInfo.breath.abnormal_info_fev1_fvc_value"
              code="fev1_fvc_value"
              :info="personInfo.breath"
            />
          </div>
          <!-- 踝臂指数（左侧） -->
          <div class="abnormal-index-tab" v-if="labiAbnormal > 0">
            <div class="title">
              <div class="name">
                踝臂指数<span class="left-right">（左侧）</span>
              </div>
              <div class="state blue">
                {{personInfo.as.abnormal_info_labi_value.status_display}}
              </div>
            </div>
            <div class="index">
              <span class="num">{{ personInfo.as.labi_value }}</span>
              <!-- <span class="unit"></span> -->
            </div>
            <div class="introduce">
              踝部胫后动脉或胫前动脉的收缩压÷上臂肱动脉的收缩压。它是周围压力传导动脉狭窄性疾病的筛查和诊断；可预测及评估心脑血管事件以及死亡风险。
            </div>
            <itemReportPhone
              v-if="personInfo.as.abnormal_info_labi_value"
              code="labi_value"
              :info="personInfo.as"
            />
          </div>
          <!-- 踝臂指数（右侧） -->
          <div class="abnormal-index-tab" v-if="rabiAbnormal > 0">
            <div class="title">
              <div class="name">
                踝臂指数<span class="left-right">（右侧）</span>
              </div>
              <div class="state blue">
                {{personInfo.as.abnormal_info_rabi_value.status_display}}
              </div>
            </div>
            <div class="index">
              <span class="num">{{ personInfo.as.rabi_value }}</span>
              <!-- <span class="unit"></span> -->
            </div>
            <div class="introduce">
              踝部胫后动脉或胫前动脉的收缩压÷上臂肱动脉的收缩压。它是周围压力传导动脉狭窄性疾病的筛查和诊断；可预测及评估心脑血管事件以及死亡风险。
            </div>
            <itemReportPhone
              v-if="personInfo.as.abnormal_info_rabi_value"
              code="rabi_value"
              :info="personInfo.as"
            />
          </div>
          <!-- 脉搏波传播速度（左侧） -->
          <div class="abnormal-index-tab" v-if="lpwvAbnormal > 0">
            <div class="title">
              <div class="name">
                脉搏波传播速度<span class="left-right">（左侧）</span>
              </div>
              <div class="state blue">
                {{personInfo.as.abnormal_info_lpwv_value.status_display}}
              </div>
            </div>
            <div class="index">
              <span class="num">{{ personInfo.as.lpwv_value }}</span>
              <span class="unit">cm/s</span>
            </div>
            <div class="introduce">
              心脏每次搏动时射血产生的沿大动脉壁传播的压力波的传导速度。指标意义：①检测大动脉的僵硬度；②辅助诊断四肢动脉和胸、腹主动脉狭窄；③预测心血管病的风险；④评估早期血管衰老程度。升高表示：①大动脉僵硬；②四肢动脉或胸腹主动脉疑似狭窄；③心血管病发生发展的风险高；④发生心血管事件及死亡的风险高。
            </div>
            <itemReportPhone
              v-if="personInfo.as.abnormal_info_lpwv_value"
              code="lpwv_value"
              :info="personInfo.as"
            />
          </div>
          <!-- 脉搏波传播速度（右侧） -->
          <div class="abnormal-index-tab" v-if="rpwvAbnormal > 0">
            <div class="title">
              <div class="name">
                脉搏波传播速度<span class="left-right">（右侧）</span>
              </div>
              <div class="state blue">
                {{personInfo.as.abnormal_info_rpwv_value.status_display}}
              </div>
            </div>
            <div class="index">
              <span class="num">{{ personInfo.as.rpwv_value }}</span>
              <span class="unit">cm/s</span>
            </div>
            <div class="introduce">
              心脏每次搏动时射血产生的沿大动脉壁传播的压力波的传导速度。指标意义：①检测大动脉的僵硬度；②辅助诊断四肢动脉和胸、腹主动脉狭窄；③预测心血管病的风险；④评估早期血管衰老程度。升高表示：①大动脉僵硬；②四肢动脉或胸腹主动脉疑似狭窄；③心血管病发生发展的风险高；④发生心血管事件及死亡的风险高。
            </div>
            <itemReportPhone
              v-if="personInfo.as.abnormal_info_rpwv_value"
              code="rpwv_value"
              :info="personInfo.as"
            />
          </div>
          <!-- 眼底照相 -->
          <div class="abnormal-index-tab" v-if="eyeAbnormal > 0">
            <div class="title">
              <div class="name">眼底照相</div>
            </div>
            <div class="dignosis">
              左眼：{{ personInfo.eye_examine.left_eye_dignosis }}
            </div>
            <div class="img-bottom">
              <img
                class="eyeImg"
                :src="personInfo.eye_examine.left_eye_picture_one"
              />
              <img
                class="eyeImg"
                :src="personInfo.eye_examine.left_eye_picture_two"
              />
            </div>
            <div class="dignosis">
              右眼：{{ personInfo.eye_examine.right_eye_dignosis }}
            </div>
            <div class="img-bottom">
              <img
                class="eyeImg"
                :src="personInfo.eye_examine.right_eye_picture_one"
              />
              <img
                class="eyeImg"
                :src="personInfo.eye_examine.right_eye_picture_two"
              />
            </div>
          </div>
        </div>
      <!-- </van-tab> -->
      <!-- <van-tab title="全部指标展示"> -->
        <div class="all-index">
          <div class="all-index-text">全部指标展示</div>
          <!-- 血糖 -->
          <div class="tab">
            <p class="title">
              <span>
                <img src="@/views/reportCenter/img/forphone/bloodSuger.png" />
                <span>血糖</span>
              </span>
              <span class="noTest" v-if="bgTested">未检查</span>
              <span class="extend" @click="change('bg')" v-else>
                <div class="extend-abnormal" v-if="bgAbnormal > 0">
                  <p class="extend-abnormal-number">{{ bgAbnormal }}</p>
                  <p class="extend-abnormal-span">项异常</p>
                </div>
                <!-- <van-icon v-if="bgExtend" name="arrow-up" />
                <van-icon v-if="!bgExtend" name="arrow-down" /> -->
              </span>
            </p>
            <div class="cellContent" v-if="bgExtend && !bgTested">
              <div class="flex-cell">
                <div class="cell-title">空腹血浆血糖</div>
                <div class="cell-value">
                  <span :class="fbgAbnormal > 0 ? 'activeRed' : ''">
                    {{ personInfo.bg.fbg_value }}
                  </span>
                  <span> mmol/L</span>
                  <span class="cell-standard" v-if="personInfo.bg.abnormal_info_fbg_value.normal_range">（{{personInfo.bg.abnormal_info_fbg_value.normal_range}}）</span>
                </div>
              </div>
              <div class="flex-cell">
                <div class="cell-title">随机末梢血糖</div>
                <div class="cell-value">
                  <span :class="gluAbnormal ? 'activeRed' : ''">
                    {{ personInfo.bg.glu_value }}
                  </span>
                  <span> mmol/L</span>
                  <span class="cell-standard" v-if="personInfo.bg.abnormal_info_glu_value.normal_range">（{{personInfo.bg.abnormal_info_glu_value.normal_range}}）</span>
                </div>
              </div>
              <div class="flex-cell">
                <div class="cell-title">糖化血红蛋白</div>
                <div class="cell-value">
                  <span :class="hba1cAbnormal > 0 ? 'activeRed' : ''">
                    {{ personInfo.bg.hba1c_value }}
                  </span>
                  <span> %</span>
                  <span class="cell-standard" v-if="personInfo.bg.abnormal_info_hba1c_value.normal_range">（{{personInfo.bg.abnormal_info_hba1c_value.normal_range}}）</span>
                </div>
              </div>
            </div>
          </div>
          <!-- 血压 -->
          <div class="tab">
            <p class="title">
              <span>
                <img
                  src="@/views/reportCenter/img/forphone/bloodPressure.png"
                />
                <span>血压</span>
              </span>
              <span class="noTest" v-if="bpTested">未检查</span>
              <span class="extend" @click="change('bp')" v-else>
                <div class="extend-abnormal" v-if="bpAbnormal > 0">
                  <p class="extend-abnormal-number">{{ bpAbnormal }}</p>
                  <p class="extend-abnormal-span">项异常</p>
                </div>
                <!-- <van-icon v-if="bpExtend" name="arrow-up" />
                <van-icon v-if="!bpExtend" name="arrow-down" /> -->
              </span>
            </p>
            <div class="cellContent" v-if="bpExtend && !bpTested">
              <div class="flex-cell">
                <div class="cell-title">收缩压</div>
                <div class="cell-value">
                  <span :class="sbpAbnormal > 0 ? 'activeRed' : ''">
                    {{ personInfo.bp.sbp_value }}
                  </span>
                  <span> mmHg</span>
                  <span class="cell-standard" v-if="personInfo.bp.abnormal_info_sbp_value.normal_range">（{{personInfo.bp.abnormal_info_sbp_value.normal_range}}）</span>
                </div>
              </div>
              <div class="flex-cell">
                <div class="cell-title">舒张压</div>
                <div class="cell-value">
                  <span :class="dbpAbnormal > 0 ? 'activeRed' : ''">
                    {{ personInfo.bp.dbp_value }}
                  </span>
                  <span> mmHg</span>
                  <span class="cell-standard" v-if="personInfo.bp.abnormal_info_dbp_value.normal_range">（{{personInfo.bp.abnormal_info_dbp_value.normal_range}}）</span>
                </div>
              </div>
              <div class="flex-cell">
                <div class="cell-title">脉搏</div>
                <div class="cell-value">
                  <span :class="pulseAbnormal > 0 ? 'activeRed' : ''">
                    {{ personInfo.bp.pulse_value }}
                  </span>
                  <span> cpm</span>
                  <span class="cell-standard" v-if="personInfo.bp.abnormal_info_pulse_value.normal_range">（{{personInfo.bp.abnormal_info_pulse_value.normal_range}}）</span>
                </div>
              </div>
            </div>
          </div>
          <!-- 血脂 -->
          <div class="tab">
            <p class="title">
              <span>
                <img src="@/views/reportCenter/img/forphone/bloodFat.png" />
                <span>血脂</span>
              </span>
              <span class="noTest" v-if="bloodFatTested">未检查</span>
              <span class="extend" @click="change('bloodFat')" v-else>
                <div class="extend-abnormal" v-if="bloodFatAbnormal > 0">
                  <p class="extend-abnormal-number">{{ bloodFatAbnormal }}</p>
                  <p class="extend-abnormal-span">项异常</p>
                </div>
                <!-- <van-icon v-if="bloodFatExtend" name="arrow-up" />
                <van-icon v-if="!bloodFatExtend" name="arrow-down" /> -->
              </span>
            </p>
            <div class="cellContent" v-if="bloodFatExtend && !bloodFatTested">
              <div class="flex-cell">
                <div class="cell-title">血清总胆固醇</div>
                <div class="cell-value">
                  <span :class="tcAbnormal > 0 ? 'activeRed' : ''">
                    {{ personInfo.blood_fat.tc_value }}
                  </span>
                  <span> mmol/L</span>
                  <span class="cell-standard" v-if="personInfo.blood_fat.abnormal_info_tc_value.normal_range">（{{personInfo.blood_fat.abnormal_info_tc_value.normal_range}}）</span>
                </div>
              </div>
              <div class="flex-cell">
                <div class="cell-title">血清甘油三酯</div>
                <div class="cell-value">
                  <span :class="tgAbnormal > 0 ? 'activeRed' : ''">
                    {{ personInfo.blood_fat.tg_value }}
                  </span>
                  <span> mmol/L</span>
                  <span class="cell-standard" v-if="personInfo.blood_fat.abnormal_info_tg_value.normal_range">（{{personInfo.blood_fat.abnormal_info_tg_value.normal_range}}）</span>
                </div>
              </div>
              <div class="flex-cell">
                <div class="cell-title">血清低密度脂蛋白胆固醇</div>
                <div class="cell-value">
                  <span :class="ldl_cAbnormal > 0 ? 'activeRed' : ''">
                    {{ personInfo.blood_fat.ldl_c_value }}
                  </span>
                  <span> mmol/L</span>
                  <span class="cell-standard" v-if="personInfo.blood_fat.abnormal_info_ldl_c_value.normal_range">（{{personInfo.blood_fat.abnormal_info_ldl_c_value.normal_range}}）</span>
                </div>
              </div>
              <div class="flex-cell">
                <div class="cell-title">血清高密度脂蛋白胆固醇</div>
                <div class="cell-value">
                  <span :class="hdl_cAbnormal > 0 ? 'activeRed' : ''">
                    {{ personInfo.blood_fat.hdl_c_value }}
                  </span>
                  <span> mmol/L</span>
                  <span class="cell-standard" v-if="personInfo.blood_fat.abnormal_info_hdl_c_value.normal_range">（{{personInfo.blood_fat.abnormal_info_hdl_c_value.normal_range}}）</span>
                </div>
              </div>
            </div>
          </div>
          <!-- 体重指数 -->
          <div class="tab">
            <p class="title">
              <span>
                <img src="@/views/reportCenter/img/forphone/BMI.png" />
                <span>体重指数</span>
              </span>
              <span class="noTest" v-if="BMITested">未检查</span>
              <span class="extend" @click="change('BMI')" v-else>
                <div class="extend-abnormal" v-if="BMIAbnormal > 0">
                  <p class="extend-abnormal-number">{{ BMIAbnormal }}</p>
                  <p class="extend-abnormal-span">项异常</p>
                </div>
                <!-- <van-icon v-if="BMIExtend" name="arrow-up" />
                <van-icon v-if="!BMIExtend" name="arrow-down" /> -->
              </span>
            </p>
            <div class="cellContent" v-if="BMIExtend && !BMITested">
              <div class="flex-cell">
                <div class="cell-title">体重指数</div>
                <div class="cell-value">
                  <span :class="BMIAbnormal > 0 ? 'activeRed' : ''">
                    {{ personInfo.height_weight.bmi_value }}
                  </span>
                  <span> kg/m²</span>
                  <span class="cell-standard" v-if="personInfo.height_weight.abnormal_info_bmi_value.normal_range">（{{personInfo.height_weight.abnormal_info_bmi_value.normal_range}}）</span>
                </div>
              </div>
            </div>
          </div>
          <!-- 肺功能 -->
          <div class="tab">
            <p class="title">
              <span>
                <img src="@/views/reportCenter/img/forphone/breathe.png" />
                <span>肺功能</span>
              </span>
              <span class="noTest" v-if="breatheTested">未检查</span>
              <span class="extend" @click="change('breathe')" v-else>
                <div class="extend-abnormal" v-if="breatheAbnormal > 0">
                  <p class="extend-abnormal-number">{{ breatheAbnormal }}</p>
                  <p class="extend-abnormal-span">项异常</p>
                </div>
                <!-- <van-icon v-if="breatheExtend" name="arrow-up" />
                <van-icon v-if="!breatheExtend" name="arrow-down" /> -->
              </span>
            </p>
            <div class="cellContent" v-if="breatheExtend && !breatheTested">
              <div class="flex-cell">
                <div class="cell-title">用力肺活量</div>
                <div class="cell-value">
                  <span>
                    {{ personInfo.breath.fvc_value }}
                  </span>
                  <span>L</span>
                </div>
              </div>
              <div class="flex-cell">
                <div class="cell-title">第一秒用力呼气容积</div>
                <div class="cell-value">
                  <span>
                    {{ personInfo.breath.fev1_value }}
                  </span>
                  <span>L</span>
                </div>
              </div>
              <div class="flex-cell">
                <div class="cell-title">一秒率</div>
                <div class="cell-value">
                  <span :class="breatheAbnormal > 0 ? 'activeRed' : ''">
                    {{ personInfo.breath.fev1_fvc_value }}
                  </span>
                  <span>%</span>
                  <span class="cell-standard" v-if="personInfo.breath.abnormal_info_fev1_fvc_value.normal_range">（{{personInfo.breath.abnormal_info_fev1_fvc_value.normal_range}}）</span>
                </div>
              </div>
            </div>
          </div>
          <!-- 动脉硬化检测 -->
          <div class="tab">
            <p class="title">
              <span>
                <img src="@/views/reportCenter/img/forphone/ABI.png" />
                <span>动脉硬化检测</span>
              </span>
              <span class="noTest" v-if="asTested">未检查</span>
              <span class="extend" @click="change('as')" v-else>
                <div class="extend-abnormal" v-if="asAbnormal > 0">
                  <p class="extend-abnormal-number">{{ asAbnormal }}</p>
                  <p class="extend-abnormal-span">项异常</p>
                </div>
                <!-- <van-icon v-if="asExtend" name="arrow-up" />
                <van-icon v-if="!asExtend" name="arrow-down" /> -->
              </span>
            </p>
            <div class="cellContent" v-if="asExtend && !asTested">
              <table class="table">
                <tr>
                  <th>指标名称</th>
                  <th>缩写</th>
                  <th>左</th>
                  <th>右</th>
                </tr>
                <tr>
                  <td>踝臂指数</td>
                  <td>ABI</td>
                  <td :class="labiAbnormal > 0 ? 'activeRed' : ''">
                    {{ personInfo.as.labi_value }}
                  </td>
                  <td :class="rabiAbnormal > 0 ? 'activeRed' : ''">
                    {{ personInfo.as.rabi_value }}
                  </td>
                </tr>
                <tr>
                  <td>脉搏波传播速度</td>
                  <td>PWV</td>
                  <td :class="lpwvAbnormal ? 'activeRed' : ''">
                    {{ personInfo.as.lpwv_value }}
                  </td>
                  <td :class="rpwvAbnormal ? 'activeRed' : ''">
                    {{ personInfo.as.rpwv_value }}
                  </td>
                </tr>
              </table>
            </div>
          </div>
          <!-- 内脏脂肪检查 -->
          <div class="tab">
            <p class="title">
              <span>
                <img src="@/views/reportCenter/img/forphone/VFA.png" />
                <span>内脏脂肪检查</span>
              </span>
              <span class="noTest" v-if="VFATested">未检查</span>
              <span class="extend" @click="change('VFA')" v-else>
                <div class="extend-abnormal" v-if="VFAAbnormal > 0">
                  <p class="extend-abnormal-number">{{ VFAAbnormal }}</p>
                  <p class="extend-abnormal-span">项异常</p>
                </div>
                <!-- <van-icon v-if="VFAExtend" name="arrow-up" />
                <van-icon v-if="!VFAExtend" name="arrow-down" /> -->
              </span>
            </p>
            <div class="cellContent" v-if="VFAExtend && !VFATested">
              <div class="flex-cell">
                <div class="cell-title">内脏脂肪面积</div>
                <div class="cell-value">
                  <span :class="VFAAbnormal > 0 ? 'activeRed' : ''">
                    {{ personInfo.vf.vat_value }}
                  </span>
                  <span>cm²</span>
                  <span class="cell-standard" v-if="personInfo.vf.abnormal_info_vat_value.normal_range">（{{personInfo.vf.abnormal_info_vat_value.normal_range}}）</span>
                </div>
              </div>
            </div>
          </div>
          <!-- 眼底照相 -->
          <div class="tab">
            <p class="title">
              <span>
                <img src="@/views/reportCenter/img/forphone/eye.png" />
                <span>眼底照相</span>
              </span>
              <span class="noTest" v-if="eyeTested">未检查</span>
              <span class="extend" @click="change('eye')" v-else>
                <div class="extend-abnormal" v-if="eyeAbnormal > 0">
                  <p class="extend-abnormal-number">{{ eyeAbnormal }}</p>
                  <p class="extend-abnormal-span">项异常</p>
                </div>
                <!-- <van-icon v-if="eyeExtend" name="arrow-up" />
                <van-icon v-if="!eyeExtend" name="arrow-down" /> -->
              </span>
            </p>
            <div class="cellContent" v-if="eyeExtend && !eyeTested">
              <div class="flex-cell">
                <div class="cell-title">左眼</div>
                <div class="cell-value">
                  <span :class="lefteyeAbnormal > 0 ? 'activeRed' : ''">
                    {{ personInfo.eye_examine.left_eye_dignosis }}
                  </span>
                </div>
              </div>
              <div class="flex-cell">
                <div class="cell-title">右眼</div>
                <div class="cell-value">
                  <span :class="righteyeAbnormal > 0 ? 'activeRed' : ''">
                    {{ personInfo.eye_examine.right_eye_dignosis }}
                  </span>
                </div>
              </div>
              <!-- <div class="flex-cell">
                <div class="cell-title">结论描述</div>
                <div class="cell-value">
                  {{ personInfo.eye_examine.describe }}
                </div>
              </div> -->
            </div>
          </div>
        </div>
      <!-- </van-tab>
    </van-tabs> -->
  </div>
</template>
<script>
import { reportProduce } from "@/api/saasReport";
import itemReportPhone from "./itemReportPhone.vue";

export default {
  components: {
    itemReportPhone
  },
  data() {
    return {
      personInfo: {
        patient: {},
        hospital_info: {},
        project: {},
        bg: {}, //血糖
        bp: {}, //血压
        blood_fat: {}, //血脂
        height_weight: {}, //体重指数
        breath: {}, //肺功能
        as: {}, //动脉硬化检测
        vf: {}, //内脏脂肪检查
        eye_examine: {}, //眼底照相
        risk_factor: {}, //风险项
      },
      // active: 0,
      bgExtend: true,
      bpExtend: true,
      bloodFatExtend: true,
      BMIExtend: true,
      breatheExtend: true,
      asExtend: true,
      VFAExtend: true,
      eyeExtend: true,
      eyesTextArray: ["未见明显糖尿病视网膜改变", "无糖尿病视网膜病变"],
    };
  },
  created() {
    this.getReportProduce();
  },
  computed: {
    bgTested() {
      return (
        this.personInfo.bg.fbg_value == "" &&
        this.personInfo.bg.glu_value == "" &&
        this.personInfo.bg.hba1c_value == ""
      );
    },
    bpTested() {
      return (
        this.personInfo.bp.sbp_value == "" &&
        this.personInfo.bp.dbp_value == "" &&
        this.personInfo.bp.pulse_value == ""
      );
    },
    bloodFatTested() {
      return (
        this.personInfo.blood_fat.tc_value == "" &&
        this.personInfo.blood_fat.tg_value == "" &&
        this.personInfo.blood_fat.ldl_c_value == "" &&
        this.personInfo.blood_fat.hdl_c_value == ""
      );
    },
    BMITested() {
      return this.personInfo.height_weight.bmi_value == "";
    },
    breatheTested() {
      return (
        this.personInfo.breath.fvc_value == "" &&
        this.personInfo.breath.fev1_value == "" &&
        this.personInfo.breath.fev1_fvc_value == ""
      );
    },
    asTested() {
      return (
        this.personInfo.as.rabi_value == "" &&
        this.personInfo.as.labi_value == "" &&
        this.personInfo.as.rpwv_value == "" &&
        this.personInfo.as.lpwv_value == ""
      );
    },
    VFATested() {
      return this.personInfo.vf.vat_value == "";
    },
    eyeTested() {
      return (
        this.personInfo.eye_examine.left_eye_dignosis == "" &&
        this.personInfo.eye_examine.right_eye_dignosis == ""
      );
    },
    // 共测了*个指标
    countNum() {
      let count = 0;
      if (this.personInfo.bg.fbg_value) {
        count += 1;
      }
      if (this.personInfo.bg.glu_value) {
        count += 1;
      }
      if (this.personInfo.bg.hba1c_value) {
        count += 1;
      }
      if (this.personInfo.bp.sbp_value) {
        count += 1;
      }
      if (this.personInfo.bp.dbp_value) {
        count += 1;
      }
      if (this.personInfo.bp.pulse_value) {
        count += 1;
      }
      if (this.personInfo.blood_fat.tc_value) {
        count += 1;
      }
      if (this.personInfo.blood_fat.tg_value) {
        count += 1;
      }
      if (this.personInfo.blood_fat.ldl_c_value) {
        count += 1;
      }
      if (this.personInfo.blood_fat.hdl_c_value) {
        count += 1;
      }
      if (this.personInfo.height_weight.bmi_value) {
        count += 1;
      }
      if (this.personInfo.breath.fvc_value) {
        count += 1;
      }
      if (this.personInfo.breath.fev1_value) {
        count += 1;
      }
      if (this.personInfo.breath.fev1_fvc_value) {
        count += 1;
      }
      if (this.personInfo.as.rabi_value) {
        count += 1;
      }
      if (this.personInfo.as.labi_value) {
        count += 1;
      }
      if (this.personInfo.as.rpwv_value) {
        count += 1;
      }
      if (this.personInfo.as.lpwv_value) {
        count += 1;
      }
      if (this.personInfo.vf.vat_value) {
        count += 1;
      }
      if (this.personInfo.eye_examine.right_eye_dignosis) {
        count += 1;
      }
      if (this.personInfo.eye_examine.left_eye_dignosis) {
        count += 1;
      }
      return count;
    },
    // 异常计算
    // 空腹血浆血糖
    fbgAbnormal() {
      if (this.getAbnormal( 'bg', 'fbg_value') !== 0) {
        return 1;
      } else {
        return 0;
      }
    },
    // 随机末梢血糖
    gluAbnormal() {
      if (this.getAbnormal( 'bg', 'glu_value') !== 0) {
        return 1;
      } else {
        return 0;
      }
    },
    // 糖化血红蛋白
    hba1cAbnormal() {
      if (this.getAbnormal( 'bg', 'hba1c_value') !== 0) {
        return 1;
      } else {
        return 0;
      }
    },
    // 收缩压
    sbpAbnormal() {
      if (this.getAbnormal( 'bp', 'sbp_value') !== 0) {
        return 1;
      } else {
        return 0;
      }
    },
    // 舒张压
    dbpAbnormal() {
      if (this.getAbnormal( 'bp', 'dbp_value') !== 0) {
        return 1;
      } else {
        return 0;
      }
    },
    // 脉搏
    pulseAbnormal() {
      if (this.getAbnormal( 'bp', 'pulse_value') !== 0) {
        return 1;
      } else {
        return 0;
      }
    },
    // 血清总胆固醇
    tcAbnormal() {
      if (this.getAbnormal( 'blood_fat', 'tc_value') !== 0) {
        return 1;
      } else {
        return 0;
      }
    },
    // 血清甘油三酯
    tgAbnormal() {
      if (this.getAbnormal( 'blood_fat', 'tg_value') !== 0) {
        return 1;
      } else {
        return 0;
      }
    },
    // 血清低密度脂蛋白胆固醇
    ldl_cAbnormal() {
      if (this.getAbnormal( 'blood_fat', 'ldl_c_value') !== 0) {
        return 1;
      } else {
        return 0;
      }
    },
    // 血清高密度脂蛋白胆固醇
    hdl_cAbnormal() {
      if (this.getAbnormal( 'blood_fat', 'hdl_c_value') !== 0) {
        return 1;
      } else {
        return 0;
      }
    },
    // 踝臂指数-右
    rabiAbnormal() {
      if (this.getAbnormal( 'as', 'rabi_value') !== 0) {
        return 1;
      } else {
        return 0;
      }
    },
    // 踝臂指数-左
    labiAbnormal() {
      if (this.getAbnormal( 'as', 'labi_value') !== 0) {
        return 1;
      } else {
        return 0;
      }
    },
    // 脉搏波传播速度-右
    rpwvAbnormal() {
      if (this.getAbnormal( 'as', 'rpwv_value') !== 0) {
        return 1;
      } else {
        return 0;
      }
    },
    // 脉搏波传播速度-左
    lpwvAbnormal() {
      if (this.getAbnormal( 'as', 'lpwv_value') !== 0) {
        return 1;
      } else {
        return 0;
      }
    },
    lefteyeAbnormal() {
      if (this.personInfo.eye_examine.left_eye_dignosis &&
        this.getAbnormal( 'eye_examine', 'left_eye_rechecked') !== 0) {
        return 1;
      } else {
        return 0;
      }
    },
    righteyeAbnormal() {
      if (this.personInfo.eye_examine.right_eye_dignosis &&
        this.getAbnormal( 'eye_examine', 'right_eye_rechecked') !== 0) {
        return 1;
      } else {
        return 0;
      }
    },
    // 风险项-血糖
    bgAbnormal() {
      return this.fbgAbnormal + this.gluAbnormal + this.hba1cAbnormal;
    },
    // 风险项-血压
    bpAbnormal() {
      return this.sbpAbnormal + this.dbpAbnormal;
    },
    // 风险项-血脂
    bloodFatAbnormal() {
      return (
        this.tcAbnormal +
        this.tgAbnormal +
        this.ldl_cAbnormal +
        this.hdl_cAbnormal
      );
    },
    // 风险项-BMI
    BMIAbnormal() {
      if (this.getAbnormal( 'height_weight', 'bmi_value') !== 0) {
        return 1;
      } else {
        return 0;
      }
    },
    // 风险项-肺功能-一秒率
    breatheAbnormal() {
      if (this.getAbnormal( 'breath', 'fev1_fvc_value') !== 0) {
        return 1;
      } else {
        return 0;
      }
    },
    // 风险项-动脉硬化
    asAbnormal() {
      return (
        this.rabiAbnormal +
        this.labiAbnormal +
        this.rpwvAbnormal +
        this.lpwvAbnormal
      );
    },
    // 风险项-内脏脂肪面积
    VFAAbnormal() {
      if (this.getAbnormal( 'vf', 'vat_value') !== 0) {
        return 1;
      } else {
        return 0;
      }
    },
    eyeAbnormal() {
      return this.lefteyeAbnormal + this.righteyeAbnormal;
    },
    allAbnormal() {
      return (
        this.bgAbnormal +
        this.bpAbnormal +
        this.pulseAbnormal +
        this.bloodFatAbnormal +
        this.BMIAbnormal +
        this.breatheAbnormal +
        this.asAbnormal +
        this.VFAAbnormal +
        this.eyeAbnormal
      );
    },
  },
  methods: {
    async getReportProduce() {
      // let res = await reportProduce({
      //   template_id: 5,
      //   sign: "5157898f5cad7745ba0dd68be7d0efbec00ac1f9d167d1a09a8563d9c6b66163a3aafad0c23e747d424508d2866a3eaa5668556917c0b864cc79e0aff23c552f7e042334b597b7bb92e61e53ce626f1799059ebf54ce0bd86c1717f464fdee1f54d741e4abdde99ed13b171bad9443b5df3338680380aa4113d2ba3b02b147825dd2f148ec2d433a78c41b5de8d3bfb39adf8ed45b159aa6244c2b710e378581a12d54ee48d89cc7721f4d9337e5a4fada76f597824e817f304046f38fbfb803879f5d25d230710cd555b46bcaa8212c5d235649c13f172613203eb211cf5ed63f6ea6e64c0b62b73955797d540cefd7",
      // });
      let res = await reportProduce({
        template_id: this.$route.query.template_id,
        sign: this.$route.query.sign,
      });

      // let res = {
      //   "code": 0,
      //   "msg": "success",
      //   "data": {
      //     "project": {
      //       "name": "三高共管心脑血管病防治健康管理项目",
      //       "generate_time": "2024-09-05 18:07:19"
      //     },
      //     "hospital_info": {
      //       "name": "系统测试医院",
      //       "dept_name": "内分泌内科"
      //     },
      //     "patient": {
      //       "name": "199.None.t",
      //       "age": 41,
      //       "sex": 1,
      //       "mobile": "***********",
      //       "vascular_age": ""
      //     },
      //     "risk_factor": {
      //       "smoking": 0,
      //       "drinking": 0,
      //       "sleeplessness": 0,
      //       "cvd": 0,
      //       "exercise_less": 0
      //     },
      //     "bg": {
      //       "fbg_value": "",
      //       "glu_value": "",
      //       "hba1c_value": "",
      //       "abnormal_info": [
      //         {
      //           "code": "fbg_value",
      //           "abnormal_code": "fasting_bg",
      //           "deflection": 0,
      //           "status_display": "正常",
      //           "indicator_label": [
      //             "2.8",
      //             "3.9",
      //             "6.1",
      //             "7.0"
      //           ],
      //           "indicator_value": [
      //             "低血糖",
      //             "降低",
      //             "正常",
      //             "空腹血糖受损",
      //             "疑似糖尿病"
      //           ],
      //           "normal_range": "3.9≤n＜6.1"
      //         },
      //         {
      //           "code": "glu_value",
      //           "abnormal_code": "GLU1=GLU",
      //           "deflection": 0,
      //           "status_display": "正常",
      //           "indicator_label": [
      //             "2.8",
      //             "3.9",
      //             "6.1",
      //             "7.0"
      //           ],
      //           "indicator_value": [
      //             "低血糖",
      //             "降低",
      //             "正常",
      //             "空腹血糖受损",
      //             "疑似糖尿病"
      //           ],
      //           "normal_range": "3.9≤n＜6.1"
      //         },
      //         {
      //           "code": "hba1c_value",
      //           "abnormal_code": "A1C",
      //           "deflection": 0,
      //           "status_display": "正常",
      //           "indicator_label": [
      //             "6.5"
      //           ],
      //           "indicator_value": [
      //             "正常",
      //             "疑似糖尿病"
      //           ],
      //           "normal_range": "＜6.5"
      //         }
      //       ]
      //     },
      //     "bp": {
      //       "sbp_value": "",
      //       "dbp_value": "",
      //       "pulse_value": "",
      //       "abnormal_info": [
      //         {
      //           "code": "sbp_value",
      //           "abnormal_code": "sbp",
      //           "deflection": 0,
      //           "status_display": "正常",
      //           "indicator_label": [
      //             "90",
      //             "140",
      //             "160",
      //             "180"
      //           ],
      //           "indicator_value": [
      //             "低血压",
      //             "正常",
      //             "高血压1级",
      //             "高血压2级",
      //             "高血压3级"
      //           ],
      //           "normal_range": "90≤n＜140"
      //         },
      //         {
      //           "code": "dbp_value",
      //           "abnormal_code": "dbp",
      //           "deflection": 0,
      //           "status_display": "正常",
      //           "indicator_label": [
      //             "60",
      //             "90",
      //             "100",
      //             "110"
      //           ],
      //           "indicator_value": [
      //             "低血压",
      //             "正常",
      //             "高血压1级",
      //             "高血压2级",
      //             "高血压3级"
      //           ],
      //           "normal_range": "60≤n＜90"
      //         },
      //         {
      //           "code": "pulse_value",
      //           "abnormal_code": "pulse",
      //           "deflection": 0,
      //           "status_display": "正常",
      //           "indicator_label": [
      //             "60",
      //             "100"
      //           ],
      //           "indicator_value": [
      //             "心动过缓",
      //             "正常",
      //             "心动过速"
      //           ],
      //           "normal_range": "60≤n≤100"
      //         }
      //       ]
      //     },
      //     "blood_fat": {
      //       "tc_value": "",
      //       "tg_value": "",
      //       "ldl_c_value": "",
      //       "hdl_c_value": "",
      //       "abnormal_info": [
      //         {
      //           "code": "tc_value",
      //           "abnormal_code": "CHOL",
      //           "deflection": 0,
      //           "status_display": "正常",
      //           "indicator_label": [
      //             "5.2",
      //             "6.2"
      //           ],
      //           "indicator_value": [
      //             "合适水平",
      //             "边缘升高",
      //             "升高"
      //           ],
      //           "normal_range": "＜5.2"
      //         },
      //         {
      //           "code": "tg_value",
      //           "abnormal_code": "TG-B",
      //           "deflection": 0,
      //           "status_display": "正常",
      //           "indicator_label": [
      //             "1.7",
      //             "2.3"
      //           ],
      //           "indicator_value": [
      //             "合适水平",
      //             "边缘升高",
      //             "升高"
      //           ],
      //           "normal_range": "＜1.7"
      //         },
      //         {
      //           "code": "ldl_c_value",
      //           "abnormal_code": "ULDL",
      //           "deflection": 0,
      //           "status_display": "正常",
      //           "indicator_label": [
      //             "2.6",
      //             "3.4",
      //             "4.1"
      //           ],
      //           "indicator_value": [
      //             "理想水平",
      //             "合适水平",
      //             "边缘升高",
      //             "升高"
      //           ],
      //           "normal_range": "＜3.4"
      //         },
      //         {
      //           "code": "hdl_c_value",
      //           "abnormal_code": "UHDL",
      //           "deflection": 0,
      //           "status_display": "正常",
      //           "indicator_label": [
      //             "1.0"
      //           ],
      //           "indicator_value": [
      //             "较低",
      //             "正常"
      //           ],
      //           "normal_range": "≥1.0"
      //         }
      //       ]
      //     },
      //     "height_weight": {
      //       "id": 0,
      //       "bmi_value": "",
      //       "abnormal_info": [
      //         {
      //           "code": "bmi_value",
      //           "abnormal_code": "bmi",
      //           "deflection": 0,
      //           "status_display": "正常",
      //           "indicator_label": [
      //             "18.5",
      //             "24",
      //             "28"
      //           ],
      //           "indicator_value": [
      //             "过低",
      //             "正常",
      //             "超重",
      //             "肥胖"
      //           ],
      //           "normal_range": "18.5≤n＜24"
      //         }
      //       ]
      //     },
      //     "breath": {
      //       "fvc_value": "",
      //       "fvc_pred_value": "",
      //       "fvc_perc_pred_value": "",
      //       "fev1_value": "",
      //       "fev1_pred_value": "",
      //       "fev1_perc_pred_value": "",
      //       "fev1_fvc_value": "",
      //       "abnormal_info": [
      //         {
      //           "code": "fev1_fvc_value",
      //           "abnormal_code": "fev_one_dot_zero_fvc",
      //           "deflection": 0,
      //           "status_display": "正常",
      //           "indicator_label": [
      //             "70"
      //           ],
      //           "indicator_value": [
      //             "慢性阻塞性肺部疾病 ",
      //             "正常"
      //           ],
      //           "normal_range": "≥70"
      //         }
      //       ]
      //     },
      //     "as": {
      //       "labi_value": "",
      //       "lpwv_value": "",
      //       "rabi_value": "",
      //       "rpwv_value": "",
      //       "abnormal_info": [
      //         {
      //           "code": "labi_value",
      //           "abnormal_code": "Labi",
      //           "deflection": 0,
      //           "status_display": "正常",
      //           "indicator_label": [
      //             "0.9",
      //             "1.4"
      //           ],
      //           "indicator_value": [
      //             "疑似动脉硬化",
      //             "正常",
      //             "疑似动脉钙化"
      //           ],
      //           "normal_range": "0.9≤n≤1.4"
      //         },
      //         {
      //           "code": "rabi_value",
      //           "abnormal_code": "Rabi",
      //           "deflection": 0,
      //           "status_display": "正常",
      //           "indicator_label": [
      //             "0.9",
      //             "1.4"
      //           ],
      //           "indicator_value": [
      //             "疑似动脉硬化",
      //             "正常",
      //             "疑似动脉钙化"
      //           ],
      //           "normal_range": "0.9≤n≤1.4"
      //         },
      //         {
      //           "code": "lpwv_value",
      //           "abnormal_code": "LbaPWV",
      //           "deflection": 0,
      //           "status_display": "正常",
      //           "indicator_label": [
      //             "1400",
      //             "1800"
      //           ],
      //           "indicator_value": [
      //             "正常",
      //             "僵硬度增加",
      //             "疑似动脉硬化"
      //           ],
      //           "normal_range": "＜1400"
      //         },
      //         {
      //           "code": "rpwv_value",
      //           "abnormal_code": "RbaPWV",
      //           "deflection": 0,
      //           "status_display": "正常",
      //           "indicator_label": [
      //             "1400",
      //             "1800"
      //           ],
      //           "indicator_value": [
      //             "正常",
      //             "僵硬度增加",
      //             "疑似动脉硬化"
      //           ],
      //           "normal_range": "＜1400"
      //         }
      //       ]
      //     },
      //     "vf": {
      //       "vat_value": "",
      //       "abnormal_info": [
      //         {
      //           "code": "vat_value",
      //           "abnormal_code": "visceral_fat",
      //           "deflection": 0,
      //           "status_display": "正常",
      //           "indicator_label": [
      //             "100"
      //           ],
      //           "indicator_value": [
      //             "正常",
      //             "内脏脂肪型肥胖"
      //           ],
      //           "normal_range": "≤100"
      //         }
      //       ]
      //     },
      //     "eye_examine": {
      //       "left_eye_picture_one": "https://saas-oss-prod-pri.oss-cn-hangzhou.aliyuncs.com/production/live/20240904/fundus/fundus66d812875b9785.98618390/100085339335_20240904_155523_Non_myd_L_009.jpg?OSSAccessKeyId=LTAI5tKUwSWh81v65L4A5eeh&Expires=1733212665&Signature=DwOx9Y+v7Z4fKuM77+JvwQauIE0=",
      //       "right_eye_picture_one": "",
      //       "left_eye_picture_two": "",
      //       "right_eye_picture_two": "",
      //       "left_eye_dignosis": "",
      //       "right_eye_dignosis": "",
      //       "describe": "",
      //       "left_warning": 0,
      //       "right_warning": 0,
      //       "abnormal_info": [
      //         {
      //           "code": "left_eye_rechecked",
      //           "abnormal_code": "left_eye_rechecked",
      //           "deflection": 0,
      //           "status_display": "正常",
      //           "indicator_label": [
      //             "6",
      //             "1",
      //             "3",
      //             "5",
      //             "4",
      //             "2"
      //           ],
      //           "indicator_value": [
      //             "正常",
      //             "正常",
      //             "异常",
      //             "异常",
      //             "异常",
      //             "异常"
      //           ],
      //           "normal_range": "无法判断"
      //         },
      //         {
      //           "code": "right_eye_rechecked",
      //           "abnormal_code": "right_eye_rechecked",
      //           "deflection": '',
      //           "status_display": "",
      //           "indicator_label": [
      //             "6",
      //             "1",
      //             "3",
      //             "5",
      //             "4",
      //             "2"
      //           ],
      //           "indicator_value": [
      //             "正常",
      //             "正常",
      //             "异常",
      //             "异常",
      //             "异常",
      //             "异常"
      //           ],
      //           "normal_range": "无法判断"
      //         }
      //       ]
      //     }
      //   }
      // }

      if (res.code == 0) {
        this.personInfo = res.data;

        Object.keys(this.personInfo).forEach(v => {
          if (['bg', 'blood_fat', 'bp', 'breath', 'as', 'height_weight', 'vf', 'eye_examine'].includes(v)){
            this.personInfo[v].abnormal_info.map(m => {
              this.personInfo[v][`abnormal_info_${m.code}`] = JSON.parse(JSON.stringify(m))
              return m
            })
          }
        })

      }
    },
    change(e) {
      // this[e + "Extend"] = !this[e + "Extend"];
    },
    getAbnormal (pcode, code) {
      const ncode = `abnormal_info_${code}`
      if (this.personInfo[pcode] && this.personInfo[pcode][ncode] && this.personInfo[pcode][ncode].deflection !== '' && this.personInfo[pcode][ncode].deflection !== null && this.personInfo[pcode][ncode].deflection !== undefined) {
        return ['left_eye_rechecked', 'right_eye_rechecked'].includes(code) ? (this.personInfo[pcode][ncode].abnormal_status || 0) : this.personInfo[pcode][ncode].deflection
      }
      return ''
    }
  },
};
</script>
<style lang="scss" scoped>
.small_font_size{
  font-size: 13px;
}
.content {
  .header {
    display: flex;
    padding: 21px 15px;
    background: #f7f7f7;
    .avatar {
      width: 55px;
      border: 3px solid #d8d8d8;
      border-radius: 55px;
    }
    .person-info {
      font-weight: 400;
      font-size: 12px;
      line-height: 17px;
      color: #333333;
      text-align: left;
      margin-left: 11px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .name {
        font-weight: 500;
        font-size: 16px;
        line-height: 22px;
      }
      .icon-span {
        span {
          margin-left: 6px;
        }
        img {
          width: 11px;
        }
      }
    }
  }
  .danger-project {
    background: #ffffff;
    .blood-vessel-age {
      text-align: left;
      padding-top: 25px;
      padding-left: 30px;
      font-weight: 500;
      font-size: 20px;
      line-height: 28px;
      color: #333333;
      .num {
        color: #2c9ef7;
      }
    }
    .text {
      text-align: left;
      padding-top: 4px;
      padding-left: 30px;
      font-size: 15px;
      line-height: 21px;
      color: #333333;
    }
    .img-position {
      position: relative;
      height: 538px;
      .danger {
        // position: absolute;
        // left: -30px;
        width: 100%;
      }
      .danger-point {
        position: absolute;
        top: 192px;
        left: 97px;
        font-weight: 600;
        font-size: 24px;
        color: #ca2200;
      }
      .count {
        font-weight: 400;
        font-size: 16px;
        color: #ca2200;
        .number {
          font-weight: 600;
          font-size: 25px;
          position: absolute;
          top: 220px;
          left: 93px;
          width: 30px;
        }
        .count-span {
          position: absolute;
          top: 225px;
          left: 124px;
        }
      }
      .health-point {
        position: absolute;
        top: 195px;
        left: 109px;
        font-weight: 500;
        font-size: 24px;
        color: #4cb684;
      }
      .health-span {
        position: absolute;
        top: 228px;
        left: 106px;
        font-weight: 400;
        font-size: 18px;
        color: #4cb684;
      }
      .box {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 80px;
        height: 80px;
        border-radius: 40px;
        font-size: 16px;
      }
      .smallbox {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 50px;
        height: 50px;
        border-radius: 25px;
        font-size: 14px;
      }
      .normalbox {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 70px;
        height: 70px;
        border-radius: 35px;
        font-size: 14px;
      }
      .normal {
        color: #2c9ef7;
        background: linear-gradient(180deg, #ffffff 0%, #def3ff 100%);
        box-shadow: 0px 3px 10px rgba(5, 134, 206, 0.3),
          inset 0px -1px 2px #ffffff;
      }
      .empty {
        color: #878F99;
        background: linear-gradient(180deg, #FFFFFF 0%, #DDDDDD 100%);
        box-shadow: 0px 3px 10px rgba(238, 238, 238, 0.3), inset 0px -1px 2px #FFFFFF;
      }
      .abnormal {
        color: #d0021b;
        background: linear-gradient(180deg, #ffffff 0%, #ffdede 100%);
        box-shadow: 0px 3px 10px rgba(206, 5, 5, 0.3),
          inset 0px -1px 2px #ffffff;
        .warning {
          width: 18px;
          margin-top: 4px;
        }
      }
      .diabetes {
        position: absolute;
        top: 20px;
        left: 22px;
      }
      .hypertension {
        position: absolute;
        top: 16px;
        left: 136px;
      }
      .pulse {
        position: absolute;
        top: 61px;
        left: 243px;
      }
      .bloodfat {
        position: absolute;
        top: 155px;
        left: 286px;
      }
      .fat {
        position: absolute;
        top: 270px;
        left: 290px;
      }
      .copd {
        position: absolute;
        top: 363px;
        left: 230px;
      }
      .arteriosclerosis {
        position: absolute;
        top: 413px;
        left: 132px;
      }
      .retinopathy {
        position: absolute;
        top: 408px;
        left: 18px;
      }
      .smoking {
        position: absolute;
        top: 125px;
        left: 15px;
      }
      .drinking {
        position: absolute;
        top: 300px;
        left: 23px;
      }
      .sleeplessness {
        position: absolute;
        top: 266px;
        left: 216px;
      }
      .cvd {
        position: absolute;
        top: 7px;
        left: 291px;
        span {
          margin: 6px;
          line-height: 18px;
        }
      }
      .exercise_less {
        position: absolute;
        top: 453px;
        left: 273px;
      }
    }
  }
  ::v-deep .van-tabs__line {
    background-color: #2c9ef7;
    span {
      font-weight: 500;
      font-size: 16px;
      color: #000000;
    }
  }
  .padding-div {
    padding: 25px;
    background: #f7f7f7;
    .abnormal-index {
      text-align: left;
      .abnormal-index-word {
        padding: 5px;
        font-weight: 500;
        font-size: 20px;
        color: #333333;
        .abnormal-index-number {
          font-size: 22px;
          color: #2c9ef7;
        }
      }
      .abnormal-index-percent {
        padding: 0 5px;
        font-weight: 400;
        font-size: 15px;
        color: #333333;
      }
    }
    .abnormal-index-tab {
      text-align: left;
      margin-top: 20px;
      padding: 17px 20px;
      background: #ffffff;
      border-radius: 4px;
      .title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .name {
          font-weight: 500;
          font-size: 17px;
          line-height: 24px;
          color: #333333;
          .left-right {
            font-weight: 400;
            font-size: 16px;
            line-height: 22px;
            color: #666666;
          }
        }
        .state {
          padding: 3px 6px 2px 6px;
          font-weight: 400;
          font-size: 11px;
          line-height: 15px;
          text-align: center;
          border-radius: 10px;
          color: #ffffff;
        }
        .dark-blue {
          background: #2e6bf5;
        }
        .blue {
          background: #2c9ef7;
        }
        .green {
          background: #4cb684;
        }
        .yellow {
          background: #f5b040;
        }
        .orange {
          background: #f5703b;
        }
        .red {
          background: #ea4846;
        }
      }
      .index {
        .num {
          font-weight: 400;
          font-size: 20px;
          line-height: 28px;
          color: #333333;
        }
        .unit {
          margin-left: 11px;
          font-weight: 400;
          font-size: 12px;
          line-height: 17px;
          color: #999999;
        }
      }
      .eyeImg {
        width: 285px;
      }
      .dignosis {
        font-weight: 400;
        font-size: 16px;
        color: #000000;
        padding: 12px 0;
      }
      .img-bottom {
        padding-bottom: 12px;
        border-bottom: 1px solid #eeeeee;
      }
      .table {
        margin-top: 22px;
        tr {
          th {
            font-weight: 500;
            font-size: 12px;
            line-height: 14px;
            text-align: left;
            color: #333333;
          }
          td {
            padding-top: 5px;
            font-weight: 400;
            font-size: 20px;
            line-height: 28px;
            color: #333333;
          }
          td:nth-child(1) {
            width: 72px;
          }
          td:nth-child(2) {
            width: 81px;
          }
          td:nth-child(3) {
            width: 92px;
          }
        }
      }
      .introduce {
        margin-top: 19px;
        font-weight: 400;
        font-size: 15px;
        line-height: 25px;
        color: #666666;
      }

    }
  }
  .all-index {
    .all-index-text{
      font-weight: 500;
      font-size: 20px;
      color: #333333;
      margin: 32px 0 14px 20px;
      text-align: left;
    }
    .tab {
      // background: #ffffff;
      .title {
        height: 30px;
        font-size: 16px;
        display: flex;
        justify-content: space-between;
        border-bottom: 1px solid #eeeeee;
        padding: 17px 0;
        margin: 0 20px;
        span {
          display: flex;
          align-items: center;
          white-space: nowrap;
          img {
            height: 28px;
          }
          span {
            margin-left: 8px;
            font-weight: 500;
            color: #333333;
          }
        }
        .extend {
          font-size: 14px;
          font-weight: 400;
          color: #666666;
          .extend-abnormal {
            display: flex;
            align-items: center;
            .extend-abnormal-number {
              font-weight: 500;
              font-size: 14px;
              // line-height: 20px;
              color: #ea4846;
              margin-right: 3px;
            }
            .extend-abnormal-span {
              font-weight: 400;
              font-size: 12px;
              // line-height: 17px;
              color: #a7a7a7;
              margin-right: 8px;
            }
          }
        }
        .noTest {
          font-size: 15px;
          font-weight: 400;
          color: #a7a7a7;
          line-height: 21px;
        }
      }
      .cellContent {
        .flex-cell {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 17px 0;
          margin: 0 20px;
          border-bottom: 1px solid #eeeeee;
          .cell-title {
            width: 130px;
            text-align: left;
            font-weight: 400;
            font-size: 16px;
            line-height: 22px;
            color: #333333;
          }
          .cell-value {
            width: 190px;
            text-align: right;
            font-weight: 400;
            font-size: 15px;
            line-height: 21px;
            color: #333333;
            .activeRed {
              color: #ea4846;
            }
            .cell-standard {
              font-size: 13px;
              color: #999999;
            }
          }
        }
        .table {
          width: calc(100% - 30px);
          margin: 15px;
          font-size: 16px;
          color: #333333;
          tr {
            // height: 32px;
            th {
              height: 40px;
              font-size: 12px;
              font-weight: 500;
              background: #f7f7f7;
              border: 1px solid #f1f1f1;
              vertical-align: middle;
              text-align: center;
            }
            td {
              height: 32px;
              line-height: 20px;
              border: 1px solid #f1f1f1;
              vertical-align: middle;
              text-align: center;
            }
            .activeRed {
              color: #ea4846;
            }
            td:nth-child(1) {
              width: 139px;
            }
            td:nth-child(2) {
              width: 71px;
            }
            td:nth-child(3) {
              width: 63px;
            }
          }
          // .red {
          //   color: #eb6048;
          // }
          // .blue {
          //   color: #318dfe;
          // }
        }
      }
    }
  }
}
</style>
