<template>
  <div class="healthInfo">
    <div v-if="source != 'doc-app'">
      <div class="title">基本信息<span class="required" v-if="source == 'zzApp-servicePackage'">（必填）</span></div>
      <div class="inner">
        <div class="inputLine">
          <div class="label">真实姓名</div>
          <input v-if="canEdit || !tempName" class="input" style="width: 50%;" v-model="name" placeholder="请输入姓名" type="text">
          <span @click="$toast('实名信息请到附近就诊的医院修改！')" v-else>{{ name }}</span>
        </div>
        <div class="inputLine genderLine">
          <div class="label">性别</div>
          <div class="genderOuter">
            <div class="male" :class="{selected: gender === 1}" @click="selectGender(1)">男</div>
            <div class="female" :class="{selected: gender === 2}" @click="selectGender(2)">女</div>
          </div>
        </div>
        <div class="inputLine">
          <div class="label">出生年月</div>
          <div class="" @click="openBirthdayPop">
            <span class="rightTips" v-if="!birthday">请选择</span>
            <span v-else>{{ birthday }}</span>
            <van-icon name="arrow" color="#B3B3B3" />
          </div>
        </div>
      </div>
      <div class="title">详细信息<span class="required" v-if="source == 'zzApp-servicePackage'">（必填）</span></div>
      <div class="inner">
        <div class="inputLine">
          <div class="label">身份证号</div>
          <input v-if="canEdit || !tempSfzid" class="input" style="width: 50%;" v-model="sfzid" placeholder="请输入身份证号" type="text">
          <span @click="$toast('实名信息请到附近就诊的医院修改！')" v-else>{{ sfzid }}</span>
        </div>
        <div class="inputLine">
          <div class="label">家庭住址</div>
          <div class="rightVal" @click="openCommonPop('location')">
            <span class="rightTips" v-if="!location">请选择</span>
            <span v-else style="text-align: right;">{{ location }}</span>
            <van-icon name="arrow" color="#B3B3B3" />
          </div>
        </div>
        <div class="inputLine">
          <div class="label"></div>
          <input class="input" style="width: 50%;" v-model="addr" placeholder="请输入详细地址" type="text">
        </div>
        <div class="inputLine">
          <div class="label">民族</div>
          <div class="rightVal" @click="openCommonPop('nation')">
            <span class="rightTips" v-if="!nation">请选择</span>
            <span v-else>{{ nation }}</span>
            <van-icon name="arrow" color="#B3B3B3" />
          </div>
        </div>
        <div class="inputLine">
          <div class="label">婚姻状况</div>
          <div class="rightVal" @click="openCommonPop('marital')">
            <span class="rightTips" v-if="!maritalStatus.name">请选择</span>
            <span v-else>{{ maritalStatus.name }}</span>
            <van-icon name="arrow" color="#B3B3B3" />
          </div>
        </div>
        <div class="inputLine">
          <div class="label">文化程度</div>
          <div class="rightVal" @click="openCommonPop('education')">
            <span class="rightTips" v-if="!education.name">请选择</span>
            <span v-else>{{ education.name }}</span>
            <van-icon name="arrow" color="#B3B3B3" />
          </div>
        </div>
        <div class="inputLine">
          <div class="label">职业类型</div>
          <div class="rightVal" @click="openCommonPop('job')">
            <span class="rightTips" v-if="!job.name">请选择</span>
            <span v-else style="text-align: right;">{{ job.name }}</span>
            <van-icon name="arrow" color="#B3B3B3" />
          </div>
        </div>
      </div>
      <div class="title">生理现状<span class="required" v-if="source == 'zzApp-servicePackage'">（必填）</span></div>
      <div class="inner">
        <div class="inputLine">
          <div class="label">身高</div>
          <div class="rightUnit">
            <!-- <input class="input" v-model="height" placeholder="请输入身高" type="number" oninput="value=value.replace(/[^\d]/g,'')" @blur="blurHeight"> -->
            <van-field :border="false" class="input" v-model="height" type="number" input-align="right" label="" @blur="blurHeight" placeholder="请输入身高"/>
            <span class="unit">cm</span>
          </div>
        </div>
        <div class="inputLine">
          <div class="label" id="weight">体重</div>
          <div class="rightUnit">
            <!-- <input class="input" v-model="weight" placeholder="请输入体重" type="number" @keyup="inputKeyup" @blur="blurWeight"> -->
            <van-field :border="false" class="input" v-model="weight" type="number" input-align="right" label="" @blur="blurWeight" placeholder="请输入体重"/>
            <span class="unit">kg</span>
          </div>
        </div>
        <div class="inputLine">
          <div class="label" style="color: #84909A;">BMI</div>
          <input readonly class="input" v-model="BMI" placeholder="自动计算" type="text">
        </div>
        <template v-if="configData.current_symptoms && from != 'todoTask'">
          <div class="radioLine">
            <div class="labelLine">当前症状</div>
            <van-checkbox-group v-model="curSymptomsSelectArr" direction="horizontal" @change="curSymptomsChange">
              <van-checkbox :name="item.name" v-for="(item,index) in configData.current_symptoms.option" :key="index">{{item.name}}</van-checkbox>
            </van-checkbox-group>
          </div>
          <div class="radioLine" v-if="curSymptomsSelectArr.indexOf('咳痰') > -1">
            <div class="labelLine">痰色</div>
            <van-radio-group v-model="sputumColor" direction="horizontal">
              <van-radio name="黄痰">黄痰</van-radio>
              <van-radio name="白痰">白痰</van-radio>
              <van-radio name="黄白痰">黄白痰</van-radio>
            </van-radio-group>
          </div>
        </template>
      </div>
      <template v-if="from != 'todoTask'">
        <div class="title">既往史<span class="required" v-if="source == 'zzApp-servicePackage'">（必填）</span></div>
        <div class="inner" v-if="configData.disease">
          <div class="radioLine">
            <div class="labelLine">所患疾病（多选）</div>
            <van-checkbox-group v-model="diseaseIdArr" direction="horizontal">
              <van-checkbox :name="item.name" v-for="(item,index) in configData.disease.option" :key="index">{{item.name}}</van-checkbox>
            </van-checkbox-group>
          </div>
        </div>
        <div class="title">生活方式<span class="required" v-if="source == 'zzApp-servicePackage'">（必填）</span></div>
        <div class="inner">
          <div class="radioLine">
            <div class="labelLine">吸烟习惯</div>
            <van-radio-group v-model="livingHabits.smokingHabit.value" direction="horizontal">
              <van-radio :name="item.id" v-for="item in livingHabits.smokingHabit.list" :key="item.id">{{item.label}}</van-radio>
            </van-radio-group>
          </div>
          <div class="radioLine">
            <div class="labelLine">饮酒习惯</div>
            <van-radio-group v-model="livingHabits.drinkHabit.value" direction="horizontal" @change="drinkHabitChange">
              <van-radio :name="item.id" v-for="item in livingHabits.drinkHabit.list" :key="item.id">{{item.label}}</van-radio>
            </van-radio-group>
          </div>
          <template v-if="livingHabits.drinkHabit.value == 2">
            <div class="radioLine">
              <div class="labelLine">饮酒种类</div>
              <van-radio-group v-model="livingHabits.drinkType.value" direction="horizontal">
                <van-radio :name="item.id" v-for="item in livingHabits.drinkType.list" :key="item.id">{{item.label}}</van-radio>
              </van-radio-group>
            </div>
            <div class="inputLine">
              <div class="label">饮酒量</div>
              <div class="rightUnit">
                <input class="input" v-model="livingHabits.drinkMl.value" placeholder="请输入" type="tel" oninput="value=value.replace(/[^\d]/g,'')">
                <span class="unit">ml/天</span>
              </div>
            </div>
          </template>
          <div class="radioLine">
            <div class="labelLine">饮食习惯</div>
            <van-checkbox-group v-model="livingHabits.eatHabit.value" direction="horizontal">
              <van-checkbox :name="item.id" v-for="item in livingHabits.eatHabit.list" :key="item.id">{{item.label}}</van-checkbox>
            </van-checkbox-group>
          </div>
          <div class="radioLine">
            <div class="labelLine">运动习惯</div>
            <div class="inputLine">
              <div class="label">平均每周运动</div>
              <div class="rightUnit">
                <input class="input" v-model="livingHabits.sportWeek.value" placeholder="请输入天数" type="tel" oninput="value=value.replace(/[^\d]/g,'')">
                <span class="unit">天</span>
              </div>
            </div>
            <div class="inputLine">
              <div class="label">平均每天运动</div>
              <div class="rightUnit">
                <input class="input" v-model="livingHabits.sportDay.value" placeholder="请输入分钟" type="tel" oninput="value=value.replace(/[^\d]/g,'')">
                <span class="unit">分钟</span>
              </div>
            </div>
            <div class="inputLine">
              <div class="label">平均每日久坐时间</div>
              <div class="rightUnit">
                <input class="input" v-model="livingHabits.sedentary.value" placeholder="请输入小时" type="tel" oninput="value=value.replace(/[^\d]/g,'')">
                <span class="unit">小时</span>
              </div>
            </div>
            <div class="radioLine">
              <div class="labelLine">睡眠状况</div>
              <van-radio-group v-model="livingHabits.sleepStatus.value" direction="horizontal">
                <van-radio :name="item.label" v-for="item in livingHabits.sleepStatus.list" :key="item.id">{{item.label}}</van-radio>
              </van-radio-group>
            </div>
            <div class="inputLine">
              <div class="label">每日平均睡眠时长</div>
              <div class="rightUnit">
                <input class="input" v-model="livingHabits.sleepTime.value" placeholder="请输入" type="tel" oninput="value=value.replace(/[^\d]/g,'')">
                <span class="unit">小时/天</span>
              </div>
            </div>
          </div>
        </div>
        <div class="title" style="font-size: 15px;">填写以下内容，可让医生更全面了解您的信息。</div>
        <div class="inner">
          <div class="radioLine" v-for="item in extraInfoArr" :key="item.label">
            <div class="labelLine">{{item.label}}</div>
            <van-radio-group v-model="item.value" direction="horizontal">
              <van-radio :name="2">无</van-radio>
              <van-radio :name="1">有</van-radio>
            </van-radio-group>
            <div class="descLine" v-if="item.value == 1">
              <div class="descLabel">详细描述</div>
              <div class="descInputOuter">
                <textarea class="descInput" maxlength="200" v-model="item.desc" name=""></textarea>
              </div>
            </div>
          </div>
          <div class="inputLine" v-if="configData.body_temperature">
            <div class="label">体温<span class="required" v-if="configData.body_temperature.is_required == 1">（必填）</span></div>
            <div class="rightUnit">
              <!-- <input class="input" v-model="temperature" placeholder="请输入" type="text" oninput="value = value.replace(/[^\d.]/g,'')" @keyup="formatVal('temperature')"> -->
              <van-field :border="false" class="input" v-model="temperature" type="number" input-align="right" @blur="blurTemperature" @keyup="formatVal('temperature')" placeholder="请输入"/>
              <span class="unit">°C</span>
            </div>
          </div>
          <div class="radioLine" v-if="configData.bp">
            <div class="labelLine">血压<span class="required" v-if="configData.bp.is_required == 1">（必填）</span></div>
            <div class="inputLine bgColor" style="border: none;" v-for="item in bpInfoArr" :key="item.label">
              <div class="label">{{item.label}}</div>
              <div class="rightUnit">
                <input class="input" v-model="item.value" placeholder="请输入" type="tel" oninput="value=value.replace(/[^\d]/g,'')" @keyup="formatVal('bp')" @blur="blurBp(item.id)">
                <span class="unit">{{item.unit}}</span>
              </div>
            </div>
          </div>
          <div class="radioLine" v-if="configData.bgArr && configData.bgArr.length > 0">
            <div class="labelLine">血糖</div>
            <div class="inputLine bgColor" style="border: none;" v-for="item in configData.bgArr" :key="item.id">
              <div class="label">{{item.name}}<span class="required" v-if="item.is_required == 1">（必填）</span></div>
              <div class="rightUnit">
                <!-- <input class="input" v-model="bsNum" placeholder="请输入" type="text" oninput="value = value.replace(/[^\d.]/g,'')" @blur="saveBsFun"> -->
                <van-field :border="false" class="input" v-model="item.value" type="number" input-align="right" label="" @blur="saveBsFun(item)" placeholder="请输入"/>
                <span class="unit">mmol/L</span>
              </div>
            </div>
          </div>
          <div class="inputLine" v-if="configData.oxygen_saturation">
            <div class="label">血氧饱和<span class="required" v-if="configData.oxygen_saturation.is_required == 1">（必填）</span></div>
            <div class="rightUnit">
              <input class="input" v-model="spo2" placeholder="请输入" type="tel" oninput="value=value.replace(/[^\d]/g,'')" @blur="formatVal('spo2')">
              <span class="unit">%</span>
            </div>
          </div>
          <div class="radioLine" v-if="configData.blood_fat">
            <div class="labelLine">血脂<span class="required" v-if="configData.blood_fat.is_required == 1">（必填）</span></div>
            <div class="inputLine" style="border: none;" v-for="item in bfInfoArr" :key="item.label">
              <div class="label" v-html="item.label"></div>
              <div class="rightUnit">
                <!-- <input class="input" v-model="item.value" placeholder="请输入" type="tel" oninput="value=value.replace(/[^\d]/g,'')" @keyup="formatVal('bf')"> -->
                <van-field :border="false" class="input" v-model="item.value" type="number" input-align="right" label="" @keyup="formatVal('bf')" @blur="blurBf(item)" placeholder="请输入"/>
                <span class="unit">mmol/L</span>
              </div>
            </div>
          </div>
          <div class="inputLine" v-if="configData.lung_function">
            <div class="label">肺功能(FEV1)<span class="required" v-if="configData.lung_function.is_required == 1">（必填）</span></div>
            <div class="rightUnit">
              <input class="input" v-model="FEV1" placeholder="请输入" type="tel" oninput="value=value.replace(/[^\d]/g,'')" @keyup="formatVal('FEV1')">
              <span class="unit">%</span>
            </div>
          </div>
          <div class="inputLine" v-if="configData.hba1c">
            <div class="label">糖化血红蛋白<span class="required" v-if="configData.hba1c.is_required == 1">（必填）</span></div>
            <div class="rightUnit">
              <!-- <input class="input" v-model="glycosylatedHemoglobin" placeholder="请输入" type="text" oninput="value = value.replace(/[^\d.]/g,'')" @keyup="formatVal('glycosylatedHemoglobin')"> -->
              <van-field :border="false" class="input" v-model="glycosylatedHemoglobin" type="number" input-align="right" label="" @blur="blurGlycosylatedHemoglobin" @keyup="formatVal('glycosylatedHemoglobin')" placeholder="请输入"/>
              <span class="unit">%</span>
            </div>
          </div>
          <!-- <div class="inputLine" @click="goMedication">
            <div class="label">我的用药</div>
            <van-icon name="arrow" color="#B3B3B3" />
          </div> -->
        </div>
      </template>
      
      <div v-if="source != 'doc-app'" class="bottomBtn" @click="saveData">完成</div>
    </div>
    <!-- 医生端只读 -->
    <div v-else>
      <div v-for="(item,index) in readonlyHealthInfoArr" :key="index">
        <div class="title">{{item.title}}</div>
        <div class="inner">
          <div class="inputLine" v-for="(innerItem,index) in item.list" :key="index">
            <div class="label">{{innerItem.label}}</div>
            <div class="readonlyText" :style="{'color': innerItem.value ? '#5A6266' : '#C0C6CC','font-size': innerItem.id == 'bf' && innerItem.value ? '13px' : ''}" v-html="innerItem.value || '未填写'"></div>
          </div>
        </div>
      </div>
      <!-- <div class="bottomTips">我是有底线的</div> -->
    </div>
    <van-popup position="bottom" round :style="{ 'min-height': '100px' }" v-model="isBdPopShow">
      <van-datetime-picker
        v-model="currentDate"
        swipe-duration="100"
        type="date"
        title="请选择出生日期"
        :min-date="minDate"
        :max-date="maxDate"
        :formatter="formatter"
        @confirm="confirmBirthday"
        @cancel="isBdPopShow = false"
      />
    </van-popup>
    <van-popup position="bottom" round :style="{ 'min-height': '100px' }" v-model="isCommonPickerPopShow">
      <van-picker show-toolbar title="请选择" :columns="commonPickerData" value-key="name" @cancel="isCommonPickerPopShow = false" @confirm="commonPickerConfirmFun" />
    </van-popup>
    
  </div>
</template>

<script>
const DECIMAL_REG = /^\d+(\.\d+)?$/
import moment from 'moment'
import { saveBp,saveBf,saveHW,saveBasicInfo,getConfig,getPatientInfo,saveSpo2,saveBs,savePatient,getMedicationUrl,getLocationList } from '@/api/ihecMiniwechat'
import { jsToClient } from '@/utils/bfBridge'
import wx from 'weixin-js-sdk'
export default {
  data(){
    return {
      canEdit: true,  //是否可编辑 性别、性别、生日
      packageId: '',
      configData: {},
      source: '',  //doc-app: 医生端-只读；zzApp-healthInfo: app/小程序-不校验；zzApp-servicePackage: 服务包-读配置、校验
      token: '',
      name: '',
      tempName: '',
      gender: '',
      tempGender: '',
      birthday: '',
      tempBirthday: '',
      height: '',
      weight: '',
      BMI: '',
      isBdPopShow: false,
      minDate: new Date(1900, 0, 1),
      maxDate: new Date(),
      currentDate: new Date(),
      curSymptomsSelectArr: [],
      sputumColor: '',
      diseaseIdArr: [],
      livingHabits: {
        smokingHabit: {
          required: 1,
          label: '吸烟习惯',
          value: 0,
          list: [
            {label:'不吸烟',id:1},
            {label:'每日0~10支',id:2},
            {label:'每日10~20支',id:3},
            {label:'每日20支以上',id:4}
          ]
        },
        drinkHabit: {
          required: 1,
          label: '饮酒习惯',
          value: 0,
          list: [
            {label:'不饮酒',id:1},
            {label:'饮酒',id:2},
            {label:'已戒酒',id:3}
          ]
        },
        drinkType: {
          required: 2,
          label: '饮酒种类',
          value: 0,
          list: [
            {label:'红酒',id:1},
            {label:'白酒',id:2},
            {label:'黄酒',id:3},
            {label:'啤酒',id:4}
          ]
        },
        drinkMl: {
          required: 2,
          label: '饮酒量',
          value: '',
        },
        eatHabit: {
          required: 1,
          label: '饮食习惯',
          value: [],
          list: [
            {label:'偏荤',id:'1'},
            {label:'偏素',id:'2'},
            {label:'偏咸',id:'3'},
            {label:'偏甜',id:'4'},
            {label:'偏淡',id:'5'},
            {label:'偏腻',id:'6'}
          ]
        },
        sportWeek: {
          required: 1,
          label: '平均每周运动',
          value: '',
        },
        sportDay: {
          required: 1,
          label: '平均每天运动',
          value: '',
        },
        sedentary: {
          required: 1,
          label: '平均每日久坐时间',
          value: '',
        },
        sleepStatus: {
          required: 1,
          label: '睡眠状况',
          value: '',
          list: [
            {label:'良好',id:0},
            {label:'不好',id:1},
            {label:'安眠药助眠',id:2}
          ]
        },
        sleepTime: {
          required: 1,
          label: '每日平均睡眠时长',
          value: '',
        },
      },
      temperature: '',
      bsNum: '',
      spo2: '',
      FEV1: '',
      glycosylatedHemoglobin: '',
      extraInfoArr: [
        {
          id: 'drug_allergy',
          label: '药物过敏史',
          value: 0,
          desc: '',
        },
        {
          id: 'family_genetic',
          label: '家族遗传史',
          value: 0,
          desc: '',
        },
        {
          id: 'operation',
          label: '手术史',
          value: 0,
          desc: '',
        },
        {
          id: 'trauma',
          label: '外伤史',
          value: 0,
          desc: '',
        }
      ],
      bpInfoArr: [
        {
          label: '收缩压',
          id: 'sbp',
          value: '',
          unit: 'mmHg',
        },
        {
          label: '舒张压',
          id: 'dbp',
          value: '',
          unit: 'mmHg',
        },
        {
          label: '脉搏',
          id: 'pulse',
          value: '',
          unit: '次/分钟',
        }
      ],
      bfInfoArr: [
        {
          label: '总胆固醇(TC)',
          id: 'tc',
          value: '',
        },
        {
          label: '甘油三酯(TG)',
          id: 'tg',
          value: '',
        },
        {
          label: '低密度脂蛋白胆固醇<br>(LDL-C)',
          id: 'ldl_c',
          value: '',
        },
        {
          label: '高密度脂蛋白胆固醇<br>(HDL-C)',
          id: 'hdl_c',
          value: '',
        },
        {
          label: '非高密度脂蛋白胆固醇<br>(非-HDL-C)',
          id: 'un_hdl_c',
          value: '',
        }
      ],
      // 健康信息 配置字段的默认值
      defaultConfigData: {
        current_symptoms: {
          "is_required": 2,
          "option": [
            { "name": "无" },
            { "name": "咳嗽" },
            { "name": "咳痰" },
            { "name": "胸闷" },
            { "name": "胸痛" },
            { "name": "呼吸困难" },
            { "name": "咯血" }
          ]
        },
        disease: {
          "is_required": 2,
          "option": [
            { "name": "无" },
            { "name": "高血压" },
            { "name": "下肢动脉疾病" },
            { "name": "冠心病" },
            { "name": "脑卒中" },
            { "name": "心力衰竭" },
            { "name": "心房颤动" },
            { "name": "肺源性心脏病" },
            { "name": "风湿性心脏病" },
            { "name": "先天性心脏病" },
            { "name": "慢性阻塞性肺病" },
            { "name": "肺动脉高压" },
            { "name": "肺气肿" },
            { "name": "哮喘" },
            { "name": "慢性支气管炎" },
            { "name": "慢性咳嗽" },

            { "name": "1型糖尿病" },
            { "name": "2型糖尿病" },
            { "name": "妊娠期糖尿病" },
            { "name": "原发性甲状腺功能亢进" },
            { "name": "原发性甲状腺功能减退" },
            { "name": "甲状腺恶性肿瘤" },
            { "name": "甲状腺结节" },
            { "name": "甲状腺炎症" },
            { "name": "痛风" },
            { "name": "高血脂" },
            { "name": "视网膜病变" },
            { "name": "周围神经病变" },
            { "name": "肾病足部病变" },
            { "name": "心梗" },

            { "name": "其他疾病" }
          ]
        },
        "body_temperature": { "is_required": 2,},
        "bp6": { "is_required": 2,},
        "bgArr": [
          {
            id: 'fpg',
            name: '空腹血糖',
            value: '',
            is_required: 2
          },
          {
            id: 'morning_bg',
            name: '早餐后血糖',
            value: '',
            is_required: 2
          },
          {
            id: 'before_lunch_bg',
            name: '午餐前血糖',
            value: '',
            is_required: 2
          },
          {
            id: 'after_lunch_bg',
            name: '午餐后血糖',
            value: '',
            is_required: 2
          },
          {
            id: 'before_dinner_bg',
            name: '晚餐前血糖',
            value: '',
            is_required: 2
          },
          {
            id: 'after_dinner_bg',
            name: '晚餐后血糖',
            value: '',
            is_required: 2
          },
          {
            id: 'before_bed_bg',
            name: '睡前血糖',
            value: '',
            is_required: 2
          },
          {
            id: 'early_morning_bg',
            name: '凌晨血糖',
            value: '',
            is_required: 2
          }
        ],

        "oxygen_saturation": { "is_required": 2,},
        "blood_fat": { "is_required": 2,},
        "lung_function": { "is_required": 2,},
        "hba1c": { "is_required": 2,},
        "bp": { "is_required": 2,}
      },
      readonlyHealthInfoArr: [
        {
          title: '基本信息',
          list: [
            {
              id: 'name',
              label: '真实姓名',
              value: '',
            },
            {
              id: 'sex_format',
              label: '性别',
              value: '',
            },
            {
              id: 'bday',
              label: '出生日期',
              value: '',
            }
          ]
        },
        {
          title: '详细信息',
          list: [
            {
              id: 'sfzid',
              label: '身份证号',
              value: '',
            },
            {
              id: 'addr',
              label: '家庭住址',
              value: '',
            },
            {
              id: 'nation',
              label: '民族',
              value: '',
            },
            {
              id: 'marital_status',
              label: '婚姻状况',
              value: '',
            },
            {
              id: 'edu_lv',
              label: '文化程度',
              value: '',
            },
            {
              id: 'profession_type',
              label: '职业类型',
              value: '',
            }
          ]
        },
        {
          title: '生理状态',
          list: [
            {
              id: 'HW',
              label: '身高/体重',
              value: '',
            },
            {
              id: 'body_temperature',
              label: '体温',
              value: '',
            },
            {
              id: 'bp',
              label: '血压',
              value: '',
            },
            {
              id: 'bg',
              label: '空腹血糖',
              value: '',
            },
            {
              id: 'morning_bg',
              label: '早餐后血糖',
              value: ''
            },
            {
              id: 'before_lunch_bg',
              label: '午餐前血糖',
              value: ''
            },
            {
              id: 'after_lunch_bg',
              label: '午餐后血糖',
              value: ''
            },
            {
              id: 'before_dinner_bg',
              label: '晚餐前血糖',
              value: ''
            },
            {
              id: 'after_dinner_bg',
              label: '晚餐后血糖',
              value: ''
            },
            {
              id: 'before_bed_bg',
              label: '睡前血糖',
              value: ''
            },
            {
              id: 'early_morning_bg',
              label: '凌晨血糖',
              value: ''
            },
            {
              id: 'spo2',
              label: '血氧饱和',
              value: '',
            },
            {
              id: 'bf',
              label: '血脂',
              value: '',
            },
            {
              id: 'lung_function',
              label: '肺功能(FEV1)',
              value: '',
            },
            {
              id: 'hba1c',
              label: '糖化血红蛋白',
              value: '',
            },
            {
              id: 'current_symptoms',
              label: '当前症状',
              value: '',
            }
          ]
        },
        {
          title: '既往史',
          list: [
            {
              id: 'disease',
              label: '所患疾病',
              value: '',
            },
            {
              id: 'drug_allergy',
              label: '药物过敏史',
              value: '',
            },
            {
              id: 'family_genetic',
              label: '家族过敏史',
              value: '',
            },
            {
              id: 'operation',
              label: '手术史',
              value: '',
            },
            {
              id: 'trauma',
              label: '外伤史',
              value: '',
            }
          ]
        },
        {
          title: '生活方式',
          list: [
            {
              id: 'cigarette_habit_format',
              label: '吸烟习惯',
              value: '',
            },
            {
              id: 'drinkHabit',
              label: '饮酒习惯',
              value: '',
            },
            {
              id: 'food_habit_format',
              label: '饮食习惯',
              value: '',
            },
            {
              id: 'sportHabit',
              label: '运动习惯',
              value: '',
            },
            {
              id: 'sleepHabit',
              label: '睡眠习惯',
              value: '',
            }
          ]
        }
      ],
      isCommonPickerPopShow: false,
      commonPickerData: [],
      locationList: [],
      maritalStatusArr: [
        {
          value: 10,
          name: '未婚',
        },
        {
          value: 20,
          name: '已婚',
        },
        {
          value: 30,
          name: '离异',
        },
        {
          value: 40,
          name: '丧偶',
        },
        {
          value: 50,
          name: '不详',
        },
      ],
      jobArr: [
        { value: 10000, name: '国家机关、党群组织、企事业单位负责人' },
        { value: 20000, name: '各类专业技术人员' },
        { value: 30000, name: '办事人员、商业及服务性工作人员' },
        { value: 50000, name: '农、林、牧、渔、水利劳动者' },
        { value: 60000, name: '生产工人、运输工人和有关人员' },
        { value: 70000, name: '军人' },
        { value: 80000, name: '不便分类的其他从业人员' },
        { value: 90001, name: '下岗或无业' },
        { value: 90002, name: '离退休' },
        { value: 90003, name: '学生' }
      ],
      educationArr: [
        {
          value: 10,
          name: '研究生及以上',
        },
        {
          value: 2030,
          name: '大专及本科',
        },
        {
          value: 4060,
          name: '高中/中专（中技）',
        },
        {
          value: 70,
          name: '初中',
        },
        {
          value: 80,
          name: '小学',
        },
        {
          value: 90,
          name: '没上过学/不详',
        },
      ],
      nationArr: [
        '汉族',
        '蒙古族',
        '回族',
        '藏族',
        '维吾尔族',
        '苗族',
        '彝族',
        '壮族',
        '布依族',
        '朝鲜族',
        '满族',
        '侗族',
        '瑶族',
        '白族',
        '土家族',
        '哈尼族',
        '哈萨克族',
        '傣族',
        '黎族',
        '僳僳族',
        '佤族',
        '畲族',
        '高山族',
        '拉祜族',
        '水族',
        '东乡族',
        '纳西族',
        '景颇族',
        '柯尔克孜族',
        '土族',
        '达斡尔族',
        '仫佬族',
        '羌族',
        '布朗族',
        '撒拉族',
        '毛南族',
        '仡佬族',
        '锡伯族',
        '阿昌族',
        '普米族',
        '塔吉克族',
        '怒族',
        '乌孜别克族',
        '俄罗斯族',
        '鄂温克族',
        '德昂族',
        '保安族',
        '裕固族',
        '京族',
        '塔塔尔族',
        '独龙族',
        '鄂伦春族',
        '赫哲族',
        '门巴族',
        '珞巴族',
        '基诺族',
      ],
      location: '',
      addr: '',
      tempSfzid: '',
      sfzid: '',
      nation: '',
      education: {name:'', value:''},
      job: {name:'', value:''},
      maritalStatus: {name:'', value:''},
      curCommonPopType: '',
      from: ''
    }
  },
  methods: {
    curSymptomsChange(val){
      if(val.indexOf('咳痰') == -1){
        this.sputumColor = ''
      }
    },
    selectGender(val){
      if(!this.canEdit && this.tempGender){
        this.$toast('实名信息请到附近就诊的医院修改！')
        return
      }
      this.gender = val
    },
    openBirthdayPop(){
      if(!this.canEdit && this.tempBirthday){
        this.$toast('实名信息请到附近就诊的医院修改！')
        return
      }
      this.isBdPopShow = true
    },
    openCommonPop(type){
      this.curCommonPopType = type
      switch(type){
        case 'location':
          this.commonPickerData = this.locationList
          break
        case 'marital':
          this.commonPickerData = this.maritalStatusArr
          break
        case 'job':
          this.commonPickerData = this.jobArr
          break
        case 'education':
          this.commonPickerData = this.educationArr
          break
        case 'nation':
          this.commonPickerData = this.nationArr
          break
      }
      this.isCommonPickerPopShow = true
    },
    commonPickerConfirmFun(e){
      console.log(e)
      switch(this.curCommonPopType){
        case 'location':
          this.location = e.join('/')
          break
        case 'marital':
          this.maritalStatus = e
          break
        case 'job':
          this.job = e
          break
        case 'education':
          this.education = e
          break
        case 'nation':
          this.nation = e
          break
      }
      this.isCommonPickerPopShow = false
    },
    formatter(type, val) {
      if (type === 'year') {
        return `${val}年`;
      } else if (type === 'month') {
        return `${val}月`;
      } else if (type === 'day') {
        return `${val}日`;
      }
      return val;
    },
    confirmBirthday(e){
      this.birthday = moment(e).format('YYYY-MM-DD')
      // this.currentDate = new Date(e)
      this.isBdPopShow = false
    },
    blurTemperature(){
      if(!this.temperature){
        return
      }
      this.temperature = Number(this.temperature).toFixed(1)
    },
    blurGlycosylatedHemoglobin(){
      if(!this.glycosylatedHemoglobin){
        return
      }
      this.glycosylatedHemoglobin = Number(this.glycosylatedHemoglobin).toFixed(1)
    },
    formatVal(type){
      switch(type){
        case 'temperature':
          if(Number(this.temperature) > 50){
            this.temperature = 50
          }
          break;
        case 'spo2':
          if(Number(this.spo2) > 100){
            this.spo2 = 100
          }
          if(Number(this.spo2) < 60){
            this.spo2 = 60
          }
          this.saveSpo2Fun()
          break;
        case 'bf':
          for(let item of this.bfInfoArr){
            if(!item.value) continue
            if(Number(item.value) > 100){
              item.value = 100
            }
          }
          break;
        case 'FEV1':
        case 'glycosylatedHemoglobin':
          if(Number(this.FEV1) > 100){
            this.FEV1 = 100
          }
          if(Number(this.glycosylatedHemoglobin) > 100){
            this.glycosylatedHemoglobin = 100
          }
          break;
        case 'bp':
          for(let item of this.bpInfoArr){
            if(item.id == 'sbp' && item.value > 299){
              item.value = 299
            }
            if(item.id == 'dbp' && item.value > 255){
              item.value = 255
            }
            if(item.id == 'pulse' && item.value > 200){
              item.value = 200
            }
          }
          break;
      }
    },
    saveSpo2Fun(){
      let params = {
        "measure_type": 3, 
        "spo2": this.spo2, 
        "measure_at": moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
        "family_user_id": 0,
        "equipment_no": ''
      }
      saveSpo2(params).then(res=>{
        if(res.status == 0){
          console.log('血氧保存成功')
        }else{
          console.log('血氧保存失败')
        }
      })
    },
    saveBsFun(item){
      if(!item.value){
        return
      }
      let bgVal = Number(item.value).toFixed(1)
      let params = {
        id: 0, // 血糖数据id
        bg: bgVal, // 血糖值
        measure_at: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'), // 测量时间
        dining_status: this.handleBgName(item.id).type, // 就餐状态
        equipment_no: '',
        measure_type: 0,
        product_id: this.source.indexOf('zzApp') > -1 ? 0 : 15,
        family_user_id: 0 
      }
      saveBs(params).then(res=>{
        if(res.status == 0){
          console.log('血糖保存成功')
        }else{
          console.log('血糖保存失败')
        }
      })
    },
    blurHeight(){
      if(!this.height){
        return
      }
      this.height = Number(this.height).toFixed(2)
      if(this.height < 50){
        this.height = 50
      }
      if(this.height >500){
        this.height = 500
      }
      if(this.weight){
        this.saveHWFun(this.height,this.weight)
      }
    },
    blurWeight(){
      if(!this.weight){
        return
      }
      this.weight = Number(this.weight).toFixed(2)
      if(Number(this.weight) < 10){
        this.weight = 10
      }
      if(Number(this.weight) >500){
        this.weight = 500
      }
      if(this.height){
        this.saveHWFun(this.height,this.weight)
      }
    },
    saveHWFun(){
      let param = {
        "product_id": this.source.indexOf('zzApp') > -1 ? 0 : 15,
        "measure_type": 0,
        "height": this.height,
        "weight": this.weight,
        "measure_at": moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
      }
      saveHW(param).then(res=>{
        if(res.status == 0){
          this.BMI = res.data.bmi
          console.log('身高体重保存成功')
        }
      })
    },
    handleBMI(h,w){
      if(h && w){
        let _h = Number(h/100).toFixed(2)
        let temp = Number(_h*_h).toFixed(2)
        this.BMI = Number(w/temp).toFixed(2)
      }
    },
    blurBp(id){
      let sbp = this.bpInfoArr.find(item => item.id == 'sbp')
      let dbp = this.bpInfoArr.find(item => item.id == 'dbp')
      let pulse = this.bpInfoArr.find(item => item.id == 'pulse')
      if(id == 'sbp' || id == 'dbp'){
        if(sbp.value && dbp.value && Number(dbp.value) >= Number(sbp.value)){
          this.$toast('舒张压不能高于收缩压！')
          return
        }
      }
      if(sbp.value && dbp.value && pulse.value){
        let bpData = {
          dbp: dbp.value,
          equipment_no: "",
          measure_at: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
          measure_type: "0",
          pulse: pulse.value,
          sbp: sbp.value,
        }
        this.saveBpFun(bpData)
      }
    },
    saveBpFun(bpData){
      let param = {
        "id": 0,
        "product_id": this.source.indexOf('zzApp') > -1 ? 0 : 15,
        "measure_type": 0,
        "measure_data": [bpData],
        "measure_time": moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
        'family_user_id': 0
      }
      saveBp(param).then(res=>{
        if(res.status == 0){
          console.log('血压保存成功')
        }else{
          console.log('血压保存失败',res)
        }
      })
    },
    blurBf(curItem){
      let valObj = this.bfInfoArr.find(item => item.id == curItem.id)
      if(valObj.value && Number(valObj.value) <= 0 || Number(valObj.value) > 100){
        this.$toast(`${curItem.label}值的范围是 0 ~ 100 之间的正数`)
        return
      }
      let params = {}
      let hasCorrectVal = false
      for(let item of this.bfInfoArr){
        params[item.id] = item.value
        if(!item.value) continue
        if(Number(item.value) <= 0 || Number(item.value) > 100){
          // this.$toast(`${item.label}值的范围是 0 ~ 100 之间的正数`)
          return
        }
        hasCorrectVal = true
      }
      if(hasCorrectVal) this.saveBfFun(params)
    },
    saveBfFun(params){
      saveBf(params).then(res=>{
        if(res.status == 0){
          console.log('血脂保存成功')
        }else{
          console.log('血脂保存失败',res)
        }
      })
    },
    drinkHabitChange(val){
      console.log(val)
      this.livingHabits.drinkType.required = val == 2 ? 1 : 2
      this.livingHabits.drinkMl.required = val == 2 ? 1 : 2
      if(val !== 2){
        this.livingHabits.drinkType.value = ''
        this.livingHabits.drinkMl.value = ''
      }
    },
    // 必填和规则校验
    checkData(){
      if(this.name == ''){
        this.$toast('请输入真实姓名')
        return
      }
      if(this.gender === ''){
        this.$toast('请选择性别')
        return
      }
      if(this.birthday == ''){
        this.$toast('请选择生日')
        return
      }
      if(this.height == ''){
        this.$toast('请输入身高')
        return
      }
      if(this.weight == ''){
        this.$toast('请输入体重')
        return
      }
      if(this.configData.current_symptoms && this.configData.current_symptoms.is_required == 1){
        if(this.curSymptomsSelectArr.length == 0){
          this.$toast('请选择当前症状')
          return
        }
        if(this.curSymptomsSelectArr.indexOf('咳痰') > -1){
          if(this.sputumColor == ''){
            this.$toast('请选择痰色')
            return
          }
        }
      }
      
      if(this.diseaseIdArr.length == 0){
        this.$toast('请选择所患疾病')
        return
      }
      // 生活方式的校验
      // ['smokingHabit','drinkHabit','drinkType','eatHabit','sleepStatus','drinkMl','sportWeek','sportDay','sedentary','sleepTime']
      let livingKeys = Object.keys(this.livingHabits)
      for(let key of livingKeys){
        if(this.livingHabits[key].required == 1 && (this.livingHabits[key].value === '' || this.livingHabits[key].value === 0 || this.livingHabits[key].value.length == 0)){
          const selectTipArr = ['smokingHabit','drinkHabit','drinkType','eatHabit','sleepStatus']
          if(selectTipArr.indexOf(key) > -1){
            this.$toast(`请选择${this.livingHabits[key].label}`)
            return
          }else{
            this.$toast(`请输入${this.livingHabits[key].label}`)
            return
          }
        }
      }
      // ['current_symptoms', 'disease', 'body_temperature', 'bp', 'fpg', 'oxygen_saturation', 'blood_fat', 'lung_function', 'hba1c']
      if(this.configData.body_temperature && this.configData.body_temperature.is_required == 1){
        if(this.temperature == ''){
          this.$toast('请输入体温')
          return 
        }
      }
      if(this.configData.bp && this.configData.bp.is_required == 1){
        let sbp = this.bpInfoArr.find(item => item.id == 'sbp')
        let dbp = this.bpInfoArr.find(item => item.id == 'dbp')
        let pulse = this.bpInfoArr.find(item => item.id == 'pulse')
        if(sbp.value == '' || dbp.value == '' || pulse.value == ''){
          this.$toast('请完善血压信息')
          return
        }
        if(sbp.value && dbp.value && Number(dbp.value) >= Number(sbp.value)){
          this.$toast('舒张压不能高于收缩压！')
          return
        }
      }
      // if(this.configData.fpg && this.configData.fpg.is_required == 1){
      //   if(this.bsNum == ''){
      //     this.$toast('请输入空腹血糖')
      //     return 
      //   }
      // }
      if(this.configData.bgArr.length > 0){
        for(let item of this.configData.bgArr){
          if(item.is_required == 1){
            if(item.value == ''){
              this.$toast(`请输入${item.name}`)
              return
            }
          }
        }
      }

      if(this.configData.oxygen_saturation && this.configData.oxygen_saturation.is_required == 1){
        if(this.spo2 == ''){
          this.$toast('请输入血氧饱和度')
          return 
        }
      }
      if(this.configData.blood_fat && this.configData.blood_fat.is_required == 1){
        // let isFull = true
        // for(let item of this.bfInfoArr){
        //   if(!item.value){
        //     isFull = false
        //     break
        //   }
        // }
        // if(!isFull){
        //   this.$toast('请完善血脂信息')
        //   return
        // }
        let hasCorrectVal = false
        for(let item of this.bfInfoArr){
          if(!item.value) continue
          if(Number(item.value) <= 0 || Number(item.value) > 100){
            this.$toast(`${item.label}值的范围是 0 ~ 100 之间的正数`)
            return
          }
          hasCorrectVal = true
        }
        if(!hasCorrectVal){
          this.$toast('请完善血脂信息,至少填写一项')
          return
        }
      }
      if(this.configData.lung_function && this.configData.lung_function.is_required == 1){
        if(this.FEV1 == ''){
          this.$toast('请输入肺功能（FEV1）')
          return 
        }
      }
      if(this.configData.hba1c && this.configData.hba1c.is_required == 1){
        if(this.glycosylatedHemoglobin == ''){
          this.$toast('请输入糖化血红蛋白')
          return 
        }
      }
      return true
    },
    // 格式校验
    formatData(){
      if((this.height || this.weight) && (!this.height || !this.weight)){
        this.$toast('请完善身高/体重')
        return
      }
      let sbp = this.bpInfoArr.find(item => item.id == 'sbp')
      let dbp = this.bpInfoArr.find(item => item.id == 'dbp')
      let pulse = this.bpInfoArr.find(item => item.id == 'pulse')
      if((sbp.value || dbp.value || pulse.value) && (!sbp.value || !dbp.value || !pulse.value)){
        this.$toast('请完善血压信息')
        return
      }
      if(sbp.value && dbp.value && pulse.value && Number(dbp.value) >= Number(sbp.value)){
        this.$toast('舒张压不能高于收缩压！')
        return
      }

      for(let item of this.bfInfoArr){
        if(!item.value) continue
        if(Number(item.value) <= 0 || Number(item.value) > 100){
          this.$toast(`${item.label}值的范围是 0 ~ 100 之间的正数`)
          return
        }
      }
      return true
    },
    locationNameToId(){
      let resArr = []
      if(this.location != ''){
        const [province,city,district] = this.location.split('/')
        this.locationList.forEach(item=>{
          if(item.name == province){
            resArr.push(item.id)
            item.children.forEach(inner=>{
              if(inner.name == city){
                resArr.push(inner.id)
                inner.children.forEach(inner2=>{
                  if(inner2.name == district){
                    resArr.push(inner2.id)
                  }
                })
              }
            })
          }
        })
      }
      return resArr
    },
    async saveData(){
      let checkRes = true
      let formatRes = true
      // 如果是患者APP完善信息进来的 不做校验 因为没有必填字段
      if(this.from != 'todoTask'){
        if(this.source == 'zzApp-servicePackage'){
          checkRes = this.checkData()
        }
        formatRes = this.formatData()
      }
      console.log(this.$data)
      console.log(checkRes,formatRes)
      if(checkRes && formatRes){
        let basicParams = { name: this.name, bday: this.birthday, sex: this.gender, sfzid: this.sfzid, scene_id: 'c0004' }
        let bascInfoRes = await saveBasicInfo(basicParams)
        let tempObj = {}
        for(let item of this.extraInfoArr){
          if(item.value == 2){
            item.desc = ''
          }
          tempObj[item.id] = item.value || 0
          tempObj[`${item.id}_desc`] = item.desc
        }
        let locationIdArr = this.locationNameToId()
        let patientParams = {
          ...tempObj,
          "current_symptoms": this.curSymptomsSelectArr,
          "phlegm_color": this.sputumColor,
          "disease": this.diseaseIdArr,
          "cigarette_habit": this.livingHabits.smokingHabit.value || 0,
          "food_habit": this.livingHabits.eatHabit.value,
          "drink_habit": this.livingHabits.drinkHabit.value,
          "wine_type": this.livingHabits.drinkType.value || 0,
          "drink_quanlity": this.livingHabits.drinkMl.value || 0,
          "weekly_exercise_avg": this.livingHabits.sportWeek.value,
          "day_exercise_avg": this.livingHabits.sportDay.value,
          "sedentary_time_avg": this.livingHabits.sedentary.value,
          "sleep_status": this.livingHabits.sleepStatus.value,
          "day_sleep_time_avg": this.livingHabits.sleepTime.value,
          "body_temperature": this.temperature,
          "lung_function": this.FEV1,
          "hba1c": this.glycosylatedHemoglobin,
          nation: this.nation,
          marital_status: this.maritalStatus.value,
          edu_lv: this.education.value,
          profession_type: this.job.value,
          province_id: locationIdArr.length > 0 ? locationIdArr[0] : '',
          city_id: locationIdArr.length > 1 ? locationIdArr[1] : '',
          district_id: locationIdArr.length > 2 ? locationIdArr[2] : '',
          addr: this.addr
        }

        let patientInfoRes = await savePatient(patientParams)

        // let bfParams = {}
        // for(let item of this.bfInfoArr){
        //   bfParams[item.id] = item.value
        // }
        // let bfRes = await saveBf(bfParams)
        Promise.all([bascInfoRes,patientInfoRes]).then(res=>{
          let [res1,res2] = res
          if(res1.status == 0 && res2.status == 0){
            this.$toast('保存成功')
            setTimeout(()=>{
              this.handleJump()
            },1500)
          }else{
            let errMsg = res1.status != 0 ? res1.msg : res2.msg
            this.$toast('保存失败，' + errMsg)
          }
        })
      }
    },
    handleJump(){
      if(this.source.indexOf('zzApp') > -1){
        jsToClient('goBack', '').catch((err) => console.log('回退错误', err))
      }else{
        wx.miniProgram.navigateBack()
      }
    },
    async init(){
      this.token = this.$route.query.token
      localStorage.setItem('authorization',this.token)
      this.source = this.$route.query.source
      this.packageId = this.$route.query.packageId
      this.from = this.$route.query.from
      console.log(this.token,this.source)
      // this.getPatientInfo()
      await this.getLocationFun()
      if(this.source == 'zzApp-servicePackage'){
        this.getConfigData()
      }else{
        this.configData = this.defaultConfigData
        this.getPatientInfo()
      }
      
    },
    async getLocationFun(){
      let res = await getLocationList()
      if(res.status == 0){
        this.locationList = res.data.list.map(item=>{
          item.children = item._child.map(inner=>{
            inner.children = inner._child
            return inner
          })
          return item
        })
      }
    },
    getPatientInfo(){
      getPatientInfo({scene_id:'c0004'}).then(res=>{
        if(res.status == 0){
          let data = res.data
          if(this.source == 'doc-app'){
            for(let item of this.readonlyHealthInfoArr){
              for(let labelItem of item.list){
                switch(labelItem.id){
                  case 'current_symptoms':
                  case 'disease':
                    // labelItem.value = data[labelItem.id].length > 0 ? data[labelItem.id].filter(item=>item).join(',') : ''
                    let filterArr = []
                    if(data[labelItem.id].length > 0){
                      filterArr = data[labelItem.id].map(item=>{
                        if(item == '咳痰'){
                          // item = `${item}(${data.phlegm_color})`
                          item = item + (data.phlegm_color?`(${data.phlegm_color})`:'')
                        }
                        return item
                      }).filter(item=>item)
                    }
                    labelItem.value = filterArr.join(',') || ''
                    break;
                  case 'HW':
                    labelItem.value = data.height && data.weight ? `${data.height}cm/${data.weight}kg/BMI：${data.bmi}` : ''
                    break;
                  case 'bp':
                    labelItem.value = data.sbp && data.dbp ? `收缩压：${data.sbp}mmHg；舒张压：${data.dbp}mmHg；脉搏：${data.pulse}次/分钟` : ''
                    break;
                  case 'sportHabit':
                    // labelItem.value = data.weekly_exercise_avg && data.day_exercise_avg && data.sedentary_time_avg ? `平均每周运动${data.weekly_exercise_avg}天，每天运动${data.day_exercise_avg}小时，每天久坐${data.sedentary_time_avg}小时` : ''
                    let week = data.weekly_exercise_avg != '' ? `平均每周运动${data.weekly_exercise_avg}天` : ''
                    let day = data.day_exercise_avg != '' ? `每天运动${data.day_exercise_avg}分钟` : ''
                    let sedentary = data.sedentary_time_avg != '' ? `每天久坐${data.sedentary_time_avg}小时` : ''
                    labelItem.value = [week,day,sedentary].filter(item=>item).join('；')
                    break;
                  case 'sleepHabit':
                    // labelItem.value = data.sleep_status && data.day_sleep_time_avg ? `睡眠状态：${data.sleep_status}，平均每天睡眠：${data.day_sleep_time_avg}分钟` : ''
                    let sleepStatus = data.sleep_status ? `睡眠状态：${data.sleep_status}` : ''
                    let sleepTime = data.day_sleep_time_avg != '' ? `平均每天睡眠：${data.day_sleep_time_avg}小时` : ''
                    labelItem.value = [sleepStatus,sleepTime].filter(item=>item).join('；')
                    break;
                  case 'drinkHabit':
                    if(data.drink_habit == 2){
                      // labelItem.value = `${ data.drink_habit_format }，${data.wine_type_format}，${data.drink_quanlity}ml/天`
                      labelItem.value = data.drink_habit_format + (data.wine_type_format ? `,${data.wine_type_format}`:'') + (data.drink_quanlity?`,${data.drink_quanlity}ml/天`:'')
                    }else{
                      labelItem.value = data.drink_habit_format || ''
                    }
                    break;
                  case 'bf':
                    labelItem.value = `总胆固醇(TC):${data.tc || ' -- '}mmol/L<br/>甘油三酯(TG):${data.tg || ' -- '}mmol/L<br/>低密度脂蛋白胆固醇(LDL-C):${data.ldl_c || ' -- '}mmol/L<br/>高密度脂蛋白胆固醇(HDL-C):${data.hdl_c || ' -- '}mmol/L<br/>非高密度脂蛋白胆固醇(非-HDL-C):${data.un_hdl_c || ' -- '}mmol/L`
                    break;
                  case 'spo2':
                  case 'hba1c':
                  case 'lung_function':
                    labelItem.value = data[labelItem.id] ? data[labelItem.id] + '%' : ''
                    break;
                  case 'body_temperature':
                    labelItem.value = data[labelItem.id] ? data[labelItem.id] + '°C' : ''
                    break;
                  case 'drug_allergy':
                  case 'family_genetic':
                  case 'operation':
                  case 'trauma':
                    labelItem.value = data[labelItem.id] == 2 ? '无' : data[labelItem.id] == 1 ? data[`${labelItem.id}_desc`] || '有' : ''
                    break;
                  case 'bg':
                  case 'morning_bg':
                  case 'before_lunch_bg':
                  case 'after_lunch_bg':
                  case 'before_dinner_bg':
                  case 'after_dinner_bg':
                  case 'before_bed_bg':
                  case 'early_morning_bg':
                    labelItem.value =  data[labelItem.id] ? `${data[labelItem.id]}mmol/L` : ''
                    break;
                  case 'sfzid':
                    labelItem.value =  data[labelItem.id] || ''
                    break;
                  case 'nation':
                    labelItem.value =  data[labelItem.id] || ''
                    break;
                  case 'profession_type':
                    let jobObj = this.jobArr.find(item=> item.value == data[labelItem.id])
                    labelItem.value =  jobObj ? jobObj.name : ''
                    break;
                  case 'edu_lv':
                    let eduObj = this.educationArr.find(item=> item.value == data[labelItem.id])
                    labelItem.value =  eduObj ? eduObj.name : ''
                    break;
                  case 'marital_status':
                    let marObj = this.maritalStatusArr.find(item=> item.value == data[labelItem.id])
                    labelItem.value =  marObj ? marObj.name : ''
                    break;
                  case 'addr':
                    this.locationList.forEach(item=>{
                      if(item.id == data.province_id){
                        labelItem.value = item.name
                        item.children.forEach(inner=>{
                          if(inner.id == data.city_id){
                            labelItem.value += inner.name
                            inner.children.forEach(inner2=>{
                              if(inner2.id == data.district_id){
                                labelItem.value += inner2.name
                              }
                            })
                          }
                        })
                      }
                    })
                    labelItem.value += data.addr
                    break;
                  default:
                    labelItem.value = data[labelItem.id] || ''
                }
                
              }
            }
          }else{
            this.name = this.tempName = data.name
            this.gender = this.tempGender =  data.sex_format == '男' ? 1 : data.sex_format == '女' ? 2 : ''
            this.birthday = this.tempBirthday = data.bday == "0000-00-00" ? '' : data.bday
            this.currentDate = data.bday == "0000-00-00" ? new Date() : new Date(data.bday)
            this.height = data.height
            this.height = data.height
            this.weight = data.weight
            this.BMI = data.bmi
            this.curSymptomsSelectArr = data.current_symptoms
            this.sputumColor = data.phlegm_color
            this.diseaseIdArr = data.disease
            this.livingHabits.smokingHabit.value = data.cigarette_habit
            this.livingHabits.drinkHabit.value = data.drink_habit
            this.livingHabits.drinkType.value = data.wine_type || ''
            this.livingHabits.drinkMl.value = data.drink_quanlity || ''
            this.livingHabits.eatHabit.value = data.food_habit
            this.livingHabits.sleepStatus.value = data.sleep_status
            this.livingHabits.sleepTime.value = data.day_sleep_time_avg
            this.livingHabits.sportWeek.value = data.weekly_exercise_avg
            this.livingHabits.sportDay.value = data.day_exercise_avg
            this.livingHabits.sedentary.value = data.sedentary_time_avg

            this.temperature = data.body_temperature
            this.spo2 = data.spo2 || ''
            // this.bsNum = data.bg || ''
            this.FEV1 = data.lung_function
            this.glycosylatedHemoglobin = data.hba1c
            // 血压回填
            for(let item of this.bpInfoArr){
              item.value = data[item.id] || ''
            }
            // 血脂回填
            for(let item of this.bfInfoArr){
              item.value = data[item.id]
            }
            // 药物过敏史，家族遗传史等四个字段的回填
            for(let item of this.extraInfoArr){
              item.value = data[item.id] || 0
              item.desc = data[`${item.id}_desc`]
            }
            // 血糖回填
            this.configData.bgArr.forEach(item=>{
              item.value = data[item.id] || ''
              if(item.id == 'fpg'){
                item.value = data.bg || ''
              }
            })
            
            this.locationList.forEach(item=>{
              if(item.id == data.province_id){
                this.location = item.name
                item.children.forEach(inner=>{
                  if(inner.id == data.city_id){
                    this.location += '/' + inner.name
                    inner.children.forEach(inner2=>{
                      if(inner2.id == data.district_id){
                        this.location += '/' + inner2.name
                      }
                    })
                  }
                })
              }
            })
            this.addr = data.addr || ''
            this.sfzid = this.tempSfzid = data.sfzid || ''
            this.nation = data.nation || ''
            let educationObj = this.educationArr.find(item=>item.value == data.edu_lv)
            let jobObj = this.jobArr.find(item=>item.value == data.profession_type)
            let maritalStatusObj = this.maritalStatusArr.find(item=>item.value == data.marital_status)
            this.education.name = educationObj ? educationObj.name : ''
            this.education.value = data.edu_lv
            this.job.name = jobObj ? jobObj.name : ''
            this.job.value = data.profession_type
            this.maritalStatus.name = maritalStatusObj ? maritalStatusObj.name : ''
            this.maritalStatus.value = data.marital_status

            if(data.user_scene_level == 2){
              this.canEdit = data.scene_level == 2 ? true : false
            }else{
              this.canEdit = true
            }
            console.log(this.canEdit)
          }
        }
      })
    },
    getConfigData(){
      getConfig({package_id: this.packageId}).then(res=>{
        if(res.status == 0){
          let bgArr = []
          Object.keys(res.data).forEach(item=>{
            if(item.indexOf('_bg') > -1 || item == 'fpg'){
              let obj = {
                id: item,
                name: this.handleBgName(item).name,
                value: '',
                is_required: res.data[item].is_required
              }
              bgArr.push(obj)
            }
          })
          this.configData = res.data
          // this.configData.bgArr = bgArr
          this.$set(this.configData,'bgArr',bgArr)
          this.getPatientInfo()
          // ['current_symptoms', 'disease', 'body_temperature', 'bp', 'fpg', 'oxygen_saturation', 'blood_fat', 'lung_function', 'hba1c']
        }
      })
    },
    handleBgName(id){
      let name = ''  //就餐状态词
      let type = 0  //就餐状态id
      switch(id){
        case 'fpg':
          name = '空腹血糖'
          type = 1
          break;
        case 'morning_bg':
          name = '早餐后血糖'
          type = 2
          break;
        case 'before_lunch_bg':
          name = '午餐前血糖'
          type = 3
          break;
        case 'after_lunch_bg':
          name = '午餐后血糖'
          type = 4
          break;
        case 'before_dinner_bg':
          name = '晚餐前血糖'
          type = 5
          break;
        case 'after_dinner_bg':
          name = '晚餐后血糖'
          type = 6
          break;
        case 'before_bed_bg':
          name = '睡前血糖'
          type = 7
          break;
        case 'early_morning_bg':
          name = '凌晨血糖'
          type = 8
          break;
      }
      return {name,type}
    },
    goMedication(){
      getMedicationUrl().then(res=>{
        if(res.status == 0){
          window.location.href = `${res.data.url}&isFrom=zzApp&scene=3`
        }
      })
    },
  },
  mounted(){
    this.init()
  }
}
</script>

<style lang="scss">
.healthInfo{
  width: 100%;
  padding-bottom: 100px;
  .title{
    width: 100%;
    height: 44px;
    line-height: 44px;
    text-align: left;
    background: #F5F5F5;
    color: #000000;
    font-size: 17px;
    font-weight: 500;
    padding-left: 15px;
    box-sizing: border-box;
    .required{
      font-size: 15px;
      color: #5A6266;
    }
  }
  .inner{
    padding: 0 15px;
    font-size: 17px;
    .inputLine{
      padding: 14px 0;
      // height: 58px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid rgba(0, 0, 0, 0.05);
      .rightVal{
        display: flex;
        align-items: center;
      }
      .label,.input{
        color: #0A0A0A;
      }
      .label{
        min-width: 88px;
        max-width: max-content;
        text-align: left;
      }
      .input{
        width: 100px;
        text-align: right;
      }
      .rightUnit{
        text-align: right;
        display: flex;
        align-items: center;
      }
      .genderOuter{
        border-radius: 4px;
        overflow: hidden;
        display: flex;
        border: .5px solid #BFC6CD;
        .male,.female{
          width: 40px;
          height: 30px;
          text-align: center;
          line-height: 32px;
        }
        .male{border-right: .5px solid #BFC6CD;}
        .selected{
          background-color: #3388FF;
          color: white;
        }
      }
    }
    .radioLine{
      border-bottom: 1px solid rgba(0,0,0,0.05);
      .labelLine{
        height: 58px;
        line-height: 58px;
        color: #0A0A0A;
        // padding: 14px 0;
        text-align: left;
      }
      .descLine{
        // padding: 10px 0 14px;
        // height: 60px;
        padding-top: 10px;
        text-align: left;
        .descLabel{
          color: #0A0A0A;
        }
        .descInputOuter{padding: 10px 0;}
        .descInput{
          width: 100%;
          height: 76px;
          text-align: left;
          font-size: 15px;
          border: 1px solid rgba(0, 0, 0, 0.05);
          padding: 6px;
          line-height: 22px;
          box-sizing: border-box;
        }
      }
    }
    .genderLine{padding: 10px 0;}
    .rightTips{
      color: #BFC6CD;
    }
    .unit{
      margin-left: 12px;
    }
    .readonlyText{
      text-align: right;
      line-height: 22px;
      font-size: 17px;
    }
  }
  .bottomTips{
    font-size: 13px;
    color: #878F99;
    padding: 16px 0;
  }
  .bottomBtn{
    width: 315px;
    text-align: center;
    height: 50px;
    line-height: 52px;
    color: white;
    font-size: 17px;
    font-weight: 600;
    border-radius: 8px;
    background-color: #008FFD;
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
  }
  .van-picker__confirm{
    color: #3388FF;
  }
  .van-radio,.van-checkbox{
    padding: 12px 0;
  }
  input::-webkit-input-placeholder {
    color: #BFC6CD;
  }
  .van-cell{
    padding: 0 !important;
    // position: static !important;
    font-size: 17px;
  }
  .bgColor{
    background: #F5F5F5;
    margin: 0 -15px;
    padding-left: 15px !important;
    padding-right: 15px !important;
    input{
      background: #F5F5F5;
    }
  }
}
</style>