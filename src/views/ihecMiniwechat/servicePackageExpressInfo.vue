<template>
  <div class="servicePackageExpressInfo">
    <div class="iframeOuter" :class="[expressList.length > 3 ? 'iframeMinH' : 'iframeMaxH']">
        <iframe width="100%" height="100%" class="mapIframe" :src="mapUrl" frameborder="0"></iframe>
    </div>
    <div class="expressInfoOuter">
        <div class="expressNoLine">
            <span class="expressNo">物流单号：<span class="target expressNo">{{expressNo}}</span></span>
            <div class="copyBtn" data-clipboard-action="copy" data-clipboard-target=".target" @click="copyExpressNo">复制</div>
        </div>
        <div class="node" v-for="(item,index) in expressList" :key="index">
            <div class="leftDot">
                <img v-if="signCodeArr.indexOf(item.statusCode) == -1" class="expressIcon" src="@/assets/images/express_bus.png" alt="">
                <span v-else>收</span>
            </div>
            <div class="rightInfo" :class="[index == 0?'curNodeColor':'oldNodeColor']">
                <div class="line"></div>
                <span class="nodeTitle">{{item.status || '未知状态'}}</span>
                <span class="time">{{item.time}}</span>
                <span class="nodeInfo textCut2">{{item.event}}</span>
            </div>
        </div>
    </div>
  </div>
</template>

<script>
import { getExpressList } from '@/api/ihecMiniwechat'
import { getUrlParams } from '@/utils/utils.js'
import Clipboard from 'clipboard'
export default {
    data(){
        return {
            expressNo: '',
            mapUrl: '',
            expressList: [],
            signCodeArr: ['3','301','302','303','304'],  //签收状态code
        }
    },
    methods: {
        init(){
            this.getExpressList()
        },
        getExpressList(){
            let url = window.location.href
            this.expressNo = getUrlParams(url,'deliver_no')
            let obj = {
                deliver_no: this.expressNo
            }
            getExpressList(obj).then(res=>{
                if(res.status == 0){
                    this.expressList = res.data.traces
                    this.mapUrl = res.data.map_url
                }
            })
        },
        copyExpressNo() {
            let that = this
            let clipboard = new Clipboard('.copyBtn')
            clipboard.on('success', function(e) {
                that.$toast('复制成功', 1500)
                e.clearSelection()
            })
      },
    },
    mounted(){
        this.init()
    }
}
</script>

<style lang="scss">
.servicePackageExpressInfo{
    .expressNoLine{
        display: flex;
        align-items: center;
        padding: 10px 0 20px;
        .expressNo{
            font-size: 14px;
            color: #0A0A0A;
            text-align: left;
            font-weight: 400;
        }
        .copyBtn{
            width: 34px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            color: #ECB45F;
            border: 1px solid #ECB45F;
            border-radius: 30px;
            margin-left: 8px;
            // margin-top: -4px;
        }
    }
    
    .iframeOuter{
        position: fixed;
        width: 100%;
    }
    .iframeMinH{
        height: calc(100% - 260px);
    }
    .iframeMaxH{
        height: calc(100% - 100px);
    }
    .expressInfoOuter::-webkit-scrollbar{
        width: 0px;
    }
    .expressInfoOuter{
        position: fixed;
        bottom: 0;
        z-index: 999;
        background: white;
        border-radius: 10px;
        padding: 10px 20px 0;
        max-height: 400px;
        overflow: auto;
        width: 100%;
        box-sizing: border-box;
        .node:last-of-type .line{
            display: none;
        }
        .node{
            display: flex;
            .leftDot{
                width: 25px;
                height: 25px;
                border-radius: 50%;
                display: flex;
                justify-content: center;
                align-items: center;
                background: #ECB45F;
                color: white;
                font-weight: 600;
                font-size: 11px;
                .expressIcon{
                    width: 17px;
                    height: 17px;
                    background: transparent;
                    filter: brightness(100);
                }
            }
            .rightInfo{
                flex: 1;
                text-align: left;
                padding: 5px 0 25px 10px;
                position: relative;
                .line{
                    border-left: 1px dashed #CCCCCC;
                    height: calc(100% - 25px);
                    position: absolute;
                    left: -13px;
                    top: 24px;
                }
                .nodeTitle{
                    font-size: 17px;
                    font-weight: 500;
                    display: block;
                }
                .time,.nodeInfo{
                    margin-top: 6px;
                    display: block;
                    font-size: 12px;
                    font-weight: 400;
                }
                .nodeInfo{
                    line-height: 15px;
                }
            }
        }
    }
    .curNodeColor{
        color: #0A0A0A;
    }
    .oldNodeColor{
        color: #878F99;
    }
    .textCut2{
        display: -webkit-box;
        word-break: break-all;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        text-overflow: ellipsis;
    }
}
</style>