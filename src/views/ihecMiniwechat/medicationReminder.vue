<!--*
*设定随访时间 
*
-->
<template>
    <div class="medicationReminder">
        <div class="header">
        </div>
        <div class="times">
            <div class="btnsT">
                <van-button @click="addTime('btn1')" type="info" block class="btn_time"
                    :color="buttonColor('btn1')"><span class="btnT_span">一个月后</span></van-button>
                <van-button @click="addTime('btn2')" type="info" block class="btn_time"
                    :color="buttonColor('btn2')"><span class="btnT_span">三个月后</span></van-button>
                <van-button @click="addTime('btn3')" type="info" block class="btn_time"
                    :color="buttonColor('btn3')"><span class="btnT_span">半年后</span></van-button>
                <van-button @click="addTime('btn4')" type="info" block class="btn_time"
                    :color="buttonColor('btn4')"><span class="btnT_span">自定义</span></van-button>
            </div>
            <div class="settingT">
                <input type="tel" class="tim" v-model="dateInfo.years" @focus="firstFocus()" @blur="firstBlur()"
                    @input="onYears" ref="firstinput">
                <span>年</span>
                <input type="tel" class="tim" v-model="dateInfo.months" @focus="secondFocus()" @blur="secondBlur()"
                    @input="onMonths" ref="secondinput">
                <span>月</span>
                <input type="tel" class="tim" v-model="dateInfo.days" @focus="thirdFocus()" @blur="thirdBlur()"
                    @input="onDays" ref="thirdinput">
                <span>日</span>
            </div>
            <div class="timesTips">注：本设置仅作为日程提醒，不可替代挂号行为</div>
        </div>
        <div class="footer">
            <div class="footer_box">
                <div class="footer_content" v-for="(item, index) in education_data" :key="index">
                    <!-- <div class="title">糖尿病肾病筛查</div>
                    <div class="text">
                        糖尿病肾病是糖尿病患者的主要死亡原因之一，建议每年至少进行一次筛查，包括尿常规、尿ACR和血肌酐等。随机尿白蛋白/肌酐的比(UACR)≥30mg/g，应在3~6月内复查。</div> -->
                    <div class="title">{{item.title}}</div>
                    <div class="text">{{item.content}}</div>
                </div>
            </div>

        </div>
        <div class="btn">
            <van-button @click="save()" type="info" block round style="width: 92%;height: 52px;"
                color="linear-gradient(180deg, #FC7C34 0%, #FF602E 100%)">
                <span class="btn_span">设置就诊提醒</span>
            </van-button>
        </div>

        <!-- <van-number-keyboard :show="keyShow" @blur="keyShow = false" @input="onInput" @delete="onDelete" :maxlength="2" /> -->

        <van-dialog v-model="successShow" width="315px" theme='round-button' confirm-button-text="知道啦"
            confirm-button-color="linear-gradient(to right,rgba(252, 124, 52, 1), rgba(255, 96, 46, 1))"
            @confirm="handleConfirm()">
            <img src="../imgPage/success.png" />
            <p class="successTitles">恭喜设定成功</p>
            <p class="successText">您已设定 {{ this.saveDate }} 提醒您复诊哦！</p>
        </van-dialog>

        <van-dialog v-model="reservedShow" width="315px" :show-cancel-button="false" :show-confirm-button="false">
            <p class="selectTips">温馨提示</p>
            <p class="selectText">您已设定&ensp;{{ this.reservedDate }}&ensp;的复诊提醒，我们会在提醒日期的前 7 天开始通过微信给您推送复诊注意事项哦。</p>
            <div style="padding: 0 28px;">
                <van-button round block style="border:none;width: 256px;margin-bottom: 12px;"
                    color="linear-gradient(to right,rgba(252, 124, 52, 1), rgba(255, 96, 46, 1))"
                    @click="() => { this.reservedShow = false, this.handleConfirm() }">好的</van-button>
                <van-button type="primary" plain block style="border:none" color="#FE6F32"
                    @click="cancel()">取消提醒并重新设定</van-button>
            </div>
        </van-dialog>
    </div>
</template>
<script>
import { Dialog } from 'vant';
import { getInfo, getEducationInfo, saveAppointment, cancelAppointment } from '@/api/medicationReminder.js'
import wx from 'weixin-js-sdk'
import moment from 'moment'
export default {
    data() {
        return {
            successShow: false,
            reservedShow: false,
            keyShow: false,
            diffType: 'first',
            dateInfo: {   //自定义日期
                years: '',
                months: '',
                days: ''
            },
            changeData: {   //一年后， 半年后， 三个月后的日期，只要输入的和这个不一致就切换到自定义
                years: '',
                months: '',
                days: ''
            },
            workroom_id: '',
            saveDate: '',
            reservedDate: '',
            appointment_num: '',
            activeButton: '',
            education_data: []
        };
    },
    created() {
        this.init()
    },
    mounted() {
        this.$nextTick(() => {
            const date = new Date();
            this.dateInfo.years = date.getFullYear().toString().slice(2, 4);
            this.dateInfo.months = (date.getMonth() + 1).toString().padStart(2, '0')
            this.dateInfo.days = date.getDate().toString().padStart(2, '0')

            //this.$refs.secondinput.focus()
            this.addTime('btn1');//默认一个月后
        })
    },
    methods: {
        async init() {
            await getEducationInfo().then(res => {
                if (res.status == 0) {
                    this.education_data = res.data;
                }
            })

            await getInfo({ project_id: this.$route.query.project_id }).then(res => {
                if (res.status == 300021) {
                    Dialog.alert({
                        title: '温馨提示',
                        message: res.msg,
                        width: '312px',
                        messageAlign: 'left',
                        confirmButtonColor: '#3388FF',
                        confirmButtonText: '确定'
                    }).then(() => {
                        this.handleConfirm()
                    });
                } else {
                    this.workroom_id = res.data.workroom_id
                    // status==1，已预约
                    if (res.data.status == 1) {
                        let { visit_at, appointment_num } = res.data.appointment
                        this.reservedDate = visit_at
                        this.appointment_num = appointment_num
                        this.reservedShow = true
                    }
                }
            })
            this.$refs.secondinput.focus()
            setTimeout(()=>{
                this.$refs.secondinput.blur()
            },300)
        },
        // 创建预约
        save() {
            this.saveDate = `20${this.dateInfo.years}-${this.dateInfo.months}-${this.dateInfo.days}`
            let nowTimes = new Date().getTime()
            let settingTimes = new Date(this.saveDate).getTime()
            let nextTimes = new Date().setFullYear(new Date().getFullYear() + 1)

            // 判断当前输入日期是否有效
            let legalDay = new Date(this.saveDate).getDate()
            if (legalDay != this.dateInfo.days) {
                this.errM('请检查当前设置日期是否有效！')
            } else {
                if (settingTimes > nowTimes && settingTimes <= nextTimes) {
                    let params = {
                        workroom_id: this.workroom_id,
                        visit_at: this.saveDate,
                        appt_mode: 1
                    }
                    saveAppointment(params).then(res => {
                        let { status } = res
                        if (status == 0) {
                            //this.successShow = true
                            this.reservedDate = this.saveDate;
                            this.reservedShow = true;
                            this.appointment_num = res.data.appointment_num;
                        } else {
                            this.errM(res.msg)
                        }
                    })
                } else if (settingTimes > nextTimes) {
                    this.errM('建议您设置365天以内的日期进行复诊提醒')
                } else {
                    this.errM('请设置今天之后的时间')
                }
            }
        },
        // 取消预约
        cancel() {
            this.reservedShow = false
            let params = {
                appointment_num: this.appointment_num,
                // workroom_id: this.workroom_id,
                // visit_at: this.reservedDate
            }
            cancelAppointment(params).then(res => {
                if (res.status != 0) {
                    this.errM(res.msg)
                } else {
                    // this.save()
                }
            })
            //this.$refs.secondinput.focus()
        },
        onInput(value) {
            this.dateInfo[this.diffType] = `${this.dateInfo[this.diffType]}${value}`
            if (this.dateInfo[this.diffType].length > 1) {
                if (this.diffType == 'years') {
                    if (this.dateInfo[this.diffType] > 99) {
                        this.dateInfo[this.diffType] = 99
                    }
                    this.$refs.secondinput.focus()
                } else if (this.diffType == 'months') {
                    if (this.dateInfo[this.diffType] > 12) {
                        this.dateInfo[this.diffType] = 12
                    } else if (this.dateInfo[this.diffType] < 10 && this.dateInfo[this.diffType] > 1) {
                        this.dateInfo[this.diffType] = (Array(2).join('0') + this.dateInfo[this.diffType]).slice(-2)
                    } else if (this.dateInfo[this.diffType] < 1) {
                        this.dateInfo[this.diffType] = 1
                        this.dateInfo[this.diffType] = (Array(2).join('0') + this.dateInfo[this.diffType]).slice(-2)
                    }
                    this.$refs.thirdinput.focus()
                } else {
                    if (this.dateInfo[this.diffType] > 31) {
                        this.dateInfo[this.diffType] = 31
                    } else if (this.dateInfo[this.diffType] < 10 && this.dateInfo[this.diffType] > 1) {
                        this.dateInfo[this.diffType] = (Array(2).join('0') + this.dateInfo[this.diffType]).slice(-2)
                    } else if (this.dateInfo[this.diffType] < 1) {
                        this.dateInfo[this.diffType] = 1
                        this.dateInfo[this.diffType] = (Array(2).join('0') + this.dateInfo[this.diffType]).slice(-2)
                    }
                }
            }
        },
        onYears(e) {
            this.dateInfo.years = this.dateInfo.years.replace(/[^0-9]/g, ''); // 只允许数字
            if (e.target.value && e.target.value.length == 2 || e.target.value.length == 3) {
                if (Number(e.target.value) > 99) {
                    this.dateInfo.years = 99
                }

                this.$refs.secondinput.focus()
            }
        },
        onMonths(e) {
            this.dateInfo.months = this.dateInfo.months.replace(/[^0-9]/g, ''); // 只允许数字
            if (e.target.value && e.target.value.length == 2 || e.target.value.length == 3) {
                if (Number(e.target.value) > 12) {
                    this.dateInfo.months = 12
                }

                this.$refs.thirdinput.focus()
            }
        },
        onDays(e) {
            this.dateInfo.days = this.dateInfo.days.replace(/[^0-9]/g, ''); // 只允许数字
            if (e.target.value && e.target.value.length == 2 || e.target.value.length == 3) {
                if (Number(e.target.value) > 31) {
                    this.dateInfo.days = 31
                }

                this.$refs.thirdinput.blur()
                this.keyShow = false
            }
        },
        onDelete() {
            const value = this.dateInfo[this.diffType].toString();
            if (Number(value.slice(0, value.length - 1)) === 0) {
                this.dateInfo[this.diffType] = '';
            } else {
                this.dateInfo[this.diffType] = Number(value.slice(0, value.length - 1));
            }
        },

        firstFocus() {
            this.keyShow = true
            this.diffType = 'years'
        },
        firstBlur() {
            if (this.dateInfo.years == 0) {
                this.dateInfo.years = new Date().getFullYear().toString().slice(2, 4)
            }
            // else if (this.dateInfo.years > 99) {
            //     this.dateInfo.years = 99
            // }
            this.dateInfo.years = this.dateInfo.years.toString().padStart(2, '0')
            // this.$refs.secondinput.focus()
            if (this.changeData.years != this.dateInfo.years) {
                this.addTime('btn4');
            }
        },
        secondFocus() {
            this.keyShow = true
            this.diffType = 'months'
        },
        secondBlur() {
            if (this.dateInfo.months == 0) {
                this.dateInfo.months = 1
            }
            // else if (this.dateInfo.months > 12) {
            //     this.dateInfo.months = 12
            // }
            this.dateInfo.months = (Array(2).join('0') + this.dateInfo.months).slice(-2)
            // this.$refs.thirdinput.focus()

            if (this.changeData.months != this.dateInfo.months) {
                this.addTime('btn4');
            }

        },
        thirdFocus() {
            this.keyShow = true
            this.diffType = 'days'
        },
        thirdBlur() {
            if (this.dateInfo.days == 0) {
                this.dateInfo.days = 1
            }
            //  else if (this.dateInfo.days > 31) {
            //     this.dateInfo.days = 31
            // }
            this.dateInfo.days = (Array(2).join('0') + this.dateInfo.days).slice(-2)

            if (this.changeData.days != this.dateInfo.days) {
                this.addTime('btn4');
            }
        },
        // 设置提醒
        errM(msg) {
            Dialog.alert({
                title: '温馨提示',
                message: msg,
                width: '312px',
                messageAlign: 'left',
                confirmButtonColor: '#3388FF',
                confirmButtonText: '确定'
            }).then(() => {
                // on close
            });
        },
        // 返回首页
        handleConfirm() {
            this.noDoubleTap(() => {
                wx.miniProgram.switchTab({
                    url: `/pages/main/home/<USER>
                })
            })
        },

        addTime(type) {
            this.activeButton = type;
            if (type == 'btn1') {
                this.addTimeByDays(30);
            } else if (type == 'btn2') {
                this.addTimeByDays(90);
            } else if (type == 'btn3') {
                this.addTimeByDays(180);
            } else {
            }
        },
        buttonColor(value) {
            return this.activeButton !== value
                ? 'linear-gradient(180deg, #FFDA9D, #FFEDC9)'
                : 'linear-gradient(to bottom, #FF8A01, #FFCD69)';
        },
        addTimeByDays(days) {
            const newDate = moment().add(days, 'days');
            this.changeData.years = this.dateInfo.years = newDate.year().toString().slice(2, 4);
            this.changeData.months = this.dateInfo.months = (newDate.month() + 1).toString().padStart(2, '0'); // 月份从 0 开始
            this.changeData.days = this.dateInfo.days = newDate.date().toString();
        }
    },
    watch: {}
};
</script>
<style lang="scss">
.medicationReminder {
    background: linear-gradient(to bottom, rgba(255, 188, 82, 1), rgba(255, 227, 126, 0));
    width: 100%;
    min-height: 100vh;
    padding-bottom: 20%;

    .header {
        width: 375px;
        height: 290px;
        background-image: url('../imgPage/settingHeader.png');
        background-repeat: no-repeat;
        background-size: contain;
    }

    .times {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 375px;
        height: 371px;
        margin-top: -101px;
        background-image: url('../imgPage/times.png');
        background-repeat: no-repeat;
        background-size: 375px auto;


        .btnsT {
            width: 320px;
            height: 120px;
            margin-top: 81px;
            flex-wrap: wrap;
            display: flex;
            column-gap: 16px;
            justify-content: space-around;
            align-items: center;

            .btn_time {
                border-radius: 9px;
                width: 150px;
                height: 52px;

                .btnT_span {
                    color: #3A1504;
                    line-height: 28px;
                    font-size: 20px;
                    font-family: pingfangsc;
                    font-weight: 500;
                }
            }

            .active {
                background-color: linear-gradient(to bottom, #FF8A01, #FFCD69);
            }
        }

        .settingT {
            background-color: white;
            margin-top: 11px;
            display: flex;
            justify-content: center;

            .tim {
                width: 76px;
                height: 94px;
                background-image: url('../imgPage/timesB.png');
                background-repeat: no-repeat;
                background-size: 76px 102px;
                color: #3A1504;
                text-align: center;
                font-family: "PingFang SC";
                font-size: 30px;
                font-style: normal;
                font-weight: 500;
                line-height: normal;
            }

            span {
                color: #3A1504;
                text-align: center;
                font-family: "PingFang SC";
                font-size: 24px;
                font-style: normal;
                font-weight: 500;
                line-height: normal;
                margin-top: 28px;
            }
        }

        .timesTips {
            color: #BC976C;
            font-family: "PingFang SC";
            font-size: 13px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
            margin-top: 14px;
        }
    }





    .footer {
        margin-top: 12px;
        padding: 59px 15px 0;
        width: 345px;
        background-image: url('../imgPage/footer.png');
        background-repeat: no-repeat;
        background-size: 375px auto;

        .footer_box {
            width: 315px;
            padding: 0 15px;
            padding-top: 12px;
            background-color: #FFF;

            .footer_content {
                width: 285px;
                border-radius: 8px;
                padding: 15px;
                margin-bottom: 10px;
                background: linear-gradient(180deg, #FFEED2 0%, #FFFAF2 100%);
                text-align: left;

                .title {
                    color: #2A0F03;
                    font-family: "PingFang SC";
                    font-size: 17px;
                    font-style: normal;
                    font-weight: 500;
                    line-height: normal;
                }

                .text {
                    margin-top: 6px;
                    color: #727272;
                    text-align: justify;
                    font-family: "PingFang SC";
                    font-size: 14px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 22px;
                    white-space: pre-wrap;
                }
            }
        }

    }

    .btn {
        padding: 8.5px 15px 0;
        position: fixed;
        bottom: 0px;
        width: 100vw;
        height: 8%;
        flex-shrink: 0;
        background: rgba(255, 255, 255, 0.90);
        backdrop-filter: blur(2px);
    }

    .btn_span {
        color: #FFF;
        text-align: center;
        font-family: "PingFang SC";
        font-size: 20px;
        font-style: normal;
        font-weight: 600;
        line-height: 140%;
    }

    .van-dialog--round-button .van-dialog__confirm {
        height: 49px !important;
    }

    .successTitles {
        color: #0A0A0A;
        text-align: center;
        font-family: "PingFang SC";
        font-size: 17px;
        font-style: normal;
        font-weight: 500;
        line-height: normal;
        margin: 12px 0 8px;
    }

    .successText {
        width: 256px;
        color: #0A0A0A;
        text-align: center;
        font-family: "PingFang SC";
        font-size: 17px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        padding: 0 29.5px;
        margin-bottom: 20px;
    }

    .van-button--normal {
        font-size: 18px;
    }

    .selectTips {
        color: #0A0A0A;
        text-align: center;
        font-family: "PingFang SC";
        font-size: 17px;
        font-style: normal;
        font-weight: 500;
        line-height: normal;
        margin: 28px 0 8px;
    }

    .selectText {
        color: #0A0A0A;
        font-family: "PingFang SC";
        font-size: 17px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        margin-bottom: 20px;
        padding: 0 30px;
        text-align: left;
    }
}
</style>