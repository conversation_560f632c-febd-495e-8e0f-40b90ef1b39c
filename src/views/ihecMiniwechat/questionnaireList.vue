<template>
  <div class="questionnaireList">
    <van-tabs class="tabOuter" @click="changeTab" title-active-color="#0A0A0A" color="#398CFF" background="rgb(243,245,249)" swipe-threshold="2">
      <van-tab v-for="item in tabArr" title-class="tabTitle" title-active-color="#0A0A0A">
        <template #title>
          <div class="tabItem">
            <img class="tabIcon" :src="item.business_logo" alt="" />
            <div class="textCut tabTitleText">{{ item.business_name }}</div>
          </div>
        </template>
      </van-tab>
    </van-tabs>
    <div class="list" id="iframeOuter" v-if="tabArr.length > 0 && tabArr[curTabIndex].business_tag == 'mmc'">
      <!-- <iframe class="iframe" ref="iframe" allowfullscreen="true" width="100%" height="100%" :src="mmcQuestionnaireUrl" frameborder="0"></iframe> -->
      <div class="item" v-for="(item,index) in visitList" :key="index">
        <span class="cycleText">{{ $t('mmcQuestionH5.manageTimeName') }}: {{item.visit_start_date }} ~ {{ item.visit_end_date }}</span>
        <div class="box">
          <div class="interview-info">
            <div class="interview-title">
              <span class="title">第{{ item.visit_level }}{{ $t('mmcQuestionH5.interviewName') }}</span>
              <span class="time">{{ item.visit_at }}</span>
            </div>
            <van-button plain class="btn" type="info" @click="goTo(item)">{{ $t('mmcQuestionH5.fillOutName') }}</van-button>
          </div>
          <div class="interview-cart">
            <div class="phase" v-for="(k, Kindex) in item.qss" :key="Kindex" @click="goToItems(item,k)">
              <span>{{k.name}}</span>
              <span>{{k.percent }}%</span>
              <span v-if="k.percent != 100" class="red"></span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="list" v-else>
      <div class="item" v-for="(item,index) in questionnaireList" :key="index">
        <span class="cycleText">管理周期：{{item.visit_start_time}} ~ {{item.visit_end_time}}</span>
        <div class="itemMain" v-for="(childItem,index) in item.child" :key="childItem.survey_id"  :class="[childItem.is_editable?'canEdit':'noEdit']">
          <div class="top">
            <div class="left">
              <p class="subTitle textCut">{{childItem.survey_name}}</p>
              <p class="time">{{childItem.updated_at}}</p>
            </div>
            <div class="button" @click="goSaas(childItem)" v-if="childItem.is_editable">去填写</div>
            <div class="button" @click="goSaas(childItem)" v-if="!childItem.is_editable && childItem.is_readable">去查看</div>
          </div>
          <div class="outLineBtn">
            <div class="btnItem" v-for="(outlineItem,outlineIndex) in childItem.outline" :key="outlineItem.id">
              <span class="label textCut" @click="goSaas(childItem,outlineIndex)">{{outlineItem.title}}</span>
              <!-- <span>66%</span> 暂时没有进度-->
            </div>
          </div>
        </div>
      </div>
    </div>
    <van-loading class="loading" v-if="isLoadingShow" size="50px" vertical color="#3388FF">加载中</van-loading>
    <!-- loading消失 && ((saas项目&&无问卷数据) || 无tab项目) 时，显示无数据 -->
    <div class="noData" v-if="!isLoadingShow && ((tabArr.length > 0 && tabArr[curTabIndex].business_tag != 'mmc' && !questionnaireList.length) || tabArr.length == 0)">
      <img class="img" src="../common/images/empty.png" alt="">
      <p>暂无数据</p>
    </div>
    <!-- mmc项目&&无mmc项目url -->
    <div class="noData" v-if="!isLoadingShow && tabArr && tabArr.length > 0 && tabArr[curTabIndex].business_tag == 'mmc' && visitList.length == 0">
      <img class="img" src="../mmc-question-h5/images/unfind-bg.png" alt="">
      <p>{{$t('mmcQuestionH5.noMMCQuestionnaire')}}</p>
      <p>{{$t('mmcQuestionH5.joinMMC')}}</p>
    </div>
  </div>
</template>

<script>
import { getQuestionnairePros,getQuestionnaireList,getQuestionnaireUrl } from "@/api/ihecMiniwechat";
import {api} from '@/api/common/mmcQuestion.js';
import { Toast, Locale } from 'vant'
import { getUrlParams } from '@/utils/utils.js'
import language from '../../i18n/index'
export default {
  data() {
    return {
      curTabIndex: 0,
      tabArr: [],
      questionnaireList: [],
      curWorkroomPro: {},  //当前页签项目相关数据  默认第一个页签
      mmcQuestionnaireUrl: '',  //mmc项目问卷url
      isLoadingShow: false,
      langType: '',
      token: '',
      workroomId: '',
      visitList: [],
      user_id: null
    };
  },
  methods: {
    init() {
      let url = window.location.href
      let localLangType = localStorage.getItem('lang')
      let langType = getUrlParams(url,'langType') || localLangType || 'zh_CN'
      let workroomId = getUrlParams(url,'workroomId')
      if(workroomId){
        this.workroomId = workroomId
        localStorage.setItem('questionnaireListWorkroomId',this.workroomId)
      }else{
        let questionnaireListWorkroomId = localStorage.getItem('questionnaireListWorkroomId')
        this.workroomId = questionnaireListWorkroomId
      }
      let token = getUrlParams(url,'token')
      if(token){
        localStorage.setItem('authorization',token)
        let url = `${this.$route.path}?workroomId=${this.workroomId}&refresh=true`
        this.$router.replace(url)
      }
      this.initLocale(langType)
      this.getTabList()
    },
    // 获取tab签数据
    getTabList() {
      this.isLoadingShow = true
      // let url = window.location.href
      // this.langType = getUrlParams(url,'langType') || 'zh_CN'
      // let token = getUrlParams(url,'token')
      // let workroomId = getUrlParams(url,'workroomId')  //小程序带来的workroomid
      // if(token){
      //   localStorage.setItem('authorization',token)
      //   let url = `${this.$route.path}?workroomId=${workroomId}&refresh=true`
      //   this.$router.replace(url)
      // }
      getQuestionnairePros(this.workroomId).then((res) => {
        if (res.status == 0) {
          this.tabArr = res.data.workrooms  //tab页签数据
          this.curWorkroomPro = res.data.workrooms[0]  //保存当前tab签的相关数据 用于判断当前页签的项目类型 mmc/saas  默认第一个tab
          this.getQuestionnaireList(this.curWorkroomPro.workroom_id)  //拿到第一个tab的workroom_id 请求问卷数据
          // this.isLoadingShow = false
        }
      });
    },
    // 获取问卷数据 init默认第一个页签的数据  切换tab调佣
    getQuestionnaireList(workroom_id){
      this.isLoadingShow = true
      getQuestionnaireList(workroom_id).then(res=>{
        if(res.status == 0){
          // this.curWorkroomPro.business_tag == 'mmc'?this.mmcQuestionnaireUrl = res.data.mmc_survey_url:this.questionnaireList = res.data.survey_list
          this.$nextTick(()=>{
            if(this.curWorkroomPro.business_tag == 'mmc'){
              this.mmcQuestionnaireUrl = res.data.mmc_survey_url  //此接口返回的url已无用，只取其token，url自己拼接
              let access_token = getUrlParams(this.mmcQuestionnaireUrl,'ihec_patient_token')
              localStorage.setItem('access-token',access_token)  //mmc问卷改造后 token使用access_token
              this.getVisitList()
              // let iframeOuter = document.getElementById('iframeOuter')
              // let nodeArr = iframeOuter.childNodes;
              // if(nodeArr.length > 0){
              //   for (var i = nodeArr.length - 1; i >= 0; i--) { // 一定要倒序，正序是删不干净的，可自行尝试
              //     iframeOuter.removeChild(nodeArr[i]);
              //   }
              // }
              // let iframe = document.createElement('iframe')
              // iframe.setAttribute("width", "100%")
              // iframe.setAttribute("height", "100%")
              // iframe.id="iframe"
              // iframe.src = res.data.mmc_survey_url   //此接口返回的url已无用，只取其token，url自己拼接
              // iframe.src = `${process.env.BASE_URL}mmc/question/index?langType=${this.langType}`
              // iframeOuter.appendChild(iframe)

            }else{
              this.questionnaireList = res.data.survey_list
              this.isLoadingShow = false
            }
          })
        }
      })
    },
    // 切换页签
    changeTab(e) {
      if(this.curTabIndex == e){
        return
      }
      this.curTabIndex = e;
      this.curWorkroomPro = this.tabArr[e]
      this.questionnaireList = []  //清空saas问卷列表
      this.visitList = []  //清空mmc问卷列表
      this.getQuestionnaireList(this.curWorkroomPro.workroom_id)
      // let iframe = document.getElementById('iframe')
      // iframe?iframe.remove():''
    },
    // 跳转saas问卷
    goSaas(childItem,outlineIndex = false){
      this.isLoadingShow = true
      let obj = {
        workroom_id: this.curWorkroomPro.workroom_id,
        survey_id: childItem.survey_id,
        level_task_id: childItem.level_task_id,
        manage_history_id: childItem.manage_history_id,
        patient_id: childItem.patient_id,
        is_readable: childItem.is_readable,
        is_editable: childItem.is_editable?1:0,
      }
      getQuestionnaireUrl(obj).then(res=>{
        if(res.status == 0){
          let url = res.data.survey_url
          if(outlineIndex !== false){
            // url = res.data.survey_url + '&outlineId=' + outlineItem.id
            url = `${res.data.survey_url}&outlineIndex=${outlineIndex}`
          }
          window.location.href = url
          this.isLoadingShow = false
          // console.log(url)
        }
      })
    },
    getVisitList() {
      this.isLoadingShow = true
      api.visitQuestionList({
        language: this.langType == 'zh_HK' ? 'simple_tradition':'' 
      }).then((res) => {
        if(res.status == 0){
          this.visitList = res.data.list;
          this.user_id = res.data.user_id
        }
        this.isLoadingShow = false
      }).catch((err) => {
        Toast({
          message: `${err}`,
          type: 'html',
          forbidClick: true,
          duration: 1000
        })
        this.isLoadingShow = false
      })
    },
    initLocale(langType){
      const messages = language._vm.messages
      let lang = ''
      switch (langType) {
        case 'zh_CN':
            lang = 'zh_CN'
          break;
        case 'zh_HK':
          lang = 'zh_HK'
          break;
        case 'zh_TW':
          lang = 'zh_HK'
          break;
        default:
          lang = 'zh_CN'
          break;
      }
      this.langType = lang
      this.$i18n.locale = lang
      Locale.use(lang, messages[lang])
      localStorage.setItem('lang', lang)
    },
    goTo(item) {
      if (item.visit_level == 1) {
        this.$router.push({
          path: '/mmc/question/userInfo',
          query: {
            user_id: this.user_id,
            visit_level:item.visit_level,
            group: item.qss[0].qss_type,
            groupName: item.qss[0].name
          },
        })
      } else {
        this.$router.push({
          path: '/mmc/question/survey',
          query: {
            user_id: this.user_id,
            visit_level: item.visit_level,
            group: item.qss[0].qss_type,
            groupName: item.qss[0].name
          },
        })
      }
    },
    goToItems(item, k) {
      if (item.visit_level == 1 && k.qss_type == "basic_info") {
        this.$router.push({
          path: '/mmc/question/userInfo',
          query: {
            user_id: this.user_id,
            visit_level: item.visit_level,
            group: k.qss_type,
            groupName: k.name
          },
        })
      } else {
        this.$router.push({
          path: '/mmc/question/survey',
          query: {
            user_id: this.user_id,
            visit_level: item.visit_level,
            group: k.qss_type,
            groupName: k.name
          },
        })
      }
    },
  },
  mounted() {
    this.init();
  },
};
</script>

<style lang="scss">
.questionnaireList {
  width: 100%;
  min-height: 100vh;
  background: rgb(243, 245, 249);
  .iframeOuter {
    width: 100%;
    height: calc(100% - 80px);
    position: absolute;
    // top: 65px;
    // left: 0;
  }
  .tabOuter {
    padding: 5px 0 20px;
    .tabTitle {
      font-size: 17px;
      color: #878f99;
      line-height: normal;
    }
    .tabItem {
      display: flex;
      align-items: center;
      font-weight: inherit;
    }
    .tabIcon {
      width: 18px;
      height: 18px;
      margin-right: 2px;
    }
    .van-tab--active .tabTitleText {
      font-weight: 500 !important;
    }
  }
  .canEdit{
    .btnItem {
      color: #5a6266;
    }
    .subTitle {
      color: #0a0a0a;
    }
    .time {
      color: #878f99;
      font-size: 15px;
      margin-top: 5px;
    }
    .button {
      color: #3388FF;
      border: 1px solid #3388FF;
    }
  }
  .noEdit{
    .btnItem {
      color: #878F99;
    }
    .subTitle {
      color: #5A6266;
    }
    .time {
      color: #C0C6CC;
    }
    .button {
      color: #5A6266;
      border: 1px solid #E5E5E5;
    }
  }
  .list {
    padding: 0 15px;
    .item:nth-of-type(1){
      margin-top: 0;
    }
    .item {
      margin-top: 16px;
      .cycleText {
        color: #878f99;
        font-size: 15px;
        text-align: left;
        display: block;
      }
      .itemMain {
        width: 100%;
        border-radius: 8px;
        // display: flex;
        background: white;
        padding: 15px 14px;
        box-sizing: border-box;
        margin-top: 10px;
        .top {
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          .left {
            display: flex;
            flex-direction: column;
            text-align: left;
            flex: 1;
            .subTitle {
              font-size: 17px;
              width: 90%;
            }
            .time {
              font-size: 15px;
              margin-top: 5px;
            }
          }
          .button {
            width: 66px;
            height: 28px;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 4px;
            font-size: 14px;
          }
        }
        .outLineBtn {
          margin-top: 16px;
          display: flex;
          flex-wrap: wrap;
          justify-content: space-between;
          .btnItem {
            display: flex;
            justify-content: space-between;
            text-align: left;
            font-weight: 500;
            font-size: 16px;
            border: 1px solid #e5e5e5;
            border-radius: 4px;
            padding: 8px 14px;
            box-sizing: border-box;
            margin: 8px 0;
            width: 45%;
            .label {
              display: inline-block;
              width: 100%;
            }
          }
        }
      }
    }
  }
  .noData{
    padding-top: 30%;
    .img{
      width: 84px;
      height: 84px;
    }
    p {
      font-size: 17px;
      color: #AAAAAA;
      font-weight: 400;
      margin-top: 20px;
    }  
  }
  .textCut {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: normal;
  }
  .loading{
    position: fixed;
    left: 50%;
    top: 30%;
    transform: translate(-50%, 0);
  }

  .manage-time {
    font-size: 15px;
    font-weight: 500;
    color: #878F99;
    text-align: left;
  }

  .box {
    background-color: #fff;
    border-radius: 8px;
    padding: 18px 10px;
    margin-top: 12px;
    margin-bottom: 20px;
    .interview-info {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .interview-title {
        display: flex;
        flex-direction: column;

        .title {
          color: #0A0A0A;
          font-weight: 400;
          font-size: 16px;
        }

        .time {
          color: #878F99;
          font-size: 16px;
          margin-top: 10px;
          text-align: left;
          font-weight: 400;
        }
      }

      .btn {
        width: 66px;
        height: 28px;
        border: 1px solid #3388FF;
        border-radius: 4px;
        display: flex;
        padding: 0px;
        align-items: center;
        justify-content: center;
        color: #3388FF;
      }
    }

    .interview-cart {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;

      .phase {
        width: 47%;
        border: 1px solid #E5E5E5;
        border-radius: 4px;
        background-clip: border-box;
        background: #fff;
        height: 42px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 14px;
        position: relative;

        span:nth-child(1) {
          font-weight: 500;
          color: #5A6266;
          margin-left: 8px;
          font-size: 16px;
        }

        span:nth-child(2) {
          color: #878F99;
          font-size: 14px;
          font-weight: 500;
          margin-right: 5px;
        }

        .red {
          position: absolute;
          width: 10px;
          height: 10px;
          border-radius: 50%;
          background: red;
          top: 0;
          right: -1px;
          z-index: 50;
        }
      }
    }
  }
}
</style>