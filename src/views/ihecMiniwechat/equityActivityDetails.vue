<template>
  <div class="equityActivityDetails">
    <div class="equityHeaderImg" v-if="pageConfig.top_img">
      <img :src="pageConfig.top_img" alt="">
    </div>
    <div class="top">
      <div class="header">
        <div class="left">权益1：{{ equityData.equity_name }}</div>
        <div class="right" @click="() => this.equityStatementShow = true" v-if="equityData.equity_desc">
          权益说明
        </div>
      </div>
      <div class="main">
        {{ equityData.equity_brief }}
      </div>
      <div class="ticket" v-for="(item, index) in equityData.coupon_list" :key="index">
        <div class="box" :style="{ 'background-image': 'url(' + item.coupon_bg_img + ')' }">
          <div class="time">{{ item.expire_start ? `${item.expire_start} ~ ${item.expire_end}` : null }}</div>
          <div class="useBtn" @click="getMapList" v-if="item.status == '10'">{{ equityStatusList[item.status] }}</div>
          <div class="unUsedBtn" v-else>{{ equityStatusList[item.status] }}</div>
        </div>
      </div>
      <div class="toUsed">
        <div @click="getMapList" v-if="equityData.coupon_list.length != 0">去药店使用</div>
      </div>
    </div>
    <div class="footer" v-for="item in pageConfig.benefit" :key="item.name">
      <div class="footer_tips">
        {{ item.name }}
      </div>
      <div class="footer_title">
        {{ item.brief }}
      </div>
      <div class="footer_img" v-if="item.img">
        <img :src="item.img" alt="">
      </div>
    </div>
    <div class="tips">
      <div>
        本活动中的购药福利部分，给予的优惠方式与幅度的具体解释权归活动药店所有。
      </div>
    </div>
    <van-dialog v-model="dialogShow" theme='round-button' confirm-button-text="知道啦" confirm-button-color="#FFFFFF"
      @confirm="handleCancel('dialog')">
      <div class="dialog_img">
        <img src="../imgPage/dialog_flower.png" />
      </div>
      <div class="dialog_main">
        <div class="successTitles">恭喜您已领取</div>
        <div class="successText">{{ dialogData.activity_name }}</div>
      </div>
    </van-dialog>
    <van-dialog v-model="dialogUnShow" theme='round-button' confirm-button-text="知道啦" confirm-button-color="#FFFFFF"
      @confirm="handleCancel('dialog_error')">
      <div class="dialog_error_img">
        <img src="../imgPage/errorImg.png" />
      </div>
      <div class="dialog_error_main">
        <div class="errorTitles">{{ this.$route.query.tips }}
        </div>
      </div>
    </van-dialog>
    <van-dialog class="equityStatement" v-model="equityStatementShow" theme='round-button' confirm-button-text="知道啦"
      confirm-button-color="#FFFFFF" @confirm="handleCancel('equityStatement')">
      <div class="equityStatement_img">
        <div class="title">权益说明</div>
        <div class="cancel" @click="handleCancel('equityStatement')">
          <img src="../imgPage/CloseOutline.png" alt="">
        </div>
      </div>
      <div class="equityStatement_main">
        <div v-html="equityData.equity_desc"></div>
      </div>

    </van-dialog>
    <van-popup v-model="popupShow" round position="bottom">
      <div class="popup_title">选择附近药店使用</div>
      <div class="popup_box">
        <div class="popup_address" v-for="(item, index) in addressList" :key="index">
          <div class="address_detail">
            <div class="hosp_img" :style="{ 'background-image': 'url(' + item.logo + ')' }"></div>
            <div class="hosp_detail">
              <div class="hosp_name">
                <div>{{ item.name }}</div>
              </div>
              <div class="hosp_address">{{ `${item.province_name}${item.city_name}${item.district_name}${item.addr}` }}
              </div>
            </div>
            <div class="hosp_phone">
              <div v-if="item.tel != '-' && item.tel != ''">
                <a class="icon" :href="`tel:${item.tel}`"></a>
                <div class="phone">电话</div>
              </div>
            </div>
            <div class="hosp_distance">
              <div class="icon" @click="navigateLine(item)"></div>
              <div class="km">{{ item.distance == 99999 ? null : `${item.distance}km` }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="popup_cancel" @click="handleCancel('popup')">取消</div>
    </van-popup>
    <van-loading v-if="isLoadingShow" class="loading" size="50" color="#ECB45F" />
  </div>
</template>
<script>
import { activityUserJoin, getBenefitActivityDetail, getActivityStoreList } from '@/api/ihecMiniwechat.js'
import wx from "weixin-js-sdk"
import axios from 'axios'
export default {
  data() {
    return {
      pageConfig: {},
      dialogShow: false,
      dialogUnShow: false,
      popupShow: false,
      equityStatementShow: false,
      equityData: {
        coupon_list: []
      },
      addressList: [],
      equityStatusList: {
        '10': '可使用',
        '20': '已使用',
        '30': '已过期',
        '40': '已取消',
        '50': '待解锁'
      },
      dialogData: {},
      patientId: '',
      disabled: false,
      originUrl: '',
      isLoadingShow: false
    };
  },
  created() {
  },
  mounted() {
    try {
      gio('track', 'enterBenefitDetails', {
        patient_id: this.patientId,
        doc_id: res.data.doc_id,
        benefit_activity_id: res.data.activity_id
      });
    } catch (error) {
      console.log('进入权益详情页埋点失败')
    }
    gio('track', 'benefitToUse', {
      patient_id: this.patientId,
      doc_id: this.equityData.doc_id,
      benefit_activity_id: this.equityData.activity_id
    });
    this.init()
    this.registerWxApi()
  },
  methods: {
    init() {
      if (this.$route.query.token) {
        this.token = this.$route.query.token
        localStorage.setItem('authorization', this.token)
      }
      this.patientId = this.$route.query.userId
      if (this.$route.query.from == 'myequityActivity') {
        let params = {}
        params.user_join_id = this.$route.query.user_join_id
        this.getActivityDetil(params)
        return
      }
      if (this.$route.query.tips) {
        this.dialogUnShow = true
        let params = {}
        params.user_join_id = this.$route.query.userJoinId
        this.getActivityDetil(params)
      } else {
        // 查看详情
        if (this.$route.query.userJoinId) {
          let params = {}
          params.user_join_id = this.$route.query.userJoinId
          this.getActivityDetil(params)
        } else {
          activityUserJoin({ workroom_opened_id: this.$route.query.objectId }).then(res => {
            if (res.status == 600001) {
              this.$toast(res.msg)
              let params = {}
              params.user_join_id = this.$route.query.userJoinId
              this.getActivityDetil(params)
            } else if (res.status == 0) {
              this.dialogShow = true
              this.dialogData = res.data
              let params = {}
              params.user_join_id = res.data.user_join_id
              this.getActivityDetil(params)
            }
          })
        }
      }
    },
    // 注册wxapi
    registerWxApi() {
      // let url = `${window.location.href}&event_source=card_share`
      let url = this.originUrl = window.location.href.split('#')[0]
      let obj = {
        url,
        account: 'ihec'
      }
      // alert(url)
      axios.post('https://patient-api.zz-med-test.com/api/v1/wechat/js_api/sign', obj).then(res => {
        // getWxConfig(obj).then(res=>{
        if (res.data.status == 0) {
          let { appId, timestamp, nonceStr, signature } = res.data.data
          wx.config({
            debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
            appId: appId, // 必填，公众号的唯一标识
            timestamp: timestamp, // 必填，生成签名的时间戳
            nonceStr: nonceStr, // 必填，生成签名的随机串
            signature: signature, // 必填，签名
            jsApiList: [
              "getLocation", "openLocation"
            ],
          })
        }
      })
    },
    // 获取详情
    getActivityDetil(params) {
      getBenefitActivityDetail(params).then(res => {
        if (res.status == 0) {
          try {
            gio('track', 'enterBenefitDetails', {
              patient_id: this.patientId,
              doc_id: res.data.doc_id,
              benefit_activity_id: res.data.activity_id
            });
          } catch (error) {
            console.log('进入权益详情页埋点失败')
          }
          this.equityData = res.data
          this.pageConfig = res.data.page_config
        }
      })
    },
    getMapList() {
      let canUse = this.equityData.coupon_list.some(item => item.status == 10)
      if(!canUse){
        this.$toast('暂无可用权益')
        return
      }
      this.isLoadingShow = true
      // alert(window.location.href.split('#')[0])
      window.history.replaceState(null, null, this.originUrl)
      // alert(window.location.href.split('#')[0])
      const that = this
      wx.ready(function () {   //需在用户可能点击分享按钮前就先调用
        wx.getLocation({
          type: 'wgs84',
          success(res) {
            const latitude = res.latitude;
            const longitude = res.longitude;
            that.getDrugStoreList(longitude, latitude)
          }, fail(res) {
            // that.$toast('当前位置定位信号弱')
            // that.getDrugStoreList()
          }
        });
      })
      wx.error(function () {
        that.getDrugStoreList()
      })
      try {
        gio('track', 'benefitToUse', {
          patient_id: that.patientId,
          doc_id: that.equityData.doc_id,
          benefit_activity_id: that.equityData.activity_id
        });
      } catch (error) {
        console.log('点击去使用埋点失败')
      }
    },
    getDrugStoreList(longitude, latitude) {
      let that = this
      const params = {}
      let obj = that.equityData.coupon_list.find(item => item.status == 10)
      params.coupon_user_id = obj.coupon_user_id
      params.doc_id = that.equityData.doc_id
      if (longitude && latitude) {
        params.longitude = longitude
        params.latitude = latitude
      }
      getActivityStoreList(params).then(res => {
        if (res.status == 0) {
          that.isLoadingShow = false
          that.addressList = res.data.list
          if (that.addressList.length == 0) {
            that.$toast('无法为您匹配到符合条件的药店，请联系客服进行处理，谢谢')
          } else {
            if (res.data.list.length == 1 && res.data.list[0].used_status == 1) {
              that.navigateLine(res.data.list[0])
            } else {
              that.popupShow = true
            }
          }
        }else{
          that.isLoadingShow = false
          that.$toast(res.msg)
        }
      })
    },
    // 打开微信地图
    navigateLine(item) {
      wx.ready(function () {   //需在用户可能点击分享按钮前就先调用
        wx.openLocation({
          latitude: Number(item.latitude),
          longitude: Number(item.longitude),
          name: item.name, // 位置名
          address: `${item.province_name}${item.city_name}${item.district_name}${item.addr}`
        })
      })
      try {
        gio('track', 'benefitToDrugstore', {
          patient_id: this.patientId,
          doc_id: this.equityData.doc_id,
          benefit_activity_id: this.equityData.activity_id,
          drugstore_id: item.id
        });
      } catch (error) {
        console.log('点击药店打开地图埋点失败')
      }
    },
    // makePhoneCall
    handlePhoneCall(num) {
      setTimeout(() => {
        this.disabled = false
      }, 2000);
      if (!this.disabled) {
        window.location.href = `tel:${item.tel}`
      }
      this.disabled = true
    },
    // 关闭弹窗
    handleCancel(type) {
      if (type == 'popup') {
        this.popupShow = false
      } else if (type == 'dialog') {
        this.dialogShow = false
      } else if (type == 'equityStatement') {
        this.equityStatementShow = false
      } else if (type == 'dialog_error') {
        this.dialogUnShow = false
      }
    },
  },
  watch: {}
};
</script>
<style lang="scss">
.equityActivityDetails {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(190deg, #FFEAC9 7.34%, rgba(255, 233, 208, 0.00) 100%);
  // position: relative;
  // padding-top: 138px;
  padding: 20px 16px;
  box-sizing: border-box;
  .equityHeaderImg {
    width: 100%;
    height: 120px;
    img{
      width: 100%;
      height: 100%;
    }
    // width: 345px;
    // height: 118px;
    // background-image: url('../imgPage/equityHeader.png');
    // background-repeat: no-repeat;
    // background-size: contain;
    // position: absolute;
    // top: 20px;
    // left: 27px;
  }

  .top {
    width: 100%;
    border-radius: 8px;
    background: #FFF;
    padding: 16px;
    box-sizing: border-box;
    // width: 315px;
    // min-height: 256px;
    // border-radius: 8px;
    // background: #FFF;
    // margin-left: 15px;
    // padding: 0 15px;

    .header {
      // height: 52px;
      // flex-shrink: 0;
      // fill: #FFF;
      display: flex;
      justify-content: space-between;

      .left {
        // margin: 16px 50px 12px 0;
        color: #0A0A0A;
        font-family: "PingFang SC";
        font-size: 17px;
        font-style: normal;
        font-weight: 500;
        line-height: normal;
      }

      .right {
        color: #DD9F40;
        font-family: "PingFang SC";
        font-size: 15px;
        font-style: normal;
        font-weight: 500;
        line-height: normal;
        // margin-top: 16px;
      }
    }

    .main {
      margin: 10px 0;
      color: #3F4447;
      font-family: "PingFang SC";
      font-size: 15px;
      font-style: normal;
      font-weight: 400;
      line-height: 25px;
      text-align: left;
      /* 166.667% */
    }
    // .ticket:first-of-type{
    //   border-bottom: none;
    // }
    .ticket {
      // width: 308px;
      // border-bottom: 1px solid #F9F3EC;

      .box {
        // width: 315px;
        height: 70px;
        margin-bottom: 10px;
        position: relative;
        background-repeat: no-repeat;
        background-size: contain;


        .time {
          color: #CEB99A;
          font-family: "PingFang SC";
          font-size: 13px;
          font-style: normal;
          font-weight: 400;
          line-height: normal;
          position: absolute;
          left: 15px;
          top: 40px;
        }

        .useBtn {
          width: 82px;
          height: 30px;
          flex-shrink: 0;
          border-radius: 15px;
          background: #ECB45F;
          color: #FFF;
          text-align: center;
          font-family: "PingFang SC";
          font-size: 14px;
          font-style: normal;
          font-weight: 500;
          line-height: 30px;
          cursor: pointer;
          position: absolute;
          top: 20px;
          left: 215px;
        }

        .unUsedBtn {
          width: 82px;
          height: 30px;
          flex-shrink: 0;
          border-radius: 40px;
          border: 1px solid #FFE0C7;
          background: #FFFAF6;
          color: #D7AC6B;
          text-align: center;
          font-family: "PingFang SC";
          font-size: 13px;
          font-style: normal;
          font-weight: 500;
          line-height: 30px;
          cursor: pointer;
          position: absolute;
          top: 20px;
          left: 214px;
        }
      }
    }

    .toUsed {
      width: 100%;
      padding-top: 16px;
      border-top: 1px solid #F9F3EC;
      // margin-top: 16px;
      // height: 52px;
      // cursor: pointer;

      div {
        color: #DD9F40;
        font-family: "PingFang SC";
        font-size: 17px;
        font-style: normal;
        font-weight: 500;
        // line-height: 52px;
      }
    }
  }

  .footer {
    width: 100%;
    border-radius: 8px;
    background: #FFF;
    padding: 16px;
    box-sizing: border-box;
    margin-top: 16px;
    // width: 313px;
    // min-height: 464px;
    // border-radius: 8px;
    // background: #FFF;
    // margin-top: 12px;
    // margin-left: 15px;
    // position: relative;
    // padding: 0 15px 16px;
    // border: 1px solid #FFF;

    .footer_tips {

      // height: 24px;
      color: #0A0A0A;
      font-family: "PingFang SC";
      font-size: 17px;
      font-style: normal;
      font-weight: 500;
      text-align: left;
      line-height: normal;
      // line-height: 24px;
      // position: absolute;
      // top: 16px;
      // left: 15px;
    }

    .footer_title {
      // width: 315px;
      // height: 50px;
      color: #3F4447;
      font-family: "PingFang SC";
      font-size: 15px;
      font-style: normal;
      font-weight: 400;
      // line-height: 25px;
      // position: absolute;
      // top: 64px;
      // left: 15px;
      text-align: left;
      margin: 10px 0;
      line-height: 25px;
    }

    .footer_img {
      width: 100%;
      // height: 520px;
      img{
        width: 100%;
        height: 100%;
      }
      // background-image: url('../imgPage/equityTwo.png');
      // background-repeat: no-repeat;
      // background-size: contain;
      // margin-top: 126px;

      // position: absolute;
      // top: 126px;
      // left: 15px;
    }
  }

  .tips {
    // height: 30px;
    padding: 9px 46px 6px;

    div {
      color: #CEB99A;
      text-align: center;
      font-family: "PingFang SC";
      font-size: 11px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }
  }

  .dialog_img {
    height: 160px;
    border-radius: 12px 12px 0px 0px;
    background: radial-gradient(69.38% 69.38% at 50% 50%, #FFE7C3 0%, #FFBC53 100%);

    img {
      margin-top: 25px;
    }
  }

  .dialog_main {
    min-height: 58px;
    padding: 12px 30px 0;
    word-break: break-all;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;

    .successTitles {
      color: #0A0A0A;
      text-align: center;
      font-family: "PingFang SC";
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
      margin-bottom: 8px;
    }
  }

  .dialog_error_img {
    height: 160px;
    border-radius: 12px 12px 0px 0px;
    background: linear-gradient(0deg, #DBDFE0 0%, #DBDFE0 100%), radial-gradient(69.38% 69.38% at 50% 50%, #F2ECE4 0%, #BFB19B 100%);
  }

  .dialog_error_main {
    padding: 12px 30px 0;
    min-height: 24px;
    max-height: 100px;
    word-break: break-all;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;

    .errorTitle {
      color: #5A6266;
      text-align: center;
      font-family: "PingFang SC";
      font-size: 17px;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
    }
  }

  .equityStatement {
    width: 315px;
    min-height: 200px;
    background-image: url('../imgPage/equityStatementbg.png');

    background-repeat: no-repeat;
    background-size: cover;

    .equityStatement_img {
      position: relative;
      height: 56px;
      width: 315px;

      .title {
        font-family: "Alimama FangYuanTi VF";
        font-size: 17px;
        font-style: normal;
        font-weight: 700;
        line-height: normal;
        position: absolute;
        top: 18px;
        left: 20px;
      }

      .cancel {
        position: absolute;
        right: 12px;
        top: 12px;
        width: 24px;
        height: 24px;
      }
    }

    .equityStatement_main {
      min-height: 58px;
      max-height: 140px;
      border-top-left-radius: 16px;
      border-top-right-radius: 16px;
      background-color: #FFF;
      flex-shrink: 0;
      fill: linear-gradient(180deg, #FFF 12.39%, #FAFCFF 100%);
      filter: drop-shadow(0px -5px 20.5px rgba(0, 0, 0, 0.02));
      padding: 12px 30px 0;
      overflow-y: scroll;

      div {
        width: 315px;
      }
    }

  }


  .van-dialog--round-button .van-dialog__confirm {
    height: 60px !important;
    border: 1px solid #DBDBDB !important
  }

  .van-dialog--round-button {
    border: 1px solid
  }

  .van-button__text {
    color: #2A2A2A;
    text-align: center;
    font-family: "PingFang SC";
    font-size: 18px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
  }

  .popup_title {
    height: 54px;
    border-bottom: 1px solid #EEE;
    color: #878F99;
    text-align: center;
    font-family: "PingFang SC";
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 54px;
  }

  .popup_box {
    min-height: 98px;
    max-height: 264px;
    overflow-y: auto;

    .popup_address {
      padding: 0 15px;

      .address_detail {
        min-height: 88px;
        // padding: 9px 17px 8px;
        display: flex;

        .hosp_img {
          width: 46px;
          height: 46px;
          border-radius: 8px;
          border: 1px solid #E8E8E8;
          background: #FFF;
          margin-top: 18px;
          margin-right: 17px;
          background-repeat: no-repeat;
          background-size: contain;
        }

        .hosp_detail {
          width: 170px;
          padding-top: 9px;

          .hosp_name {
            min-height: 24px;
            font-size: 17px;
            color: #0A0A0A;
            font-family: 'PingFang SC';
            font-style: normal;
            font-weight: 500;
            justify-content: center;
            text-align: left;
            display: table;

            div {
              display: table-cell;
              vertical-align: middle;
            }

          }

          .hosp_address {
            flex-shrink: 0;
            font-size: 12px;
            color: #878F99;
            font-weight: 400;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
            font-family: 'PingFang SC';
            margin-top: 8px;
            text-align: left;
          }
        }

        .hosp_phone {
          margin-left: 15px;
          width: 34px;
          position: relative;
          // height: 62px;

          .icon {
            width: 24px;
            height: 24px;
            background-image: url('../imgPage/phone.png');
            background-repeat: no-repeat;
            background-size: contain;
            position: absolute;
            left: 0px;
            top: 21px;
          }

          .phone {
            color: #878F99;
            text-align: center;
            font-family: "PingFang SC";
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
            position: absolute;
            left: 0px;
            top: 46px;
            height: 20px;
            line-height: 20px;
            // bottom: 0px;
          }
        }

        .hosp_distance {
          margin-left: 14px;
          width: 35px;
          // height: 62px;
          position: relative;

          .icon {
            width: 24px;
            height: 24px;
            background-image: url('../imgPage/navigation.png');
            background-repeat: no-repeat;
            background-size: contain;
            position: absolute;
            left: 7px;
            top: 21px;
          }

          .km {
            color: #878F99;
            text-align: center;
            font-family: "PingFang SC";
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
            position: absolute;
            left: 4px;
            top: 46px;
            height: 20px;
            line-height: 20px;
            // bottom: 0;
          }
        }
      }
    }
  }

  .popup_cancel {
    border-top: 8px solid #F5F5F5;
    height: 57px;
    color: #0A0A0A;
    text-align: center;
    font-family: "PingFang SC";
    font-size: 18px;
    font-style: normal;
    font-weight: 400;
    line-height: 57px;
  }

  .van-popup {
    padding-bottom: 0 !important;
  }

  .equityStatement_main>>>html,
  address,
  blockquote,
  body,
  dd,
  div,
  dl,
  dt,
  fieldset,
  form,
  frame,
  frameset,
  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  noframes,
  ol,
  p,
  ul,
  center,
  dir,
  hr,
  menu,
  pre {
    display: block
  }

  li {
    display: list-item
  }

  head {
    display: none
  }

  table {
    display: table
  }

  tr {
    display: table-row
  }

  thead {
    display: table-header-group
  }

  tbody {
    display: table-row-group
  }

  tfoot {
    display: table-footer-group
  }

  col {
    display: table-column
  }

  colgroup {
    display: table-column-group
  }

  td,
  th {
    display: table-cell;
  }

  caption {
    display: table-caption
  }

  th {
    font-weight: bolder;
    text-align: center
  }

  caption {
    text-align: center
  }

  body {
    margin: 8px;
    line-height: 1.12
  }

  h1 {
    font-size: 2em;
    margin: .67em 0
  }

  h2 {
    font-size: 1.5em;
    margin: .75em 0
  }

  h3 {
    font-size: 1.17em;
    margin: .83em 0
  }

  h4,
  p,
  blockquote,
  ul,
  fieldset,
  form,
  ol,
  dl,
  dir,
  menu {
    margin: 1.12em 0
  }

  h5 {
    font-size: .83em;
    margin: 1.5em 0
  }

  h6 {
    font-size: .75em;
    margin: 1.67em 0
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  b,
  strong {
    font-weight: bolder
  }

  blockquote {
    margin-left: 40px;
    margin-right: 40px
  }

  i,
  cite,
  em,
  var,
  address {
    font-style: italic
  }

  pre,
  tt,
  code,
  kbd,
  samp {
    font-family: monospace
  }

  pre {
    white-space: pre
  }

  button,
  textarea,
  input,
  object,
  select {
    display: inline-block;
  }

  big {
    font-size: 1.17em
  }

  small,
  sub,
  sup {
    font-size: .83em
  }

  sub {
    vertical-align: sub
  }

  sup {
    vertical-align: super
  }

  table {
    border-spacing: 2px;
  }

  thead,
  tbody,
  tfoot {
    vertical-align: middle
  }

  td,
  th {
    vertical-align: inherit
  }

  s,
  strike,
  del {
    text-decoration: line-through
  }

  hr {
    border: 1px inset
  }

  ol,
  ul,
  dir,
  menu,
  dd {
    margin-left: 40px
  }

  ol {
    list-style-type: decimal
  }

  ol ul,
  ul ol,
  ul ul,
  ol ol {
    margin-top: 0;
    margin-bottom: 0
  }

  u,
  ins {
    text-decoration: underline
  }

  br:before {
    content: "\A"
  }

  :before,
  :after {
    white-space: pre-line
  }

  center {
    text-align: center
  }

  abbr,
  acronym {
    font-variant: small-caps;
    letter-spacing: 0.1em
  }

  :link,
  :visited {
    text-decoration: underline
  }

  :focus {
    outline: thin dotted invert
  }

  BDO[DIR="ltr"] {
    direction: ltr;
    unicode-bidi: bidi-override
  }

  BDO[DIR="rtl"] {
    direction: rtl;
    unicode-bidi: bidi-override
  }

  *[DIR="ltr"] {
    direction: ltr;
    unicode-bidi: embed
  }

  *[DIR="rtl"] {
    direction: rtl;
    unicode-bidi: embed
  }
  .loading{
    position: fixed;
    left: 50%;
    top: 50%;
    transform: translate(-50%,-50%);
  }
}
</style>