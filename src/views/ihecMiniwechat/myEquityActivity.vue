<!-- 核销码 -->
<template>
  <div class="myEquityActivity">
    <div class="top">
      <div class="top_img"></div>
    </div>
    <div class="main" v-if="equityList.length != 0">
      <div class="main_title">权益列表：</div>
      <div class="main_equity">
        <div class="equityDetail" v-for="(item, index) in equityList" :key="index"
          @click="handleToEquity(item.user_join_id)">
          <div class="equity_name">{{ item.activity_name }}</div>
          <div class="equity_btn">
            <div></div>
          </div>
        </div>
      </div>
    </div>
    <div class="main nocontent" v-else>
      <div class="noContentBox">
        <div class="img"></div>
        <div>暂无数据</div>
      </div>
    </div>
  </div>
</template>
<script>
import { getBenefitActivityList } from '@/api/ihecMiniwechat.js'
export default {
  data() {
    return {
      equityList: []
    };
  },
  created() {
    this.init()
  },
  mounted() {
  },
  methods: {
    async init() {
      if (this.$route.query.token) {
        this.token = this.$route.query.token
        localStorage.setItem('authorization', this.token)
      }
      await getBenefitActivityList({ page: 1, size: 40 }).then(res => {
        if (res.status == 0) {
          let { list } = res.data
          this.equityList = list
        }
      })
    },
    handleToEquity(user_join_id) {
      this.$router.push({
        path: '/ihecMiniwechat/equityActivityDetails',
        query: {
          user_join_id,
          from: 'myequityActivity'
        }
      })
    },
  },
  watch: {}
};
</script>
<style lang="scss">
.myEquityActivity {
  width: 100%;
  min-height: 100vh;
  background: #F5F5F5;

  .top {
    height: 81px;
    padding: 43px 35px 0;

    .top_img {
      width: 305px;
      height: 81px;
      background-image: url('../imgPage/myEquity.png');
      background-repeat: no-repeat;
      background-size: contain;

    }
  }

  .main {
    width: 345px;
    height: 660px;
    flex-shrink: 0;
    border-top-left-radius: 24px;
    border-top-right-radius: 24px;
    background: #FFF;
    box-shadow: 0px 16px 16px 0px rgba(228, 191, 116, 0.10);
    padding: 16px 15px 0;

    .main_title {
      height: 24px;
      color: #999;
      font-family: "PingFang SC";
      font-size: 17px;
      font-style: normal;
      font-weight: 500;
      line-height: 24px;
      text-align: left;
      margin-left: 9px;
      margin-bottom: 16px;
    }

    .main_equity {
      min-height: 56px;
      border-radius: 6px;
      background: #FFF5E5;
      flex-shrink: 0;
      max-height: 620px;
      overflow-y: auto;

      .equityDetail {
        padding: 0 4px 0 15px;
        height: 70px;
        display: flex;
        // position: relative;
        border-bottom: 1px solid #FFF;
        justify-content: space-between;
        align-items: center;

        .equity_name {
          width: 270px;
          min-height: 24px;
          color: #0A0A0A;
          font-family: "PingFang SC";
          font-size: 17px;
          font-style: normal;
          font-weight: 500;
          line-height: 24px;
          text-align: left;
          // position: absolute;
          // top: 16px;
          // left: 15px;
        }

        .equity_btn {
          width: 44px;
          height: 56px;
          margin: auto 0;
          // position: absolute;
          // right: 0px;
          // top: 0;

          div {
            margin: 16px 13px;
            width: 18px;
            height: 18px;
            background-image: url('../imgPage/btn_right.png');
            background-repeat: no-repeat;
            background-size: contain;
          }
        }
      }
    }
  }

  .nocontent {
    .noContentBox {
      padding-top: 107px;
      padding-left: 14px;

      .img {
        width: 300px;
        height: 159.82px;
        background-image: url('../imgPage/nocontent.png');
        background-repeat: no-repeat;
        background-size: contain;
        margin: 0 auto;
      }
    }

  }
}
</style>