import axios from '@/utils/api.request'

export function getBsList(data = {}) {
  return axios.request({
    url: '/api/health/v1/bg/history/list',
    method: 'get',
    params: data
  })
}

export function getBpList(data = {}) {
  return axios.request({
    url: '/api/health/v1/bp/history/list',
    method: 'get',
    params: data
  })
}

export function getMotionList(data = {}) {
  return axios.request({
    url: '/api/health/v1/motion/history/list',
    method: 'get',
    params: data
  })
}

export function getWeightList(data = {}) {
  return axios.request({
    url: '/api/health/v1/weight/history/list',
    method: 'get',
    params: data
  })
}

export function getBsList2(data = {}) {
  return axios.request({
    url: '/api/health/v2/bg/history/list',
    method: 'get',
    params: data
  })
}

export function getBpList2(data = {}) {
  return axios.request({
    url: '/api/health/v2/bp/history/list',
    method: 'get',
    params: data
  })
}

export function getMotionList2(data = {}) {
  return axios.request({
    url: '/api/health/v2/motion/history/list',
    method: 'get',
    params: data
  })
}

export function getWeightList2(data = {}) {
  return axios.request({
    url: '/api/health/v2/weight/history/list',
    method: 'get',
    params: data
  })
}
