/*
 * @Descripttion: MMC问卷接口
 * @version:
 * @Author: guxiang
 * @Date: 2021年12月24日10:04:43
 * @LastEditors: guxiang
 * @LastEditTime: 2021年12月24日10:04:43
 */
import mmcReportAxios from "@/utils/mmc_request.js";

// 参加活动
const api = {
  answerSign(data) {
    return mmcReportAxios.request({
      url: "/api/v1/answer/sign",
      method: "get",
      params: data,
    });
  },
  qssInfo(data) {
    return mmcReportAxios.request({
      url: "/api/v1/answer/qss/info",
      method: "get",
      params: data,
    });
  },
  // 获取最新访视
  getCurrentVisitLevel(user_id) {
    return mmcReportAxios.request({
      url: `api/v1/patients/${user_id}/visits/last`,
      method: 'get',
    });
  },
  // 获取用药访视基线日期
  getDrugDateBaseLine(data) {
    return mmcReportAxios.request({
      url: '/api/v1/drug_dateline',
      method: 'get',
      params: data,
    });
  },
  // 获取用药列表
  getDrugList(data) {
    return mmcReportAxios.request({
      url: '/api/v1/drug_list',
      method: 'get',
      params: data,
    });
  },
  adverseEventList(user_id) {
    return mmcReportAxios.request({
      url: `/api/v2/adverse_event/lists/?user_id=${user_id}`,
      method: 'get',
    });
  },
  eyeResult(data) {
    return mmcReportAxios.request({
      url: "/api/v1/eye/result",
      method: "get",
      params: data,
    });
  },
};

export default api;
