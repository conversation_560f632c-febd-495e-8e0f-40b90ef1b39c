/*
 * @Descripttion: 医生工作室
 * @version:
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-22 14:18:10
 * @LastEditors: l<PERSON><PERSON><PERSON>n
 * @LastEditTime: 2021-06-28 18:19:26
 */
import axios from '@/utils/workroom_api.request.js'
import axiosAndroid from '@/utils/workroom_api_android.request.js'
import patientAxios from '@/utils/api.request.js'
import saasAxios from '@/utils/saas.api.request.js'
import bfAxios from '@/utils/bf_api.request.js'
/**
 * 医生工作室获取血压图谱
 * @param {String} startDate 开始时间（例如：2020-04-02。格式Y-m-d）
 * @param {String} endDate 结束时间（例如：2020-05-12。格式Y-m-d）
 */
export function bpGraphData(startDate, endDate, user_id) {
  return axios.request({
    url: `/api/studio/v1/stat/patient/bp/graph`,
    method: 'get',
    params: {
      start_date: startDate,
      end_date: endDate,
      user_id
    }
  })
}
/**
 * 医生工作室获取血压详情
 * @param {String} start_date
 * @param {String} end_date
 * @param {String} user_id
 * @returns
 */
export function reportBp(start_date, end_date, user_id) {
  return axios.request({
    url: `/api/studio/v1/stat/patient/bp/map/list`,
    method: 'get',
    params: {
      start_date,
      end_date,
      user_id
    }
  })
}
/**
 * 医生工作室血压7日报告
 */
export function getGuangjiaBpDateRecords(startDate, endDate, user_id) {
  return axios.request({
    url: `/api/studio/v1/stat/patient/bp/report`,
    method: 'get',
    params: {
      start_date: startDate,
      end_date: endDate,
      user_id
    }
  })
}
/**
 * 医生工作室血糖7日报告
 */
export function getGuangjiaBgDateRecords(endDate, user_id) {
  return axios.request({
    url: `/api/studio/v1/stat/patient/bg/report`,
    method: 'get',
    params: {
      end_date: endDate,
      user_id
    }
  })
}
/**
* 获取某一天的血糖数据
* @param {String} date 日期，例如：2020-04-30。格式Y-m-d
*/
export function bgOneDyData(date, user_id) {
  return axios.request({
    url: `/api/studio/v1/stat/patient/bg/one-day`,
    method: 'get',
    params: {
      date,
      user_id
    }
  })
}

/**
* 获取血糖趋势
* @param {String} startDate 开始时间（例如：2020-04-02。格式Y-m-d）
* @param {String} endDate 结束时间（例如：2020-05-12。格式Y-m-d）
* @param {String} dinStatus 就餐状态（0：全部，1：空腹，2：早餐后，3：午餐前，4：午餐后，5：晚餐前，6：晚餐后，7：睡前，8：凌晨）
*/
export function bgGraphData(startDate, endDate, dinStatus, user_id) {

  return axios.request({
    url: `/api/studio/v1/stat/patient/bg/trend`,
    method: 'get',
    params: {
      start_date: startDate,
      end_date: endDate,
      din_status: dinStatus,
      user_id
    }
  })
}

/**
 * 获取7日运动步数记录
 */
export function sportStepGraphData(endDate, userId) {
  return axios.request({
    url: `/api/studio/v1/patient/stat/patient/motion_history`,
    method: 'get',
    params: {
      end_date: endDate,
      user_id: userId
    }
  })
}
/**
 * 获取身高体重7日变化
 */
export function heightWeightGraphData(endDate, userId) {
  return axios.request({
    url: `/api/studio/v1/patient/stat/patient/height_history`,
    method: 'get',
    params: {
      end_date: endDate,
      user_id: userId
    }
  })
}
/**
* 获取兑换服务包临时码
*/
export function getTempExchangeCode(qrcode_id, token) {
  return patientAxios.request({
    url: `/api/ehosp/v1/service/package/redeem/qrcode_get`,
    method: 'post',
    params: {
      qrcode_id,
      token
    }
  })
}

/**
  * ocr获取检查单详情
  */
export function recordDetail(params) {
  return axios.request({
    url: `/api/v1/detection/ocr/request/record/detail`,
    method: 'get',
    params: params
  })
}

/**
  * ocr保存检查单
  */
export function recordSave(data) {
  return axios.request({
    url: `/api/v1/detection/ocr/request/record/save`,
    method: 'post',
    data
  })
}
/**
  * 获取验证码
  */
export function getSmsCode(data) {
  return axios.request({
    url: `/api/doc/getCode`,
    method: 'post',
    data
  })
}
/**
  * 获取退关人配置
  */
export function getPromotionPersonInfo(data) {
  return axios.request({
    url: `/api/v1/channel/doc_intro/config?channel_id=${data}`,
    method: 'get',
  })
}
/**
  * 医生注册登录
  */
export function docLogin(data) {
  return axios.request({
    url: `/api/doc/login`,
    method: 'post',
    data
  })
}
/**
  * 医生注册登录
  */
export function getWxConfig(data) {
  return patientAxios.request({
    url: `/api/v1/wechat/js_api/sign`,
    method: 'post',
    data
  })
}

// 渠道码 - 获取邀请码
export function getinvitationQrcode(data) {
  return axios.request({
    url: `/api/channel/v1/invitation/qrcode`,
    method: 'post',
    data
  })
}

// 渠道码 - 修改渠道信息
export function updateChannelInfo(data) {
  return axios.request({
    url: `/api/channel/v1/channel_info/update`,
    method: 'post',
    data
  })
}

// 渠道码 - 我的订单
export function getChannelOrderData(data) {
  return axios.request({
    url: `/api/studio/v2/channel_code/order/list`,
    method: 'post',
    data
  })
}

// 渠道码 - 我的订单筛选下发
export function getChannelSearchConfig(data) {
  return axios.request({
    url: `/api/studio/v2/channel_code/order/search/config`,
    method: 'get',
    params: data
  })
}

// 渠道码 - 订单筛选
export function getChannelSearchConfigApi(url, params) {
  return axios.request({
    url,
    method: 'get',
    params
  })
}

// 渠道码 - 我的订单筛选下发 - 机构列表
export function getChannelHospList(data) {
  return axios.request({
    url: `/api/ehosp/v2/hosp/list`,
    method: 'get',
    params: data
  })
}

// 渠道码 - 我的订单筛选下发 - 科室列表
export function getChannelDeptList(data) {
  return axios.request({
    url: `/api/dept/pend_query`,
    method: 'get',
    params: data
  })
}

// 渠道码 - 我的渠道商 代理商/个人
export function getChannelListData(data) {
  return axios.request({
    url: `/api/studio/v2/channel_code/inner/channel/list`,
    method: 'get',
    params: data
  })
}

// 渠道码 - 代理商下的员工 - 二级页面
export function getChannelAgentListData(data) {
  return axios.request({
    url: `/api/studio/v2/channel_code/inner/channel/agent/list`,
    method: 'get',
    params: data
  })
}

// 渠道码 - 代理商下的员工 - 金刚位
export function getChannelAdminListData(data) {
  return axios.request({
    url: `/api/studio/v2/channel_code/agent_admin/channel/list`,
    method: 'get',
    params: data
  })
}

// 用药搜索
export function getSearchDrugList(data) {
  return saasAxios.request({
    url: `/api/web/v1/drug/search/list?keyword=${data.searchStr}&patient_id=${data.patientId}&scene=${data.scene}`,
    method: 'get',
    data
  })
}
// 用药频次、途径、剂量单位
export function getMedicationConfig(data) {
  return saasAxios.request({
    url: `/api/web/v1/patient/drug/dictionary`,
    method: 'post',
    data
  })
}
// 添加用药
export function addMedication(data) {
  return saasAxios.request({
    url: `/api/web/v1/patient/drug/add`,
    method: 'post',
    data
  })
}
// 通过条形码 搜索药品
export function getDrugListByBarCode(data) {
  return saasAxios.request({
    url: `/api/web/v1/drug/search/barcode?barcode=${data.barCode}&patient_id=${data.patientId}&scene=${data.scene}`,
    method: 'get',
    data
  })
}
// 获取变更记录
export function getChangeList(data) {
  return saasAxios.request({
    url: `/api/web/v1/patient/drug/record/list`,
    method: 'post',
    data
  })
}
// 编辑变更记录 保存
export function updateRecord(data) {
  return saasAxios.request({
    url: `/api/web/v1/patient/drug/record/update`,
    method: 'post',
    data
  })
}
// 变更记录 保存
export function addMedicationRecord(data) {
  return saasAxios.request({
    url: `/api/web/v1/patient/drug/record/add`,
    method: 'post',
    data
  })
}

//治疗方案列表
export function getPatientDrugList(data) {
  return saasAxios.request({
    url: `/api/web/v1/patient/drug/list`,
    method: 'post',
    data
  })
}

//治疗方案历史列表
export function getPatientRecordList(data) {
    return saasAxios.request({
      url: `/api/web/v1/patient/drug/record/list`,
      method: 'post',
      data
    })
  }

//治疗方案列表删除
export function getPatientDrugDelete(data) {
  return saasAxios.request({
    url: `/api/web/v1/patient/drug/delete`,
    method: 'post',
    data
  })
}
//治疗方案历史记录列表删除
export function getPatientDrugRecordDelete(data) {
  return saasAxios.request({
    url: `/api/web/v1/patient/drug/record/delete`,
    method: 'post',
    data
  })
}

//his记录列表
export function getPatientDrugHisdList(data) {
  return saasAxios.request({
    url: `/api/web/v1/patient/drug/his/list`,
    method: 'post',
    data
  })
}


//微信获取科室列表
export function getPatientDeptRoomList(data) {
  return saasAxios.request({
    url: `/api/drug/v1/get/patient/dept_room_list?patient_id=${data}`,
    method: 'get',
    data
  })
}


//家庭记录获取历史
export function getPatientHistory(data) {
  return saasAxios.request({
    url: `/api/drug/v1/get/patient/history/family_record?user_id=${data}`,
    method: 'get',
    data
  })
}
//APP首页配置接口
export function getAppHomeData(data) {
  return axios.request({
    url: `/api/studio/v2/doc/promotion/index`,
    method: 'get',
    data
  })
}
//APP首页配置新接口
export function getAppHomeDataNew(data) {
  return axios.request({
    url: `/api/studio/v2/doc/promotion/index/new`,
    method: 'get',
    data
  })
}
//APP首页配置接口
export function getTargetUrl(params) {
  return patientAxios.request({
    url: `/api/ehosp/v1/service/package/redeem/scan/target_path_get`,
    method: 'get',
    params
  })
}
// APP首页案例动态
export function getPatientHomePage(params) {
  return axios.request({
    url: `/api/studio/v2/patient/home/<USER>
    method: 'get',
    params
  })
}

//核销核销券
export function couponUse(data) {
  return axiosAndroid.request({
    url: `/api/coupon/v1/coupon/use`,
    method: 'post',
    data
  })
}

//患者权益列表
export function getBenefitActivityCouponList(params) {
  return axiosAndroid.request({
    url: `/api/coupon/v1/get_benefit_activity_coupon_list`,
    method: 'get',
    params
  })
}

//服务包介绍列表
export function getPackageIntroList(params) {
  return axios.request({
    url: `/api/studio/v2/channel_code/ehosp_package/list`,
    method: 'get',
    params
  })
}

// 获取血糖列表
export function getBgList(params) {
  return patientAxios.request({
    url: `/api/v2/wechat/bg/list`,
    method: 'get',
    params
  })
}

//内部员工-客服经理
export function getStaffServiceManager(params) {
  return axios.request({
    url: `/api/studio/v2/channel_code/channel_doc/list`,
    method: 'get',
    params
  })
}

// 获取血氧饱和度列表
export function getSpo2List(params) {
  return patientAxios.request({
    url: `/api/v2/wechat/spo2/list`,
    method: 'get',
    params
  })
}

//代理商管理员-客服经理
export function getAgentServiceManager(params) {
  return axios.request({
    url: `/api/studio/v2/channel_code/agent_admin/channel_doc/list`,
    method: 'get',
    params
  })
}
//血压测量记录 图表
export function getBpLineCharts(params) {
  return patientAxios.request({
    url: `/api/health/v1/bp/chart`,
    method: 'get',
    params
  })
}

//客服公司
export function getAgentCompany(params) {
  return axios.request({
    url: `/api/studio/v2/channel_code/channel_hosp/list`,
    method: 'get',
    params
  })
}
// 获取流速峰值列表
export function getPefList(params) {
  return patientAxios.request({
    url: `/api/v2/wechat/pef/list`,
    method: 'get',
    params
  })
}

//我服务的医生列表--搜索配置项
export function getSearchConfig(params) {
  return axios.request({
    url: `/api/studio/v2/channel_code/doc/search/config`,
    method: 'get',
    params
  })
}

// 获取BMI列表
export function getBmiList(params) {
  return patientAxios.request({
    url: `/api/v2/wechat/bmi/list`,
    method: 'get',
    params
  })
}

//搜索医院
export function getHospList(params) {
  return axios.request({
    url: `/api/ehosp/v2/hosp/list`,
    method: 'get',
    params
  })
}

// 获取血脂列表
export function getBloodfatList(params) {
  return patientAxios.request({
    url: `/api/v2/wechat/bloodfat/list`,
    method: 'get',
    params
  })
}
//搜索科室
export function getDeptList(params) {
  return axios.request({
    url: `/api/dept/pend_query`,
    method: 'get',
    params
  })
}

//我服务的医生列表
export function getMyServiceDocList(data) {
  return axios.request({
    url: `/api/studio/v2/channel_code/doc/list`,
    method: 'post',
    data
  })
}

// 获取哮鸣音列表
export function getWheezesfatList(params) {
  return patientAxios.request({
    url: `/api/v2/wechat/wheezes/list`,
    method: 'get',
    params
  })
}

// 获取哮鸣音报告
export function getWheezesfatReport(params) {
  return patientAxios.request({
    url: `/api/v2/wechat/wheezes/report`,
    method: 'get',
    params
  })
}

//血压测量记录 配置
export function getBpConfig(params) {
  return patientAxios.request({
    url: `/api/v2/wechat/bp/config`,
    method: 'get',
    params
  })
}

// 获取微信步数列表
export function getmotionList(params) {
  return patientAxios.request({
    url: `/api/v2/wechat/motion/list`,
    method: 'get',
    params
  })
}

//血压测量列表
export function getBpList(params) {
  return patientAxios.request({
    url: `/api/v2/wechat/bp/list`,
    method: 'get',
    params
  })
}
// 获取雾化用药列表
export function getNebuList(params) {
  return patientAxios.request({
    url: `/api/v2/wechat/nebu/list`,
    method: 'get',
    params
  })
}
//获取测量记录 正确测量范围（测量记录右上角问号）
export function getNormalRange(params) {
  return patientAxios.request({
    url: `/api/v2/wechat/get_measure_indicator_range`,
    method: 'get',
    params
  })
}
// 数据统计-血压趋势报告
export function getBpTrend(data) {
  return bfAxios.request({
    url: `/patient/measure/bp/trend/records/v1.0`,
    method: 'post',
    data
  })
}

// 血压 饼图
export function getBpPieCharts(params) {
  return patientAxios.request({
    url: `/api/v2/wechat/bp/pie_avg`,
    method: 'get',
    params
  })
}
// 血压 折线图和表格
export function getBpTableAndCharts(params) {
  return patientAxios.request({
    url: `/api/v2/wechat/bp/map`,
    method: 'get',
    params
  })
}
// 血压 图谱
export function getBpGraph(params) {
  return patientAxios.request({
    url: `/api/v2/wechat/bp/graph`,
    method: 'get',
    params
  })
}
// 血糖 删除一条数据
export function delBgOne(data) {
  return patientAxios.request({
    url: `/api/v1/wechat/bg/del_one`,
    method: 'post',
    data
  })
}
// 血糖 加备注
export function addReason(data) {
  return patientAxios.request({
    url: `/api/health/v1/bg_bp/reason`,
    method: 'post',
    data
  })
}


//搜索公司
export function searchCompanyList(params) {
  return axios.request({
    url: `/api/channel/v1/company/search`,
    method: 'get',
    params,
    timeout: 10000
  })
}
//推广页 进入页面后换取scanid
export function promotionAfterScan(data) {
  return axios.request({
    url: `/api/channel/v1/invitation/qrcode/after_scan`,
    method: 'post',
    data
  })
}
// 流速峰值 饼图数据
export function getPefPie(params) {
  return patientAxios.request({
    url: `/api/v2/wechat/pef/pie_avg`,
    method: 'get',
    params
  })
}
// 步数 图表
export function getStepHistogram(params) {
  return patientAxios.request({
    url: `/api/v2/wechat/motion/chart`,
    method: 'get',
    params
  })
}
// 异常列表
export function getAbnormalList(data) {
  return axios.request({
    url: `/api/v1/patient/inspect/abnormal`,
    method: 'post',
    data
  })
}

// 进入推广页 获取配置
export function promotionGetConfig(params) {
  return axios.request({
    url: `/api/channel/v1/scan_page/config`,
    method: 'get',
    params
  })
}
// 异常列表-指标筛选项
export function getIndicatorList(params) {
  return axios.request({
    url: `/api/v1/patient/inspect/indicator_code`,
    method: 'get',
    params
  })
}

// 进入推广页 保存前校验
export function promotionSaveCheck(params) {
  return axios.request({
    url: `/api/channel/v1/invitation/before_invite`,
    method: 'get',
    params
  })
}
// 血氧饱和 饼图
export function getSpo2Pie(params) {
  return patientAxios.request({
    url: `/api/v2/wechat/spo2/report`,
    method: 'get',
    params
  })
}

// 进入推广页 保存前校验
export function promotionSave(data) {
  return axios.request({
    url: `/api/channel/v1/invitation/qrcode/scan_save`,
    method: 'post',
    data
  })
}
// 血氧饱和 折线图
export function getSpo2LineChart(params) {
  return patientAxios.request({
    url: `/api/v2/wechat/spo2/chart`,
    method: 'get',
    params
  })
}
// BMI 折线图
export function getBMILineChart(params) {
  return patientAxios.request({
    url: `/api/v2/wechat/weight/report`,
    method: 'get',
    params
  })
}
// 血糖 饼图
export function getBgPie(params) {
  return patientAxios.request({
    url: `/api/v2/wechat/bg/pie_avg`,
    method: 'get',
    params
  })
}
// 血糖 表格
export function getBgTable(params) {
  return patientAxios.request({
    url: `/api/v2/wechat/bg/table`,
    method: 'get',
    params
  })
}
// 血糖 折线图
export function getBgLine(params) {
  return patientAxios.request({
    url: `/api/v2/wechat/bg/line`,
    method: 'get',
    params
  })
}
// 血压 删除一条数据
export function delBpOne(data) {
  return patientAxios.request({
    url: `/api/v1/wechat/bp/del_one`,
    method: 'post',
    data
  })
}
export function getChannelIncomeSearch(data) {
  return axios.request({
    url: `/api/studio/v2/channel_code/income/search`,
    method: 'get',
    params: data
  })
}
export function getChannelIncomeListData(data) {
  return axios.request({
    url: `/api/studio/v2/channel_code/agent_admin/income/list`,
    method: 'get',
    params: data
  })
}

export function getChannelInnerIncomeListData(data) {
  return axios.request({
    url: `/api/studio/v2/channel_code/inner/income/list`,
    method: 'get',
    params: data
  })
}

export function getChannelPersonalIncomeListData(data) {
  return axios.request({
    url: `/api/studio/v2/channel_code/personal/income/list`,
    method: 'get',
    params: data
  })
}

export function getIncomeByAgentApi(data) {
  return axios.request({
    url: '/api/studio/v2/channel_code/agent_admin/income_by_agent',
    method: 'post',
    params: data
  })
}

export function getChannelFileData(data) {
  return axios.request({
    url: `/api/ehosp/v1/withdraw/invoice/upload`,
    method: 'post',
    data
  })
}
// 异常列表 标记已读
export function abnormalRead(data) {
  return axios.request({
    url: `/api/v1/patient/inspect/abnormal/read`,
    method: 'post',
    data
  })
}

export function getWithdrawBankList(data) {
  return axios.request({
    url: `/api/ehosp/v1/withdraw/bank-search`,
    method: 'get',
    params: data
  })
}

export function getChannelAgentWithdrawSave(data) {
  return axios.request({
    url: `/api/ehosp/v1/withdraw/sale/channel/agent/withdraw`,
    method: 'post',
    data
  })
}

// 查询 cgm 设备列表
export function getCgmDeviceListApiV2(params) {
  return patientAxios.request({
    url: `/api/v2/wechat/cgm/device/list`,
    method: 'get',
    params
  })
}

// 查询 cgm 图表
export function getCgmDeviceDataApiV2(params) {
  return patientAxios.request({
    url: `/api/v2/wechat/cgm/device/data`,
    method: 'get',
    params
  })
}
export function getChannelAgenConfig(data) {
  return axios.request({
    url: `/api/ehosp/v1/withdraw/sale/channel/agent/config`,
    method: 'get',
    params: data
  })
}
// 停用渠道
export function setChannelStopApi(data) {
  return axios.request({
    url: '/api/studio/v2/channel_code/channel/stop',
    method: 'post',
    params: data
  })
}


// 血糖测量结果
export function getBgMeasureResult(params) {
  return patientAxios.request({
    url: `/api/v2/wechat/bg/get_result`,
    method: 'get',
    params
  })
}

// 血压测量结果
export function getBpMeasureResult(params) {
  return patientAxios.request({
    url: `/api/v2/wechat/bp/show`,
    method: 'get',
    params
  })
}

// 获取新闻列表
export function getNewsList(params) {
  return axios.request({
    url: `/api/ehosp/v1/applied/project/column/news/list`,
    method: 'get',
    params
  })
}

// 获取首页更多案例动态
export function getHomeNewsList(params) {
  return axios.request({
    url: `/api/studio/v2/patient/home/<USER>/list`,
    method: 'get',
    params
  })
}

// 获取指标组
export function getIndexGroup(params) {
  return axios.request({
    url: `/api/studio/v2/patient/search/abnormal/group`,
    method: 'get',
    params
  })
}
// 服务包介绍列表 是否显示集合列表按钮
export function packageIntroListAllQrcodeShow(params) {
  return axios.request({
    url: `/api/ehosp/v1/service/package/opened/doc/qrcode/is_show`,
    method: 'get',
    params
  })
}
// 服务包介绍列表 合集列表码
export function packageIntroListAllCode(params) {
  return axios.request({
    url: `/api/ehosp/v1/service/package/opened/doc/qrcode`,
    method: 'get',
    params
  })
}
// 服务包介绍列表 单个服务包码
export function packageIntroListItemCode(params) {
  return axios.request({
    url: `/api/ehosp/v1/service/package/opened/qrcode`,
    method: 'get',
    params
  })
}

// 医生转交搜索
export function docTransferSaleSearch(params) {
  return axios.request({
    url: `/api/channel/v1/doc/transfer/sale/search`,
    method: 'get',
    params
  })
}

//医生转交确定
export function docTransfer(data) {
  return axios.request({
    url: '/api/channel/v1/doc/transfer',
    method: 'post',
    data
  })
}

// 科室管理患者情况
export function getDeptPatients(params) {
  return axios.request({
    url: `/api/studio/v2/panel/dept/patients`,
    method: 'get',
    params
  })
}
export function getCategory(params) {
  return axios.request({
    url: `/api/ehosp/v1/service/package/product/category`,
    method: 'get',
    params
  })
}
export function getShowcaseGoods(params) {
  return axios.request({
    url: `/api/ehosp/v1/service/package/product/my/list`,
    method: 'get',
    params
  })
}
export function getAllGoods(params) {
  return axios.request({
    url: `/api/ehosp/v1/service/package/product/not/added/list`,
    method: 'get',
    params
  })
}
export function addToMyShowcase(params) {
  return axios.request({
    url: `/api/ehosp/v1/service/package/product/save`,
    method: 'post',
    params
  })
}
// 从橱窗移除商品
export function removeFromMyShowcase(params) {
  return axios.request({
    url: `/api/ehosp/v1/service/package/product/remove`,
    method: 'post',
    params
  })
}
// 商品详情
export function goodsDetail(params) {
  return axios.request({
    url: `/api/ehosp/v1/service/package/product/detail`,
    method: 'get',
    params
  })
}
// 医生橱窗获取类别
export function getCategoryForDoc(params) {
  return axios.request({
    url: `/api/ehosp/v1/service/package/product/my/category`,
    method: 'get',
    params
  })
}
// 获取腰臀比列表
export function getWhrList(params) {
  return patientAxios.request({
    url: `/api/v2/wechat/wh/list`,
    method: 'get',
    params
  })
}
export function getTwList(params) {
  return patientAxios.request({
    url: `/api/v2/wechat/body/tw/list`,
    method: 'get',
    params
  })
}
// 订单-查看收货人信息
export function getConsigneeInfo(params) {
  return axios.request({
    url: `/api/studio/v2/channel_code/order/recipient`,
    method: 'get',
    params
  })
}