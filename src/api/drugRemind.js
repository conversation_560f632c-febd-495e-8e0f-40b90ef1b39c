import axios from '@/utils/api.request'
// 用药提醒列表
export function getRemindList(data) {
  return axios.request({
    url: '/api/health/v1/drug/remind',
    method: 'get',
    params: data
  })
}
// 暂停用药提醒
export function stopRemind(data) {
  return axios.request({
    url: '/api/health/v1/drug/remind',
    method: 'post',
    data
  })
}
// 新增用药提醒
export function addRemind(data) {
  return axios.request({
    url: '/api/health/v1/drug/remind',
    method: 'post',
    data
  })
}
// 删除用药提醒
export function delRemind(data) {
  return axios.request({
    url: '/api/health/v1/drug/remind',
    method: 'DELETE',
    data
  })
}
// 判断是否关注公众号
export function getStatus(data) {
  return axios.request({
    url: '/api/vipdiabetes/v1/subscribe/status',
    method: 'get',
    params: data
  })
}
// 编辑详情
export function getDetails(data) {
  return axios.request({
    url: '/api/health/v1/drug/info',
    method: 'get',
    params: data
  })
}

// 药品列表
export function getDrugList(data) {
  return axios.request({
    url: '/api/health/v1/drug/list',
    method: 'get',
    params: data
  })
}
// 提醒时间获取
export function getRemindTime(data) {
  return axios.request({
    url: '/api/vipdiabetes/v1/user/camp',
    method: 'get',
    params: data
  })
}

// 用药记录单
export function getDrugExcel(data) {
  return axios.request({
    url: '/api/health/v1/drug/remind/detail',
    method: 'get',
    params: data
  })
}

// 设置按时服药状态
export function setDrugStatus(data) {
  return axios.request({
    url: '/api/health/v1/drug/use/ontime',
    method: 'post',
    data
  })
}
// 数据同步
export function getRefreshDatas(data) {
  return axios.request({
    url: '/api/health/v1/drug/is_owner_remind',
    method: 'get',
    params: data
  })
}
// 数据同步
export function addHealthy(data) {
  return axios.request({
    url: '/api/health/v1/drug/save_health_ability',
    method: 'get',
    params: data
  })
}
