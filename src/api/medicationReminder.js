import axios from '@/utils/api.request'

export function getInfo(data = {}) {
  return axios.request({
    url: '/api/appointment/v1/appointment/remind/info',
    method: 'get',
    params: data
  }, false)
}

// 创建预约接口
export function saveAppointment(data) {
  return axios.request({
    url: '/api/appointment/v1/appointment/remind/saveAppointment',
    method: 'post',
    data
  }, false)
}

// 取消预约接口
export function cancelAppointment(data) {
  return axios.request({
    url: '/api/appointment/v1/appointment/remind/cancelAppointment',
    method: 'post',
    data
  }, false)

}
export function getEducationInfo(data = {}) {
  return axios.request({
    url: '/api/v1/mmc/patient/care/tag/education',
    method: 'get',
    params: data
  }, false)
}
