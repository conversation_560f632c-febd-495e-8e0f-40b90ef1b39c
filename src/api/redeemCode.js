import axios from '@/utils/api.request'

/**
 * 血糖兑换码兑换
 * @param {Object} data 请求参数（cell：手机号；redeem_code：兑换码；verify_code：手机验证码）
 */
export function postRedeemCodeUse(data) {
  return axios.request({
    url: '/api/auth/wechat/v1/bg/redeem_code/use',
    method: 'post',
    data: data,
  })
}

/**
 * 发送短信
 * @param {String} cell 手机号
 */
export function postRedeemCodeSendMsg(data) {
  return axios.request({
    url: '/api/auth/wechat/v1/bg/redeem_code/send_msg',
    method: 'post',
    data: data
  })
}

/**
 * 兑换码验证
 * @param {String} redeemCode 兑换码
 */
export function postRedeemCodeCheck(redeemCode) {
  return axios.request({
    url: '/api/auth/wechat/v1/bg/redeem_code/check',
    method: 'get',
    params: {redeem_code: redeemCode},
  })
}
