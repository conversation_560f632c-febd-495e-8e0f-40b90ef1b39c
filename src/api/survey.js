import axios from '@/utils/api.request'

/**
 * 满意度问卷
 * @param {Object} data 请求参数
 */
export function getReportSatisfied(data = {}) {
  return axios.request({
    url: '/api/v1/diabetes/report/satisfied',
    method: 'post',
    data
  })
}

export function getUserInfo(data = {}) {
  return axios.request({
    url: '/api/v1/diabetes/user_info',
    method: 'get',
    params: data
  })
}

export function getArticleInfo(data = {}) {
  return axios.request({
    url: '/api/wechat/v2/article_info',
    method: 'get',
    params: data
  })
}

export function addArticleHelpful(data = {}) {
  return axios.request({
    url: '/api/wechat/v2/add_article_helpful',
    method: 'post',
    data
  })
}
