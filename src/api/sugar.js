import axios from '@/utils/api.request'

/**
 * 获取某一天的血糖数据
 * @param {String} date 日期，例如：2020-04-30。格式Y-m-d
 */
export function bgOneDayData(date) {
    return axios.request({
        url: `/api/v4/bg/one-day/data`,
        method: 'get',
        params: { date }
    })
}

/**
 * 获取血糖趋势
 * @param {String} startDate 开始时间（例如：2020-04-02。格式Y-m-d）
 * @param {String} endDate 结束时间（例如：2020-05-12。格式Y-m-d）
 * @param {String} dinStatus 就餐状态（0：全部，1：空腹，2：早餐后，3：午餐前，4：午餐后，5：晚餐前，6：晚餐后，7：睡前，8：凌晨）
 */
export function bgGraphData(startDate, endDate, dinStatus) {
    return axios.request({
        url: `/api/v4/bg/graph/data`,
        method: 'get',
        params: {
            start_date: startDate,
            end_date: endDate,
            din_status: dinStatus
        }
    })
}

/**
 * 管家血糖7日报告
 */
export function getGuangjiaBgDateRecords(startDate) {
  return axios.request({
    url: `/api/v4/bg/record_report`,
    method: 'get',
    params: {
      start_date: startDate
    }
  })
}

/**
 * 获取控糖日历table list
 */
export function getTableRecords(data) {
    return axios.request({
        url: `/api/health/v1/bg/table_records`,
        method: 'get',
        params: data
    })
}

/**
 * 获取血糖记录明细列表
 */
export function getDetailRecords(data) {
    return axios.request({
        url: `/api/health/v1/bg/records`,
        method: 'get',
        params: data
    })
}

/**
 * 获取血糖记录明细列表
 */
export function getDetailM90Records(data) {
  return axios.request({
    url: `/api/v4/bg/records`,
    method: 'get',
    params: data
  })
}

/**
 * 获取血糖趋势
 * @param {String} endDate 结束时间（例如：2020-05-12。格式Y-m-d）
 * @param {String} dinStatus 就餐状态（0：全部，1：空腹，2：早餐后，3：午餐前，4：午餐后，5：晚餐前，6：晚餐后，7：睡前，8：凌晨）
 */
export function bgQSGraphData(endDate, dinStatus) {
    return axios.request({
        url: `/api/health/v1/bg/record_trend`,
        method: 'get',
        params: {
            end_date: endDate,
            status: dinStatus
        }
    })
}

/**
 * 血糖7日报告
 */
export function getRecordReport(endDate) {
    return axios.request({
        url: `/api/health/v1/bg/record_report`,
        method: 'get',
        params: {
            end_date: endDate
        }
    })
}

/**
 * 血糖7日报告
 */
export function getBgRecord(recordId) {
    return axios.request({
        url: `/api/health/v1/bg/record`,
        method: 'get',
        params: {
            record_id: recordId
        }
    })
}

/**
 * 血糖7日报告
 */
export function getBgDateRecords(startDate) {
    return axios.request({
        url: `/api/health/v1/bg/date_records`,
        method: 'get',
        params: {
            start_date: startDate
        }
    })
}

/**
 * 获取健康力解锁模块
 */
export function getHealthUnlock() {
  return axios.request({
    url: `/api/v1/zzdiabetes/health/unlock`,
    method: 'get'
  })
}

