import axios from '@/utils/api.request'

export function getExchangeCode(data = {}) {
  let httpInfo = {
    url: '/api/v1/national/exchange/code',
    method: 'post',

  }
  return axios.request(httpInfo)
}



export function hideMission(data) {
  let httpInfo = {
    url: '/api/mission/v1/user/mission/hide',
    method: 'post',
    data

  }
  return axios.request(httpInfo)
}

/**
 * 获取用药提醒任务状态
 */
export function detailMission() {
  let httpInfo = {
    url: '/api/mission/v1/user/mission/detail',
    method: 'get'
  }
  return axios.request(httpInfo)
}
