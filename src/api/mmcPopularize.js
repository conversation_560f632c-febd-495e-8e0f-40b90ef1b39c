import axios from '@/utils/api.request'

/**
 * mmc活动
 * @param {Object} data 请求参数
 */
export function getUserInfo(data) {
  return axios.request({
    url: '/api/v3/activity/user/join',
    method: 'get',
    params: data
  })
}

// 参加活动
export function joinActivity(data) {
  return axios.request({
    url: '/api/v3/activity/user/join',
    method: 'post',
    data
  })
}

// 控糖文章列表
export function getList(data) {
  return axios.request({
    url: '/api/v3/activity/article?type=11&pageSize=12',
    method: 'get',
    params: data
  })
}

// 控糖文章已读
export function articleRead(data) {
  return axios.request({
    url: '/api/v3/activity/article/read',
    method: 'post',
    data: data
  })
}

// 填写地址提交
export function submitUserInfo(data) {
  return axios.request({
    url: '/api/v3/activity/user/address',
    method: 'post',
    data
  })
}

// 问卷提交
export function submitQuestion(data) {
  return axios.request({
    url: '/api/v3/activity/join',
    method: 'post',
    data
  })
}
