/*
 * @Descripttion: 
 * @version: 
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-09 16:13:00
 * @LastEditors: l<PERSON><PERSON><PERSON>n
 * @LastEditTime: 2021-08-06 09:55:53
 */
import axios from '@/utils/workroom_api.request.js'
/**
 * 个人运营报告
 */
export function singleReport(data) {
  return axios.request({
    url: '/api/studio/v1/doc/operation_report',
    method: 'get',
    params: data
  })
}

/**
 * 医护团队个人运营报告
 */
export function teamSingleReport(data) {
  return axios.request({
    url: '/api/studio/v1/team/member/operation_report',
    method: 'get',
    params: data
  })
}

/**
 * 医护团队运营报告
 */
export function teamReport(data) {
  return axios.request({
    url: '/api/studio/v1/team/operation_report',
    method: 'get',
    params: data
  })
}

/**
 * mmc tab 个人周报
 */
export function mmcSingleReport(data) {
  return axios.request({
    url: '/api/studio/v2/report/personal',
    method: 'get',
    params: data
  })
}
/**
 * mmc tab 团队周报
 */
export function mmcTeamReport(data) {
  return axios.request({
    url: '/api/studio/v2/report/team',
    method: 'get',
    params: data
  })
}

/**
 * mmc tab 团队个人周报
 * @param {}} data 
 * @returns 
 */
export function mmcTeamSingleReport(data) {
  return axios.request({
    url: '/api/studio/v2/report/team/personal',
    method: 'get',
    params: data
  })
}

/**
 * 完整档案 -- 生命体征
 */
export function medicalSignFun(data) {
  return axios.request({
    url: '/api/medical/ihec/signs',
    method: 'get',
    params: data
  })
}
/**
 * 完整档案 --- 眼底检查
 */
export function medicalAuxInfoFun(data) {
  return axios.request({
    url: '/api/medical/auxInfo',
    method: 'get',
    params: data
  })
}

/**
 * 完整档案 --- 辅助检查离列表（患者医院数据）
 */
export function auxiliaryExaminationFun(data) {
  return axios.request({
    url: '/api/studio/v2/patient/auxiliary/examination',
    method: 'get',
    params: data
  })
}
/**
 * 完整档案 --- 动脉硬化检测详情（患者医院数据）
 */
export function reportDetailFun(data) {
  return axios.request({
    url: '/api/studio/v2/patient/as/report/detail',
    method: 'get',
    params: data
  })
}
/**
 * 完整档案 --- 动脉硬化检测图片（患者医院数据）
 */
export function reportImgFun(data) {
  return axios.request({
    url: '/api/studio/v2/patient/as/report/img',
    method: 'get',
    params: data
  })
}

// 用户信息 --- 权限
export function userInfoFun(data) {
  return axios.request({
    url: '/api/doc/user/info',
    method: 'get',
    params: data
  })
}

//科室报告的 患者数量
export function getWorkroomData(data) {
  return axios.request({
    url: '/api/studio/v1/dept/member/operation_report',
    method: 'get',
    params: data
  })
}
