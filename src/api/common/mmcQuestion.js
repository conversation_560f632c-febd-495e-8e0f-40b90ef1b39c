/*
 * @Descripttion: MMC问卷接口
 * @version:
 * @Author: guxiang
 * @Date: 2021年12月24日10:04:43
 * @LastEditors: guxiang
 * @LastEditTime: 2021年12月24日10:04:43
 */
import mmcReportAxios from "@/utils/mmc_request.js";

// 问卷列表
export const api = {
  questionSave(data) {
    return mmcReportAxios.request({
      url: "/api/patient/v1/question",
      method: "POST",
      data,
    });
  },
  questionList(data) {
    return mmcReportAxios.request({
      url: "/api/patient/v1/question",
      method: "GET",
      params: data,
    });
  },
  //访视阶段
  visitQuestionList(data) {
    return mmcReportAxios.request({
      url: "/api/patient/v1/visit/question",
      method: "GET",
      params: data,
    });
  },
  //访视用户信息
  visitBasicinfo(data) {
    return mmcReportAxios.request({
      url: "/api/patient/v1/visit/basicinfo",
      method: "GET",
      params: data,
    });
  },
  //中间状态页
  getMiddleStatus(data) {
    return mmcReportAxios.request({
      url: `/api/patient/v1/visit/complete`,
      method: "GET",
      params: data
    });
  }
};

