import wrokroomAxios from '@/utils/workroom_api.request.js'

/**
 * 个人主页-简历、排班
 * @param {String} workRoomId 工作室id
 */
export function userResum(workRoomId) {
  return wrokroomAxios.request({
    url: `/api/studio/v1/user/resume`,
    method: 'get',
    params: {
      workroom_id: workRoomId
    }
  })
}

/**
 * 个人主页-简历、排班-修改
 * @param {Object} data 保存数据
 *  workroom_id 工作室id 必填
 *  intro 医生简介 非必填
 *  expert 医生擅长 非必填
 *  clinic_addr 门诊地址 非必填
 *  clinic_time 坐诊时间 非必填
 */
export function userResumSave(data) {
  return wrokroomAxios.request({
    url: `/api/studio/v1/user/resume/save`,
    method: 'post',
    data
  })
}
/**
 * 个人主页-设置图文
 * @param {String} workRoomId 工作室id
 */
 export function getService(workRoomId) {
  return wrokroomAxios.request({
    url: `/api/ehosp/v1/doc/service/tw`,
    method: 'get',
    params: {
      workroom_id: workRoomId
    }
  })
}