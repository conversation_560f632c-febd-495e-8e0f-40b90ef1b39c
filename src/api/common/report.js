import wrokroomAxios from '@/utils/workroom_api.request.js'
import axios from '@/utils/api.request'

/**
 * 患者报告
 * @param {String} userId 用户ID
 */
export function reportData(userId) {
  return wrokroomAxios.request({
    url: `/api/studio/v1/report/report`,
    method: 'get',
    params: {
      user_id: userId
    }
  })
}

/**
 * 患者报告-体重
 * @param {Object} params 请求数据
 *  user_id 用户ID
 *  start_date 开始时间
 *  end_date 结束时间
 */
export function reportWeight(params) {
  return wrokroomAxios.request({
    url: `/api/studio/v1/report/weight`,
    method: 'get',
    params
  })
}

/**
 * 患者报告-运动
 * @param {Object} params 请求数据
 *  user_id 用户ID
 *  start_date 开始时间
 *  end_date 结束时间
 */
export function reportMotion(params) {
  return wrokroomAxios.request({
    url: `/api/studio/v1/report/motion`,
    method: 'get',
    params
  })
}

/**
 * 患者报告-血压
 * @param {Object} params 请求数据
 *  user_id 用户ID
 *  start_date 开始时间
 *  end_date 结束时间
 */
export function reportBp(params) {
  return wrokroomAxios.request({
    url: `/api/studio/v1/report/bp`,
    method: 'get',
    params
  })
}

/**
 * 患者报告-血糖
 * @param {Object} params 请求数据
 *  user_id 用户ID
 *  start_date 开始时间
 *  end_date 结束时间
 */
export function reportBg(params) {
  return wrokroomAxios.request({
    url: `/api/studio/v1/report/bg`,
    method: 'get',
    params
  })
}


export function hospTheoryReportBp(params) {
  return axios.request({
    url: `/api/v1/wechat/yl/report`,
    method: 'get',
    params
  })
}

// MMC患者病历 - 患者信息
export function getMedicalUserInfo(params) {
  return wrokroomAxios.request({
    url: 'api/medical/patient_basic_info',
    method: 'get',
    params
  })
}

// MMC患者病历 - 异常提醒
export function getMedicalAbnormalInfo(params) {
  return wrokroomAxios.request({
    // url: 'api/medical/patient_abnormal_info',
    url: 'api/medical/patient_abnormal_info/v2',
    method: 'get',
    params
  })
}
// MMC患者病历 - 现病史、既往史
export function getMedicalHistories(params) {
  return wrokroomAxios.request({
    url: 'api/medical/histories',
    method: 'get',
    params
  })
}

// MMC患者病历 - 生命体征
export function getMedicalsigns(params) {
  return wrokroomAxios.request({
    url: 'api/medical/signs',
    method: 'get',
    params
  })
}

// MMC患者病历 - 核心指标
export function getMedicalIndicators(params) {
  return wrokroomAxios.request({
    url: 'api/medical/indicators',
    method: 'get',
    params
  })
}

// MMC患者病历 - 检查记录
export function getMedicalLaboratory(params) {
  return wrokroomAxios.request({
    url: 'api/medical/laboratory',
    method: 'get',
    params
  })
}