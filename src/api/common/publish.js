import axios from '@/utils/api.request'
import axiosDoc from '@/utils/doc_api.request.js'

/**
 * 医生动态
 * @param {Object} data 请求数据
 *  doc_id 医生ID 选填
 *  page_no 页码 必填
 *  page_size 数量 必填
 */
export function doctorActivityList(data) {
  return axios.request({
    url: `/api/mixed/v1/post/list`,
    method: 'get',
    params: data
  })
}

/**
 * 动态详情
 * @param {String} doctorId 医生ID
 */
export function activityDetail(doctorId = '') {
  return axios.request({
    url: `/api/mixed/v1/post/detail/${doctorId}`,
    method: 'get'
  })
}
/**
 * 分享的动态详情
 * @param {String} doctorId 动态Id
 */
 export function shareActivityDetail(doctorId = '', source='') {
  return axiosDoc.request({
    url: `/api/v1/doc/share/post/${doctorId}?source=${source}`,
    method: 'get'
  })
}

/**
 * 医生信息
 * @param {String} doctorId 医生ID
 */
export function doctorInfo(doctorId = '') {
  return axios.request({
    url: `/api/mixed/v1/doc/index`,
    method: 'get',
    params: {
      doc_id: doctorId
    }
  })
}

/**
 * 医生简历、排班
 * @param {String} doctorId 医生ID
 */
export function doctorShow(doctorId) {
  return axios.request({
    url: `/api/mixed/v1/doc/show`,
    method: 'get',
    params: {
      doc_id: doctorId
    }
  })
}

/**
 * 发布通知-获取通知内容
 * @param {String} noticeId 通知ID
 */
export function noticeDetail(noticeId) {
  return axiosDoc.request({
    url: `/api/studio/v1/notice/detail/${noticeId}`,
    method: 'get'
  })
}
// 新的  发布通知-获取通知内容  同上，但是调用的是新的 patient-api接口
export function noticeDetailNew(noticeId) {
  return axios.request({
    url: `/api/v1/wechat/doc/notice/${noticeId}/detail`,
    method: 'get'
  })
}

/**
 * 发布通知-更新阅读状态
 * @param {String} noticeId 通知ID
 * @param {String} userId 用户ID
 */
export function noticeChangeStatus({noticeId, userId}) {
  return axiosDoc.request({
    url: `/api/studio/v1/notice/change/status`,
    method: 'post',
    data: {
      notice_id: noticeId,
      user_id: userId
    }
  })
}

/**
 * 关注医生列表
 * @param {Object} data 请求数据
 *  doc_id 医生ID 选填
 *  page_no 页码 必填
 *  page_size 数量 必填
 */
export function getAttachDocList(data) {
  return axios.request({
    url: `/api/mixed/v1/doc/search`,
    method: 'get',
    params: data
  })
}

/**
 * 搜索医生列表
 * @param {Object} data 请求数据
 *  doc_id 医生ID 选填
 *  page_no 页码 必填
 *  page_size 数量 必填
 */
export function searchDocList(data) {
  return axios.request({
    url: `/api/mixed/v1/doc/search`,
    method: 'get',
    params: data
  })
}


/**
 * 搜索排班按钮是否展示
 * @param {Object} data 请求数据
 *  doc_id 医生ID 选填
 *  page_no 页码 必填
 *  page_size 数量 必填
 */
export function searchScheduleList(data) {
  return axios.request({
    url: `/api/v3/hosp/schedule/list`,
    method: 'get',
    params: data
  })
}

/**
 * 医生关注和取消关注接口
 * @param {String} type 1 关注 2取消关注
 * @param {String} docId 用户ID
 */
export function docFollowDetachStatusChange(type, docId) {
  if (type == 1) {
    return axios.request({
      url: `/api/mixed/v1/doc/attach`,
      method: 'post',
      data: {
        doc_id: docId
      }
    })
  } else {
    return axios.request({
      url: `/api/mixed/v1/doc/detach`,
      method: 'post',
      data: {
        doc_id: docId
      }
    })
  }
}

/**
 * 医生动态关注和取消关注接口
 * @param {String} type 1 关注 2取消关注
 * @param {String} id 用户ID
 */
export function newsDetachStatusChange(type, id) {
  if (type == 1) {
    return axios.request({
      url: `/api/mixed/v1/post/attach`,
      method: 'post',
      data: {
        post_id: id
      }
    })
  } else {
    return axios.request({
      url: `/api/mixed/v1/post/detach`,
      method: 'post',
      data: {
        post_id: id
      }
    })
  }
}

// 新的  发布通知-获取通知内容  同上，但是调用的是新的 patient-api接口
export function getWechatJsSign(url) {
  return axios.request({
    url: `/api/v1/wechat/js_api/sign`,
    method: 'post',
    data: {
      url: url
    }
  })
}
