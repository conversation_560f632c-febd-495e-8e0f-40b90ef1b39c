import axiosDoc from '@/utils/doc_api.request.js'




//获取项目目录
export function getInformationList(data) {
  return axiosDoc.request({
    url: `/api/ehosp/v1/applied/project/information/list?column_id=${data}`,
    method: 'get',
    data
  })
}


//获取项目详情
export function getProjectDetail(data) {
  return axiosDoc.request({
    url: `/api/ehosp/v1/applied/project/detail?project_id=${data}`,
    method: 'get',
    data
  })
}

//获取项目结果
export function getProjectResult(data) {
  return axiosDoc.request({
    url: `/api/ehosp/v1/applied/project/apply/result?project_id=${data}`,
    method: 'get',
    data
  })
}
// 新 获取项目首页数据
export function newGetProDetail(data) {
  return axiosDoc.request({
    url: `/api/ehosp/v1/applied/project/new/detail?project_id=${data}`,
    method: 'get',
    data
  })
}
// 获取培训平台链接
export function getTrainingPlatformUrl(data) {
  return axiosDoc.request({
    url: `/api/studio/v1/edu/info?project_id=${data}`,
    method: 'get',
    data
  })
}
// 获取项目报名前数据 不需要token
export function getDefaultProDetail(data) {
  return axiosDoc.request({
    url: `/api/ehosp/v1/applied/project/new/detail_info?project_id=${data}`,
    method: 'get',
    data
  })
}
