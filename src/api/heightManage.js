import axios from '@/utils/api.request'

export function getHeightInfo(data = {}) {
  return axios.request({
    url: '/api/health/v1/weight/last',
    method: 'get'
  })
}

export function getHeightChat(data = {}) {
  return axios.request({
    url: '/api/health/v1/weight/chart',
    method: 'get'
  })
}

export function getHeightList(data = {}) {
  return axios.request({
    url: '/api/health/v1/weight/history',
    method: 'get',
    params: data
  })
}



export function getHeightInfoM90(data = {}) {
  return axios.request({
    url: '/api/v4/bmi/last-one/data',
    method: 'get'
  })
}
export function getHeightInfoFree(data = {}) {
  return axios.request({
    url: '/api/v5/height_weight/last_data',
    method: 'get'
  })
}

export function getHeightChatM90(data = {}) {
  return axios.request({
    url: '/api/v5/height_weight/history',
    method: 'get',
    params: data
  })
}

export function getHeightListM90(data = {}) {
  return axios.request({
    url: '/api/v5/height_weight/list',
    method: 'get',
    params: data
  })
}
