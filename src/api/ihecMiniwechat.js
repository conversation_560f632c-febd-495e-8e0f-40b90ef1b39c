import axios from '@/utils/api.request'
export function getQuestionnairePros(data = {}) {
  return axios.request({
    url: '/api/survey/v1/workrooms?dept_workroom_id=' + data,
    method: 'get',
  })
}
export function getExpressList(data = {}) {
  return axios.request({
    url: '/api/ehosp/v1/service-package/express-list',
    method: 'get',
    params: data
  })
}
export function getQuestionnaireList(data = {}) {
  return axios.request({
    url: '/api/survey/v1/survey_list?workroom_id=' + data,
    method: 'get',
  })
}
export function getQuestionnaireUrl(data = {}) {
  return axios.request({
    url: '/api/survey/v1/survey_url',
    method: 'get',
    params: data
  })
}
export function saveBp(data = {}) {
  return axios.request({
    url: '/api/v1/wechat/bp/upload',
    method: 'post',
    data
  })
}
export function saveBf(data = {}) {
  return axios.request({
    url: '/api/health/v1/user/bloodfat/save',
    method: 'post',
    data
  })
}
export function saveHW(data = {}) {
  return axios.request({
    url: '/api/v1/wechat/weight/save',
    method: 'post',
    data
  })
}
export function saveBasicInfo(data = {}) {
  return axios.request({
    url: '/api/v1/wechat/information/set_info_from_health_record',
    method: 'post',
    data
  })
}
export function getConfig(data = {}) {
  return axios.request({
    url: '/api/wechat/v3/get_service_package_form_show',
    method: 'get',
    params: data
  })
}
export function getPatientInfo(data = {}) {
  return axios.request({
    url: '/api/wechat/v3/patient_info',
    method: 'get',
    params: data
  })
}
export function saveSpo2(data = {}) {
  return axios.request({
    url: '/api/v1/wechat/breathe/spo2/save',
    method: 'post',
    data
  })
}
export function saveBs(data = {}) {
  return axios.request({
    url: '/api/v1/wechat/bg/save_bg_data',
    method: 'post',
    data
  })
}
export function savePatient(data = {}) {
  return axios.request({
    url: '/api/wechat/v3/save_patient',
    method: 'post',
    data
  })
}
export function getMedicationUrl(data = {}) {
  return axios.request({
    url: '/api/drug/v1/drug_url',
    method: 'get',
    params: data
  })
}
export function getBenefitActivityDetail(data = {}) {
  return axios.request({
    url: '/api/activity/v1/benefit/activity/get_benefit_activity_detail',
    method: 'get',
    params: data
  })
}
export function getBenefitActivityList(data = {}) {
  return axios.request({
    url: '/api/activity/v1/benefit/activity/get_benefit_activity_list',
    method: 'get',
    params: data
  })
}
export function getActivityStoreList(data = {}) {
  return axios.request({
    url: '/api/activity/v1/benefit/activity/store/list',
    method: 'get',
    params: data
  })
}
export function activityUserJoin(data = {}) {
  return axios.request({
    url: '/api/activity/v1/benefit/activity/user/join',
    method: 'post',
    data
  })
}
// 省市区
export function getLocationList(data = {}) {
  return axios.request({
    url: '/api/ehosp/v1/location/list',
    method: 'get',
    params: data
  })
}