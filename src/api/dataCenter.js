import wrokroomAxios from '@/utils/workroom_api.request.js'

/**
 * 医院数据
 */
export function getihecHospitalData(data) {
    return wrokroomAxios.request({
        url: `/api/studio/v1/patient/bp/list`,
        method: 'get',
        params: data
    })
}
/**
 * mmc 数据中心
 */
export function getmmcDataCenter(data) {
    return wrokroomAxios.request({
        url: '/api/studio/v2/report/personal',
        method: 'get',
        params: data
    })
}

/**
 * ihec 数据中心
 */
export function getihecDataCenter(data) {
    return wrokroomAxios.request({
        url: '/api/studio/v2/panel/patient_stats',
        method: 'get',
        params: data
    })
}