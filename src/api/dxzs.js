import axios from '@/utils/api.request'

export function getSaveData(data) {
  return axios.request({
    url: '/api/v3/bolise/save',
    method: 'post',
    data: data
  })
}

export function getQuestionUserInfo() {
  return axios.request({
    url: '/api/vipdiabetes/v1/questionnaire/user/info',
    method: 'get'
  })
}

export function shareGuoshouImage(data) {
  return axios.request({
    url: '/api/v1/national/metabolise/share',
    method: 'get',
    params: data
  })
}

/**
 * 二型糖尿病问卷结果
 * @param {Object} data
 */
export function questionSecondBlood(data) {
  return axios.request({
    url: '/api/v1/wechat/question/diabetes_question_notvalidate',
    method: 'post',
    data: data
  })
}


/**
 * 国人缺血性心血管病(ICVD)风险评估接口
 * @param {Object} data
 */
export function questionIcvdquestion(data) {
  return axios.request({
    url: '/api/v4/question/icvd_question',
    method: 'post',
    data: data
  })
}
