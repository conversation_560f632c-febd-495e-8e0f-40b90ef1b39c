import axios from '@/utils/api.request'

const healthCoreUrl = process.env.VUE_APP_HEALTH_CORE_URL

/**
 * 获取健康报告首页数据
 * @param {Object} data 请求参数
 */
export function getVisitReport(data = {}) {
  return axios.request({
    url: '/api/v3/health/report',
    method: 'get',
    params: data
  })
}

/**
 * 获取健康报告列表数据
 */
export function getReportList() {
  return axios.request({
    url: '/api/v3/report/list',
    method: 'get'
  })
}

/**
 * 获取代谢指数分数
 * @param {Object} data 请求参数
 */
export function getMetaboliseScore(data = {}) {
  return axios.request({
    url: '/api/v3/metabolise/score',
    method: 'get',
    params: data
  })
}

/**
 * 获取检查报告数据
 * @param {Object} data 请求参数
 */
export function getExamData(data) {
  const params = {}
  if (data.visit_at) params.visit_at = data.visit_at
  if (data.hosp_id) params.hosp_id = data.hosp_id
  return axios.request({
    url: `/api/v3/${data.checkItem}/visit`,
    method: 'get',
    params
  })
}

/**
 * 获取曲线图数据
 * @param {Object} data 请求参数
 */
export function getPeriodData(data) {
  return axios.request({
    url: `/api/v3/${data.checkItem}/change`,
    method: 'get',
    params: data.params
  })
}

/**
 * 获取曲线图数据
 * @param {Object} data 请求参数
 */
export function getVisitHistory() {
  return axios.request({
    url: `/api/v3/user/visithistory`,
    method: 'get'
  })
}

/**
 * 心超心电图详情
 * @param {Object} data 请求参数
 */
export function getHeartData(data) {
  const params = {}
  if (data.record_id) params.record_id = data.record_id
  return axios.request({
    url: `/api/v3/${data.checkItem}/health`,
    method: 'get',
    params
  })
}

export function getHealthCoreUrl(userId, type) {
  return healthCoreUrl + '?user_id=' + userId + '&type=' + type
}
