import axios from '@/utils/api.request'
/**
 * 获取食谱
 */
export function getCookBook(data) {
  return axios.request({
    url: '/api/health/v1/recipes/info',
    method: 'get',
    params: data
  })
}

/**
 * 获取打卡情况
 */
export function getCheckInDetails(data) {
  return axios.request({
    url: '/api/mission/v1/complete/info',
    method: 'get',
    params: data
  })
}
/**
 * 获取打卡情况
 */
export function getFirstOpen(data) {
  return axios.request({
    url: '/api/mission/v1/complete/recipes',
    method: 'get',
    params: data
  })
}
/**
 * 日历初始化
 */
export function getDateInit(data) {
  return axios.request({
    url: '/api/health/v1/recipes/init',
    method: 'get',
    params: data
  })
}

/**
 * 估算重量
 */
export function getFoodList(data) {
  return axios.request({
    url: '/api/health/v1/food/estimation',
    method: 'get',
    params: data
  })
}

// 食物替换
export function changeFood(data) {
  return axios.request({
    url: '/api/health/v1/recipes/replace',
    method: 'get',
    params: data
  })
}

// 食物详情
export function foodDetail(data) {
  return axios.request({
    url: '/api/health/v1/food/detail',
    method: 'get',
    params: data
  })
}

// 食物替换
export function resetFood(data) {
  return axios.request({
    url: '/api/health/v1/recipes/replace',
    method: 'post',
    data
  })
}

// 打卡
export function checkInFood(data) {
  return axios.request({
    url: '/api/health/v1/recipes/dineontime',
    method: 'post',
    data
  })
}
