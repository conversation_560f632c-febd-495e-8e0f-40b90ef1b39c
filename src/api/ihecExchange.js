import axios from '@/utils/api.request'
export function getSendCode(data = {}) {
  return axios.request({
    url: '/api/v3/bp_redeem_code_send_sms',
    method: 'post',
    data
  })
}

export function getRedeemCode(data = {}) {
  return axios.request({
    url: '/api/v3/bp_redeem_code',
    method: 'post',
    data
  })
}

export function getRedeemCodev3(data = {}) {
  return axios.request({
    url: '/api/auth/wechat/v1/bg/redeem_code/check',
    method: 'get',
    params: data
  })
}

export function getSendCodev3(data = {}) {
  return axios.request({
    url: '/api/auth/wechat/v1/bg/redeem_code/send_msg',
    method: 'post',
    data
  })
}

export function saveQrcodeUser(data = {}) {
  return axios.request({
    url: '/api/v3/qrcode_user_save',
    method: 'post',
    data
  })
}

export function getQrcodeYouUrl(data = {}) {
  return axios.request({
    url: '/api/v3/qrcode_youzan_url',
    method: 'get',
    params: data
  })
}

export function getUserOpenid(data = {}) {
  return axios.request({
    url: '/api/v3/qrcode_youzan/code_session',
    method: 'get',
    params: data
  })
}

export function getRedeemCodeOld(data = {}) {
  return axios.request({
    url: '/api/v3/bp_redeem_code_old',
    method: 'post',
    data
  })
}

export function postRedeemCodeUse(data = {}) {
  return axios.request({
    url: '/api/auth/wechat/v1/bg/redeem_code/check',
    method: 'post',
    data
  })
}

export function postRedeemCodeUseV3(data = {}) {
  return axios.request({
    url: '/api/auth/wechat/v1/bg/redeem_code/use',
    method: 'post',
    data
  })
}
