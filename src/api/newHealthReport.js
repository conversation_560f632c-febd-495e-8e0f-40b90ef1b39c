import axios from '@/utils/api.request'

/**
 * 获取全部指标分组
 */
export function getMetricsGroup() {
  return axios.request({
    url: '/api/v4/hospital/metrics_group',
    method: 'get'
  })
}

/**
 * 获取指标详细
 * @param {Number} metricsCode 指标分组编号(有次参数时取此参数对应指标组下所有指标值，无此参数时取默认指标组下的指标值)
 */
export function getMetricsDetail(metricsCode = '') {
  let params = {}
  metricsCode !== '' && (params.metrics_code = metricsCode)
  return axios.request({
    url: '/api/v4/hospital/metrics_detail',
    method: 'get',
    params
  })
}

/**
 * 核心指标趋势图表
 * @param {String} code 指标分组编号
 */
export function trendChart(code) {
  return axios.request({
    url: '/api/v4/hospital/trendchart',
    method: 'get',
    params: { code }
  })
}

/**
 * 历次就诊数据详情
 * @param {String} id 报告ID
 */
export function reportDetail(id) {
  return axios.request({
    url: '/api/v4/hospital/report_detail',
    method: 'get',
    params: { record_id: id }
  })
}

/**
 * 获取用户上传的检查报告
 * @param {String} id 报告ID
 */
export function getUploadReport(id) {
  return axios.request({
    url: '/api/v4/hospital/get_upload_report',
    method: 'get',
    params: { record_id: id }
  })
}

/**
 * 用户上传检查报告
 * @param {String} data 请求参数(img[]: 用户上传的报告图片地址; record_id: 需要关联的已有的医院检查报告ID; id: 非必须，如果有传值为修改已有记录，如果没此参数为新加报告记录)
 */
export function uploadReport(data) {
  return axios.request({
    url: '/api/v4/hospital/upload_report',
    method: 'post',
    data
  })
}

/**
 * 获取血糖的状态
 * @param {String} id 血糖数据ID
 */
export function bgStatus(id) {
  return axios.request({
    url: `/api/v4/bg/status/${id}`,
    method: 'get'
  })
}

/**
 * 获取血压的状态
 * @param {String} id 血压数据ID
 */
export function bpStatus(id) {
  return axios.request({
    url: `/api/v4/bp/status/${id}`,
    method: 'get'
  })
}

/**
 * 获取运动步数
 */
export function motionLastOneData() {
  return axios.request({
    url: `/api/v4/motion/last-one/data`,
    method: 'get'
  })
}

/**
 * 获取BMI测量结果
 */
export function bmiLastOneData() {
  return axios.request({
    url: `/api/v4/bmi/last-one/data`,
    method: 'get'
  })
}
