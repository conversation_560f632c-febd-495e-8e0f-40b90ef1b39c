import axios from '@/utils/api.request'
import axiosDoc from '@/utils/doc_api.request.js'
/**
 * 获取往期报告列表
 * @param {String} page 当前页
 * @param {String} perPage 每一页数据条数
 */
export function weeklyReportList(page) {
  return axios.request({
    url: '/api/v4/home/<USER>/report/list',
    method: 'get',
    params: {per_page: 5, page: page}
  })
}

/**
 * 获取往期报告详情
 * @param {String} id 周报的id
 */
export function weeklyReportDetail(id) {
  return axios.request({
    url: `/api/v4/home/<USER>/report/detail/${id}`,
    method: 'get'
  })
}

/**
 * 获取某一天的血糖数据
 * @param {String} date 日期，例如：2020-04-30。格式Y-m-d
 */
export function bgOneDyData(date) {
  return axios.request({
    url: `/api/v4/bg/one-day/data`,
    method: 'get',
    params: {date}
  })
}

/**
 * 获取血糖趋势
 * @param {String} startDate 开始时间（例如：2020-04-02。格式Y-m-d）
 * @param {String} endDate 结束时间（例如：2020-05-12。格式Y-m-d）
 * @param {String} dinStatus 就餐状态（0：全部，1：空腹，2：早餐后，3：午餐前，4：午餐后，5：晚餐前，6：晚餐后，7：睡前，8：凌晨）
 */
export function bgGraphData(startDate, endDate, dinStatus) {
  return axios.request({
    url: `/api/v4/bg/graph/data`,
    method: 'get',
    params: {
      start_date: startDate,
      end_date: endDate,
      din_status: dinStatus
    }
  })
}

/**
 * 获取血压图谱
 * @param {String} startDate 开始时间（例如：2020-04-02。格式Y-m-d）
 * @param {String} endDate 结束时间（例如：2020-05-12。格式Y-m-d）
 */
export function bpGraphData(startDate, endDate) {
  return axios.request({
    url: `/api/v4/bp/graph/data`,
    method: 'get',
    params: {
      start_date: startDate,
      end_date: endDate
    }
  })
}

/**
 * 获取血压报告
 * @param {String} id 报告id
 */
export function getBpData(id) {
  return axios.request({
    url: `/api/v4/bp/report/detail/${id}`,
    method: 'get'
  })
}

/**
 * 管家血呀7日报告
 */
export function getGuangjiaBpDateRecords(startDate, endDate) {
  return axios.request({
    url: `/api/v4/bp/history`,
    method: 'get',
    params: {
      start_date: startDate,
      end_date: endDate
    }
  })
}

/**
 * 哮鸣音7日报告
 */
export function getWheezingRateDateRecords(userId,workroomId, startDate, endDate) {
  return axiosDoc.request({
    url: `/api/studio/v1/stat/patient/wheeze/report`,
    method: 'get',
    params: {
      workroom_id: workroomId,
      user_id: userId,
      start_date: startDate,
      end_date: endDate
    }
  })
}


/* 获取血糖健康报告
* @param {String} id 报告id
*/
export function bpReportData(id) {
  return axios.request({
    url: `/api/v4/bg/report/detail/${id}`,
    method: 'get'
  })
}

/**
 * 管家血糖7日报告
 */
export function getGuangjiaBgDateRecords(endDate) {
  return axios.request({
    url: `/api/v4/bg/record_report`,
    method: 'get',
    params: {
      end_date: endDate
    }
  })
}


export function reportBp(data) {
  return axios.request({
    url: `/api/v4/bp/map/list`,
    method: 'get',
    params: data
  })
}

export function readFood(data) {
  return axios.request({
    url: `/api/v1/zzdiabetes/task/recipes/save`,
    method: 'post',
    params: data
  })
}



