/*
 * @Descripttion: 智众医信
 * @version:
 * @Author: guxiang
 * @Date: 2021年12月24日10:04:43
 * @LastEditors: guxiang
 * @LastEditTime: 2021年12月24日10:04:43
 */
import saasReportAxios from '@/utils/saasReport.request.js'
import winAppReportAxios from '@/utils/win_app_api.request.js'
import saasAxios from '@/utils/saas.api.request.js'
// 参加活动
export function reportProduce(data) {
    return saasReportAxios.request({
        url: '/api/rc/data/produce',
        method: 'post',
        data
    })
}

// 获取眼底报告
export function getEyeReport(data) {
  return winAppReportAxios.request({
    url: '/api/v1/eye/encode/get_user_yandi_report',
    method: 'post',
    data
  })
}
 
// saas获取眼底报告
export function getSaasEyeReport(data) {
  return saasAxios.request({
    url: `/api/web/v1/inspect/eye/report/info?token=${data.token}`,
    method: 'get',
  })
}
// vmc报告
export function getVMCDetail(params) {
  return saasAxios.request({
    url: `/api/inside/v1/report/vmc/detail`,
    method: 'get',
    params
  })
}