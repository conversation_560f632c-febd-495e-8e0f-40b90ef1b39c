import axios from '@/utils/api.request'

/**
 * 国寿用户添加微信
 */
export function nationalUserStore(data) {
  return axios.request({
    url: '/api/v1/national/user/store',
    method: 'post',
    data
  })
}

/**
 * 保存收集的用户信息
 * @param {Object} data 请求参数
 */
export function userBasicSave(data) {
  return axios.request({
    url: '/api/mission/v1/user/basic/save/v2',
    method: 'post',
    data
  })
}

/**
 * 获取收集的用户信息
 */
export function userBasicGet() {
  return axios.request({
    url: '/api/mission/v1/user/basic/get',
    method: 'get'
  })
}

/**
 * 用户是否收集过信息接口
 */
export function userBasicCheck() {
  return axios.request({
    url: '/api/mission/v1/user/basic/check/v2',
    method: 'get'
  })
}

export function dailyTask(data){

  return axios.request({
    url: '/api/v2/zzdiabetes/index',
    method: 'get'
  })
}


/**
 * 获取目标管理数据
 */
export function userTargetGet() {
  return axios.request({
    url: '/api/mission/v1/user/target/get',
    method: 'get'
  })
}

/**
 * 首页-医生提醒、控糖目标、控压目标、减重目标
 */
export function userIndexTarget() {
  return axios.request({
    url: '/api/mission/v1/user/index/target',
    method: 'get'
  })
}

/**
 * 首页-阶段、积分
 */
export function userProgress() {
  return axios.request({
    url: 'api/mission/v1/user/progress',
    method: 'get'
  })
}

/**
 * 首页-任务列表
 * @param {String} date 日期。格式：2020-06-02，如不传默认取当天
 */
export function missionProgress(date) {
  return axios.request({
    url: '/api/mission/v1/mission/progress',
    method: 'get',
    params: date
  })
}

/**
 * 首页-任务列表-运营任务（直播）
 * @param {String} date 日期。格式：2020-06-02，如不传默认取当天
 */
export function missionOperationProgress(date) {
  return axios.request({
    url: '/api/mission/v1/mission/operation/progress',
    method: 'get',
    params: date
  })
}

/**
 * 保存准备状态数据
 * @param {Number} readyStatus 准备状态 0-否 1-准备好
 */
export function userReadyData(readyStatus) {
  return axios.request({
    url: '/api/mission/v1/user/ready/data',
    method: 'post',
    data: {
      ready_status: readyStatus
    }
  })
}

/**
 * 国寿用户置换token接口
 * @param {String} uid 国寿用户ID
 */
export function nationalLifeLogin(uid) {
  return axios.request({
    url: '/api/auth/v1/national/life/login',
    method: 'get',
    params: {
      national_uid: uid
    }
  })
}

/**
 * 新手礼包-状态
 */
export function noviceActivityStatus() {
  return axios.request({
    url: '/api/activity/v1/novice/activity/status',
    method: 'get'
  })
}

/**
 * 新手礼包-基础信息
 */
export function noviceActivityInfo() {
  return axios.request({
    url: '/api/activity/v1/novice/activity/index',
    method: 'get'
  })
}

/**
 * 新手礼包-添加一条记录
 */
export function noviceActivitySave() {
  return axios.request({
    url: '/api/activity/v1/novice/activity/save',
    method: 'post'
  })
}
/**
 * MMC控糖助手检查是否填写量表
 */
export function judgeController() {
  return axios.request({
    url: '/api/v1/zzdiabetes/user/has_basic',
    method: 'get'
  })
}

