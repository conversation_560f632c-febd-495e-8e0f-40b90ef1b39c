/*
 * @Descripttion: 智众医信
 * @version:
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-09 16:43:49
 * @LastEditors: l<PERSON><PERSON><PERSON>n
 * @LastEditTime: 2021-08-07 13:24:03
 */
import saasAxios from '@/utils/saas.api.request.js'
import { sassFalse } from 'sass'


// 获取知情同意书
export function userAgreementApi(params) {
  return saasAxios.request({
    // url: `/api/web/v1/common/project/informed-consent`,
    url: '/api/web/v1/common/informed/consent',
    method: 'get',
    params
  })
}

/**
 * 医信与医生工作室对接
 */

// 血压图表
export function getBpChartApi(params) {
  return saasAxios.request({
    url: '/api/h5/v1/bp/chart',
    method: 'get',
    params
  })
}

// 血糖图表
export function getBgChartApi(params) {
  return saasAxios.request({
    url: '/api/h5/v1/bg/chart',
    method: 'get',
    params
  })
}

// 血脂图表
export function getTcChartApi(params) {
  return saasAxios.request({
    url: '/api/h5/v1/blood/fat/chart',
    method: 'get',
    params
  })
}

// 完整病例
export function getCompletePatientExampleApi(params) {
  return saasAxios.request({
    url: '/api/h5/v1/patient/case',
    method: 'get',
    params
  })
}

// 内皮检测
export function getFmdDetailApi(params) {
  return saasAxios.request({
    url: '/api/h5/v1/fmd/detail',
    method: 'get',
    params
  })
}

// 内脏脂肪检测
export function getVfDetailApi(params) {
  return saasAxios.request({
    url: '/api/h5/v1/vf/detail',
    method: 'get',
    params
  })
}

// 动脉硬化检测
export function getAsDetailApi(params) {
  return saasAxios.request({
    url: '/api/h5/v1/as/detail',
    method: 'get',
    params
  })
}
// 眼底检测
export function getEyebaseDetailApi(params) {
  return saasAxios.request({
    url: '/api/h5/v1/patient/eyebase/detail',
    method: 'get',
    params
  })
}

// 呼吸检测
export function getBreathDetailApi(params) {
  return saasAxios.request({
    url: '/api/h5/v1/patient/breathe/detail',
    method: 'get',
    params
  })
}
// 患者报告列表
export function getPatientReportApi(params) {
  return saasAxios.request({
    url: '/api/h5/v1/patient/report',
    method: 'get',
    params
  })
}
// 患者报告图片
export function getPatientReportDetailApi(params) {
  return saasAxios.request({
    url: '/api/h5/v1/patient/report/png',
    method: 'get',
    params,
    responseType: 'arraybuffer'
  })
}
// 问卷
export function getQuestionnaire(params) {
  return saasAxios.request({
    url: '/api/h5/v1/survey/visit/history',
    method: 'get',
    params
  })
}
// 问卷列表
export function getQuestionnaireSurvey(params) {
  return saasAxios.request({
    url: '/api/h5/v1/patient/current/survey',
    method: 'get',
    params
  })
}
// 问卷复制
export function getQuestionnaireSurveyCopy(data) {
  return saasAxios.request({
    url: '/api/h5/v1/patient/current/survey/copy',
    method: 'post',
    data
  })
}
// 入口配置
export function getEnter(params) {
  return saasAxios.request({
    url: '/api/h5/v1/workshop/func/list',
    method: 'get',
    params
  })
}

// PWV + ABI + TBI 列表
export function getAsLists(params) {
  return saasAxios.request({
    url: '/api/h5/v1/as/lists',
    method: 'get',
    params
  })
}
// 内脏脂肪 列表
export function getVfLists(params) {
  return saasAxios.request({
    url: '/api/h5/v1/vf/lists',
    method: 'get',
    params
  })
}
// 血管内皮 列表
export function getFmdLists(params) {
  return saasAxios.request({
    url: '/api/h5/v1/fmd/lists',
    method: 'get',
    params
  })
}
// 眼底检查 列表
export function getEyebaseLists(params) {
  return saasAxios.request({
    url: '/api/h5/v1/patient/eyebase/lists',
    method: 'get',
    params
  })
}
// 呼吸检查 列表
export function getBreatheLists(params) {
  return saasAxios.request({
    url: '/api/h5/v1/patient/breathe/lists',
    method: 'get',
    params
  })
}

// 心电图 列表
export function getEcgLists(params) {
  return saasAxios.request({
    url: '/api/h5/v1/ecg/list',
    method: 'get',
    params
  })
}

// 心电图 图表
export function getEcgChart(params) {
  return saasAxios.request({
    url: '/api/h5/v1/ecg/chart_data',
    method: 'get',
    params
  })
}

// 工作室 趋势图接口
export function getQuotaChart(params) {
  return saasAxios.request({
    url: '/api/h5/v1/major/quota/chart',
    method: 'get',
    params
  })
}

// 肝脏瞬时弹性硬度检测 列表
export function getLiverLists(params) {
  return saasAxios.request({
    url: '/api/h5/v1/patient/examination/liver/list',
    method: 'get',
    params
  })
}

// 肝脏瞬时弹性硬度检测 图表
export function getLiverChart(params) {
  return saasAxios.request({
    url: '/api/h5/v1/patient/examination/liver/chart',
    method: 'get',
    params
  })
}

// 人体成分分析
export function getCompositionList(params) {
  return saasAxios.request({
    url: '/api/h5/v1/patient/body/composition/list',
    method: 'get',
    params
  })
}

// 神经传导
export function getDpnList(params) {
  return saasAxios.request({
    url: '/api/h5/v1/dpn/lists',
    method: 'get',
    params
  })
}

// 神经传导
export function getDpnDetail(params) {
  return saasAxios.request({
    url: '/api/h5/v1/dpn/detail',
    method: 'get',
    params
  })
}

// 诊断信息
export function getDagnosis(params) {
  return saasAxios.request({
    url: '/api/h5/v1/workshop/get/diagnosis',
    method: 'get',
    params
  })
}

// 诊断信息列表
export function getDiagnosisList(params) {
  return saasAxios.request({
    url: '/api/h5/v1/workshop/get/diagnosis/list',
    method: 'get',
    params
  })
}

// 医生端病案首页患者医院科室列表
export function getAppDoctorDepartment(params) {
  return saasAxios.request({
    url: '/api/platform/v1/doctor/hospital/department/list',
    method: 'get',
    params
  })
}

// 患者端病案首页患者医院科室列表
export function getDoctorDepartment(params) {
  return saasAxios.request({
    url: '/api/platform/v1/patient/manage_plan/patient/hospital/department/list',
    method: 'get',
    params
  })
}

// 管理方案信息
export function getCurrentLevelInfo(params) {
  return saasAxios.request({
    url: '/api/platform/v1/patient/manage_plan/current_level_info',
    method: 'get',
    params
  })
}

// 代办事项(医信老接口直接请求)
export function getManageplanNew(params) {
  return saasAxios.request({
    url: '/api/web/v1/department/patient/manage/plan_new',
    method: 'get',
    params
  })
}

// 核心数据(医信老接口直接请求)
export function getCoreDataList(params) {
  return saasAxios.request({
    url: '/api/platform/v1/patient/indicator/core_data_list',
    method: 'get',
    params
  })
}

// 权限数据(医信老接口直接请求)
export function getsickerMedicalRecordist(params) {
  return saasAxios.request({
    url: '/api/platform/v1/patient/indicator/core_data_list',
    method: 'get',
    params
  })
}


// 详情权限
export function getFuncDetailApi (params) {
  return saasAxios.request({
    url: '/api/platform/v1/patient/manage_plan/patient/hospital/project/list',
    // url: '/api/web/v1/func/detail',
    method: 'get',
    params
  })
}

// 获取病案 实验室检查，辅助检查，生物学标志物 时间
export function getExaminationItemListApi(params) {
  return saasAxios.request({
    url: '/api/platform/v1/patient/examination/item/list',
    method: 'get',
    params
  })
}

// 查询指标详情
export function getDetectionDetailApi(params) {
  return saasAxios.request({
      url: '/api/web/v1/detection/detail',
      method: 'get',
      params
  })
}

// 检查数据--获取模板
export function getDetectionConfigApi(params) {
  return saasAxios.request({
    url: '/api/platform/v1/config',
    method: 'get',
    params
  })
}

/**
 * 生命体征
 */
// 身高体重
export function getHeightWeightListApi(params) {
  return saasAxios.request({
    url: '/api/web/v1/height/list',
    method: 'get',
    params
  })
}
// 血糖
export function getBloodSugarListApi(params) {
  return saasAxios.request({
    url: '/api/web/v1/bg/list',
    method: 'get',
    params
  })
}

// 血氧饱和
export function getSpoListApi(params) {
  return saasAxios.request({
    url: '/api/web/v1/spo2/list',
    method: 'get',
    params
  })
}

// 流速峰值
export function getPefListApi(params) {
  return saasAxios.request({
    url: '/api/web/v1/pef/list',
    method: 'get',
    params
  })
}

// 血压
export function getBpListApi(params) {
  return saasAxios.request({
    url: '/api/web/v1/bp/list',
    method: 'get',
    params
  })
}

// 纬度信息
export function getSidesListApi(params) {
  return saasAxios.request({
    url: '/api/web/v1/sign/list',
    method: 'get',
    params
  })
}

// 一般体征
export function getGeneralSignListApi(params) {
  return saasAxios.request({
    url: '/api/web/v1/general/sign/list',
    method: 'get',
    params
  })
}

// 评估
export function getAssessmentApi(params) {
  return saasAxios.request({
    url: '/api/web/v1/assessment/list',
    methods: 'get',
    params
  })
}
// 24小时动态血压
export function getBloodHoursbpListApi(params) {
  return saasAxios.request({
    url: '/api/web/v1/bp/hours24/list',
    method: 'get',
    params
  })
}

// 获取病案核心数据和个人体征数据折线图数据
export function getCoreDataChartListApi(params) {
  return saasAxios.request({
    url: '/api/platform/v1/patient/indicator/chart',
    method: 'get',
    params
  })
}

// C端功能清单
export function getWorkshopFuncListApi(params) {
  return saasAxios.request({
    url: '/api/h5/v1/workshop/func/listing',
    method: 'get',
    params
  })
}

// 项目统计查询列表
export function getProjectStatisticsListApi(params) {
  return saasAxios.request({
    url: '/api/web/v1/zz_hosp/project/statics',
    method: 'get',
    params
  })
}

// 查询 cgm 设备列表
export function getCgmDeviceListApi(params) {
  return saasAxios.request({
    url: '/api/platform/v1/patient/manage_plan/patient/cgm/device/list',
    method: 'get',
    params
  })
}

// 查询 cgm 图表
export function getCgmDeviceDataApi(params) {
  return saasAxios.request({
    url: '/api/platform/v1/patient/manage_plan/patient/cgm/device/data',
    method: 'get',
    params
  })
}

// 异常数据
export function getAbnormalIndicatorListApi(params) {
  return saasAxios.request({
    url: '/api/platform/v1/patient/abnormal/indicator/list',
    method: 'get',
    params
  })
}

// 异常数据详情
export function getAbnormalIndicatorDetailListApi(params) {
  return saasAxios.request({
    url: '/api/platform/v1/patient/abnormal/indicator/detail/list',
    method: 'get',
    params
  })
}
