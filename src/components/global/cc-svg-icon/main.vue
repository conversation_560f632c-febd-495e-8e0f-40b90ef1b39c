<template>
  <svg class="cc-svg-icon" :class="svgClass" aria-hidden="true" v-on="$listeners">
    <use :xlink:href="iconName" />
  </svg>
</template>
<script>
export default {
  name: 'CcSvgIcon',
  props: {
    iconClass: {
      default: '',
      type: String
    },
    className: {
      default: '',
      type: String
    }
  },
  computed: {
    iconName() {
      return `#icon-${this.iconClass}`
    },
    svgClass() {
      if (this.className) {
        return `svg-icon ${this.className}`
      }
      return `svg-icon`
    }
  }
}
</script>

<style lang="scss" scoped>
  .cc-svg-icon {
    width: 16px;
    height: 16px;
    vertical-align: middle;
    fill: currentColor;
    overflow: hidden;
  }
</style>
