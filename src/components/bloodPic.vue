<template>
  <div class="pic-wrapper">
    <div class="pic">
      <i
        v-for="(item, index) in data"
        :key=index
        :style="{
          'position': 'absolute',
          'z-index': '1000',
          'bottom': (item[0] > 200 ? 200 : item[0]) * 0.5 + '%',
          'left': (item[1] > 140 ? 140 : item[1]) * 0.71 + '%'
        }"
        class="create"
      ></i>
      <div class="down"></div>
      <div class="normal"></div>
      <div class="high"></div>
      <span class="xTitle">收缩压(mmHg)</span>
      <span class="xnormal">135</span>
      <span class="xdown">90</span>
      <span class="yTitle">舒张压(mmHg)</span>
      <span class="ydown">60</span>
      <span class="ynormal">85</span>
    </div>
  </div>
</template>
<script>
  export default {
    data() {
      return {}
    },
    props: ['data'],
    watch: {},
    created() {},
    mounted() {},
    methods: {}
  }
</script>
<style lang="scss" scoped>
  .pic-wrapper{
    width: 100%;
    height: 180px;
    padding: 0 64px 25px;
    box-sizing: border-box;
    .pic{
      width: 100%;
      height: 100%;
      position: relative;
      .high{
        width: 100%;
        height: 100%;
        display: flex;
        align-items: flex-start;
        justify-content: center;
        background: #F8875c;
        position: absolute;
        left: 0;
        bottom: 0;
        z-index: 9;
        font-size: 14px;
        color: #8B4C11;
      }
      .normal{
        width: 60.35%;
        height: 67.5%;
        display: flex;
        align-items: flex-start;
        justify-content: center;
        background: #07C160;
        position: absolute;
        left: 0;
        bottom: 0;
        z-index: 99;
        font-size: 14px;
        color: #118A1D;
        border-top: 2px solid #fff;
        border-right: 2px solid #fff;
      }
      .down{
        width: 42.6%;
        height: 45%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #61A7FF;
        position: absolute;
        left: 0;
        bottom: 0;
        z-index: 999;
        font-size: 14px;
        color: #245DA5;
        border-top: 2px solid #fff;
        border-right: 2px solid #fff;
      }
      .xTitle{
        width: 45px;
        line-height: 16px;
        text-align: center;
        font-size: 12px;
        color: #999;
        display: block;
        position: absolute;
        left: -50px;
        top: 0;
      }
      .xnormal{
        width: 40px;
        line-height: 16px;
        text-align: center;
        font-size: 12px;
        color: #999;
        display: block;
        transform: translateY(50%);
        position: absolute;
        left: -35px;
        bottom: 67.5%;
      }
      .xdown{
        width: 40px;
        line-height: 16px;
        text-align: center;
        font-size: 12px;
        color: #999;
        display: block;
        transform: translateY(50%);
        position: absolute;
        left: -35px;
        bottom: 45%;
      }
      .yTitle{
        width: 45px;
        line-height: 16px;
        color: #999;
        font-size: 12px;
        transform: translateX(100%);
        position: absolute;
        right: -6px;
        bottom: 0;
      }
      .ydown{
        line-height: 16px;
        font-size: 12px;
        color: #999;
        display: block;
        transform: translateX(-50%);
        position: absolute;
        left: 42.6%;
        bottom: -20px;
      }
      .ynormal{
        line-height: 16px;
        font-size: 12px;
        color: #999;
        display: block;
        transform: translateX(-50%);
        position: absolute;
        left: 60.35%;
        bottom: -20px;
      }
      .create{
        width: 5px;
        height: 5px;
        display: block;
        border-radius: 50%;
        transform: translate(-50%, 50%);
        background: rgba(255,255,255,0.7);
      }
    }
  }
</style>
