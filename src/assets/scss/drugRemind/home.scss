.main{
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    // justify-content: center;
    align-items: center;
    background:#fff;
    .follow{
        width: 355px;
        height: 77px;
        margin-top: 10px;
        background: url('~@/assets/images/beijing.png') no-repeat;
        background-size: cover;
        .p1{
            width: 190px;
            height: auto;
            float: left;
            margin-left: 75px;
            margin-top: 15px;
            padding-left: 10px;
            span:nth-child(1) {
                font-size: 17px;
                color: #222;
                float: left;
                line-height: 28px;
            }
            span:nth-child(2) {
                font-size: 14px;
                float: left;
                line-height: 20px;
                color: #666;
            }
        }
        .p2{
            width: 58px;
            height: 32px;
            float: right;
            line-height: 32px;
            margin-top: 22px;
            margin-right: 10px;
            background:rgba(7,193,96,1);
            border-radius:3px;
            font-size:17px;
            font-weight:500;
            color:rgba(255,255,255,1);
        }
    }
    .follower{
        font-size:15px;
        font-weight:400;
        height: 50px;
        color:rgba(153,153,153,1);
        line-height:56px;
    }

    .noData{
        .sugarTime{
            font-size:15px;
            line-height: 21px;
            font-weight:400;
            color:rgba(153,153,153,1);
            margin: 14px 0 24px 0;
        }
        .img1{
            width:206px;
            height:154px;
            margin-top: 70px;
            margin-bottom: 34px;
        }
        .img11{
            width:206px;
            height:154px;
            margin-top:157px;
            margin-bottom: 34px;
        }
        .method{
            font-size:18px;
            font-weight:400;
            color:rgba(51,51,51,1);
            line-height:48px;
        }
        .remind{
            font-size:16px;
            font-weight:400;
            color:rgba(153,153,153,1);
            line-height:18px;
        }
        .img2{
            width:28px;
            height:68px;
            margin-top: 45px;
        }
    }
    .wxhasData, .apphasData{
        margin-bottom: 60px;
        ul{
            width:327px;
            padding: 0 14px;
            height:auto;
            border-radius:5px;
            border:1px solid rgba(238,238,238,1);
            background: #fff;
            margin-top: 10px;
            .title{
                height: 48px;
                line-height: 46px;
                border-bottom: 2px solid rgba(238,238,238,1);
                span{
                    display: block;
                    font-size:14px;
                    font-weight:400;
                    color:#666;
                }
                .first-span{
                    color: #999;
                }
                .first-span-child{
                    color: #FFB44C;
                }
                span:nth-child(1){
                    width: 170px;
                    text-align: left;
                    margin-right: 16px;
                    float: left;
                    // i{
                    //     color: #FF7C35;
                    //     font-weight: 600;
                    //     font-style: normal;
                    //     margin-left: 5px;
                    // }
                }
                span:nth-child(2){
                    margin-right: 12px;
                    float: left;
                }
                span:nth-child(3){
                    float: right;
                }
            }
            .li{
                height: 48px;
                line-height:47px;
                border-bottom: 1px solid rgba(238,238,238,1);
                span{
                    font-size:17px;
                    font-weight:400;
                    color:rgba(51,51,51,1);
                }
                span:nth-child(1){
                    float: left;
                    text-align: left;
                    width: 210px;
                    overflow: hidden;
                    text-overflow:ellipsis;
                    white-space: nowrap;
                }
                span:nth-child(2){
                    float: right;
                    width: 110px;
                    text-align: right;
                }
            }
        }
        .sugarTime{
            font-size:15px;
            line-height: 21px;
            font-weight:400;
            color:rgba(153,153,153,1);
            margin: 14px 0 24px 14px;
            text-align: left;
        }
    }
    .wxadd{
        width:345px;
        line-height:50px;
        margin: 95px auto 8px;
        background:linear-gradient(90deg, #FFA555 0%, #F67710 100%);
        font-size:18px;
        font-weight:500;
        color:rgba(255,255,255,1);
        border-radius: 6px;
        img{
            width: 16px;
            height: 16px;
            margin-right: 6px;
        }
    }
    .appadd{
        margin-top: 30px;
        .add{
            width:345px;
            height:50px;
            line-height: 50px;
            background:linear-gradient(90deg,rgba(255,165,85,1) 0%,rgba(246,119,16,1) 100%);
            font-size:18px;
            border-radius:6px;
            font-weight:500;
            color:rgba(255,255,255,1);
            img{
                width: 15px;
                height: 15px;
                margin-right: 6px;
            }
        }
        .noadd{
            width:345px;
            height:50px;
            line-height: 50px;
            border-radius:6px;
            margin-top: 20px;
            background:#fff;
            font-size:18px;
            font-weight:500;
            color: #666;
            border:1px solid rgba(204,204,204,1);
        }
    }
}
.mask{
        width: 100%;
        height: 100%;
        position: fixed;
        top: 0;
        z-index: 99;
        background:rgba(0,0,0,1);
        opacity:0.6;
    }
    .deleteDrug{
        width:296px;
        height:176px;
        background:rgba(255,255,255,1);
        border-radius:6px;
        position: fixed;
        z-index: 100;
        left: 0;
        top: 0;
        right: 0;
        bottom: 0;
        margin: auto;
        p:nth-child(1) {
            width: 100%;
            height: 48px;
            line-height: 56px;
            font-size:18px;
            font-weight:500;
            color:rgba(3,8,26,1);
        }
        p:nth-child(2) {
            width: 75%;
            margin: 0 auto;
            text-align: left;
            line-height: 28px;
            font-size:16px;
            font-weight:400;
            color: #666;
        }
        p:nth-child(3){
            margin: 0 auto;
            margin-top: 23px;
            width: 86px;
            height: 36px;
            line-height: 36px;
            font-size:17px;
            font-weight:500;
            background: #ffa555;
            color: #fff;
            text-align: center;
            border-radius: 3px;
        }
    }
