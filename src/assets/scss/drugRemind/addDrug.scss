.main{
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    // justify-content: center;
    align-items: center;
    background:rgba(242,242,242,1);
    .add{
        width:327px;
        height:auto;
        padding: 0 14px;
        margin-top: 10px;
        margin-bottom: 10px;
        border-radius:5px;
        border:1px solid rgba(238,238,238,1);
        background: #fff;
        p{
            height: 48px;
            line-height: 52px;
            font-size:15px;
            font-weight:400;
            color:rgba(102,102,102,1);
            text-align: left;
        }
        .addDom{
            border-top: 2px solid rgba(238,238,238,1);
            margin-bottom: 20px;
            .drugs{
                height: 54px;
                border-bottom: 1px solid #eee;
                position: relative;
                .label-name{
                    float: left;
                    font-size:17px;
                    font-weight:400;
                    color: #666;
                    line-height: 54px;
                }
                p{
                    width:80px;
                    height:32px;
                    float: right;
                    margin-top: 10px;
                    line-height: 30px;
                    // border-radius:16px;
                    font-size:17px;
                    font-weight:400;
                    color:#F3955F;;
                    img{
                        width: 12px;
                        height: 18px;
                        float: left;
                        margin: 7px 3px 0 12px;
                    }
                    span{
                        float: left;
                        margin-top: 2px;
                    }
                }
                input{
                    float: left;
                    width: 180px;
                    height: 54px;
                    line-height: 54px;
                    border: 0;
                    font-size: 17px;
                    padding-left: 10px;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                }
                .jiantou{
                    float: right;
                    font-size: 20px;
                    width:20px;
                    height: 52px;
                    text-align: right;
                    .van-icon{
                        font-size: 20px;
                        margin-top: 15px;
                        color: #F3955F;
                    }

                }
                .ul{
                    width:244px;
                    height:164px;
                    overflow-y: auto;
                    background:rgba(255,255,255,1);
                    // border-radius:6px;
                    border: 1px solid #979797;
                    position:absolute;
                    left: 50px;
                    top: 40px;
                    z-index: 99;
                    box-sizing: border-box;
                    padding: 0 8px;
                    li{
                        width: 100%;
                        line-height: 38px;
                        text-align: left;
                        font-size: 16px;
                        white-space:nowrap;
                        overflow:hidden;
                        text-overflow:ellipsis;
                        &:not(:last-child){
                            border-bottom: 1px solid #eee;
                        }
                    }
                }
            }
            .doses{
                height: 54px;
                line-height: 54px;
                border-bottom: 1px solid #eee;
                p{
                    width:83px;
                    height:32px;
                    float: left;
                    margin-top: 14px;
                    line-height: 30px;
                    font-size:17px;
                    font-weight:400;
                    text-align: left;
                }
                .chooseDose{
                    width: 220px;
                    float: right;
                    .van-cell{
                        border-bottom: 0;
                        padding: 0;
                        width: 220px;
                        height: 42px;
                        line-height: 42px;
                        font-size: 17px;
                        margin-top: 8px;
                        .van-cell__value--alone{
                            font-size: 17px;
                            font-weight:400;
                            color:rgba(51,51,51,1);
                            border-bottom: 0;
                            text-align: right;
                        }
                        .van-cell__right-icon{
                            font-size: 20px;
                            margin-top: 7px;
                            color: #ccc;
                        }
                    }
                    .van-cell:not(:last-child)::after{
                        border-bottom: 0;
                    }
                }
            }
            .delDom{
                width: 100%;
                height:48px;
                font-size:17px;
                font-weight:400;
                color:rgba(153,153,153,1);
                line-height:50px;
                background: url('~@/assets/images/shanchu.png') no-repeat 125px center;
                background-size: 17px 17px;
            }
        }
        .btn{
            width:159px;
            height:38px;
            line-height: 38px;
            background:linear-gradient(90deg,rgba(255,165,85,1) 0%,rgba(246,119,16,1) 100%);
            border-radius:20px;
            opacity:0.7;
            font-size:17px;
            font-weight:500;
            color:rgba(255,255,255,1);
            margin: 0 auto;
            margin-top: 40px;
            margin-bottom: 30px;
            img{
                width: 15px;
                height: 15px;
                margin-right: 6px;
            }
        }
    }
    .drugTime{
        width:327px;
        // height:310px;
        padding: 0 14px;
        margin-bottom: 10px;
        border-radius:5px;
        border:1px solid rgba(238,238,238,1);
        background: #fff;
        .times{
            .van-cell{
                padding: 0;
                height: 54px;
                line-height: 54px;
                text-align: center;
            }
            .van-cell__value--alone{
                font-size:19px;
                font-weight:400;
                color:rgba(255,124,53,1);
                text-align: center;
                border-bottom: 1px solid #eee;
            }
            .van-cell__right-icon{
                display: none;
            }
        }
        .dayRemind{
            .select{
                p{
                    width:83px;
                    height:32px;
                    float: left;
                    margin-top: 14px;
                    line-height: 30px;
                    font-size:17px;
                    font-weight:400;
                    text-align: left;
                }
                .van-cell{
                    border-bottom: 0;
                    padding: 0;
                    width: 220px;
                    float: right;
                    height: 42px;
                    line-height: 42px;
                    font-size: 17px;
                    margin-top: 8px;
                    .van-cell__value--alone{
                        width: 310px;
                        font-size: 17px;
                        font-weight:400;
                        color:rgba(51,51,51,1);
                        border-bottom: 0;
                        text-align: right;
                    }
                    .van-cell__right-icon{
                        font-size: 20px;
                        margin-top: 7px;
                        color: #ccc;
                    }
                }
                .van-cell:not(:last-child)::after{
                    border-bottom: 0;
                }
            }
            
        }
    
    }
    .drugNumber{
        width:327px;
        height:104px;
        padding: 0 14px;
        margin-bottom: 10px;
        border-radius:5px;
        border:1px solid rgba(238,238,238,1);
        background: #fff;
        .drugNumbertimes{
            width: 100%;
            height: 42px;
            .van-cell{
                border-bottom: 0;
                padding: 0;
                width: 100%;
                float: right;
                height: 42px;
                line-height: 42px;
                font-size: 17px;
                margin-top: 8px;
                .van-cell__value--alone{
                    width: 310px;
                    font-size: 17px;
                    font-weight:400;
                    color:#FF7C35;
                    border-bottom: 0;
                    text-align: left;
                }
                .van-cell__right-icon{
                    font-size: 20px;
                    margin-top: 7px;
                    color: #ccc;
                }
            }
            .van-cell:not(:last-child)::after{
                border-bottom: 0;
            }
        }
    }
    .save{
        width:355px;
        height:50px;
        line-height: 52px;
        background:linear-gradient(90deg,rgba(255,165,85,1) 0%,rgba(246,119,16,1) 100%);
        border-radius:6px;
        font-size:18px;
        font-weight:500;
        color:rgba(255,255,255,1);
        margin-top: 30px;
        margin-bottom: 38px;
    }
    .p{
        height: 48px;
        line-height: 52px;
        border-bottom: 2px solid rgba(238,238,238,1);
        font-size:15px;
        font-weight:400;
        color:rgba(102,102,102,1);
        text-align: left;
    }
    .del{
        width:156px;
        height:34px;
        line-height: 34px;
        border-radius:4px;
        border:1px solid rgba(204,204,204,1);
        font-size:16px;
        font-weight:400;
        margin-bottom: 90px;
        color:rgba(102,102,102,1);
    }
    .mask{
        width: 100%;
        height: 100%;
        position: fixed;
        top: 0;
        z-index: 99;
        background:rgba(0,0,0,1);
        opacity:0.6;
    }
    .deleteDrug{
        width:296px;
        height:155px;
        background:rgba(255,255,255,1);
        border-radius:6px;
        position: fixed;
        z-index: 100;
        left: 0;
        top: 0;
        right: 0;
        bottom: 0;
        margin: auto;
        p:nth-child(1) {
            width: 100%;
            height: 48px;
            line-height: 56px;
            font-size:18px;
            font-weight:500;
            color:rgba(3,8,26,1);
        }
        p:nth-child(2) {
            width: 100%;
            line-height: 28px;
            font-size:16px;
            font-weight:400;
            color:rgba(102,102,102,1);
        }
        p:nth-child(3){
            width: 100%;
            height: 55px;
            line-height: 54px;
            border-top: 1px solid #ccc;
            margin-top: 23px;
            span:nth-child(1){
                width: 50%;
                display: block;
                float: left;
                font-size:17px;
                font-weight:500;
                color:rgba(0,0,0,1);
                text-align: center;
                border-right: 1px solid #ccc;
            }
            span:nth-child(2){
                width: 49%;
                display: block;
                float: left;
                font-size:17px;
                font-weight:500;
                color:#576B95;
                text-align: center;
            }
        }
    }
    .drugModal{
        width:100%;
        height:100%;
        background: #F2F2F2;
        position: fixed;
        z-index: 100;
        left: 0;
        top: 0;
        right: 0;
        bottom: 0;
        margin: auto;
        overflow: auto;
        p{
            width: 70px;
            height: 28px;
            line-height: 28px;
            position: fixed;
            z-index: 999;
            background:linear-gradient(90deg,rgba(255,165,85,1) 0%,rgba(246,119,16,1) 100%);
            border-radius:17px;
            right: 20px;
            top: 10px;
            font-size:15px;
            font-weight:500;
            color:rgba(255,255,255,1);
        }
        div{
            width: 100%;
            height: 100%;
            ul{
                background: #fff;
                li {
                    width: 335px;
                    text-align: left;
                }
                .li {
                    height: 52px;
                    line-height: 60px;
                    font-size:19px;
                    font-weight:600;
                    color:rgba(51,51,51,1);
                    padding: 0 20px;
                    background: #F2F2F2;
                }
                .lis{
                    line-height: 24px;
                    background: #fff;
                    font-size:17px;
                    font-weight:400;
                    color:rgba(51,51,51,1);
                    border-bottom: 1px solid #eee;
                    background: #fff;
                    margin-left: 20px;
                    padding: 15px 0;
                }
                .lis:last-child{
                    border-bottom: 0;
                }
            }
            ul:last-child{
                margin-bottom: 60px;
            }
        } 
    }
    .van-picker__cancel, .van-picker__confirm{
        font-size: 16px !important;
    }
}
