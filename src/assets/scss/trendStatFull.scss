.wrapper {
  width: 100vh;
  height: 100vw;
  overflow: hidden;
  overflow-y: auto;
  transform-origin: 0 0;
  transform: rotate(90deg);
  position: fixed;
  left: 100%;
  top: 0;

  &::-webkit-scrollbar {
    width: 0;
  }

  .content {

    .tab {
      display: flex;
      flex-wrap: wrap;

      .tab-item {
        width: 14%;
        height: 8vw;
        color: #666;
        font-size: 16px;
        margin-top: 4vw;
        margin-left: 5%;
        font-weight: 400;
        line-height: 8vw;
        border-radius: 3px;
        box-sizing: border-box;
        border: 1px solid #E8E8E8;
      }

      .tab-item-select {
        color: #FF9600;
        border: 1px solid #FF9600;
      }
    }

    .chart {
      width: 100%;
      height: 68vw;
      margin-top: 6vw;

      .chart-box {
        width: 100%;
        height: 100%;
      }
    }

    .chart-all {
      height: 59vw;
    }

    .all-date {
      display: flex;
      align-items: center;
      padding: 0 15px 0 25px;
      justify-content: space-between;

      .chart-date {
        width: 38px;
        color: #333;
        font-size: 12px;
        font-weight: 400;
        line-height: 9vw;
        text-align: center;
        border-radius: 4px;
        background: linear-gradient(180deg, #FFF7F1 0%, #FFECDE 100%);
      }
    }
  }

  .single-dialog {
    width: 100vh;
    height: 100vw;
    overflow: hidden;
    backface-visibility: hidden;
    background-color: #000000b3;
    transform: translate3d(-50%,-50%,0);
    position: fixed;
    top: 50%;
    left: 50%;
    z-index: 9999;

    .single-wrapper {
      width: 90%;
      border-radius: 10px;
      background-color: #fff;
      transform: translate3d(-50%,-50%,0);
      position: fixed;
      top: 50%;
      left: 50%;

      .single-title {
        width: 100%;
        height: 50px;
        color: #333;
        font-size: 20px;
        font-weight: 400;
        line-height: 50px;
      }

      .single-chart {
        width: 100%;
        height: 270px;
        padding: 0 10px;
        box-sizing: border-box;

        .chart-box {
          width: 100%;
          height: 100%;
        }
      }

      .single-footer {
        width: 100%;
        height: 40px;
        line-height: 40px;
        border-top: 1px solid #E8E8E8;

        .single-footer-btn {
          color: #333;
          font-size: 17px;
          font-weight: 400;
        }
      }
    }
  }
}
