.wrapper {
  overflow: hidden;
  position: relative;
  background-color: #F5F6FA;

  .header-backgroud {
    width: 100%;
    height: 200px;
    position: absolute;
    top: 0;
    z-index: 0;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .content {
    margin: 20px 15px 0 15px;
    overflow: hidden;
    position: relative;

    .header {
      margin: 20px 0;
      color: #FFFFFF;
      text-align: center;

      h1 {
        height: 60px;
        font-size: 48px;
        line-height: 60px;
      }

      p {
        height: 20px;
        font-size: 15px;
        line-height: 20px;
      }
    }

    .group {
      overflow: hidden;
      border-radius: 9px;
      margin-bottom: 15px;
      padding: 10px 20px 20px;
      background-color: #FFFFFF;

      .group-title {
        height: 20px;
        color: #333;
        font-size: 15px;
        text-align: left;
        line-height: 20px;
        position: relative;
        margin-bottom: 10px;

        &::before {
          content: "";
          width: 3px;
          height: 13px;
          background-color: #F67710;
          transform: translateY(-50%);
          position: absolute;
          left: -6px;
          top: 50%;
        }
      }

      .metabolism-score {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        justify-content: space-between;

        .metabolism-echarts-wrapper {
          width: 120px;
          height: 120px;
          position: relative;
          z-index: 0;

          .metabolism-echarts-box {
            width: 120px;
            height: 120px;
          }

          .metabolism-echarts-title {
            width: 70%;
            height: 70%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            border-radius: 50%;
            background-color: #fff;
            transform: translate(-50%, -50%);
            position: absolute;
            top: 50%;
            left: 50%;
            z-index: 0;

            .echarts-title-main {
              height: 28px;
              font-size: 10px;
              color: #151515;
              // margin-top: 20px;
              line-height: 28px;

              span {
                color: #333;
                font-size: 22px;
                margin-right: 2px;
              }
            }

            .echarts-title-sub {
              color: #333;
              font-size: 13px;
            }

            .echarts-title-sub2 {
              color: #333;
              font-size: 10px;
              line-height: 15px;
              white-space: nowrap;
            }
          }
        }

        .metabolism-score-p {
          height: 24px;
          color: #333;
          font-size: 17px;
          line-height: 24px;
        }
      }

      .metabolism-level {
        display: flex;
        justify-content: space-between;

        .level-group {
          width: 48%;

          .level-box {
            width: 100%;
            margin-bottom: 6px;

            .level-line {
              height: 22px;
              display: flex;
              padding: 0 8px;
              line-height: 22px;
              align-items: center;
              border-radius: 10px;
              box-sizing: border-box;
              justify-content: space-between;

              span {
                font-size: 14px;
                color: #FFFFFF;
              }
            }

            .level-total,
            .level-area {
              background-image: linear-gradient(-90deg, #84CBFF 0%, #6F74FF 100%);
            }

            .level-mine {
              background-image: linear-gradient(90deg, #FF9500 0%, #FFC305 100%);
            }

          }

          .level-txt {
            font-size: 14px;
            color: #333333;
            text-align: left;
            line-height: 22px;
          }
        }
      }
    }

    .introduce-wrapper {
      overflow: hidden;
      border-radius: 9px;
      margin-bottom: 15px;

    }
  }

  footer {
    margin: 0 15px 20px;

    .next-btn {
      border: 0;
      width: 100%;
      height: 46px;
      color: #fff;
      font-size: 16px;
      line-height: 46px;
      text-align: center;
      margin-bottom: 10px;
      background-image: linear-gradient(90deg, #FFA555 0%, #F67710 100%);
    }

    .remark {
      color: #999;
      font-size: 12px;
      text-align: left;
      line-height: 17px;
    }
  }
}