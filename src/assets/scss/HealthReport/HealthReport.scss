.wrapper {
  overflow: hidden;
  position: relative;
  background-color: #F3955F;
  min-height: -webkit-fill-available;

  .header-backgroud {
    width: 100%;
    height: 200px;
    position: absolute;
    top: 0;
    z-index: 0;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .content {
    margin: 0 15px;
    overflow: hidden;
    position: relative;

    .content-top {
      color: #fff;
      display: flex;
      margin: 5px 0;
      align-items: center;
      justify-content: space-between;

      span {
        display: flex;
        font-size: 15px;
        align-items: center;
      }
    }

    .group {
      overflow: hidden;
      margin-top: 15px;
      border-radius: 9px;
      background-color: #fff;

      &>div {
        margin: 0 20px;
      }

      .group-top {
        height: 41px;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .group-top-title {
          color: #333;
          font-size: 15px;
          position: relative;
          font-weight: 600;
          &::before {
            content: "";
            width: 3px;
            height: 13px;
            background-color: #F67710;
            transform: translateY(-50%);
            position: absolute;
            left: -6px;
            top: 50%;
          }
        }

        .group-top-right {
          color: #999;
          display: flex;
          font-size: 13px;
          align-items: center;
        }
      }

      .group-up {
        margin: 0;
        height: 41px;
        display: flex;
        align-items: center;
        background: #FFE4C1;
        justify-content: space-between;

        .group-up-left {
          display: flex;
          align-items: center;

          .group-up-icon {
            width: 29px;
            height: 30px;
            margin-left: 15px;
            margin-top: -5px;
          }

          .group-up-title {
            font-size: 18px;
            font-weight: 600;
            color: #894E00;
            margin-left: 9px;
          }

        }

        .group-up-btn {
          width: 72px;
          height: 26px;
          color: #FFF;
          background: linear-gradient(360deg,rgba(240,92,65,1) 0%,rgba(255,147,68,1) 100%);
          font-size: 14px;
          font-weight: 400;
          line-height: 26px;
          margin-right: 15px;
          border-radius: 5px;
        }
      }

      .abnormal-wrapper {
        margin-bottom: 15px;

        .abnormal-title {
          color: #333;
          font-size: 15px;
          text-align: left;
          margin-bottom: 6px;
        }

        .abnormal-normal {
          color: #666;
          height: 28px;
          font-size: 17px;
          text-align: left;
          line-height: 28px;
          margin-bottom: 10px;
        }

        .abnormal-group {
          display: flex;
          align-items: center;
          justify-content: space-between;

          .abnormal-group-top-title {
            color: #333;
            font-size: 17px;
            line-height: 28px;
          }

          .abnormal-group-num {
            display: flex;
            align-items: center;

            span {
              color: #333;
              font-size: 20px;
              text-align: right;
              line-height: 28px;
            }

            img {
              margin-left: 2px
            }
          }
        }

        .abnormal-show {
          height: 20px;
          display: flex;
          font-size: 15px;
          margin-top: 5px;
          color: #262626;
          align-items: center;
          justify-content: center;
        }
      }

      .group-data-wrapper {
        margin: 0;
        display: flex;
        flex-wrap: wrap;

        .group-data-item {
          width: 50%;
          height: 160px;
          display: flex;
          padding: 15px 0;
          align-items: center;
          box-sizing: border-box;
          flex-direction: column;
          border-top: 1px solid #E8E8E8;
          border-right: 1px solid #E8E8E8;

          &:nth-of-type(even) {
            border-right: 0;
          }

          .data-item-top {
            width: 42px;
            height: 42px;
            margin-bottom: 10px;
          }

          .data-item-middle {
            display: flex;
            height: 22px;
            margin-bottom: 10px;

            .item-middle-name {
              color: #333;
              font-size: 19px;
              line-height: 22px;
            }

            .item-middle-niaodanbai {
              font-size: 12px;
            }

            .item-middle-unit {
              font-size: 13px;
              color: #151515;
              margin-left: 5px;
              line-height: 18px;
              align-self: flex-end;
            }
          }

          .data-item-bottom {
            display: flex;
            align-items: flex-start;
            justify-content: center;

            .item-bottom-fasting {
              height: 22px;
              font-size: 12px;
              color: #7AA0FF;
              margin-right: 4px;
              line-height: 22px;
            }

            .item-bottom-num {
              height: 22px;
              color: #333;
              font-size: 20px;
              line-height: 22px;
            }

            .item-bottom-line {
              width: 1px;
              height: 22px;
              margin: 0 6px;
              background-color: #E8E8E8;
            }

            .item-bottom-remind {
              width: 50px;
              height: 20px;
              color: #CCC;
              font-size: 17px;
              line-height: 20px;
              text-align: center;
              border-radius: 11px;
              border: 1px solid #CCC;
            }

            .item-remind-highest {
              color: #C44353;
              border: 1px solid #C44353;
            }

            .item-remind-up {
              color: #EF7C69;
              border: 1px solid #EF7C69;
            }

            .item-remind-down {
              color: #73CCF6;
              border: 1px solid #73CCF6;
            }

            .item-remind-bmi-normal {
              color: #92E464;
              border: 1px solid #92E464;
            }

            .item-remind-bmi-up {
              color: #FCC569;
              border: 1px solid #FCC569;
            }

            .item-remind-bmi-down {
              color: #73CCF6;
              border: 1px solid #73CCF6;
            }

            .item-remind-bmi-highest {
              color: #EF7C69;
              border: 1px solid #EF7C69;
            }

            .bottom-line-tangwang {
              height: 52px;
            }

            .item-bottom-tangwang {
              flex: 1;
              height: 52px;

              p {
                color: #333;
                font-size: 14px;
                line-height: 18px;
              }

              .bottom-tangwang-top {
                font-size: 15px;
                color: #F67710;
                position: relative;
                z-index: 0;

                &::after {
                  content: "";
                  width: 22px;
                  height: 22px;
                  border-radius: 50%;
                  background-color: #FFECDE;
                  position: absolute;
                  top: 50%;
                  left: 50%;
                  transform: translate(-50%, -50%);
                  z-index: -1;
                }
              }
            }
          }

          .data-item-reference {
            color: #999;
            font-size: 13px;
            margin-top: 6px;
            text-align: center;
          }
        }

        .group-data-item2 {
          height: 110px;

          .data-item-middle {
            .item-middle-name {
              font-size: 17px;
            }
          }
        }
      }

      .metabolism-middle {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        justify-content: space-between;

        .metabolism-echarts-wrapper {
          width: 120px;
          height: 120px;
          position: relative;
          z-index: 0;

          .metabolism-echarts-box {
            width: 120px;
            height: 120px;
          }

          .metabolism-echarts-title {
            width: 70%;
            height: 70%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            border-radius: 50%;
            background-color: #fff;
            transform: translate(-50%, -50%);
            position: absolute;
            top: 50%;
            left: 50%;
            z-index: 0;

            .echarts-title-main {
              height: 28px;
              font-size: 10px;
              color: #151515;
              // margin-top: 20px;
              line-height: 28px;

              span {
                color: #333;
                font-size: 22px;
                margin-right: 2px;
              }
            }

            .echarts-title-sub {
              color: #333;
              font-size: 13px;
            }

            .echarts-title-sub2 {
              color: #333;
              font-size: 10px;
              line-height: 15px;
              white-space: nowrap;
            }
          }
        }

        .metabolism-middle-p {
          color: #333;
          font-size: 17px;
        }
      }

      .metabolism-bottom {
        display: flex;
        align-items: center;
        margin-bottom: 15px;

        span {
          color: #333;
          font-size: 15px;
          margin-left: 5px;
        }
      }

      .metabolism-no {
        height: 70px;
        color: #333;
        font-size: 16px;
        text-align: left;
        font-weight: 400;
        line-height: 22px;
        margin-bottom: 5px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        background: #fff url("~@/assets/images/nodata.png") no-repeat right center;
        background-size: 96px;
      }

      .report-general {
        margin: 0;
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-start;

        .report-item-lipid,
        .report-item-bg {
          background-image: url("~@/assets/images/report-icon1.png");
        }

        .report-item-bp {
          background-image: url("~@/assets/images/report-icon2.png");
        }

        .report-item-motion {
          background-image: url("~@/assets/images/report-icon3.png");
        }

        .report-item-bmi {
          background-image: url("~@/assets/images/report-icon4.png");
        }

        .report-item-hemoglobin {
          background-image: url("~@/assets/images/report-icon5.png");
        }

        .report-item-creatinine {
          background-image: url("~@/assets/images/report-icon7.png");
        }

        .report-item-arteriosclerosis {
          background-image: url("~@/assets/images/report-icon8.png");
        }

        .report-item-vat {
          background-image: url("~@/assets/images/report-icon9.png");
        }

        .report-item-sugarnet {
          background-image: url("~@/assets/images/report-icon10.png");
        }

        .report-item-neuropathy {
          background-image: url("~@/assets/images/report-icon11.png");
        }

        .report-item-liver {
          background-image: url("~@/assets/images/report-icon12.png");
        }

        .report-item-carotid {
          background-image: url("~@/assets/images/report-icon13.png");
        }

        .report-item-heart {
          background-image: url("~@/assets/images/report-icon14.png");
        }

        .report-item-lab {
          background-image: url("~@/assets/images/report-icon15.png");
        }

        .report-item-assist {
          background-image: url("~@/assets/images/report-icon16.png");
        }

        .report-item {
          width: 50%;
          padding: 10px 0;
          text-align: left;
          background-size: 50px;
          box-sizing: border-box;
          background-repeat: no-repeat;
          background-position: 90% 90%;
          border-bottom: 1px solid #F2F3F4;
          display: flex;
          flex-direction: column;
          justify-content: space-between;

          &:nth-of-type(odd) {
            border-right: 1px solid #F2F3F4;
          }

          .report-item-name {
            color: #333;
            display: flex;
            padding: 0 10px;
            align-items: center;
            justify-content: space-between;

            .report-item-txt {
              font-size: 16px;
              font-weight: 600;
              line-height: 20px;
            }

            .report-item-go {
              width: 8px;
              height: 15px;
            }
          }

          .report-item-value {
            display: flex;
            padding: 0 10px;
            margin-top: 14px;
            align-items: center;
            justify-content: space-between;

            .report-item-num {
              display: flex;
              align-items: center;
              justify-content: flex-start;

              .report-item-val {
                height: 18px;
                color: #333;
                font-size: 26px;
                font-weight: 500;
                line-height: 18px;
              }

              img {
                width: 20px;
                height: 20px;
              }

              .report-step-unit {
                font-size: 22px;
              }
            }

            .report-item-remind {
              width: 50px;
              height: 20px;
              font-size: 17px;
              color: #92E464;
              line-height: 20px;
              text-align: center;
              border-radius: 11px;
              border: 1px solid #92E464;
            }

            .item-remind-highest {
              color: #C44353;
              border: 1px solid #C44353;
            }

            .item-remind-up {
              color: #EF7C69;
              border: 1px solid #EF7C69;
            }

            .item-remind-down {
              color: #73CCF6;
              border: 1px solid #73CCF6;
            }

            .item-remind-bmi-normal {
              color: #92E464;
              border: 1px solid #92E464;
            }

            .item-remind-bmi-up {
              color: #FCC569;
              border: 1px solid #FCC569;
            }

            .item-remind-bmi-down {
              color: #73CCF6;
              border: 1px solid #73CCF6;
            }

            .item-remind-bmi-highest {
              color: #EF7C69;
              border: 1px solid #EF7C69;
            }

            .report-item-go {
              width: 8px;
              height: 15px;
            }
          }

          .report-item-refer {
            color: #999;
            padding: 0 10px;
            font-size: 14px;
            margin-top: 14px;
            font-weight: 400;
            line-height: 18px;

            .report-item-range {
              margin-left: 2px;
            }
          }

          .report-item-date {
            height: 18px;
            color: #999;
            font-size: 13px;
            padding: 0 10px;
            margin-top: 4px;
            font-weight: 400;
            line-height: 18px;
          }

          .report-item-date-no-tab {
            height: 18px;
            color: #999;
            font-size: 13px;
            margin-top: 4px;
            font-weight: 400;
            line-height: 18px;
          }

          .report-item-eye {
            display: flex;
            margin-top: 8px;
            align-items: flex-start;
            justify-content: flex-start;

            .item-eye-info {
              flex: 1;
              box-sizing: border-box;

              .eye-info-top {
                font-size: 15px;
                margin: 0 0 10px;
                color: #F67710;
                line-height: 19px;
                position: relative;
                text-align: center;
                z-index: 0;

                &::after {
                  content: "";
                  width: 22px;
                  height: 22px;
                  border-radius: 50%;
                  background-color: #FFECDE;
                  position: absolute;
                  top: 50%;
                  left: 50%;
                  transform: translate(-50%, -50%);
                  z-index: -1;
                }
              }

              .eye-info-down {
                color: #333;
                padding: 0 4px;
                font-size: 14px;
                margin-top: 3px;
                font-weight: 400;
                line-height: 18px;
                text-align: center;
              }
            }

            .item-eye-line {
              width: 1px;
              height: 40px;
              align-self: center;
              background-color: #E8E8E8;
            }
          }

          .report-item-anomaly {
            margin-top: 8px;
            font-size: 15px;
            padding: 0 10px;
            font-weight: 400;
            line-height: 21px;
          }

          .report-item-include {
            color: #333;
            margin-top: 3px;
            font-size: 14px;
            padding: 0 10px;
            font-weight: 400;
            line-height: 18px;
          }

          .report-item-tips {
            padding: 0 10px;
            margin-top: 10px;

            p {
              color: #999;
              font-size: 14px;
              font-weight: 400;
              line-height: 21px;
            }
          }

          .report-item-no {
            color: #999;
            padding: 0 10px;
            font-size: 14px;
            margin-top: 10px;
            font-weight: 400;
            line-height: 21px;
            margin-bottom: 15px;
          }
        }
      }
    }

    .overview {
      display: flex;
      padding: 10px;
      justify-content: space-between;
      background: #fff url("~@/assets/images/blueman.png") no-repeat center;
      background-size: 90%;

      .overview-item {
        width: 50%;
        margin: 0;
        color: #333;
        display: flex;
        text-align: left;
        font-weight: 400;
        padding-left: 30px;
        box-sizing: border-box;
        justify-content: center;

        &:first-of-type {
          padding-left: 0;
          padding-right: 30px;
        }

        .overview-box {
          .overview-title {
            height: 22px;
            font-size: 16px;
            margin-top: 40px;
            line-height: 22px;
            padding-left: 6px;
          }

          .overview-txt {
            display: flex;
            font-size: 15px;
            margin-top: 6px;
            padding-left: 6px;
            align-items: center;

            .overview-num {
              margin: 0 5px;
              font-size: 24px;
              font-weight: 500;
              color: #7AC3FF;
            }

            img {
              width: 10px;
              margin-left: 3px;
            }
          }

          .overview-txt-abnormal {
             display: none;
          }

          .overview-txt-normal {
             display: flex;
             margin-top: 10px;
             align-items: center;

            .overview-num {
              margin: 0;
              font-size: 18px;
              font-weight: 600;
            }

            img {
              width: 20px;
              margin-left: 4px;
            }
          }
        }
      }
    }

    .overview-abnormal {
      background: #fff url("~@/assets/images/redman.png") no-repeat center;
      background-size: 90%;

      .overview-item {
        .overview-box {
          .overview-txt {
            .overview-num-grey {
              color: #A7A7A7;
            }

            .overview-num-red {
              color: #C35353;
            }

            .overview-num-green {
              color: #11CF98;
            }
          }

          .overview-txt-abnormal {
            display: block;
         }

        }
      }
    }
  }

  footer {
    margin: 10px 15px;
    min-height: 34px;
    .footer-tips {
      color: #FFF;
      font-size: 12px;
      font-weight: 400;
      text-align: left;
      line-height: 17px;
    }
  }
}

.wrapper-no {
  background-color: #fff;

  .no-data {
    width: 100%;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);

    img {
      width: 100%;
    }
  }
}
