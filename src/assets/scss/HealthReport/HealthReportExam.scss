.wrapper {
  overflow: hidden;
  background-color: #F5F6FA;
  min-height: -webkit-fill-available;

  .header {
    font-size: 18px;
    color: #262626;
    line-height: 24px;
    background-color: #fff;
  }

  .content {

    .attention {
      display: flex;
      padding: 10px 15px;
      align-items: flex-start;
      justify-content: flex-start;
      background-color: #FFFBF8;

      img {
        width: 18px;
        margin-top: 2px;
      }

      span {
        color: #999;
        font-size: 13px;
        text-align: left;
        margin-left: 3px;
        line-height: 20px;
      }
    }

    .group {
      padding: 15px;
      overflow: hidden;
      border-radius: 9px;
      margin: 15px 15px 0;
      background-color: #fff;

      .group-top {
        display: flex;
        justify-content: space-between;

        .curves-period {
          display: flex;
          align-items: center;

          .curves-btn {
            width: 24px;
            height: 24px;
            color: #999;
            display: flex;
            font-size: 16px;
            border-radius: 50%;
            align-items: center;
            justify-content: center;
            background-color: #fff;
            border: 1px solid #999;

            span {
              line-height: 22px;
            }
          }

          .curves-btn-click {
            color: #F67710;
            border-color: #F67710;
          }

          .curves-date {
            margin: 0 8px;
            font-size: 16px;
            color: #262626;
          }
        }
      }

      .group-title {
        display: flex;
        font-size: 15px;
        color: #262626;
        position: relative;
        align-items: center;

        &::before {
          content: "";
          width: 3px;
          height: 13px;
          background-color: #F67710;
          transform: translateY(-50%);
          position: absolute;
          left: -6px;
          top: 50%;
        }

        .group-title-unit {
          font-size: 13px;
          margin-left: 5px;
          color: #3F3F3F;
        }
      }

      .familybp-wrapper {
        margin-top: 10px;

        .bp-pie-echart {
          display: flex;
          align-items: center;
          margin-bottom: 10px;
          justify-content: space-between;

          .bp-pie-echart-left {
            width: 140px;
            height: 140px;
            position: relative;
            z-index: 0;

            .bp-pie-echart {
              width: 140px;
              height: 140px;
            }

            .bp-pie-echart-txt {
              width: 70%;
              height: 70%;
              display: flex;
              align-items: center;
              flex-direction: column;
              justify-content: center;
              overflow: hidden;
              border-radius: 50%;
              background-color: #fff;
              transform: translate(-50%, -50%);
              position: absolute;
              top: 50%;
              left: 50%;
              z-index: 0;

              .bp-pie-echart-txt-up {
                font-size: 14px;
                color: #262626;
                line-height: 20px;
              }

              .bp-pie-echart-txt-down {
                color: #000;
                font-size: 13px;
                text-align: left;
                line-height: 36px;

                span {
                  font-size: 28px;
                  color: #262626;
                }
              }
            }
          }

          .bp-pie-echart-right {
            flex: 1;
            display: flex;
            margin-left: 20px;
            flex-direction: column;
            justify-content: space-around;

            .bp-right-group {
              display: flex;
              align-items: center;
              justify-content: space-between;

              .bp-group-sign {
                width: 15px;
                height: 15px;
                border-radius: 50%;
              }

              .bp-group-sign1 {
                background: #C44353;
              }

              .bp-group-sign2 {
                background: #EF7C69;
              }

              .bp-group-sign3 {
                background: #5FDF98;
              }

              .bp-group-sign4 {
                background: #73CCF6;
              }

              .bp-group-tips {
                color: #666;
                font-size: 15px;
              }

              .bp-group-ratio {
                color: #666;
                font-size: 18px;
              }

              .bp-group-num {
                font-size: 18px;
                color: #2A2A2A;
                line-height: 36px;

                b {
                  color: #666;
                  font-size: 28px;
                }
              }
            }
          }
        }

        .bp-box {
          display: flex;
          align-items: center;
          justify-content: space-between;

          &>div {
            width: 49%;
            height: 80px;
            padding: 10px 20px;
            border-radius: 6px;
            box-sizing: border-box;

            &:first-of-type {
              background: url(../../../views/HealthReport/images/dbp-bg.png) no-repeat;
              background-size: cover;
            }

            &:last-of-type {
              background: url(../../../views/HealthReport/images/sbp-bg.png) no-repeat;
              background-size: cover;
            }

            .bp-box-title {
              height: 22px;
              font-size: 16px;
              color: #CD8C5B;
              line-height: 22px;
            }

            .bp-box-title2 {
              color: #956100;
            }

            .bp-content {
              display: flex;
              align-items: center;
              justify-content: space-between;

              .bp-top {
                font-size: 26px;
                color: #F67710;
              }

              .bp-down {
                font-size: 13px;
                color: #CD8C5B;
              }
            }
          }
        }

        .bg-box {
          display: flex;
          padding: 15px;
          border-radius: 6px;
          align-items: center;
          background: url(../../../views/HealthReport/images/bg-bg.png) no-repeat;
          background-size: cover;


          &>div {
            flex: 1;

            .bg-top {
              font-size: 26px;
              color: #F67710;
              line-height: 33px;
            }

            .bg-down {
              font-size: 13px;
              color: #CD8C5B;
              line-height: 18px;
            }
          }

          &>i {
            width: 1px;
            height: 28px;
            background: #CD8C5B;
          }
        }
      }

      .curves-tabs {
        width: 100%;
        overflow: auto;
        margin-top: 10px;

        &::-webkit-scrollbar {
          display: none;
        }

        .curves-tabs-box {
          width: 441px;
          height: 24px;

          .curves-item1 {
            width: 32px;
          }

          .curves-item2 {
            width: 48px;
          }

          .curves-item {
            float: left;
            display: flex;
            margin-right: 15px;
            align-items: center;
            flex-direction: column;

            &:last-of-type {
              margin: 0;
            }

            span {
              height: 20px;
              font-size: 16px;
              color: #262626;
              line-height: 20px;
              white-space: nowrap;
            }

            img {
              width: 100%;
              height: 4px;
              display: none;
            }
          }

          .curves-item-active {
            span {
              font-weight: bold;
            }

            img {
              display: block;
            }
          }
        }
      }

      .bar-echart {
        width: 100%;
        height: 180px;
        margin-top: 10px;
      }

      .line-echart {
        width: 100%;
        height: 260px;
        margin-top: 10px;

        &:first-child {
          margin-top: 0;
        }
      }

      .bmi-wrapper {
        display: flex;
        margin: 15px 0;
        align-items: center;
        justify-content: space-evenly;

        .bmi-group {
          width: 30%;

          .bmi-group-val {
            font-size: 26px;
            color: #262626;
            margin-bottom: 10px;
          }

          .bmi-group-name {
            color: #333;
            font-size: 13px;
          }
        }
      }

      .carotid-nodata-img {
        padding: 15px;
        border-bottom: 1px solid #E8E8E8;

        &:last-of-type {
          border-bottom: 0;
        }

        img {
          width: 150px;
        }

        p {
          color: #999;
          font-size: 13px;
          line-height: 18px;
        }
      }

      .carotid-nodata-tips {
        padding: 15px;

        &>div {
          position: relative;

          img {
            position: absolute;
            width: 18px;
            left: 4px;
            top: 4px;
          }

          p {
            color: #333;
            font-size: 15px;
            text-indent: 30px;
            text-align: left;
            line-height: 24px;
          }
        }
      }
    }

    .familybg-table-wrapper {
      padding: 15px 0;

      .group-title {
        margin-left: 15px;
      }

      .familybg-table-content {
        overflow: auto;
        margin-top: 10px;
        max-height: 360px;

        &::-webkit-scrollbar {
          display: none;
        }

        .familybg-table {
          width: 100%;
          border-top: 1px solid #E8E8E8;
          border-bottom: 1px solid #E8E8E8;

          tr {
            border-bottom: 1px solid #E8E8E8;

            th,
            td {
              height: 40px;
              width: 11.11%;
              color: #333;
              font-size: 16px;
              box-sizing: border-box;
              vertical-align: middle;
              background-color: #FAFBFC;
              border-right: 1px solid #E8E8E8;

              &:last-of-type {
                border-right: 0;
              }

              .record-up {
                color: #EF4026;
              }

              .record-down {
                color: #3EC77C;
              }
            }

            &:nth-of-type(odd) td {
              background-color: #fff
            }
          }
        }
      }
    }

    .exam-wrapper {
      overflow: hidden;
      margin: 0 15px 15px;

      .point {
        height: 35px;
        display: flex;
        align-items: center;

        &>div {
          display: flex;
          margin-right: 10px;
          align-items: center;

          img {
            width: 15px;
            margin-right: 4px;
          }

          span {
            color: #999;
            font-size: 13px;
          }
        }
      }

      .exam-content {
        overflow: hidden;
        border-radius: 9px;
        background-color: #fff;
      }

      .arteriosclerosis {
        overflow: hidden;
        border-radius: 9px;
        background-color: #fff;

        .arteriosclerosis-exam {
          display: flex;
          align-items: center;
          border-bottom: 1px solid #E8E8E8;

          .arteriosclerosis-exam-item {
            flex: 1;
            padding: 15px;
            border-right: 1px solid #E8E8E8;

            &:last-of-type {
              border: 0;
            }

            .arteriosclerosis-exam-title {
              font-size: 15px;
              color: #F67710;
              position: relative;
              margin-bottom: 15px;

              span {
                position: relative;
                z-index: 1;
              }

              i {
                width: 22px;
                height: 22px;
                border-radius: 50%;
                position: absolute;
                top: -3px;
                left: 50%;
                z-index: 0;
                transform: translateX(-50%);
                background-color: #FFECDE;
              }
            }

            .arteriosclerosis-exam-val {
              display: flex;
              align-items: center;
              margin-bottom: 10px;
              justify-content: space-between;

              &:last-of-type {
                margin: 0;
              }

              span {
                font-size: 16px;
                color: #333333;
                line-height: 18px;
              }

              b {
                max-width: 80px;
                font-size: 20px;
                color: #F28B05;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
              }
            }
          }
        }

        .arteriosclerosis-conclusion {
          padding: 15px;

          p {
            font-size: 16px;
            color: #333333;
            text-align: left;
            line-height: 24px;

            &:first-of-type {
              margin-bottom: 5px;
            }
          }
        }
      }

      .sugarnet {
        display: flex;
        overflow: hidden;
        position: relative;
        border-radius: 9px;
        align-items: flex-start;
        background-color: #fff;

        .sugarnet-item {
          flex: 1;
          padding: 15px;

          .sugarnet-title {
            font-size: 15px;
            color: #F67710;
            position: relative;
            margin-bottom: 15px;

            span {
              position: relative;
              z-index: 1;
            }

            i {
              width: 22px;
              height: 22px;
              border-radius: 50%;
              position: absolute;
              top: -3px;
              left: 50%;
              z-index: 0;
              transform: translateX(-50%);
              background-color: #FFECDE;
            }
          }

          .sugarnet-val {
            height: 36px;
            color: #333;
            font-size: 16px;
            text-align: left;
            line-height: 18px;
            margin-bottom: 15px;
          }

          .sugarnet-pics {
            width: 100%;

            .sugarnet-pic {
              width: 100%;
              padding-top: 100%;
              position: relative;
              margin-bottom: 15px;

              &:last-of-type {
                margin-bottom: 0;
              }

              img {
                position: absolute;
                height: 100%;
                width: 100%;
                left: 0;
                top: 0;
              }
            }

            .sugarnet-pic-no {
              width: 136px;

              img {
                width: 100%;
              }
            }

            div {
              display: flex;
              flex-direction: column;
            }

            span {
              color: #999;
              font-size: 12px;
              margin-top: 10px;
            }

          }
        }

        .sugarnet-line {
          width: 1px;
          height: 100%;
          background-color: #E8E8E8;
          position: absolute;
          left: 50%;
        }
      }

      .neuropathy {
        display: flex;
        padding: 15px;
        overflow: hidden;
        text-align: left;
        border-radius: 9px;
        align-items: flex-start;
        background-color: #fff;

        img {
          width: 18px;
        }

        .neuropathy-title {
          font-size: 16px;
          color: #F67710;
          margin-left: 2px;
          line-height: 18px;
          margin-right: 5px;
        }

        .neuropathy-answer {
          flex: 1;
          font-size: 16px;
          color: #151515;
          line-height: 18px;
        }
      }
    }

    .introduce-wrapper {
      padding: 0;
    }
  }

  footer {
    margin: 15px;
  }
}

.directionIcon{
  width: 30px;
  height: 30px;
}
.resultTips{
  font-size: 13px;
  text-align: left;
  line-height: 20px;
  margin-top: 10px;
  color: #999;
}