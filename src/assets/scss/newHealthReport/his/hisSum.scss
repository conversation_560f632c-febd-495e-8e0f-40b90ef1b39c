.wrapper {
  width: 100%;
  min-height: 100vh;
  background-color: #fff;

  .menu,
  .content {
    overflow-y: auto;
    overflow-x: hidden;
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;

    &::-webkit-scrollbar {
      width: 0;
    }
  }

  .menu {
    width: 100px;
    background-color: #F5F6FA;
    z-index: 2;

    .menu-box {
      display: flex;
      flex-direction: column;
      background-color: #fff;

      .menu-item {
        width: 100px;
        color: #666;
        display: flex;
        padding: 15px;
        font-size: 16px;
        text-align: left;
        font-weight: 400;
        line-height: 25px;
        align-items: center;
        box-sizing: border-box;
        background-color: #F5F6FA;
      }

      .menu-item-active {
        position: relative;
        background-color: #fff;

        .menu-item-text {
          color: #333;
          font-weight: bolder;
        }

        .menu-item-arrow {
          width: 10px;
          transform: translateY(-50%);
          position: absolute;
          right: 0;
          top: 50%;
        }
      }

      .menu-item-active::after {
        content: '';
        width: 5px;
        height: 100%;
        position: absolute;
        background: linear-gradient(90deg, #FF8100 0%, #FD9A57 100%);
        top: 0;
        left: 0;
      }

      .menu-item-active + .menu-item {
        border-radius: 0 9px 0 0;
      }

      .menu-item-prev {
        border-radius: 0 0 9px 0;
      }
    }
  }

  .content {
    width: 100%;
    box-sizing: border-box;
    padding: 10px 15px 10px 115px;
    z-index: 1;
  }
}