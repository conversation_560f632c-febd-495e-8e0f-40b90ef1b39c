.wrapper {
  height: 100vh;
  padding: 10px;
  box-sizing: border-box;
  background-color: #f3f3f3;

  .content {
    padding: 15px;
    overflow: hidden;
    border-radius: 5px;
    background-color: #fff;

    .date-seven {
      height: 28px;
      display: flex;
      margin-top: 10px;
      align-items: center;
      justify-content: center;

      .date-arrow {
        width: 70px;
        display: flex;
        color: #333333;
        align-items: center;
        justify-content: space-between;

        .arrow-text {
          font-size: 13px;
        }

        .arrow-icon {
          font-size: 18px;
        }
      }

      .date-comp {
        width: 200px;
        margin: 0 8px;
        color: #F67710;
        position: relative;

        .date-comp-cont {
          height: 25px;
          display: flex;
          font-size: 13px;
          color: #F67710;
          border-radius: 5px;
          align-items: center;
          justify-content: center;
          border: 1px solid #ffecdd;

          .date-comp-sign {
            margin: 0 2px;
          }

          .date-comp-icon {
            margin-left: 5px;
          }
        }

        .date-comp-input {
          width: 200px;
          height: 25px;
          font-size: 13px;
          text-align: center;
          border-radius: 5px;
          border: 1px solid #ffecdd;
          position: absolute;
          top: 0;
          left: 0;
          opacity: 0;
        }
      }
    }

    .date-tips {
      font-size: 13px;
      color: #999999;
      margin-top: 10px;
    }

    .tab {
      display: flex;
      flex-wrap: wrap;
      margin-top: 20px;

      .tab-item {
        width: 30%;
        height: 30px;
        font-size: 16px;
        color: #666666;
        font-weight: 400;
        line-height: 30px;
        border-radius: 3px;
        box-sizing: border-box;
        border: 1px solid #E8E8E8;
      }

      .tab-item:not(:nth-of-type(3n)) {
        margin-right: 5%;
      }

      .tab-item:not(:nth-of-type(-n + 3)) {
        margin-top: 10px;
      }

      .tab-item-select {
        color: #FF9600;
        border: 1px solid #FF9600;
      }
    }

    .chart {
      width: 100%;
      height: 180px;
      margin-top: 20px;

      .chart-box {
        width: 100%;
        height: 100%;
      }
    }

    .all-date {
      display: flex;
      padding: 0 15px;
      align-items: center;
      justify-content: space-between;

      .chart-date {
        width: 38px;
        height: 35px;
        color: #333;
        font-size: 12px;
        font-weight: 400;
        line-height: 35px;
        text-align: center;
        border-radius: 4px;
        background: linear-gradient(180deg, #FFF7F1 0%, #FFECDE 100%);
      }
    }

    .footer {
      margin-top: 20px;

      .footer-btn {
        font-size:18px;
        font-weight: 400;
        color: #F66031;
        padding: 5px 16px;
        line-height: 35px;
        border-radius: 18px;
        border:1px solid #F8875C;
      }
    }
  }

  .single-dialog {
    width: 320px;

    .single-wrapper {
      width: 100%;

      .single-title {
        width: 100%;
        height: 50px;
        color: #333;
        font-size: 20px;
        font-weight: 400;
        line-height: 50px;
      }

      .single-chart {
        width: 100%;
        height: 270px;
        padding: 0 10px;
        box-sizing: border-box;

        .chart-box {
          width: 100%;
          height: 100%;
        }
      }

      .single-footer {
        width: 100%;
        height: 40px;
        line-height: 40px;
        border-top: 1px solid #E8E8E8;

        .single-footer-btn {
          color: #333;
          font-size: 17px;
          font-weight: 400;
        }
      }
    }
  }
}
