.content {
  .sugarTitle {
    width: 375px;
    height: 50px;
    padding-top: 10px;
    line-height: 20px;
    background: url('https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/feedback.png')
      no-repeat 13px 20px rgba(255, 251, 241, 1);
    background-size: 19px 19px;
    font-size: 14px;
    color: #666;
    .title1 {
      width: 288px;
      height: 50px;
      display: block;
      float: left;
      margin-left: 42px;
      text-align: left;
      span {
        color: #666;
        font-weight: 400;
      }
    }
    .title2 {
      width: 22px;
      height: 22px;
      background: url('https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/close.png')
        no-repeat;
      background-size: cover;
      display: block;
      border-radius: 100%;
      overflow: hidden;
      float: right;
      margin: 7px 16px 0 0;
    }
  }
  .blood {
    width: 355px;
    margin: 0 auto;
    .bloodTitle {
      margin-top: 13px;
      margin-bottom: 3px;
      width: 355px;
      height: 50px;
      line-height: 50px;
      background: url('./../../assets/images/clock <EMAIL>') no-repeat 4px
        center;
      background-size: 19px 20px;
      .name {
        line-height: 50px;
        font-weight: 400;
        color: rgba(51, 51, 51, 1);
        font-size: 18px;
        display: block;
        float: left;
        padding-left: 30px;
      }
      .before {
        width: 104px;
        height: 33px;
        line-height: 33px;
        text-align: center;
        border-radius: 18px;
        border: 1px solid #f8875c;
        font-weight: 400;
        color: #f66031;
        font-size: 18px;
        display: block;
        margin-top: 8px;
        float: right;
        img {
          width: 5px;
          height: 10px;
          margin-left: 3px;
        }
      }
    }
    .reportStatus {
      width: 325px;
      min-height: 90px;
      border-radius: 6px;
      padding: 15px;
      text-align: left;
      .report-title {
        color: #fff;
        font-size: 23px;
        font-weight: 600;
        line-height: 32px;
      }
      .report-content {
        color: #fff;
        font-size: 18px;
        font-weight: 400;
        line-height: 28px;
      }
    }
    .normal {
      background: url('./../../assets/images/<EMAIL>') no-repeat;
      background-size: cover;
    }
    .high {
      background: url('./../../assets/images/<EMAIL>') no-repeat;
      background-size: cover;
    }
    .down {
      background: url('./../../assets/images/<EMAIL>') no-repeat;
      background-size: cover;
    }
    .sugar,
    .bloods {
      width: 331px;
      height: 54px;
      background: #f5f6fa;
      margin-top: 18px;
      border-radius: 9px;
      padding: 15px 12px;
      p {
        float: left;
      }
      .p1 {
        width: 50px;
        height: 54px;
        border-right: 1px solid #e8e8e8;
      }
      .p2 {
        width: 135px;
        height: 54px;
        float: left;
        .title {
          width: 133px;
          line-height: 24px;
          color: #333;
          font-size: 18px;
          float: left;
          text-align: left;
          padding-left: 7px;
          i {
            font-style: normal;
            font-size: 12px;
          }
        }
        p {
          width: 133px;
          height: 30px;
          line-height: 30px;
          color: #333;
          font-size: 20px;
          font-weight: 600;
          float: left;
          text-align: left;
          padding-left: 7px;
          span {
            float: left;
          }
          .colornormal {
            color: #07c160;
          }
          .colorhigh {
            color: #f8875c;
          }
          .colordown {
            color: #FF9600;
          }
          .colorno {
            color: #ccc;
          }
          img {
            width: 18px;
            height: 18px;
            float: left;
            margin-left: 3px;
            margin-top: 5px;
          }
        }
      }
      .sugarnormal {
        background: url('./../../assets/images/<EMAIL>')
          no-repeat center center;
        background-size: 29px 29px;
      }
      .sugarhigh {
        background: url('./../../assets/images/<EMAIL>')
          no-repeat center center;
        background-size: 29px 29px;
      }
      .sugardown {
        background: url('./../../assets/images/<EMAIL>')
          no-repeat center center;
        background-size: 29px 29px;
      }
      .sugarno {
        background: url('./../../assets/images/<EMAIL>')
          no-repeat center center;
        background-size: 29px 29px;
      }
      .bloodnormal {
        background: url('./../../assets/images/<EMAIL>')
          no-repeat center center;
        background-size: 29px 29px;
      }
      .bloodhigh {
        background: url('./../../assets/images/<EMAIL>') no-repeat
          center center;
        background-size: 29px 29px;
      }
      .blooddown {
        background: url('./../../assets/images/<EMAIL>')
          no-repeat center center;
        background-size: 29px 29px;
      }
      .bloodno {
        background: url('./../../assets/images/<EMAIL>')
          no-repeat center center;
        background-size: 29px 29px;
      }
      .p3 {
        width: 75px;
        height: 54px;
        span:nth-child(1) {
          font-size: 30px;
          color: #333;
          float: left;
          margin-left: 7px;
        }
        span:nth-child(2) {
          font-size: 16px;
          color: #333;
          float: left;
          margin-left: 3px;
          margin-top: 8px;
        }
        span:nth-child(3) {
          display: block;
          width: 100%;
          font-size: 16px;
          color: #999;
          float: left;
          margin-top: 5px;
        }
      }
      .p4 {
        width: 70px;
        height: 54px;
        span:nth-child(1) {
          display: block;
          font-size: 30px;
          color: #333;
        }
        span:nth-child(2) {
          display: block;
          width: 100%;
          font-size: 16px;
          color: #999;
          margin-top: 5px;
        }
      }
    }
    .advice {
      margin-top: 30px;

      .advice-title {
        height: 28px;
        color: #333;
        font-size: 20px;
        text-align: left;
        font-weight: 400;
        line-height: 28px;
      }

      .advice-box {
        padding: 15px;
        margin-top: 6px;
        border-radius: 9px;
        background: #f5f6fa;

        .advice-group {
          min-height: 50px;
          position: relative;

          .advice-icon {
            width: 34px;
            height: 50px;
            position: absolute;
            top: 0;
            left: 0;
          }

          .advice-text {
            color: #333;
            font-size: 18px;
            text-align: left;
            font-weight: 400;
            line-height: 30px;
            margin-left: 16px;
            padding-left: 32px;
            padding-bottom: 20px;
            border-left: 1px solid #E8E8E8;
          }

          &:last-of-type {

            .advice-text {
              border-left: 0;
              padding-bottom: 0;
            }
          }
        }
      }
    }

    .consult {
      width: 350px;
      font-size: 14px;
      color: #666;
      line-height: 20px;
      text-align: left;
      margin: 20px auto;
    }
  }
}
