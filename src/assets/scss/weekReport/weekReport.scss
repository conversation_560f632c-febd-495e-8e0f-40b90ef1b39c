.main{
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    // justify-content: center;
    align-items: center;
    background:#fff;
    .top{
        width: 375px;
        height: 377px;
        background: url('./../../assets/images/bg1.png') no-repeat #F5F6FA;
        background-size: 375px 377px;
        p{
            width: 100%;
            height: 36px;
            background: url('./../../assets/images/<EMAIL>') no-repeat center center;
            background-size: 198px 36px;
            margin-top: 30px;
        }
        .result{
            width:215px;
            height:170px;
            margin: 13px auto;
            img{
                width:215px;
                height:130px;
                float: left;
            }
            .times1{
                width: 215px;
                height: 40px;
                line-height: 40px;
                text-align: center;
                background: url('~@/assets/images/<EMAIL>') no-repeat center center;
                background-size: 159px 40px;
                float: left;
                font-size:20px;
                font-weight:400;
                color:rgba(255,255,255,1);
            }
            .times2{
                width: 215px;
                height: 40px;
                line-height: 40px;
                text-align: center;
                background: url('~@/assets/images/<EMAIL>') no-repeat center center;
                background-size: 159px 40px;
                float: left;
                font-size:20px;
                font-weight:400;
                color:rgba(255,255,255,1);
            }
        }
        .ranking{
            width: 126px;
            height: 34px;
            line-height: 38px;
            text-align: center;
            background: url('./../../assets/images/<EMAIL>') no-repeat center center;
            background-size: 136px 34px;
            font-size:18px;
            font-weight:400;
            color:rgba(255,255,255,1);
            margin: 13px auto;
            padding-left: 23px;
        }
    }
    .stage{
        width:325px;
        background:rgba(255,255,255,1);
        box-shadow:0px 2px 10px 0px rgba(0,0,0,0.1);
        border-radius:8px;
        margin-top: -52px;
        padding: 20px 10px;
        .target{
            width: 100%;
            height: 130px;
            img{
                width: 80px;
                height: 100px;
                float: left;
                margin-right: 15px;
            }
            .right{
                float: left;
                width:210px;
                p{  
                    width: 100%;
                    text-align: left;
                    font-size:18px;
                    font-weight:500;
                    color:rgba(0,0,0,1);
                    line-height:28px;
                }
                span{
                    display: inline-block;
                    text-align: left;
                    height:78px;
                    font-size:18px;
                    font-weight:400;
                    color:rgba(51,51,51,1);
                    line-height:26px;
                    i{
                        font-size:20px;
                        font-weight:600;
                        color: #FF7C35;
                        font-style: normal;
                    }
                }
            }
        }
        .managePlan{
            width: 325px;
            overflow: hidden;

            .managePlan-title {
                height: 48px;
                font-size: 22px;
                font-weight: 400;
                line-height: 48px;
                text-align: center;
            }

            .managePlan-title-h {
                color: #FFFFFF;
                border: 1px solid #FFD966;
                background: linear-gradient(0deg, rgba(250, 165, 55, 1) 0%, rgba(248, 135, 92, 1));
            }

            .managePlan-title-n {
                color: #787878;
                background: #F5F6FA;
                border: 1px solid #E8E8E8;
            }

            .managePlan-content {
                background: #FFFBF1;
                border: 1px solid #FFD966;
                border-top: 0;
            }

            .details{
                width: 300px;
                border-bottom: 2px solid #FFECDE;
                margin-left: 12px;
                p{  
                    width: 100%;
                    border-left: 4px solid #FF9600;
                    padding-left: 5px;
                    height: 20px;
                    font-size:20px;
                    font-weight:600;
                    color:rgba(220,133,53,1);
                    text-align: left;
                    margin-top: 20px;
                    float: left;
                }
                span{
                    line-height: 40px;
                    padding-left: 9px;
                    font-size:18px;
                    text-align: left;
                    display: block;
                    float: left;
                    font-weight:400;
                    color:rgba(51,51,51,1);
                    margin-bottom: 10px;
                    margin-top: 7px;
                    i{
                        font-size:30px;
                        font-weight:600;
                        color:rgba(255,124,53,1);
                        font-style: normal;
                    }
                    img{
                        width: 10px;
                        height: 22px;
                    }
                }
            }
            .details:last-child{
                border-bottom: 0;
            }
        }
    }
    .imgNoData{
        width: 343px;
        height: 50px;
        margin-top: -20px;
    }
    .title{
        width: 360px;
        margin-left: 15px;
        height:37px;
        font-size:26px;
        font-weight:400;
        color:rgba(51,51,51,1);
        line-height:37px;
        text-align: left;
        margin-top: 34px;
        margin-bottom: 10px;
    }
    .reportStatus {
        width: 325px;
        min-height: 90px;
        border-radius: 6px;
        padding: 10px;
        text-align: left;
        position: relative;
        .report-title {
            color: #fff;
            font-size: 23px;
            font-weight: 600;
            line-height: 32px;
        }
        .report-content {
            color: #fff;
            font-size: 18px;
            font-weight: 400;
            line-height: 28px;
        }
        img{
            width: 40px;
            height: 50px;
            position: absolute;
            bottom: -20px;
            right: 17px;
        }
    }
    .normal {
      background: url('./../../assets/images/<EMAIL>') no-repeat;
      background-size: cover;
    }
    .high {
      background: url('./../../assets/images/<EMAIL>') no-repeat;
      background-size: cover;
    }
    .down {
      background: url('./../../assets/images/<EMAIL>') no-repeat;
      background-size: cover;
    }
    .healthJob{
        width:315px;
        // height:400px;
        background:rgba(255,255,255,1);
        box-shadow:0px 2px 10px 0px rgba(0,0,0,0.1);
        border-radius:8px;
        margin-bottom: 16px;
        padding: 25px 15px;
        .money{
            float: left;
            p{
                height: 34px;
                line-height:34px;
                float: left;
                span{
                    font-size:18px;
                    font-weight:400;
                    color:rgba(51,51,51,1);
                    height: 34px;
                    line-height:34px;
                    display: block;
                    text-align: left;
                    float: left;
                    i{
                        font-size:20px;
                        font-weight:600;
                        color:rgba(255,124,53,1);
                        font-style: normal;
                        margin: 0 2px;
                    }
                }
                img{
                    width: 28px;
                    height: 29px;
                    float: left;
                    margin-top: 2px;
                    margin-left: 3px;
                }
            }
            span{
                font-size:18px;
                font-weight:400;
                color:rgba(51,51,51,1);
                height: 34px;
                line-height:34px;
                display: block;
                text-align: left;
                float: left;
                i{
                    font-size:20px;
                    font-weight:600;
                    color:rgba(255,124,53,1);
                    font-style: normal;
                    margin: 0 2px;
                }
            }
        }
        .imgUpload{
            width: 230px;
            height: 68px;
            margin: 10px 0;
            float: left;
        }
        .bg{
            width:315px;
            height:1px;
            float: left;
            background: #E8E8E8;
            margin-bottom: 14px;
        }
        .p{
            width: 100%;
            font-size:18px;
            font-weight:400;
            color:rgba(51,51,51,1);
            line-height:34px;
            text-align: left;
            float: left;
            img{
                width: 21px;
                height: 21px;
                float: left;
                margin: 6px 5px 0 0;
            }
            span{
                float: left;
            }
        }
    }
}