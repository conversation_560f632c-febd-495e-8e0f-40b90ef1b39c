.content{
    .sugarTitle{
        width:375px;
        height:50px;
        padding-top: 10px;
        line-height: 20px;
        background: url('https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/feedback.png') no-repeat 13px 20px rgba(255,251,241,1);
        background-size: 19px 19px;
        font-size: 14px;
        color: #666;
        .title1{
            width: 288px;
            height: 50px;
            display: block;
            float: left;
            margin-left: 42px;
            text-align: left;
            span{
                color: #666;
                font-weight:400;
            }
        }
        .title2{
            width: 22px;
            height: 22px;
            background:url('https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/close.png') no-repeat;
            background-size:cover;
            display: block;
            border-radius: 100%;
            overflow: hidden;
            float: right;
            margin:7px 16px 0 0;
        }
    }
    .blood{
      width: 355px;
      margin: 0 auto;
      .bloodTitle{
        margin-top: 15px;
        width: 355px;
        height: 40px;
        .name{
          font-weight:400;
          color:rgba(51,51,51,1);
          font-size: 20px;
          display: block;
          float: left;
          padding-left: 7px;
          border-left: 4px solid #FF7C35;
          margin-top: 10px;
        }
        .mark{
          width: 18px;
          height: 18px;
          line-height: 22px;
          text-align: center;
          border-radius: 100%;
          border: 1px solid #FCC569;
          font-weight:400;
          color:#FCC569;
          font-size: 18px;
          display: block;
          float: left;
          margin-top: 9px;
          margin-left: 5px;
        }
        .weekDate{
          font-weight:400;
          color:#999;
          font-size: 16px;
          display: block;
          float: right;
          margin-top: 10px;
          padding-left: 18px;
          background: url('https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/time.png') no-repeat;
          background-size:15px 15px ;
        }
      }
      .tabPic{
        width: 355px;
        height: 300px;
        background: #F5F6FA;
        .tab{
          height: 120px;
          ul{
            li{
              width: 100px;
              height: 28px;
              line-height: 28px;
              border-radius:3px;
              border:1px solid #E8E8E8;
              float: left;
              font-size: 16px;
              color: #666;
              margin: 9px 0 0 12px;
            }
            .active{
              border:1px solid #FF9600;
              color: #FF9600;
            }
          }
        }
        .sugarPic{
          height: 180px;
          margin-top: 5px;
        }
      }
      .bloodList{
        margin-bottom: 20px;
        ul{
          background: #F5F6FA;
          float: left;
          li{
            float: left;
            .listTime{
              width: 97%;
              padding-left: 3%;
              text-align: left;
              height: 28px;
              line-height: 28px;
              font-size: 15px;
              color: #999;
              border-bottom: 1px solid #398CFF;
            }
            .listDetail{
              width: 355px;
              float: left;
              margin: 0 auto;
              p{
                font-size: 15px;
                color: #666;
                display: block;
              }
              .time1{
                width: 25%;
                height: 34px;
                line-height: 34px;
                float: left;
                text-align: left;
                padding-left: 5%;
              }
              .time2{
                width: 40%;
                height: 34px;
                line-height: 34px;
                text-align: center;
                float: left;
              }
              .time3{
                width: 30%;
                height: 34px;
                line-height: 34px;
                float: left;
                text-align: center;
              }
              .active{
                width: 12px;
                height: 12px;
                display: inline-block;
                margin-left: 5px;
                background: #07c160;
                border-radius: 100%;
              }
              .bgHigh{
                background: red;
                margin-right: 5px;
              }
              .bgDown{
                background: #61a7ff;
                margin-right: 5px;
              }
              .bgNormal{
                background: #07C160;
                margin-right: 5px;
              }
              .colorHigh{
                color: red;
              }
              .colorDown{
                color: #61a7ff;
              }
              .colorNormal{
                color: #07C160;
              }
            }
          }
        }
      }
      .sugarTable{
        width: 355px;
        height: 283px;
        margin: 0 auto;
        .tables{
          width: 355px;
          background: rgba(122,213,255,0.05);
          border-radius: 6px;
          font-size: 13px;
          .tr{
            display: flex;
            width: 355px;
            border-top: 1px solid #fff;
            border-left: 1px solid #fff;
            color: #555;
            box-sizing: border-box;
            .td {
              width: 20%;
              justify-content: center;
              text-align: center;
              border-right: 1px solid #fff;
              .tdd1 {
                border-bottom: none;
                .thsmall {
                  justify-content: center;
                  display: flex;
                  align-items: center;
                  height: 31px;
                  color: #555;
                  border-right: 1px solid #fff;
                }
              }
              .tdd2 {
                border-bottom: 1px solid #fff;
                padding: 6px 4px;
              }

            }
            .th {
              justify-content: center;
              display: flex;
              height: 62px;
              align-items: center;
              color: #555;
              border-right: 1px solid #fff;
            }
            .td_center {
              display: flex;
              align-items: center;
            }
            .width1 {
              width: 13.34%;
            }
            .width4{
              width: 10%;
            }
            .width2 {
              width: 20%;
            }
            .width3{
              width: 50%;
            }
          }
          .table:nth-child(2), .table:nth-child(4), .table:nth-child(6), .table:nth-child(8){
            .data{
              background: #E0F0FD;
              font-size: 15px;
            }
          }
          .table:nth-child(1), .table:nth-child(3), .table:nth-child(5), .table:nth-child(7){
            .data{
              background: EEF7FF;
              font-size: 15px;
            }
          }
          .table{
            .date{
              background:rgba(122,213,255,0.05);
            }
            .thsmall {
              justify-content: center;
              display: flex;
              align-items: center;
              height: 31px;
              color: #555;
              border-right: 1px solid #fff;
            }
          }
        }
      }
    }
    .checkPic{
      width: 355px;
      height: 132px;
      background: #F5F6FA;
      margin: 0 auto;
      margin-top: 2px;
      .left{
        width: 120px;
        height: 132px;
        float: left;
      }
      .right{
        width: 230px;
        height: 132px;
        float: left;
        .one{
          width: 220px;
          height: 33px;
          line-height: 33px;
          color: #333;
          font-size: 18px;
          font-weight:400;
          text-align: left;
          margin-top: 8px;
          span{
            font-size:23px;
            font-weight:600;
            margin-left: 2px;
            color:rgba(7,193,96,1);
          }
          .color{
            color: #FF9600;
          }
          .colorRed{
            color: red;
          }
          .colorNor{
            color: #07C160;
          }
        }
        .two{
          width: 220px;
          height: 33px;
          margin-top: 8px;
          line-height: 33px;
          font-size: 18px;
          font-weight:400;
          color:rgba(102,102,102,1);
          text-align: left;
          .two1{
            width: 15px;
            height: 15px;
            display: inline-block;
            background: #07C160;
            border-radius: 100%;
            margin-right: 3px;
          }
          .two2{
            width: 15px;
            height: 15px;
            display: inline-block;
            background: #FF9600;
            border-radius: 100%;
            margin-left: 3px;
            margin-right: 3px;
          }
        }
        .three{
          width:160px;
          height:26px;
          margin-top: 8px;
          line-height: 26px;
          border-radius:13px;
          border:1px solid rgba(255,124,53,1);
          font-size: 16px;
          font-weight:400;
          color:rgba(255,124,53,1);
        }
      }
    }
    .normalSugar{
      width: 355px;
      font-size:14px;
      color: #666;
      height: 30px;
      line-height: 40px;
      text-align: left;
      margin: 0 auto;
    }
    .mask, .maskHelp{
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.6);
      position: fixed;
      top: 0;
      z-index: 9;
    }
    .maskContent{
      width: 320px;
      height: 222px;
      border-radius:10px;
      background: #fff;
      position: fixed;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      margin: auto;
      z-index: 99;
      .title{
        width: 100%;
        height: 60px;
        line-height: 60px;
        text-align: center;
        color: #333;
        font-size: 20px;
      }
      div{
        width: 280px;
        padding: 0 20px;
        line-height: 22px;
        font-size: 16px;
        color: #333;
        text-align: left;
        span{
          display: block;
        }
      }
      .tips {
        color: #666;
        font-size: 14px;
        margin-top: 5px;
        font-weight: 400;
        line-height: 20px;
      }
      .sure{
        width: 100%;
        height: 50px;
        line-height: 50px;
        font-size: 16px;
        color: #333;
        text-align: center;
        border-top: 1px solid #E8E8E8;
      }
    }
    .maskContentHelp{
      width: 320px;
      height: 298px;
      border-radius:10px;
      background: #fff;
      position: fixed;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      margin: auto;
      z-index: 99;
      .title{
        width: 100%;
        height: 60px;
        line-height: 60px;
        text-align: center;
        color: #333;
        font-size: 20px;
      }
      div{
        width: 280px;
        padding: 0 20px;
        height: 186px;
        line-height: 22px;
        font-size: 16px;
        color: #333;
        text-align: left;
        span{
          display: block;
        }
      }
      .sure{
        width: 100%;
        height: 50px;
        line-height: 50px;
        font-size: 16px;
        color: #333;
        text-align: center;
        border-top: 1px solid #E8E8E8;
      }
    }
}

