/* pages/home/<USER>/foodDetail/foodDetail.wxss */
.main{
  display: flex;
  flex-direction: column;
  align-items: center;
}

.foodImg{
  width: 350px;
  margin-top: 10px;
  border-radius: 6px;
}

.titleBox{
  width: 100%;
  height: 70px;
  display: flex;
  align-items: center;
}

.titleImg1{
  width: 19px;
  height: 19px;
  margin: 0 10px 0 23px;
}

.titleImg2{
  width: 23px;
  height: 19px;
  margin: 0 7px 0 23px;
}

.titleImg3{
  width: 20px;
  height: 20px;
  margin: 0 7px 0 24px;
}

.title{
  color:#000;
  font-size: 18px;
  line-height: 18px;
}

.elementBox{
  width: 350px;
  height: 200px;
  border-radius: 6px;
  box-shadow:0px 1px 9px 0px rgba(168,168,168,0.25);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.elementTop{
  display: flex;
  margin-top: 28px;
  align-items: center;
  color: #666;
  font-size: 13px;
}

.elementTop div:nth-child(1){
  width: 48px;
  height: 25px;
  border: 1px #EB7257 solid;
  color: #EB7257;
  font-size: 13px;
  border-radius: 13px;
  line-height: 25px;
  text-align: center;
  margin-right: 20px;
}

.elementTop div:nth-child(2){
  color: #000;
  font-size: 20px;
  margin-right: 10px;
}

.elementMid{
  margin: 14px 0 30px;
  color: #666;
  font-size: 13px;
  line-height: 18px;
}

.elementBottom{
  display: flex;
}

.proteinBox, .sugarBox{
  width: 119px;
  text-align: center;
  color: #666;
  font-size: 13px;
}

.proteinNum, .sugarNum{
  height: 44px;
  line-height: 44px;
}

.proteinNum span, .sugarNum span{
  color: #000;
  font-size: 20px;
}

.fatBox{
  width: 108px;
  text-align: center;
  color: #666;
  font-size: 13px;
}

.fatNum{
  height: 44px;
  line-height: 44px;
  border-left: 2px solid  #E5E5E5;
  border-right: 2px solid  #E5E5E5;
}

.fatNum span{
  color: #000;
  font-size: 20px;
}

.protein, .fat, .sugar{
  margin-top: 5px;
  color: #666;
  font-size: 13px;
}

.cookStepBox{
  width: 316px;
  border-radius: 6px;
  box-shadow:0px 1px 9px 0px rgba(168,168,168,0.25);
  padding: 17px;
}

.cookStepBox div{
  color: #666;
  font-size: 15px;
  line-height: 23px;
  text-align: left;
}

.moreElementBox{
  width: 311px;
  padding: 0 20px;
  margin-bottom: 24px;
  border-radius: 6px;
  box-shadow:0px 1px 9px 0px rgba(168,168,168,0.25);
}

.moreElementTitle{
  display: flex;
  width: 311px;
  margin: 0 auto;
}

.moreElementTitle div{
  font-size: 13px;
  color: #333;
  line-height: 53px;
  flex: 1;
  height: 53px;
  font-weight: 500;
  text-align: center;
}

.moreElementTitle div:nth-child(2){
  margin: 0 35px;
}

.morenElement{
  display: flex;
  width: 311px;
  margin: 0 auto;
  border-bottom: 1px #efefef solid;
}

.morenElement:last-child{
  border-bottom: none;
}

.morenElement div{
  font-size: 14px;
  color: #666;
  line-height: 53px;
  flex: 1;
  height: 53px;
  text-align: center;
}

.morenElement div:nth-child(2){
  margin: 0 25px;
}

