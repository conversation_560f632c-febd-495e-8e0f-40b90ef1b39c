.changeFoodType {
  display: flex;
  flex-direction: column;
  height: 90%;
  overflow: hidden;
  overflow-y: auto;
  margin-top: 10px;
  position: relative;
}

.foodTypeBox {
  display: flex;
  flex-direction: column;
  margin-bottom: 25px;
}

.foodModelTitle {
  display: flex;
  align-items: center;
  margin: 10px 0;
  padding: 0 20px;
}

.foodIcon {
  width: 4px;
  height: 20px;
  border-radius: 2px;
  background: #f67c49;
}

.itemTitle {
  display: flex;
  align-items: center;
  margin-top: 10px;
  padding: 0 20px;
}

.foodIcon1 {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #f67c49;
}

.foodType {
  font-size: 18px;
  color: #333;
  line-height: 18px;
  margin: 0 10px;
}

.foodType span{
  color: #000;
  font-size: 18px;
  font-weight: 600;
}

.foodType1 {
  font-size: 18px;
  color: #333;
  line-height: 17px;
  margin: 0 8px;
}

.foodType1 span{
  color: #F67C49!important;
  font-size: 17px;
  font-weight: 600;
}

.itemFoodImg {
  display: flex;
  flex-wrap: wrap;
  overflow: hidden;
  padding: 0 20px;
}

.foodTips {
  color: #999;
  font-size: 13px;
  line-height: 18px;
  margin-right: 50px;
}

.foodChangeBtn {
  width: 84px;
  height: 27px;
  border-radius: 14px;
  border: 1px #f67c49 solid;
  text-align: center;
  line-height: 27px;
  color: #f67c49;
  font-size: 14px;
}

.foodImgBox {
  display: flex;
  flex-wrap: wrap;
  overflow: hidden;
  padding: 0 20px;
}

.lookMore {
  height: 28px;
  width: 100%;
  background: url('https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/wechat/sugarController/riqibg3.png');
  background-size:cover;
  text-align: center;
  margin-bottom: 5px;
  font-size: 12px;
  color: #333;
}

.lookMore1{
  height: 40px!important;
}

.lookMore img {
  width: 11px;
  height: 7px;
}

.foodImg {
  display: flex;
  flex-direction: column;
  margin-top: 13px;
  margin-right: 11px;
  position: relative;
}

.checkImg {
  position: absolute;
  width: 75px;
  height: 75px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 6px;
}

.checkImg img {
  width: 24px !important;
  height: 19px !important;
  margin-top: 27px;
  margin-left: 7px;
}

.foodImg:nth-child(4n) {
  margin-right: 0 !important;
}

.foodImg img {
  width: 71px;
  height: 71px;
  border-radius: 6px;
}

.foodImg span {
  font-size: 16px;
  color: #333;
  line-height: 24px;
  text-align: center;
  margin-top: 6px;
  width: 75px;
  height: 48px;
}

.changeFoodBtn {
  background: linear-gradient(90deg, rgba(255, 186, 36, 1) 0%, rgba(254, 134, 23, 1) 100%);
  width: 325px;
  height: 50px;
  line-height: 50px;
  text-align: center;
  border-radius: 13px;
  font-size: 17px;
  color: #fff;
  margin: 0 auto;
  margin-top: 9px;
  margin-bottom: 9px;
}

.zhiyin{
  position: absolute;
  width: 48px!important;
  height: 48px!important;
  top: 100px;
  left: 50px;
  z-index: 2;
}