.main{
    width: 100%;
    height: 100%;
}
.pressureResult {
  display: flex;
  margin-top: 19px;
  padding: 0 12px;
}
.dataBox {
  display: flex;
  width: 170px;
  height: 90px;
  border-radius: 6px;
  box-shadow: 0px 0px 8px 0px rgba(201, 201, 201, 0.4);
  flex-direction: column;
}

.dataBox:nth-child(1) {
  background: linear-gradient(-54deg, rgba(235, 114, 87, 1) 0%, rgba(243, 148, 135, 1) 100%);
  margin-right: 10px;
}

.kcalTips {
  display: flex;
}

.kcalIcon {
  width: 20px;
  height: 23px;
  margin: 16px 9px 0 19px;
}

.kcalWords {
  color: #fff;
}

.kcalWords div:nth-child(1) {
  font-size: 14px;
  line-height: 19px;
  margin-top: 10px;
  text-align: left;
}

.kcalWords div:nth-child(2) {
  font-size: 12px;
  line-height: 19px;
}

.kcalNum {
  display: flex;
  color: #fff;
  font-size: 13px;
  line-height: 19px;
  padding-left: 33px;
  margin-top: 14px;
  align-items: flex-end;
}

.kcalNum span {
  font-size: 22px;
  margin: 0 5px;
}

.kcalNum div {
  font-size: 12px;
}

.weightTips {
  display: flex;
  color: #fff;
}

.weightTips div {
  font-size: 14px;
  line-height: 19px;
  margin-top: 17px;
}

.weightIcon {
  width: 23px;
  height: 20px;
  margin: 17px 10px 0 19px;
}

.weightNum {
  font-size: 12px;
  color: #fff;
  line-height: 19px;
  display: flex;
  align-items: flex-end;
  padding-left: 23px;
  margin-top: 10px;
}

.weightNum span {
  font-size: 22px;
  margin: 0 5px;
}

.weightNum div {
  width: 45px;
  height: 23px;
  border-radius: 12px;
  background: #fff;
  color: #f67c49;
  font-size: 12px;
  text-align: center;
  line-height: 25px;
}

.dataBox:nth-child(2) {
  background: linear-gradient(-56deg, rgba(108, 143, 228, 1) 0%, rgba(115, 168, 235, 1) 100%);
}

.titleBox {
  display: flex;
  height: 79px;
  align-items: center;
  justify-content: space-between;
}

.titleBox div:nth-child(1) {
  font-size: 21px;
  color: #333;
  font-weight: 500;
  margin-left: 17px;
}

.titleBox div:nth-child(2) {
  margin: 0 18px 0 102px;
  background: linear-gradient(90deg, rgba(244, 187, 79, 1) 0%, rgba(238, 142, 54, 1) 100%);
  border-radius: 16px;
  color: #fff;
  font-size: 14px;
  width: 108px;
  height: 31px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.call {
  width: 18px;
  height: 17px;
  margin-right: 6px;
}

.switch {
  width: 56px;
  height: 31px;
}

.eatTimeBox {
  width: 350px;
  border-radius: 6px;
  box-shadow: 0px 0px 8px 0px rgba(201, 201, 201, 0.4);
  margin: 0 auto;
  margin-bottom: 15px;
}

.foodTitle {
  display: flex;
  align-items: center;
  height: 51px;
}

.foodTitle img {
  width: 25px;
  height: 25px;
  margin-left: 11px;
}

.foodTitle span {
  margin: 0 185px 0 12px;
  color: #302f2f;
  font-size: 14px;
}

.foodTitle div {
  width: 72px;
  height: 27px;
  line-height: 28px;
  text-align: center;
  border: 1px #efb243 solid;
  border-radius: 14px;
  font-size: 13px;
  color: #efb243;
}

.foodBox {
  display: flex;
  flex-direction: column;
  padding-bottom: 20px;
}

.food {
  display: flex;
  margin-top: 10px;
  align-items: center;
}

.food img {
  width: 45px;
  height: 45px;
  border-radius: 5px;
  margin-left: 13px;
}

.food span {
  color: #302f2f;
  font-size: 16px;
  padding: 2px 0;
  width: 165px;
  margin-left: 10px;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  text-align: left;
}

.foodNum {
  width: 90px;
  text-align: right;
  color: #666;
  font-size: 14px;
  margin-left: 15px;
}

.footer {
  margin: 6px 32px 70px 32px;
  color: #999;
  font-size: 14px;
  line-height: 20px;
  text-align: left;
}
.clockIn{
  width:375px;
  height:60px;
  position: fixed;
  bottom: 0;
  background:rgba(255,255,255,1);
  box-shadow:0px 2px 10px 0px rgba(0,0,0,0.15);
  p.status {
    width: 240px;
    height: 60px;
    line-height: 60px;
    font-size:15px;
    font-weight:400;
    color:rgba(51,51,51,1);
    float: left;
    i{
      font-style: normal;
      float: left;
      margin-left: 9px;
    }
    span{
      width: 15px;
      height: 15px;
      display: block;
      float: left;
      margin-top: 22px;
      margin-left: 4px;
    }
  }
  .checkBtn{
    .clockOut{
      width:110px;
      height:35px;
      background:linear-gradient(90deg,rgba(255,129,0,1) 0%,rgba(253,154,87,1) 100%);
      box-shadow:0px 2px 4px 0px rgba(255,206,147,1);
      border-radius:18px;
      float: left;
      font-size:18px;
      font-weight:600;
      color:rgba(255,255,255,1);
      line-height:35px;
      margin: 12px 0 0 12px;
    }
    .clock {
      width:110px;
      height:35px;
      background:rgba(255,206,147,1);
      border-radius:18px;
      float: left;
      font-size:18px;
      font-weight:600;
      color:rgba(255,255,255,1);
      line-height:35px;
      margin: 12px 0 0 12px;
    }
  }
}
.otherClock{
  width:90px;
  padding-left: 20px;
  height:35px;
  line-height: 37px;
  background:url('https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/Rounded.png') no-repeat 16px center rgba(245,246,250,1);
  background-size: 18px 18px;
  box-shadow:0px 2px 10px 0px rgba(0,0,0,0.1);
  border-radius:25px;
  border:2px solid rgba(232,232,232,1);
  font-size:18px;
  font-weight:600;
  color:rgba(167,167,167,1);
  text-shadow:0px 2px 10px rgba(0,0,0,0.1);
  position: fixed;
  bottom: 12px;
  right: 15px;
}
.calendarBox {
  display: flex;
  flex-direction: column;
  width: 375px;
  height: 101px;
  align-items: center;
  /* justify-content: center; */
  background-size: cover;
  position: relative;
}

.monthDays {
  height: 227px !important;
  // height: 272px !important;
  justify-content: flex-start !important;
}
.foodRemind{
  width: 336px;
  line-height: 18px;
  padding: 7px;
  font-size: 15px;
  text-align: left;
  margin: 10px auto;
  border-radius: 6px;
  background: rgba(215, 215,215, 1);
}
.calendar {
  width: 100%;
  height: 93px;
  display: flex;
  flex-direction: column;
}

.calendarMonth {
  height: 227px !important;
}

.weekBox {
  display: flex;
  height: 35px;
  justify-content: space-around;
}

.weekBox div {
  height: 35px;
  line-height: 35px;
  color: #a3a3a3;
  font-size: 12px;
}

.dayBox {
  display: flex;
  height: 33px;
  justify-content: space-around;
}

.dayBox div {
  width: 33px;
  height: 33px;
  border-radius: 50%;
  line-height: 33px;
  color: #282828;
  font-size: 14px;
  text-align: center;
}

.active {
  width: 33px !important;
  background: #faaf19;
  color: #fff !important;
  border-radius: 50%;
  margin: 0 auto;
}

.active1 {
  width: 33px !important;
  background: #faaf19;
  color: #fff !important;
  border-radius: 50%;
}

.more{

}

.xiaLa {
  width: 22px;
  height: 13px;
  position: absolute;
  left: 0;
  right: 6px;
  padding: 4px 6px;
  bottom: 0;
  top: 73px;
  margin: auto;
}

.xiaLaMonth {
  right: -1px !important;
  top: 195px !important;
}

/* 一个月日历 */

.header {
  height: 35px;
  display: flex;
  justify-content: space-around;
}

.header>div {
  color: #a3a3a3;
  font-size: 12px;
  height: 35px;
  line-height: 35px;
}

.weekMark {
  position: relative;
  width: 100%;
  margin: 0 auto;
}

.date-box {
  font-size: 0;
  width: 100%;
  margin: 0 auto;
}

.date-box>div {
  position: relative;
  display: inline-block;
  width: 14.285%;
  color: #282828;
  text-align: center;
  vertical-align: middle;
  font-size: 14px;
  float: left;
}

.date-head {
  height: 33px;
  line-height: 33px;
  font-size: 14px;
}

.nowDay .date-head {
  width: 33px;
  height: 33px;
  border-radius: 50%;
  text-align: center;
  color: #fff;
  background-color: #faaf19;
  margin: 0 auto;
}

.changeFoodModel{
  position: fixed;
  width: 340px;
  height: 540px;
  background: #fff;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  margin: auto;
  display: flex;
  flex-direction: column;
}

.changeFoodTitle{
  display: flex;
  margin-top: 23px;
}

.changeFoodTitle img{
  width: 14px;
  height: 14px;
  margin-left: 25px;
  margin-right: 20px;
}

.changeFoodTitle div{
  color: #000;
  font-size: 19px;
  line-height: 12px;
}

.changeFoodType{
  display: flex;
  /* padding: 0 60rpx 40rpx 60rpx; */
  flex-direction: column;
  height: 440px;
  overflow: hidden;
  overflow-y: auto;
  margin-top: 10px;
}

.foodTypeBox{
  display: flex;
  flex-direction: column;
}

.foodModelTitle{
  display: flex;
  align-items: center;
  margin-top: 10px;
  padding: 0 30px;
}

.foodIcon{
  width: 4px;
  height: 20px;
  border-radius: 2px;
  background: #F67C49;
}

.itemTitle{
  display: flex;
  align-items: center;
  margin-top: 10px;
  padding: 0 30px;
}

.foodIcon1{
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #F67C49;
}

.foodType{
  font-size: 14px;
  color: #F67C49;
  line-height: 18px;
  margin: 0 10px;
}

.foodType1{
  font-size: 14px;
  color: #F67C49;
  line-height: 18px;
  margin: 0 8px;
}

.itemFoodImg{
  display: flex;
  flex-wrap: wrap;
  overflow: hidden;
  margin-bottom: 15px;
  padding: 0 30px;
}

.foodTips{
  color: #999;
  font-size: 13px;
  line-height: 18px;
  margin-right: 50px;
}

.foodChangeBtn{
  width: 84px;
  height: 27px;
  border-radius: 14px;
  border: 1px #F67C49 solid;
  text-align: center;
  line-height: 27px;
  color: #F67C49;
  font-size: 14px;
}

.foodImgBox{
  display: flex;
  flex-wrap: wrap;
  overflow: hidden;
  padding: 0 30px;
}

.lookMore{
  height: 28px;
  width: 100%;
  background-size: cover;
  text-align: center;
  margin-bottom: 5px;
  font-size: 13px;
  color: #666;
}

.lookMore img{
  width: 11px;
  height: 7px;
}

.foodImg{
  display: flex;
  flex-direction: column;
  margin-top: 15px;
  margin-right: 25px;
  position: relative;
}

.checkImg{
  position: absolute;
  width: 50px;
  height: 50px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 6px;
}

.checkImg img{
  width: 24px!important;
  height: 19px!important;
  margin-top: 15px;
  margin-left: 13px;
}

.foodImg:nth-child(4n){
  margin-right: 0!important;
}

.foodImg img{
  width: 50px;
  height: 50px;
  border-radius: 6px;
}

.foodImg span{
  font-size: 12px;
  color: #666;
  line-height: 18px;
  text-align: center;
  margin-top: 6px;
  width: 50px;
  height: 36px;
}

.changeFoodBtn{
  background:linear-gradient(90deg,rgba(244,187,79,1) 0%,rgba(238,142,54,1) 100%);
  width: 275px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  border-radius: 6px;
  font-size: 17px;
  color: #fff;
  margin: 0 auto;
  margin-top: 5px;
}

.fixed{
   max-height: 225px;
}

.calendarTitle{
  font-weight:600;
  font-size: 16px;
  line-height: 32px;
  width: 375px;
  text-align: center;
}
div.color1{
  color: #A6A8FD
}
div.color2{
  color: #91E464
}
div.color3{
  color: #FCC569
}
div.color4{
  color: #EE7C68
}

.mask{
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    z-index: 99;
    background:rgba(0,0,0,0.8);
}
.remarkTipsModal{
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
  width: 100%;
  height: 100%;
  .imgTips1{
      width: 18px;
      height: 18px;
      display: block;
      margin: 28px 0 11px 21px;
    }
    .contentTips{
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      .imgTips2{
        width: 335px;
        height: auto;
      }
      .titleTips1{
        height: 22px;
        font-size: 16px;
        font-weight: 400;
        color: #DDDDDD;
        line-height: 22px;
        margin-bottom: 10px;
      }
      .titleTips2{
        height: 28px;
        font-size: 20px;
        font-weight: 500;
        color: #FFFFFF;
        line-height: 28px;
        margin-bottom: 12px;
      }
      .titleTips3{
        width: 100%;
        height: 25px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 61px;
        .spanTips1{
          display: inline-block;
          width: 70px;
          height: 2px;
          background: linear-gradient(117deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.5) 100%);
          margin-right: 6px;
        }
        .spanTips4{
          display: inline-block;
          width: 70px;
          height: 2px;
          background: linear-gradient(117deg, rgba(255, 255, 255, 0.5) 0%, rgba(255, 255, 255, 0) 100%);
          margin-left: 6px;
        }
        .spanTips2{
          font-size: 18px;
          font-weight: 500;
          color: #F4D96C;
          line-height: 25px;
        }
        .imgTips3{
          width: 19px;
          height: 19px;
          margin: 0 4px;
        }
        .spanTips3{
          font-size: 18px;
          font-weight: 500;
          color: #F4D96C;
          line-height: 25px;
        }
      }
      .btnTips{
        width: 153px;
        height: 40px;
        background: linear-gradient(90deg, #FFBB4F 0%, #FF6F26 100%);
        border-radius: 20px;
        font-size: 18px;
        font-weight: 500;
        color: #FFFFFF;
        line-height: 40px;
      }
    }
}
.additionalModal{
  width:335px;
  height:360px;
  background:rgba(255,255,255,1);
  border-radius:9px;
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  margin: auto;
  z-index: 999;
  h2{
    width: 100%;
    height: 48px;
    span{
      height: 48px;
      float: left;
      display: block;
    }
    span:nth-child(1) {
      width: 20%;
    }
    span:nth-child(2) {
      width: 60%;
      line-height: 68px;
      font-size:20px;
      font-weight:400;
      color: #000;
    }
    span:nth-child(3) {
      width: 20%;
      background:url('https://zz-med-national.oss-cn-hangzhou.aliyuncs.com/H5/food_close.jpg') no-repeat center 17px;
      background-size: 25px 25px;
    }
  }
  div.detail{
    // border: 1px solid pink;
  }
  p.title{
    width: 100%;
    height: 60px;
    text-align: center;
    font-size:14px;
    font-weight:400;
    color:rgba(51,51,51,1);
    line-height:50px;
  }
  p.left{
    width: 46%;
    height: 56px;
    line-height: 56px;
    float: left;
    img{
      width: 26px;
      height: 26px;
      float: left;
      margin: 12px 6px 0 70px;
    }
    span{
      float: left;
      font-size:18px;
      font-weight:400;
      color:rgba(51,51,51,1);
    }

  }
  p.right{
    width: 54%;
    height: 56px;
    line-height: 56px;
    float: left;
    text-align: left;
  }
  .smile{
    margin: 0 auto;
    img{
      width: 20px;
      height: 20px;
      float: left;
      margin: 17px 5px 0 17px;
    }
    span{
      float: left;
      font-size:18px;
    font-weight:600;
    color:rgba(167,167,167,1);
    }
  }
  .buka{
    width:100px;
    height:36px;
    line-height: 36px;
    display: block;
    text-align: center;
    background:linear-gradient(90deg,rgba(255,129,0,1) 0%,rgba(253,154,87,1) 100%);
    box-shadow:0px 2px 4px 0px rgba(255,206,147,1);
    border-radius:18px;
    font-size:15px;
    font-weight:600;
    color: #fff;
    margin-left: 14px;
    margin-top: 10px;
  }
  .waiting{
    display: block;
    text-align: left;
    font-size:20px;
    font-weight:400;
    color:rgba(204,204,204,1);
  }
}