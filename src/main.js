/*
 * @Descripttion:
 * @version:
 * @Author: xulh
 * @Date: 2021-05-24 10:35:49
 * @LastEditors: xulh
 * @LastEditTime: 2021-05-26 15:05:41
 */
import './assets/scss/vant/index.scss'
import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import './components'
import './assets/icons'
import { Toast, Calendar, Lazyload } from 'vant'
// import 'lib-flexible/flexible.js'
import './utils/rem' // px转rem
import FastClick from 'fastclick'
// import growingio from 'vue-growingio'
import Print from 'vue-print-nb'
import i18n from './i18n/index'
import { debounce } from './utils/debounce.js';
import { getSystemType } from './utils/utils';
Vue.prototype.$debounce = debounce;

FastClick.attach(document.body)

FastClick.prototype.focus = function(targetElement) {
  var length;
  //兼容处理:在iOS7中，有一些元素（如date、datetime、month等）在setSelectionRange会出现TypeError
  //这是因为这些元素并没有selectionStart和selectionEnd的整型数字属性，所以一旦引用就会报错，因此排除这些属性才使用setSelectionRange方法
  if (getSystemType() === 'ios' && targetElement.setSelectionRange && targetElement.type.indexOf('date') !== 0 && targetElement.type !== 'time' && targetElement.type !== 'month' && targetElement.type !== 'email') {
      length = targetElement.value.length;
      targetElement.setSelectionRange(length, length);
      /*修复bug ios 11.3不弹出键盘，这里加上聚焦代码，让其强制聚焦弹出键盘*/
      targetElement.focus();
  } else {
      targetElement.focus();
  }
};

if (process.env.NODE_ENV === 'production') {
  // Vue.use(growingio, '97a93c40a8d42a48')
}
Vue.use(Print);
Vue.use(Calendar);
Vue.use(Lazyload);

Vue.config.productionTip = false
Vue.prototype.baseUrl = process.env.VUE_APP_BASE_URL
Vue.config.$btnDisabled = true

Vue.prototype.noDoubleTap = function (fn, autoAble = true) {
  if (this.$btnDisabled || this.$btnDisabled === undefined || this.$btnDisabled === null) {
    this.$btnDisabled = false
    if (autoAble === true) {
      setTimeout(() => {
        this.$btnDisabled = true
      }, 1000)
    }
    fn()
  }
}
Vue.prototype.$toast = message =>
  Toast({
    message,
    type: 'html',
    forbidClick: true,
    duration: 1500
  })
new Vue({
  router,
  store,
  i18n,
  render: h => h(App)
}).$mount('#app')
