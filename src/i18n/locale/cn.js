module.exports = {
  mmcQuestionH5: {
    manageTimeName: '管理周期',
    interviewName:'次访视',
    fillOutName: '去填写',
    realName: '请核对您的真实信息',
    realInfoNmae: '根据信息为您提供精准的服务',
    labelName: '姓名',
    MobileName: '手机号',
    identityIdName: '身份证',
    startTopicName:'开始答题',
    middleBottomTips1: '为了让医生更好的为您诊断，',
    middleBottomTips2: '请及时准确的填好随访问卷，谢谢！',
    youComplete: '您已完成',
    fillQuestionnaire: '问卷的填写',
    pleaseClick: '请点击',
    questionnaire: '问卷',
    fillIn: '填写',
    continueBtnText: '继续',
    finishBtnText: '已完成',
    returnQuestionnaireList: '返回问卷列表',
    coffeeTeaTipsTitle: '尊敬的MMC代谢中心患者：',
    coffeeTeaTipsHi: '您好！',
    coffeeTeaTipsText: '为了提供给您更精准的健康生活指导，需要您抽出宝贵的时间回答几个关于咖啡与茶、健康状态的问题，大概需要3分钟时间，您是否愿意继续回答？',
    inconvenienceBtn: '不方便',
    okBtn: '好的',
    continueImprove: '继续完善',
    unfinished: '未完成的',
    PreviousQuestion: '上一题',
    nextQuestion: '下一题',
    SingleChoiceQuestion: '单选题',
    MultipleChoiceQuestion: '多选题',
    gapFillingQuestion: '填空题',
    schedule:'填写进度',
    notLogin: '用户未登录',
    noMMCQuestionnaire: '您还没有MMC问卷',
    joinMMC: '请先到MMC中心登记成为我们的一员'
  }
}