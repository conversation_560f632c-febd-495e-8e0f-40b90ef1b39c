module.exports = {
  mmcQuestionH5: {
    realName: 'Please verify your true information',
    realInfoNmae: 'Provide you with precise services based on information',
    labelName: 'full name',
    MobileName: 'phone number',
    identityIdName: 'ID card',
    startTopicName: 'Start answering questions',
    manageTimeName: 'Management cycle',
    interviewName: 'Visits',
    fillOutName: 'Go fill in',
    middleBottomTips1: 'In order for doctors to diagnose you better,',
    middleBottomTips2: 'Please fill out the follow-up questionnaire promptly and accurately. Thank you!',
    youComplete: 'You have completed ',
    fillQuestionnaire: ' filling out the questionnaire',
    pleaseClick: 'Click ',
    questionnaire: 'questionnaire',
    fillIn: ' fill in ',
    continueBtnText: 'Continue',
    finishBtnText: 'Completed',
    returnQuestionnaireList: 'Return to questionnaire list',
    coffeeTeaTipsTitle: 'Dear MMC Metabolic Center Patients:',
    coffeeTeaTipsHi: 'Hello!',
    coffeeTeaTipsText: 'In order to provide you with more accurate guidance on healthy living, we need you to take some valuable time to answer a few questions about coffee and tea, and health status, which will take about 3 minutes. Would you be willing to continue answering?',
    inconvenienceBtn: 'Inconvenience',
    okBtn: 'Ok',
    continueImprove: 'Continue to improve',
    unfinished: 'unfinished',
    PreviousQuestion: 'Previous question',
    nextQuestion: 'Next question',
    SingleChoiceQuestion: 'Multiple Choice',
    MultipleChoiceQuestion: 'Multiple choice questions',
    gapFillingQuestion: 'gapFillingQuestion',
    schedule: 'Fill in progress',
    notLogin: 'User not logged in',
    noMMCQuestionnaire: "You don't have the MMC questionnaire yet",
    joinMMC: 'Please register as one of our members at the MMC center first'
  }
}