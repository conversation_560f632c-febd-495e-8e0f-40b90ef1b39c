import * as types from '../mutation-types'

// state
export const state = {
  tabId: localStorage.getItem('tabId')
}

// getters
export const getters = {
  tabId: state => state.tabId
}

// mutations
export const mutations = {
  [types.docDynamicTabId](state, tabId) {
    console.log(tabId, 'tabId')
    state.tabId = tabId
    localStorage.setItem('tabId', tabId)
  }
}

// actions
export const actions = {
  saveTabId({ commit, dispatch }, payload) {
    commit(types.docDynamicTabId, payload)
  }
}
