// state
export const state = {
  platform: localStorage.getItem('docworkroom-platform'),
  searchInfo: localStorage.getItem('docworkroom-search-info') || '{}',
  navigatorInfo: localStorage.getItem('docworkroom-navigator-info') || '{}',
}

// getters
export const getters = {
  platform: state => state.platform,
  isApp: state => state.platform === 'saas',
  platformColor: state => state.platform === 'saas' ? '#F7830D' : '#3388FF',
  searchInfo: state => JSON.parse(state.searchInfo),
  navigatorInfo: state => JSON.parse(state.navigatorInfo),
}

// mutations
export const mutations = {
  SET_PLATFORM_DATA(state, data) {
    state.platform = data
    localStorage.setItem('docworkroom-platform', data)
  },
  SET_SEARCHINFO_DATA(state, data) {
    const prvData = JSON.parse(state.searchInfo)
    const NexData = JSON.stringify({ ...prvData, ...data })
    state.searchInfo = NexData
    console.log('datadata')
    console.log(data)
    localStorage.setItem('docworkroom-search-info', NexData)
  },
  SET_NAVIGATOR_DATA(state, data) {
    const prvData = JSON.parse(state.navigatorInfo)
    const NexData = JSON.stringify({ ...prvData, ...data })
    state.navigatorInfo = NexData
    localStorage.setItem('docworkroom-navigator', NexData)
  },
}

// actions
export const actions = {
  setPlatform({ commit }, data) { // app/小程序标识
    localStorage.setItem('docworkroom-search-info', '{}')
    commit('SET_PLATFORM_DATA', data)
  },
  setSearchInfo({ commit }, data) { // 保存病案搜索参数
    commit('SET_SEARCHINFO_DATA', data)
  },
  setNavigator({ commit }, data) { // 保存当前平台环境标识
    commit('SET_NAVIGATOR_DATA', data)
  }
}
