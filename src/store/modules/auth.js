import * as types from '../mutation-types'

// state
export const state = {
  user: localStorage.getItem('user')
}

// getters
export const getters = {
  user: state => state.user
}

// mutations
export const mutations = {
  [types.SAVE_TOKEN](state, { token, expires }) {
    state.token = token
  }
}

// actions
export const actions = {
  saveToken({ commit, dispatch }, payload) {
    commit(types.SAVE_TOKEN, payload)
  }
}
