// 禁止H5页面微信端的字体样式紊乱
export function getBanFontSize () {
  if (typeof WeixinJSBridge == "object" && typeof WeixinJSBridge.invoke == "function") {
    handleFontSize()
  } else {
    document.addEventListener("WeixinJSBridgeReady", handleFontSize, false)
  }
}

function handleFontSize() {
  // 设置网页字体为默认大小
  WeixinJSBridge.invoke('setFontSizeCallback', { 'fontSize' : 0 })
  // 重写设置网页字体大小的事件
  WeixinJSBridge.on('menu:setfont', function() {
    WeixinJSBridge.invoke('setFontSizeCallback', { 'fontSize' : 0 })
  })
}

/**
 * 深拷贝
 */
export function deepCopy (obj) {
  // 只拷贝对象
  if (typeof obj !== 'object') return
  // 根据obj的类型判断是新建一个数组还是一个对象
  let newObj = obj instanceof Array ? [] : {}
  for (let key in obj) {
    // 遍历obj,并且判断是obj的属性才拷贝
    if (obj.hasOwnProperty(key)) {
      // 判断属性值的类型，如果是对象递归调用深拷贝
      newObj[key] = typeof obj[key] === 'object' ? deepCopy(obj[key]) : obj[key]
    }
  }
  return newObj
}

/**
 * 获取可视范围的高度
 */
export function getClientHeight () {
  var clientHeight = 0
  if (document.body.clientHeight && document.documentElement.clientHeight) {
    clientHeight = Math.min(document.body.clientHeight, document.documentElement.clientHeight)
  } else {
    clientHeight = Math.max(document.body.clientHeight, document.documentElement.clientHeight)
  }
  return clientHeight
}

/**
 * 获取当前滚动条的位置
 */
export function getScrollTop () {
  var scrollTop = 0
  if (document.documentElement && document.documentElement.scrollTop) {
    scrollTop = document.documentElement.scrollTop
  } else if (document.body) {
    scrollTop = document.body.scrollTop
  }
  return scrollTop
}

/**
 * 获取文档完整的高度
 */
export function getScrollHeight () {
  return Math.max(document.body.scrollHeight, document.documentElement.scrollHeight)
}

export function getNowTime(count = 0) {
  var timestamp = Date.parse(new Date())
  timestamp = timestamp / 1000
  var n = timestamp * 1000
  var date = new Date(n + count)
  // 年
  var Y = date.getFullYear()
  // 月
  var M =
    date.getMonth() + 1 < 10
      ? '0' + (date.getMonth() + 1)
      : date.getMonth() + 1
  // 日
  var D = date.getDate() < 10 ? '0' + date.getDate() : date.getDate();

  // 时
  var h = date.getHours() < 10 ? '0' + date.getHours() : date.getHours();
  // 分
  var m = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes();
  // 秒
  var s = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds();

  return {
    timestamp: timestamp,
    time: Y + '-' + M + '-' + D + ' ' + h + ':' + m,
    ctsTime: Y + '' + M + '' + D + '' + h + '' + m + '' + s,
    ctsDay: Y + '-' + M + '-' + D,
    hourTime: h + ':' + m + ':' + s,
    hourMTime: h + ':' + m,
    measureTime: Y + '-' + M + '-' + D + ' ' + h + ':' + m + ':' + s
  }
}

/**
 * 获取指标或者功能的对应权限
 * @param {array} list 权限集合
 * @param {array} codes code集合
 */
export function getChildRight (list, codes) {
  const code = codes[0]
  const role = list && list.find(item => item.code == code)
  const childCodes = codes.filter(item => item != code)
  if (childCodes.length > 0) {
    return getChildRight(role && role.child, childCodes)
  }
  return role && role.child
}

export const getDataProcessing = (data) => {
  data.fields.map(v => {
    v.showValue = v.value
    if (v.type === 'select') {
      v.choices.forEach(m => {
        if (v.value == m.value) {
          v.showValue = m.text
        }
      })
    }
    if (v.type == 'content-options') {
      v.choices.forEach(m => {
        if (v.value == m.id) {
          v.showValue = m.text
        }
      })
    }
    return v
  })
  return data
}

const GetFileUrl = '/web/v1/inspect/file', GetPwvFileUrl = '/web/v1/inspect/pwv/file'
// 获取文件内容
export const getFileUrl = (ext, path, filename) => {
  const token = localStorage.getItem('authorization')
  if (localStorage.getItem('docworkroom-platform') == 'wechat') {
    return process.env.VUE_APP_SAAS_BASE_URL + 'api' + GetFileUrl + `${filename ? '/' + filename + `报告.${ ext }` : ''}?token=` + token +
    '&ext=' + ext + '&file_path=' + path + '&Auth-Platform=wechat'
  } else {
    return process.env.VUE_APP_SAAS_BASE_URL + 'api' + GetFileUrl + `${filename ? '/' + filename + `报告.${ ext }` : ''}?token=` + token +
    '&ext=' + ext + '&file_path=' + path
  }

}

// 获取 pwv 文件内容
export const getPwvFileUrl = (ext, path, filename) => {
  const token = localStorage.getItem('authorization')
  if (localStorage.getItem('docworkroom-platform') == 'wechat') {
    return process.env.VUE_APP_SAAS_BASE_URL + 'api' + GetPwvFileUrl + `${filename ? '/' + filename + `报告.${ ext }` : ''}?token=` + token +
  '&ext=' + ext + '&file_path_id=' + path + '&Auth-Platform=wechat'
  } else {
    return process.env.VUE_APP_SAAS_BASE_URL + 'api' + GetPwvFileUrl + `${filename ? '/' + filename + `报告.${ ext }` : ''}?token=` + token +
  '&ext=' + ext + '&file_path_id=' + path;
  }
}

export function getUrlParams(url, name) {
  // 通过 ? 分割获取后面的参数字符串
  let urlStr = url.split('?')[1]
  // 创建空对象存储参数
  let obj = {};
  if(urlStr==undefined){
     return undefined
  }
  // 再通过 & 将每一个参数单独分割出来
  let paramsArr = urlStr.split('&')
  for(let i = 0,len = paramsArr.length;i < len;i++){
      // 再通过 = 将每一个参数分割为 key:value 的形式
      let arr = paramsArr[i].split('=')
      obj[arr[0]] = arr[1];
  }
  if (obj[name] != undefined) {
      return obj[name]
  }
  return undefined
}

export function getSystemType(){
  let type = ''
  const UA = navigator.userAgent
  if(UA.indexOf('Android') > -1 || UA.indexOf('Adr') > -1){
    type = 'android'
  }else if(!!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)){
    type = 'ios'
  }
  return type
}

export function loadNativePage(url){
  if (getSystemType() === 'ios') {
    window.webkit.messageHandlers.breathMessage.postMessage(url)
  } else if (getSystemType() === 'android') {
    window.android.breathMessage(url)
  }
}

// 返回
export function loadNativePageBack(url){
  if (getSystemType() === 'ios') {
    window.webkit.messageHandlers.backGoHomePage.postMessage(url)
  } else if (getSystemType() === 'android') {
    window.android.backGoHomePage(url)
  }
}
// 刷新上一页
export function loadNativePageRefresh(url) {
  if (getSystemType() === 'ios') {
    window.webkit.messageHandlers.nextRefresh.postMessage(url)
  } else if (getSystemType() === 'android') {
    window.android.nextRefresh(url)
  }
}

export function isMobile() {
  const userAgent = navigator.userAgent || navigator.vendor || window.opera;
  const isMobileDevice = /android|iPhone|iPod|iPad/i.test(userAgent);

  return isMobileDevice || (window.outerWidth <= 800 && window.outerHeight <= 600);
}

// 返回并刷新上一页
export function loadNativechannelRefreshPop(data = '') {
  if (getSystemType() === 'ios') {
    window.webkit.messageHandlers.channelRefreshPop.postMessage(data)
  } else if (getSystemType() === 'android') {
    window.android.channelRefreshPop(data)
  }
}
