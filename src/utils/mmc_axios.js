import axios from 'axios'
import { Toast } from 'vant'

class HttpRequest {
    constructor(baseUrl) {
        this.baseUrl = baseUrl
        this.queue = {}
    }

    getInsideConfig() {
        const config = {
            baseURL: this.baseUrl,
            timeout: 60000,
            headers: {
                //
            }
        }
        return config
    }

    interceptors(instance, url) {
        // 请求拦截
        instance.interceptors.request.use(request => {
              const access_token = localStorage.getItem('access-token')
              if (access_token) {
                  request.headers['authorization'] = `Bearer ${access_token}`
                  //mmc问卷api兼容
                  request.headers['access-token'] = `${access_token}`
              }
            return request
        })
        // 响应拦截
        instance.interceptors.response.use(response => {
            if (response.data.status === 0) {
                return response.data
            } else {
                const urlArr = ["/api/patient/v1/question","/api/patient/v1/visit/question","/api/patient/v1/visit/basicinfo","/api/patient/v1/visit/complete"]
                if(urlArr.indexOf(response.config.url) != -1){
                    if(response.data.status == 41005){
                        location.href = '/mmc/question/notLogin'
                    }
                }
                Toast(response.data.msg || response.data.message)
                return response.data
            }
        }, error => {
            return Promise.reject(error)
        })
    }

    request(options) {
        const instance = axios.create()
        options = Object.assign(this.getInsideConfig(), options)
        this.interceptors(instance, options.url)
        return instance(options)
    }
}

export default HttpRequest
