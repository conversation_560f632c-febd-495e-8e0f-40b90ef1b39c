import axios from 'axios'
import { Toast } from 'vant'

class HttpRequest {
    constructor(baseUrl) {
        this.baseUrl = baseUrl
        this.queue = {}
    }

    getInsideConfig() {
        const config = {
            baseURL: this.baseUrl,
            timeout: 60000,
            headers: {
                //
            }
        }
        return config
    }

    interceptors(instance, url) {
        // 请求拦截
        instance.interceptors.request.use(request => {
            //   const authorization = localStorage.getItem('authorization')
            //   if (authorization) {
            //     request.headers.common['Authorization'] = `Bearer ${authorization}`
            //   }

            //   const wrokroomId = localStorage.getItem('workroom_id')
            //   if (wrokroomId) {
            //     request.headers.common['workroom-id'] = wrokroomId
            //   }

            // const appInfo = localStorage.getItem('appInfo')
            // if (appInfo) {
            //   request.headers.common['X-ZZ-APP-INFO'] = appInfo
            // }

            //   if (request.headers === undefined) {
            //     request.headers['Content-Type'] = 'application/json'
            //   }
            // 增加接口时间戳
            //   request.params = {
            //       _t: Date.parse(new Date())/1000,
            //       ...request.params
            //   }
            return request
        })
        // 响应拦截
        instance.interceptors.response.use(response => {
            console.log(response)
            if (response.data.code === 0) {
                return response.data
            } else {
                Toast(response.data.msg)
                return response.data
            }
        }, error => {
            return Promise.reject(error)
        })
    }

    request(options) {
        const instance = axios.create()
        options = Object.assign(this.getInsideConfig(), options)
        this.interceptors(instance, options.url)
        return instance(options)
    }
}

export default HttpRequest
