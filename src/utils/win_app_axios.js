import axios from 'axios'
import {Toast} from 'vant'

class HttpRequest {
  constructor(baseUrl) {
    this.baseUrl = baseUrl
    this.queue = {}
  }

  getInsideConfig() {
    const config = {
      baseURL: this.baseUrl,
      timeout: 60000,
      headers: {
        //
      }
    }
    return config
  }

  interceptors(instance, url) {
    // 请求拦截
    instance.interceptors.request.use(request => {



      if (request.headers === undefined) {
        request.headers['Content-Type'] = 'application/json'
      }
      // 增加接口时间戳
      request.params = {
          _t: Date.parse(new Date())/1000,
          ...request.params
      }
      return request
    })
    // 响应拦截
    instance.interceptors.response.use(response => {
      if (response.status === 200) {
        return response.data
      } else {
        Toast(response.msg)
        return response.data
      }
    }, error => {
      return Promise.reject(error)
    })
  }

  request(options) {
    const instance = axios.create()
    options = Object.assign(this.getInsideConfig(), options)
    this.interceptors(instance, options.url)
    return instance(options)
  }
}

export default HttpRequest
