import axios from 'axios'
import { Toast } from 'vant'
import router from '@/router'

class HttpRequest {
  constructor(baseUrl) {
    this.baseUrl = baseUrl
    this.queue = {}
    this.noToast = true  // 请求status不为0时，不使用Toast
  }

  getInsideConfig() {
    const config = {
      baseURL: this.baseUrl,
      timeout: 60000,
      headers: {
        //
      }
    }
    return config
  }

  interceptors(instance, url) {
    // 请求拦截
    instance.interceptors.request.use(request => {
      const token = localStorage.getItem('access-token')
      if (token && token != null && token != undefined && token != 'null') {
        request.headers.common['access-token'] = `${token}`
      }
      const authorization = localStorage.getItem('authorization')
      if (authorization && authorization != null && authorization != undefined && authorization != 'null') {
        request.headers.common['Authorization'] = 'Bearer ' + `${authorization}`
      }
      // const appInfo = localStorage.getItem('appInfo')
      // if (appInfo) {
      //   request.headers.common['X-ZZ-APP-INFO'] = appInfo
      // }
      request.headers.common['X-ZZ-APP-INFO'] =  JSON.stringify({"zz_app_id": 120002, "zz_app_name": "minp_ihec_bp", "zz_app_version": "1.8.1"})
      if (request.headers === undefined) {
        request.headers['Content-Type'] = 'application/json'
      }
      // 增加接口时间戳
      request.params = {
        _t: Date.parse(new Date()) / 1000,
        ...request.params
      }
      // axios禁止请求缓存   以下设置测试都未生效，原因未知
      // request.headers['Cache-Control'] = 'no-cache'
      // request.headers['Cache-Control'] = 'no-store'
      return request
    })
    // 响应拦截
    instance.interceptors.response.use(response => {
      const source_type = localStorage.getItem('source_type')
      if (response.headers === undefined) {
        if (response.status === 0) {
          return response
        } else {
          if ((response.status == 400 || response.status == 403) && source_type == 2) {
            const UA = navigator.userAgent
            const isAndroid = UA.indexOf('Android') > -1 || UA.indexOf('Adr') > -1 // android终端
            const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
            //原生app方法名称：jumpToReport
            if (isAndroid) {
              window.android.loginInvalid()
            } else if (isIOS) {
              window.webkit.messageHandlers.loginInvalid.postMessage('')
            }
          }
          // 请求status不为0时，不使用Toast
          if(this.noToast){
            Toast(response.msg)
          }
          return response
        }
      } else {

        let status = response.data.status
        if (status === 0) {
          return response.data
        } else {
          if ((status == 400 || status == 403) && source_type == 2) {
            const UA = navigator.userAgent
            const isAndroid = UA.indexOf('Android') > -1 || UA.indexOf('Adr') > -1 // android终端
            const isIOS = !!UA.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
            // 原生app方法名称：jumpToReport
            if (isAndroid) {
              window.android.loginInvalid()
            } else if (isIOS) {
              window.webkit.messageHandlers.loginInvalid.postMessage('')
            }
          }
          // 请求status不为0时，不使用Toast
          if(this.noToast){
            Toast(response.data.msg)
          }
          return response.data
        }
      }
    }, error => {
      return Promise.reject(error)
    })
  }

  request(options,type) {
    // 请求status不为0时，不使用Toast
    this.noToast = type
    const instance = axios.create()
    options = Object.assign(this.getInsideConfig(), options)
    this.interceptors(instance, options.url)
    return instance(options)
  }
}


export default HttpRequest
