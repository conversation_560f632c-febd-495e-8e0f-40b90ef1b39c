import axios from "axios";
import { Toast } from "vant";

class HttpRequest {
  constructor(baseUrl) {
    this.baseUrl = baseUrl;
    this.queue = {};
  }

  getInsideConfig() {
    const config = {
      baseURL: this.baseUrl,
      timeout: 60000,
      headers: {
        //
      },
    };
    return config;
  }

  interceptors(instance, url) {
    // 请求拦截
    instance.interceptors.request.use((request) => {
      const authorization = localStorage.getItem("authorization");

      if (authorization) {
        request.headers.common["Authorization"] = `Bearer ${authorization}`;
      }

      if (
        ["zmsc.zz-med-test.com", "zmsc.zz-med-stg.com", "zmsc.zz-med.com"].some(
          (v) => this.baseUrl.indexOf(v) !== -1
        )
      ) {
        const saas_room_id = localStorage.getItem("saas_room_id");
        // 针对商米kpi 单独处理 https://zmsc.zz-med.com/api/web/v1/zz_hosp/project/statics
        if (url.indexOf("zz_hosp/project/statics") == -1) {
          request.headers["room-id"] = saas_room_id;
        }
        if (localStorage.getItem("docworkroom-platform"))
          request.headers["Auth-Platform"] = localStorage.getItem(
            "docworkroom-platform"
          );
        // 暴风进入病案 为方便sentry统计报错 header加标示
        let isFrom = localStorage.getItem("isFrom")
        if(isFrom && isFrom == 'zzApp') request.headers["request-source"] = 'new_health_app'
      }

      if (request.headers === undefined) {
        request.headers["Content-Type"] = "application/json";
      }

      // 测试接口，上线删除
      request.headers["zz-saas-project-id"] =
        localStorage.getItem("docworkroom_project_id") || "0";
      request.headers["zz-saas-app-info"] = JSON.stringify({
        saas_app_name: "saas-h5", //软件用用名
        saas_app_version: "1.0.0", //软件版本
        saas_web_name: "", //web名称
        saas_web_version: "1.0.0", //web版本号
        saas_client_id: "", //软件 clientId 从windows
        saas_app_id: "1", // 来源app id
      });

      // 增加接口时间戳
      request.params = {
        _t: Date.parse(new Date()) / 1000,
        ...request.params,
      };
      return request;
    });
    // 响应拦截
    instance.interceptors.response.use(
      (response) => {
        if (response.status === 200) {
          return response.data;
        } else {
          Toast(response.msg);
          return response.data;
        }
      },
      (error) => {
        return Promise.reject(error);
      }
    );
  }

  request(options) {
    const instance = axios.create();
    options = Object.assign(this.getInsideConfig(), options);
    this.interceptors(instance, options.url);
    return instance(options);
  }
}

export default HttpRequest;
